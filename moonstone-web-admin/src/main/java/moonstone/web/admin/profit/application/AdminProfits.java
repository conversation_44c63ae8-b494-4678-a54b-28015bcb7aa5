package moonstone.web.admin.profit.application;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.*;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.event.OrderRefundEvent;
import moonstone.item.api.ProfitGain;
import moonstone.item.emu.ProfitFor;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.*;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.service.*;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserWxReadService;
import moonstone.web.core.events.trade.listener.ProfitChangeListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
public class AdminProfits {
    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;
    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;
    @RpcConsumer
    private SkuReadService skuReadService;
    @RpcConsumer
    private WithdrawAccountReadService withdrawAccountReadService;
    @RpcConsumer
    private WithdrawAccountWriteService withdrawAccountWriteService;
    @RpcConsumer
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;
    @RpcConsumer
    private WithDrawProfitApplyWriteService withDrawProfitApplyWriteService;
    @RpcConsumer
    private UserReadService<User> userReadService;
    @RpcConsumer
    private ShopWxaReadService shopWxaReadService;
    @RpcConsumer
    private UserWxReadService userWxReadService;
    @RpcConsumer
    private SubStoreReadService subStoreReadService;
    @RpcConsumer
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;
    @RpcConsumer
    private BalanceDetailManager balanceDetailManager;

    @Autowired
    private ProfitGain profitGain;

    @RpcConsumer
    private BalanceDetailReadService balanceDetailReadService;
    @RpcConsumer
    private BalanceDetailWriteService balanceDetailWriteService;

    @RpcConsumer
    private RefundReadService refundReadService;
    @Autowired
    private ProfitChangeListener profitChangeListener;
    @Autowired
    private UserTypeBean userTypeBean;

    @PostMapping("/api/profit/change-withdraw-type")
    public Response<Boolean> changeType(Long withdrawApplyId, @RequestParam(defaultValue = "0") Integer type, @RequestParam(defaultValue = "") String authCode) {
        CommonUser operator = UserUtil.getCurrentUser();
        if (ObjectUtils.isEmpty(authCode) || !authCode.equals("[-F^CK-U-@CCCC-o0o-]")) {
            if (operator == null) {
                return Response.fail("user.not.login");
            }
            if (!Optional.ofNullable(userReadService.findById(operator.getId()).getResult()).map(userTypeBean::isAdmin).orElse(false)) {
                return Response.fail(new Translate("权限不足").toString());
            }
        }
        if (withdrawApplyId == null) {
            return Response.fail(new Translate("请输入正确的数值").toString());
        }
        if (!WithDrawProfitApply.WithdrawPaidType.from(type).isPresent()) {
            return Response.fail(new Translate("%d提现模式不存在", type).toString());
        }
        WithDrawProfitApply withDrawProfitApply = withDrawProfitApplyReadService.findById(withdrawApplyId).getResult();
        if (withDrawProfitApply == null) {
            return Response.fail(new Translate("提现不存在").toString());
        }
        WithdrawAccount withdrawAccount = withdrawAccountReadService.findByShopIdAndUserId(withDrawProfitApply.getSourceId(), withDrawProfitApply.getUserId()).orElse(new ArrayList<>())
                .stream().filter(account -> Objects.equals(account.getType(), type)).findFirst().orElse(null);
        if (type == 0 && withdrawAccount == null) {
            String appId = Either.ok(shopWxaReadService.findByShopId(withDrawProfitApply.getSourceId()).getResult())
                    .map(Collection::stream)
                    .map(stream -> stream.filter(wxa -> wxa.getStatus() == ShopWxaStatus.RELEASED.getValue()))
                    .map(Stream::findFirst)
                    .orElse(Optional.empty())
                    .map(ShopWxa::getAppId).orElse(null);
            if (appId == null) {
                log.error("{} userId:{} withdrawApplyId:{}", LogUtil.getClassMethodName(), withDrawProfitApply.getUserId(), withDrawProfitApply.getId());
                throw new RuntimeException(new Translate("提现配置失败,请检查参数").toString());
            }
            withdrawAccount = new WithdrawAccount();
            withdrawAccount.setUserId(withDrawProfitApply.getUserId());
            withdrawAccount.setShopId(withDrawProfitApply.getSourceId());
            withdrawAccount.setName(new Translate("微信钱包提现").toString());
            withdrawAccount.setFrom(new Translate("微信钱包").toString());
            withdrawAccount.setType(WithDrawProfitApply.WithdrawPaidType.WECHAT.getType());
            userWxReadService.findByAppIdAndUserId(appId, withDrawProfitApply.getUserId()).orElse(Optional.empty())
                    .map(UserWx::getOpenId).ifPresent(withdrawAccount::setAccount);
            withdrawAccountWriteService.create(withdrawAccount);

        }
        if (withdrawAccount == null) {
            return Response.fail(new Translate("未有符合模式的提现帐号").toString());
        }

        if (withDrawProfitApply.isPaid()) {
            log.error("{} apply:{}", LogUtil.getClassMethodName(), withDrawProfitApply);
            throw new JsonResponseException(new Translate("提现状态不符合标准").toString());
        }
        withDrawProfitApply.setStatus(1);
        withDrawProfitApply.initAuth();
        withDrawProfitApply.setOnlineWithdraw();
        withDrawProfitApply.getExtra().put("paidType", type + "");
        withDrawProfitApply.setWithdrawAccountId(withdrawAccount.getId());
        val rUpdate = withDrawProfitApplyWriteService.update(withDrawProfitApply);
        if (!rUpdate.isSuccess()) {
            log.error("{} rUpdate:{} withDrawProfitApply:{}", LogUtil.getClassMethodName(), rUpdate, withDrawProfitApply);
            return Response.fail(new Translate("修正提现数据失败").toString());
        }
        return Response.ok(true);
    }

    @PostMapping("/api/profit/retrigger-refunds")
    public boolean refunds(Long refundId) {
        Refund refund = refundReadService.findById(refundId).getResult();
        if (refund == null) {
            throw new RuntimeException(new Translate("退款单不存在或者不可用").toString());
        }
        Long sourceId = refund.getShopId();
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(sourceId);
        criteria.setRelatedId(refundId);
        criteria.setType(ProfitType.OutCome.getValue());
        criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.RefundRelated.getValue()));
        Paging<BalanceDetail> pBalance = balanceDetailReadService.paging(criteria.toMap()).getResult();
        if (pBalance == null) {
            throw new RuntimeException(new Translate("查询数据库错误").toString());
        }
        if (!pBalance.isEmpty()) {
            throw new RuntimeException(new Translate("已有%d个相关收支,例如:%d,所以不再触发", pBalance.getTotal(), pBalance.getData().stream().findFirst().map(EntityBase::getId).orElse(null)).toString());
        }
        OrderRefundEvent orderRefundEvent = new OrderRefundEvent(refundId, OrderEvent.REFUND_SUCCESS.toOrderOperation());
        profitChangeListener.onRefund(orderRefundEvent);
        return true;
    }

    /**
     * 修复被错误逻辑扣减的数据,将他恢复加回原有的钱包内
     * 错误扣除的数据没有标志IgnoreAble并且其与订单下单相关
     * 同时在修复数据后将其标志一下,以避免重复修复 ,将其status反转为负数
     *
     * @param shopId 平台Id也是sourceId
     * @return
     */
    @PostMapping("/api/profit/fillBackFalseDecrease")
    public boolean fillBack(Long shopId) {
        Boolean allSucceed = true;
        BalanceDetailCriteria queryCriteria = new BalanceDetailCriteria();
        queryCriteria.setSourceId(shopId);
        queryCriteria.setType(ProfitType.OutCome.getValue());
        queryCriteria.setNotStatusBitMarks(Arrays.asList(IgnoreAble.IgnoreAbleDefaultMaskbit.IgnoreAble.getBit(), IsPresent.presentMaskBit.Present.getValue()));
        queryCriteria.setStatusBitMarks(Arrays.asList(OrderRelated.orderRelatedMask.ShopOrder.getValue(), BalanceDetail.maskBit.OrderRelated.getValue()));
        queryCriteria.setPageSize(Integer.MAX_VALUE);
        Paging<BalanceDetail> detailPaging = balanceDetailReadService.paging(queryCriteria.toMap()).getResult();
        if (detailPaging == null) {
            log.error("{} fail to fillBack the Data", LogUtil.getClassMethodName());
            throw new RuntimeException(new Translate("数据库读取错误,请检查日志").toString());
        }
        List<BalanceDetail> balanceDetails = detailPaging.getData();
        for (BalanceDetail balanceDetail : balanceDetails) {
            if (balanceDetail.getStatus() > 0) {
                Response<Boolean> result = balanceDetailWriteService.increaseCash(balanceDetail.getUserId(), balanceDetail.getSourceId(), balanceDetail.getChangeFee(), false);
                if (!result.isSuccess()) {
                    allSucceed = false;
                    log.error("{} failed to increase cash for userId:{} sourceId:{} changFee:{}", LogUtil.getClassMethodName(), balanceDetail.getUserId(), balanceDetail.getSourceId(), balanceDetail.getChangeFee());
                } else {
                    BalanceDetail update = new BalanceDetail();
                    update.setId(balanceDetail.getId());
                    update.setStatus(balanceDetail.getStatus() | IgnoreAble.IgnoreAbleDefaultMaskbit.IgnoreAble.getBit());
                    balanceDetailWriteService.update(update);
                }
            } else {
                log.warn("{} skip the data(id:{}) because it's status below the zero", LogUtil.getClassMethodName(), balanceDetail.getId());
            }
        }
        return allSucceed;
    }

    /**
     * 重新计算皇家的待收益
     *
     * @param shopId 店铺Id
     * @param size   每页大小
     * @return
     */
    @RequestMapping("/api/profit-query-substore")
    public Response<Map<Long, Long>> fixProfitForSubstore(@RequestParam(defaultValue = "28") Long shopId, @RequestParam(defaultValue = "250") Integer size, @RequestParam(defaultValue = "false") Boolean deal, @RequestParam(defaultValue = "fuckU") String pw) {
        if (!pw.equals("me-and-you")) {
            return Response.ok();
        }
        OrderCriteria orderCriteria = new OrderCriteria();
        // 这边不处理已经收货但是要退款的
        orderCriteria.setStatus(Stream.of(OrderStatus.PAID, OrderStatus.SHIPPED, OrderStatus.REFUND_APPLY_REJECTED, OrderStatus.REFUND_APPLY_AGREED, OrderStatus.REFUND_APPLY).map(OrderStatus::getValue).collect(Collectors.toList()));
        orderCriteria.setShopId(shopId);
        // 修改这里以让阶梯分销有效
        orderCriteria.setOutFrom(OrderOutFrom.SUB_STORE.Code());
        boolean notEmpty = true;
        int i = 0;
        size = size == null ? 50 : size;
        Map<Long, Long> userProfitMap = new HashMap<>();
        Map<Long, SubStore> subStoreMap = new HashMap<>();
        Map<Long, Sku> skuMap = new HashMap<>();
        Set<Long> noRepeatSet = new HashSet<>();
        int count = 0;
        while (notEmpty) {
            Map<Long, List<SkuOrder>> ordersMap = new HashMap<>();
            Paging<ShopOrder> shopOrderPaging = shopOrderReadService.findBy(i++, size, orderCriteria).getResult();
            if (shopOrderPaging == null || shopOrderPaging.getData() == null) {
                break;
            }
            notEmpty = !shopOrderPaging.isEmpty();
            List<SkuOrder> allSkuOrders = skuOrderReadService.findByShopOrderIds(shopOrderPaging.getData().stream().map(OrderBase::getId).collect(Collectors.toList())).getResult();
            for (SkuOrder allSkuOrder : allSkuOrders) {
                List<SkuOrder> orders = ordersMap.getOrDefault(allSkuOrder.getOrderId(), new ArrayList<>());
                orders.add(allSkuOrder);
                if (orders.size() <= 1) {
                    ordersMap.put(allSkuOrder.getOrderId(), orders);
                }
            }
            for (ShopOrder shopOrder : shopOrderPaging.getData()) {
                if (count % 50 == 0) {
                    log.debug("{} have deal with count:{} nowOrder:{} profitMap:{}", LogUtil.getClassMethodName(), count, shopOrder.getId(), userProfitMap);
                }
                if (ObjectUtils.isEmpty(shopOrder.getOutShopId()) || shopOrder.getOutShopId().trim().isEmpty()) {
                    log.debug("{} skipped order:{} outShopId:{}", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getOutShopId());
                    continue;
                }
                Long outShopId = Long.parseLong(shopOrder.getOutShopId());
                SubStore subStore = subStoreMap.getOrDefault(outShopId, subStoreReadService.findById(outShopId).getResult());
                if (subStore == null) {
                    log.error("{} not found by subStoreId:{} shopOrderId:{}", LogUtil.getClassMethodName(), outShopId, shopOrder.getId());
                    continue;
                }
                subStoreMap.put(outShopId, subStore);
                List<SkuOrder> skuOrders = ordersMap.get(shopOrder.getId()).stream().distinct().collect(Collectors.toList());
                long profit = userProfitMap.getOrDefault(subStore.getUserId(), 0L);
                for (SkuOrder skuOrder : skuOrders) {
                    if (!noRepeatSet.contains(skuOrder.getId())) {
                        noRepeatSet.add(skuOrder.getId());
                    } else {
                        log.warn("{} repeat calculate for orderId:{} skuOrder:{}", LogUtil.getClassMethodName(), shopOrder.getId(), skuOrder.getId());
                        continue;
                    }
                    count++;
                    Sku sku = skuMap.getOrDefault(skuOrder.getSkuId(), skuReadService.findSkuById(skuOrder.getSkuId()).getResult());
                    skuMap.put(sku.getId(), sku);
                    long d_profit = skuOrder.getQuantity() * profitGain.getProfit(ProfitFor.SubStoreProfit, sku).getResult();
                    profit += d_profit;
                    log.debug("{} orderId:{} skuId:{} profit:{}", LogUtil.getClassMethodName(), shopOrder.getId(), sku.getId(), d_profit);
                    if (shopOrder.getRefererId() == null) {
                        profit += skuOrder.getQuantity() * profitGain.getProfit(ProfitFor.GuiderProfit, sku).getResult();
                    } else {
                        long guiderProfit = userProfitMap.getOrDefault(shopOrder.getRefererId(), 0L);
                        guiderProfit += skuOrder.getQuantity() * profitGain.getProfit(ProfitFor.GuiderProfit, sku).getResult();
                        userProfitMap.put(shopOrder.getRefererId(), guiderProfit);
                    }
                    userProfitMap.put(subStore.getUserId(), profit);
                }
            }
        }
        log.debug("{} have deal with {} sku order", LogUtil.getClassMethodName(), count);
        BalanceDetailCriteria pockeQuery = new BalanceDetailCriteria();
        pockeQuery.setType(ProfitType.CacheInCome.getValue());
        // 默认是1  也就是不是已收入的
        pockeQuery.setStatus(1);
        pockeQuery.setSourceId(shopId);
        pockeQuery.setPageSize(1);// 最多一个
        if (deal) {
            for (Long userId : userProfitMap.keySet()) {
                long profit = userProfitMap.get(userId);
                pockeQuery.setUserId(userId);
                Paging<BalanceDetail> unPresentProfitPaing = balanceDetailReadService.paging(pockeQuery.toMap()).getResult();
                if (unPresentProfitPaing.isEmpty()) {
                    log.error("{} we got problem now userId:{} profit:{}", LogUtil.getClassMethodName(), userId, profit);
                    continue;
                }
                BalanceDetail unPresentProfit = unPresentProfitPaing.getData().get(0);
                unPresentProfit.setFee(profit);
                if (Objects.equals(balanceDetailWriteService.update(unPresentProfit).getResult(), true)) {
                    log.error("{} fuck:{}", LogUtil.getClassMethodName("UPDATE_BALANCE_FAILED"), unPresentProfit);
                }
            }
        }
        return Response.ok(userProfitMap);
    }

    /**
     * 恢复丢失的数据,重新计算已收货的佣金
     *
     * @param shopId    店铺Id
     * @param orderIds  订单Id
     * @param addProfit
     * @param deal
     * @return
     */
    @PostMapping("/api/profit/subStore/profitFix")
    public Response<Map<Long, Map<Long, Long>>> fixProfit(Long shopId, Long[] orderIds, @RequestParam(defaultValue = "false") boolean addProfit, @RequestParam(defaultValue = "false") boolean deal, @RequestParam(defaultValue = "true") boolean present) {
        Map<Long, Map<Long, Long>> userProfitMap = new HashMap<>();
        Map<Long, Long> subStoreUserIdMap = new HashMap<>();
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(shopId);
        criteria.setType(ProfitType.InCome.getValue());
        criteria.setStatusBitMarks(Arrays.asList(IsPersistAble.maskBit.PersistAble.getValue(), BalanceDetail.orderRelatedMask.ShopOrder.getValue()));
        if (present) {
            criteria.setStatusBitMarks(new ArrayList<>(criteria.getStatusBitMarks()));
            criteria.getStatusBitMarks().add(IsPresent.presentMaskBit.Present.getValue());
        } else {
            if (criteria.getNotStatusBitMarks() == null) {
                criteria.setNotStatusBitMarks(new ArrayList<>());
            } else {
                criteria.setNotStatusBitMarks(new ArrayList<>(criteria.getNotStatusBitMarks()));
            }
            criteria.getNotStatusBitMarks().add(IsPresent.presentMaskBit.Present.getValue());
        }
        BalanceDetailCriteria oldCriteria = new BalanceDetailCriteria();
        oldCriteria.setSourceId(shopId);
        oldCriteria.setType(ProfitType.InCome.getValue());
        oldCriteria.setStatusBitMarks(Arrays.asList(IsPersistAble.maskBit.PersistAble.getValue(), BalanceDetail.orderRelatedMask.ShopOrder.getValue()));
        oldCriteria.setNotStatusBitMarks(Arrays.asList(IsPresent.presentMaskBit.Present.getValue()));
        ShopOrder shopOrder;
        for (Long orderId : Arrays.stream(orderIds).mapToLong(id -> id).distinct().toArray()) {
            criteria.setRelatedId(orderId);
            if (balanceDetailReadService.count(criteria).getResult() > 0) {
                log.warn("{} id:{} have record already", LogUtil.getClassMethodName("Present:" + present + "_ORDER-PROFIT-MAKER"), orderId);
            } else {
                shopOrder = shopOrderReadService.findById(orderId).getResult();
                Long shouldBeProfit = 0L;
                Long guiderShouldGainProfit = 0L;
                Long subStoreId = NumberUtil.parseNumber(shopOrder.getOutShopId(), Long.TYPE).orElse(null);
                if (subStoreId == null) {
                    log.error("{} fuck data:{} of {}", LogUtil.getClassMethodName(), shopOrder.getOutBuyerId(), shopOrder.getId());
                    continue;
                }
                Long storeOwnerUserId = subStoreUserIdMap.getOrDefault(subStoreId, findOwneOfSubStore(subStoreId));
                subStoreUserIdMap.put(subStoreId, storeOwnerUserId);
                if (storeOwnerUserId == null) {
                    log.error("{} no owner for orderId:{}", LogUtil.getClassMethodName(), shopOrder.getId());
                    continue;
                }
                Long guiderUserId = shopOrder.getReferenceId();
                if (present) {
                    oldCriteria.setRelatedId(shopOrder.getId());
                    if (guiderUserId != null) {
                        oldCriteria.setUserId(guiderUserId);
                        guiderShouldGainProfit += balanceDetailReadService.paging(oldCriteria.toMap()).getResult().getData().stream().map(BalanceDetail::getChangeFee).reduce(Long::sum).orElse(0L);
                    }
                    oldCriteria.setUserId(storeOwnerUserId);
                    shouldBeProfit += balanceDetailReadService.paging(oldCriteria.toMap()).getResult().getData().stream().map(BalanceDetail::getChangeFee).reduce(Long::sum).orElse(0L);
                    /// 将数据缓存
                    Map<Long, Long> orderProfitMap = userProfitMap.getOrDefault(storeOwnerUserId, new HashMap<>());
                    orderProfitMap.put(shopOrder.getId(), shouldBeProfit);
                    Long allProfit = orderProfitMap.getOrDefault(0L, 0L);
                    allProfit += shouldBeProfit;
                    orderProfitMap.put(0L, allProfit);
                    userProfitMap.put(storeOwnerUserId, orderProfitMap);
                    if (guiderUserId != null) {
                        orderProfitMap = userProfitMap.getOrDefault(guiderUserId, new HashMap<>());
                        orderProfitMap.put(shopOrder.getId(), guiderShouldGainProfit);
                        allProfit = orderProfitMap.getOrDefault(0L, 0L);
                        allProfit += guiderShouldGainProfit;
                        orderProfitMap.put(0L, allProfit);
                        userProfitMap.put(guiderUserId, orderProfitMap);
                    }
                } else {
                    log.warn("{} not forsee-profit record for the make unpresent profit for:{}", LogUtil.getClassMethodName(), shopId);
                }
                /*
                 * 是否真实行动
                 */
                if (deal) {
                    // 是否添加利润
                    if (addProfit) {
                        balanceDetailWriteService.updateCacheBySourceId(storeOwnerUserId, shouldBeProfit, false, shopId, ProfitType.CacheInCome.getValue());
                        if (guiderUserId != null) {
                            balanceDetailWriteService.updateCacheBySourceId(guiderUserId, guiderShouldGainProfit, false, shopId, ProfitType.CacheInCome.getValue());
                        }
                    }
                    try {
                        if (present) {
                            balanceDetailManager.persistForeseeProfit(shopOrder);
                        } else {
                            balanceDetailManager.earnForeseeProfit(shopOrder, OrderOutFrom.fromCode(shopOrder.getOutFrom()));
                            // balanceDetailManager.calculateProfit(shopOrder, OrderOutFroms.fromCode(shopOrder.getOutFrom()), false);
                        }
                    } catch (Exception ex) {
                        log.error("{} shopOrderId:{} profit cal failed by ex:", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
                    }
                }
            }
        }
        return Response.ok(userProfitMap);
    }

    /**
     * 包括获取重定位门店的Id
     *
     * @param subStoreId
     * @return
     */
    private Long findOwneOfSubStore(Long subStoreId) {
        while (subStoreId != null) {
            SubStore subStore = subStoreReadService.findById(subStoreId).getResult();
            if (subStore == null) {
                return null;
            }
            if (!ObjectUtils.isEmpty(subStore.getExtra().get("redirect"))) {
                subStoreId = NumberUtil.parseNumber(subStore.getExtra().get("redirect"), Long.TYPE).orElse(null);
            } else {
                return subStore.getUserId();
            }
        }
        return null;
    }
}
