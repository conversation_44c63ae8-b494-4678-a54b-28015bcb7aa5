/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.admin.category;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.FrontCategory;
import moonstone.category.service.FrontCategoryReadService;
import moonstone.category.service.FrontCategoryWriteService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-25
 */
@RestController
@Slf4j
@RequestMapping("/api/frontCategories")
public class AdminFrontCategories {

    @RpcConsumer
    private FrontCategoryReadService frontCategoryReadService;

    @RpcConsumer
    private FrontCategoryWriteService frontCategoryWriteService;

    @RequestMapping(value = "/children",method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<List<FrontCategory>> findChildrenOfFrontend(@RequestParam(value = "pid", defaultValue = "0") Long pid) {
        Response<List<FrontCategory>> r = frontCategoryReadService.findChildrenByPid(pid);
        if (!r.isSuccess()) {
            log.warn("failed to find children of front category(id={}), error code:{}", pid, r.getError());
        }
        return r;
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<FrontCategory> create(@RequestBody  FrontCategory frontCategory) {
        Response<FrontCategory> r = frontCategoryWriteService.create(frontCategory);
        if (!r.isSuccess()) {
            log.warn("failed to create {}, error code:{}", frontCategory, r.getError());
        }
        return r;
    }

    @RequestMapping(value = "/{id}/name", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> updateName(@PathVariable("id") long id,
                              @RequestParam("name") String name) {
        Response<Boolean> r = frontCategoryWriteService.updateName(id, name);
        if (!r.isSuccess()) {
            log.warn("failed to update front category(id={}) name to {} ,error code:{}",
                    id, name, r.getError());
        }
        return r;
    }

    @RequestMapping(value = "/{id}/logo", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> updateLogo(@PathVariable("id") long id,
                              @RequestParam("logo") String logo) {
        Response<Boolean> r = frontCategoryWriteService.updateLogo(id, logo);
        if (!r.isSuccess()) {
            log.warn("failed to update front category(id={}) logo to {} ,error code:{}",
                    id, logo, r.getError());
        }
        return r;
    }

    @RequestMapping(value="/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> delete(@PathVariable("id")Long id){
        Response<Boolean> r = frontCategoryWriteService.delete(id);
        if(!r.isSuccess()){
            log.warn("failed to delete front category(id={}) ,error code:{}",id, r.getError());
        }
        return r;
    }
}
