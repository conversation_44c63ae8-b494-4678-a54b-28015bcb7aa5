package moonstone.common.utils;

import moonstone.common.model.Condition;
import org.junit.Test;

import java.util.Objects;
import java.util.Optional;
import java.util.Scanner;


/**
 * 测试条件
 */
public class ConditionTest {
    @Test
    public void test() {
        SimpleConditionExecutor<<PERSON><PERSON>an, <PERSON><PERSON>an> executor = (condition, o) -> Objects.equals(condition.getCondition(), Boolean.TRUE);
        Scanner s = new Scanner("t&t t&f t|(f&f)");
        Scanner r = new Scanner("true false true");
        while (s.hasNext()) {
            try {
                Condition<Boolean> test1 = Condition.parseCondition(str -> Optional.of(str.toLowerCase().startsWith("t"))
                        , s.next());
                assert r.next().equals(executor.execute(test1, true) + "");
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println(e.getMessage());
            }

        }
    }
}
