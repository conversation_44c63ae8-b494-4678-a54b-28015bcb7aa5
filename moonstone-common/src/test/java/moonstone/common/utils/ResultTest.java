package moonstone.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import moonstone.common.model.Either;
import org.junit.Test;

import java.util.Arrays;
import java.util.Objects;

/**
 * test for simple serialize for Either
 */
public class ResultTest {

    @Test
    public void successTestAndJson() {
        Either<?> fail = Either.fail();
        Either<Boolean> ok = Either.ok(true);

        assert !fail.isSuccess();
        assert ok.isSuccess();
        assert ok.orElse(false);
        assert Objects.isNull(fail.getResult());
        assert ok.intoOptional().isPresent();
        assert !fail.intoOptional().isPresent();

        Either<?> jFail = JSON.parseObject(JSON.toJSONString(fail), Either.class);
        Either<Boolean> jOk = JSON.parseObject(JSON.toJSONString(ok), new TypeReference<Either<Boolean>>() {
        });

        assert !jFail.isSuccess();
        assert jOk.isSuccess();
        assert jOk.orElse(false);
        assert jOk.intoOptional().isPresent();
        assert !jFail.intoOptional().isPresent();
        assert Objects.isNull(jFail.getResult());
    }

    @Test
    public void testJson() {
        assert JSON.toJSONString(Either.ok(true)).equals("{\"result\":true,\"success\":true}");
    }

    @Test
    public void testJsonDeserialize() {
        assert JSON.parseObject(JSON.toJSONString(Either.ok(true)), new TypeReference<Either<Boolean>>() {
        })
                .equals(Either.ok(true));

        JSONObject object = JSONObject.parseObject(JSON.toJSONString(ImmutableMap.of("test", "ok", "list", Arrays.asList(1, 3, 2, 4, 5), "bool", "true")));
        assert JSON.parseObject(JSON.toJSONString(Either.ok(object)), new TypeReference<Either<JSONObject>>() {
        })
                .equals(Either.ok(object));
    }

    @Test(expected = RuntimeException.class)
    public void testErrorJsonDeserialize() {
        Either<?> error = Either.error(new IllegalStateException("fail to test job"));
        Either<?> de = JSON.parseObject(JSON.toJSONString(error), Either.class);
        assert de.getErrorMsg().equals("fail to test job");
        de.take();
    }

    @Test(expected = RuntimeException.class)
    public void testErrorEmpty() {
        Either<?> either = Either.ok(null);
        assert either.getErrorMsg().equals("<init>");
        either.take();
    }
}
