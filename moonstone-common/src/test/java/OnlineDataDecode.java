import scala.Int;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyStore;
import java.util.*;

interface OnlineDataDecode {
  static void main(String[] args) throws Exception {
    var input = new FileInputStream("/home/<USER>/order.list");
    var s = new Scanner(input);
    var ks = KeyStore.getInstance(KeyStore.getDefaultType());
    var codePath = "/home/<USER>/mall";
    ks.load(new FileInputStream(codePath+"/moonstone-web-core/src/main/resources/online.keystore"), "online-secret".toCharArray());
    var key = ks.getKey("CommonKey", "online-secret".toCharArray());
    List<OrderInfo> infos = new ArrayList<>(32);
    while (s.hasNext()) {
      var line = s.nextLine().trim();
      var rom = line.split("\\|");
      if (rom.length < 12) continue;
      var name = rom[2].trim();
      var no = rom[3].trim();
      var passwd = rom[4].trim();
      var order = rom[5].trim();
      var buyerId = rom[7].trim();
      var status = Integer.parseInt(rom[9].trim());
      var time = rom[11].trim();
      if (status ==-1 || status < -13) continue;
      var info = decode(key, new PayerInfo(name, no, passwd));
      infos.add(new OrderInfo(Long.parseLong(order),
        status,
        time,
        Long.parseLong(buyerId),
        info.name,
        info.no));
    }
    var map = new HashMap<String, List<OrderInfo>>();
    for (OrderInfo info : infos) {
      map.computeIfAbsent(info.name(), i -> new ArrayList<>())
        .add(info);
    }
    var out = new PrintStream(new FileOutputStream("/home/<USER>/order-decode"));
    for (List<OrderInfo> list : map.values()) {
      for (OrderInfo orderInfo : list) {
        System.out.println(orderInfo);
        out.println(orderInfo);
      }
    }
  }
  record OrderInfo(Long id, Integer status, String time, Long buyerId, String name, String no) {}

  static byte[] base64Decode(String data) {
    var last = data.getBytes(StandardCharsets.UTF_8);
    try {
      while (true) {
        last = Base64.getDecoder().decode(last);
      }
    } catch (Exception ignore) {

    }
    return last;
  }

  static Info decode(Key key, PayerInfo payerInfo) {
    try {
      if (payerInfo == null) return new Info(null, null);
      Cipher cipher = Cipher.getInstance("AES");
      cipher.init(Cipher.DECRYPT_MODE, key);
      byte[] pass = cipher.doFinal(base64Decode(payerInfo.password()));
      Key dataKey = new SecretKeySpec(pass, "AES");
      cipher.init(Cipher.DECRYPT_MODE, dataKey);
      // the data maybe encoded64 twice.
      var payerName = base64Decode(payerInfo.payerName());
      var payerNo = base64Decode(payerInfo.payerNo());
      return new Info(new String(cipher.doFinal(payerName), StandardCharsets.UTF_8),
        new String(cipher.doFinal(payerNo), StandardCharsets.UTF_8));
    } catch (Exception e) {
      throw new RuntimeException("无法解码数据");
    }
  }

  record PayerInfo(String payerName, String payerNo, String password) {

  }

  record Info(String name, String no) {
  }
}
