import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.image.slice.ImageDomainSplitter;
import org.junit.Test;

@Slf4j
public class ImageDomainSplitterTest {
    @Test
    public void imageSplitTest() {
        assert ImageDomainSplitter.splitDomain("https://127.0.0.1:8080/backend").equals("/backend");
        assert ImageDomainSplitter.splitDomain("http://www.baidu.com/backend").equals("/backend");
    }

    @Test
    public void sog(){
        log.warn("or");
    }

    @Test
    public void imageCompleteTest() {
        assert ImageDomainSplitter.completeDomain("http://127.0.0.1:8080", "/backend").equals("http://127.0.0.1:8080/backend");
        assert ImageDomainSplitter.completeDomain("http://127.0.0.1:8080/backend", "/api").equals("http://127.0.0.1:8080/api");
        assert ImageDomainSplitter.completeDomain("http://127.0.0.1:8080/bakcen", "https://test.com/backend").equals("https://test.com/backend");
    }
}
