package moonstone.common.model;

import java.util.Arrays;
import java.util.Collection;

/**
 * 规则裁决器
 * 与JudgeBean解偶,可以手动实现或者使用JudgeBean来实现,主要用于规则判断
 *
 * @param <T>
 */
public interface SimpleRulerJudgeBean<T> {
    /**
     * 对被裁决实体的裁决
     *
     * @param aimTarget 裁决实体
     * @return 裁决结果
     */
    boolean allow(T aimTarget);

    /**
     * 对字符串的裁决
     *
     * @param code 被序列化后的数据
     * @return 结果
     */
    default boolean allow(String code) {
        return false;
    }

    default boolean allow(T[] array) {
        return allow(Arrays.asList(array));
    }

    default boolean allow(Collection<T> collection) {
        for (T t : collection) {
            if (!allow(t))
                return false;
        }
        return true;
    }
}
