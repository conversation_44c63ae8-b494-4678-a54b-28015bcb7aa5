package moonstone.common.model;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

@Slf4j
public class ExtraMap<K,V> extends HashMap<K,V> {
    private WithExtraMap withExtraMap;
    ExtraMap()
    {
        super();
    }
    ExtraMap(WithExtraMap withExtraMap)
    {
        super();
        Gson gson=new Gson();
        this.withExtraMap=withExtraMap;
        if (!ObjectUtils.isEmpty(withExtraMap.getExtraStr())) {
            try {
                final Type type=new TypeToken<Map<K,V>>(){}.getType();
                Map<K, V> map = gson.fromJson(withExtraMap.getExtraStr(), type);
                /// 理论上字符串不会为null "null" 因为null会导致跳过实例化,"null"必然为傻叉故意引发的 或者直接错误手动操纵extra字符串导致的
                super.putAll(map == null ? Collections.emptyMap() : map);
            }
            catch (Exception ex)
            {
                log.error("[extra](parse) fail to parse String:{} into Map",withExtraMap.getExtraStr());
                throw ex;
            }
        }
    }
    private void persist()
    {
        Gson gson=new Gson();
        withExtraMap.setExtraStr(gson.toJson(this));
    }

    @Override
    public V put(K key, V value) {
        V res=super.put(key, value);
        persist();
        return res;
    }

    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        super.putAll(m);
        persist();
    }

    @Override
    public V remove(Object key) {
        V res=super.remove(key);
        persist();
        return res;
    }

    @Override
    public void clear() {
        super.clear();
        persist();
    }

    @Override
    public V putIfAbsent(K key, V value) {
        V res=super.putIfAbsent(key, value);
        persist();
        return res;
    }

    @Override
    public boolean remove(Object key, Object value) {
        boolean res=super.remove(key, value);
        persist();
        return res;
    }

    @Override
    public boolean replace(K key, V oldValue, V newValue) {
        boolean res=super.replace(key, oldValue, newValue);
        persist();
        return res;
    }

    @Override
    public V replace(K key, V value) {
        V res=super.replace(key, value);
        persist();
        return res;
    }

    @Override
    public V computeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction) {
        V res=super.computeIfAbsent(key, mappingFunction);
        persist();
        return res;
    }

    @Override
    public V computeIfPresent(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        V res=super.computeIfPresent(key, remappingFunction);
        persist();
        return res;
    }

    @Override
    public V compute(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        V res=super.compute(key, remappingFunction);
        persist();
        return res;
    }

    @Override
    public V merge(K key, V value, BiFunction<? super V, ? super V, ? extends V> remappingFunction) {
        V res=super.merge(key, value, remappingFunction);
        persist();
        return res;
    }

    @Override
    public void replaceAll(BiFunction<? super K, ? super V, ? extends V> function) {
        super.replaceAll(function);
        persist();
    }
}
