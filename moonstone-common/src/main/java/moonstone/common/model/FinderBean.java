package moonstone.common.model;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 查找某个实体Bean
 *
 * @param <I> 索引
 * @param <T> 实体
 */
public interface FinderBean<I, T> {
    /**
     * 查询实体
     *
     * @param index 索引
     * @return 实体
     */
    T findBy(I index);

    /**
     * 列表查询实体
     *
     * @param indexList 索引列表
     * @return 实体列表
     */
    default List<T> findListBy(List<I> indexList) {
        return indexList.stream().map(this::findBy).collect(Collectors.toList());
    }
}
