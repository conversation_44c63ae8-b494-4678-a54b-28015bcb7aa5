package moonstone.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Author: yousx
 * @Date: 2024/12/20
 * @Description:
 */
@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiV3Resp {

    protected String error;
    protected String errorMessage;
    protected Object result;
    protected boolean success;

}
