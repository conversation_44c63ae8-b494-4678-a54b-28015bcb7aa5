package moonstone.common.model;

import moonstone.common.api.B3Hash;

import java.nio.charset.StandardCharsets;

public record ExtraView(Long id,
                        String hash,
                        byte[] extraJson,
                        byte[] tagsJson) implements B3Hash {
    public ExtraView(Long id, String extraJson, String tagsJson, String hash) {
        this(id, hash, extraJson == null? null: extraJson.getBytes(StandardCharsets.UTF_8), tagsJson==null?null: tagsJson.getBytes(StandardCharsets.UTF_8));
    }

    public ExtraView(Long id, byte[] extraJson, byte[] tagsJson, String hash) {
        this(id, hash, extraJson, tagsJson);
    }
}
