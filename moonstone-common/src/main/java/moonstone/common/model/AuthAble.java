package moonstone.common.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;

/**
 * @serial 1-2
 */
public interface AuthAble {
    /**
     * @return 是否已经验证
     */
    default boolean isAuthed() {
        return false;
    }

    /**
     * @return 是否已经被拒绝
     */
    default boolean isReject() {
        return false;
    }

    /**
     * 审核通过
     *
     * @return 操作成功
     */
    default boolean auth() {
        return false;
    }

    /**
     * 审核拒绝
     *
     * @return 操作成功
     */
    default boolean reject() {
        return false;
    }

    /**
     * 回滚审核
     *
     * @return 操作成功
     */
    default boolean revokeAuth() {
        return false;
    }

    /**
     * 初始化验证
     *
     * @return 操作成功
     */
    default boolean initAuth() {
        return false;
    }

    /**
     * @return 获取验证日期
     */
    default Date getAuthAt() {
        return null;
    }

    /**
     * 设置验证日期
     *
     * @param date 审核日期
     */
    default void setAuthAt(Date date) {

    }

    @AllArgsConstructor
    enum AuthStatus    /// 默认情况下使用该验证值
    {
        MASK_CODE(2 + (2 << 1)), /// 掩码 通过这个来掩盖bit码
        AUTHED(2),  /// 验证通过
        REJECT(2 << 1); /// 验证失败
        @Getter
        int value;
    }
}
