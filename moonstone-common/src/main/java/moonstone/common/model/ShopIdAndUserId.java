package moonstone.common.model;

/**
 * 用户与平台Id隔离
 */
public class ShopIdAndUserId {
    Long shopId;
    Long userId;

    public ShopIdAndUserId(Long shopId, Long userId) {
        this.shopId = shopId;
        this.userId = userId;
    }

    public ShopIdAndUserId() {
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
