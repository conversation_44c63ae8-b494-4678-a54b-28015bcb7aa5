package moonstone.common.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
/**
 * 登记中心
 */
public class EntityInformationCenter {
    private final Map<Integer, Class> integerEntityInformationMap = new ConcurrentHashMap<>();
    private final Map<Class, Integer> rIntegerEntityInformationMap = new ConcurrentHashMap<>();
    private Map<String, Integer> simpleRegMap = new HashMap<>();

    @AllArgsConstructor
    @Getter
    public enum RegClass {
        User(0),
        BalanceDetail(1),
        ShopOrder(2),
        SkuOrder(3),
        Refund(4),
        ShopVipInformation(5);
        int key;
    }

    @PostConstruct
    public void init() {
        simpleRegMap = Stream.of(RegClass.values()).collect(Collectors.toMap(RegClass::name, RegClass::getKey));
    }

    /**
     * 利用在注册中心这里编写的枚举类进行简单注册
     *
     * @param clazz 希望被注册的类型
     * @return 是否注册成功
     */
    public Either<Boolean> regSimply(Class clazz) {
        if (simpleRegMap.containsKey(clazz.getSimpleName())) {
            reg(clazz, simpleRegMap.get(clazz.getSimpleName()));
            return Either.ok(true);
        }
        return Either.error(new Translate("该类型不属于可以simpleReg的类型").toString());
    }

    /**
     * 注册某个部件
     *
     * @param tClass 类型
     * @param key    key
     * @param <T>    没有意义的东西
     * @return 返回自己便于注册
     */
    public <T> EntityInformationCenter reg(Class<T> tClass, Integer key) {
        Class contain = integerEntityInformationMap.get(key);
        while (contain == null) {
            integerEntityInformationMap.putIfAbsent(key, tClass);
            contain = integerEntityInformationMap.get(key);
        }
        rIntegerEntityInformationMap.put(tClass, key);
        if (!contain.equals(tClass)) {
            log.error("{} failed to reg the key:{} of:{},because already exists :{}", LogUtil.getClassMethodName(), key, tClass.getName(), integerEntityInformationMap.get(key).getName());
        }
        return this;
    }

    /**
     * 强行组装,请在代码逻辑上保证没有错误,不然将导致比较严重的后果
     *
     * @param key       key
     * @param relatedId 关联的id
     * @param <T>       编译范型
     * @return 组装好的
     */
    public <T> EntityInformation<T> build(Class key, Long relatedId) {
        EntityInformation<T> entityInformation = new EntityInformation<T>();
        entityInformation.setRelatedClass(key);
        entityInformation.setClassSimpleName(Optional.ofNullable(entityInformation.getRelatedClass()).map(Class::getSimpleName).orElse("null"));
        entityInformation.setRelatedId(relatedId);
        entityInformation.setType(rIntegerEntityInformationMap.get(key));
        return entityInformation;
    }

    /**
     * 强行组装,请在代码逻辑上保证没有错误,不然将导致比较严重的后果
     *
     * @param key       key
     * @param relatedId 关联的id
     * @param <T>       编译范型
     * @return 组装好的
     */
    public <T> EntityInformation<T> build(Integer key, Long relatedId) {
        EntityInformation<T> entityInformation = new EntityInformation<T>();
        entityInformation.setRelatedClass(integerEntityInformationMap.get(key));
        entityInformation.setClassSimpleName(Optional.ofNullable(entityInformation.getRelatedClass()).map(Class::getSimpleName).orElse("null"));
        entityInformation.setRelatedId(relatedId);
        entityInformation.setType(key);
        return entityInformation;
    }
}
