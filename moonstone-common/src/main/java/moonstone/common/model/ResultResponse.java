package moonstone.common.model;

import io.terminus.common.model.Response;

import java.util.Objects;

public final class ResultResponse<T> extends Response<T> {
    private final Either<T> result;

    public ResultResponse(Either<T> in) {
        Objects.requireNonNull(in);
        result = in;
        setError(result.getErrorMsg());
        setResult(result.getResult());
        setSuccess(result.isSuccess());
    }

    public Response<T> getResponse() {
        return result.isSuccess() ? Response.ok(result.take()) : Response.fail(result.getErrorMsg());
    }

}
