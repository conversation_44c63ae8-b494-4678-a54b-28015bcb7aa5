package moonstone.common.model;

import java.time.Instant;
import java.util.Date;

/**
 * @serial 1-2
 */
public interface AuthAbleByStatus extends AuthAble {
    Integer getStatus();

    void setStatus(Integer status);

    /// 待审核状态
    default boolean isPending() {
        return (getStatus() & AuthStatus.MASK_CODE.getValue()) == AuthStatus.MASK_CODE.getValue();
    }

    @Override
    default boolean isReject() {
        return (getStatus() & AuthStatus.MASK_CODE.getValue()) == AuthStatus.REJECT.getValue();
    }

    @Override
    default boolean isAuthed() {
        return (getStatus() & AuthStatus.MASK_CODE.getValue()) == AuthStatus.AUTHED.getValue();
    }

    @Override
    default boolean auth() {
        if ((getStatus() & AuthStatus.MASK_CODE.getValue()) == AuthStatus.MASK_CODE.getValue()) {
            setAuthAt(Date.from(Instant.now()));
            setStatus(getStatus() & ~AuthStatus.REJECT.getValue());
            return true;
        }
        return false;
    }

    @Override
    default boolean reject() {
        if ((getStatus() & AuthStatus.MASK_CODE.getValue()) == AuthStatus.MASK_CODE.getValue()) {
            setAuthAt(Date.from(Instant.now()));
            setStatus(getStatus() & ~AuthStatus.AUTHED.getValue());
            return true;
        }
        return false;
    }

    @Override
    default boolean revokeAuth() {
        if ((getStatus() & AuthStatus.MASK_CODE.getValue()) != 0) {
            setAuthAt(null);
            setStatus(getStatus() | AuthStatus.MASK_CODE.getValue());
            return true;
        }
        return false;
    }

    @Override
    default boolean initAuth() {
        setAuthAt(null);
        setStatus(getStatus() | AuthStatus.MASK_CODE.getValue());
        return true;
    }
}
