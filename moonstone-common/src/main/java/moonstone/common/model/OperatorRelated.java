package moonstone.common.model;

import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 获取操作人有关信息
 */
public interface OperatorRelated<T extends OperatorRelated> {
    // 获取操作人的Id
    Long getOperatorId();

    // 设置操作人的Id
    void setOperatorId(Long id);

    /**
     * 自动注入且携带操作人id
     *
     * @param action 执行的动作
     * @param userId 操作人
     * @param <R>    返回类型(不重要只需要不是NUll)
     * @return 返回由Result包裹的结果
     */
    default <R> Either<R> action(Function<T, R> action, Long userId) {
        try {
            return Either.ok(action.apply((T) this));
        } catch (Exception ex) {
            return Either.error(ex);
        } finally {
            setOperatorId(userId);
        }
    }

    /**
     * 自动注入且携带操作人id
     *
     * @param action 执行的动作
     * @param userId 操作人
     * @param <R>    返回类型(不重要只需要不是NUll)
     * @return 返回由Result包裹的结果
     */
    default <R> Either<R> action(BiFunction<Long, T, R> action, Long userId) {
        try {
            return Either.ok(action.apply(userId, (T) this));
        } catch (Exception ex) {
            return Either.error(ex);
        } finally {
            setOperatorId(userId);
        }
    }
}
