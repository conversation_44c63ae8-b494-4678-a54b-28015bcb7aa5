/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.common.model;

import io.terminus.common.model.BaseUser;
import lombok.Data;

import java.io.Serial;
import java.util.List;
import java.util.Map;

/**
 * 通用 User
 */
@Data
public class CommonUser implements BaseUser {
    @Serial
    private static final long serialVersionUID = -2961193418926377287L;
    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 用户类型,0:管理员, 1: 买家, 2: 商家
     */
    private Integer type;

    /**
     * 店铺id, 只有商家才有这个信息
     */
    private Long shopId;

    /**
     * 微分销店铺id, 只有微分销店主才有这个信息
     */
    private Long weShopId;

    /**
     * 用户所有的角色列表
     */
    private List<String> roles;


    /**
     * 额外信息
     */
    private Map<String, String> extra;

    /**
     * 标签信息
     */
    private Map<String, String> tags;

    /**
     * 用于标记是否拥有密码
     */
    private Boolean hasPassword;

    /**
     * 用于标记是否拥有手机号
     */
    private Boolean hasMobile;

    /**
     * 用于标记微分销商城关联分销的状态
     * @see moonstone.common.enums.ParanaUserDistributedStatus
     */
    private Integer distributedStatus;

    /**
     * 1：强制修改 0：提示修改 -1：无需修改
     */
    private Integer passwordStatus;

    /**
     * 获取用户类型名称 含义由子类自行定义
     *
     * @return 用户类型名称
     */
    public String getTypeName() {
        return null;
    }
}
