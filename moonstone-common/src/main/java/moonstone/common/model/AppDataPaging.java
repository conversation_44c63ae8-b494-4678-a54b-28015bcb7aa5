package moonstone.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppDataPaging implements Serializable {

    private static final long serialVersionUID = 1L;

    AppPagination pagination;
    //列表数据
    private List<?> list;

    /**
     * 分页
     *
     * @param list       列表数据
     * @param pagination 分页数据
     */
    public AppDataPaging(List<?> list, AppPagination pagination) {
        this.list = list;
        this.pagination = pagination;
    }

    public AppDataPaging() {
    }

    /**
     * 构造
     */
    public static AppDataPaging build() {
        AppDataPaging appPaging = new AppDataPaging();
        return appPaging;
    }

}
