package moonstone.common.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import moonstone.common.exception.EitherMsgException;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */ /// # Result 结果
/// 并非用于支持null 而是用于处理null的错误结果与包裹抛出,理论上貌似没有什么用,但是在使用非null的情况下可替代null与Optional的存在
/// 如果需要可以提纯其中属性到基类或者其中方法到属性
@Getter
@NoArgsConstructor
public class Either<T> {
    protected Throwable error;
    protected String errorMsg;
    protected T result;
    protected boolean success;

    /// ## Ok 表示结果的生成
    /// 如果值为null 则会生成failed  因为这个不是用于null的机制 如果要与null共存请使用Optional
    public static <R> Either<R> ok(@NotNull R result) {
        return new Either<R>(null, null, result, result != null);
    }

    /// 最基础的失败
    public static <R> Either<R> fail() {
        return new Either<>(new EitherMsgException(), "fail Result", null, false);
    }

    /// 以下三个都是构建错误情况的Result,分别接受不同的值
    /// ## error 生产错误的形式
    public static <R> Either<R> error(@Nullable Throwable throwable, @Nullable String errorMsg) {
        return new Either<R>(throwable == null ? new EitherMsgException(errorMsg) : throwable, errorMsg, null, false);
    }

    /// ## error 生产错误的形式
    public static <R> Either<R> error(@Nullable String errorMsg) {
        return new Either<R>(new EitherMsgException(errorMsg), errorMsg, null, false);
    }

    /// ## error 生产错误的形式
    public static <R> Either<R> error(@Nullable Throwable throwable) {
        return new Either<R>(throwable, throwable == null ? null : throwable.getMessage(), null, false);
    }

    /// ## ifFail 如果错误则生产一个新的值返回
    public Either<T> ifFail(Supplier<? extends T> supplier) {
        Objects.requireNonNull(supplier);
        if (!success) {
            return Either.ok(supplier.get());
        }
        return this;
    }

    /// ## logError 如果出现Exception错误则进行消费错误
    public Either<T> logErrorStr(Consumer<String> logConsumer) {
        Objects.requireNonNull(logConsumer);
        if (!success) {
            logConsumer.accept(this.getErrorMsg());
        }
        return this;
    }

    /// ## logError 如果出现Exception错误则进行消费错误
    public Either<T> logException(Consumer<? super Throwable> logConsumer) {
        Objects.requireNonNull(logConsumer);
        if (!success) {
            logConsumer.accept(this.getError());
        }
        return this;
    }

    /// ## logError 如果出现错误则进行消费错误
    public Either<T> logError(Consumer<Either<T>> logConsumer) {
        Objects.requireNonNull(logConsumer);
        if (!success) {
            logConsumer.accept(this);
        }
        return this;
    }

    /// ## ifSuccess 如果是成功的则调用一个消费者
    public Either<T> ifSuccess(Consumer<? super T> consumer) {
        Objects.requireNonNull(consumer);
        if (success && Objects.nonNull(result)) {
            consumer.accept(result);
        }
        return this;
    }

    /// ## map 转换函数
    /// 如果成功则使用一个转换转换自己,但是不接受null返回值,最好使用Optional包裹可能是null的值
    public <R> Either<R> map(Function<? super T, R> mapper) {
        Objects.requireNonNull(mapper);
        if (success && Objects.nonNull(result)) {
            return Either.ok(mapper.apply(result));
        }
        return Either.error(getError());
    }

    /// flatMap
    /// Monad概念之一 自动解包进入下一个Monad但是由于类型的限制除非实现一个monad类不然无法自动执行
    public <R> Either<R> flatMap(Function<? super T, Either<R>> mapper) {
        Objects.requireNonNull(mapper);
        if (success && Objects.nonNull(result)) {
            return mapper.apply(result);
        }
        return Either.error(getError());
    }

    /// orElse
    /// 如果是错误不可用的则用另外的值代替
    public T orElse(T value) {
        return success ? result : value;
    }

    /// orElseGet
    /// 如果是错误不可用的则调用生产者生产
    public T orElseGet(Supplier<? extends T> supplier) {
        Objects.requireNonNull(supplier);
        return success ? result : supplier.get();
    }

    public T elseThrow() throws RuntimeException {
        if (success) {
            return result;
        }
        throw new RuntimeException(error.getMessage(), error.getCause());
    }

    public T elseMap(Function<Throwable, T> errorMap) {
        if (!success) {
            return errorMap.apply(error == null ? new RuntimeException(errorMsg) : error);
        }
        return take();
    }

    /// ## elseThrow
    /// 如果是错误不可用的则抛出错误
    public <X extends Throwable> T elseThrow(Function<String, ? extends X> throwableSupplier) throws X {
        Objects.requireNonNull(throwableSupplier);
        if (success) {
            return result;
        }
        throw throwableSupplier.apply(errorMsg);
    }

    /// ## elseThrow
    /// 如果是错误不可用的则抛出错误
    public <X extends Throwable> T elseThrow(Supplier<? extends X> throwableSupplier) throws X {
        Objects.requireNonNull(throwableSupplier);
        if (success) {
            return result;
        }
        throw throwableSupplier.get();
    }

    /// ## getResult 获取一个值
    public T take() {
        if (success) {
            return result;
        }
        String errorMsg = this.errorMsg == null ? "Empty Result" : this.errorMsg;
        if (this.error == null) {
            throw new EitherMsgException(errorMsg);
        } else {
            throw new RuntimeException(errorMsg, this.error);
        }
    }

    /// ## 构造函数
    /// 构造一个Result模型,不接受也不生成可能返回的null值
    private Either(Throwable error, String errorMsg, T result, Boolean success) {
        this.result = result;
        this.success = success;
        if (!success) {
            /// user error first
            if (error != null) {
                this.error = error;
            } else {
                /// if error==null and result == null so NullPointerException;
                this.error = new EitherMsgException(errorMsg);
            }
            /// if errorMsg==null use the error occupy method.
            this.errorMsg = errorMsg == null ? this.error.getStackTrace()[2].getMethodName() : errorMsg;
        } else {
            /// success 情况下调用非Bean代码不会导致错误
            this.error = null;
            this.errorMsg = null;
        }
    }

    /// ## intoOptional 转换为Optional
    /// 因为Optional会筛选null 所以result为null的情况直接返回empty即可
    /// *into* rust命名法 不客气
    public Optional<T> intoOptional() {
        if (success && result != null) {
            return Optional.of(take());
        }
        return Optional.empty();
    }

    /// ## hashCode 如果有值会返回值的hashCode否则是0
    /// `Objects.hashCode`已经处理了这个null情况,错误情况下的hashCode均为0
    @Override
    public int hashCode() {
        return Objects.hashCode(result);
    }

    /// ## equals 判断是否相等 当然是要全部判断啦
    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof Either)) {
            return false;
        }
        Either<?> otherResult = (Either) obj;
        return Objects.equals(success, otherResult.success)
                && Objects.equals(result, otherResult.result)
                && Objects.equals(error, otherResult.error)
                && Objects.equals(errorMsg, otherResult.errorMsg);
    }

    /// ## toString 返回一个可以描述该类的字符串 不可用于序列化哦
    @Override
    public String toString() {
        return success ?
                String.format("Result[%s]", result == null ? "null" : result.toString())
                :
                String.format("Result[Fail,Error=%s]", errorMsg == null ? error == null ? "null" : error.getMessage() : errorMsg);
    }

    /**
     * set error, should only used by JSON or Gson (other serialize tool)
     *
     * @param error error
     * @see Either#ok(Object)
     * @see Either#error(Throwable)
     */
    @Deprecated
    public void setError(Throwable error) {
        this.error = error;
    }

    /**
     * set errorMsg, should only usedBy JSON or Gson(other serialize tool)
     *
     * @param errorMsg errorMsg
     * @see Either#error(String)
     * @see Either#error(Throwable)
     */
    @Deprecated
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }


    /**
     * set result should only usedBy JSON or Gson(other serialize tool)
     *
     * @param result result
     * @see Either#ok(Object)
     */
    @Deprecated
    public void setResult(T result) {
        this.result = result;
    }

    /**
     * set success should only usedBy JSON or Gson(other serialize tool)
     *
     * @param success is success
     * @see Either#ok(Object)
     * @see Either#error(String)
     */
    @Deprecated
    public void setSuccess(boolean success) {
        this.success = success;
    }
}
