package moonstone.common.model;

import moonstone.common.api.APIResp;

import java.util.ArrayList;
import java.util.List;

/**
 * PageView for App response
 * when method return Type, suggest use <code>AppResponse<List<T>></code> not <code>AppPaging<T></code>
 *
 * @param <T>
 */
public interface CommonPaging<T> extends APIResp<T> {

    static <T> CommonPaging<List<T>> empty(int page, int pageSize) {
        return sliceOf(0, 0, page, pageSize, new ArrayList<>());
    }

    static <T> CommonPaging<List<T>> empty() {
        return sliceOf(0, 0, 1, 20, new ArrayList<>());
    }

    static <T> CommonPaging<List<T>> sliceOf(Integer total, Integer totalCount, Integer currentPage, Integer pageSize, List<T> data) {
        return new CommonPaging<List<T>>() {
            @Override
            public Pagination getPagination() {
                return new SimplePagination(currentPage, pageSize, total, totalCount);
            }

            @Override
            public int getCode() {
                return 0;
            }

            @Override
            public String getErrorMsg() {
                return "success";
            }

            @Override
            public List<T> getData() {
                return data;
            }
        };
    }

    Pagination getPagination();

    interface Pagination {
        Integer getCurrentPage();

        Integer getPageSize();

        Integer getTotal();

        Integer getTotalCount();
    }

    class SimplePagination implements Pagination {
        Integer currentPage;
        Integer pageSize;
        Integer total;
        Integer totalCount;

        public SimplePagination(Integer currentPage, Integer pageSize, Integer total, Integer totalCount) {
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.total = total;
            this.totalCount = totalCount;
        }

        @Override
        public Integer getCurrentPage() {
            return currentPage;
        }

        @Override
        public Integer getPageSize() {
            return pageSize;
        }

        @Override
        public Integer getTotal() {
            return total;
        }

        @Override
        public Integer getTotalCount() {
            return totalCount;
        }
    }
}
