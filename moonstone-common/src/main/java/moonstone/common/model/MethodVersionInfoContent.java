package moonstone.common.model;

import java.util.Map;

/**
 * 用于方法版本切换
 */
public class MethodVersionInfoContent {
    String className;
    Map<String, String> methodVersion;

    public MethodVersionInfoContent() {
    }

    public MethodVersionInfoContent(String className, Map<String, String> methodVersion) {
        this.className = className;
        this.methodVersion = methodVersion;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Map<String, String> getMethodVersion() {
        return methodVersion;
    }

    public void setMethodVersion(Map<String, String> methodVersion) {
        this.methodVersion = methodVersion;
    }
}
