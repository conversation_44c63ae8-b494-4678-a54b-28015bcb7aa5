package moonstone.common.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

@Data
public abstract class EntityBase implements WithExtraMap<String, String> {
    /// 实体id
    Long id;
    /// 实体创建时间
    Date createdAt;
    /// 实体修改时间
    Date updatedAt;
    /// 额外字段json
    @JsonIgnore
    String extraStr;
    /// 状态
    Integer status = 1;
    /// 乐观锁
    String lock;
}
