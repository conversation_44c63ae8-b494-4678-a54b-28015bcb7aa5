package moonstone.common.model.image.slice;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface ImageDomainSplitter {
    String HTTPS_HEAD = "HTTPS://";
    String HTTP_HEAD = "HTTP://";

    /**
     * 拆分图片地址
     *
     * @param url 图片地址
     * @return 被拆下domain的图片地址
     */
    static String splitDomain(String url) {
        String domainBody;
        if (url.toUpperCase().startsWith(HTTPS_HEAD)) {
            domainBody = url.substring(HTTPS_HEAD.length());
        } else if (url.toUpperCase().startsWith(HTTP_HEAD)) {
            domainBody = url.substring(HTTP_HEAD.length());
        } else {
            return url;
        }
        return domainBody.substring(domainBody.indexOf("/"));
    }

    /**
     * 打包上头部
     *
     * @param domain 带有domain头的地址
     * @param url    没有domain头的地址
     * @return 完整地址
     */
    static String completeDomain(String domain, String url) {
        if (Objects.isNull(url)) {
            return null;
        }
        if (url.toUpperCase().startsWith(HTTPS_HEAD)) {
            return url;
        } else if (url.toUpperCase().startsWith(HTTP_HEAD)) {
            return url;
        }
        int protocolEnd = domain.toUpperCase().startsWith(HTTPS_HEAD)
                ? HTTPS_HEAD.length() : domain.toUpperCase().startsWith(HTTP_HEAD) ? HTTP_HEAD.length() : -1;
        String urlPath = url.startsWith("/") ? url : "/" + url;
        int pathStart = domain.indexOf("/", protocolEnd);
        return domain.substring(0, pathStart > 0 ? pathStart : domain.length()) + urlPath;
    }
}
