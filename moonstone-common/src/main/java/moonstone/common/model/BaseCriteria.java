/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.common.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import org.springframework.beans.BeanUtils;

import java.beans.PropertyDescriptor;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * Date: 2016-04-22
 */
public abstract class BaseCriteria {

    protected static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        //设置输出时包含属性的风格
        MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //注册guava类型
        MAPPER.registerModule(new GuavaModule());
        //规范化日期类型
        MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> toMap() {
        Map<String, Object> map = MAPPER.convertValue(this, Map.class);
        for (PropertyDescriptor propertyDescriptor : BeanUtils.getPropertyDescriptors(getClass())) {
            if (Date.class == propertyDescriptor.getPropertyType()) {
                Optional.ofNullable(map.get(propertyDescriptor.getName()))
                        .map(Object::toString)
                        .map(Timestamp::valueOf)
                        .ifPresent(timestamp -> map.put(propertyDescriptor.getName(), timestamp));
            }
        }
        return map;
    }
}
