package moonstone.common.model;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.utils.MD5Util;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Data
public class Y800OpenRequest {
    String serviceName;
    String v = "2.0";
    // 由洋800平台分配
    String partnerId;
    // 业务数据转json字符串后base加密后的字符串,具体字段见下-bizData字段内容
    String bizData;
    // 上述参数连接后md5加密后的字符串（加密方法见签名算法）
    String sign;

    public void setBizData(BizData bizData) {
        this.bizData = bizData.encoded();
    }

    public void setBizData(String bizData) {
        this.bizData = Base64.getEncoder().encodeToString(bizData.getBytes());
    }

    public Optional<BizData> rawBizData(Class<?> clazz) {
        return bizData == null || bizData.isEmpty() ?
                Optional.empty() : Optional.ofNullable(JSON.parseObject(new String(Base64.getDecoder().decode(bizData.getBytes())), BizData.class));
    }

    /**
     * 使用已有数据签名
     *
     * @param privateKey 密钥
     * @apiNote 在setBizData调用
     */
    public void sign(String privateKey) {
        String strBuff = "bizData=" + bizData + "partnerId=" + partnerId + "serviceName=" + serviceName + "v=" + v + privateKey;
        this.sign = MD5Util.MD5(strBuff);
    }

    public Map<String, String> toMap() {
        Map<String, String> resultMap = new HashMap<>();
        new Gson().toJsonTree(this).getAsJsonObject()
                .entrySet().iterator().forEachRemaining(entry -> resultMap.put(entry.getKey(), entry.getValue().getAsString()));
        return resultMap;
    }

    // Test
    public static void main(String[] args) {
        Y800OpenRequest request = new Y800OpenRequest();
        request.setBizData(new BizData() {
        });
        System.out.println(request.toMap());

        System.out.println(BizData.from("ash", ImmutableMap.of("ok", "yes", "no", "Fuck")));
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class BizDataByJson extends BizData {
        JsonElement jsonElement;

        BizDataByJson(JsonElement jsonElement) {
            this.jsonElement = jsonElement;
        }

        @Override
        String encoded() {
            return encoder.encodeToString(gson.toJson(jsonElement).getBytes());
        }
    }

    @Data
    public static abstract class BizData {
        static Gson gson = new Gson();
        static Base64.Encoder encoder = Base64.getEncoder();
        //	洋800为应用分配的访问编号，用以处理同一应用下的多供应商/多渠道的情况
        String accessCode;
        // 	推送订单时的商户平台订单编号

        /**
         * {@link moonstone.common.utils.OutSystemIdProvider}
         * 目前使用这边的单号
         */
        protected BizData() {
        }

        String encoded() {
            return encoder.encodeToString(JSON.toJSONString(this).getBytes());
        }

        /**
         * 直接加密一个数据出去
         *
         * @param accessCode 访问码
         * @param bizData    真正的内容
         * @return 加密后的数据
         */
        public static String from(String accessCode, Object bizData) {
            if (bizData == null) {
                BizData empty = new BizData() {
                };
                empty.setAccessCode(accessCode);
                return gson.toJson(empty);
            }

            JsonElement element = gson.toJsonTree(bizData);

            JsonObject argsObject;
            if (element.isJsonObject()) {
                argsObject = element.getAsJsonObject();
            } else {
                // auto add Data
                argsObject = new JsonObject();
                argsObject.add("data", element);
            }
            argsObject.addProperty("accessCode", accessCode);
            return gson.toJson(argsObject);
        }
    }
}
