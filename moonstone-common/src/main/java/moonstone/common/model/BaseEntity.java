package moonstone.common.model;

import lombok.Data;

import java.util.Date;

@Data
public class BaseEntity {

    /**
     * '扩展字段'
     */
    private String extraJson;

    /**
     * '数据是否有效(1-是, 2-否)'
     *
     * @see moonstone.common.enums.DataValidEnum
     */
    private Integer isValid;

    /**
     * '创建时间'
     */
    private Date createdAt;

    /**
     * '最后更新时间'
     */
    private Date updatedAt;

    /**
     * '创建人'
     */
    private Long createdBy;

    /**
     * '最后更新人'
     */
    private Long updatedBy;
}
