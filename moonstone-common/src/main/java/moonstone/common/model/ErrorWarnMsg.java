package moonstone.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import moonstone.common.utils.LogUtil;

@AllArgsConstructor
@Data
public class ErrorWarnMsg {
    Boolean error;
    String title;
    String receiverGroup;
    String[] receiver;
    String content;
    String errorDetail;

    /**
     * 制定多个收件人的信息
     *
     * @param title     标题
     * @param content   内容
     * @param receivers 收件人列表
     * @return 信息实体
     */
    public static ErrorWarnMsg singleMsg(String title, String content, String... receivers) {
        return singleMsg(title, content, null, receivers);
    }

    public static ErrorWarnMsg singleMsg(String title, String content) {
        return singleMsg(title, content, null, null);
    }

    /**
     * 携带错误信息的指定多人发信信息
     *
     * @param title     标题
     * @param content   内容
     * @param throwable 错误信息
     * @param receiver  收件人
     * @return 信息实体
     */
    public static ErrorWarnMsg singleMsg(String title, String content, Throwable throwable, String... receiver) {
        return new ErrorWarnMsg(throwable != null, title, null, receiver, content, throwable == null ? null : LogUtil.express(throwable));
    }

    /**
     * 制定某个收件群的信息实体
     *
     * @param title         标题
     * @param content       消息内容
     * @param receiverGroup 收件群
     * @return 信息实体
     */
    public static ErrorWarnMsg groupMsg(String title, String content, String receiverGroup) {
        return groupMsg(title, content, null, receiverGroup);
    }

    /**
     * 制定某个收件群的信息实体
     *
     * @param title         标题
     * @param throwable     携带信息
     * @param content       消息内容
     * @param receiverGroup 收件群
     * @return 信息实体
     */
    public static ErrorWarnMsg groupMsg(String title, String content, Throwable throwable, String receiverGroup) {
        return new ErrorWarnMsg(throwable != null, title, receiverGroup, null, content, throwable == null ? null : LogUtil.express(throwable));
    }

}
