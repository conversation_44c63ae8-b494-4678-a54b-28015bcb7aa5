package moonstone.common.model;

import cn.hutool.core.bean.BeanUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class IdNameVO {
    private Object id;
    private Object name;

    /**
     * 构建ID-Name结果
     *
     * @param list
     * @param idKey
     * @param nameKey
     * @return
     */
    public static List<IdNameVO> build(List<?> list, String idKey, String nameKey) {
        if (list == null) {
            return null;
        }

        List<IdNameVO> resultList = new ArrayList<>();
        for (Object o : list) {
            Object keyValue = BeanUtil.getFieldValue(o, idKey);
            Object nameValue = BeanUtil.getFieldValue(o, nameKey);

            IdNameVO idNameVO = new IdNameVO();
            idNameVO.setId(keyValue);
            idNameVO.setName(nameValue);

            resultList.add(idNameVO);
        }

        return resultList;
    }
}
