package moonstone.common.model;


import lombok.Data;
import moonstone.common.utils.SimpleConditionExecutor;
import moonstone.common.utils.Translate;

import java.util.*;
import java.util.function.Function;

/**
 * 条件树
 *
 * @param <T> 内嵌的条件判断实体
 * @apiNote 注意condition为null的情况，在自动解析时必须要注意这一点
 * 这是个条件树,仅仅承载条件,解析与进一步解析是必要的
 * @see SimpleConditionExecutor  通过这个来执行自动解析
 */
@Data
public class Condition<T> {
    // true=and;false=or
    // 表示为这个和前者的连接关系
    boolean and = false;
    // 是否取反
    boolean anti = false;
    // 条件(目前用于Id)
    T condition;
    // 条件 下一个
    Condition<T> next;
    // 条件 内敛
    Condition<T> inner;

    // 如何将条件嵌套起来呢？ 编译原理白学
    // A&&(B||C)=AB|AC 所以碰到false便可以跳过接下来的判断 除非是 and=false
    // A(B|C)= (A,o,(B,a,_,(C,o,_,_)),_)

    /**
     * 自动解析条件构造为条件体
     *
     * @param constuctor   将单个条件体解析为实体
     * @param conditionStr 条件数据实体
     * @param <R>          返回的条件容纳类型
     * @return 可自动解析的条件链表
     * @throws RuntimeException 解析错误的异常，只要不是手动写的基本不会有,因此采取RuntimeException
     */
    public static <R> Condition<R> parseCondition(Function<String, Optional<R>> constuctor, String conditionStr) throws RuntimeException {
        // 状态流设置
        final int constructEntity = 0;
        final int readOp = 2;
        final int readBracket = 3;
        final int closeBracket = 1;
        // 基础匹配字符串 &为and |为or （）为优先级
        final char mAnd = '&';
        final char mOr = '|';
        final char mbraS = '(';
        final char mbraE = ')';
        final char mAnti = '!';
        Set<Character> fSet = new HashSet<>(Arrays.asList(mAnd, mOr, mbraE, mbraS));
        // 替代真堆栈的堆,解析最怕暴栈
        Stack<Condition<R>> stack = new Stack<>();
        // 目前读写位置
        int step = 0;
        // 转换为数组方便遍历
        char[] rawChar = conditionStr.toCharArray();
        // 链表根
        Condition<R> head = new Condition<>();
        Condition<R> cons = head;
        // 由于可以直接抛出runtimeException就不加标签了
        int stepI = 0;
        int workFlow = readBracket;
        try {
            while (step < rawChar.length) {
                switch (workFlow) {
                    case constructEntity: {
                        // 读取实体
                        for (stepI = 0; step + stepI < rawChar.length; stepI++) {
                            if (fSet.contains(rawChar[stepI])) {
                                break;// 找到了，跳出这个循环吧
                            }
                        }
                        // 故意的无判断get 使得构造失败自动抛出
                        //noinspection OptionalGetWithoutIsPresent
                        cons.setCondition(constuctor.apply(new String(rawChar, step, stepI)).get());
                        workFlow++;
                        step += stepI;
                        break;
                    }
                    case readOp: {
                        workFlow++;
                        Condition<R> pre = cons;
                        cons = new Condition<>();
                        if (rawChar[step] == mAnd) {
                            cons.setAnd(true);
                        } else if (rawChar[step] == mOr) {
                            cons.setAnd(false);
                        } else {
                            throw new RuntimeException(new Translate("未意料到的%c,此处应该为%c或者%c", rawChar[step], mAnd, mOr).toString());
                        }
                        pre.setNext(cons);
                        step++;
                        break;
                    }
                    case readBracket: {
                        while (step < rawChar.length && rawChar[step] == mAnti) {
                            cons.anti = !cons.anti;
                            step++;
                        }
                        while (step < rawChar.length && rawChar[step] == mbraS) {
                            Condition<R> pre = cons;
                            cons = new Condition<>();
                            pre.setInner(cons);
                            stack.push(pre);
                            step++;
                        }
                        workFlow = constructEntity;
                        break;
                    }
                    case closeBracket: {
                        workFlow++;
                        while (step < rawChar.length && rawChar[step] == mbraE) {
                            if (!stack.isEmpty()) {
                                cons = stack.pop();
                            } else {
                                throw new RuntimeException(new Translate("未意料到的%c,括号不对齐", mbraE).toString());
                            }
                            step++;
                        }
                        break;
                    }
                    default: {
                        throw new RuntimeException(new Translate("未知的工作状态").toString());
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            String working;
            switch (workFlow) {
                case constructEntity: {
                    working = new Translate("构建内部实体失败").toString();
                    break;
                }
                case readOp: {
                    working = new Translate("读取操作符号失败").toString();
                    break;
                }
                case readBracket: {
                    working = new Translate("试图判断是否内嵌失败").toString();
                    break;
                }
                case closeBracket: {
                    working = new Translate("试图判断是否退出内嵌失败").toString();
                    break;
                }
                default: {
                    working = new Translate("未知操作状态").toString();
                    break;
                }
            }
            working = working + " at " + (step + stepI);
            throw new RuntimeException(working + "\n" + ex.getMessage());
        }
        return head;
    }
}
