package moonstone.common.model;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.CommonSqlConstant;
import moonstone.common.exception.ApiException;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * author：书生
 */
@Slf4j
public abstract class BaseQuery {

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        try {
            for (Field field : getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object value = field.get(this);
                if (ObjUtil.isEmpty(value)) {
                    continue;
                }
                String name = field.getName();
                Class<?> type = field.getType();
                if (type.equals(Date.class)) {
                    String time = DateUtil.formatDateTime((Date) value);
                    map.put(name, time);
                } else {
                    map.put(name, value);
                }
            }
            map.put(CommonSqlConstant.SORT_BY, "id");
            map.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_ASC);
            log.info("查询条件 {}", JSONUtil.toJsonStr(map));
            return map;
        } catch (Exception e) {
            log.error("查询条件转换失败 {}", e.getMessage(), e);
            throw new ApiException("查询条件转换失败");
        }

    }

}
