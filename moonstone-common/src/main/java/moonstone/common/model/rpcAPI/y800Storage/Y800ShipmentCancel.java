package moonstone.common.model.rpcAPI.y800Storage;

import lombok.Data;

@Data
public class Y800ShipmentCancel {

    private String accessCode;//访问代码	string	是	洋800为应用分配的访问编号，用以处理同一应用下的多客户的情况

    private String thirdNo;//第三方电商平台订单号	string	是

    private String whCode;//仓库编码	string	否

    private String type;//类型	string	否	stockout - 出库单取消,stockin-入库单取消, delivery-发货单取消, 默认为发货单取消

    private String reason;//取消原因	string	否
}
