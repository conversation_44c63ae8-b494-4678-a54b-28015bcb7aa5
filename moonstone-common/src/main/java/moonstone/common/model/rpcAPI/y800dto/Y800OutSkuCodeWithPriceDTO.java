package moonstone.common.model.rpcAPI.y800dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Y800OutSkuCodeWithPriceDTO {
    String outCode;
    String code;
    BigDecimal price;

    public Y800OutSkuCodeWithPriceDTO(String code, BigDecimal price) {
        this.outCode = code;
        this.code = code;
        this.price = price;
    }
}
