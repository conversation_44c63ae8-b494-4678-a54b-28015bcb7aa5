package moonstone.common.model.rpcAPI.y800Storage;

import lombok.Data;

@Data
public class Y800GoodsAndRecordQueryRequest {

    /**
     * 访问代码
     * <br/>
     * 洋800为应用分配的访问编号，用以处理同一应用下的多客户的情况
     * <br/>
     * 是否必填：是
     */
    private String accessCode;

    /**
     * 口岸
     * <br/>
     * 如果查备案信息此字段为必填，如未传则返回为空 (如：原产国字段)
     * <br/>
     * 是否必填：否
     */
    private String port;

    /**
     * 页数
     * <br/>
     * 是否必填：	是
     */
    private Integer page;

    /**
     * 大小
     * <br/>
     * 是否必填：	是
     */
    private Integer size;
}
