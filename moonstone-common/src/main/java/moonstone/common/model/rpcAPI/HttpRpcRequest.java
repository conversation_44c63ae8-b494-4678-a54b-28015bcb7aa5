package moonstone.common.model.rpcAPI;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import moonstone.common.utils.MD5Util;

import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;

public interface HttpRpcRequest {
    String serviceName();

    String appId();

    default String secret() {
        return "*********";
    }

    String data();

    String sign();

    String version();

    Map<String, String> toParam();

    record OMSRequest(String serviceName,
                      String appId,
                      String v,
                      String bizData,
                      String sign) implements HttpRpcRequest {

        public OMSRequest(String appId, String serviceName, Object data, String secret) {
            this(appId,
                    serviceName,
                    "3.0",
                    data instanceof String ? data.toString() : Context.gson.toJson(data),
                    sign(appId, serviceName,
                            data instanceof String ? data.toString() : Context.gson.toJson(data),
                            secret));
        }

        public static void main(String[] args) {
            var a = new HttpRpcRequest.OMSRequest("1234", "aoea", "aoeaoe", "aoeaoe");
        }

        static String sign(String appId, String serviceName, String bizData, String secret) {
            var raw = new OMSRequest(appId, serviceName, "3.0", bizData, null);
            Map<?,?> signParam = Context.gson.fromJson(Context.gson.toJson(raw), Map.class);
            signParam.remove("sign");
            var builder = new StringBuilder();
            signParam.forEach((name, value) -> builder.append(name).append("=").append(value));
            //noinspection deprecation
            return Objects.requireNonNull(MD5Util.MD5(builder + secret)).toLowerCase(Locale.ROOT);
        }

        @Override
        public Map<String, String> toParam() {
            var json = Context.gson.fromJson(Context.gson.toJson(this), JsonObject.class);
            return new TreeMap<>(json.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toString())));
        }

        @Override
        public String data() {
            return bizData;
        }

        @Override
        public String version() {
            return v;
        }

        interface Context {
            Gson gson = new Gson();
        }
    }
}
