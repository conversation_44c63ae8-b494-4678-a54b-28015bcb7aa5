package moonstone.common.model.rpcAPI.y800dto;

import lombok.Data;

import java.math.BigDecimal;

// 其中如果用,隔开则
@Data
public class Y800ThirdSkuDataDTO {
    // 商品名称
    String name;
    // 商品洋800商品代号
    String outCode;
    String code;
    // 商品规格
    BigDecimal specification;
    // 计量单位
    String unit;
    // 原产地
    String origin;
    // 商品状态
    Integer status;
    // 商品状态名
    String statusName;
    // 体积
    String volume;
    // 重量
    String weight;
    // 图片Url
    String image;
    // 品牌
    String brand;
    // 商品交易类型
    //1跨境保税 , 2为普通商品 3跨境直邮
    String tradeType;
    String depotName;
    String depotCode;
}
