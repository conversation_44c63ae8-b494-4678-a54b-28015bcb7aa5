package moonstone.common.model.rpcAPI.y800Storage;

import lombok.Data;

@Data
public class Y800ShipmentOrderSku {
    String skuNo;            //洋800平台商品编码	string	否	skuNo和barCode必传一个
    String skuName;            //商品名称	string	否
    String barCode;            //商品条码	String	否	skuNo和barCode必传一个
    String num;            //商品数量	String	是
    String goodsAmount;            //此商品项总价	string	否	跨境和混合订单必填
    String tax;            //此商品项总税费	string	否	跨境和混合订单必填
    String discount;            //此商品项总优惠	string	否	跨境和混合订单必填
    String shipFee;            //此商品项总运费	string	否	跨境和混合订单必填
    String inventoryType;            //库存类型	string	否	正品ZP,次品CP,默认是正品
    String hsCode;            //hs编码	string	否
    String originCountry;            //产销国	string	否
    String goodsModel;            //商品规格、型号	string	否
}
