package moonstone.common.model.rpcAPI.y800Storage;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2024/12/20
 * @Description:
 */
@Data
public class Y800JdInventoryRes implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    // 库存状态。0：无货 1：有货 2：采购中
    private Integer areaStockState;

    private String leadTime;

    private SkuQuantity skuQuantity;

    @Data
    public static class SkuQuantity implements Serializable {

        @Serial
        private static final long serialVersionUID = 2L;

        private String skuId;

        private Integer quantity;
    }

}
