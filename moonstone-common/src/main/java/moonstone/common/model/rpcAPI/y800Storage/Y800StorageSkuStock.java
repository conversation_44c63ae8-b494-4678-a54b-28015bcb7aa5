package moonstone.common.model.rpcAPI.y800Storage;

import lombok.Data;

@Data
public class Y800StorageSkuStock {
    String skuNo;//商品sku	string	是
    String batchCode;//批次号	string	否
    Integer num;//库存数	int	是 在仓库的总库存，包含可售数量+锁定数量
    Integer lockNum; // 锁定数量

    /**
     * @see moonstone.common.model.rpcAPI.enums.Y800V3SkuStockInventoryTypeEnum
     */
    String inventoryType;//库存类型	string	是	正品/次品

    /**
     * 仓库名称 (目前仅跨境支持)
     */
    String warehouseName;

    /**
     * 仓库编码 (目前仅跨境支持)
     */
    String warehouseSn;

    /**
     * 正品可售 (目前仅跨境支持)
     */
    Integer availableNum;

    /**
     * 次品可售 (目前仅跨境支持)
     */
    Integer defectiveAvailableNum;
}
