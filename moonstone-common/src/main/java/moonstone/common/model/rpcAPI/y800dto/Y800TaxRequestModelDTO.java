package moonstone.common.model.rpcAPI.y800dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用于Y800外部接口调用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Y800TaxRequestModelDTO {
    List<Y800OutSkuCodeWithPriceDTO> infoList;
    String split;

    String accessCode;

    public Y800TaxRequestModelDTO(List<Y800OutSkuCodeWithPriceDTO> infoList) {
        this.infoList = infoList;
    }

    public Y800TaxRequestModelDTO(List<Y800OutSkuCodeWithPriceDTO> infoList, String accessCode) {
        this.infoList = infoList;
        this.accessCode = accessCode;
    }
}
