package moonstone.common.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ParanaUserProfile implements Serializable {

    private static final long serialVersionUID = 0;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 头像图片URL
     */
    private String avatar;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 性别, 1: 男, 2: 女, 0 or null: 未知
     */
    private Integer gender;

    /**
     * 生日 (天)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd",timezone = "CTT")
    private Date birthday;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 市
     */
    private String city;

    /**
     * 区id
     */
    private Integer regionId;

    /**
     * 区
     */
    private String region;

    /**
     * 街道
     */
    private String street;

    /**
     * 其他参数
     */
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private Map<String, Object> other = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> any() {
        return other;
    }

    @JsonAnySetter
    public void set(String key, Object value) {
        other.put(key, value);
    }
}
