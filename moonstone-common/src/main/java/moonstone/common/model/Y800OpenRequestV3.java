package moonstone.common.model;

import com.google.gson.Gson;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.MD5Util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Data
public class Y800OpenRequestV3 {
    static final Gson gson = new Gson();
    String serviceName;
    String v = "3.0";
    // 由洋800平台分配
    String appId;
    // 业务数据转json字符串后base加密后的字符串,具体字段见下-bizData字段内容
    String bizData;
    // 上述参数连接后md5加密后的字符串（加密方法见签名算法）
    String sign;

    // Test
    public static void main(String[] args) {
        Y800OpenRequestV3 requestV3 = new Y800OpenRequestV3();
        requestV3.setAppId("1688");
        requestV3.setBizData("W3siY2l0eSI6IuadreW3njEiLCJjb3VudCI6M31d");
        requestV3.setV("4.0");
        requestV3.setServiceName("order.cancel");
        requestV3.sign("1234");
        System.out.println(requestV3.getSign().equals("4c35edd2e926e6ec5ffb2fdf1de1633a"));

        requestV3.setAppId("WEBSCV3");
        requestV3.setBizData("eyJ3aENvZGUiOiJETUpIWVQiLCJzaXplIjoxLCJhY2Nlc3NDb2RlIjoiRE1KSFlUIiwicGFnZSI6MX0");
        requestV3.setV("3.0");
        requestV3.setServiceName("goods.query");
        requestV3.sign("87d6809dbca3e96b4334abae02f7ab99");
        System.out.println(requestV3.getSign());
        System.out.println(requestV3.toMap());
    }

    public void setBizData(Object data) {
        this.bizData = Base64.getEncoder().encodeToString(gson.toJson(data).getBytes(StandardCharsets.UTF_8));
    }

    public void setBizData(String bizData) {
        this.bizData = bizData;
    }

    public <T> T decodeBizData(Class<T> clazz) {
        return bizData == null || bizData.isEmpty() ?
                null : gson.fromJson(new String(Base64.getDecoder().decode(bizData.getBytes()), StandardCharsets.UTF_8), clazz);
    }

    /**
     * 使用已有数据签名
     *
     * @param privateKey 密钥
     * @apiNote 在setBizData调用
     */
    public void sign(String privateKey) {
        var map = new TreeMap<Object, Object>(gson.fromJson(gson.toJson(this), Map.class));
        StringBuilder builder = new StringBuilder();
        map.remove("sign");
        map.forEach((k, v) -> builder.append(v == null ? "" : String.format("%s=%s", k, v)));
        map.forEach((k, v) -> log.debug("[Sign] [{} => {}]", k, v));
        builder.append(privateKey);
        log.debug("[Sign] data [{}]", builder);
        sign = MD5Util.MD5(builder.toString()).toLowerCase(Locale.ROOT);
    }

    public Map<String, String> toMap() {
        var json = gson.toJson(this);
        return gson.fromJson(json, Map.class);
    }
}
