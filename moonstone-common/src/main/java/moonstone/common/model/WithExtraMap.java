package moonstone.common.model;

import com.google.gson.Gson;

import java.util.Map;

/// # 自动获取Map
public interface WithExtraMap<K, V> {
    Gson gson = new Gson();

    /// 依赖项
    String getExtraStr();

    /// 依赖项
    void setExtraStr(String str);

    /// 获取一个可以自动填充的map
    default Map<K, V> getExtra() {
        return new ExtraMap<>(this);
    }

    /// 设置填充一个map
    default void setExtra(Map<K, V> extra) {
        if (extra == null)
            setExtraStr(null);
        else
            setExtraStr(gson.toJson(extra));
    }
}
