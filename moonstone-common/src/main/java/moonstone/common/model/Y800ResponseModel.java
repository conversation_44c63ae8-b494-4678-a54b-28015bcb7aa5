package moonstone.common.model;

import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Optional;

@Data
public class Y800ResponseModel<T> {
    /**
     * 请求成功标识
     * success-成功
     * fail-失败
     */
    private String code;

    /**
     * 错误代码
     */
    private String actionCode;

    /**
     * 错误描述
     */
    private String errorMsg;

    /**
     * 业务数据
     * 当code为success时有返回
     */
    private T data;

    //code
    public static final String CODE_SUCCESS = "success";
    public static final String CODE_FAIL = "fail";

    //actionCode
    public static final String CODE_INVALID_PARTNER = "invalid partner"; //商户错误
    public static final String CODE_INVALID_PARAMS = "invalid param"; //信息错误
    public static final String CODE_SIGN_ERROR = "sign error";
    public static final String CODE_ACCESSCODE_ERROR = "accessCode error";
    public static final String CODE_NO_PERMISSION = "permission deny";
    public static final String CODE_PURCHASING_LIMIT = "purchasing limit";
    public static final String CODE_UNKNOWN = "unknow";
    public static final String CODE_UNAVAILABLE_SERVICE = "unavailable service";
    public static final String RESULT_NOT_FOUND = "result not found";
    public static final String EXCEPTION = "exception";
    public static final String EXCEPTION_NOT_FOUND = "exception not found";

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public boolean isSuccess(){
        return "success".equals(this.code);
    }

    @SuppressWarnings("unchecked")
    public static <T> Optional<Y800ResponseModel<T>> from(String dataStr,Class<T> clazz)
    {
        Gson gson=new Gson();
        try {
            return Optional.ofNullable(gson.fromJson(dataStr,Y800ResponseModel.class));
        }
        catch (Exception ex)
        {
            return Optional.empty();
        }
    }

    public static void main(String[] args) {
        @Data
        @AllArgsConstructor
        class A{
            String a;
            int b;
            A(){}
        }
        Gson gson=new Gson();
        Y800ResponseModel<A> model=new Y800ResponseModel<> ();
        model.setData(new A("a",1));
        model.setCode("ash");
        System.out.println(Y800ResponseModel.from("{\"data\":{\"a\":\"ash\",\"b\":\"1\"}}",A.class).get().getData());
    }
}
