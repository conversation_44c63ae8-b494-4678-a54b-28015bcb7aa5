package moonstone.common.api;

import io.vertx.core.VertxOptions;
import io.vertx.core.json.JsonObject;

import java.util.concurrent.TimeUnit;

public interface Worker extends ConfiguredVerticle {
    @Override
    default JsonObject config() {
        return ConfiguredVerticle.super.config()
                .put("worker", true)
                .put("ha", true)
                .put("instances", 4)
                .put("workerPoolSize", VertxOptions.DEFAULT_WORKER_POOL_SIZE)
                .put("maxWorkerExecuteTime", TimeUnit.MINUTES.toNanos(8))
                .put("maxWorkerExecuteTimeUnit", TimeUnit.NANOSECONDS);
    }
}
