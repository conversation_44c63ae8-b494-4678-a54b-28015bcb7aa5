package moonstone.common.utils;

import java.io.*;
import java.lang.annotation.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

/**
 * Simple XML, for the crazy world.
 * why there is so many Json Library, but the XML library is short?
 * XML is a sub dialect of Lisp, let Lisp come alive again!!
 * in mostly situation, we need just a well formed XML file as configure, so that we need no a complex Parse or some support 'require' function that can be executed Module, we need a pure simple data parse.
 * here is a simple XML parse, that may tolerate the Syntax Bug.
 **/
public interface XML {

    /**
     * just return the content of the xml
     *
     * @param content content
     * @return xml
     * @apiNote wrapped by CDATA!!!
     */
    static XML content(String content) {
        // replace all the content
        content = "<![CDATA[" + content + "]]>";
        return new SimpleXML("1.0", "UTF-8", null, content, new LinkedHashMap<>(), new LinkedHashMap<>());
    }

    static SimpleXML xml(String name) {
        if (!name.chars().allMatch(c -> Character.isAlphabetic(c) || c == '_' || c == '-' || c == ':')) {
            throw new RuntimeException("XML NODE NAME MUST BE THE Alphabetic or _-:!!!");
        }
        return new SimpleXML("1.0", "UTF-8", name, "", new LinkedHashMap<>(), new LinkedHashMap<>());
    }

    static XML parseXML(String xml) {
        return parseXML("UTF-8", "1.0", new StringReader(xml));
    }

    static XML parseXML(byte[] xml) {
        return parseXML(new ByteArrayInputStream(xml));
    }

    static XML parseXML(InputStream xml) {
        return parseXML("UTF-8", "1.0", new InputStreamReader(xml));
    }

    static XML parseXML(String encode, String version, Reader xml) {
        var log = java.util.logging.Logger.getLogger("SimpleXMLParser");

        var name = "#DEFAULT";
        var attribute = new LinkedHashMap<String, String>();
        var element = new LinkedList<XML>();
        var foundXML = false;
        var contentBuffer = new StringBuilder();
        var buff = new StringBuilder();

        var charSet = new HashSet<>();
        var _d = 'A' - 'a';
        for (var i = (int) 'a'; i <= (int) 'z'; i++) {
            charSet.add((char) i);
            charSet.add((char) (i + _d));
        }
        for (var i = (int) '0'; i <= (int) '9'; i++) {
            charSet.add((char) i);
        }
        charSet.add('_');
        charSet.add('-');
        charSet.add(':');
        charSet.add('.');
        Supplier<Integer> xmlRead = () -> {
            try {
                return xml.read();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
        Supplier<Character> parseAtom = () -> {
            char c = 0;
            for (var i = xmlRead.get(); i > -1; i = xmlRead.get()) {
                c = (char) i.intValue();
                // will return a atom if it can
                if (!charSet.contains(c)) {
                    break;
                }
                buff.append(c);
            }
            return c;
        };
        Function<XMLType, Character> parseChar = (s) -> {
            var i = xmlRead.get();
            if (i < 0) throw new RuntimeException("Unexpected End!!! When expect to parse " + s.toString());
            buff.append((char) i.intValue());
            return (char) i.intValue();
        };
        Function<Character, Boolean> requireChar = (c) -> {
            var i = xmlRead.get();
            if (i < 0) throw new RuntimeException("UNEXPECTED END!!! When expect to get " + c);
            return c == (char) (i.intValue());
        };
        Supplier<String> parseString = () -> {
            // read string that allow the ' and "
            var start = (char) xmlRead.get().intValue();
            StringBuilder b = new StringBuilder();
            for (var c = xmlRead.get(); c > -1; c = xmlRead.get()) {
                if (start == (char) c.intValue()) {
                    return b.toString();
                }
                b.append((char) c.intValue());
            }
            return b.toString();
        };
        Supplier<Character> skipBlank = () -> {
            for (int b = xmlRead.get(); b > -1; b = xmlRead.get()) {
                var c = (char) b;
                if (' ' != c && '\n' != c && '\t' != c && '\r' != c) {
                    buff.append(c);
                    return c;
                }
            }
            return null;
        };


        for (var i = skipBlank.get(); i != null; i = skipBlank.get()) {
            boolean nextTurn = false;
            char c = i;
            // append all the char parse read
            buff.append(c);
            // if start with < means xml start
            if (c == '<') {
                if (contentBuffer.length() > 0) {
                    return new SimpleXML(version, encode, null, contentBuffer.toString(), null, null);
                }
                c = parseChar.apply(XMLType.Element);
                if (c == '!') {
                    // check if quote or CDATA
                    c = parseChar.apply(XMLType.Quote);
                    if (c == '[') {
                        // expect CDATA
                        buff.delete(0, buff.toString().length());
                        char next = parseAtom.get();
                        var CDATA = buff.toString();
                        if (!CDATA.equals("CDATA")) {
                            throw new RuntimeException("Require CDATA at <![ part, but got ATOM: " + CDATA);
                        }
                        if (next != '[') {
                            throw new RuntimeException("Not CDATA!!! but get CDATA start");
                        }
                        buff.delete(0, buff.toString().length());
                        // read until ]]>
                        while (true) {
                            if (parseChar.apply(XMLType.CDATA) == '>' &&
                                    buff.toString().endsWith("]]>")) {
                                break;
                            }
                        }
                        if (!foundXML)
                            foundXML = true;
                        buff.delete(0, buff.toString().length());
                        contentBuffer.append(buff);
                        // CDATA parse over
                        continue;
                    }
                    // quote?
                    if (c == '-' && parseChar.apply(XMLType.Quote) == '-') {
                        // good quote, just skip until we receive the -->
                        while (true) {
                            if (parseChar.apply(XMLType.CDATA) == '>' &&
                                    buff.toString().endsWith("-->")) {
                                break;
                            }
                        }
                        buff.delete(0, buff.toString().length());
                        continue;
                    }
                    // wtf?? ok here is the DTD or THE XLS situaiton, let's skip it!!!
                    // as far as we know it's the xml too for the DTD and XLS so just parse it
                    // TODO SUPPORT FULL DTD, but now just skip one line
                    buff.delete(0, 1);
                    var last = parseAtom.get();
                    var atom = buff.toString();
                    buff.append(last);
                    if (atom.equals("<!DOCTYPE")) {
                        // FIXME: give it a full schema support
                        // read until the > just kill it
                        while (true) {
                            if (parseChar.apply(XMLType.CDATA) == '>') {
                                buff.delete(0, buff.length());
                                nextTurn = true;
                                break;
                            }
                        }
                    } else {
                        throw new RuntimeException("THIS IS NOT FULL XML SUPPORT USE A BETTER ONE PLEASE");
                    }
                }

                // VERSION
                if (c == '?') {
                    // version, let's parse it!
                    // xml lol
                    var next = parseAtom.get();
                    // skip useless blank
                    do {
                        next = skipBlank.get();
                        buff.delete(0, buff.toString().length());
                        if (next == '?') {
                            break;
                        }
                        buff.append(next);
                        next = parseAtom.get();
                        var atom = buff.toString();
                        if (atom.equals("version") || atom.equals("encoding")) {
                            buff.delete(0, buff.toString().length());
                            if (next != '=')
                                throw new RuntimeException("Parsing " + atom + " attribute, require '=' after the attribute name");
                            buff.delete(0, buff.toString().length());
                            var value = parseString.get();
                            if (atom.equals("version")) {
                                version = value;
                            } else {
                                encode = value;
                            }
                        } else log.warning("UNKNOWN PART '" + atom + "' at <?version?>");
                        c = parseChar.apply(XMLType.Attribute);
                    } while (c != '?');
                    if (!requireChar.apply('>'))
                        throw new RuntimeException("Require closing '>' at <?version ?> element!!!");
                    if (!foundXML)
                        foundXML = true;
                    continue;
                }

                // CLOSING ELEMENT
                if (c == '/') {
                    // closing an element head
                    buff.delete(0, buff.toString().length());
                    // ignore the name? should we?

                    // skip the blank
                    char blank = parseAtom.get();
                    if (blank == ' ' || blank == '\r' || blank == '\n' || blank == '\t') {
                        blank = skipBlank.get();
                    }
                    if (blank == '>') {
                        var elements = new LinkedHashMap<String, List<XML>>();
                        return new SimpleXML(version, encode, name, contentBuffer.toString(), attribute, elements);
                    }
                    throw new RuntimeException("UNKNOWN CHAR AT ELEMENT CLOSE!!!!");
                }
                if (c == ' ')
                    throw new RuntimeException("UNEXPECTED BLANK!!! when expect XML node start like <node !!!");
                if (nextTurn) {
                    nextTurn = false;
                    continue;
                }
                // read an atom for this is a element start, only in this situation will continue parse
                buff.delete(0, buff.toString().length());
                buff.append(c);
                c = parseAtom.get();
                name = buff.toString();
                // next should parse the attribute or close
                if (c == ' ' || c == '\t' || c == '\n' || c == '\r') {
                    c = skipBlank.get();
                }
                while (c != '>' && c != '/') {
                    buff.delete(0, buff.length());
                    buff.append(c);
                    var next = parseAtom.get();
                    var attributeName = buff.toString();
                    buff.delete(0, buff.toString().length());
                    // actually it can be just a meta on some situation, but i can't support it now
                    if (next != '=') throw new RuntimeException("Attribute must follow by the '='");
                    var attributeValue = parseString.get();
                    attribute.put(attributeName, attributeValue);
                    buff.delete(0, buff.toString().length());
                    c = skipBlank.get();
                }
                if (c == '/') {
                    // closing element
                    if (!requireChar.apply('>')) {
                        throw new RuntimeException("XML CLOSING!!!! <name attribute=... /> must end with />!!!");
                    }
                    var elements = new LinkedHashMap<String, List<XML>>();
                    return new SimpleXML(version, encode, name, contentBuffer.toString(), attribute, elements);
                }
                // must be the > here, means the element head has been complete, continue parsing the element
                while (true) {
                    var xmlElement = parseXML(encode, version, xml);
                    if (xmlElement == null) {
                        throw new RuntimeException("UNEXPECTED ENDING");
                    }
                    // after a xml element is parse, there still be possible to be another element exists
                    while (xmlElement.name() == null) {
                        element.add(xmlElement);
                        PushbackReader pushBack = new PushbackReader(xml);
                        try {
                            pushBack.unread('<');
                        } catch (Exception e) {
                            throw new RuntimeException("Can't push back");
                        }
                        xmlElement = parseXML(encode, version, pushBack);
                        if (xmlElement == null) {
                            throw new RuntimeException("UNEXPECTED ENDING");
                        }
                    }
                    if (element.size() == 1 && element.get(0).name() == null) {
                        contentBuffer.append(element.get(0).value());
                        element.clear();
                    }
                    if ("#DEFAULT".equals(xmlElement.name())) {
                        // ok the element is closing for nothing, parse stop
                        contentBuffer.append(xmlElement.value());
                        break;
                    }
                    element.add(xmlElement);
                }
                var elements = new LinkedHashMap<String, List<XML>>();
                for (var one : element) {
                    elements.computeIfAbsent(one.name(), _i -> new LinkedList<>())
                            .add(one);
                }
                return new SimpleXML(version, encode, name, contentBuffer.toString(), attribute, elements);
            } else {
                if (!foundXML) {
                    contentBuffer.append(c);
                }
            }
        }
        if (contentBuffer.isEmpty()) {
            return null;
        }
        return new SimpleXML(version, encode, null, contentBuffer.toString(), new LinkedHashMap<>(), new LinkedHashMap<>());
    }

    default <T> T toJava(Class<T> beanWithXMLDefinition) throws Exception {
        // skip the root if possible
        Annotation rootElement = Context.getAnnotation(beanWithXMLDefinition, Context.xmlRootElement());
        String root = rootElement == null ? null : Context.name(rootElement);
        XML node = this;
        if (root != null && !root.equals(node.name())) {
            node = getElement(root).iterator().next();
        }
        Object primitiveJava = Context.toPrimitiveJava(beanWithXMLDefinition, node.value());
        if (primitiveJava != null) {
            return (T) primitiveJava;
        }
        Constructor<?> pojo = null;
        Constructor<?> max = null;
        for (Constructor<?> constructor : beanWithXMLDefinition.getConstructors()) {
            // get max one or the zero one
            if (beanWithXMLDefinition.isRecord()) {
                max = beanWithXMLDefinition.getConstructors()[0];
                break;
            }
            if (constructor.getParameterCount() == 0) {
                pojo = constructor;
            } else {
                if (max == null) {
                    max = constructor;
                } else {
                    max = constructor.getParameterCount() > max.getParameterCount() ? constructor : max;
                }
            }
        }
        Map<String, Object> fields = new LinkedHashMap<>();
        Map<String, Method> methodMap = new LinkedHashMap<>();
        Set<String> fieldNameSet = new HashSet<>();
        for (Field field : beanWithXMLDefinition.getDeclaredFields()) {
            fieldNameSet.add(field.getName());
        }
        for (Method declaredMethod : beanWithXMLDefinition.getDeclaredMethods()) {
            if (!beanWithXMLDefinition.isRecord()) {
                if ((declaredMethod.getParameterCount() == 0 && declaredMethod.getReturnType() == Void.class) || declaredMethod.getParameterCount() > 1) {
                    continue;
                }
            }
            Annotation xmlElement = Context.getAnnotation(declaredMethod, Context.xmlElement());
            Annotation attribute = Context.getAnnotation(declaredMethod, Context.xmlAttribute());
            String fieldNameMaybe;
            if (beanWithXMLDefinition.isRecord()) {
                fieldNameMaybe = declaredMethod.getName();
            } else {
                fieldNameMaybe = declaredMethod.getName().length() < 4 ? "" : declaredMethod.getName().substring(3, 4).toLowerCase(Locale.ROOT) + declaredMethod.getName().substring(4);
            }
            if (xmlElement == null && attribute == null && fieldNameSet.contains(fieldNameMaybe)) {
                xmlElement = Context.getAnnotation(beanWithXMLDefinition.getDeclaredField(fieldNameMaybe), Context.xmlElement());
                attribute = Context.getAnnotation(beanWithXMLDefinition.getDeclaredField(fieldNameMaybe), Context.xmlAttribute());
            }
            String name = fieldNameMaybe;
            if (attribute != null) {
                String namespace = (Context.namespace(attribute).isEmpty() || "##default".equals(Context.namespace(attribute)))
                        ? "" : Context.namespace(attribute) + ":";
                name = (!"##default".equals(Context.name(attribute)) && !Context.name(attribute).isEmpty())
                        ? Context.name(attribute) : name;
                name = namespace + name;
            }
            if (!beanWithXMLDefinition.isRecord()) {
                if (attribute == null && name.startsWith("set")) {
                    name = name.substring(3, 4).toLowerCase(Locale.ROOT) + name.substring(4);
                }
                // the pojo will only use the pojo name
                if (!declaredMethod.getName().startsWith("get") && !declaredMethod.getName().startsWith("set")) {
                    continue;
                }
            } else {
                // the record will only use the field name
                if (!fieldNameSet.contains(declaredMethod.getName())) {
                    continue;
                }
            }
            boolean attMatch = node.getAttribute(declaredMethod.getName()) != null;
            if (declaredMethod.getName().startsWith("set")) {
                methodMap.put(name, declaredMethod);
            }
            if (attribute != null || attMatch) {
                String value = node.getAttribute(name);
                if (value == null && (attribute != null && Context.required(attribute))) {
                    throw new RuntimeException("Attribute " + name + " Lost, but " + beanWithXMLDefinition.getName() + " require the Attribute at" + this);
                }
                Class<?> type = declaredMethod.getParameterCount() == 0 ? declaredMethod.getReturnType() : declaredMethod.getParameterTypes()[0];
                Annotation adapter = Context.getAnnotation(declaredMethod, Context.xmlAdapter());
                if (adapter == null) {
                    adapter = Context.getAnnotation(type, Context.xmlAdapter());
                }
                Object arg;
                if (adapter == null) {
                    arg = null;
                    if (value == null) {
                        fields.put(name, arg);
                        continue;
                    }
                    arg = Context.toPrimitiveJava(type, value);
                } else {
                    arg = Context.adapter(adapter, value);
                }
                fields.put(name, arg);
                // now let's work with this
                continue;
            }
            name = xmlElement == null ? name : Context.name(xmlElement);
            List<Object> beans = new LinkedList<>();
            Class<?> fieldType = declaredMethod.getParameterCount() > 0 ? declaredMethod.getParameterTypes()[0] : declaredMethod.getReturnType();
            Class<?> beanType = fieldType;
            if (xmlElement != null) {
                beanType = Context.type(xmlElement);
            }
            if (beanType.isAssignableFrom(Collection.class)) {
                Annotation adapter = Context.getAnnotation(declaredMethod, Context.xmlAdapter());
                if (xmlElement == null && adapter == null) {
                    throw new RuntimeException("can't understand the type, give a adapter or type define please");
                }
                if (adapter == null) {
                    beanType = Context.type(xmlElement);
                }
            }
            if (node.getElement(name) == null)
                continue;
            for (XML xml : node.getElement(name)) {
                Annotation adapter = Context.getAnnotation(declaredMethod, Context.xmlAdapter());
                if (adapter == null) {
                    adapter = Context.getAnnotation(beanType, Context.xmlAdapter());
                }
                if (adapter != null) {
                    beans.add(Context.adapter(adapter, xml));
                } else {
                    beans.add(xml.toJava(beanType));
                }
            }
            if (Collection.class.isAssignableFrom(fieldType)) {
                Object arg = beans;
                if (Set.class.isAssignableFrom(fieldType)) {
                    arg = new HashSet<>(beans);
                }
                if (Map.class.isAssignableFrom(fieldType)) {
                    var map = new LinkedHashMap<>();
                    var i = 0;
                    for (XML xml : node.getElement(name)) {
                        map.put(Optional.ofNullable(xml.getAttribute("key")).orElse(i + ""), beans.get(i++));
                    }
                    arg = map;
                }
                fields.put(name, arg);
            } else {
                fields.put(name, beans.get(0));
            }
        }
        if (max == null && pojo != null) {
            T bean = (T) pojo.newInstance();
            for (Map.Entry<String, Method> pair : methodMap.entrySet()) {
                Annotation attribute = Context.getAnnotation(pair.getValue(), Context.xmlAttribute());
                Annotation element = Context.getAnnotation(pair.getValue(), Context.xmlElement());
                // if the is setter use the field-name
                String fieldNameMaybe = pair.getKey();
                if (element == null && attribute == null && fieldNameSet.contains(fieldNameMaybe)) {
                    element = Context.getAnnotation(beanWithXMLDefinition.getDeclaredField(fieldNameMaybe), Context.xmlElement());
                    attribute = Context.getAnnotation(beanWithXMLDefinition.getDeclaredField(fieldNameMaybe), Context.xmlAttribute());
                }
                String name = pair.getKey();
                if (attribute != null) {
                    String namespace = (Context.namespace(attribute).isEmpty() || "##default".equals(Context.namespace(attribute)))
                            ? "" : Context.namespace(attribute) + ":";
                    name = (!"##default".equals(Context.name(attribute)) && !Context.name(attribute).isEmpty())
                            ? Context.name(attribute) : name;
                    name = namespace + name;
                }
                if (element != null) {
                    name = Context.name(element);
                }
                pair.getValue().invoke(bean, fields.get(name));
            }
            return bean;
        } else if (max != null) {
            // only read from the method for security
            List<Object> args = new ArrayList<>(max.getParameterCount());
            for (Parameter parameter : max.getParameters()) {
                String name = parameter.getName();
                Annotation xmlElement = Context.getAnnotation(parameter, Context.xmlElement());
                Annotation xmlAttribute = Context.getAnnotation(parameter, Context.xmlAttribute());
                if (xmlElement != null && !"##default".equals(Context.name(xmlElement))) {
                    name = Context.name(xmlElement);
                }
                if (xmlAttribute != null) {
                    if (!"##default".equals(Context.name(xmlAttribute))) {
                        name = Context.name(xmlAttribute);
                    }
                    String ns = Context.namespace(xmlAttribute);
                    if (!"##default".equals(ns) && !ns.isEmpty()) {
                        name = Context.namespace(xmlAttribute) + ":" + name;
                    }
                }
                args.add(fields.get(name));
            }
            return (T) max.newInstance(args.toArray());
        }
        throw new RuntimeException("NONE CONSTRUCTOR!!!");
    }

    String version();

    String encode();

    /**
     * (getElement <a><b v="1"/><b v="2"/><c name="me"/></a> "b") -> [<b v="1"/> <b v="2"/>]
     * (getElement <a><b v="1"/><b v="2"/><c name="me"/></a> "c") -> [<c name="me"/>]
     **/
    Iterable<XML> getElement(String name);

    /**
     * (getAttribute <a name="joke" value="me"/> "name") -> "joke", use fake code of lisp as doc because i'm favorite of lisp
     **/
    String getAttribute(String name);

    /**
     * get the value, the <a>name</a> return name with type String.
     * the <a><b>name</b><c><item>1</item><item>2</item></c></a> will return a iterator of [<b.../> <c.../>] the content XML
     **/
    Object value();

    /**
     * get the name, the <a>name</a> will return a
     **/
    String name();

    String toString(String prefix);

    /**
     * <a name="joke" value="me"/> will return ["name" "value"]
     **/
    Iterable<String> getAllAttribute();

    Map<String, List<XML>> elements();

    /**
     * describe the parse now status
     **/
    enum XMLType {
        Quote, Attribute, Element, CDATA
    }

    enum Context {
        ;

        public static String name(Object o) {
            try {
                return (String) o.getClass().getMethod("name")
                        .invoke(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        public static Class<?> valueType(Annotation o) {
            try {
                return (Class<?>) o.getClass().getMethod("value").invoke(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        public static String namespace(Object o) {
            try {
                return (String) o.getClass().getMethod("namespace")
                        .invoke(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        public static Boolean required(Object o) {
            try {
                return (Boolean) o.getClass().getMethod("required")
                        .invoke(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        public static Object unmarshal(Object o, Object xml) {
            try {
                return Stream.of(o.getClass().getMethods()).filter(m -> m.getName().equals("unmarshal"))
                        .findFirst().orElseThrow(() -> new RuntimeException("unmarshal Method not found")).invoke(o, xml);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        public static Class<?> type(Object o) {
            try {
                return (Class<?>) o.getClass().getMethod("type")
                        .invoke(o);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        public static Annotation getAnnotation(Parameter bean, String annotationName) {
            for (Annotation annotation : bean.getAnnotations()) {
                if (annotation.annotationType().getName().endsWith(annotationName)) {
                    return annotation;
                }
            }
            return null;
        }

        public static Annotation getAnnotation(Class<?> bean, String annotationName) {
            for (Annotation annotation : bean.getAnnotations()) {
                if (annotation.annotationType().getName().endsWith(annotationName)) {
                    return annotation;
                }
            }
            return null;
        }

        public static Annotation getAnnotation(Field bean, String annotationName) {
            for (Annotation annotation : bean.getAnnotations()) {
                if (annotation.annotationType().getName().endsWith(annotationName)) {
                    return annotation;
                }
            }
            return null;
        }

        public static Annotation getAnnotation(Method bean, String annotationName) {
            for (Annotation annotation : bean.getAnnotations()) {
                if (annotation.annotationType().getName().endsWith(annotationName)) {
                    return annotation;
                }
            }
            return null;
        }

        public static String xmlAdapter() {
            return "XmlJavaTypeAdapter";
        }

        public static String xmlRootElement() {
            return "XmlRootElement";
        }

        public static String xmlAttribute() {
            return "XmlAttribute";
        }

        public static String xmlElement() {
            return "XmlElement";
        }

        public static Object toPrimitiveJava(Class<?> type, Object value) {
            Object arg = null;
            if (String.class.isAssignableFrom(type)) {
                arg = value;
            }
            if (Boolean.class.isAssignableFrom(type)) {
                arg = Boolean.parseBoolean(value.toString());
            }
            if (Long.class.isAssignableFrom(type)) {
                arg = Long.parseLong(value.toString());
            }
            if (Integer.class.isAssignableFrom(type)) {
                arg = Integer.parseInt(value.toString());
            }
            if (Double.class.isAssignableFrom(type)) {
                arg = Double.parseDouble(value.toString());
            }
            if (Float.class.isAssignableFrom(type)) {
                arg = Float.parseFloat(value.toString());
            }
            if (BigDecimal.class.isAssignableFrom(type)) {
                arg = new BigDecimal(value.toString());
            }
            if (Enum.class.isAssignableFrom(type)) {
                // let's do it!
                for (Object enumConstant : type.getEnumConstants()) {
                    if (enumConstant.toString().equals(value)) {
                        arg = enumConstant;
                        break;
                    }
                }
            }
            return arg;
        }

        public static Object adapter(Annotation adapter, Object value) throws Exception {
            if (valueType(adapter).isAssignableFrom(Enum.class)) {
                for (Object enumConstant : valueType(adapter).getEnumConstants()) {
                    Object arg;
                    try {
                        arg = unmarshal(enumConstant, value);
                    } catch (Exception e) {
                        e.printStackTrace();
                        continue;
                    }
                    if (arg != null) {
                        return arg;
                    }
                }
            } else {
                return unmarshal(valueType(adapter).getDeclaredConstructor((Class<?>[]) null).newInstance((Object[]) null), value);
            }
            throw new RuntimeException("not support adapter");
        }
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.TYPE})
    @interface XmlRootElement {
        String name();
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
    @interface XmlAttribute {
        String name();

        String namespace() default "";

        boolean required() default true;
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
    @interface XmlElement {
        String name();

        Class<?> type();
    }

    interface XmlAdapter {
        Object unmarshal(Object data);
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
    @interface XmlJavaTypeAdapter {
        Class<? extends XmlAdapter> value();
    }

    /**
     * a simple implement of XML
     **/
    record SimpleXML(String version, String encode,
                     String name,
                     String content,
                     Map<String, String> attribute,
                     Map<String, List<XML>> elements) implements XML {

        public Iterable<XML> getElement(String element) {
            return elements().get(element);
        }

        public String getAttribute(String attribute) {
            return this.attribute().get(attribute);
        }

        public Iterable<String> getAllAttribute() {
            return attribute.keySet();
        }

        public Object value() {
            return content() == null ?
                    elements() : content();
        }

        public String toString() {
            return toString("");
        }

        public String toString(String prefix) {
            if (name == null) return prefix + content;
            return prefix + "<" + name
                    // attribute
                    + (attribute.isEmpty() ? "" : attribute.entrySet().stream().collect(StringBuilder::new,
                    (stringBuilder, s) -> stringBuilder.append(" ").append(s.getKey())
                            .append("=").append(s.getValue().contains("'") ? "\"" : "'")
                            .append(s.getValue()).append(s.getValue().contains("'") ? "\"" : "'"), StringBuilder::append))
                    // element
                    + (elements.isEmpty() && content.isEmpty() ? "/>" : (">" + wrapPrefix(prefix, content) + "\n" +
                    elements.values().stream().map(l -> {
                        StringBuilder b = new StringBuilder();
                        for (XML xml : l) {
                            b.append(xml.toString(prefix + "\t"));
                            b.append("\n");
                        }
                        return b.toString();
                    }).collect(StringBuilder::new, StringBuilder::append, StringBuilder::append)
                    + prefix + "</" + name + ">"));
        }

        private String wrapPrefix(String prefix, String content) {
            if (content.trim().startsWith("\n")) {
                return prefix + "\n" + content;
            }
            return content;
        }

        public SimpleXML attr(String name, String value) {
            attribute.put(name, value);
            return this;
        }

        public SimpleXML node(String name, XML xml) {
            elements.computeIfAbsent(name, i -> new ArrayList<>())
                    .add(xml);
            return this;
        }

        public SimpleXML node(XML xml) {
            elements.computeIfAbsent(xml.name(), i -> new ArrayList<>())
                    .add(xml);
            return this;
        }
    }
}
