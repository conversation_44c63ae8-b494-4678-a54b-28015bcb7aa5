package moonstone.common.utils;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.BaseUser;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.model.CommonUser;

import javax.servlet.http.HttpSession;
import java.util.Optional;

/**
 * Mail: <EMAIL>
 * Data: 16/6/28
 * Author: yangzefeng
 */
public final class UserUtil {

    private final static ThreadLocal<Long> currentShopId = new ThreadLocal<>();
    private final static ThreadLocal<BaseUser> userThreadLocal = new ThreadLocal<>();
    private final static ThreadLocal<HttpSession> sessionThreadLocal = new ThreadLocal<>();
    private final static ThreadLocal<String> sourceThreadLocal = ThreadLocal.withInitial(() -> "mall");

    private final static String APP_TYPE = "appType";

    public static void putCurrentShopId(Long currentUserAtShopId) {
        currentShopId.set(currentUserAtShopId);
    }

    public static Long getCurrentShopId() {
        return Optional.ofNullable(currentShopId.get())
                .orElseGet(() -> Optional.ofNullable((CommonUser) getCurrentUser()).map(CommonUser::getShopId).orElse(null));
    }

    public static void putCurrentUser(BaseUser baseUser) {
        userThreadLocal.set(baseUser);
    }

    public static <T extends BaseUser> T getCurrentUser() {
        return (T) userThreadLocal.get();
    }

    /**
     * 获取登录后的用户, 如果未登录则表示需要登录
     *
     * @param <T> 类型
     * @return 用户
     * @throws JsonResponseException 包含未登录提示的错误
     */
    public static <T extends BaseUser> T requireLoginUser() throws JsonResponseException {
        if (getCurrentUser() == null) {
            throw new JsonResponseException("user.not.login");
        }
        return getCurrentUser();
    }

    public static HttpSession getCurrentSession() {
        return Optional.ofNullable(sessionThreadLocal.get()).orElseGet(HttpSessionHelper::getCurrentSession);
    }

    public static void putCurrentSession(HttpSession httpSession) {
        sessionThreadLocal.set(httpSession);
    }

    public static void clearCurrentUser() {
        userThreadLocal.remove();
        currentShopId.remove();
        sessionThreadLocal.remove();
        sourceThreadLocal.remove();
    }

    public static String getSource() {
        return sourceThreadLocal.get();
    }

    public static Long getUserId() {
        BaseUser baseUser = userThreadLocal.get();
        if (null != baseUser) {
            return baseUser.getId();
        }
        return null;
    }

    public static AppTypeEnum getCurrentAppType() {
        var session = sessionThreadLocal.get();
        if (session == null) {
            return null;
        }

        var appType = session.getAttribute(APP_TYPE);
        if (appType == null) {
            return null;
        }

        return AppTypeEnum.from(Integer.parseInt(appType.toString()));
    }
}
