package moonstone.common.utils;

import lombok.Data;

import java.util.Date;

/// 生成订单在外部系统使用的订单号
public interface OutSystemIdProvider {
    enum type {
        PaymentTradeNo("PT", 0), WithDrawTradeNo("WT", 0)// 提现使用的自动生成单号
        , CustomPushSystem("CP", 0) // 默认海关推送使用类型
        , ShangHaiPushSystem("SP", 1) // 上海海关推送类型 由于上海限制推送号不超过20位
        , OutSideRequest("OS", 0)// 外部调用使用的生成器
        , OutSideQueryByShop("QS", 0)// 外部使用的查询编号生成器
        , OutSideUserId("OM", 0) // 外部使用的用户Id
        , OutSideStoreProxyId("OP", 0);
        public String prefix;
        public int version; // 不同的版本将使用不同的数据生成规则

        type(String prefix, int version) {
            this.prefix = prefix;
            this.version = version;
        }

        public String getPrefix() {
            return prefix;
        }
    }

    /// 由原id 获取Id
    String getId(long id, type type);

    String newId(long id, type type);

    /// 由原id 与创建时间 获取Id
    String getId(long id, Date createdAt, type type);

    /**
     * 根据id与创建时间 序号 类型 与版本获取Id
     *
     * @param id        主键
     * @param seq       序号
     * @param createdAt 创建时间
     * @param type      类型
     * @return Id
     */
    String getId(Long id, Integer seq, Date createdAt, type type);

    /// 用途类型,也包括了支付单

    OutSystemIdContainer decode(String outSystemId);

    @Data
    class OutSystemIdContainer {
        type type;
        boolean online;
        Long id;
        Integer seq;
        Date createAt;
    }
}
