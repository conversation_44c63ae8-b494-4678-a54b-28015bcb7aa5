package moonstone.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.json.GsonJsonParser;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/// # 翻译工具类
///
/// 通过配合Translate进行翻译,其中Translate通过重载toString进行翻译.
/// 而该类加载本地资源文件进行map型翻译
/// ** 目前Component无效! **
@Slf4j
@Component
public class TranslateUtil {
    static private TranslateUtil single;
    private Map<String, String> stringMap = new TreeMap<>();
    private final GsonJsonParser gsonJsonParser = new GsonJsonParser();

    private TranslateUtil() {
    }

    @PostConstruct
    private static void init() {
        synchronized (TranslateUtil.class) {
            single = new TranslateUtil();
            single.initMap();
        }
    }

    public static TranslateUtil getInstance() {
        if (single != null) {
            return single;
        }
        synchronized (TranslateUtil.class) {
            if (single == null) {
                init();
            }
            return getInstance();
        }
    }

    @SuppressWarnings("unchecked")
    private void initMap() {
        String content = "uninitial";
        try {
            /// ** 重要因为是本地文件所以允许使用BuffInputStream!
            /// 通过锁死翻译文件达到方案
            String translateFileName = "/translate.json";
            InputStream inputStream = getClass().getResource(translateFileName).openStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            for (int c = inputStream.read(); c >= 0; c = inputStream.read())
                byteArrayOutputStream.write(c);
            content = byteArrayOutputStream.toString();
            Map<String, String> map = new HashMap<>(); //new TreeMap<String, Object>();
            gsonJsonParser.parseMap(content).forEach((x, y) -> map.put(x, y.toString()));
            synchronized (TranslateUtil.class) {
                this.stringMap = map;
            }
        } catch (Exception ex) {
            log.error("init translate map error:{},content:{}", ex.getMessage(), content);
            ex.printStackTrace();
        }
    }

    public String beautify(String oldWord) {
        if (CollectionUtils.isEmpty(stringMap)) {
            synchronized (TranslateUtil.class) {
                if (CollectionUtils.isEmpty(stringMap)) {
                    initMap();
                }
            }
        }
        return stringMap.getOrDefault(oldWord, oldWord);
    }
}
