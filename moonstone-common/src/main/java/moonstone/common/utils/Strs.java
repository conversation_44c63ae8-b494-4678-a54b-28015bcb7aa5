package moonstone.common.utils;

import com.google.common.base.Optional;
import io.terminus.common.utils.Params;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
public class Strs {

    public static Optional<Long> parseLong(@Nullable Object obj) {
        String str = fixRead(obj);
        return str == null ? Optional.absent() : Optional.of(Long.parseLong(str));
    }

    public static Optional<Integer> parseInt(@Nullable Object obj) {
        String str = fixRead(obj);
        return str == null ? Optional.absent() : Optional.of(Integer.parseInt(str));
    }

    private static String fixRead(Object obj) {
        String str = Params.trimToNull(obj);
        if ("null".equals(str)) {
            return null;
        }
        return str;
    }
}
