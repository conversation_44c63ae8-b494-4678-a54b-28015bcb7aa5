package moonstone.common.utils;


import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 */
public class R extends HashMap<String, Object> {
    private static final long serialVersionUID = 1L;

    public R() {
        add("code", 0);
        add("msg", "success");
    }

    public static R error() {
        return error(500, "未知异常，请联系管理员");
    }

    public static R error(String msg) {
        return error(500, msg);
    }

    public static R error(int code, String msg) {
        R r = new R();
        r.add("code", code);
        r.add("msg", msg);
        return r;
    }

    public static R ok(String msg) {
        R r = new R();
        r.add("msg", msg);
        return r;
    }

    public static R ok(Map<String, Object> map) {
        R r = new R();
        r.putAll(map);
        return r;
    }

    public static R ok() {
        return new R();
    }

    public @NotNull
    R add(String key, Object value) {
        super.put(key, value);
        return this;
    }
}
