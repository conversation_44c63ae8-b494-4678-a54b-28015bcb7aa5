//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON>flower decompiler)
//

package moonstone.common.utils;


import moonstone.common.api.ResultCode;

import java.io.Serializable;

/**
 * 功能描述:  返回信息
 * 创建时间:  2020/7/2 11:12 上午
 *
 * <AUTHOR>
 */
public class RespResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 状态码
     */
    private int code;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 承载数据
     */
    private T data;

    /**
     * 返回消息
     */
    private String msg;

    private RespResult(IResultCode resultCode) {
        this(resultCode, null, resultCode.getMessage());
    }

    private RespResult(IResultCode resultCode, String msg) {
        this(resultCode, null, msg);
    }

    private RespResult(IResultCode resultCode, T data) {
        this(resultCode, data, resultCode.getMessage());
    }

    private RespResult(IResultCode resultCode, T data, String msg) {
        this(resultCode.getCode(), data, msg);
    }

    private RespResult(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
        this.success = ResultCode.SUCCESS.getCode() == code;
    }


    public static <T> RespResult<T> data(T data) {
        return data(data, "操作成功");
    }

    public static <T> RespResult<T> data(T data, String msg) {
        return data(0, data, msg);
    }

    public static <T> RespResult<T> data(int code, T data, String msg) {
        return new RespResult(code, data, data == null ? "暂无承载数据" : msg);
    }

    public static <T> RespResult<T> success(String msg) {
        return new RespResult(ResultCode.SUCCESS, msg);
    }

    public static <T> RespResult<T> success(IResultCode resultCode) {
        return new RespResult(resultCode);
    }

    public static <T> RespResult<T> success(IResultCode resultCode, String msg) {
        return new RespResult(resultCode, msg);
    }

    public static <T> RespResult<T> fail(String msg) {
        return new RespResult(ResultCode.FAILURE, msg);
    }

    public static <T> RespResult<T> fail(int code, String msg) {
        return new RespResult(code, null, msg);
    }

    public static <T> RespResult<T> fail(IResultCode resultCode) {
        return new RespResult(resultCode);
    }

    public static <T> RespResult<T> fail(IResultCode resultCode, String msg) {
        return new RespResult(resultCode, msg);
    }

    public static <T> RespResult<T> status(boolean flag) {
        return flag ? success("操作成功") : fail("操作失败");
    }

    public int getCode() {
        return this.code;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public T getData() {
        return this.data;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public void setSuccess(final boolean success) {
        this.success = success;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public void setMsg(final String msg) {
        this.msg = msg;
    }

    public String toString() {
        return "R(code=" + this.getCode() + ", success=" + this.isSuccess() + ", data=" + this.getData() + ", msg=" + this.getMsg() + ")";
    }

    public RespResult() {
    }
}
