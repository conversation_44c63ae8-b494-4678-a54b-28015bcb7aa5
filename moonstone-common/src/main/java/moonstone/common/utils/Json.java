package moonstone.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.InputStream;
import java.util.Objects;
import java.util.TimeZone;


/**
 * <AUTHOR>
 */
public interface Json {
    ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    /**
     * configure the static part to load default set
     */
    Config CONFIG = new Config();

    class Config {
        static {
            OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            OBJECT_MAPPER.setTimeZone(TimeZone.getDefault());
        }
    }

    /**
     * parse object
     *
     * @param input stream
     * @param tType type
     * @param <T>   type
     * @return object
     */
    static <T> T parseObject(InputStream input, TypeReference<T> tType) {
        try {
            if (Objects.isNull(input)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(input, tType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * parse object
     *
     * @param input stream
     * @param tType type
     * @param <T>   type
     * @return object
     */
    static <T> T parseObject(InputStream input, JavaType tType) {
        try {
            if (Objects.isNull(input)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(input, tType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * parse object
     *
     * @param input stream
     * @param tType type
     * @param <T>   type
     * @return object
     */
    static <T> T parseObject(InputStream input, Class<T> tType) {
        try {
            if (Objects.isNull(input)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(input, tType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * parse object
     *
     * @param str   string
     * @param tType type
     * @param <T>   type
     * @return object
     */
    static <T> T parseObject(String str, TypeReference<T> tType) {
        try {
            if (Objects.isNull(str)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(str, tType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * parse object
     *
     * @param str   string
     * @param tType type
     * @param <T>   type
     * @return object
     */
    static <T> T parseObject(String str, JavaType tType) {
        try {
            if (Objects.isNull(str)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(str, tType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * parse object
     *
     * @param str   string
     * @param tType type
     * @param <T>   type
     * @return object
     */
    static <T> T parseObject(String str, Class<T> tType) {
        try {
            if (Objects.isNull(str)) {
                return null;
            }
            return OBJECT_MAPPER.readValue(str, tType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * toJsonString
     *
     * @param o object
     * @return string
     */
    static String toJson(Object o) {
        try {
            if (Objects.isNull(o)) {
                return null;
            }
            return OBJECT_MAPPER.writeValueAsString(o);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
