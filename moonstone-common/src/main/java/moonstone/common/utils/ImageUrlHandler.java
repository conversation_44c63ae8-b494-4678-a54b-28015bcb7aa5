package moonstone.common.utils;

import moonstone.common.enums.ImageUrlTransformType;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 图片url处理器
 * Author:cp
 * Created on 9/3/16.
 */
public final class ImageUrlHandler {

    private static String _protocol = "//";

    private static String _domain = "";

    private static final Pattern PATTERN = Pattern.compile("(http://|https://|//)?([\\S]*?)(/[\\S]*)");

    public void setProtocol(String protocol) {
        _protocol = protocol;
    }

    public void setDomain(String domain) {
        _domain = domain;
    }

    /**
     * <ul>
     * 将以下几种格式的图片url转化为:/2016/07/22/05f73408-82fa-4390-99ef-e81a0132b4a1.jpg,即去掉协议和域名部分
     * <li>
     * //terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/05f73408-82fa-4390-99ef-e81a0132b4a1.jpg
     * </li>
     * <li>
     * http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/05f73408-82fa-4390-99ef-e81a0132b4a1.jpg
     * </li>
     * <li>
     * https://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/05f73408-82fa-4390-99ef-e81a0132b4a1.jpg
     * </li>
     * </ul>
     *
     * @param completePath 输入的完整图片url
     * @return 去掉协议和域名后的路径
     */
    public static String simplify(String completePath) {
        if (isIgnore()) {
            return completePath;
        }

        if (!StringUtils.hasText(completePath)) {
            return null;
        }

        if (!isComplete(completePath)) {
            return completePath;
        }

        Matcher matcher = PATTERN.matcher(completePath);
        if (!matcher.find()) {
            return completePath;
        }

        //如果域名不一样,说明可能是外部导入的图片,不处理
        String domain = matcher.group(2);
        if (!getDomain().equalsIgnoreCase(domain)) {
            return completePath;
        }

        String shortPath = matcher.group(3);
        return shortPath;
    }

    /**
     * 将去掉协议和域名后的图片url重新加上协议和域名
     *
     * @param shortPath 简化后的图片url
     * @return 加上协议和域名后的图片url
     */
    public static String complete(String shortPath) {
        if (isIgnore()) {
            return shortPath;
        }

        if (!StringUtils.hasText(shortPath)) {
            return null;
        }

        if (shortPath.startsWith("//") && !getProtocol().equals("//")) {
            return getProtocol() + ":" + shortPath;
        }

        if (isComplete(shortPath)) {
            return shortPath;
        }

        return normalizeProtocol() + getDomain() + normalizePath(shortPath);
    }

    /**
     * 图片路径转换
     *
     * @param path          原路径
     * @param transformType 转换类型
     * @return 转换后的新路径
     */
    public static String handle(String path, ImageUrlTransformType transformType) {
        switch (transformType) {
            case SIMPLIFY:
                return simplify(path);
            case COMPLETE:
                return complete(path);
            default:
                throw new IllegalArgumentException("unknown transform type");
        }
    }

    private static boolean isIgnore() {
        return !StringUtils.hasText(getDomain());
    }

    private static String getProtocol() {
        return _protocol;
    }

    private static String getDomain() {
        return _domain;
    }

    private static boolean isComplete(String path) {
        if (path.startsWith("&#"))
            return true;
        if (path.startsWith("//")) {
            return true;
        }
        if (path.startsWith("http://")) {
            return true;
        }
        return path.startsWith("https://");
    }

    private static String normalizeProtocol() {
        String protocol = getProtocol();
        if (protocol.equals("//")) {
            return protocol;
        }
        if (protocol.equals("http")) {
            return protocol + "://";
        }
        if (protocol.equals("https")) {
            return protocol + "://";
        }
        return protocol;
    }

    private static String normalizePath(String path) {
        if (path.startsWith("/")) {
            return path;
        }
        return "/" + path;
    }
}
