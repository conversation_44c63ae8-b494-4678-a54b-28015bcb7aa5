package moonstone.common.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * DATE: 16/5/6 下午2:20 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
public class MapUtil<K, V> {

    private final Map<K, V> map;
    private boolean ignoreNullValue = false;

    private MapUtil() {
        this.map = new HashMap<K, V>();
    }

    private MapUtil(Map<K, V> map) {
        this.map = map;
    }

    public static <K, V> MapUtil<K, V> from() {
        return new MapUtil<>();
    }

    public static <K, V> MapUtil<K, V> from(Map<K, V> map) {
        return new MapUtil<K, V>(map);
    }

    public static <K, V> MapUtil<K, V> hashMap() {
        return from(new HashMap<K, V>());
    }

    public static <K, V> MapUtil<K, V> treeMap() {
        return from(new TreeMap<K, V>());
    }

    public MapUtil<K, V> ignoreNullValue() {
        ignoreNullValue = true;
        return this;
    }

    public MapUtil<K, V> of(K key, V value) {
        if (ignoreNullValue && value == null) {
            return this;
        }
        map.put(key, value);
        return this;
    }

    public MapUtil<K, V> of(K k1, V v1, K k2, V v2) {
        return of(k1, v1).of(k2, v2);
    }

    public MapUtil<K, V> of(K k1, V v1, K k2, V v2, K k3, V v3) {
        return of(k1, v1).of(k2, v2).of(k3, v3);
    }

    public MapUtil<K, V> of(K k1, V v1, K k2, V v2, K k3, V v3, K k4, V v4) {
        return of(k1, v1).of(k2, v2).of(k3, v3).of(k4, v4);
    }

    public MapUtil<K, V> of(K k1, V v1, K k2, V v2, K k3, V v3, K k4, V v4, K k5, V v5) {
        return of(k1, v1).of(k2, v2).of(k3, v3).of(k4, v4).of(k5, v5);
    }

    public Map<K, V> toMap() {
        return map;
    }

    @Override
    public String toString(){
        return map.toString();
    }
}
