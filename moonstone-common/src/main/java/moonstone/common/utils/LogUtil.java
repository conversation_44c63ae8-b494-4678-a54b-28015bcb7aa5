package moonstone.common.utils;

import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.Arrays;
import java.util.List;

@Slf4j
public abstract class LogUtil {
    public static String getClassMethodName() {
        return getClassMethodName(null, 0);
    }

    public static String getClassMethodName(int level) {
        return getClassMethodName(null, level);
    }

    public static String getClassMethodName(String aim) {
        return getClassMethodName(aim, 1);
    }

    public static String getClassMethodName(String aim, int level) {
        Exception e = new RuntimeException();
        StackTraceElement s = aim == null ? e.getStackTrace()[2 + level] : e.getStackTrace()[1 + level];
        if (s == null) {
            s = e.getStackTrace()[0];
        }
        return String.format("[%s@%d]", s.getMethodName(), s.getLineNumber()) + (aim == null ? "" : String.format("(%s)", aim));
    }

    public static String express(Throwable throwable) {
        try {
            return throwable == null ? "" : showThrowAble(throwable);
        } catch (Exception ex) {
            log.error("{} fail to express throwable", LogUtil.getClassMethodName());
            return "[FAIL-EXPRESS-THROWABLE]";
        }
    }

    /**
     * 返回异常全栈文本,将该转换为printStream的数据通过ByteArrayStream转换为字符串
     *
     * @param throwable 异常栈
     * @return 异常的全栈文本
     */
    public static String showThrowAble(Throwable throwable) {
        if (throwable instanceof JsonResponseException) {
            return throwable.getMessage() +
                    "\n" + throwable.getStackTrace()[0].getFileName() +
                    "@" + throwable.getStackTrace()[0].getClassName() +
                    ":" + throwable.getStackTrace()[0].getLineNumber();
        }
        // skip package first
        List<String> packageRequire = Arrays.asList("moonstone", "io.terminus", "com.dan", "vertx");
        boolean found = false;
        int last = 0;
        String[] limit = new String[]{
                "org.springframework.web.method.support.InvocableHandlerMethod@doInvoke",
                "org.springframework.web.method.support.InvocableHandlerMethod@invokeForRequest",
                "org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod@invokeAndHandle"
        };
        int reachLimit = -1;
        for (int i = 0; i < throwable.getStackTrace().length; i++) {
            StackTraceElement element = throwable.getStackTrace()[i];
            for (String p : packageRequire) {
                if (element.getClassName().startsWith(p)) {
                    found = true;
                    last = i;
                }
            }
            if(reachLimit + 1 == limit.length) {
                last = i - 3;
                break;
            }
            if (String.format("%s@%s", element.getClassName(), element.getMethodName())
                    .equals(limit[reachLimit + 1])){
                reachLimit ++;
            }
        }
        if (found) {
            StringBuilder builder = new StringBuilder();
            builder.append(throwable).append("\n");
            for (int i = 0; i <= last; i++) {
                StackTraceElement element = throwable.getStackTrace()[i];
                builder.append("  at ")
                        .append(element.getFileName())
                        .append("(")
                        .append(element.getClassName())
                        .append("@")
                        .append(element.getMethodName())
                        .append(":")
                        .append(element.getLineNumber())
                        .append(")\n");
            }
            return builder.toString();
        }
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        throwable.printStackTrace(new PrintStream(stream));
        return stream.toString();
    }

    public static void main(String[] args) {
        System.out.println(LogUtil.express(new RuntimeException()));
    }
}
