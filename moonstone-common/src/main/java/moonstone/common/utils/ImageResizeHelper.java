package moonstone.common.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ImageResizeHelper {
    /**
     * resize image as weight does
     *
     * @param image  src
     * @param weight target weight
     * @param height target height
     * @return new Image
     */
    static BufferedImage resize(RenderedImage image, Integer weight, Integer height) {
        if (image instanceof Image) {
            BufferedImage bufferedImage = new BufferedImage(Optional.ofNullable(weight).orElseGet(() -> image.getWidth() * height / image.getHeight())
                    , Optional.ofNullable(height).orElseGet(() -> image.getHeight() * weight / image.getWidth())
                    , BufferedImage.TYPE_INT_ARGB);
            Graphics graphics = bufferedImage.createGraphics();
            graphics.drawImage((Image) image, 0, 0, bufferedImage.getWidth(), bufferedImage.getHeight(), null);
            graphics.dispose();
            return bufferedImage;
        } else {
            try {
                ByteArrayOutputStream buff = new ByteArrayOutputStream();
                ImageIO.write(image, "png", buff);
                return resize(ImageIO.read(new ByteArrayInputStream(buff.toByteArray())), weight, height);
            } catch (Exception transformError) {
                throw new RuntimeException(transformError);
            }
        }
    }
}
