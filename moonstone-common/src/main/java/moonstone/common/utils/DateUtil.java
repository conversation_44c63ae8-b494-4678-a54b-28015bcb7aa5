/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.DateTimeFormatterBuilder;
import org.joda.time.format.DateTimeParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.chrono.ChronoLocalDate;
import java.time.chrono.ChronoLocalDateTime;
import java.time.chrono.ChronoZonedDateTime;
import java.time.temporal.Temporal;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Stream;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-22
 *
 * <AUTHOR>
 */
public interface DateUtil {
    Logger log = LoggerFactory.getLogger(DateUtil.class);

    String YMDHMS_FORMAT = "yyyy-MM-dd HH:mm:ss";
    String YYYYMMDD = "yyyy-MM-dd";
    String YYYYMMDD_SLASH = "yyyy/MM/dd";
    String YYYY_MM_DD_HH_MM_SS_SSS = "yyyyMMddHHmmssSSS";
    String YYYY_MM = "yyyyMM";
    String YYYYMMDD_NO_HYPHEN = "yyyyMMdd";
    String yyyyMMddhhmmss = "yyyyMMddhhmmss";

    DateTimeParser[] PARSERS = {
            DateTimeFormat.forPattern("yyyy-MM-dd").getParser(),
            DateTimeFormat.forPattern(YYYYMMDD_NO_HYPHEN).getParser(),
            DateTimeFormat.forPattern(YMDHMS_FORMAT).getParser(),
            DateTimeFormat.forPattern(YYYY_MM).getParser(),
            DateTimeFormat.forPattern(yyyyMMddhhmmss).getParser()
    };
    DateTimeFormatter DATE_FORMATTER = new DateTimeFormatterBuilder().append(null, PARSERS).toFormatter();


    /**
     * 检查输入的日期是否为有效格式
     *
     * @param value 输入的日期
     * @return 是否有效
     */
    static boolean isValidDate(String value) {
        try {
            DATE_FORMATTER.parseDateTime(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当天的开始时间,即yyyy-mm-dd 00:00:00
     *
     * @param date 当天日期
     * @return 当天的开始时间
     */
    static Date withTimeAtStartOfDay(Date date) {
        return new DateTime(date).withTimeAtStartOfDay().toDate();
    }

    /**
     * 获取当月的开始时间（yyyy-mm-dd 00:00:00）
     *
     * @param date
     * @return
     */
    static Date withTimeAtStartOfMonth(Date date) {
        return new DateTime(date).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate();
    }

    /**
     * 获取当天的结束时间,即yyyy-mm-dd 23:59:59
     *
     * @param date 当天日期
     * @return 当天的结束时间
     */
    static Date withTimeAtEndOfDay(Date date) {
        return new DateTime(date).withTimeAtStartOfDay().plusDays(1).minusSeconds(1).toDate();
    }

    /**
     * 获取当月的结束时间 (yyyy-mm-dd 23:59:59)
     *
     * @param date
     * @return
     */
    static Date withTimeAtEndOfMonth(Date date) {
        return new DateTime(date).dayOfMonth().withMaximumValue().withTimeAtStartOfDay().plusDays(1).minusSeconds(1).toDate();
    }

    /**
     * 获取时间的yyyymmddHH24mmss
     */
    static String getDateString() {
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    static Date parseDate(String dateString) {
        return DATE_FORMATTER.parseDateTime(dateString).toDate();
    }

    /**
     * 清洗数据中的日期数据，包括“AC_”的数据不会被清洗，其他的都会被清洗，包括END的是延长到该日最后一秒，其他为该日第0秒
     *
     * @param t     被清洗的数据
     * @param clazz 查找类型
     */
    static <T> void washTheDate(T t, Class<T> clazz) {
        if (t == null) {
            return;
        }
        Consumer<Field> wash = (field) -> {
            try {
                boolean ac = field.canAccess(t);
                field.setAccessible(true);
                if (field.get(t) != null) {
                    if (!field.getName().toUpperCase().contains("AC_")) {
                        if (field.getName().toUpperCase().contains("END")) {
                            field.set(t, DateUtil.withTimeAtEndOfDay((Date) field.get(t)));
                        } else {
                            field.set(t, DateUtil.withTimeAtStartOfDay((Date) field.get(t)));
                        }
                    }
                }
                field.setAccessible(ac);
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("{} field:{} data:{}", LogUtil.getClassMethodName("wash-fail"), field.getName(), t);
            }
        };
        Stream.of(clazz.getDeclaredFields())
                .filter(field -> field.getType().equals(Date.class))
                .forEach(wash);
    }

    /**
     * 比较时间大小
     */
    static Boolean compareTime(Date time1, Date time2) {
        return time1.after(time2);
    }

    /**
     * 将时间+1年转时间戳
     */
    static Long getTimeMillisOfYearAfter(int year) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.YEAR, year);
        cal.getTime();
        return cal.getTimeInMillis();
    }


    static String toString(Date date) {
        if (date == null) {
            return null;
        }

        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault())
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    static String toString(Date date, String format) {
        if (date == null || StringUtils.isBlank(format)) {
            return null;
        }

        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault())
                .format(java.time.format.DateTimeFormatter.ofPattern(format));
    }

    static String toString(Temporal temporal) {
        if (Objects.isNull(temporal)) {
            return null;
        }
        if (temporal instanceof ChronoZonedDateTime<?>) {
            temporal = ((ChronoZonedDateTime<?>) temporal).toInstant();
        }
        if (temporal instanceof ChronoLocalDateTime) {
            temporal = ((ChronoLocalDateTime<?>) temporal).atZone(ZoneId.systemDefault()).toInstant();
        }
        if (temporal instanceof ChronoLocalDate) {
            temporal = ((ChronoLocalDate) temporal).atTime(LocalTime.ofSecondOfDay(0)).atZone(ZoneId.systemDefault()).toInstant();
        }
        if (temporal instanceof LocalTime) {
            temporal = ((LocalTime) temporal).atDate(LocalDate.now()).atZone(ZoneId.systemDefault()).toInstant();
        }
        if (temporal instanceof Instant) {
            return new DateTime(((Instant) temporal).toEpochMilli()).toString(YMDHMS_FORMAT);
        }
        return new DateTime(temporal).toString(YMDHMS_FORMAT);
    }
}
