package moonstone.common.utils;

import lombok.EqualsAndHashCode;


@EqualsAndHashCode
public class Translate {
    private String originStr;
    private Object[] params;

    public Translate(String originStr) {
        this.originStr = originStr;
        if (originStr == null) {
            this.originStr = "";
        }
    }

    public Translate(String originStr, Object... params) {
        this.originStr = originStr;
        if (originStr == null) {
            this.originStr = "";
        }
        this.params = params;
    }

    public String getOriginStr() {
        return originStr;
    }

    public void setOriginStr(String originStr) {
        this.originStr = originStr;
    }

    /**
     * 获取对应字符串的对应翻译
     *
     * @param originStr 原数据
     * @param params    参数
     * @return 翻译
     */
    public static String of(String originStr, Object... params) {
        return new Translate(originStr, params).toString();
    }

    /**
     * 创建一个异常 错误信息为
     *
     * @param originStr 格式化字符串
     * @param params    参数
     * @return 运行异常
     */
    public static RuntimeException exceptionOf(String originStr, Object... params) {
        return new RuntimeException(of(originStr, params));
    }

    /**
     * 创建一个异常 错误信息为
     *
     * @param throwable 异常
     * @param originStr 格式化字符串
     * @param params    参数
     * @return 运行异常
     */
    public static RuntimeException exceptionOf(Throwable throwable, String originStr, Object... params) {
        return new RuntimeException(of(originStr, params), throwable);
    }

    @Override
    public String toString() {
        return String.format(TranslateUtil.getInstance().beautify(originStr), params);
    }
}
