package moonstone.common.utils;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;

/**
 * DATE: 16/5/1 下午10:27 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
public class RespUtil {
    public RespUtil() {
    }

    public static <T> T or(Response<T> resp, T failValue) {
        return resp.isSuccess()?resp.getResult():failValue;
    }

    public static Boolean orFalse(Response<Boolean> resp) {
        return or(resp, Boolean.FALSE);
    }

    public static <T> Response<T> log(Response<T> resp, String method, Object... params) {
        if(!resp.isSuccess()) {
            log.error("{} fail, cause=[{}], params=[{}]", method, resp.getError(), params);
        }
        return resp;
    }

    public static <T> T orJsonEx(Response<T> resp, String method, Object... params) {
        if(resp.isSuccess()) {
            return resp.getResult();
        } else {
            log.error("{} fail, cause=[{}], params=[{}]", method, resp.getError(), params);
            throw new JsonResponseException(500, resp.getError());
        }
    }

    public static <T> T orServerEx(Response<T> resp, String method, Object... params) {
        if(resp.isSuccess()) {
            return resp.getResult();
        } else {
            log.error("{} fail, cause=[{}], params=[{}]", method, params, resp.getError());
            throw new ServiceException(resp.getError());
        }
    }

}
