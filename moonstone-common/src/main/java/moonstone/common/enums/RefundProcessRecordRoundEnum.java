package moonstone.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundProcessRecordRoundEnum {

	REFUND_APPLY(10, "申请退款"),

	REFUND_APPLY_RECEIVE_RECEIPT(20, "收到申请退款回执"),

	/**
	 * 同意申请
	 */
	REFUND_APPLY_AGREE(30, "同意申请"),

	/**
	 * 上传物流单号
	 */
	UPLOAD_EXPRESS_NO(40, "上传物流单号"),

	/**
	 * 拒绝申请
	 */
	REFUND_APPLY_REJECT(80, "拒绝申请"),

	/**
	 * 退款失败
	 */
	REFUND_HANDLE_FAIL(99, "退款失败"),

	/**
	 * 退款完成
	 */
	REFUND_HANDLE_SUCCESS(100, "退款完成"),

	;

	private final int code;

	private final String desc;


	}
