package moonstone.common.enums;

public enum Y800GoodsAndRecordTypeEnum {
    BONDED("BONDED", "保税"),
    DUTY_PAID("DUTY_PAID", "完税"),
    ;

    private final String code;
    private final String description;

    Y800GoodsAndRecordTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
