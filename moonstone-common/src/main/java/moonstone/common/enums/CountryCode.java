package moonstone.common.enums;

import java.util.Optional;

public enum CountryCode {
    China("0086"), Australia("0061");
    String code;
    public static String PREFIX_CODE = "00";
    public static int PREFIX_CODE_LEN = 4;

    CountryCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static Optional<CountryCode> fromCode(String code) {
        for (CountryCode c : CountryCode.values()) {
            if (c.code.equals(code)) {
                return Optional.of(c);
            }
        }
        return Optional.empty();
    }
}
