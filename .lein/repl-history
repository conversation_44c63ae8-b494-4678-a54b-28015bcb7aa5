                              80)
                            port)] r_port)
(cond (= 1 1) (display "OK") (> 2 3) (display "done"))
(cond (= 1 1) (print "OK") (> 2 3) (print "done"))
(cond (= 1 1) (print "OK") (< 2 3) (print "done"))
(cond (= 1 3) (print "OK") (< 2 3) (print "done"))
(defn a [&a b] (if a (print "got a") (print "none")))
(a)
(a 1)
(a 1 2)
(defn a [&a &b] (if a (print "got a") (print "none")))
(a 1 2)
(a )
((fn ([] 1) ([a] 2)) 'a)
((fn ([] 1) ([a] 2)) )
(defn a "OO" [] )
(doc a)
(a)
#($1)
(#($1) 1)
(%($1) 1)
(string? "ash")
(map? {})
(number? 1)
(atom? 'a)
(type 'a)
(symbol? 'a)
(array? [])
(type [])
(Vector? [])
(vector? [])
(bigInt 1)
(BigDecimal. 1)
(BigDecimal. 12)
(BigDecimal. 5)
(BigDecimal. 5.2)
(BigDecimal. (str 5.2))
(character? 'a)
(character? \a)
(char? \a)
(Character/toUpperCase \a)
(keyword? :a)
(list? [])
(list? '[])
(list? '())
(set? [])
(set? {})
(set? #{})
(map (fn [x] (print x)) #{:a})
(instance? String "ash")
(instance? Iterable "ash")
(instance? Iterable [])
(instance? Iterable '())
(confd
)
(cond
true 1)
(type (ArrayList.))
(.getClass (ArrayList.))
(.getClassName (ArrayList.))
 (ArrayList.)
(ArrayList.)
(import java.util.ArrayList)
(.getClass (ArrayList.))
(.getName (.getClass (ArrayList.)))
(println "ash " 123)
(require funcool.promesa :as p)
(require promesa :as p)
(require promesa.core :as p)
(defn a [& b] (:ok b))
(a :ok 1)
(a :ok 1 2)
(a :ok 1 2 3)
(defn a [& b] (map b))
(a :ok 1 2 3)
(:ok (a :ok 1 2 3))
(defn a [& b] b)
 (a :ok 1 2 3)
(a :ok 1 2 3)
(def a 'clojure)
(def a 'clojure.edn)
(def f 'read-string)
(require a)
(require (eval a))
(require a)
a
(str a '/' f)
(str a "/" f)
(eval (str a "/" f))
(eval (eval (str a "/" f)))
(eval (eval-string (str a "/" f)))
(eval-string (str a "/" f))
(eval (str a "/" f))
 (str a "/" f)
(symbol (str a "/" f))
(eval (symbol (str a "/" f)))
(defn a [x y] (+ x y))
(apply a [1 2])
(defn a [{:keys [a b c]}] [a b c])
(a {:a 1 :b 2 :c :c})
(let loop [])
(let loop [x 1] (+ x 1))
(let loop [x +1] (+ x 1))
(let loop [[x +1]] (+ x 1))
(let loop [[x 1]] (+ x 1))
(letrec loop [[x 1]] (+ x 1))
{:a .a}
{:a a}
{:a .toString}
{:a toString}
{:a 'toString}
[:a 'toString]
(first (rest [:a 'toString]))
(type (first (rest [:a 'toString])))
(eval (type (first (rest [:a 'toString]))))
 (first (rest [:a 'toString])))
 (first (rest [:a 'toString]))
(first (rest [:a 'toString]))
.(first (rest [:a 'toString]))
(.(first (rest [:a 'toString])) 1)
(. 1 (first (rest [:a 'toString])) )
(. 1 toString)
(. 1 (eval (first (rest [:a 'toString])) ))
(. 1  (first (rest [:a 'toString])) )
(. 1 (first (rest [:a 'toString])) )
(. 1 (first (rest [:a '.toString])) )
(1 (first (rest [:a '.toString])) )
((first (rest [:a '.toString])) 1)
('String 1)
('toString 1)
.toString
1 .toString
(fn [x] (.toString x))
((fn [x] (.toString x)) 1)
(fn [x] (.toString x))
(fn [x] .toString x)
'toString
(eval 'toString)
(eval '.toString)
((eval '.toString) 1))
((eval '.toString) 1)
((eval '.toString 1))
((eval ".toString 1"))
(eval ".toString 1")
(eval-string ".toString 1")
(eval-string "(.toString 1)")
(eval (symbol "(.toString 1)"))
(Long/toString)
(Long/toString 1)
Long/toString
[Long/toString]
[Long/toString 1]
[Long/toString]
[Long#toString]
[Long\toString]
[Long/toString]
(Long/toString 1)
{:toString Long/toString}
{:toString Object/toString}
{:toString Objects/toString}
{:toString Object/toString}
(throw "error")
(throw (Exception.))
(throw (Exception. "NO IMPL"))
(load-string  "(display \"ok\") (+ 2 3)")
(load-string  "(println \"ok\") (+ 2 3)")
(clojure.core/list 1 2 3)
(clojure.core/list 1 2 3 4)
(clojure.core/vec 1 2 3 4)
(clojure.core/vector 1 2 3 4)
(conj [1 2 3] [4 5 6])
(cons [1 2 3] [4 5 6])
(concat [1 2 3] [4 5 6])
(let (concat [1 2 3] [4 5 6]))
(let (vector a 1) a)
(let [a 1] a)
(let (vector a 1) a)
(let '(vector a 1) a)
(let `(vector a 1) a)
(let ((a 1)) a)
(def a_list (java.util.ArrayList.))
(.add a_list 1)
(.add a_list 2)
(.add a_list "ok")
(list a_list)
(vector a_list)
(map (fn[x] (println x)) a_list)
(let [a 1] (set! a 2) (println a))
(let [&a 1] (set! a 2) (println a))
(let [a 1] (def a 2) (println a))
a
(set! a 1)
(set a 1)
(empty? {})
(empty? {1})
(empty? [1])
(/ 1 2)
(/ 1 2 3)
(/ 1 2)
(BigDecimal. (/ 1 2))
(BigDecimal. (toString (/ 1 2)))
(BigDecimal. (str (/ 1 2)))
(BigDecimal. (.toString (/ 1 2)))
(double (/ 1 2))
(def a {:1 2})
(import java.util.HashMap)
(HashMap. a)
(def b (HashMap. a))
(.get "1" b))
(:1 b)
(HashMap.)
(str 1 2 3 )
(try
(/ 1 0)
(catch ExceptionInfo e (print e)))
(try
(/ 1 0)
(catch Exception e (println e)))
(try
(/ 1 0)
(catch ExceptionInfo e (println e)))
(try
(/ 1 0)
(catch Exception e (println (str e)))))
(try
(/ 1 0)
(catch Exception e (println (str e))))
(try
(/ 1 0)
(catch Exception e (define E e)))
(define e (try (/ 1 0) (catch Exception e e)))
(def (try (/ 1 0) (catch Exception e e)))
(def E (try (/ 1 0) (catch Exception e e)))
E
(str E)
(.toString E)
(.getData E)
e
(eval "ash")
(load-string "ash")
(load-string "(a)")
(defn a[] (println "a"))
(load-string "(a)")
(load-string "(a) (b)")
(load-string "(a)\n(b)")
(load-string "")
(load-string nil)
(re-pattern "ash")
#"ash"
#".*"
(#".*" "
a")
(#".*" "a")
(re-matches #"(\\d*)/([^/]*)" "123/action")
(re-groups #"(\\d*)/([^/]*)" "123/action")
(re-find #"(\\d*)/([^/]*)" "123/action")
(re-pattern #"(\\d*)/([^/]*)" "123/action")
(re-find #"(\\d*)/([^/]*)" "123/action")
(re-pattern "(\\d*)/([^/]*)")
(def a (re-pattern "(\\d*)/([^/]*)"))
(def b "/213/auth")
(re-find b a)
(re-find a b)
(let [[a b c] [1 2 3]] (+ a b c))
(def a (re-pattern "/(\\d*)/([^/]+)"))
(re-find a b)
(re-find #"/(\\d*)/(.*+)" b)
(re-find #"(\d*)/([^/]*)" b)
(re-find #"/(\\d*)/(.*+)" b)
b
(re-find #"/(\\d*)/([^/]+)" b)
(re-find #"/(\\d+)/([^/]+)" b)
b
(re-find #"/(\\d+)/([^/]+)" b)
(re-find (re-pattern "/(\\d+)/([^/]+)" b)
)
(re-find (re-pattern "/(\\d+)/([^/]+)") b)
(re-find  #"/(\\d+)/([^/]+)" b)
(re-find (re-pattern "/(\\d+)/([^/]+)") b)
ls
(format "ash" 1)
(format "ash %s" 1)
(format "ash {}" 1)
(format "ash %s" (RuntimeException.))
(format "ash %s" (RuntimeException. "ash"))
(format "ash %s" [(RuntimeException. "ash") "ash"])
(format "ash %s" [(RuntimeException. "ash")])
(format "ash %e" [(RuntimeException. "ash")])
(format "ash %e" (RuntimeException. "ash"))
(format "ash %s" (RuntimeException. "ash"))
(format "ash %s" [(RuntimeException. "ash")])
(format "ash %s" ["ash" (RuntimeException. "ash")])
(defn a [ str & args] (format str args))
(a "Log -> %s" 123 456)
(a "Log -> %s" 123 456 789)
(a "Log -> %s %s" 123 456 789)
(defn a [ str & args] (apply format str args))
(a "Log -> %s %s" 123 456 789)
(a "Log -> %s %s %s" 123 456 789)
(a "Log -> %s %s %s" 123 456 789 [RuntimeException. "ash"])
(a "Log -> %s %s %s %s" 123 456 789 [RuntimeException. "ash"])
(a "Log -> %s %s %s" 123 456 789 [java.lang.RuntimeException. "ash"])
(a "Log -> %s %s %s" 123 456 789 [RuntimeException. "ash"])
(a "Log -> %s %s %s" 123 456 789 [RuntimeException.])
(RuntimeException. "ash")
(a "Log -> %s %s %s" 123 456 789 [RuntimeException.])
(a "Log -> %s %s %s" 123 456 789 [RuntimeException. "ash"])
(a "Log -> %s %s %s" 123 456 [RuntimeException. "ash"])
(a "Log -> %s %s %s" 123 456 789 [RuntimeException. "ash"])
(RuntimeException. "ash")
(defn a [ str & args] (format str args))
(a "Log -> %s %s %s" 123 456 789 [RuntimeException. "ash"])
(a "Log -> %s %s %s" 123 456 789 [RuntimeException. "ash")
(defn a [ str & args] (apply format str args))
(a "Log -> %s %s %s" 123 456 789 [(RuntimeException. "ash")])
(a "Log -> %s %s %s %s" 123 456 789 [(RuntimeException. "ash")])
(a "Log -> %s %s %s %s" 123 456 789 (RuntimeException. "ash"))
(re-pattern "%")
(re-find (re-pattern "%") "%s %a")
(re-groups (re-pattern "%") "%s %a")
(re-matches (re-pattern "%") "%s %a")
(re-matches (re-pattern "\\%") "%s %a")
(re-matches (re-pattern "%") "%s %a")
(re-matcher (re-pattern "%") "%s %s")
(re-seq (re-pattern "%") "%s %s")
(re-seq (re-pattern "%") "%s %s %a")
(count (re-seq (re-pattern "%") "%s %s %a"))
(last [1 2 3])
(replace "ash" "a" "1")
(replace "ash a" "a" "1")
(replace "ash a" "a")
(defn format'
  [str args]
  (if (= (count args) (re-seq (re-pattern "%") str))
    (apply format str args)
    (str (apply format str args)
         (.replaceAll (format "%s" (last args))
                      "\\n" "\n"))
    ))
(defn a [str & args] (format str args))
(defn b [str & args] (a str args))
(b "%s %s %s" 1 2 3)
(defn b [str & args] (apply a str args))
(b "%s %s %s" 1 2 3)
(defn a [str & args] (apply format str args))
(b "%s %s %s" 1 2 3)
(defn a [a str & args] (apply format str args))
(b "%s %s %s" 1 2 3)
(b 2 "%s %s %s" 1 2 3)
(defn format'
  [str & args]
  (if (= (count args) (re-seq (re-pattern "%") str))
    (apply format str args)
    (str (apply format str args)
         (.replaceAll (format "%s" [(last args)])
                      "\\n" "\n"))
    ))
(defn log [level str & args] (apply format' str args))
(defn a [str & logs ] (apply log 1 str logs))
(a "ash -> %s" 1 (RuntimeException. "error"))
(format' "%s" 1)
(defn format'
  [str & args]
  (if (= (count args) (count (re-seq (re-pattern "%") str)))
    (apply format str args)
    (str (apply format str args)
         (.replaceAll (format "%s" [(last args)])
                      "\\n" "\n"))
    ))
(format' "%s" 1)
(format' "%s" 1 (RuntimeException. "error"))
(.getStackTrace (RuntimeException. ))
(def a (.getStackTrace (RuntimeException. )))
(reduce (fn [_ i] (println i)) nil a)
a
(a 3)
(.get a 0)
(type a)
(first a)
(-> a (rest) (rest)(rest) (first))
(def a (atom 1))
(def b (or b @b))
b
(def c (or c (atom 0)))
c
@c
c
d
(or d 1)
(def a (or a (atom 1)))
a
(def a (or a (atom 2)))
a
(type b)
(var? b)
(var? a)
(var? c)
b
(true? b)
(false? b)
(atom? b)
(isa? b)
(isa? a)
(type a)
(type b)
(instance? Atom a)
(instance? clojure.lang.Atom a)
(if 1 (def b 1))
b
(not a)
(def a (or a a))
a
(when (not b) (def b 1))
(var b)
(var 1)
(var `b)
(var 'b)
(var? a)
(var? `a)
(var? 'a)
(def a (or a 1))
a
(def a 1)
(def a (or a 1))
a
(bound? a)
(bound? b)
(bound? @a)
(bound? `a)
(bound? 'a)
(bound? c)
(bound? `c)
(bound? 'c)
(bound? (eval 'c))
(bound? d)
#'a
(bound? #'a)
(bound? #'b)
(bound? #'c)
#'b
b
(def b (or b 1))
(bound? b)
(bound? #`b)
(bound? #'b)
(clojure.edn/read-string "['clojure.edn]")
(def a (clojure.edn/read-string "['clojure.edn]"))
a
(type a)
(type (first a))
(require (first a))
(require (eval (first a)))
(require (first a))
(def a (clojure.edn/read-string "[clojure.edn]"))
(type (first a))
'a
(type 'a)
(type '[a])
(type (first '[a]))
(first '[a])
(str (first '[a]))
(symbol (first '[a]))
(symbol (first '['a]))
(str (first '['a]))
(def a (clojure.edn/read-string "['clojure.edn]"))
a
(first a)
(str (first a))
(require a)
(require 'a)
'(require a)
(last '(require a))
(type (last '(require a)))
(require "a")
(require 'clojure.edn)
(require (last '[clojure.edn]))
(require (last '['clojure.edn]))
(require 'clojure.edn)
(require clojure.edn)
(require clojure)
(name)
(name 1)
(name "',.")
(type (name "',."))
(name clojure)
(empty? "")
(empty? "1")
(empty? nil)
(assoc {} :a "")
(-> {} (assoc :a "1") (assoc :b "2"))
(clojure.uuid )
(require clojure.uuid )
(require clojure.uuid)
(type clojure.uuid)
(type 'clojure.uuid)
(concat [[1] [2]])
(join [[1] [2]])
(concat [[1] [2]])
(concat "1" "2")
(apply concat [[1] [2]])
