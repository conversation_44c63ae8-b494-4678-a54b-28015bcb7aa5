package moonstone.user.impl.dao;

import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import moonstone.user.model.SubSellerRole;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

public class SellerRoleDaoTest extends BaseDaoTest {

    @Autowired
    private SubSellerRoleDao subSellerRoleDao;

    private SubSellerRole mock(Long masterUserId, Integer status) {
        //SubSellerRole
        SubSellerRole role = new SubSellerRole();
        role.setName("name");
        role.setDesc("desc");
        role.setMasterUserId(masterUserId);
        role.setStatus(status);
        role.setAllowJson("[\"k1\",\"k2\"]");
        role.setExtraJson("{\"key\":1}");
        return role;
    }

    @Test
    public void testFindById() {
        SubSellerRole role = mock(1L, 1);
        subSellerRoleDao.create(role);

        SubSellerRole actual = subSellerRoleDao.findById(role.getId());
        assertNotNull(actual);

        SubSellerRole role2 = mock(1L, 2);
        subSellerRoleDao.create(role2);

        List<SubSellerRole> founds = subSellerRoleDao.findByIds(Lists.newArrayList(role.getId(), role2.getId()));
        assertEquals(2, founds.size());
        List<Long> actualIds = Lists.newArrayList(founds.get(0).getId(), founds.get(1).getId());
        assertThat(actualIds, containsInAnyOrder(role2.getId(), role.getId()));
    }

    @Test
    public void testUpdate() {
        SubSellerRole role = mock(1L, 1);
        subSellerRoleDao.create(role);

        SubSellerRole toUpdate = new SubSellerRole();
        toUpdate.setId(role.getId());
        toUpdate.setName("_name");
        toUpdate.setDesc("_desc");
        toUpdate.setMasterUserId(2L);
        toUpdate.setAllowJson("[\"_changed\"]");
        toUpdate.setExtraJson("{\"changed\":1}");

        subSellerRoleDao.update(toUpdate);

        SubSellerRole actual = subSellerRoleDao.findById(role.getId());
        assertEquals(toUpdate.getName(), actual.getName());
        assertEquals(toUpdate.getDesc(), actual.getDesc());
        assertEquals(toUpdate.getMasterUserId(), actual.getMasterUserId());
        assertEquals(toUpdate.getAllowJson(), actual.getAllowJson());
        assertEquals(toUpdate.getExtraJson(), actual.getExtraJson());
    }

    @Test
    public void testDelete() {
        SubSellerRole role = mock(2L, 1);
        subSellerRoleDao.create(role);
        assertNotNull(subSellerRoleDao.findById(role.getId()));

        subSellerRoleDao.delete(role.getId());
        assertNull(subSellerRoleDao.findById(role.getId()));
    }

    @Test
    public void testPaging() {
        List<SubSellerRole> roles = Lists.newArrayList(
                mock(1L, 1),
                mock(1L, 2),
                mock(1L, 1),
                mock(2L, 1)
        );
        for (SubSellerRole role : roles) {
            subSellerRoleDao.create(role);
        }
        SubSellerRole criteria = new SubSellerRole();
        criteria.setMasterUserId(1L);
        Paging<SubSellerRole> result = subSellerRoleDao.paging(0, 10, criteria);
        assertThat(result.getTotal(), is(3L));
        List<Long> ids = Lists.newArrayList();
        for (SubSellerRole o : result.getData()) {
            ids.add(o.getId());
        }
        assertThat(ids, containsInAnyOrder(roles.get(0).getId(), roles.get(1).getId(), roles.get(2).getId()));


        criteria.setStatus(1);
        result = subSellerRoleDao.paging(0, 10, criteria);
        assertThat(result.getTotal(), is(2L));
        assertThat(
                Lists.newArrayList(result.getData().get(0).getId(), result.getData().get(1).getId()),
                containsInAnyOrder(roles.get(0).getId(), roles.get(2).getId())
        );
    }

    @Test
    public void testFindByMasterUserIdAndStatus() {
        List<SubSellerRole> roles = Lists.newArrayList(
                mock(1L, 1),
                mock(1L, 2),
                mock(1L, 1),
                mock(2L, 1)
        );
        for (SubSellerRole role : roles) {
            subSellerRoleDao.create(role);
        }

        List<SubSellerRole> result = subSellerRoleDao.findByMasterUserIdAndStatus(1L, 1);
        assertThat(result.size(), is(2));
        assertThat(
                Lists.newArrayList(result.get(0).getId(), result.get(1).getId()),
                containsInAnyOrder(roles.get(0).getId(), roles.get(2).getId())
        );
    }
}
