package moonstone.user.impl.dao;


import com.google.common.collect.Lists;
import moonstone.user.model.UserProfile;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * Created by cuiwen<PERSON>o on 16/3/8.
 */
public class UserProfileDaoTest extends BaseDaoTest  {

    @Autowired
    private UserProfileDao userProfileDao;

    private UserProfile userProfile;

    private List<Long> ids;

    /**
     * create a new UserProfile
     *
     * @param id
     * @return
     */
    private UserProfile createOne(Long id) {
        //UserProfile
        userProfile = new UserProfile();
        userProfile.setId(id);
        userProfile.setUserId(1 + id);
        userProfile.setRealName("real");
        userProfile.setGender(1);
        userProfile.setProvinceId(1);
        userProfile.setProvince("zhejiang");
        userProfile.setCityId(2);
        userProfile.setCity("hangzhou");
        userProfile.setRegionId(3);
        userProfile.setRegion("binjiang");
        userProfile.setStreet("liuhe");
        userProfile.setAvatar("avatar");
        userProfile.setBirth("birth");
        userProfile.setCreatedAt(new Date());
        userProfile.setUpdatedAt(new Date());


        return userProfile;
    }
    private void testFindById(){
        UserProfile model = userProfileDao.findById(1L);
        Assert.assertNotNull(model.getId());
    }
    private void testFindByUserId(){
        UserProfile model = userProfileDao.findByUserId(3L);
        Assert.assertNotNull(model.getId());
    }

    private void testUpdate(){
        Boolean result = userProfileDao.update(userProfile);
        Assert.assertTrue(result);
    }

    private void testDelet(){
        Assert.assertTrue(userProfileDao.delete(2L));
    }

    private void testDeletByUserId() {
        Assert.assertTrue(userProfileDao.deleteByUserId(3L));
    }

    private void testFindByIds() {
        List<UserProfile> result = userProfileDao.findByIds(ids);
        Assert.assertTrue(!result.isEmpty());
    }

    @Test
    public void testUserRole(){
        ids = Lists.newArrayList();
        for (int i = 0;i < 5; i++) {
            UserProfile model = createOne(Long.valueOf(i));
            userProfileDao.create(model);
            ids.add(model.getId());
        }
        testFindById();
        testFindByIds();
        testFindByUserId();
        testUpdate();

        testDelet();
        testDeletByUserId();
    }
}
