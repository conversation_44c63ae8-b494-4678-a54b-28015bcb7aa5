/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.impl.dao;

import com.google.common.collect.Lists;
import moonstone.user.model.Operator;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

public class OperatorDaoTest extends BaseDaoTest {

    @Autowired
    private OperatorDao operatorDao;

    private Operator mock(Long userId, Long roleId, Integer status) {
        Operator op = new Operator();
        op.setUserId(userId);
        op.setRoleId(roleId);
        op.setStatus(status);
        op.setExtraJson("{\"key\":\"value\"}");
        return op;
    }

    @Test
    public void testFinds() {
        // create
        Operator toCreate = mock(1L, 1L, 0);
        Boolean result = operatorDao.create(toCreate);
        assertThat(result, is(true));

        // find by ID
        Operator model = operatorDao.findById(toCreate.getId());
        assertNotNull(model);

        // find by IDs
        List<Long> ids = Lists.newArrayList();
        ids.add(toCreate.getId());
        List<Operator> modelList = operatorDao.findByIds(ids);
        assertTrue(!modelList.isEmpty());
    }

    @Test
    public void testFindByUserId() {
        operatorDao.create(mock(1L, 1L, 0));

        assertNotNull(operatorDao.findByUserId(1L));
    }

    @Test
    public void testUpdate() throws Exception {
        Operator op = new Operator();
        op.setUserId(1L);
        op.setStatus(0);
        op.setRoleId(3L);
        operatorDao.create(op);

        op.setRoleId(2L);
        operatorDao.update(op);

        Operator actual = operatorDao.findById(op.getId());
        assertThat(actual.getRoleId(), is(2L));
    }
}
