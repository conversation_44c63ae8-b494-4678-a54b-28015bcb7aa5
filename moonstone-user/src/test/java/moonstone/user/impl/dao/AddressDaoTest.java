/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.impl.dao;

import moonstone.user.address.model.Address;
import moonstone.user.impl.address.dao.AddressDao;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * 菜鸟地址测试
 *
 * Author  : panxin
 * Date    : 10:09 AM 3/7/16
 * Mail    : <EMAIL>
 */
public class AddressDaoTest extends BaseDaoTest{

    @Autowired
    private AddressDao addressDao;

    /**
     * 参照 schema.sql 的测试数据
     */
    @Test
    public void testFind(){
        // 中国
        Address top = addressDao.findById(1);
        assertThat(top.getEnglishName(), is("China"));

        // 获得省, 北京, 浙江
        List<Address> lv1 = addressDao.findByPid(top.getId());
        assertThat(lv1.size(), is(2));

        // 获得市 schema.sql 浙江 ID = 330000  杭州, 宁波
        List<Address> lv2 = addressDao.findByPid(330000);
        assertThat(lv2.size(), is(2));

        // 获得区 schema.sql 杭州市 ID = 330100
        List<Address> lv3 = addressDao.findByPid(330100);
        assertThat(lv3.size(), is(14));

        // 获得街道 schema.sql 滨江区 ID = 330108
        List<Address> lv4 = addressDao.findByPid(330108);
        assertThat(lv4.size(), is(3));
    }

}
