/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.impl.dao;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 用户模块DAO层测试
 *   1: 用户信息
 *   2: 收货地址
 *
 *  <NAME_EMAIL>
 */
@Configuration
@EnableAutoConfiguration
@ComponentScan({"moonstone.user.impl.dao",
        "moonstone.user.impl.address.dao"})
public class DaoConfiguration {
}
