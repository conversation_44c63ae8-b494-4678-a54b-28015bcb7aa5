package moonstone.user.impl.dao;

import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import moonstone.user.model.User;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Date: 6/30/16
 * Time: 10:18 AM
 * Author: 2016年 <a href="mailto:<EMAIL>">张成栋</a>
 */
public class UserDaoTest extends BaseDaoTest {
    private User user;

    private User make() {
        User user = new User();
        user.setName("test");
        user.setType(1);
        user.setStatus(1);
        return user;
    }

    @Autowired
    private UserDao userDao;

    @Before
    public void setup() {
        user = make();
        userDao.create(user);

        assertNotNull(user.getId());
    }

    @Test
    public void testPaging() {
        User another = make();
        another.setName("another");
        userDao.create(another);

        Map<String, Object> criteria = Maps.newHashMap();
        Paging<User> paging = userDao.paging(0, 20, criteria);
        assertEquals(2L, paging.getTotal().longValue());
        assertEquals(2, paging.getData().size());

        criteria.put("name", "test");
        paging = userDao.paging(0, 20, criteria);
        assertEquals(1L, paging.getTotal().longValue());
        assertEquals(1, paging.getData().size());
    }
}
