/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.impl.dao;

import com.google.common.collect.Lists;
import moonstone.user.model.SubSeller;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

public class SubSellerDaoTest extends BaseDaoTest {

    @Autowired
    private SubSellerDao subSellerDao;

    private SubSeller mock(Long userId, Long masterUserId, Long roleId){
        SubSeller ss = new SubSeller();
        ss.setUserId(userId);
        ss.setUserName("user" + userId);
        ss.setMasterUserId(masterUserId);
        ss.setRoleId(roleId);
        ss.setRoleName("role" + roleId);
        ss.setStatus(0);
        ss.setExtraJson("{\"key\":\"value\"}");
        return ss;
    }

    @Test
    public void testFinds(){
        SubSeller toCreate = mock(1L, 1L, 1L);
        // create
        Boolean result = subSellerDao.create(toCreate);
        assertThat(result, is(true));

        // find by ID
        SubSeller model = subSellerDao.findById(toCreate.getId());
        assertNotNull(model);

        // find by IDs
        List<Long> ids = Lists.newArrayList();
        ids.add(toCreate.getId());
        List<SubSeller> modelList = subSellerDao.findByIds(ids);
        assertTrue(!modelList.isEmpty());
    }

    @Test
    public void testFindByShopIdAndUserId(){
        SubSeller toCreate = mock(1L, 1L, 1L);
        subSellerDao.create(toCreate);

        // find by userId
        SubSeller subSeller = subSellerDao.findByUserId(toCreate.getUserId());
        assertNotNull(subSeller);
    }
}
