package moonstone.user.impl.dao;

import moonstone.user.model.ThirdPartyUser;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Author:cp
 * Created on 1/13/17
 */
public class ThirdPartyUserDaoTest extends BaseDaoTest {
    private ThirdPartyUser thirdPartyUser;

    @Autowired
    private ThirdPartyUserDao thirdPartyUserDao;

    @Before
    public void setUp() {
        thirdPartyUser = new ThirdPartyUser();
        thirdPartyUser.setId(1L);
        thirdPartyUser.setUserId(1L);
        thirdPartyUser.setThirdPartyId("3464557567");
        thirdPartyUser.setType(1);
        thirdPartyUserDao.create(thirdPartyUser);
    }

    @Test
    public void testCreate() {
        ThirdPartyUser actual = thirdPartyUserDao.findById(thirdPartyUser.getId());
        Assert.assertNotNull(actual.getId());
    }

    @Test
    public void testDelete() {
        thirdPartyUserDao.delete(thirdPartyUser.getId());
        ThirdPartyUser actual = thirdPartyUserDao.findById(thirdPartyUser.getId());
        Assert.assertNull(actual);
    }

    @Test
    public void testFindByThirdPartyId() {
        ThirdPartyUser actual = thirdPartyUserDao.findByTypeAndThirdPartyId(
                thirdPartyUser.getType(), thirdPartyUser.getThirdPartyId());
        Assert.assertNotNull(actual);
    }
}
