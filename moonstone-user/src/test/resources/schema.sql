-- Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.

-- 用户表: parana_users
CREATE TABLE `parana_users` (
  `id`                BIGINT          NOT NULL    AUTO_INCREMENT,
  `name`              VARCHAR(40)     NULL        COMMENT '用户名',
  `email`             VARCHAR(32)     NULL        COMMENT '邮件',
  `mobile`            VARCHAR(16)     NULL        COMMENT '手机号码',
  `password`          VARCHAR(32)     NULL        COMMENT '登录密码',
  `type`              SMALLINT        NOT NULL    COMMENT '用户类型',
  `status`            TINYINT         NOT NULL    COMMENT '状态 0:未激活, 1:正常, -1:锁定, -2:冻结, -3: 删除',
  `roles_json`        VARCHAR(512)    NULL        COMMENT '用户角色信息',
  `extra_json`        VARCHAR(1024)   NULL        COMMENT '用户额外信息,建议json字符串',
  `tags_json`         VARCHAR(1024)   NULL        COMMENT '用户标签的json表示形式,只能运营操作, 对商家不可见',
  `created_at`        DATETIME        NOT NULL,
  `updated_at`        DATETIME        NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_users_name(name),
  UNIQUE KEY idx_users_email(email),
  UNIQUE KEY idx_users_mobile(mobile)
) COMMENT='用户表';

-- 菜鸟地址表
CREATE TABLE `parana_addresses` (
  `id`                BIGINT          NOT NULL,
  `pid`               BIGINT          NULL        COMMENT '父级ID',
  `name`              VARCHAR(50)     NULL        COMMENT '名称',
  `level`             INT             NULL        COMMENT '级别',
  `pinyin`            VARCHAR(100)    NULL        COMMENT '拼音',
  `english_name`      VARCHAR(100)    NULL        COMMENT '英文名',
  `unicode_code`      VARCHAR(200)    NULL        COMMENT 'ASCII码',
  `order_no`          VARCHAR(32)     NULL        COMMENT '排序号',
  PRIMARY KEY (`id`)
);

-- 用户详情表: parana_user_profiles
CREATE TABLE `parana_user_profiles` (
  `id`                BIGINT          NOT NULL    AUTO_INCREMENT,
  `user_id`           BIGINT          NULL        COMMENT '用户id',
  `realname`          VARCHAR(32)     NULL        COMMENT '真实姓名',
  `gender`            SMALLINT        NULL        COMMENT '性别1男2女',
  `province_id`       BIGINT          NOT NULL    COMMENT '省id',
  `province`          VARCHAR(100)    NOT NULL    COMMENT '省',
  `city_id`           BIGINT          NULL        COMMENT '城id',
  `city`              VARCHAR(100)    NULL        COMMENT '城',
  `region_id`         BIGINT          NULL        COMMENT '区id',
  `region`            VARCHAR(100)    NULL        COMMENT '区',
  `street`            VARCHAR(130)    NULL        COMMENT '地址',
  `extra_json`        VARCHAR(2048)   NULL        COMMENT '其他信息, 以json形式存储',
  `avatar`            VARCHAR(512)    NOT NULL    COMMENT '头像',
  `birth`             VARCHAR(40)     NULL        COMMENT '出生日期',
  `created_at`        DATETIME        NOT NULL,
  `updated_at`        DATETIME        NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_user_id(user_id)
) COMMENT='用户详情表';

-- 运营角色表
CREATE TABLE `parana_operator_roles` (
  `id`         BIGINT        NOT NULL AUTO_INCREMENT,
  `name`       VARCHAR(32)   NULL COMMENT '角色名',
  `desc`       VARCHAR(256)  NULL COMMENT '角色描述',
  `status`     TINYINT       NOT NULL COMMENT '0. 未生效(冻结), 1. 生效, -1. 删除',
  `allow_json` VARCHAR(2048) NULL COMMENT '角色对应选中权限树节点列表',
  `extra_json` VARCHAR(4096) NULL COMMENT '用户额外信息,建议json字符串',
  `created_at` DATETIME      NOT NULL,
  `updated_at` DATETIME      NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT = '运营角色表';

-- 用户运营表
CREATE TABLE `parana_user_operators` (
  `id`         BIGINT NOT NULL AUTO_INCREMENT,
  `user_id`    BIGINT          NOT NULL COMMENT '用户 ID',
  `user_name`  VARCHAR(64)     NULL COMMENT '用户名 (登录名, 冗余)',
  `role_id`    BIGINT          NULL COMMENT '运营角色 ID',
  `role_name`  VARCHAR(32)     NULL COMMENT '角色名 (冗余)',
  `status`     TINYINT         NOT NULL COMMENT '运营状态',
  `extra_json` VARCHAR(4096)   NULL COMMENT '运营额外信息, 建议json字符串',
  `created_at` DATETIME        NOT NULL,
  `updated_at` DATETIME        NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT = '用户运营表';
CREATE UNIQUE INDEX idx_user_operator_user_id ON `parana_user_operators` (`user_id`);
CREATE INDEX idx_user_operator_role_id ON `parana_user_operators` (`role_id`);

-- 商家角色表
CREATE TABLE `parana_sub_seller_roles` (
  `id`             BIGINT        NOT NULL AUTO_INCREMENT,
  `name`           VARCHAR(32)   NULL COMMENT '角色名',
  `desc`           VARCHAR(256)  NULL COMMENT '角色描述',
  `master_user_id` BIGINT        NOT NULL COMMENT '主账户用户 ID',
  `status`         TINYINT       NOT NULL COMMENT '0. 未生效(冻结), 1. 生效, -1. 删除',
  `allow_json`     VARCHAR(2048) NULL COMMENT '角色对应选中权限树节点列表',
  `extra_json`     VARCHAR(4096) NULL COMMENT '用户额外信息,建议json字符串',
  `created_at`     DATETIME      NOT NULL,
  `updated_at`     DATETIME      NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT = '商家角色表';
CREATE INDEX idx_sub_seller_roles_master_user_id ON parana_sub_seller_roles (`master_user_id`);

-- 商家子账户表
CREATE TABLE `parana_user_sub_sellers` (
  `id`             BIGINT        NOT NULL AUTO_INCREMENT,
  `user_id`        BIGINT        NOT NULL COMMENT '用户 ID',
  `user_name`      VARCHAR(64)   NULL COMMENT '用户名 (冗余)',
  `master_user_id` BIGINT        NOT NULL COMMENT '主账户用户 ID',
  `role_id`        BIGINT        NULL COMMENT '角色 ID',
  `role_name`      VARCHAR(32)   NULL COMMENT '角色名 (冗余)',
  `status`         TINYINT       NOT NULL COMMENT '状态',
  `extra_json`     VARCHAR(4096) NULL COMMENT '用户额外信息, 建议json字符串',
  `created_at`     DATETIME      NOT NULL,
  `updated_at`     DATETIME      NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT = '商家子账户表';
CREATE UNIQUE INDEX idx_user_sub_seller_user_id ON `parana_user_sub_sellers` (`user_id`);
CREATE INDEX idx_user_sub_seller_sub_id ON `parana_user_sub_sellers` (master_user_id);

-- 第三方用户
CREATE TABLE `parana_third_party_users` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '关联的内部用户id',
  `third_party_id` varchar(64) NOT NULL COMMENT '第三方用户id',
  `type` smallint(6) NOT NULL COMMENT '第三方类型',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
)COMMENT='第三方用户表';
create index idx_tpu_third_party_id_type on parana_third_party_users(third_party_id,`type`);
