package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.model.UserProfile;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by cu<PERSON><PERSON><PERSON><PERSON> on 16/3/8.
 */
@Repository
public class UserProfileDao extends MyBatisDao<UserProfile> {

    /**
     * 以用户id查询用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    public UserProfile findByUserId(Long userId) {
        return getSqlSession().selectOne(sqlId("findByUserId"), userId);
    }

    /**
     * 删除
     *
     * @param userId 用户id
     * @return 是否删除成功
     */
    public Boolean deleteByUserId(Long userId) {
        return getSqlSession().delete(sqlId("deleteByUserId"), userId) == 1;
    }

    /**
     * 修改用户的地区id
     *
     * @param userProfile 用户
     * @return 是否修改成功
     */
    public Boolean updateAreaId(UserProfile userProfile) {
        return getSqlSession().update(sqlId("updateAreaId"), userProfile) == 1;
    }

    /**
     * 获得用户信息最大id
     */
    public Long maxId() {
        return getSqlSession().selectOne(sqlId("maxId"));
    }

    public List<UserProfile> findProfileByNickName(String nickName) {
        return getSqlSession().selectList(sqlId("findProfileByNickName"),
                ImmutableMap.of("nickName", nickName));
    }

    public List<UserProfile> findProfileByRealNameLike(String realName) {
        return getSqlSession().selectList(sqlId("findProfileByRealNameLike"), ImmutableMap.of("realName", realName));
    }

    public List<UserProfile> findByUserIds(List<Long> userIds) {
        return getSqlSession().selectList(sqlId("findByUserIds"), Map.of("userIds", userIds));
    }
}

