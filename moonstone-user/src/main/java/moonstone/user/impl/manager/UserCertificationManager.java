package moonstone.user.impl.manager;

import lombok.extern.slf4j.Slf4j;
import moonstone.user.impl.dao.UserCertificationDao;
import moonstone.user.model.UserCertification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by CaiZhy on 2018/11/14.
 */
@Slf4j
@Component
public class UserCertificationManager {
    @Autowired
    private UserCertificationDao userCertificationDao;

    /**
     * 设置默认实名认证信息
     * 若原来有一个默认实名信息, 如果这个设置不成功, 那么原来那个默认实名信息应该还是默认的.
     * 不加事务的话，如果这个默认实名信息设置不成功, 就有可能导致原来默认实名信息消失, 导致错误.
     *
     * @param id   实名认证信息ID
     * @param userId      用户ID
     */
    @Transactional
    public void makeDefault(Long id, Long userId){
        UserCertification userCertification = new UserCertification();
        userCertification.setUserId(userId);
        userCertification.setIsDefault(Boolean.FALSE);
        userCertificationDao.updateByUserId(userCertification);
        userCertificationDao.makeDefault(id, userId);
    }
}
