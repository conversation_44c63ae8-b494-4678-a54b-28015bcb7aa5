package moonstone.user.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.user.impl.dao.IntegralRiskManagementDao;
import moonstone.user.model.IntegralRiskManagement;
import moonstone.user.service.IntegralRiskManagementReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/11/4 16:18
 */
@Slf4j
@Service
@RpcProvider
public class IntegralRiskManagementReadServiceImpl implements IntegralRiskManagementReadService {
    @Autowired
    IntegralRiskManagementDao integralRiskManagementDao;

    @Override
    public Either<Optional<IntegralRiskManagement>> findByRiskManagementType(Long shopId, Integer code) {
        return Either.ok(Optional.ofNullable(integralRiskManagementDao.findByRiskManagementType(shopId, code)));
    }
}
