package moonstone.user.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.IntegralRiskManagementDao;
import moonstone.user.model.IntegralRiskManagement;
import moonstone.user.service.IntegralRiskManagementWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/11/4 16:19
 */
@Slf4j
@Service
@RpcProvider
public class IntegralRiskManagementWriteServiceImpl implements IntegralRiskManagementWriteService {
    @Autowired
    IntegralRiskManagementDao integralRiskManagementDao;

    @Override
    public Either<Long> insert(IntegralRiskManagement risk) {
        try {
            if (integralRiskManagementDao.create(risk)) {
                return Either.ok(risk.getId());
            }
            return Either.error("fail.create.IntegralRiskManagement");
        } catch (Exception ex) {
            log.error("{} IntegralRiskManagement:{}", LogUtil.getClassMethodName(), risk);
            return Either.error(ex, "fail.create.IntegralRiskManagement");
        }
    }
}
