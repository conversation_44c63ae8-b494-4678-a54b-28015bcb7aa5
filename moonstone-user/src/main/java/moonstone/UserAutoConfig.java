/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone;

import moonstone.common.config.ImageConfig;
import moonstone.user.ext.DefaultUserTypeBean;
import moonstone.user.ext.UserTypeBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@ComponentScan({"moonstone.user.impl"})
@Import(ImageConfig.class)
public class UserAutoConfig {

    @ConditionalOnMissingBean(UserTypeBean.class)
    @Configuration
    public static class DefaultUserTypeBeanConfig {

        @Bean
        public UserTypeBean userTypeBean() {
            return new DefaultUserTypeBean();
        }
    }
}
