<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="DataExportTask">
    <resultMap id="BaseResultMap" type="moonstone.user.model.DataExportTask">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="shop_id" property="shopId"/>
        <result column="type" property="type"/>
        <result column="file_name" property="fileName"/>

        <result column="file_url" property="fileUrl"/>
        <result column="status" property="status"/>
        <result column="exception_message" property="exceptionMessage"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>

        <result column="extra_json" property="extraJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="base_columns">
        `id`, `shop_id`, `user_id`, `type`, `file_name`,
        `file_url`, `status`, `exception_message`, `start_time`, `end_time`,
        `extra_json`, `created_at`, `updated_at`
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="base_columns"></include>
        from parana_data_export_task
        where id = #{id}
    </select>

    <insert id="insertSelective" parameterType="moonstone.user.model.DataExportTask" keyProperty="id" useGeneratedKeys="true">
        insert into parana_data_export_task
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null">
                user_id,
            </if>
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="fileUrl != null">
                file_url,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="exceptionMessage != null">
                exception_message,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="extraJson != null">
                extra_json,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="shopId != null">
                #{shopId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="fileName != null">
                #{fileName},
            </if>
            <if test="fileUrl != null">
                #{fileUrl},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="exceptionMessage != null">
                #{exceptionMessage},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="extraJson != null">
                #{extraJson},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="updatedAt != null">
                #{updatedAt}
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="moonstone.user.model.DataExportTask">
        update parana_data_export_task
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="fileName != null">
                file_name = #{fileName},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="exceptionMessage != null">
                exception_message = #{exceptionMessage},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="extraJson != null">
                extra_json = #{extraJson},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
        </set>
        where id = #{id}
    </update>

    <sql id="paging_criteria">
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="shopId != null">
                and shop_id = #{shopId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="fileName != null">
                and file_name = #{fileName}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="startTimeFrom != null">
                and <![CDATA[ start_time >= #{startTimeFrom} ]]>
            </if>
            <if test="startTimeTo != null">
                and <![CDATA[ start_time <= #{startTimeTo} ]]>
            </if>
            <if test="endTimeFrom != null">
                and <![CDATA[ end_time >= #{endTimeFrom} ]]>
            </if>
            <if test="endTimeTo != null">
                and <![CDATA[ end_time <= #{endTimeTo} ]]>
            </if>
        </where>
    </sql>

    <select id="paging" parameterType="moonstone.user.criteria.DataExportTaskCriteria" resultMap="BaseResultMap">
        select
        <include refid="base_columns"></include>

        from parana_data_export_task
        <include refid="paging_criteria"></include>

        order by id desc

        limit #{offset}, #{limit}
    </select>

    <select id="count" resultType="java.lang.Long" parameterType="moonstone.user.criteria.DataExportTaskCriteria">
        select count(*)
        from parana_data_export_task
        <include refid="paging_criteria"></include>
    </select>
</mapper>