<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Supplier">
    <resultMap id="SupplierMap" type="Supplier">
        <id column="id" property="id"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="mobile" property="mobile"/>
        <result column="company_name" property="companyName"/>
        <result column="main_categories" property="mainCategories"/>
        <result column="business_license_no" property="businessLicenseNo"/>
        <result column="business_license_image_url" property="businessLicenseImageUrl"/>
        <result column="legal_person" property="legalPerson"/>
        <result column="legal_person_id_number" property="legalPersonIdNumber"/>
        <result column="legal_person_image_url" property="legalPersonImageUrl"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="LongMap" type="Long"></resultMap>

    <sql id="tb">
        parana_supplier
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        user_id, user_name, mobile, company_name, main_categories, business_license_no, business_license_image_url,
        legal_person, legal_person_id_number, legal_person_image_url, status, created_at, updated_at
    </sql>

    <sql id="vals">
        #{userId}, #{userName}, #{mobile}, #{companyName}, #{mainCategories}, #{businessLicenseNo},
        #{businessLicenseImageUrl},
        #{legalPerson}, #{legalPersonIdNumber}, #{legalPersonImageUrl}, #{status}, #{createdAt}, #{updatedAt}
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'userId'">ORDER BY user_id
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>


    <insert id="create" parameterType="Supplier" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findByUserId" parameterType="long" resultMap="SupplierMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id = #{userId}
    </select>


    <update id="update" parameterType="Supplier">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="mainCategories != null">main_categories = #{mainCategories},</if>
            <if test="businessLicenseNo != null">business_license_no = #{businessLicenseNo},</if>
            <if test="businessLicenseImageUrl != null">business_license_image_url = #{businessLicenseImageUrl},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="legalPersonIdNumber != null">legal_person_id_number = #{legalPersonIdNumber},</if>
            <if test="legalPersonImageUrl != null">legal_person_image_url = #{legalPersonImageUrl},</if>
            <if test="status != null">status = #{status},</if>
            id = id
        </set>
        WHERE id=#{id}
    </update>

</mapper>
