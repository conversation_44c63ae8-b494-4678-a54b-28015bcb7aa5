<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="IntegralRiskManagement">

    <resultMap id="IntegralRiskManagementMap" type="IntegralRiskManagement">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="num" property="num"/>
        <result column="frequency" property="frequency"/>
        <result column="type" property="type"/>
        <result column="time_type" property="timeType"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
       parana_integral_risk_management
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
         shop_id,num, frequency,`type`,time_type,  `status`, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
         #{shopId},#{num} ,#{frequency} ,#{type}, #{timeType}, #{status}, #{extraStr}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="status != null">AND `status` = #{status}</if>
        <if test="shopId != null">AND `shop_id` = #{shopId}</if>
    </sql>


    <insert id="create" parameterType="IntegralRiskManagement" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="IntegralRiskManagement">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="shopId != null">shop_id = #{shopId},</if>
        <if test="num != null">num = #{num},</if>
        <if test="frequency != null">frequency = #{frequency},</if>
        <if test="type != null">type = #{type},</if>
        <if test="timeType != null">time_type = #{timeType},</if>
        <if test="status != null and status != 1 ">`status` = #{status},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="IntegralRiskManagementMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByRiskManagementType" parameterType="map" resultMap="IntegralRiskManagementMap">
        SELECT id ,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where shop_id=#{shopId}
        AND `type`=#{type}
        ORDER BY created_at DESC
        LIMIT 1
    </select>

</mapper>