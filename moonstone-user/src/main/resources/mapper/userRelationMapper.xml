<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="UserRelationEntity">
    <resultMap id="UserRelationEntityMap" type="UserRelationEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="relation_id" property="relationId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="relation_id_a" property="additionRelationA"/>
        <result column="relation_id_b" property="additionRelationB"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <resultMap id="LongMap" type="Long"/>

    <sql id="tb">
        parana_user_relation_entity
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        user_id, relation_id, type, status, extra_json,relation_id_a,relation_id_b, created_at, updated_at
    </sql>

    <sql id="vals">
        #{userId}, #{relationId}, #{type}, #{status},
        #{extraStr},#{additionRelationA},#{additionRelationB}, now(), now()
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'userId'">ORDER BY `user_id`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'relationId'">ORDER BY `relation_id`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <sql id="criteria">
        <if test="id != null">and `id` = #{id}</if>
        <if test="ids != null">and `id` in
            <foreach collection="ids" item="i" close=")" open="(" separator=",">
                #{i}
            </foreach>
        </if>
        <if test="userId != null">and `user_id` = #{userId}</if>
        <if test="userIds != null">and `user_id` in
            <foreach collection="userIds" item="ui" open="(-1," close=")" separator=",">
                #{ui}
            </foreach>
        </if>
        <if test="relationId != null">and `relation_id` = #{relationId}</if>
        <if test="relationIdList != null">
            and `relation_id` in
            <foreach collection="relationIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="relationIds != null">
            and `relation_id` in
            <foreach collection="relationIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="relationIdA != null">and `relation_id_a` in
            <foreach collection="relationIdA" item="ri" open="(" close=")" separator=",">
                #{ri}
            </foreach>
        </if>
        <if test="relationIdB != null"> and relation_id_b = #{relationIdB} </if>
        <if test="status == null">and `status` != -1</if>
        <if test="status != null">and `status` = #{status}</if>
        <if test="statuses != null">and `status` in
            <foreach collection="statuses" open="(-1," separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="maskbit != null">
            <foreach collection="maskbit" open="and (" close=")" separator=" and " item="i">
                (`status` <![CDATA[
                & #{i} ) =#{i}
            ]]>
            </foreach>
        </if>
        <if test="notMaskbit != null">
            <foreach collection="notMaskbit" open="and (" close=")" separator=" and " item="i">
                (`status` <![CDATA[
                & #{i} ) !=#{i}
            ]]>
            </foreach>
        </if>
        <if test="type != null">and `type` = #{type}</if>
        <if test="createdFrom != null">AND `created_at` <![CDATA[ >= ]]> #{createdFrom}</if>
        <if test="createdTo != null">AND `created_at` <![CDATA[ <= ]]> #{createdTo}
        </if>
    </sql>

    <sql id="criteria1">
        <if test="relationId != null">and `relation_id` = #{relationId}</if>
        <if test="type != null">and `type` = #{type}</if>
        <if test="status != null">and `status` = #{status}</if>
        <if test="notStatus != null">and `status` != #{notStatus}</if>
        <if test="relationIdA != null">
            and relation_id_a = #{relationIdA}
        </if>
    </sql>

    <insert id="create" parameterType="UserRelationEntity" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.userId}, #{i.relationId}, #{i.type}, #{i.status},
            #{i.extraStr},#{i.additionRelationA},#{i.additionRelationB}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByUserIdAndStatus" parameterType="map" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `user_id` = #{userId}
        <if test="status != null">and `status` = #{status}</if>
        <if test="notStatus != null">and `status` != #{status}</if>
    </select>

    <select id="countByUserIdAndStatus" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        WHERE `user_id`=#{userId]
        <if test="status">and `status` = #{status}</if>
        <if test="notStatus">and `status` != #{status}</if>
    </select>


    <select id="findByRelationAndTypeAndStatus" parameterType="map" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `relation_id`=#{relationId} and `type`=#{type}
        <if test="status">and `status` = #{status}</if>
        <if test="notStatus">and `status` != #{status}</if>
    </select>

    <select id="findByIds" parameterType="list" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

  <select id="findUserIdForServiceProviderByShopId" parameterType="list" resultType="long">
    SELECT user_id from <include refid="tb"/>
    <where>
      relation_id_a = #{shopId}
      and `type` = #{type}
      <if test="level"> and relation_id_b = #{level}</if>
    </where>
  </select>
    <update id="update" parameterType="UserRelationEntity">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId != null">`user_id`= #{userId},</if>
            <if test="relationId != null">`relation_id` = #{relationId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="extraStr != null">extra_json = #{extraStr},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <delete id="deleteByUserIdAndType" parameterType="map">
        DELETE FROM
        <include refid="tb"/>
        WHERE user_id = #{user_id} and `type` = #{type}
    </delete>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
    </update>


    <update id="batchUpdateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="updateType" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        type = #{type},
        updated_at= now()
        WHERE id = #{id}
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    <select id="listByUserIdAndTypeAndStatus" parameterType="map" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="userId!=null">
                user_id = #{userId}
            </if>
            <if test="type!=null">
                and `type`=#{type}
            </if>
            <if test="status!=null">
                and `status`=#{status}
            </if>
            <if test="notStatus!=null">
                and `status`!=#{notStatus}
            </if>
        </where>
    </select>

    <select id="listByRelationAndTypeAndStatus" parameterType="list" resultMap="UserRelationEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria1"/>
        </where>
    </select>
    <insert id="createWhenNotExists" parameterType="map">
        insert into
        <include refid="tb"/>
        (`user_id`,`relation_id`,`type`,`status`,`created_at`,`updated_at`
        <if test="relationIdA != null">,`relation_id_a`</if>
        <if test="relationIdB != null">,`relation_id_b`</if>)
        (select
        #{userId},#{relationId},#{type},1,now(),now()
        <if test="relationIdA != null">,#{relationIdA}</if>
        <if test="relationIdB != null">,#{relationIdB}</if>
        from
        <include refid="tb"/>
        where
        not exists(
        select id from
        <include refid="tb"/>
        where
        `user_id` = #{userId}
        AND
        `relation_id` = #{relationId}
        AND
        `type`=#{type}
        ) limit 1)
    </insert>
    <update id="updateByUserIdAndShopIdAndType" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            <if test="relationIdA != null">`relation_id_a`=#{relationIdA},</if>
            <if test="relationIdB != null">`relation_id_b`=#{relationIdB},</if>
        </set>
        <where>
            `user_id`=#{userId}
            and
            `relation_id`=#{relationId}
            and
            `type`=#{type}
        </where>
    </update>
    <delete id="deleteByUserId" parameterType="map">
        delete from <include refid="tb"/>
        <where>
            user_id = #{userId}
        </where>
    </delete>

    <update id="updateRelationId" parameterType="moonstone.user.model.UserRelationEntity">
        update parana_user_relation_entity
           set `relation_id` = #{relationId}

         where `user_id`= #{userId}
           and `type`= #{type}
           and `relation_id_a` = #{additionRelationA}
           and `status` > 0
    </update>

    <delete id="deleteByUserIdAndShopIdAndType">
        delete from
        <include refid="tb"/>

        where `user_id` = #{userId}
          and `relation_id_a`= #{relationIdA}
          and `type`= #{type}
    </delete>

    <select id="findByShopIdAndUserIdList" resultMap="UserRelationEntityMap"
            parameterType="moonstone.user.model.parameter.UserRelationEntityQueryParameter">
        select <include refid="cols_all"/>
          from <include refid="tb"/>
         where `type`= #{type}
           and (relation_id_a, user_id) in
            <foreach collection="shopIdAndUserIdList" open="(" item="item" separator="," close=")">
                (#{item.shopId}, #{item.userId})
            </foreach>
    </select>

    <select id="listByShopId" parameterType="map" resultMap="UserRelationEntityMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where relation_id_a = #{shopId}
        and status = 1;
    </select>

    <select id="findByUserIdAndShopIdAndType" parameterType="map" resultMap="UserRelationEntityMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where relation_id_a = #{shopId}
        and user_id = #{userId}
        and type = #{type}
        and status = 1;
    </select>
</mapper>
