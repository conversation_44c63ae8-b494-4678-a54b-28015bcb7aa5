<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="UserBindAllinSignature">
    <resultMap id="UserBindAllinSignatureResultMap" type="moonstone.user.entity.UserBindAllinSignature">
        <id property="id" column="id" />
        <result property="shopId" column="shop_id" />
        <result property="userId" column="user_id" />
        <result property="signatureStatus" column="signature_status" />
        <result property="signatureAccountId" column="signature_account_id" />
        <result property="signatureTempFid" column="signature_temp_fid" />
        <result property="signatureFid" column="signature_fid" />
        <result property="signatureOssUrl" column="signature_oss_url" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
    </resultMap>

    <sql id="tb">parana_user_bind_allin_signature</sql>

    <sql id="select_all">
        `id`, `shop_id`, `user_id`,`signature_status`,`signature_account_id`,`signature_temp_fid`,`signature_fid`,`signature_oss_url`,
            `created_at`, `updated_at`
    </sql>

    <sql id="insertOrUpdate">
        <trim prefix="" suffixOverrides=",">
            <if test="shopId != null">
                shop_id = #{shopId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="signatureStatus != null">
                signature_status = #{signatureStatus},
            </if>
            <if test="signatureAccountId != null">
                signature_account_id = #{signatureAccountId},
            </if>
            <if test="signatureTempFid != null">
                signature_temp_fid = #{signatureTempFid},
            </if>
            <if test="signatureFid != null">
                signature_fid = #{signatureFid},
            </if>
            <if test="signatureOssUrl != null">
                signature_oss_url = #{signatureOssUrl},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
        </trim>
    </sql>

    <select id="getSignatureByShopIdAndUserId" parameterType="map" resultMap="UserBindAllinSignatureResultMap">
        select <include refid="select_all"/> from
        <include refid="tb"/>
        where user_id = #{userId} and shop_id = #{shopId}
    </select>

    <insert id="create" parameterType="moonstone.user.entity.UserBindAllinSignature" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        set
        <include refid="insertOrUpdate"/>
    </insert>

    <insert id="update" parameterType="moonstone.user.entity.UserBindAllinSignature">
        update
        <include refid="tb"/>
        set
        <include refid="insertOrUpdate"/>
         where id = #{id}

    </insert>
</mapper>
