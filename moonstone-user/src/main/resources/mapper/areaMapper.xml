<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Area">

    <resultMap id="AreaMap" type="Area">
        <id column="Id" property="id"/>
        <result column="Type" property="type"/>
        <result column="Name" property="name"/>
        <result column="FullName" property="fullName"/>
        <result column="ParentId" property="parentId"/>
        <result column="Zip" property="zip"/>
        <result column="CreateTime" property="createTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <sql id="tb">
        parana_area
    </sql>

    <sql id="cols_all">
        Id, <include refid="cols_exclude_id" />
    </sql>

    <sql id="cols_exclude_id">
      `Type`, `Name`, `FullName`, ParentId, Zip, CreateTime, status
    </sql>

    <sql id="vals">
        #{type}, #{name}, #{fullName}, #{parentId}, #{zip}, #{createTime}, #{status}
    </sql>

    <insert id="create" parameterType="Area" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb" />
        (<include refid="cols_exclude_id" />)
        VALUES
        (<include refid="vals" />)
    </insert>

    <insert id="createWithId" parameterType="Area" keyProperty="id" useGeneratedKeys="false">
        INSERT INTO
        <include refid="tb" />
        (<include refid="cols_all" />)
        VALUES
        (#{id}, <include refid="vals" />)
    </insert>

    <select id="findById" parameterType="Long" resultMap="AreaMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE Id = #{id}
    </select>

    <select id="findByParentId" parameterType="Long" resultMap="AreaMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE ParentId = #{parentId} order by convert(FullName using gbk) asc
    </select>

    <select id="findLikeName" parameterType="String" resultMap="AreaMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE `Name` like concat(concat('%',#{name}),'%') limit 1
    </select>

    <select id="findCountryLikeName" parameterType="String" resultMap="AreaMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE `Name` like concat(concat('%',#{name}),'%') AND ParentId = 0
    </select>

    <select id="findByName" parameterType="String" resultMap="AreaMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE `Name` = #{name} limit 1
    </select>

    <select id="findByType" parameterType="Integer" resultMap="AreaMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE `Type` = #{type}
    </select>

</mapper>