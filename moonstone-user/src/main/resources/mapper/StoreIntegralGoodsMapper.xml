<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="StoreIntegralGoods">

    <resultMap id="StoreIntegralGoodsMap" type="StoreIntegralGoods">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="code" property="code"/>
        <result column="face_value" property="faceValue"/>
        <result column="third_id" property="thirdId"/>
        <result column="third_name" property="thirdName"/>
        <result column="batch" property="batch"/>
        <result column="flag" property="flag"/>
        <result column="status" property="status"/>
        <result column="num" property="num"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_store_integral_goods
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        shop_id,code, face_value, third_id,third_name, batch,flag, `status`,num, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{shopId},#{code}, #{faceValue}, #{thirdId}, #{thirdName},#{batch},#{flag},  #{status},#{num} ,#{extraStr}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="shopId != null">AND `shop_id` = #{shopId}</if>
        <if test="status != null">AND `status` = #{status}</if>
        <if test="status == null">AND `status` != -1</if>
    </sql>


    <insert id="create" parameterType="StoreIntegralGoods" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="StoreIntegralGoods">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="shopId != null">shop_id = #{shopId},</if>
        <if test="code != null">code = #{code},</if>
        <if test="faceValue != null">face_value= #{faceValue},</if>
        <if test="thirdId != null">third_id= #{thirdId},</if>
        <if test="thirdName != null">third_name= #{thirdName},</if>
        <if test="batch != null">batch= #{batch},</if>
        <if test="flag != null">flag= #{flag},</if>
        <if test="status != null">`status` = #{status},</if>
        <if test="num != null">num = #{num},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="findStoreIntegralGoodsById" parameterType="long" resultMap="StoreIntegralGoodsMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>
    <select id="findByThirdId" parameterType="long" resultMap="StoreIntegralGoodsMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE third_id = #{thirdId}  limit 1
    </select>

    <select id="findByBatch" parameterType="map" resultMap="StoreIntegralGoodsMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE batch = #{batch}  limit 1
    </select>


    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="StoreIntegralGoodsMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <update id="updateBythirdIdAndShopId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status=#{status}, updated_at = now()
        WHERE shop_id = #{shopId} and third_id = #{thirdId}
    </update>

</mapper>