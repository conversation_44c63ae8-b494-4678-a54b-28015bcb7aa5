<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Distribution">
    <resultMap id="DistributionMap" type="Distribution">
        <id column="id" property="id"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="mobile" property="mobile"/>
        <result column="company_name" property="companyName"/>
        <result column="main_categories" property="mainCategories"/>
        <result column="interested_categories" property="interestedCategories"/>
        <result column="business_license_no" property="businessLicenseNo"/>
        <result column="business_license_image_url" property="businessLicenseImageUrl"/>
        <result column="store_name" property="storeName"/>

        <result column="province" property="province"/>
        <result column="province_id" property="provinceId"/>
        <result column="city" property="city"/>
        <result column="city_id" property="cityId"/>
        <result column="region" property="region"/>
        <result column="region_id" property="regionId"/>

        <result column="store_details_address" property="storeDetailsAddress"/>
        <result column="legal_person" property="legalPerson"/>
        <result column="legal_person_id_number" property="legalPersonIdNumber"/>
        <result column="legal_person_image_url" property="legalPersonImageUrl"/>
        <result column="shop_name" property="shopName"/>
        <result column="shop_image_url" property="shopImageUrl"/>
        <result column="business_platform" property="businessPlatform"/>
        <result column="we_chat_number" property="weChatNumber"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="LongMap" type="Long"></resultMap>

    <sql id="tb">
        parana_distribution
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>


    <sql id="cols_exclude_id">
        user_id, user_name, mobile, company_name, main_categories, interested_categories, business_license_no,
        business_license_image_url, store_name, province, province_id, city, city_id, region, region_id,
        store_details_address,
        legal_person, legal_person_id_number, legal_person_image_url, shop_name, shop_image_url, business_platform,
        we_chat_number, status, created_at, updated_at
    </sql>

    <sql id="vals">
        #{userId}, #{userName}, #{mobile}, #{companyName}, #{mainCategories}, #{interestedCategories},
        #{businessLicenseNo}, #{businessLicenseImageUrl}, #{storeName}, #{province}, #{provinceId}, #{city}, #{cityId},
        #{region}, #{regionId}, #{storeDetailsAddress},
        #{legalPerson}, #{legalPersonIdNumber}, #{legalPersonImageUrl}, #{shopName}, #{shopImageUrl},
        #{businessPlatform}, #{weChatNumber}, #{status}, #{createdAt}, #{updatedAt}
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'userId'">ORDER BY user_id
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>


    <insert id="create" parameterType="Distribution" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findByUserId" parameterType="long" resultMap="DistributionMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id = #{userId}
    </select>


    <update id="update" parameterType="Distribution">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="mainCategories != null">main_categories = #{mainCategories},</if>
            <if test="interestedCategories != null">interested_categories = #{interestedCategories},</if>
            <if test="businessLicenseNo != null">business_license_no = #{businessLicenseNo},</if>
            <if test="businessLicenseImageUrl != null">business_license_image_url = #{businessLicenseImageUrl},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="province != null">province = #{province},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="city != null">city = #{city},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="region != null">region = #{region},</if>
            <if test="regionId != null">region_id = #{regionId},</if>
            <if test="storeDetailsAddress != null">store_details_address = #{storeDetailsAddress},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="legalPersonIdNumber != null">legal_person_id_number = #{legalPersonIdNumber},</if>
            <if test="legalPersonImageUrl != null">legal_person_image_url = #{legalPersonImageUrl},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="shopImageUrl != null">shop_image_url = #{shopImageUrl},</if>
            <if test="businessPlatform != null">business_platform = #{businessPlatform},</if>
            <if test="weChatNumber != null">we_chat_number = #{weChatNumber},</if>
            <if test="status != null">status = #{status},</if>
            id = id
        </set>
        WHERE id=#{id}
    </update>

</mapper>
