<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Email">

    <resultMap id="EmailMap" type="Email">
        <id column="id" property="id"/>
        <result column="email" property="email"/>
        <result column="user_name" property="userName"/>
        <result column="shop_id" property="shopId"/>
        <result column="business_type" property="businessType"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraJson"/>
        <result column="create_at" property="createAt"/>
    </resultMap>

    <sql id="tb">
        parana_emails
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        email, user_name, shop_id, business_type, status, extra_json, create_at
    </sql>

    <sql id="vals">
        #{email}, #{userName}, #{shopId}, #{businessType}, #{status}, #{extraJson}, now()
    </sql>

    <select id="findByShopIdAndType" parameterType="map" resultMap="EmailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
        and business_type = #{businessType}
        and status = #{status}
    </select>

    <update id="updateStatus" parameterType="map">
        update <include refid="tb"/>
        set status = #{status}
        where shop_id = #{shopId}
        and business_type = #{businessType}
    </update>

    <insert id="batchInsert" parameterType="list">
        insert into <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.email}, #{item.userName}, #{item.shopId}, #{item.businessType}, #{item.status} ,#{item.extraJson}, now())
        </foreach>
    </insert>



</mapper>