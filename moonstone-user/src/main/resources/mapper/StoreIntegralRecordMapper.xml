<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="StoreIntegralRecord">

    <resultMap id="StoreIntegralRecordMap" type="StoreIntegralRecord">
        <id column="id" property="id"/>
        <result column="integral_id" property="integralId"/>
        <result column="third_id" property="thirdId"/>
        <result column="face_value" property="faceValue"/>
        <result column="remain_value" property="remainValue"/>
        <result column="status" property="status"/>
        <result column="origin" property="origin"/>
        <result column="extra_json" property="extraStr"/>
        <result column="valid_at" property="validAt"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_store_integral_record
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        integral_id, face_value, remain_value, `status`,`origin`,third_id, extra_json,valid_at, created_at, updated_at
    </sql>

    <sql id="vals">
        #{integralId}, #{faceValue}, #{remainValue},#{status},#{origin}, #{thirdId},#{extraStr}, date_add(now(),
        interval 2 year),now(), now()
    </sql>

    <sql id="criteria">
        <if test="status != null">AND `status` = #{status}</if>
        <if test="status != null">AND `status` = #{status}</if>
    </sql>


    <insert id="create" parameterType="StoreIntegralRecord" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="StoreIntegralRecord">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="integralId != null">integral_id = #{integralId},</if>
        <if test="thirdId != null">third_id = #{thirdId},</if>
        <if test="faceValue != null">face_value= #{faceValue},</if>
        <if test="remainValue != null">remain_value= #{remainValue},</if>
        <if test="status != null">`status` = #{status},</if>
        <if test="origin != null">`origin` = #{origin},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="StoreIntegralRecordMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByIntegralId" parameterType="map" resultMap="StoreIntegralRecordMap">
        SELECT id ,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where integral_id=#{integralId}
        and status=#{status}
        order by created_at asc
    </select>

    <update id="updateByThirdAndIntegralId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status=#{status}, updated_at = now()
        WHERE integral_id = #{integralId} and third_id = #{thirdId}
    </update>

    <select id="countByOriginAndIntegralId" parameterType="map" resultType="int">
        select count(1) from
        <include refid="tb"/>
        <where>
            origin = #{origin}
            and integral_id = #{integralId}
        </where>
    </select>
</mapper>