<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="UserCertification">

    <resultMap id="UserCertificationMap" type="UserCertification">
        <id property="id" column="id"/>
        <result column="user_id" property="userId"/>
        <result column="paper_name" property="paperName"/>
        <result column="paper_no" property="paperNo"/>
        <result column="paper_back" property="paperBack"/>
        <result column="paper_front" property="paperFront"/>
        <result column="extra_json" property="extraJson"/>
        <result column="is_default" property="isDefault"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="verify_status" property="verifyStatus"/>
    </resultMap>

    <sql id="tb">
        parana_user_certification
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `user_id`, `paper_name`, `paper_no`, `paper_back`, `paper_front`,
        `extra_json`, `is_default`, `status`, `created_at`, `updated_at`
        ,`verify_status`
    </sql>

    <sql id="vals">
        #{userId}, #{paperName}, #{paperNo}, #{paperBack}, #{paperFront},
        #{extraJson}, #{isDefault}, #{status}, now(), now()
        ,#{verifyStatus}
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND `id` IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="userId != null">AND `user_id` = #{userId}</if>
        <if test="paperName != null">AND `paper_name` LIKE CONCAT(#{paperName} ,'%')</if>
        <if test="isDefault!=null">AND `is_default` = #{isDefault}</if>
        <if test="statuses == null and status == null">
            AND `status` != -1
        </if>
        <if test="status != null">AND `status` = #{status}</if>
        <if test="statuses != null">
            and `status` in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
        <if test="verifyStatus != null">
            AND `verify_status` = #{verifyStatus}
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY `id`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'userId'">ORDER BY `user_id`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'paperName'">ORDER BY `paper_name`
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="UserCertification" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.userId}, #{i.paperName}, #{i.paperNo}, #{i.paperBack}, #{i.paperFront},
            #{i.extraJson}, #{i.isDefault}, #{i.status}, now(), now()
            ,#{verifyStatus}
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="UserCertificationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="UserCertificationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByUserId" parameterType="long" resultMap="UserCertificationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id = #{userId} AND status != -1 limit 1
    </select>

    <select id="findDefaultByUserId" parameterType="long" resultMap="UserCertificationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id = #{userId} AND is_default = 1 AND status != -1 limit 1
    </select>

    <update id="update" parameterType="UserCertification">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="paperName != null">`paper_name` = #{paperName},</if>
            <if test="paperNo != null">paper_no = #{paperNo},</if>
            <if test="paperBack != null">paper_back = #{paperBack},</if>
            <if test="paperFront != null">`paper_front` = #{paperFront},</if>
            <if test="extraJson != null">`extra_json` = #{extraJson},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="status != null">status = #{status},</if>
            <if test="verifyStatus != null">
                verify_status = #{verifyStatus},
            </if>
            <if test="verifyStatus == null and (paperName != null or paperNo != null)">
                verify_status = 0,
            </if>
            updated_at = now()
        </set>
        WHERE id = #{id} and status != -1
    </update>

    <update id="updateByUserId" parameterType="UserCertification">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="paperName != null">`paper_name` = #{paperName},</if>
            <if test="paperNo != null">paper_no = #{paperNo},</if>
            <if test="paperBack != null">paper_back = #{paperBack},</if>
            <if test="paperFront != null">`paper_front` = #{paperFront},</if>
            <if test="extraJson != null">`extra_json` = #{extraJson},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="status != null">status = #{status},</if>
            <if test="verifyStatus != null">
                verify_status = #{verifyStatus},
            </if>
            <if test="verifyStatus == null and (paperName != null or paperNo != null)">
                verify_status = 0,
            </if>
            updated_at = now()
        </set>
        WHERE user_id = #{userId} and status != -1
    </update>

    <update id="makeDefault" parameterType="map">
        UPDATE <include refid="tb"/>
        SET is_default = TRUE, updated_at = now()
        WHERE id = #{id} AND user_id = #{userId}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status=-1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="UserCertificationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>
</mapper>
