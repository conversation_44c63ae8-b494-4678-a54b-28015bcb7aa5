<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="AreaInfoOfEntity">

    <resultMap id="AreaInfoOfEntityMap" type="AreaInfoOfEntity">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="main_id" property="mainId"/>
        <result column="province_id" property="provinceId"/>
        <result column="city_id" property="cityId"/>
        <result column="county_id" property="countyId"/>
        <result column="address" property="address"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_at" property="createdAt"/>
        <result column="status" property="status"/>
    </resultMap>

    <sql id="tb">
        p_area_info_of_entity
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `type`,main_id,province_id,city_id,county_id,address,updated_at,created_at,status
    </sql>

    <sql id="vals">
        #{type},#{mainId},#{provinceId},#{cityId},#{countyId},#{address},now(),now(),#{status}
    </sql>

    <insert id="create" parameterType="AreaInfoOfEntity" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="Long" resultMap="AreaInfoOfEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>
    <select id="findByMainIdAndType" parameterType="map" resultMap="AreaInfoOfEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE main_id = #{mainId} and `type` = #{type} and `status` != -99
    </select>
    <select id="findCurrentByMainIdAndType" parameterType="map" resultMap="AreaInfoOfEntityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE main_id = #{mainId} and `type` = #{type} and `status` != -99 order by id desc limit 1
    </select>

    <update id="update" parameterType="AreaInfoOfEntity">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="status != null">status = #{status},</if>
        <if test="provinceId != null">province_id = #{provinceId},</if>
        <if test="cityId != null">city_id = #{cityId},</if>
        <if test="countyId != null">county_id = #{countyId},</if>
        <if test="address != null">address = #{address},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>


</mapper>
