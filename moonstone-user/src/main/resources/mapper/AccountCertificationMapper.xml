<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="AccountCertification">
    <resultMap id="AccountCertificationMap" type="AccountCertification">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="shop_id" property="shopId"/>
        <result column="front_url" property="frontUrl"/>
        <result column="back_url" property="backUrl"/>
        <result column="name" property="name"/>
        <result column="mobile" property="mobile"/>
        <result column="identity_code" property="identityCode"/>
        <result column="status" property="status"/>
        <result column="auth_status" property="authStatus"/>
        <result column="reason" property="reason"/>
        <result column="auth_at" property="authAt"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="sign_img" property="signImg"/>
    </resultMap>

    <resultMap id="Long" type="long"/>
    <sql id="tb">
        p_account_certification
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        user_id, shop_id, front_url, back_url, name, mobile, identity_code, status, auth_status, reason,
        auth_at, created_at, updated_at, sign_img
    </sql>

    <sql id="vals">
        #{userId}, #{shopId}, #{frontUrl}, #{backUrl}, #{name}, #{mobile}, #{identityCode}, #{status}, #{authStatus},
        #{reason}, #{authAt}, now(), now(), #{signImg}
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'userId'">ORDER BY user_id
                <include refid="custom_sort_type"/>
            </if>
        </if>
        <if test="sortBy == null">
            ORDER BY id desc
        </if>
    </sql>

    <sql id="criteria">
        <if test="id">and `id` = #{id}</if>
        <if test="userId">and `user_id` = #{userId}</if>
        <if test="shopId">and `shop_id` = #{shopId}</if>
        <if test="mobile">and `mobile` = #{mobile}</if>
        <if test="status">and `status` = #{status}</if>
        <if test="authStatus">and `auth_status` = #{authStatus}</if>
        <if test="createdAt">and `created_at` = #{createdAt}</if>
        <if test="updatedAt">and `updated_at` = #{updatedAt}</if>
    </sql>


    <select id="findById" parameterType="long" resultMap="AccountCertificationMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>id = #{id}</where>
    </select>
    <select id="paging" parameterType="map" resultMap="AccountCertificationMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <insert id="create" parameterType="AccountCertification" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findByUserIdAndShopId" parameterType="map" resultMap="AccountCertificationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id = #{userId} AND shop_id = #{shopId}
    </select>

    <update id="update" parameterType="AccountCertification">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="frontUrl != null">front_url = #{frontUrl},</if>
            <if test="backUrl != null">back_url = #{backUrl},</if>
            <if test="name != null">name = #{name},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="identityCode != null">identity_code = #{identityCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="authAt != null">auth_at = #{authAt},</if>
            <if test="signImg != null">sign_img = #{signImg},</if>
            id = id,
            updated_at = now()
        </set>
        WHERE id=#{id}
    </update>

    <insert id="saveAndUpdateWhenDuplicate" parameterType="AccountCertification" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
        ON DUPLICATE KEY UPDATE
            <if test="frontUrl != null">front_url = #{frontUrl},</if>
            <if test="backUrl != null">back_url = #{backUrl},</if>
            <if test="name != null">name = #{name},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="identityCode != null">identity_code = #{identityCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="authAt != null">auth_at = #{authAt},</if>
            <if test="signImg != null">sign_img = #{signImg},</if>
        updated_at = now()
    </insert>
</mapper>
