<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="StoreIntegralLogSum">

    <resultMap id="StoreIntegralLogSumMap" type="StoreIntegralLogSum">

        <result column="user_id" property="userId"/>
        <result column="integralFeeAll" property="integralFeeAll"/>

    </resultMap>

    <sql id="tb">
        parana_store_integral_log
    </sql>


    <select id="findIntegralTotal" parameterType="list" resultMap="StoreIntegralLogSumMap">
        SELECT
        user_id,sum(integral_fee) as  integralFeeAll
        FROM
        <include refid="tb"/>
        WHERE
        user_id in
        <foreach collection="userIds" open="(" separator="," close=")" item="userId">
            #{userId}
        </foreach>
        group by user_id
    </select>


</mapper>