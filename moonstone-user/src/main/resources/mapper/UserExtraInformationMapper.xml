<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="UserExtraInformation">
    <resultMap id="UserExtraInformationMap" type="UserExtraInformation">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="shop_id" property="shopId"/>
        <result column="hash_data" property="hashData"/>
        <result column="img_url" property="imgUrl"/>
        <result column="related_id" property="relatedId"/>
        <result column="type" property="type"/>
        <result column="auth_at" property="authAt"/>
        <result column="operator_id" property="operatorId"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <resultMap id="LongMap" type="Long"></resultMap>

    <sql id="tb">
        parana_user_extra_informations
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        user_id,shop_id,hash_data,img_url,related_id,type,auth_at,operator_id
        ,extra_json,status,created_at,updated_at
    </sql>

    <sql id="vals">
        #{userId},#{shopId},#{hashData},#{imgUrl},#{relatedId},#{type},#{authAt},#{operatorId}
        ,#{extraStr},#{status},now(),now()
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <sql id="criteria">
        <if test="id">and `id` = #{id}</if>
        <if test="userId">and `user_id` = #{userId}</if>
        <if test="shopId">and `shop_id` = #{shopId}</if>
        <if test="hashData">and `hash_data` = #{hashData}</if>
        <if test="operatorId">and `operator_id` = #{operatorId}</if>
        <if test="status">and `status` = #{status}</if>
        <if test="status  == null">and `status` != -1</if>
        <if test="statusMasks">
            <foreach collection="statusMasks" item="bit">
                and `status` <![CDATA[
                    & #{bit} =#{bit}
                ]]>
            </foreach>
        </if>
        <if test="notStatusMasks">
            <foreach collection="notStatusMasks" item="bit">
                and `status` <![CDATA[
                    & #{bit} !=#{bit}
                ]]>
            </foreach>
        </if>
        <if test="type">and `type` = #{type}</if>
        <if test="createdFrom">AND `created_at` &gt;= #{createdFrom}</if>
        <if test="createdTo">AND `created_at` &lt; #{createdTo}</if>
    </sql>

    <insert id="create" parameterType="User" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="UserExtraInformationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByUserIdAndShopId" resultMap="UserExtraInformationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        where `user_id`=#{userId} and `shop_id`=#{shopId} and `status` != -1
    </select>

    <select id="countByUserIdAndShopId" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        where `user_id`=#{userId} and `shop_id`=#{shopId} and `status` != -1
    </select>

    <select id="findByHashDataAndShopId" parameterType="map" resultMap="UserExtraInformationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `hash_data`=#{hashData} and `shop_id`=#{shopId} and status != -1 and `type`=#{type}
    </select>

    <select id="findByRelatedId" resultMap="UserExtraInformationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `related_id` = #{relatedId} and status != -1
        <if test="shopId">
            and `shop_id`=#{shopId}
        </if>
    </select>

    <select id="findByIds" parameterType="list" resultMap="UserExtraInformationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="User">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId!= null">user_id=#{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="hashData!= null">hash_data= #{hashData},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="relatedId!= null">related_id= #{relatedId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="authAt != null">auth_at = #{authAt},</if>
            <if test="operatorId!= null">operator_id= #{operatorId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="extraStr != null">extra_json = #{extraStr},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
    </update>


    <update id="batchUpdateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateType" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        type = #{type},
        updated_at= now()
        WHERE `id`=#{id}
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="UserExtraInformationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countAll" resultType="long">
        SELECT count(1) from
        <include refid="tb"/>
    </select>
</mapper>
