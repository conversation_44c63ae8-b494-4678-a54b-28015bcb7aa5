<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--<mapper namespace="moonstone.user.impl.mapper.UserBindAllinPayMapper">-->
<mapper namespace="UserBindAllinPay">
    <resultMap id="UserBindAllinPayResultMap" type="moonstone.user.entity.UserBindAllinPay">
        <id property="id" column="id" />
        <result property="shopId" column="shop_id" />
        <result property="bizUserId" column="biz_user_id" />
        <result property="userId" column="user_id" />
        <result property="allinpayUserId" column="allinpay_user_id" />
        <result property="allinpayRoleType" column="allinpay_role_type" />
        <result property="roleType" column="role_type" />
        <result property="authUserName" column="auth_user_name" />
        <result property="authUserIdNumber" column="auth_user_id_number" />
        <result property="authUserIdType" column="auth_user_id_type" />
        <result property="bindPhone" column="bind_phone" />
        <result property="bindStep" column="bind_step" />
        <result property="stepStatus" column="step_status" />
        <result property="acct" column="acct" />
        <result property="appId" column="app_id" />
        <result property="acctType" column="acct_type" />
        <result property="bindCardNo" column="bind_card_no" />
        <result property="bindCardPhone" column="bind_card_phone" />
        <result property="bindCardData" column="bind_card_data" />
        <result property="companyInfo" column="company_info" />
        <result property="companyInfoResult" column="company_info_result" />
        <result property="companyInfoFailReason" column="company_info_fail_reason" />
        <result property="signContractStatus" column="sign_contract_status" />
        <result property="bindCardBranchName" column="bind_card_branch_name" />
        <result property="memberStatus" column="member_status" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="acctProtocolNo" column="acct_protocol_no" />
        <result property="isEnabled" column="is_enabled" />
        <result property="payAcctType" column="pay_acct_type" />
    </resultMap>

    <sql id="tb">parana_user_bind_allin_pay</sql>

    <sql id="select_all">
        `id`, `shop_id`, `user_id`,`role_type`,`biz_user_id`,`allinpay_user_id`,`allinpay_role_type`,`auth_user_name`,`auth_user_id_number`,
        `auth_user_id_type`,`bind_phone`,`bind_card_no`,`bind_card_phone`,`bind_card_data`,`company_info`,`company_info_result`,`company_info_fail_reason`,`sign_contract_status`,`bind_card_branch_name`,
            `bind_step`,`step_status`,`acct`, `app_id`, `acct_type`, `created_at`, `updated_at`, `acct_protocol_no`, `is_enabled`, `pay_acct_type`
    </sql>
    <sql id="insertOrUpdate">
        <trim prefix="" suffixOverrides=",">
            <if test="shopId != null">
                shop_id = #{shopId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="roleType != null">
                role_type = #{roleType},
            </if>
            <if test="bizUserId != null">
                biz_user_id = #{bizUserId},
            </if>
            <if test="allinpayUserId != null">
                allinpay_user_id = #{allinpayUserId},
            </if>
            <if test="allinpayRoleType != null">
                allinpay_role_type = #{allinpayRoleType},
            </if>
            <if test="authUserName != null">
                auth_user_name = #{authUserName},
            </if>
            <if test="authUserIdNumber != null">
                auth_user_id_number = #{authUserIdNumber},
            </if>

            <if test="authUserIdType != null">
                auth_user_id_type = #{authUserIdType},
            </if>
            <if test="bindPhone != null">
                bind_phone = #{bindPhone},
            </if>
            <if test="bindCardNo != null">
                bind_card_no = #{bindCardNo},
            </if>
            <if test="bindCardPhone != null">
                bind_card_phone = #{bindCardPhone},
            </if>
            <if test="bindCardData != null">
                bind_card_data = #{bindCardData},
            </if>
            <if test="companyInfo != null">
                company_info = #{companyInfo},
            </if>
            <if test="companyInfoResult != null">
                company_info_result = #{companyInfoResult},
            </if>
            <if test="companyInfoFailReason != null">
                company_info_fail_reason = #{companyInfoFailReason},
            </if>
            <if test="signContractStatus != null">
                sign_contract_status = #{signContractStatus},
            </if>
            <if test="bindCardBranchName != null">
                bind_card_branch_name = #{bindCardBranchName},
            </if>
            <if test="bindStep != null">
                bind_step = #{bindStep},
            </if>
            <if test="stepStatus != null">
                step_status = #{stepStatus},
            </if>
            <if test="acct != null">
                acct = #{acct},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="acctType != null">
                acct_type = #{acctType},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="acctProtocolNo != null">
                acct_protocol_no = #{acctProtocolNo},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled},
            </if>
            <if test="payAcctType != null">
                pay_acct_type = #{payAcctType},
            </if>
        </trim>
    </sql>

    <select id="getByShopIdAndUserId" parameterType="map" resultMap="UserBindAllinPayResultMap">
        select <include refid="select_all"/> from
        <include refid="tb"/>
        where user_id = #{userId} and shop_id = #{shopId}
    </select>
    <select id="getActiveByShopIdAndUserId" parameterType="map" resultMap="UserBindAllinPayResultMap">
        select <include refid="select_all"/> from
        <include refid="tb"/>
        where user_id = #{userId} and shop_id = #{shopId} and is_enabled = #{isEnabled}
    </select>
    <select id="getByShopIdAndUserIdAndAllinUserType" parameterType="map" resultMap="UserBindAllinPayResultMap">
        select <include refid="select_all"/> from
        <include refid="tb"/>
        where user_id = #{userId} and shop_id = #{shopId} and allinpay_role_type = #{allinpayRoleType}
    </select>

    <select id="getByBizUserId" parameterType="map" resultMap="UserBindAllinPayResultMap">
        select <include refid="select_all"/> from
        <include refid="tb"/>
        where biz_user_id = #{bizUserId}
    </select>

    <select id="getByOpenIdAndAppId" parameterType="map" resultMap="UserBindAllinPayResultMap">
        select <include refid="select_all"/> from
        <include refid="tb"/>
        where open_id = #{openId} and app_id = #{appId} and allinpay_role_type = #{allinPayRoleType}
    </select>





    <insert id="create" parameterType="moonstone.user.entity.UserBindAllinPay" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        set
        <include refid="insertOrUpdate"/>
    </insert>

    <insert id="update" parameterType="moonstone.user.entity.UserBindAllinPay">
        update
        <include refid="tb"/>
        set
        <include refid="insertOrUpdate"/>
         where id = #{id}

    </insert>
</mapper>
