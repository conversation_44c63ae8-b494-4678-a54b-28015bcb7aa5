<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="OperatorRole">

    <resultMap id="OperatorRoleMap" type="OperatorRole">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="status" property="status"/>
        <result column="allow_json" property="allowJson"/>
        <result column="extra_json" property="extraJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_operator_roles
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `name`,`desc`, status, allow_json, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{name}, #{desc}, #{status}, #{allowJson}, #{extraJson}, now(), now()
    </sql>

    <insert id="create" parameterType="OperatorRole" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="OperatorRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="OperatorRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="OperatorRole">
        update
        <include refid="tb"/>
        <set>
            <if test="name!=null">`name` = #{name},</if>
            <if test="desc!=null">`desc` = #{desc},</if>
            <if test="status!=null">`status` = #{status},</if>
            <if test="allowJson!=null">`allow_json` = #{allowJson},</if>
            <if test="extraJson!=null">`extra_json` = #{extraJson},</if>
            `updated_at` = now()
        </set>
        where `id` = #{id}
    </update>

    <delete id="delete" parameterType="long">
        delete from
        <include refid="tb"/>
        where id = #{id}
    </delete>

    <select id="findByStatus" parameterType="map" resultMap="OperatorRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `status` = #{status}
    </select>

    <sql id="criteria">
        status != -1
        <if test="status != null">AND status = #{status}</if>
    </sql>

    <select id="paging" parameterType="map" resultMap="OperatorRoleMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        order by `id` desc
        limit #{offset}, #{limit}
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>
</mapper>