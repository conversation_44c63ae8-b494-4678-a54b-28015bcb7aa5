<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="IntegralParanaDataStatus">

    <resultMap id="IntegralParanaDataStatusMap" type="IntegralParanaDataStatus">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="item_id" property="itemId"/>
        <result column="type" property="type"/>
        <result column="flag" property="flag"/>
        <result column="code" property="code"/>
        <result column="batch" property="batch"/>
        <result column="start_num" property="startNum"/>
        <result column="end_num" property="endNum"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
       parana_integral_data_status
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
         shop_id,item_id, type,flag, code,batch, start_num,end_num, `status`, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
         #{shopId},#{itemId}, #{type}, #{flag},#{code}, #{batch},#{startNum},#{endNum},  #{status}, #{extraStr}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="status != null">AND `status` = #{status}</if>
        <if test="batch != null">AND `batch` = #{batch}</if>
        <if test="shopId != null">AND `shop_id` = #{shopId}</if>
    </sql>


    <insert id="create" parameterType="IntegralParanaDataStatus" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="IntegralParanaDataStatus">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="shopId != null">shop_id = #{shopId},</if>
        <if test="itemId != null">item_id = #{itemId},</if>
        <if test="type != null">type = #{type},</if>
        <if test="flag != null">flag = #{flag},</if>
        <if test="code != null">code= #{code},</if>
        <if test="batch != null">batch= #{batch},</if>
        <if test="startNum != null">start_num= #{startNum},</if>
        <if test="endNum != null">end_num= #{endNum},</if>
        <if test="status != null and status != 1 ">`status` = #{status},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="IntegralParanaDataStatusMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findIntegralParanaDataStatusByBatch" parameterType="map" resultMap="IntegralParanaDataStatusMap">
        SELECT id ,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where shop_id=#{shopId}
        and batch=#{batch}
    </select>

    <select id="findIntegralParanaDataStatusById" parameterType="map" resultMap="IntegralParanaDataStatusMap">
        SELECT id ,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where id=#{id}

    </select>

</mapper>