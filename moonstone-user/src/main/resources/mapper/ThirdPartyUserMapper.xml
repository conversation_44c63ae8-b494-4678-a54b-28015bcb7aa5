<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- ~ Copyright (c) 2016 杭州端点网络科技有限公司 -->

<mapper namespace="ThirdPartyUser">
    <resultMap id="ThirdPartyUserMap" type="ThirdPartyUser">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="third_party_id" property="thirdPartyId"/>
        <result column="type" property="type"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">parana_third_party_users</sql>

    <sql id="cols_all">
        id,<include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        user_id,third_party_id,type,created_at,updated_at
    </sql>

    <sql id="vals">
        #{userId},#{thirdPartyId},#{type},now(),now()
    </sql>

    <insert id="create" parameterType="ThirdPartyUser" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO<include refid="tb"/>(<include refid="cols_exclude_id"/>) VALUES(<include refid="vals"/>)
    </insert>

    <delete id="delete" parameterType="long">
        delete from <include refid="tb"/>
        where id = #{id}
    </delete>

    <select id="findById" parameterType="long" resultMap="ThirdPartyUserMap">
        select id,<include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where id=#{id}
    </select>

    <select id="findByTypeAndThirdPartyId" parameterType="map" resultMap="ThirdPartyUserMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where `type`=#{type} and third_party_id = #{thirdPartyId} limit 1
    </select>


    <select id="findByTypeAndUserId" parameterType="map" resultMap="ThirdPartyUserMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where `type`=#{type} and user_id = #{userId} limit 1
    </select>

</mapper>
