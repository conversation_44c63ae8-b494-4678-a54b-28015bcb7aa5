<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="StoreIntegralCode">

    <resultMap id="StoreIntegralCodeMap" type="StoreIntegralCode">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="gen_code" property="genCode"/>
        <result column="face_value" property="faceValue"/>
        <result column="third_id" property="thirdId"/>
        <result column="code" property="code"/>
        <result column="batch" property="batch"/>
        <result column="num" property="num"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_store_integral_code
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
         shop_id,gen_code, face_value, third_id,code, batch,num, `status`, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
         #{shopId},#{genCode}, #{faceValue}, #{thirdId}, #{code},#{batch},#{num},  #{status}, #{extraStr}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="status != null">AND `status` = #{status}</if>
        <if test="status != null">AND `status` = #{status}</if>
        <if test="batch != null">AND `batch` = #{batch}</if>
        <if test="shopId != null">AND `shop_id` = #{shopId}</if>
    </sql>


    <insert id="create" parameterType="StoreIntegralCode" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="StoreIntegralCode">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="shopId != null">shop_id = #{shopId},</if>
        <if test="genCode != null">gen_code = #{genCode},</if>
        <if test="faceValue != null">face_value= #{faceValue},</if>
        <if test="thirdId != null">third_id= #{thirdId},</if>
        <if test="code != null">code= #{code},</if>
        <if test="batch != null">batch= #{batch},</if>
        <if test="num != null">num= #{num},</if>
        <if test="status != null">`status` = #{status},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="StoreIntegralCodeMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByCodeAndPwd" parameterType="map" resultMap="StoreIntegralCodeMap">
        SELECT id ,<include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where code=#{code}
        and gen_code=#{genCode}
    </select>

    <select id="findBygenCode" parameterType="map" resultMap="StoreIntegralCodeMap">
        SELECT id ,<include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where  gen_code=#{genCode}
    </select>

    <select id="findByCodeAndShopIdListOrderByNum" parameterType="map" resultMap="StoreIntegralCodeMap">
        SELECT id ,<include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where batch=#{code}
        and shop_id=#{shopId}
        order by num desc
        limit 1
    </select>

</mapper>