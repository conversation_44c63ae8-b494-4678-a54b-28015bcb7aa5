<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="StoreIntegral">

    <resultMap id="StoreIntegralMap" type="StoreIntegral">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="shop_id" property="shopId"/>
        <result column="total_grade" property="totalGrade"/>
        <result column="available_grade" property="availableGrade"/>
        <result column="use_grade" property="useGrade"/>
        <result column="frozen_grade" property="frozenGrade"/>
        <result column="trade_all" property="tradeAll"/>
        <result column="trade_num" property="tradeNum"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_store_integral
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        user_id, shop_id,total_grade, available_grade, use_grade,frozen_grade, trade_all,trade_num, `status`, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{userId}, #{shopId},#{totalGrade}, #{availableGrade}, #{useGrade}, #{frozenGrade},#{tradeAll},#{tradeNum},  #{status}, #{extraStr}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="userId != null">AND user_id = #{userId}</if>
        <if test="userIds != null">AND user_id in <foreach collection="userIds" item="i" open="(" close=")"
                                                           separator=",">
            #{i}
        </foreach>
        </if>
        <if test="shopId != null">AND shop_id = #{shopId}</if>

        <if test="status != null">AND `status` = #{status}</if>
        <if test="status == null">AND `status` != -1</if>
    </sql>


    <insert id="create" parameterType="StoreIntegral" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="StoreProxy">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="userId != null">user_id = #{userId},</if>
        <if test="shopId != null">shop_id = #{shopId},</if>
        <if test="totalGrade != null">total_grade = #{totalGrade},</if>
        <if test="availableGrade != null">available_grade= #{availableGrade},</if>
        <if test="useGrade != null">use_grade= #{useGrade},</if>
        <if test="frozenGrade != null">frozen_grade= #{frozenGrade},</if>
        <if test="tradeAll != null">trade_all= #{tradeAll},</if>
        <if test="tradeNum != null">trade_num= #{tradeNum},</if>
        <if test="status != null">`status` = #{status},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="StoreIntegralMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="StoreIntegralMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="StoreIntegralMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findAvailableGradeByUserIdAndShopId" parameterType="map" resultMap="StoreIntegralMap">
        SELECT id ,<include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        where user_id=#{userId}
        and shop_id=#{shopId}
    </select>

    <update id="updateStoreIntegralExtra" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET extra_json=#{extraJson}, updated_at = now()
        WHERE shop_id = #{shopId} and user_id = #{userId}
    </update>

    <select id="findByShopId" parameterType="long" resultMap="StoreIntegralMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
    </select>

</mapper>