package moonstone.thirdParty.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ThirdPartySkuShop implements Serializable {
    private static final long serialVersionUID = 626644504538671129L;
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 第三方平台标识（1：洋800）
     */
    private Integer thirdPartyId;

    /**
     * 商品来源 (1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 第三方sku编码
     */
    private String outerSkuId;

    /**
     * 第三方sku名称
     */
    private String outerSkuName;

    /**
     * 状态 1：启用，-1：禁用
     *
     * @see moonstone.thirdParty.enums.ThirdPartySkuStatusEnum
     */
    private Integer status;

    /**
     * 贸易类型
     *
     * @see moonstone.item.emu.SkuTypeEnum
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
