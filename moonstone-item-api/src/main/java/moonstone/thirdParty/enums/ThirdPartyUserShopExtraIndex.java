package moonstone.thirdParty.enums;

public enum ThirdPartyUserShopExtraIndex {
    SUPPRESS_ERROR_EMAIL("suppressErrorEmail", "不发送错误邮件通知开发"),
    LAST_SUCCESS_AT("lastSuccessAt", "最后一次成功的时间"),
    LAST_ERROR_AT("lastErrorAt", "最后一次失败的时间"),
    LAST_ERROR("lastError", "最后一次失败的错误信息"),
    DEFAULT_CUSTOMS("defaultCustoms", "默认关区"),
    ACCESS_CODE("accessCode", "不知道什么玩意"),
    ;

    private String code;
    private String description;

    ThirdPartyUserShopExtraIndex(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
