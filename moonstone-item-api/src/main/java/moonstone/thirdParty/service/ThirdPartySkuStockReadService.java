package moonstone.thirdParty.service;

import io.terminus.common.model.Response;
import moonstone.thirdParty.model.ThirdPartySkuStock;

import java.util.List;

public interface ThirdPartySkuStockReadService {
    Response<List<ThirdPartySkuStock>> findByThirdPartyIdAndOuterSkuId(Long shopId, Integer thirdPartyId, String outerSkuId, String depotName);

    /**
     * 无仓库名称的查询
     * @param thirdPartyId  第三方编号
     * @param outerSkuId    第三方Sku编号
     */
    Response<List<ThirdPartySkuStock>> findByThirdPartyIdAndOuterSkuId(Long shopId, Integer thirdPartyId, String outerSkuId);
}
