package moonstone.shop.dto.shoppayinfo.payConfig;

import lombok.Data;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDetailDTO;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.model.ShopPayInfo;
import org.springframework.util.CollectionUtils;

import java.io.Serial;
import java.util.HashMap;

@Data
public class AllInPayConfigDetailDTO extends ShopPayInfoDetailDTO {
    @Serial
    private static final long serialVersionUID = -571815506087821445L;

    /**
     * 通联支付分配的商户号
     */
    private String cusid;

    /**
     * 通联支付分配的appid
     */
    private String appid;

    /**
     * 签名方式：RSA 或者 SM2
     */
    private String signType;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    public static AllInPayConfigDetailDTO from(ShopPayInfo source) {
        var target = new AllInPayConfigDetailDTO();

        target.setAppid(source.getAccountNo());
        target.setCusid(source.getMchId());
        target.setPrivateKey(source.getSecretKey());
        target.setPublicKey(source.getPublicKey());

        var extra = source.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        target.setSignType(extra.get(ShopPayInfoExtraIndexEnum.SIGN_TYPE.getCode()));

        return target;
    }
}
