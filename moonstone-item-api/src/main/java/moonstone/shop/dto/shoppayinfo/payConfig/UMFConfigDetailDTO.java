package moonstone.shop.dto.shoppayinfo.payConfig;

import lombok.Data;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDetailDTO;
import moonstone.shop.model.ShopPayInfo;

import java.io.Serial;

@Data
public class UMFConfigDetailDTO extends ShopPayInfoDetailDTO {
    @Serial
    private static final long serialVersionUID = -8036294318322331936L;

    /**
     * OAuth2认证用到的 client_id
     */
    private String clientId;

    /**
     * OAuth2认证用到的 client_secret
     */
    private String clientSecret;

    public static UMFConfigDetailDTO from(ShopPayInfo source) {
        var target = new UMFConfigDetailDTO();

        target.setClientId(source.getAccountNo());
        target.setClientSecret(source.getMchId());

        return target;
    }

    /**
     * 脱敏
     *
     * @return
     */
    public UMFConfigDetailDTO encryptClientSecret() {
        if (this.clientSecret != null && this.clientSecret.length() > 3) {
            this.clientSecret = this.clientSecret.substring(0, 3) + "******" + this.clientSecret.substring(this.clientSecret.length() - 3);
        }

        return this;
    }
}
