package moonstone.shop.dto.shoppayinfo.payConfig;

import lombok.Data;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDetailDTO;
import moonstone.shop.model.ShopPayInfo;

import java.io.Serial;

@Data
public class AlipayConfigDetailDTO extends ShopPayInfoDetailDTO {
    @Serial
    private static final long serialVersionUID = 4885064903440869412L;

    /**
     * 支付宝合作者身份id(alipay-wap使用)
     */
    private String pid;

    /**
     * 支付宝账号(alipay-pc使用)
     */
    private String account;

    /**
     * 支付秘钥
     */
    private String paySecret;

    public static AlipayConfigDetailDTO from(ShopPayInfo source) {
        var target = new AlipayConfigDetailDTO();

        target.setAccount(source.getAccountNo());
        target.setPaySecret(source.getSecretKey());
        target.setPid(source.getMchId());

        return target;
    }
}
