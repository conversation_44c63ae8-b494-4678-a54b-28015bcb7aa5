package moonstone.shop.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SubStoreTStoreGuiderCriteria extends PagingCriteria {
    List<Long> ids;
    List<Long> subStoreIds;
    List<Long> storeGuiderIds;
    Long shopId;
    String storeGuiderNickname;
    String storeGuiderMobile;
    List<Integer> bitMarkList;
    List<Integer> statuses;
    Integer status;
}
