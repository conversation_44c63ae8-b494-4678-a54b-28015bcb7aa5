package moonstone.shop.service;

import io.terminus.common.model.Response;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.model.ShopPayInfo;


public interface ShopPayInfoWriteService {
    /**
     * 添加店铺支付信息
     *
     * @param shopPayInfo 支付信息
     * @return 新增记录的ID
     */
    Response<Long> create(ShopPayInfo shopPayInfo);

    /**
     * 更新店铺支付信息
     *
     * @param shopPayInfo 新的支付信息
     * @return 成功返回true
     */
    Response<Boolean> update(ShopPayInfo shopPayInfo);

    /**
     * 把当前使用中的配置变为停用
     *
     * @param shopId
     * @return
     */
    Response<Boolean> revokeActive(Long shopId, Integer usageChannel);

    /**
     * 状态变更
     *
     * @param shopPayInfoId
     * @return
     */
    Response<Boolean> updateStatus(Long shopPayInfoId, ShopPayInfoStatusEnum newStatus, ShopPayInfoStatusEnum oldStatus);
}
