package moonstone.shop.enums;

/**
 * 各种支付方式下的配置项key
 */
public enum ShopPayInfoDetailKeyEnum {
    UMF_CLIENT_ID(ShopPayInfoPayChannelEnum.UMF.getCode(), "clientId", "OAuth2认证用到的 client_id"),
    UMF_CLIENT_SECRET(ShopPayInfoPayChannelEnum.UMF.getCode(), "clientSecret", "OAuth2认证用到的 client_secret"),

    WECHATPAY_MCH_ID(ShopPayInfoPayChannelEnum.WECHATPAY.getCode(), "mchId", "微信商户号"),
    WECHATPAY_APP_ID(ShopPayInfoPayChannelEnum.WECHATPAY.getCode(), "appId", "微信小程序的 app id"),
    WECHATPAY_PAY_SECRET(ShopPayInfoPayChannelEnum.WECHATPAY.getCode(), "paySecret", "支付秘钥"),
    WECHATPAY_CERT_FILE_STATUS(ShopPayInfoPayChannelEnum.WECHATPAY.getCode(), "certFileStatus", "证书的上传状态"),

    ALIPAY_PID(ShopPayInfoPayChannelEnum.ALIPAY.getCode(), "pid", "支付宝合作者身份id(alipay-wap使用)"),
    ALIPAY_ACCOUNT(ShopPayInfoPayChannelEnum.ALIPAY.getCode(), "account", "支付宝账号(alipay-pc使用)"), // 当下应该没人用这个了
    ALIPAY_PAY_SECRET(ShopPayInfoPayChannelEnum.ALIPAY.getCode(), "paySecret", "支付秘钥"),
    ;

    private final String payChannel;
    private final String key;
    private final String description;

    ShopPayInfoDetailKeyEnum(String payChannel, String key, String description) {
        this.payChannel = payChannel;
        this.key = key;
        this.description = description;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }
}
