package moonstone.shop.model;

import lombok.Data;

import java.util.Date;

/**
 * 门店月度打卡信息表
 * author：书生
 */
@Data
public class StoreMonthlyPunch {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 门店id
     */
    private Long subStoreId;

    /**
     * 门店名称
     */
    private String subStoreName;

    /**
     * 服务商省份
     */
    private String serviceProviderProvince;

    /**
     * 服务商城市
     */
    private String serviceProviderCity;

    /**
     * 服务商名称
     */
    private String serviceProviderName;

    /**
     * 服务商手机号
     */
    private String serviceProviderMobile;

    /**
     * 服务商用户id
     */
    private Long serviceProviderUserId;

    /**
     * 门店省份
     */
    private String subStoreProvince;

    /**
     * 门店城市
     */
    private String subStoreCity;

    /**
     * 门店手机号
     */
    private String subStoreMobile;

    /**
     * 门店用户id
     */
    private Long subStoreUserId;

    /**
     * 上传者id
     */
    private Long uploaderId;

    /**
     * 门头照 url
     */
    private String doorPictureUrl;

    /**
     * 陈列照 url
     */
    private String displayPictureUrl;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 修改时间
     */
    private Date updatedAt;


}
