/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.search.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-08
 */
@Data
@NoArgsConstructor
public class AggNav implements Serializable {
    private static final long serialVersionUID = 8091726228902107984L;

    public AggNav(Object key, String name, Long count) {
        this.key = key;
        this.name = name;
        this.count = count;
    }

    /**
     * 可以是品牌id, 类目id, 以及属性key
     */
    private Object key;

    /**
     * 可以是品牌名称, 类目名称, 以及属性value
     */
    private String name;

    /**
     * 聚合的个数
     */
    private Long count;

    /**
     * 额外的字段集合，例如品牌的图片
     */
    private Map<String, String> extra;
}
