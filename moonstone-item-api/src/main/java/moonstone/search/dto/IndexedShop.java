package moonstone.search.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 店铺搜索dump到搜索引擎的 DTO
 * Author:cp
 * Created on 7/25/16.
 */
@Setter
@Getter
@ToString
public class IndexedShop implements Serializable {

    private static final long serialVersionUID = 111582188995384758L;

    /**
     * 店铺ID
     */
    private Long id;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 店铺主图
     */
    private String imageUrl;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 店铺所在省份
     */
    private String province;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 店铺所在市
     */
    private String city;

    /**
     * 区id
     */
    private Integer regionId;

    /**
     * 店铺所在区域
     */
    private String region;

    /**
     * 店铺所在街道
     */
    private String street;

    /**
     * 商品数量
     */
    private long itemCount;

}
