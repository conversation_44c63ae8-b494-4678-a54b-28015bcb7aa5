package moonstone.search.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Origin implements Serializable {

    private static final long serialVersionUID = -7365423698288976174L;
    /**
     * 来源国id
     */
    private Object originId;

    /**
     * 来源国url
     */
    private Object originUrl;

    /**
     * 来源国名称
     */
    private Object origin;
}
