/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.search.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-07
 */
@Data
public class SearchedItem implements Serializable {
    private static final long serialVersionUID = 6486052625288623389L;

    /**
     * 商品id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 商品价格, 以分为单位(可能是营销执行后的价格)
     */
    private Integer price;

    /**
     * 推荐销售价格
     */
    private Integer suggestPrice;
    /**
     * 使用的营销价格
     */
    private Integer previewPrice;

    private String promotionName;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 销量
     */
    private Integer saleQuantity;

    /**
     * 是否保税 1：保税，0：完税，2：跨境直邮（保税）
     */
    private Integer isBonded;

    /**
     * 来源地id
     */
    private Long originId;

    /**
     * 来源地
     */
    private String origin;
    /**
     * 来源地国家图片url
     */
    private String originUrl;

    /**
     * 商品类型
     * 1普通商品 2-组合商品 3-积分商品 4-新客礼品 5-累计礼品
     */
    private Integer type;

    /**
     * 营销类型
     */
    private List<Integer> promotionTypes;

    private Integer index;

    private Long dumpedAt;
}
