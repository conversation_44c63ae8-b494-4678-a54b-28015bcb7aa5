package moonstone.delivery.component;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.delivery.service.DeliveryFeeReadService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Author:cp
 * Created on 8/16/16.
 */
@Slf4j
public record DefaultDeliveryFeeTemplateChecker(DeliveryFeeReadService deliveryFeeReadService) implements DeliveryFeeTemplateChecker {

    @Override
    public boolean checkIfHasItemBindTemplate(Long deliveryFeeTemplateId) {
        Response<Boolean> checkResp = deliveryFeeReadService.checkIfHasItemBindTemplate(deliveryFeeTemplateId);
        if (!checkResp.isSuccess()) {
            log.error("fail to check if has item bind delivery fee template(id={}),cause:{}",
                    deliveryFeeTemplateId, checkResp.getError());
            throw new ServiceException(checkResp.getError());
        }
        return checkResp.getResult();
    }
}
