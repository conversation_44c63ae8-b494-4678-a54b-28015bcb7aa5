package moonstone.delivery.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品运费信息
 * Author:cp
 * Created on 06/07/16
 */
@Data
public class ItemDeliveryFee implements Serializable {

    private static final long serialVersionUID = -3424943972199627464L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 运费, 不指定运费模板时用
     */
    private Integer deliveryFee;

    /**
     * 运费模板id
     */
    private Long deliveryFeeTemplateId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
