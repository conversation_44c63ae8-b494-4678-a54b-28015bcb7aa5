package moonstone.delivery.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运费模板
 * Author:cp
 * Created on 06/07/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeliveryFeeTemplate extends DeliveryFeeBase implements Serializable {

    private static final long serialVersionUID = -6954339181162917351L;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 是否是默认模板
     */
    private Boolean isDefault;

    /**
     * 是否包邮
     */
    private Boolean isFree;

}
