/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 前后台叶子类目的绑定关系
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public class CategoryBinding implements Serializable {

    private static final long serialVersionUID = 6760480438124681550L;

    /**
     * ID
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 前台叶子类目ID
     */
    @Getter
    @Setter
    private Long frontCategoryId;

    /**
     * 后台叶子类目ID
     */
    @Getter
    @Setter
    private Long backCategoryId;


    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;
}