/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Response;
import moonstone.category.model.BackCategory;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public interface BackCategoryWriteService {

    /**
     * 创建后台类目, 只有父类目没有创建spu时, 才能创建子类目
     *
     * @param backCategory  后台类目
     * @return  新创建类目的id
     */
    Response<BackCategory> create(BackCategory backCategory);

    /**
     * 后台类目名称修改
     *
     * @param categoryId 后台类目id
     * @param name  类目名称
     * @return  是否更名成功
     */
    Response<Boolean> updateName(Long categoryId, String name);


    /**
     * 启用后台类目
     *
     * @param categoryId  后台类目id
     * @return  是否更新状态成功
     */
    Response<Boolean> enable(Long categoryId);

    /**
     * 禁用用后台类目
     *
     * @param categoryId  后台类目id
     * @return  是否更新状态成功
     */
    Response<Boolean> disable(Long categoryId);

}
