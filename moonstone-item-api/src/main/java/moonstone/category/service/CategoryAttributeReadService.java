/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Response;
import moonstone.category.model.CategoryAttribute;

import java.util.List;

/**
 * 后台类目属性读服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public interface CategoryAttributeReadService {

    /**
     * 根据后台类目id查询挂在该类目下的属性列表
     *
     * @param categoryId  后台类目id
     * @return  属性列表
     */
    Response<List<CategoryAttribute>> findByCategoryId(Long categoryId);


}
