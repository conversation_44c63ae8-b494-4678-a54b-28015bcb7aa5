/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.category.model.ShopCategoryItem;
import moonstone.common.model.Either;

import java.util.List;

/**
 * 店铺内类目和商品关联的服务, 这是作为在店铺管理用的, 目前的约定是店铺内类目只有两级
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public interface ShopCategoryItemReadService {

    /**
     * 根据shopIds与shopCatId进行查找
     *
     * @param shopIds   shopId列表
     * @param shopCatId shopCategoryId
     * @return 查询结果 ShopCategoryItem
     */
    Response<List<ShopCategoryItem>> findByShopIdsAndShopCategoryId(List<Long> shopIds, Long shopCatId);

    /**
     * 根据shopIds与shopCatId进行分页查找
     *
     * @param shopIds   shopId列表
     * @param shopCatId shopCategoryId
     * @param offset    第几页
     * @param limit     每页数量
     * @return 查询结果 ShopCategoryItem
     */
    Response<List<ShopCategoryItem>> pageByShopIdsAndShopCategoryId(List<Long> shopIds, Long shopCatId, int offset, int limit);

    /**
     * 根据店铺id和商品id查询关联的类目
     *
     * @param shopId 店铺id
     * @param itemId 商品id
     * @return 此商品关联的店铺内类目
     */
    Response<List<ShopCategoryItem>> findByShopIdAndItemId(Long shopId, Long itemId);


    /**
     * 根据店铺id批量查询关联的类目
     *
     * @param shopId 店铺id
     * @return 店铺内所有的
     */
    Either<List<ShopCategoryItem>> findByShopId(Long shopId);


    /**
     * 根据店铺id和商品id列表批量查询关联的类目
     *
     * @param shopId  店铺id
     * @param itemIds 商品id列表
     * @return 此商品关联的店铺内类目
     */
    Response<List<ShopCategoryItem>> findByShopIdAndItemIds(Long shopId, List<Long> itemIds);

    /**
     * 根据商品id列表批量查询关联的类目
     *
     * @param itemIds 商品id列表
     * @return 商品关联的店铺内类目
     */
    Response<List<ShopCategoryItem>> findByItemIds(List<Long> itemIds);

    /**
     * 根据店铺id和店铺内id查找商品
     *
     * <p>{@code shopCategoryId == 0} 时查询绑定在根节点的商品 (所有商品)
     * {@link moonstone.category.model.ShopCategory#ID_ROOT}
     *
     * <p>{@code shopCategoryId == -1} 时查询未分类商品
     * {@link moonstone.category.model.ShopCategory#ID_UNKNOWN}
     *
     * <p>{@code shopCategoryId > 0} 时查询绑定在指定店铺类目的商品
     *
     * <p>{@code shopCategoryId == null} 时查询未分类商品
     *
     * @param shopId         店铺id
     * @param shopCategoryId 店铺内类目id
     * @param pageNo         起始页码, 以1开始
     * @param pageSize       每页显示条数
     * @return 此店铺内类目关联的商品id列表
     */
    Response<Paging<Long>> findByShopIdAndCategoryId(Long shopId,
                                                     Long shopCategoryId,
                                                     Integer pageNo,
                                                     Integer pageSize);
}
