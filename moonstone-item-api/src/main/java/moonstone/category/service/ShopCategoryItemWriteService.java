/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Response;
import moonstone.category.model.ShopCategoryItem;

import java.util.List;

/**
 * 店铺内类目和商品关联的写服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public interface ShopCategoryItemWriteService {

    /**
     * 单个创建店铺类目和商品的关联关系
     * @param shopCategoryItem  店铺类目和商品的关联关系
     * @return   新关联关系的id
     */
    Response<Long> create(ShopCategoryItem shopCategoryItem);


    /**
     * 创建店铺类目和商品的关联关系
     *
     * @param shopId          店铺 ID
     * @param shopCategoryIds 店铺类目 ID 列表
     * @param itemIds         商品列表
     * @return 是否创建成功
     */
    Response<Boolean> batchCreate(Long shopId, List<Long> shopCategoryIds, List<Long> itemIds);


    /**
     * 单个解除店铺类目和商品的关联关系
     *
     * @param shopId         店铺id
     * @param shopCategoryId 店铺类目id
     * @param itemId         商品id
     * @return 是否解除成功
     */
    Response<Boolean>  delete(Long shopId, Long shopCategoryId, Long itemId);

    /**
     * 批量解除某个类目下的多个商品的关联关系
     *
     * @param shopId          店铺 ID
     * @param shopCategoryIds 店铺类目 ID 列表
     * @param itemIds         商品列表
     * @return 是否解除成功
     */
    Response<Boolean> batchDelete(Long shopId, List<Long> shopCategoryIds, List<Long> itemIds);

    /**
     * 删除商品店铺分类
     *
     * @param shopId 店铺ID
     * @param itemIds 商品id
     * @return 是否删除成功
     */
    Boolean deleteItemShopCategoryByItemId(Long shopId, List<Long> itemIds);

    Boolean saveBatch(List<ShopCategoryItem> needSaveList);
}
