/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Response;
import moonstone.category.dto.ExchangeIndexDto;
import moonstone.category.model.CategoryAttribute;

/**
 * 后台类目属性写服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-25
 */
public interface CategoryAttributeWriteService {

    /**
     * 创建后台类目属性
     *
     * @param categoryAttribute  类目属性
     * @return   如果创建成功, 则返回id
     */
    Response<Long> create(CategoryAttribute categoryAttribute);

    /**
     * 更新后台类目属性
     *
     * @param categoryAttribute  类目属性
     * @return  是否更新成功
     */
    Response<Boolean> update(CategoryAttribute categoryAttribute);

    /**
     * 删除后台类目属性
     *
     * @param id 类目属性id
     * @return  是否删除成功
     */
    Response<Boolean> delete(Long id);

    /**
     * 交换两个属性的相对位置
     *
     * @param exchangeIndexDto  属性id及位置信息
     * @return 是否交换成功
     */
    Response<Boolean> exchangeIndex(ExchangeIndexDto exchangeIndexDto);
}
