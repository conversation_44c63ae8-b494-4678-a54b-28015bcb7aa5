/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.constants.JacksonType;
import moonstone.item.dto.ImageInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 所有商品级别的属性, 通过spu发的商品, spu属性也会冗余到这里
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2015-12-11
 */
@ToString
@EqualsAndHashCode(of = {"itemId"})
public class ItemDetail implements Serializable {

    @Serial
    private static final long serialVersionUID = 1837956092030029010L;


    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 商品ID, 同时作为主键使用
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * 商品辅图, 不存数据库
     */
    @Getter
    private List<ImageInfo> images;

    /**
     * 视频地址, 每个商品只有一个视频地址
     */
    @Getter
    @Setter
    private String videoUrl;

    /**
     * 商品辅图列表的json表示形式, 存数据库
     */
    @JsonIgnore
    @Getter
    private String imagesJson;

    /**
     * 包装清单, 不存数据库
     */
    @Getter
    private Map<String, String> packing;

    @JsonIgnore
    @Getter
    private String packingJson;

    /**
     * 售后服务
     */
    @Getter
    @Setter
    private String service;

    /**
     * 富文本表现形式的商品详情
     */
    @Getter
    @Setter
    private String detail;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    private Date updatedAt;


    public void setImages(List<ImageInfo> images) {
        this.images = images;
        if (images == null) {
            this.imagesJson = null;
        } else {
            this.imagesJson = JsonMapper.JSON_NON_EMPTY_MAPPER.toJson(ImageInfo.simplifyImageUrl(images));
        }
    }

    public void setImagesJson(String imagesJson) throws Exception {
        this.imagesJson = imagesJson;
        if (Strings.isNullOrEmpty(imagesJson)) {
            this.images = Collections.emptyList();
        } else {
            List<ImageInfo> simplifyImageInfos = objectMapper.readValue(imagesJson, new TypeReference<List<ImageInfo>>() {
            });
            this.images = ImageInfo.completeImageUrl(simplifyImageInfos);
        }
    }

    public void setPacking(Map<String, String> packing) {
        this.packing = packing;
        if (packing == null) {
            this.packingJson = null;
        } else {
            try {
                this.packingJson = objectMapper.writeValueAsString(packing);
            } catch (Exception e) {
                //ignore this fucking exception
            }
        }
    }

    public void setPackingJson(String packingJson) throws Exception {
        this.packingJson = packingJson;
        if (Strings.isNullOrEmpty(packingJson)) {
            this.packing = Collections.emptyMap();
        } else {
            this.packing = objectMapper.readValue(packingJson, JacksonType.MAP_OF_STRING);
        }
    }
}
