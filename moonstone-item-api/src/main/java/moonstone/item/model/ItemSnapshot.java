/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.common.constants.JacksonType;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.item.dto.ImageInfo;
import org.springframework.util.CollectionUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品快照信息
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
@ToString(of={"itemId", "itemInfoMd5"})
@EqualsAndHashCode(of={"itemId", "itemInfoMd5"})
public class ItemSnapshot implements Serializable {
    @Serial
    private static final long serialVersionUID = 8020192989436775091L;

    private final static ObjectMapper objectMapper = JsonMapper.nonDefaultMapper().getMapper();

    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;


    /**
     * 商品id
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * 商品编码
     */
    @Getter
    @Setter
    private String itemCode;


    /**
     * 商品信息摘要
     */
    @Getter
    @Setter
    private String itemInfoMd5;


    /**
     * 店铺ID
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * 店铺名称
     */
    @Getter
    @Setter
    private String shopName;

    /**
     * 商品名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 商品辅图, 不存数据库
     */
    @Getter
    private List<ImageInfo> images;

    /**
     * 商品辅图列表的json表示形式, 存数据库
     */
    @JsonIgnore
    @Getter
    private String imagesJson;

    /**
     * 广告语
     */
    @Getter
    @Setter
    private String advertise;

    /**
     * 型号
     */
    @Getter
    @Setter
    private String specification;


    /**
     * 放商品扩展信息, 建议json字符串, 不存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;

    /**
     * 放商品扩展信息,存数据库
     */
    @Getter
    private Map<String, String> extra;


    /**
     * 商品本身的tag信息, 由运营操作, 对商家不可见, 不存数据库
     */
    @Getter
    private Map<String, String> tags;

    /**
     * 商品本身的tag信息, 由运营操作, 对商家不可见, 不存数据库
     */
    @Getter
    private String tagsJson;

    @Getter
    @Setter
    private String barCode;

    /**
     * sku属性 及属性值们, sku属性按照属性key值归组, 不存数据库
     */
    @Getter
    private List<GroupedSkuAttribute> skuAttrs;

    /**
     * 其他属性及属性值们, 其他属性则按照组名归组, 不存数据库
     */
    @Getter
    private List<GroupedOtherAttribute> otherAttrs;

    /**
     * otherAttrs的json表示形式, 存数据库
     */
    @JsonIgnore
    @Getter
    private String otherAttrsJson;


    /**
     * skuAttrs的json表示形式, 存数据库
     */
    @JsonIgnore
    @Getter
    private String skuAttrsJson;

    /**
     * 富文本表现形式的商品详情
     */
    @Getter
    @Setter
    private String detail;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;


    public void setExtraJson(String extraJson) throws Exception{
        this.extraJson = extraJson;
        if(Strings.isNullOrEmpty(extraJson)){
            this.extra= Collections.emptyMap();
        } else{
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if(extra ==null ||extra.isEmpty()){
            this.extraJson = null;
        }else{
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setTagsJson(String tagsJson) throws Exception{
        this.tagsJson = tagsJson;
        if(Strings.isNullOrEmpty(tagsJson)){
            this.tags= Collections.emptyMap();
        } else{
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if(tags ==null ||tags.isEmpty()){
            this.tagsJson = null;
        }else{
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    @JsonIgnore
    public String getMainImage() {
        return ImageUrlHandler.simplify(this.mainImage);
    }

    @JsonProperty("mainImage")
    public String getMainImage_() {
        return this.mainImage;
    }

    @JsonSetter
    public void setMainImage(String mainImage) {
        this.mainImage = ImageUrlHandler.complete(mainImage);
    }

    public void setImages(List<ImageInfo> images) {
        this.images = images;
        if(images == null){
            this.imagesJson = null;
        }else {
            this.imagesJson = JsonMapper.JSON_NON_EMPTY_MAPPER.toJson(ImageInfo.simplifyImageUrl(images));
        }
    }

    public void setImagesJson(String imagesJson) throws Exception {
        this.imagesJson = imagesJson;
        if(Strings.isNullOrEmpty(imagesJson)){
            this.images = Collections.emptyList();
        }else{
            List<ImageInfo> simplifyImageInfos = objectMapper.readValue(imagesJson, new TypeReference<List<ImageInfo>>() {
            });
            this.images = ImageInfo.completeImageUrl(simplifyImageInfos);
        }
    }

    public void setSkuAttrs(List<GroupedSkuAttribute> skuAttrs) {
        this.skuAttrs = skuAttrs;
        if (CollectionUtils.isEmpty(skuAttrs)) {
            this.skuAttrsJson = null;
        } else {
            try {
                this.skuAttrsJson = objectMapper.writeValueAsString(skuAttrs);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }


    public void setOtherAttrs(List<GroupedOtherAttribute> otherAttrs) {
        this.otherAttrs = otherAttrs;
        if (otherAttrs ==null) {
            this.otherAttrsJson = null;
        } else {
            try {
                this.otherAttrsJson = objectMapper.writeValueAsString(otherAttrs);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }

    }


    public void setSkuAttrsJson(String skuAttrsJson) throws Exception {
        this.skuAttrsJson = skuAttrsJson;

        if (Strings.isNullOrEmpty(skuAttrsJson)) {
            this.skuAttrs = Collections.emptyList();
        } else {
            this.skuAttrs = objectMapper.readValue(skuAttrsJson,
                    new TypeReference<List<GroupedSkuAttribute>>() {});
        }
    }


    public void setOtherAttrsJson(String otherAttrsJson) throws Exception {
        this.otherAttrsJson = otherAttrsJson;
        if (Strings.isNullOrEmpty(otherAttrsJson)) {
            this.otherAttrs = Collections.emptyList();
        } else {
            this.otherAttrs = objectMapper.readValue(otherAttrsJson,
                    new TypeReference<List<GroupedOtherAttribute>>() {});
        }
    }

}
