/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.constants.JacksonType;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.item.emu.ItemExtraIndex;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-14
 */
@EqualsAndHashCode(of = {"itemCode", "shopId", "specification"})
@ToString
public class Item implements Serializable {
    @Serial
    private static final long serialVersionUID = -4535812928439539071L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();


    /**
     * ID
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 商品编码
     */
    @Getter
    @Setter
    private String itemCode;

    /**
     * 后台类目 ID
     */
    @Getter
    @Setter
    private Long categoryId;


    /**
     * SPU ID , 如果是通过spu发布商品
     */
    @Getter
    @Setter
    private Long spuId;


    /**
     * 店铺ID
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * 是否保税(1:保税，0:完税)
     *
     * @see moonstone.common.enums.BondedType  保税类型变化
     */
    @Getter
    @Setter
    private Integer isBonded;

    /**
     * 是否为第三方商品（0：为自建商品，1：第三方商品）
     *
     * @see moonstone.item.emu.ThirdPartyItemType
     */
    @Getter
    @Setter
    private Integer isThirdPartyItem;


    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    @Getter
    @Setter
    private Integer sourceType;


    /**
     * 店铺名称
     */
    @Getter
    @Setter
    private String shopName;

    /**
     * 品牌ID
     */
    @Getter
    @Setter
    private Long brandId;

    /**
     * 品牌名称
     */
    @Getter
    @Setter
    private String brandName;

    /**
     * 商品名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 商品简介
     */
    @Getter
    @Setter
    private String intro;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 最低实际售价
     */
    @Getter
    @Setter
    private Integer lowPrice;

    /**
     * 最高实际售价
     */
    @Getter
    @Setter
    private Integer highPrice;

    /**
     * 库存类型, 0: 不分仓存储, 1: 分仓存储
     */
    @Getter
    @Setter
    private Integer stockType;


    /**
     * 库存
     */
    @Getter
    @Setter
    private Integer stockQuantity;

    /**
     * 销量
     */
    @Getter
    @Setter
    private Integer saleQuantity;


    /**
     * 状态  1: 上架, -1:下架, -2:冻结, -3:删除
     *
     * @see moonstone.item.emu.ItemStatusEnum
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 计划上架时间
     */
    @Getter
    @Setter
    private Date onShelfAt;

    /**
     * 广告语
     */
    @Getter
    @Setter
    private String advertise;

    /**
     * 型号
     */
    @Getter
    @Setter
    private String specification;

    /**
     * 商品类型 1, 普通商品, 2. 组合商品, 3 扫码积分商品  4 新客礼  5 累计礼
     *
     * @see moonstone.item.emu.ItemTypeEnum
     */
    @Getter
    @Setter
    private Integer type;


    /**
     * 减库存方式, 1为拍下减库存, 2为付款减库存
     */
    @Getter
    @Setter
    private Integer reduceStockType;

    /**
     * 是否参与活动 0-不参与 1-参与
     */
    @Getter
    @Setter
    private Integer tips;

    /**
     * 活动图文开关 (0-不开启 1-开启)
     */
    @Getter
    @Setter
    private Integer activityImageSwitch;


    /**
     * 放商品扩展信息, 建议json字符串, 存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;

    /**
     * 放商品扩展信息,不存数据库
     */
    @Getter
    private Map<String, String> extra;


    @Getter
    @Setter
    private String barCode;

    /**
     * 商品本身的tag信息, 由运营操作, 对商家不可见, 不存数据库
     */
    @Getter
    private Map<String, String> tags;

    /**
     * 商品本身的tag信息, 由运营操作, 对商家不可见, 存数据库
     */
    @Getter
    private String tagsJson;


    /**
     * 当前商品信息的md5值
     */
    @Getter
    @Setter
    private String itemInfoMd5;


    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    @Getter
    @Setter
    private Integer index;

    /**
     * 售罄状态
     *
     * @see moonstone.item.emu.ItemSellOutStatusEnum
     */
    @Getter
    @Setter
    private Integer sellOutStatus;

    /**
     * 区域限售模板id
     */
    @Getter
    @Setter
    private Long restrictedSalesAreaTemplateId;

    @JsonIgnore
    public String getMainImage() {
        return ImageUrlHandler.simplify(this.mainImage);
    }

    @JsonProperty("mainImage")
    public String getMainImage_() {
        return this.mainImage;
    }

    @JsonSetter
    public void setMainImage(String mainImage) {
        this.mainImage = ImageUrlHandler.complete(mainImage);
    }

    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);

            //图片url处理
            if (this.extra.containsKey(ItemExtraIndex.activityMainImage.name())) {
                this.extra.put(ItemExtraIndex.activityMainImage.name(),
                        ImageUrlHandler.complete(this.extra.get(ItemExtraIndex.activityMainImage.name())));
            }
            if (this.extra.containsKey(ItemExtraIndex.activityDetailImages.name())) {
                String[] pictureUrls = this.extra.get(ItemExtraIndex.activityDetailImages.name()).split(",");
                Arrays.stream(pictureUrls)
                        .filter(StringUtils::hasText)
                        .map(ImageUrlHandler::complete)
                        .reduce((a, b) -> a + "," + b)
                        .ifPresent(e -> this.extra.put(ItemExtraIndex.activityDetailImages.name(), e));
            }
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            try {
                //图片url处理
                String originActivityMainImage = this.extra.get(ItemExtraIndex.activityMainImage.name());
                if (this.extra.containsKey(ItemExtraIndex.activityMainImage.name())) {
                    this.extra.put(ItemExtraIndex.activityMainImage.name(),
                            ImageUrlHandler.simplify(this.extra.get(ItemExtraIndex.activityMainImage.name())));
                }

                String originActivityDetailImages = this.extra.get(ItemExtraIndex.activityDetailImages.name());
                if (this.extra.containsKey(ItemExtraIndex.activityDetailImages.name())) {
                    String[] pictureUrls = this.extra.get(ItemExtraIndex.activityDetailImages.name()).split(",");
                    Arrays.stream(pictureUrls)
                            .filter(StringUtils::hasText)
                            .map(ImageUrlHandler::simplify)
                            .reduce((a, b) -> a + "," + b)
                            .ifPresent(e -> this.extra.put(ItemExtraIndex.activityDetailImages.name(), e));
                }

                this.extraJson = objectMapper.writeValueAsString(extra);

                //map 对象中值还原
                if (this.extra.containsKey(ItemExtraIndex.activityMainImage.name())) {
                    this.extra.put(ItemExtraIndex.activityMainImage.name(), originActivityMainImage);
                }
                if (this.extra.containsKey(ItemExtraIndex.activityDetailImages.name())) {
                    this.extra.put(ItemExtraIndex.activityDetailImages.name(), originActivityDetailImages);
                }
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setTagsJson(String tagsJson) throws Exception {
        this.tagsJson = tagsJson;
        if (Strings.isNullOrEmpty(tagsJson)) {
            this.tags = Collections.emptyMap();
        } else {
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if (tags == null || tags.isEmpty()) {
            this.tagsJson = null;
        } else {
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    /**
     * extra字段中记录的活动是否当前有效
     *
     * @return
     */
    public boolean isExtraActivityValid() {
        if (CollectionUtils.isEmpty(this.getExtra())) {
            return false;
        }

        //开始时间与结束时间任一为空，则活动无效
        String itemActivityStart = this.getExtra().get(ItemExtraIndex.activityStartTime.name());
        String itemActivityEnd = this.getExtra().get(ItemExtraIndex.activityEndTime.name());
        if (!StringUtils.hasText(itemActivityStart) || !StringUtils.hasText(itemActivityEnd)) {
            return false;
        }

        //当前时间须处于开始时与结束时间的范围内，否则活动无效
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.YMDHMS_FORMAT);

            Date start = simpleDateFormat.parse(itemActivityStart);
            Date end = simpleDateFormat.parse(itemActivityEnd);
            Date current = new Date();

            return start.before(current) && current.before(end);
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 是否没有使用生效的活动数据
     *
     * @return
     */
    public boolean notUseValidActivityData() {
        if (getExtra() == null) {
            return false;
        }
        return !Boolean.TRUE.toString().equals(getExtra().get(ItemExtraIndex.isUseExtraActivity.name())) &&
                isExtraActivityValid();
    }

    /**
     * 是否使用了无效的活动数据
     *
     * @return
     */
    public boolean isUseInvalidActivityData() {
        if (getExtra() == null) {
            return false;
        }
        return Boolean.TRUE.toString().equals(getExtra().get(ItemExtraIndex.isUseExtraActivity.name())) &&
                !isExtraActivityValid();
    }
}
