package moonstone.item.model;

import lombok.Data;
import moonstone.common.model.BaseEntity;

@Data
public class RestrictedSalesAreaTemplate extends BaseEntity {

    private Long id;

    /**
     * 商家平台id
     */
    private Long shopId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板状态(1-启用，2-停用)
     *
     * @see moonstone.item.emu.RestrictedSalesAreaTemplateStatusEnum
     */
    private Integer status;

    /**
     * 是否为默认模板(1-是，2-否)
     *
     * @see moonstone.item.emu.DefaultOneEnum
     */
    private Integer defaultOne;

    /**
     * 模板说明
     */
    private String description;

    /**
     * 允许的省份(名称数组)
     */
    private String allowProvince;
}
