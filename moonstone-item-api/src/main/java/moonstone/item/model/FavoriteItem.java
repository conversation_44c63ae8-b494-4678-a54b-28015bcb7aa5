package moonstone.item.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品收藏
 * Author:cp
 * Created on 5/15/17
 */
@Data
public class FavoriteItem implements Serializable {

    private static final long serialVersionUID = -6607139482026833698L;

    private Long id;

    /**
     * 收藏的买家id
     */
    private Long buyerId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long itemId;

    private Date createdAt;

    private Date updatedAt;
}
