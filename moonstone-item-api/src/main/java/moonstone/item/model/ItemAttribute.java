/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.GroupedSkuAttribute;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 所有商品级别的属性, 通过spu发的商品, spu属性也会冗余到这里
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2015-12-11
 */
@ToString
@EqualsAndHashCode(of = {"itemId"})
public class ItemAttribute implements Serializable {

    private static final long serialVersionUID = 5192201046613468045L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 对应的商品id,当作主键用了
     */
    @Getter
    @Setter
    private Long itemId;


    /**
     * sku属性 及属性值们, sku属性按照属性key值归组, 不存数据库
     */
    @Getter
    private List<GroupedSkuAttribute> skuAttrs;

    /**
     * 其他属性及属性值们, 其他属性则按照组名归组, 不存数据库
     */
    @Getter
    private List<GroupedOtherAttribute> otherAttrs;


    /**
     * skuAttrs的json表示形式, 存数据库
     */
    @JsonIgnore
    @Getter
    private String skuAttrsJson;

    /**
     * otherAttrs的json表示形式, 存数据库
     */
    @JsonIgnore
    @Getter
    private String otherAttrsJson;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;


    public void setSkuAttrs(List<GroupedSkuAttribute> skuAttrs) {
        this.skuAttrs = skuAttrs;
        if (CollectionUtils.isEmpty(skuAttrs)) {
            this.skuAttrsJson = null;
        } else {
            try {
                this.skuAttrsJson = objectMapper.writeValueAsString(GroupedSkuAttribute.simplifyImageUrl(skuAttrs));
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }


    public void setOtherAttrs(List<GroupedOtherAttribute> otherAttrs) {
        this.otherAttrs = otherAttrs;
        if (otherAttrs ==null) {
            this.otherAttrsJson = null;
        } else {
            try {
                this.otherAttrsJson = objectMapper.writeValueAsString(otherAttrs);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }

    }


    public void setSkuAttrsJson(String skuAttrsJson) throws Exception {
        this.skuAttrsJson = skuAttrsJson;

        if (Strings.isNullOrEmpty(skuAttrsJson)) {
            this.skuAttrs = Collections.emptyList();
        } else {
            List<GroupedSkuAttribute> simplifySkuAttrs = objectMapper.readValue(skuAttrsJson,
                    new TypeReference<List<GroupedSkuAttribute>>() {
                    });
            this.skuAttrs = GroupedSkuAttribute.completeImageUrl(simplifySkuAttrs);
        }
    }


    public void setOtherAttrsJson(String otherAttrsJson) throws Exception {
        this.otherAttrsJson = otherAttrsJson;
        if (Strings.isNullOrEmpty(otherAttrsJson)) {
            this.otherAttrs = Collections.emptyList();
        } else {
            this.otherAttrs = objectMapper.readValue(otherAttrsJson,
                    new TypeReference<List<GroupedOtherAttribute>>() {});
        }
    }

}
