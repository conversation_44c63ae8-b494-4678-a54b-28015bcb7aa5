package moonstone.item.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.common.enums.ThirdIntermediateType;

/**
 * 用于缓存
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IntermediateInfoIndex {
    /**
     * 相关类型实体的Id
     */
    Long relatedId;
    /**
     * 类型 可能是Shop Item 也可能是Sku
     */
    ThirdIntermediateType type;
}
