package moonstone.item.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendItem implements Serializable {

    private static final long serialVersionUID = 9101756159736709577L;
    // 商品ID
    private Long itemId;
    // 后台类目ID
    private Long categoryId;
    // 店铺ID
    private Long shopId;
    // 商品名称
    private String name;
    // 商品主图URL
    private String mainImage;
    // 商品最低价格
    private Integer lowPrice;
}
