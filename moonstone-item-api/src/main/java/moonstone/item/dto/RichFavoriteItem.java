package moonstone.item.dto;

import lombok.Data;
import moonstone.item.model.Item;

import java.io.Serializable;
import java.util.Date;

/**
 * 收藏的商品信息
 * Created by cp on 5/15/17.
 */
@Data
public class RichFavoriteItem implements Serializable {

    private static final long serialVersionUID = -2581602269345819884L;

    /**
     * 收藏的商品
     */
    private Item item;

    /**
     * 收藏时间
     */
    private Date collectedAt;

}
