package moonstone.item.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.io.Serializable;
import java.util.List;

/**
 * Created by CaiZhy on 2018/10/26.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemCriteria extends PagingCriteria implements Serializable {
    private static final long serialVersionUID = 2164806785139211560L;

    /**
     * 商品id
     */
    private List<Long> ids;

    /**
     * 排除在外的商店id
     */
    private List<Long> exShopIds;

    /**
     * 类型id
     */
    private Long categoryId;
    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 状态  1: 上架, -1:下架, -2:冻结, -3:删除
     */
    private Integer status;

    private String sortBy;

    /// 在门店上出售
    private Boolean sellOnSubStore;

    private Integer sortType;

    private Integer type;

    /**
     * 仅用于传参，不用于限制条件
     */
    private String paranaWxaUrl;

    /**
     * @see moonstone.item.emu.ItemSellOutStatusEnum
     */
    private Integer sellOutStatus;

    /**
     * 商品类目名称
     */
    private String shopCategoryName;
}
