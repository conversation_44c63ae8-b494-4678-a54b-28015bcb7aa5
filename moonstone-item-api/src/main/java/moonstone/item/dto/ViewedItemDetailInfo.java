/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.dto;

import lombok.Data;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;

import java.io.Serializable;
import java.util.List;

/**
 * 对应商品详情页的下半部分, 包括富文本, 包装参数, 服务信息, 分组属性信息等
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-22
 */
@Data
public class ViewedItemDetailInfo  implements Serializable{

    private static final long serialVersionUID = 899873581042411121L;

    private Item item;

    /**
     * 商品详情
     */
    private ItemDetail itemDetail;

    private ProfitReVO profitReVO;
    /**
     * 分组属性参数
     */
    private List<GroupedOtherAttribute> groupedOtherAttributes;
}
