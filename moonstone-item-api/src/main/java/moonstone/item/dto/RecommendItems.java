package moonstone.item.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendItems implements Serializable {
    private static final long serialVersionUID = 7697569250192751915L;

    // 推荐商品列表
    private List<RecommendItem> recommendItems;
    /**
     * 平台标识
     * 是平台推荐：true
     * 不是平台推荐：false
     */
    private Boolean isPlatform;
    //店铺ID，平台标识为false时有值
    private Long shopId;
}
