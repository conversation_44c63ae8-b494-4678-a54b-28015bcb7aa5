package moonstone.item.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商品优惠卷vo
 */
@Data
public class ItemPromotionVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3897969797126053647L;

    /**
     * 优惠卷的id
     */
    private Long promotionId;

    /**
     * 优惠卷名称
     */
    private String name;

    /**
     * 优惠卷的类型，满减卷 / 阶梯卷
     */
    private String couponType;
    private String couponTypeDescription;

    /**
     * 卷的使用条件，即起点金额(单位：分)
     */
    private Long conditionFee;

    /**
     * 满减卷的优惠金额 / 阶梯卷的每减金额 (单位：分)
     */
    private Long reduceFee;

    /**
     * 最大优惠金额 (单位：分)
     */
    private Long maxDiscount;

    /**
     * 优惠卷的使用条件的描述
     */
    private String conditionDescription;
}
