package moonstone.item.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.category.model.ShopCategory;
import moonstone.item.model.Item;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ItemWithShopCategory extends Item {

    private static final long serialVersionUID = 0;

    /**
     * 店铺类目列表
     */
    private List<ShopCategory> shopCategories;
}
