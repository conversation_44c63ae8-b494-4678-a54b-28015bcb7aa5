/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.dto;

import lombok.*;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ItemIdWithName implements Serializable {

    private static final long serialVersionUID = -1428580613936185378L;

    /**
     * 商品id
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * 商品名称
     */
    @Getter
    @Setter
    private Long itemName;

}
