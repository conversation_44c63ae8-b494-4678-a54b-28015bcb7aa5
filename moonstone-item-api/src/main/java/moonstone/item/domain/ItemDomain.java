package moonstone.item.domain;

import lombok.AllArgsConstructor;
import moonstone.item.model.Item;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public class ItemDomain {
    Item item;

    /**
     * 最高售价
     *
     * @return 最高价格
     */
    public Optional<Long> maxPriceLimit() {
        return Optional.ofNullable(item.getExtra())
                .map(extra -> extra.get(ExtraPriceIndex.maxPrice.name()))
                .map(Long::parseLong);
    }

    /**
     * 最低售价
     *
     * @return 最低售价
     */
    public Optional<Long> miniPriceLimit() {
        return Optional.ofNullable(item.getExtra())
                .map(extra -> extra.get(ExtraPriceIndex.minPrice.name()))
                .map(Long::parseLong);
    }

    public enum ExtraPriceIndex {
        /**
         * key name
         */
        maxPrice, minPrice
    }
}
