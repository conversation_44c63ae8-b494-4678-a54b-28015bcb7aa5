/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.common;

import com.google.common.base.Charsets;
import com.google.common.base.MoreObjects;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.ItemDetail;
import moonstone.spu.model.Spu;
import moonstone.spu.model.SpuAttribute;
import moonstone.spu.model.SpuDetail;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
public final class Digestors {

    private static final HashFunction md5 = Hashing.md5();

    /**
     * 商品信息快照摘要
     *
     * @param item  商品
     * @param itemDetail  商品详情
     * @param itemAttribute   商品属性
     * @return  商品信息快照摘要
     */
    public static String itemDigest(Item item, ItemDetail itemDetail, ItemAttribute itemAttribute){
        return md5.newHasher()
                .putString(MoreObjects.firstNonNull(item.getItemCode(),""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getBarCode(),""), Charsets.UTF_8)
                .putLong(item.getShopId())
                .putString(MoreObjects.firstNonNull(item.getShopName(),""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getName(),""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getMainImage_(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(itemDetail.getImagesJson(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getAdvertise(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getSpecification(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getExtraJson(), ""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(item.getTagsJson(),""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(itemAttribute.getSkuAttrsJson(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(itemAttribute.getOtherAttrsJson(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(itemDetail.getDetail(),""),Charsets.UTF_8)
                .hash().toString();

    }


    /**
     * spu 信息摘要
     *
     * @param spu  spu
     * @param spuDetail   spu详情
     * @param spuAttribute   spu属性
     * @return spu信息快照摘要
     */
    public static String spuDigest(Spu spu, SpuDetail spuDetail, SpuAttribute spuAttribute){
        return md5.newHasher()
                .putString(MoreObjects.firstNonNull(spu.getSpuCode(),""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spu.getName(),""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spu.getMainImage(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spuDetail.getImagesJson(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spu.getAdvertise(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spu.getSpecification(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spu.getExtraJson(), ""), Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spuAttribute.getSkuAttrsJson(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spuAttribute.getOtherAttrsJson(),""),Charsets.UTF_8)
                .putString(MoreObjects.firstNonNull(spuDetail.getDetail(),""),Charsets.UTF_8)
                .hash().toString();
    }
}
