/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.item.dto.paging.SkuCriteria;
import moonstone.item.model.Sku;

import java.util.List;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2015-12-14
 */
public interface SkuReadService {

    /**
     * 根据skuId查找sku
     *
     * @param id sku id
     * @return SKU信息
     */
    Response<Sku> findSkuById(Long id);


    /**
     * 根据sku id 列表获取sku列表
     *
     * @param skuIds skuid列表
     * @return sku列表
     */
    Response<List<Sku>> findSkusByIds(List<Long> skuIds);

    /**
     * 根据商品名称和店铺id模糊查找skuId
     * @param name
     * @param shopId
     * @return
     */
    Response<List<Long>> findIdsByNameAndShopId(String name, Long shopId);

    /**
     * 根据外部数据信息查询sku数据
     *
     * @param shopId  店铺id
     * @param skuCode 外部sku编号
     * @return List
     * 返回sku列表
     */
    Response<List<Sku>> findSkuByCode(Long shopId, String skuCode);

    /**
     * 根据第三方skuId查询sku数据
     * @param shopId
     * @param outerSkuId
     * @return
     */
    Response<List<Sku>> findSkuByOuterSkuId(Long shopId, String outerSkuId);

    /**
     * 根据商品id找sku
     *
     * @param itemId 商品id, 对于分销系统也有可能是spuId , 这里统称为商品id
     * @return 商品的SKU
     */
    Response<List<Sku>> findSkusByItemId(Long itemId);

    /**
     * 根据商品id列表查询sku
     *
     * @param itemIds 商品id列表
     * @return 商品的sku
     */
    Response<List<Sku>> findSkusByItemIds(List<Long> itemIds);

    Response<Long> countByItemId(Long itemId);

    /**
     * 商家分页查询sku列表
     * @param shopId 商家id
     * @param skuId sku id，可空
     * @param skuCode sku编码，可空
     * @param itemId 商品id，可空
     * @param skuName sku名称，可空
     * @param statuses 多个状态，逗号分隔，可空
     * @param status 状态，可空
     * @param pageNo 页面
     * @param pageSize 每页大小
     * @return sku分页列表
     */
    Response<Paging<Sku>> findBy(Long shopId,
                                 Long skuId,
                                 String skuCode,
                                 Long itemId,
                                 String skuName,
                                 String statuses,
                                 Integer status,
                                 Integer pageNo,
                                 Integer pageSize);

    /**
     * 商家分页查询微分销sku列表
     * @param shopId 商家id
     * @param itemId 商品id，可空
     * @param skuName sku名称，可空
     * @param pageNo 页面
     * @param pageSize 每页大小
     * @return sku分页列表
     */
    Response<Paging<Sku>> pagingSellInWeShop(Long shopId,
                                             Long itemId,
                                             String skuName,
                                             Integer pageNo,
                                             Integer pageSize);


    Response<List<Sku>> findByShopIdAndProfitSet(long shopId, boolean profitSet);

    Response<Paging<Sku>> paing(SkuCriteria criteria);



    List<Sku> selectList(Map<String, Object> query);
}
