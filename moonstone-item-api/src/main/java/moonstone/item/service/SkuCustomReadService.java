package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;

import java.util.List;

public interface SkuCustomReadService {
    List<SkuWithCustom> makeSkuWithCustomsBySkus(List<Sku> skus);

    void fillSkuWithCustoms(List<SkuWithCustom> skuWithCustoms);

    SkuCustom findBySkuId(Long skuId);

    Response<List<SkuCustom>> findBySkuIds(List<Long> skuIds);

    SkuCustom getBySkuId(Long skuId);
}
