/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.item.dto.FullItem;
import moonstone.item.model.Item;
import moonstone.shop.model.Shop;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
public interface ItemWriteService {

    /**
     * 创建商品
     *
     * @param fullItem 待创建的商品, 这里已经过各种校验,且商品信息已经完善
     * @return 新创建的商品id
     */
    Response<Long> create(FullItem fullItem);

    Response<Long> copy(Item item, Shop shop);

    Response<Long> create(Item item);

    /**
     * 更新商品
     *
     * @param editedItem 待更新的商品, 这里已经过各种校验,且商品信息已经完善
     * @return 是否更新成功
     */
    Response<Boolean> update(FullItem editedItem);

    Response<Boolean> update(Item item);


    /**
     * 删除商品, 先都逻辑删除吧
     *
     * @param shopId 商品id
     * @param itemId 待删除的商品id
     * @return 是否删除成功
     */
    Response<Boolean> delete(Long shopId, Long itemId);

    /**
     * 商家更新店铺商品状态, 防止商家更新别家的商品
     *
     * @param shopId 店铺id
     * @param itemId 商品id
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     * @return 是否更新成功
     */
    Response<Boolean> updateStatusByShopIdAndItemId(Long shopId, Long itemId, Integer status);

    /**
     * 根据id更新商品状态
     *
     * @param itemId 商品id
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     * @return 是否更新成功
     */
    Response<Boolean> updateStatusByItemId(Long itemId, Integer status);

    /**
     * 批量更新商品状态, 这是给商家用的
     *
     * @param shopId 店铺id
     * @param ids    商品id列表
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    Response<Boolean> batchUpdateStatusByShopIdAndItemIds(Long shopId, List<Long> ids, Integer status);


    /**
     * 编辑商品详情富文本
     *
     * @param itemId   商品id
     * @param richText 详情富文本
     * @return 是否编辑成功
     */
    Response<Boolean> editRichText(Long itemId, String richText);

    /**
     * 更新商品信息摘要
     *
     * @param itemId 商品id
     * @param digest 快照的摘要
     * @return 是否更新成功
     */
    Response<Boolean> updateDigest(Long itemId, String digest);

    /**
     * 更新商品(不会更新商品摘要)
     *
     * @param item 待更新的商品
     * @return 是否更新成功
     */
    Response<Boolean> updateItem(Item item);

    /**
     * 设置商品的销量
     *
     * @param itemId       商品Id
     * @param saleQuantity 销量
     * @return 是否执行成功
     */
    Either<Boolean> setSaleQuantity(Long itemId, Integer saleQuantity);

    Response<Boolean> batchUpdateSellOutStatusByIds(List<Item> updateList);

    Response<Boolean> updateRestrictedSalesAreaTemplate(Long shopId, List<Long> itemIds, Long templateId);

    Response<Boolean> initOldItemDefaultTemplateId(Long shopId, Long restrictedSalesAreaTemplateId);

    Boolean batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 批量更新商品售罄状态
     * @param ids 商品id列表
     * @param sellOutStatus 售罄状态
     * @return 是否修改成功
     */
    Boolean batchUpdateSellOutStatusByIds(List<Long> ids, Integer sellOutStatus);
}
