package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.common.enums.IntermediateInfoMatchingTypeEnum;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.item.model.IntermediateInfo;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/22 18:19
 */
public interface IntermediateInfoWriteService {
    Response<Boolean> create(IntermediateInfo intermediateInfo);

    Response<Boolean> update(IntermediateInfo intermediateInfo);

    /**
     * 删除
     *
     * @param thirdId
     * @param type
     * @param matchingTypeEnum
     * @return
     */
    Response<Boolean> delete(Long thirdId, ThirdIntermediateType type, IntermediateInfoMatchingTypeEnum matchingTypeEnum);
}
