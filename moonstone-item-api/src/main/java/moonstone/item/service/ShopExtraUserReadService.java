package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.shop.model.ShopExtraUser;

import java.util.List;

public interface ShopExtraUserReadService {
    Response<List<ShopExtraUser>> findByShopId(long shopId);
    Response<List<ShopExtraUser>> findNormalByShopId(long shopId);
    Response<List<ShopExtraUser>> findByApiId(long apiId);
    Response<List<ShopExtraUser>> findNormalByApiId(long apiId);
}
