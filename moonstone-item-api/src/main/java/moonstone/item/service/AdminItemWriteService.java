/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Response;

import java.util.List;
import java.util.Map;

/**
 * 运营管理商品写服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
public interface AdminItemWriteService {

    /**
     * 批量更新一个店铺的所有状态, 这是给运营使用的
     *
     * @param shopId   店铺id
     * @param status   商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    Response<Boolean> batchUpdateStatusByShopId(Long shopId, Integer status);

    /**
     * 批量更新商品状态, 这是给运营使用的
     *
     * @param ids  商品id列表
     * @param status  商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    Response<Boolean> batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 更新单个商品状态, 这是给运营使用的
     *
     * @param id 商品id
     * @param status  商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    Response<Boolean> updateStatus(Long id, Integer status);


    /**
     * 运营标记商品
     *
     * @param itemId  商品id
     * @param tags  商品标签
     * @return  是否标记成功
     */
    Response<Boolean> tags(Long itemId, Map<String,String> tags);
}
