package moonstone.item.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.item.model.RestrictedSalesAreaTemplate;

import java.util.List;
import java.util.Map;

public interface RestrictedSalesAreaTemplateReadService {

    Response<Paging<RestrictedSalesAreaTemplate>> find(Long shopId, String name, Integer status, int pageNo, int pageSize);

    Response<RestrictedSalesAreaTemplate> findById(Long id);

    Response<List<RestrictedSalesAreaTemplate>> findByIds(List<Long> idList);

    /**
     *
     * @param idList
     * @return key = RestrictedSalesAreaTemplate.id
     */
    Map<Long, RestrictedSalesAreaTemplate> findMapByIds(List<Long> idList);

    Response<List<RestrictedSalesAreaTemplate>> findByName(Long shopId, String name);

    Response<RestrictedSalesAreaTemplate> findDefault(Long shopId);

    /**
     * 获取区域限售模板下拉框列表
     * @return 区域限售模板下拉框列表
     */
    List<ComboBoxVo> comboBoxList(Long shopId);


}
