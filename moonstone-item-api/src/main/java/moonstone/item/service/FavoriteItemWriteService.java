package moonstone.item.service;

import io.terminus.common.model.Response;

/**
 * 商品收藏写服务
 * Created by cp on 5/15/17.
 */
public interface FavoriteItemWriteService {

    /**
     * 收藏商品
     *
     * @param buyerId 买家id
     * @param itemId  要收藏的商品id
     * @return 是否收藏成功
     */
    Response<Boolean> collect(Long buyerId, Long itemId);

    /**
     * 删除收藏的商品
     *
     * @param buyerId 买家id
     * @param itemId  要删除的商品id
     * @return 是否删除成功
     */
    Response<Boolean> delete(Long buyerId, Long itemId);

}
