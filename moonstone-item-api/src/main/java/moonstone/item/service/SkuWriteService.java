/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.item.model.Sku;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-20
 */
public interface SkuWriteService {

    /**
     * 更新对应的sku信息, 仅限库存, 价格方面的信息
     *
     * @param sku sku信息
     * @return 是否更新成功
     */
    Response<Boolean> updateSku(Sku sku);

    Response<Boolean> create(Sku sku);
}
