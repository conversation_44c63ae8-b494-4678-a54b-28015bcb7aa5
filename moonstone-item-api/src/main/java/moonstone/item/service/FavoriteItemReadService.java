package moonstone.item.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.item.dto.RichFavoriteItem;


/**
 * 商品收藏读服务
 * Created by cp on 5/15/17.
 */
public interface FavoriteItemReadService {

    /**
     * 买家查询收藏的商品列表
     *
     * @param buyerId  买家id,非空
     * @param shopId   店铺id，可空
     * @param pageNo   页码，可空
     * @param pageSize 每页大小，可空
     * @return 收藏的商品信息
     */
    Response<Paging<RichFavoriteItem>> findForBuyer(Long buyerId, Long shopId,
                                                    Integer pageNo, Integer pageSize);

    /**
     * 检查买家是否收藏了该商品
     *
     * @param buyerId 买家id
     * @param itemId  商品id
     * @return 已收藏返回true, 否则返回false
     */
    Response<Boolean> checkIfCollect(Long buyerId, Long itemId);
}
