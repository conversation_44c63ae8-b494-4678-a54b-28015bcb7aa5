package moonstone.item.service;

import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.Either;
import moonstone.item.model.IntermediateInfo;

import java.util.List;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/22 18:18
 */
public interface IntermediateInfoReadService {
    /**
     * 默认不包含活动配置
     *
     * @param thirdId
     * @param type
     * @return
     */
    Either<List<IntermediateInfo>> findByThirdAndType(Long thirdId, Integer type);

    Either<List<IntermediateInfo>> findByThirdAndType(Long thirdId, Integer type, Integer matchingType);

    /**
     * 包含活动配置
     *
     * @param thirdId
     * @param type
     * @return
     */
    Either<List<IntermediateInfo>> findAllByThirdAndType(Long thirdId, Integer type);

    Either<Boolean> exists(Long thirdId, int type);

    /**
     * 如果存在符合条件的活动配置，则取活动配置，否则取一般的通常配置
     *
     * @param thirdId
     * @param type
     * @return
     */
    Either<IntermediateInfo> findWithActivityByThirdAndType(Long thirdId, ThirdIntermediateType type);

    Either<List<IntermediateInfo>> findByThirdIdsAndType(List<Long> thirdIdList, ThirdIntermediateType type);
}
