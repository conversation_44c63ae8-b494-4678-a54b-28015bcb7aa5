/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.item.model.ItemSnapshot;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
public interface ItemSnapshotReadService {

    /**
     * 根据快照id查找对应的快照信息
     *
     * @param id  快照id
     * @return   快照信息
     */
    Response<ItemSnapshot> findById(Long id);


    /**
     * 根据商品id及商品信息摘要查找对应的快照信息
     *
     * @param itemId  商品id
     * @param itemInfoMd5  商品信息摘要
     * @return   对应的快照信息, 注意: 如果没有找到, 返回null
     */
    Response<ItemSnapshot> findByItemIdAndItemInfoMd5(Long itemId, String itemInfoMd5);
}
