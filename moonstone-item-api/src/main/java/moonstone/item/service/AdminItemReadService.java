/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.item.model.Item;

import java.util.List;

/**
 * 运营后台商品读服务
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-16
 */
public interface AdminItemReadService {


    /**
     * 根据店铺id分页查找商品, 这是给运营后台用的, 商家前台请使用findBy
     *
     * @param shopId   店铺id
     * @param pageNo   起始页码, 从1开始
     * @param pageSize 每页显示数目
     * @return 分页结果
     */
    Response<Paging<Item>> findByShopId(Long shopId, Integer pageNo,
                                        Integer pageSize);


    /**
     * 根据商家id分页查找商品, 这是给运营后台用的, 商家前台请使用findBy
     *
     * @param userId   商家id
     * @param pageNo   起始页码, 从1开始
     * @param pageSize 每页显示数目
     * @return 分页结果
     */
    Response<Paging<Item>> findByUserId(Long userId, Integer pageNo,
                                        Integer pageSize);


    /**
     * 支持运营后台商品管理查询的各种请求
     * <p/>
     * 逻辑如下:
     * <p/>
     * 1. 如果指定了itemId, 则其他条件忽略
     * 2. 如果指定了userId或者shopId, 则只考虑itemName
     *
     * @param itemId   商品id
     * @param userId   用户id
     * @param shopId   店铺id
     * @param itemName 商品名称
     * @param status   状态
     * @param type     类型
     * @param pageNo   起始页码, 从1开始
     * @param pageSize 每页显示数目
     * @return 分页结果
     */
    Response<Paging<Item>> findBy(Long itemId, Long userId, Long shopId, List<Long> exShopIds,
                                  String itemName, Integer status, Integer type,
                                  Integer pageNo, Integer pageSize);


}
