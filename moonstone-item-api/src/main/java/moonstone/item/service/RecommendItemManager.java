package moonstone.item.service;

import moonstone.common.model.Either;
import moonstone.item.dto.RecommendItems;

/**
 * 推荐商品Manager
 * 用户推荐商品列表的配置和查询
 */
public interface RecommendItemManager {

    /**
     * 小二或商家 配置推荐商品
     * 存入MongoDB
     *
     * @param items 推荐商品列表
     * @return
     */
    Either<Boolean> createRecommendItems(RecommendItems items);

    /**
     * 小二或商家 删除推荐商品配置
     *
     * @param isPlatform 是否平台配置
     * @param shopId     店铺ID，当isPlatform为true时传入null
     * @return
     */
    Either<Boolean> removeRecommendItems(Boolean isPlatform, Long shopId);

    /**
     * 查询配置的推荐商品列表
     *
     * @param isPlatform 是否平台配置
     * @param shopId     店铺ID，当isPlatform为true时传入null
     * @return
     */
    Either<RecommendItems> queryRecommendItems(Boolean isPlatform, Long shopId);

}
