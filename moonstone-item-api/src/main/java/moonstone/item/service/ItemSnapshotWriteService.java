/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.ItemDetail;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
public interface ItemSnapshotWriteService {

    /**
     * 为商品创建快照
     *
     * @param item 商品
     * @param itemDetail  商品详情
     * @param itemAttribute   商品属性
     * @return   快照id
     */
    Response<Long> create(Item item, ItemDetail itemDetail, ItemAttribute itemAttribute);
}
