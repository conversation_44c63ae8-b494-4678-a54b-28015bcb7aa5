package moonstone.item.service;

import io.terminus.common.model.Response;
import moonstone.item.model.RestrictedSalesAreaTemplate;

import java.util.List;

public interface RestrictedSalesAreaTemplateWriteService {
    Response<Boolean> create(RestrictedSalesAreaTemplate parameter);

    Response<Boolean> update(RestrictedSalesAreaTemplate parameter);

    Response<Boolean> creates(List<RestrictedSalesAreaTemplate> list);
}
