package moonstone.item.service;

import io.terminus.common.model.Paging;
import moonstone.common.model.Either;
import moonstone.item.dto.paging.ItemWithTagsCriteria;
import moonstone.item.dto.paging.TagCriteria;
import moonstone.item.dto.paging.UserWithTagsCriteria;
import moonstone.item.model.ItemWithTags;
import moonstone.item.model.Tag;
import moonstone.item.model.UserWithTags;

import javax.annotation.Nullable;
import java.util.List;

public interface ItemTagManager {
    /**
     * 创建商品Tag 只包含名字
     */
    Either<Tag> createTag(Tag tag);

    /**
     * 更新Tag信息,修改名字,同时返回连带修改的数量
     */
    Either<Integer> updateTag(Tag tag);

    /**
     * 删除Tag
     *
     * @return 连带删除的数量
     */
    Either<List<ItemWithTags>> deleteTag(Tag tag);

    /**
     * 给某个标签绑定上一个用户
     *
     * @param userId 用户Id
     * @param tagId  标签Id
     */
    Either<Boolean> boundUser(Long userId, String tagId);

    /**
     * 给某个标签绑定上一个商品
     *
     * @param itemId 商品Id
     * @param tagId  标签Id
     * @param shopId 店铺Id
     */
    Either<String> boundItem(Long itemId, String tagId, Long shopId);

    /**
     * 查找标签
     *
     * @param tagCriteria 标签查找条件
     * @return 返回的标签列表
     */
    Either<Paging<Tag>> queryTag(TagCriteria tagCriteria);

    /**
     * 寻找一个商品与标签关联的东西
     *
     * @param itemId 商品Id
     */
    Either<List<ItemWithTags>> findItemWithTagsByItemId(Long itemId);

    /**
     * 查找商品Tag
     *
     * @param itemWithTagsCriteria 商品Criteria
     */
    Either<Paging<ItemWithTags>> queryItemWithTag(ItemWithTagsCriteria itemWithTagsCriteria);

    /**
     * 查找用户Tag
     *
     * @param userWithTagsCriteria 用户Criteria
     */
    Either<Paging<UserWithTags>> queryUserWithTag(UserWithTagsCriteria userWithTagsCriteria);

    /**
     * 查找标签id list
     *
     * @param userId 用户Id
     * @param shopId 店铺Id
     */
    Either<List<String>> findTagIdsByUserId(Long userId, @Nullable Long shopId);

    /**
     * 修改商品与Tag绑定
     *
     * @param itemId   商品Id
     * @param tagId    原来的TagId
     * @param newTagId 新的TagId,如果为null 则只删除tag绑定关系
     */
    Either<String> modifyItemWithTags(Long itemId, String tagId, @Nullable String newTagId);

    /**
     * 修改用户Tag绑定
     *
     * @param userId   用户Id
     * @param tagId    原来的TagId
     * @param newTagId 新的TagId
     */
    Either<String> modifyUserWithTags(Long userId, String tagId, @Nullable String newTagId);

    /**
     * 根据Tag Id获取Tag
     */
    Either<Tag> getTag(String id);

    /**
     * 获取这个店铺所有的TagId
     */
    Either<List<Tag>> findTagIdsByShopId(Long shopId);
}