package moonstone.item.emu;

import moonstone.common.model.rpcAPI.enums.Y800StorageSkuTradeTypeEnum;

public enum SkuTypeEnum {
    DUTY_PAID(0, "完税"),
    BONDED(1, "保税"),
    ;

    private final Integer code;
    private final String description;

    SkuTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static SkuTypeEnum from(Y800StorageSkuTradeTypeEnum source) {
        if (source == null) {
            return DUTY_PAID;
        }

        if (Y800StorageSkuTradeTypeEnum.BONDED.equals(source) || Y800StorageSkuTradeTypeEnum.CROSS_BORDER_DIRECT.equals(source)) {
            return SkuTypeEnum.BONDED;
        } else {
            return SkuTypeEnum.DUTY_PAID;
        }
    }
}
