package moonstone.item.emu;

public enum DefaultOneEnum {
    YES(1, "默认"),
    NO(2, "非默认"),
    ;

    private final Integer code;
    private final String description;

    DefaultOneEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
