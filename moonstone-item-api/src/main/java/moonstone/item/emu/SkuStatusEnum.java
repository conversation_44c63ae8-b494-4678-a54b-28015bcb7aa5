package moonstone.item.emu;

public enum SkuStatusEnum {
    ON_SHELF(1, "上架"),
    OFF_SHELF(-1, "下架"),
    FROZEN(-2, "冻结"),
    DELETED(-3, "已删除");

    private Integer code;
    private String description;

    SkuStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
