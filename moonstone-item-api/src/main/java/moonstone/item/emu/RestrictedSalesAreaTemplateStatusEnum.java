package moonstone.item.emu;

public enum RestrictedSalesAreaTemplateStatusEnum {
    ACTIVE(1, "启用"),
    INACTIVE(2, "停用"),
    ;

    private final Integer code;
    private final String description;

    RestrictedSalesAreaTemplateStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RestrictedSalesAreaTemplateStatusEnum parse(Integer code) {
        for (var current : values()) {
            if (current.getCode().equals(code)) {
                return current;
            }
        }

        return null;
    }
}
