package moonstone.item.emu;

import java.util.Objects;

public enum ItemStatusEnum {
    ON_SHELF(1, "上架"),
    OFF_SHELF(-1, "下架"),
    FROZEN(-2, "冻结"), //这个真的有在用？
    DELETED(-3, "已删除");

    private Integer code;
    private String description;

    ItemStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String convertDesc(Integer status) {
        for (ItemStatusEnum value : ItemStatusEnum.values()) {
            if (Objects.equals(value.code, status)) {
                return value.description;
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
