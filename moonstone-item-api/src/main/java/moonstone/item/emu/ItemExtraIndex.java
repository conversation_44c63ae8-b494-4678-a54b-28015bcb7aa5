package moonstone.item.emu;

/**
 * <AUTHOR>
 */
public enum ItemExtraIndex {
    /**
     * 商品额外字段
     */
    unit("商品数量单位"),
    source("商品来源"),

    skuOrderSplitLine("sku级别订单拆单数量,超过这个数量便开始拆单"),

    activityMainImage("活动生效期间的商品目录图"),
    activityDetailImages("活动生效期间的商品明细图列表"),
    activityStartTime("活动生效的开始时间"),
    activityEndTime("活动生效的结束时间"),
    isUseExtraActivity("商品视图是否使用了上面的活动配置"),

    @Deprecated
    sellOutStatus("售罄状态"),

    migrateSourceItemId("当商品是由老商家迁移过来的时候，此为源parana_item的id"),
    ;

    ItemExtraIndex(String desc) {
        this.desc = desc;
    }

    String desc;
}
