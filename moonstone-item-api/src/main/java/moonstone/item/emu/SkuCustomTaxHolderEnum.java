package moonstone.item.emu;

import org.apache.commons.lang3.StringUtils;

/**
 * 税金承担方
 */
public enum SkuCustomTaxHolderEnum {
    BUYER(1, "买家承担"),
    SELLER(2, "卖家承担"),
    ;

    private final Integer code;
    private final String description;

    SkuCustomTaxHolderEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(Integer code) {
        for (var current : values()) {
            if (current.getCode().equals(code)) {
                return current.getDescription();
            }
        }

        return StringUtils.EMPTY;
    }
}
