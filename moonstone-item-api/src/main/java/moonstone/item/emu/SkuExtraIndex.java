package moonstone.item.emu;

/**
 * <AUTHOR>
 */
public enum SkuExtraIndex {
    /**
     * 单品额外数据
     */
    orderQuantityLimit("orderQuantityLimit", "订单购买上限"),
    unitQuantity("unitQuantity", "组合装数量, 一件对应外部订单多件"),
    skuOrderSplitLine("skuOrderSplitLine", "sku级别订单拆单数量,超过这个数量便开始拆单"),
    SubProfitFee("SPF", "门店分销利润"),
    SubProfitRate("SPR", "门店分销利润比"),
    GuiderProfitRate("GPR", "导购员分销利润比"),
    GuiderProfitFee("GPF", "导购员分销利润"),
    NeedAuth("needAuth", "需要审核"),
    origin("originPrice", "原始价格"),
    tax("tax", "税率"),
    suggest("suggest", "推荐价格"),

    activitySalesPrice("activitySalesPrice", "活动销售价"),
    activityStartTime("activityStartTime", "活动生效的开始时间"),
    activityEndTime("activityEndTime", "活动生效的结束时间"),
    isUseExtraActivity("isUseExtraActivity","商品视图是否使用了上面的活动配置"),

    migrateSourceSkuId("migrateSourceSkuId", "当商品是由老商家迁移过来的时候，此为源parana_skus的id"),
    ;

    String code;
    String desc;

    SkuExtraIndex(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return null == code ? name() : code;
    }
}
