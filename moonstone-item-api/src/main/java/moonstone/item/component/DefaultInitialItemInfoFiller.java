package moonstone.item.component;

import com.google.common.base.MoreObjects;
import moonstone.item.api.InitialItemInfoFiller;
import moonstone.item.model.Item;

/**
 * Author:cp
 * Created on 8/8/16.
 */
public record DefaultInitialItemInfoFiller() implements InitialItemInfoFiller {

    @Override
    public void fill(Item item) {
        item.setStatus(-1); //默认下架
        item.setSaleQuantity(MoreObjects.firstNonNull(item.getSaleQuantity(), 0));  //默认销量为0
        item.setType(MoreObjects.firstNonNull(item.getType(), 1));   //默认为普通商品
        item.setReduceStockType(MoreObjects.firstNonNull(item.getReduceStockType(), 1)); //默认拍下减库存
        item.setStockType(MoreObjects.firstNonNull(item.getStockType(), 0));//默认不分仓存储
        item.setSourceType(MoreObjects.firstNonNull(item.getSourceType(), 1));
    }
}
