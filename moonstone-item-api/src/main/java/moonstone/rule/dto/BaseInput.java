/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.GroupedSkuAttribute;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 给规则校验用的信息
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
public abstract class BaseInput implements Serializable {

    private static final long serialVersionUID = -5808128957323868612L;

    /**
     * 类目id
     */
    @JsonIgnore
    public abstract Long getCategoryId();

    /**
     * spuId
     */
    @JsonIgnore
    public abstract Long getSpuId();

    /**
     * 对应的sku或者skuTemplate的销售属性key
     */
    @JsonIgnore
    public abstract Set<String> getSkuAttrKeys();



    /**
     * 设置处理后的sku或者skuTemplate
     *
     * @return   sku或者skuTemplate 列表
     */
    @JsonIgnore
    public abstract List<? extends GeneralSku> getGeneralSkus();

    /**
     * spu的sku属性
     */
    @Getter
    @Setter
    protected List<GroupedSkuAttribute> groupedSkuAttributes;

    /**
     * spu的其他属性
     */
    @Getter
    @Setter
    protected List<GroupedOtherAttribute> groupedOtherAttributes;

}
