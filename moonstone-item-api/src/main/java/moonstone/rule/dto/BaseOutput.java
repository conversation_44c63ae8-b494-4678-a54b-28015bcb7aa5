/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.dto;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.component.dto.attribute.GroupedOtherAttributeWithRule;
import moonstone.component.dto.attribute.GroupedSkuAttributeWithRule;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
@EqualsAndHashCode(of={"skuAttrs","otherAttrs"})
public abstract class BaseOutput implements Serializable {
    private static final long serialVersionUID = -1434623015265583711L;

    /**
     * sku属性 及属性值们, sku属性按照属性key值归组
     */
    @Getter
    @Setter
    protected List<GroupedSkuAttributeWithRule> skuAttrs;

    /**
     * 其他属性及属性值们, 其他属性则按照组名归组
     */
    @Getter
    @Setter
    protected List<GroupedOtherAttributeWithRule> otherAttrs;


    /**
     * 设置处理后的sku或者skuTemplate
     *
     * @param generalSkus  sku或者skuTemplate
     */
    public abstract void setGeneralSku(List<? extends GeneralSku> generalSkus);
}
