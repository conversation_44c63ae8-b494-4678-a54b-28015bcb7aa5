/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule;

import moonstone.attribute.dto.AttributeMetaKey;
import moonstone.common.exception.InvalidException;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 规则基类
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
public abstract class RuleExecutor {

    /**
     *
     * 规则引擎在处理数据输入时会调用这个方法 (例如用户发布或者更新商品/spu),
     *
     * 校验数据,一旦数据不合法, 会抛出异常
     *
     * 重要: 可以根据需求而定是否抛出异常,
     * 例如可能的需求有:
     * 1.忽略或者过滤用户提交的无效数据
     * 2. 可以处理用户提交的数据, 比如修正或者添加更多的信息
     *
     * @param input 用户提交的数据
     * @param output 可能转换成为不同类型的数据, 注意, 也可能直接修改input的数据作为处理结果
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    public abstract void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException;


    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品/spu, 或者进入编辑商品/spu的界面)
     *
     * 处理数据, 如果数据不合法, 不会抛出异常, 而是根据规则做相应的修正
     *
     * 目前的策略是, 根据规则本身来修正或者过滤, 或者添加信息,  也可以根据需要抛出异常
     *
     * @param input 用户提交的数据, 在处理过程中可能会逐步的修正
     * @param output  如果要求输出数据类型和输入类型不一致,则需要使用这个参数, 用来收集处理的输出,可能需要分步处理
     */
    public abstract  void doHandleOutboundData(BaseInput input, BaseOutput output);

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return  是否适用本规则
     */
    public abstract  boolean support(BaseInput input);


    /**
     * 处理数据输入
     *
     * @param input  待处理的输入数据
     * @param output 可能转换成为不同类型的数据, 注意, 也可能直接修改input的数据作为处理结果
     * @throws InvalidException  如果策略是校验失败, 抛出异常说明原因
     */
    protected void handleInboundData(BaseInput input, BaseOutput output) throws InvalidException{
        if(support(input)){
            doHandleInboundData(input, output);
        }
    }

    /**
     * 处理数据输出
     *
     * @param data 待处理的数据
     * @param output 如果要求输出数据类型和输入类型不一致,则需要使用这个参数, 用来收集处理的输出,可能需要分步处理
     */
    protected void handleOutboundData(BaseInput data, BaseOutput output){
        if(support(data)){
            doHandleOutboundData(data,output);
        }
    }

    /**
     * 获取本执行器要执行的规则列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    protected abstract List<?> getRules(BaseInput data);


    /**
     * 是否允许自定义值
     *
     * @param attrMetas  属性元信息
     * @return  是否允许自定义值
     */
    protected static boolean userDefinedValueAllowed(Map<AttributeMetaKey, String> attrMetas) {
        if (CollectionUtils.isEmpty(attrMetas)) {
            return false;
        }
        return Boolean.valueOf(attrMetas.get(AttributeMetaKey.USER_DEFINED));
    }


}
