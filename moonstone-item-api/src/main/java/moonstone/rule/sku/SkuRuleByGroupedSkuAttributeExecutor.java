/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.sku;

import com.google.common.base.Objects;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.common.exception.InvalidException;
import moonstone.item.dto.FullItem;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import moonstone.rule.dto.GeneralSku;
import moonstone.spu.dto.FullSpu;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

/**
 * 保证sku的属性在groupedSkuAttribute的范围内
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-04
 */
@Slf4j
public class SkuRuleByGroupedSkuAttributeExecutor extends RuleExecutor {
    /**
     * 规则引擎在处理数据输入时会调用这个方法 (例如用户发布或者更新商品/spu),
     * <p/>
     * <p/>
     * 检查sku的属性是否在GroupedSkuAttributes中
     *
     * @param input  用户提交的数据
     * @param output 可能转换成为不同类型的数据, 注意, 也可能直接修改input的数据作为处理结果
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException {
        List<GroupedSkuAttribute> rules = (List<GroupedSkuAttribute>) getRules(input);

        Multimap<String, SkuAttribute> byAttrKey = HashMultimap.create();
        if (!CollectionUtils.isEmpty(rules)) {
            for (GroupedSkuAttribute rule : rules) {
                String attrKey = rule.getAttrKey();
                for (SkuAttribute skuAttribute : rule.getSkuAttributes()) {
                    byAttrKey.put(attrKey, skuAttribute);
                }
            }
        }

        if (CollectionUtils.isEmpty(input.getGeneralSkus())) {
            log.error("no sku specified for {}", input);
            throw new InvalidException("sku.missing");
        }

        for (GeneralSku generalSku : input.getGeneralSkus()) {
            List<SkuAttribute> skuAttributes = generalSku.getAttrs();

            //如果groupedSkuAttribute和sku都没有指定销售属性, 视为合法
            if (CollectionUtils.isEmpty(skuAttributes) && byAttrKey.isEmpty()) {
                continue;
            }

            //如果 groupedSkuAttribute定义了销售属性, 但是sku没有指定销售属性, 校验失败
            if (CollectionUtils.isEmpty(skuAttributes) && !byAttrKey.isEmpty()) {
                log.error("no sku attribute for {}", generalSku);
                throw new InvalidException("sku.attribute.missing");
            }

            for (SkuAttribute skuAttribute : skuAttributes) {
                String attrKey = skuAttribute.getAttrKey();
                if (!byAttrKey.containsKey(attrKey)) {
                    log.error("sku attrKey:{} is invalid", attrKey);
                    throw new InvalidException("sku.attr(key={0}).invalid", attrKey);
                }
                Collection<SkuAttribute> allowedValues = byAttrKey.get(attrKey);

                boolean valid = false;
                for (SkuAttribute allowedValue : allowedValues) {
                    //暂时只校验value和unit吧
                    if (Objects.equal(allowedValue.getAttrVal(), skuAttribute.getAttrVal())
                            && Objects.equal(allowedValue.getUnit(), skuAttribute.getUnit())) {
                        valid = true;
                        break;
                    }
                }
                if (!valid) {
                    log.error("sku attrKey:{} is invalid", skuAttribute);
                    throw new InvalidException("sku.attr(key={0}).invalid", skuAttribute);
                }
            }
        }

    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品/spu, 或者进入编辑商品/spu的界面)
     * <p/>
     * <p/>
     * 对于销售属性不在GroupedSkuAttribute的sku, 需要被过滤掉
     *
     * @param input  用户提交的数据, 在处理过程中可能会逐步的修正
     * @param output 如果要求输出数据类型和输入类型不一致,则需要使用这个参数, 用来收集处理的输出,可能需要分步处理
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput output) {
        //没有定义sku, 直接返回
        if (CollectionUtils.isEmpty(input.getGeneralSkus())) {
            return;
        }

        List<GroupedSkuAttribute> rules = (List<GroupedSkuAttribute>) getRules(input);
        //如果groupedSkuAttributes为空, 那么只允许一个sku/skuTemplate,且该sku的销售属性必须为空
        if (CollectionUtils.isEmpty(rules)) {
            GeneralSku generalSku = input.getGeneralSkus().get(0);
            if(!CollectionUtils.isEmpty(generalSku.getAttrs())) {
                generalSku.getAttrs().clear();
            }
            output.setGeneralSku(Lists.newArrayList(generalSku));
            return;
        }



        Multimap<String, SkuAttribute> byAttrKey = HashMultimap.create();

        for (GroupedSkuAttribute rule : rules) {
            String attrKey = rule.getAttrKey();
            for (SkuAttribute skuAttribute : rule.getSkuAttributes()) {
                byAttrKey.put(attrKey, skuAttribute);
            }
        }

        List<GeneralSku> result = Lists.newArrayListWithCapacity(input.getGeneralSkus().size());
        for (GeneralSku generalSku : input.getGeneralSkus()) {
            List<SkuAttribute> skuAttributes = generalSku.getAttrs();

            //如果 groupedSkuAttribute定义了销售属性, 但是sku没有指定销售属性, 校验失败
            if (CollectionUtils.isEmpty(skuAttributes)) {
                continue;
            }

            boolean valid = true;
            for (SkuAttribute skuAttribute : skuAttributes) {
                String attrKey = skuAttribute.getAttrKey();
                //如果sku key
                if (!byAttrKey.containsKey(attrKey)) {
                    valid = false;
                    break;
                }
                Collection<SkuAttribute> allowedValues = byAttrKey.get(attrKey);
                valid = false;
                for (SkuAttribute allowedValue : allowedValues) {
                    //暂时只校验value和unit吧
                    if (Objects.equal(allowedValue.getAttrVal(), skuAttribute.getAttrVal())
                            && Objects.equal(allowedValue.getUnit(), skuAttribute.getUnit())) {
                        valid = true;
                        break;
                    }
                }
            }

            if(valid){
               result.add(generalSku);
            }
        }
        output.setGeneralSku(result);
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {
        return input instanceof FullItem ||
                input instanceof FullSpu;
    }

    /**
     * 获取本执行器要执行的规则列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        return data.getGroupedSkuAttributes();
    }
}
