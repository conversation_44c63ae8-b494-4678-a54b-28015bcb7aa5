/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.sku;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.category.model.CategoryAttribute;
import moonstone.common.exception.InvalidException;
import moonstone.component.dto.attribute.AttributeRule;
import moonstone.rule.RuleExecutor;
import moonstone.rule.attribute.AttributeRuleBuilder;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import moonstone.rule.dto.GeneralSku;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类目属性级别定义的sku/skuTemplate规则
 *
 * 用类目属性级别定义的销售属性信息来校验商品或者spu的sku或者skuTemplate
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
@Slf4j
public abstract class GeneralSkuRuleByCategoryExecutor extends RuleExecutor {

    /**
     * 校验用户提交的数据时调用, 即用户输入时调用(例如用户发布或者更新商品)
     * <p/>
     * 重要: 可以根据需求而定是否抛出异常,
     * 例如可能的需求有:
     * 1.忽略或者过滤用户提交的无效数据
     * 2. 可以处理用户提交的数据, 比如修正或者添加更多的信息
     *
     * @param input 用户提交的数据
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput baseOutput) throws InvalidException {
        List<?> rules = getRules(input);
        // protect the attribute duplicate situation
        Map<String, Object> byAttrKey = new HashMap<>();
        rules.stream().map(r -> (AttributeRule) r)
                .forEach(r -> byAttrKey.put(r.getAttrKey(), r));
        //Maps.uniqueIndex(rules,                (Function<Object, String>) rule -> ((AttributeRule) rule).getAttrKey());

        //如果类目未定义销售属性 那么最多只允许一个sku/skuTemplate存在
        if(CollectionUtils.isEmpty(rules) && input.getGeneralSkus().size()>1){
            log.error("multiple sku not allowed, since no sku attribute rule defined by category(id={}) ",
                    input.getCategoryId());
            throw new InvalidException("single.sku.allowed");
        }

        //校验每个sku或者skuTemplate
        for (GeneralSku generalSku : input.getGeneralSkus()) {
            List<SkuAttribute> skuAttributes = generalSku.getAttrs();

            //如果类目定义了销售属性, 那么sku可以自由选择是否需要销售属性
            if(!CollectionUtils.isEmpty(rules)&&CollectionUtils.isEmpty(skuAttributes)){
                return;
            }

            //类目和sku都没定义销售属性, 视为有效
            if(CollectionUtils.isEmpty(rules)&& CollectionUtils.isEmpty(skuAttributes)){
                return;
            }

            //只有sku或者skuTemplates的每个属性key和value都合法, 才是有效的
            for (SkuAttribute skuAttribute : skuAttributes) {
                final String attrKey = skuAttribute.getAttrKey();

                //key是否有效?
                if (!byAttrKey.containsKey(attrKey)) {
                    log.error("sku attribute key ({}) is invalid", attrKey);
                    throw new InvalidException("sku.attr(key={0}).invalid", attrKey);
                }

                //值是否有效?
                AttributeRule attributeRule = (AttributeRule) byAttrKey.get(attrKey);
                if (!attributeRule.getAttrVals().contains(skuAttribute.getAttrVal())
                        && !userDefinedValueAllowed(attributeRule.getAttrMetas())) {
                    log.error("sku attribute (key={},val={})'s value is invalid",
                            skuAttribute.getAttrKey(), skuAttribute.getAttrVal());
                    throw new InvalidException("sku.attr(key={0},val={1}).invalid",
                            skuAttribute.getAttrKey(),skuAttribute.getAttrVal());
                }
            }
        }

    }

    /**
     * 返回数据给用户时调用(例如用户查询商品),
     * <p/>
     * 目前的策略是, 根据规则本身来修正或者过滤, 或者添加信息,  也可以根据需要抛出异常
     *
     * @param input  用户提交的数据
     * @param result 经过处理后的数据,可能需要分步处理, 这里期望是StrictEditItem或者StrictSpu
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput result) {
        List<?> rules = getRules(input);

        Map<String, ?> byAttrKey = Maps.uniqueIndex(rules, new Function<Object, String>() {
            @Override
            public String apply(Object rule) {
                return ((AttributeRule) rule).getAttrKey();
            }
        });

        List<GeneralSku> generalSkus =
                Lists.newArrayListWithCapacity(input.getGeneralSkus().size());

        //校验每个sku或者skuTemplate
        for (GeneralSku generalSku : input.getGeneralSkus()) {
            List<SkuAttribute> skuAttributes = generalSku.getAttrs();

            if(CollectionUtils.isEmpty(skuAttributes)){ //如果销售属性为空, 那么直接返回
                generalSkus.add(generalSku);
                continue;
            }

            boolean valid = true;
            //只有sku或者skuTemplates的每个属性key和value都合法, 才是有效的
            for (SkuAttribute skuAttribute : skuAttributes) {
                final String attrKey = skuAttribute.getAttrKey();

                //key是否有效?
                if (!byAttrKey.containsKey(attrKey)) {
                    log.error("sku attribute key ({}) is invalid", attrKey);
                    valid = false;
                    break;
                }

                //值是否有效?
                AttributeRule attributeRule = (AttributeRule) byAttrKey.get(attrKey);
                if (!attributeRule.getAttrVals().contains(skuAttribute.getAttrVal())
                        && !userDefinedValueAllowed(attributeRule.getAttrMetas())) {
                    log.error("sku attribute (key={},val={})'s value is invalid",
                            skuAttribute.getAttrKey(), skuAttribute.getAttrVal());
                    valid = false;
                    break;
                }
            }
            if(valid){
                generalSkus.add(generalSku);
            }
        }
        result.setGeneralSku(generalSkus);
    }

    protected abstract List<CategoryAttribute> getCategoryAttributes(Long categoryId);

    /**
     * 获取本执行器要执行的规则列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        List<CategoryAttribute> categoryAttributes = getCategoryAttributes(data.getCategoryId());
        if (CollectionUtils.isEmpty(categoryAttributes)) {
            return Collections.emptyList();
        }
        return AttributeRuleBuilder.buildSkuAttributeRule(categoryAttributes);
    }
}
