/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.sku;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cache.SpuCacher;
import moonstone.common.exception.InvalidException;
import moonstone.component.dto.item.EditItem;
import moonstone.item.dto.FullItem;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.model.Sku;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import moonstone.rule.dto.GeneralSku;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.model.SkuTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * spu级别定义的sku规则, 通过spu发布的商品的sku必须遵循spu定义的sku规则
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-28
 */
@Slf4j
public class SkuRuleBySpuExecutor extends RuleExecutor {

    private final SpuCacher spuCacher;

    public SkuRuleBySpuExecutor(SpuCacher spuCacher) {
        this.spuCacher = spuCacher;
    }

    /**
     * 规则引擎在处理数据输入时会调用这个方法 (例如用户发布或者更新商品/spu),
     * <p/>
     * 校验商品的sku中的属性是否为spu定义的skuTemplate的子集
     *
     * @param input  用户提交的数据
     * @param output 可能的转换的数据结果, 注意: 也可能直接修改input的数据作为处理结果
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException {
        List<SkuTemplate> skuTemplates = (List<SkuTemplate>) getRules(input);
        //如果spu和商品都没有定义sku, 略过
        if (CollectionUtils.isEmpty(skuTemplates) && CollectionUtils.isEmpty(input.getGeneralSkus())) {
            return;
        }
        //如果spu没有定义skuTemplate, 但是商品有sku, 不合法
        if (CollectionUtils.isEmpty(skuTemplates) && !CollectionUtils.isEmpty(input.getGeneralSkus())) {
            log.error("no sku allowed for spu(id={})", input.getSpuId());
            throw new InvalidException("sku.not.permit");
        }

        //如果spu定义了skuTemplate, 但是商品一个sku都没有, 不合法
        if (!CollectionUtils.isEmpty(skuTemplates) && CollectionUtils.isEmpty(input.getGeneralSkus())) {
            log.error("sku required for spu(id={})", input.getSpuId());
            throw new InvalidException("sku.missing");
        }

        //按照attrKe分组skuTemplate允许的attrVal
        Multimap<String, String> allowedSkuAttributes = HashMultimap.create();
        for (SkuTemplate skuTemplate : skuTemplates) {
            if(!CollectionUtils.isEmpty(skuTemplate.getAttrs())) {
                for (SkuAttribute skuAttribute : skuTemplate.getAttrs()) {
                    allowedSkuAttributes.put(skuAttribute.getAttrKey(), skuAttribute.getAttrVal());
                }
            }
        }

        //判断sku的属性是否在允许的范围内
        for (GeneralSku generalSku : input.getGeneralSkus()) {
            Sku sku = (Sku) generalSku;
            if(!CollectionUtils.isEmpty(sku.getAttrs())) {
                for (SkuAttribute skuAttribute : sku.getAttrs()) {
                    final String attrKey = skuAttribute.getAttrKey();
                    if (!allowedSkuAttributes.containsKey(attrKey)) {
                        log.error("sku attr key({}) not allowed", attrKey);
                        throw new InvalidException("sku.attr(key={0}).invalid", attrKey);
                    }
                    final String attrVal = skuAttribute.getAttrVal();
                    Collection<String> allowedValues = allowedSkuAttributes.get(attrKey);
                    if (!allowedValues.contains(attrVal)) {
                        log.error("sku attr val({}) of sku attr key ({}) is invalid", attrVal, attrKey);
                        throw new InvalidException("sku.attr(key={0},val={1}).invalid", attrKey, attrVal);
                    }
                }
            }
        }


    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品/spu, 或者进入编辑商品/spu的界面)
     * <p/>
     * 处理数据, 如果数据不合法, 不会抛出异常, 而是根据规则做相应的修正
     * <p/>
     *
     * 如果商品定义的sku有属性不在spu定义的skuTemplate范围内, 则会被过滤掉
     *
     * @param input  用户提交的数据, 在处理过程中可能会逐步的修正
     * @param output 如果要求输出数据类型和输入类型不一致,则需要使用这个参数, 用来收集处理的输出,可能需要分步处理
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput output) {
        List<SkuTemplate> skuTemplates = (List<SkuTemplate>) getRules(input);
        //如果spu和商品都没有定义sku, 略过
        if (CollectionUtils.isEmpty(skuTemplates) && CollectionUtils.isEmpty(input.getGeneralSkus())) {
            return;
        }
        //如果spu没有定义skuTemplate, 但是商品有sku, 不合法
        if (CollectionUtils.isEmpty(skuTemplates) && !CollectionUtils.isEmpty(input.getGeneralSkus())) {
            log.error("no sku allowed for spu(id={})", input.getSpuId());
            ((EditItem) output).setSkuWithCustoms(Collections.emptyList());
        }

        //如果spu定义了skuTemplate, 但是商品一个sku都没有, 不合法
        if (!CollectionUtils.isEmpty(skuTemplates) && CollectionUtils.isEmpty(input.getGeneralSkus())) {
            log.error("sku of item missing because spu(id={}) required", input.getSpuId());
            return;
        }

        //按照attrKe分组skuTemplate允许的attrVal
        Multimap<String, String> allowedSkuAttributes = HashMultimap.create();
        for (SkuTemplate skuTemplate : skuTemplates) {
            if (!CollectionUtils.isEmpty(skuTemplate.getAttrs())) {
                for (SkuAttribute skuAttribute : skuTemplate.getAttrs()) {
                    allowedSkuAttributes.put(skuAttribute.getAttrKey(), skuAttribute.getAttrVal());
                }
            }
        }

        List<Sku> validSkus = Lists.newArrayList();
        //判断sku的属性是否在允许的范围内
        for (GeneralSku generalSku : input.getGeneralSkus()) {
            Sku sku = (Sku) generalSku;
            boolean valid = true;
            if(!CollectionUtils.isEmpty(sku.getAttrs())) {
                for (SkuAttribute skuAttribute : sku.getAttrs()) {
                    final String attrKey = skuAttribute.getAttrKey();
                    if (!allowedSkuAttributes.containsKey(attrKey)) {
                        log.error("sku attr key({}) not allowed", attrKey);
                        valid = false;
                        break;
                    }
                    final String attrVal = skuAttribute.getAttrVal();
                    Collection<String> allowedValues = allowedSkuAttributes.get(attrKey);
                    if (!allowedValues.contains(attrVal)) {
                        log.error("sku attr val({}) of sku attr key ({}) is invalid", attrVal, attrKey);
                        valid = false;
                        break;
                    }
                }
            }
            if(valid){
                validSkus.add(sku);
            }
        }
        List<SkuWithCustom> skuWithCustoms = new ArrayList<>();
        for (Sku sku:validSkus) {
            SkuWithCustom skuWithCustom = new SkuWithCustom();
            skuWithCustom.setSku(sku);
            skuWithCustoms.add(skuWithCustom);
        }
        ((EditItem)output).setSkuWithCustoms(skuWithCustoms);
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {
        //只支持商品
        if (!(input instanceof FullItem)) {
            return false;
        }
        //如果spu id不存在, 则本规则也不适用
        if (input.getSpuId() == null) {
            return false;
        }

        //如果对应spu的状态不正常, 则本规则也不适用
        FullSpu fullSpu = spuCacher.findFullSpuById(input.getSpuId());
        return fullSpu.getSpu().getStatus() >= 0;
    }

    /**
     * 获取本执行器要执行的规则列表, 这里直接返回spu定义的skuTemplate列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        final Long spuId = data.getSpuId();
        FullSpu fullSpu = spuCacher.findFullSpuById(spuId);
        return fullSpu.getSkuTemplates();
    }
}
