/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.sku;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.BackCategoryCacher;
import moonstone.cache.CategoryAttributeCacher;
import moonstone.category.model.BackCategory;
import moonstone.category.model.CategoryAttribute;
import moonstone.item.dto.FullItem;
import moonstone.rule.dto.BaseInput;

import java.util.List;

/**
 * 本规则用类目下的销售属性来处理商品的sku, 类目属性走缓存查询
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-07
 */
@Slf4j
public class SkuRuleByCategoryExecutor extends GeneralSkuRuleByCategoryExecutor {

    private final BackCategoryCacher backCategoryCacher;

    private final CategoryAttributeCacher categoryAttributeCacher;

    public SkuRuleByCategoryExecutor(BackCategoryCacher backCategoryCacher,
                                     CategoryAttributeCacher categoryAttributeCacher) {
        this.backCategoryCacher = backCategoryCacher;
        this.categoryAttributeCacher = categoryAttributeCacher;
    }

    @Override
    protected List<CategoryAttribute> getCategoryAttributes(Long categoryId) {
        return categoryAttributeCacher.findByCategoryId(categoryId);
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {
        if(!(input instanceof FullItem)){
            return false;
        }
        Long categoryId = input.getCategoryId();
        try {
            BackCategory backCategory = backCategoryCacher.findBackCategoryById(categoryId);
            if(backCategory.getStatus()<0){
                log.warn("back category(id={}) is deleted logically", categoryId);
                return false;
            }
        } catch (Exception e) { //may be not found, treat as be delete logically
            log.error("failed to find back category (id={}), cause:{}", categoryId, e.getMessage());
            return false;
        }
        return true;
    }
}
