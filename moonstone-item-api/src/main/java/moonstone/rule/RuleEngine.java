/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule;

import moonstone.common.exception.InvalidException;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 规则引擎
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
@Component
public class RuleEngine {

    private final RuleExecutorPipeline ruleExecutorPipeline;


    @Autowired
    public RuleEngine(RuleExecutorPipeline ruleExecutorPipeline) {
        this.ruleExecutorPipeline = ruleExecutorPipeline;
    }

    /**
     * 依次按照规则校验用户输入数据
     *
     * @param input  用户输入数据, 分步进行校验
     * @param output 可能转换成为不同类型的数据, 注意, 也可能直接修改input的数据作为处理结果
     * @throws InvalidException 如果校验失败, 可能会抛出异常说明原因
     */
    public <T extends BaseInput> void handleInboundData(T input, BaseOutput output) throws InvalidException {
        for (RuleExecutor ruleExecutor : ruleExecutorPipeline.getRuleExecutors()) {
            ruleExecutor.handleInboundData(input, output);
        }
    }

    /**
     * 依次按照规则处理返回给用户的数据
     *
     * @param input  待处理的输出数据
     * @param output 待组装的数据, 分步进行组装, 可能转换成为不同类型的数据, 注意, 也可能直接修改input的数据作为处理结果
     * @param <T>    泛型参数
     */
    public <T extends BaseInput> void handleOutboundData(T input, BaseOutput output) {
        for (RuleExecutor ruleExecutor : ruleExecutorPipeline.getRuleExecutors()) {
            ruleExecutor.handleOutboundData(input, output);
        }
    }
}
