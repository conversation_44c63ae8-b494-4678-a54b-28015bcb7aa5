/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.CharMatcher;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.common.exception.InvalidException;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 校验属性名和属性值均不能包含冒号和下划线
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-16
 */
@Slf4j
public class AttributeLiteralRule extends RuleExecutor {

    private final CharMatcher COLON_OR_UNDERLINE = CharMatcher.anyOf(":_");

    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException {

        //校验非销售属性
        List<GroupedOtherAttribute> goas =  input.getGroupedOtherAttributes();
        if(!CollectionUtils.isEmpty(goas)){
            for (GroupedOtherAttribute goa : goas) {
                final List<OtherAttribute> otherAttributes = goa.getOtherAttributes();
                if(!CollectionUtils.isEmpty(otherAttributes)) {
                    for (OtherAttribute otherAttribute : otherAttributes) {
                        final String attrKey = otherAttribute.getAttrKey();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrKey)){
                            log.error("attrKey:{} contains illegal characters(: or _)", attrKey);
                            throw new InvalidException("attrKey({0}).contains.colon.or.underline", attrKey);
                        }
                        final String attrVal = otherAttribute.getAttrVal();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrVal)){
                            log.error("attrVal:{} contains illegal characters(: or _)", attrVal);
                            throw new InvalidException("attrVal({0}).contains.colon.or.underline", attrVal);
                        }
                    }
                }
            }
        }

        //校验销售属性
        List<GroupedSkuAttribute> gsas = input.getGroupedSkuAttributes();
        if(!CollectionUtils.isEmpty(gsas)){
            for (GroupedSkuAttribute gsa : gsas) {
                final List<SkuAttribute> skuAttributes = gsa.getSkuAttributes();
                if(!CollectionUtils.isEmpty(skuAttributes)) {
                    for (SkuAttribute skuAttribute : skuAttributes) {
                        final String attrKey = skuAttribute.getAttrKey();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrKey)){
                            log.error("attrKey:{} contains illegal characters(: or _)", attrKey);
                            throw new InvalidException("attrKey({0}).contains.colon.or.underline", attrKey);
                        }
                        final String attrVal = skuAttribute.getAttrVal();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrVal)){
                            log.error("attrVal:{} contains illegal characters(: or _)", attrVal);
                            throw new InvalidException("attrVal({0}).contains.colon.or.underline", attrVal);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput output) {
        //过滤掉不合法的非销售属性
        List<GroupedOtherAttribute> goas =  input.getGroupedOtherAttributes();
        if(!CollectionUtils.isEmpty(goas)){
            for (GroupedOtherAttribute goa : goas) {
                final List<OtherAttribute> otherAttributes = goa.getOtherAttributes();
                if(!CollectionUtils.isEmpty(otherAttributes)) {
                    final List<OtherAttribute> validOtherAttributes = Lists.newArrayListWithCapacity(otherAttributes.size());
                    for (OtherAttribute otherAttribute : otherAttributes) {
                        final String attrKey = otherAttribute.getAttrKey();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrKey)){
                            log.error("attrKey:{} contains illegal characters(: or _)", attrKey);
                            continue;
                        }
                        final String attrVal = otherAttribute.getAttrVal();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrVal)){
                            log.error("attrVal:{} contains illegal characters(: or _)", attrVal);
                            continue;
                        }
                        validOtherAttributes.add(otherAttribute);
                    }
                    goa.setOtherAttributes(validOtherAttributes);
                }
            }
        }

        //过滤掉不合法的销售属性
        List<GroupedSkuAttribute> gsas = input.getGroupedSkuAttributes();
        if(!CollectionUtils.isEmpty(gsas)){
            for (GroupedSkuAttribute gsa : gsas) {
                final List<SkuAttribute> skuAttributes = gsa.getSkuAttributes();
                if(!CollectionUtils.isEmpty(skuAttributes)) {
                    final List<SkuAttribute> validSkuAttributes = Lists.newArrayListWithCapacity(skuAttributes.size());
                    for (SkuAttribute skuAttribute : skuAttributes) {
                        final String attrKey = skuAttribute.getAttrKey();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrKey)){
                            log.error("attrKey:{} contains illegal characters(: or _)", attrKey);
                            continue;
                        }
                        final String attrVal = skuAttribute.getAttrVal();
                        if(COLON_OR_UNDERLINE.matchesAnyOf(attrVal)){
                            log.error("attrVal:{} contains illegal characters(: or _)", attrVal);
                            continue;
                        }
                        validSkuAttributes.add(skuAttribute);
                    }
                    gsa.setSkuAttributes(validSkuAttributes);
                }
            }
        }
    }

    @Override
    public boolean support(BaseInput input) {
        return true;
    }

    @Override
    protected List<?> getRules(BaseInput data) {
        return Collections.emptyList();
    }
}
