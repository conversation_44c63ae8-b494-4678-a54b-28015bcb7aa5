/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.Function;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.AttributeMetaKey;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.category.model.CategoryAttribute;
import moonstone.common.exception.InvalidException;
import moonstone.component.dto.attribute.AttributeRule;
import moonstone.component.dto.attribute.GroupedSkuAttributeWithRule;
import moonstone.component.dto.attribute.SkuAttributeRule;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 类目属性级别定义的销售属性规则
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
@Slf4j
public abstract class SkuAttributeRuleByCategoryExecutor extends RuleExecutor {

    /**
     * 规则引擎在处理数据输入时会调用这个方法 (例如用户发布或者更新商品/spu)
     *
     * @param input 用户提交的数据
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException {
        final List<?> rules = getRules(input);
        final List<GroupedSkuAttribute> groupedSkuAttributesOfItemOrSpu = input.getGroupedSkuAttributes();
        //如果skuAttributeRules为空, 视为完全开放
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        //如果类目属性中定义了销售属性范围, 而商品或者spu没有选择任何销售属性, 也视为合法
        if (CollectionUtils.isEmpty(groupedSkuAttributesOfItemOrSpu)) {
            return;
        }

        Map<String, ?> byAttrKey = Maps.uniqueIndex(rules, new Function<Object, String>() {
            @Override
            public String apply(Object rule) {
                AttributeRule attributeRule = (AttributeRule) rule;
                return attributeRule.getAttrKey();
            }
        });

        //校验选择的销售属性是否在类目允许的销售属性范围内
        for (GroupedSkuAttribute groupedSkuAttributeOfItemOrSpu : groupedSkuAttributesOfItemOrSpu) {
            final String attrKey = groupedSkuAttributeOfItemOrSpu.getAttrKey();

            //校验销售属性key是否在类目允许的范围内
            if (!byAttrKey.containsKey(attrKey)) {
                log.error("attrKey:{} is not allowed", attrKey);
                throw new InvalidException("sku.attr(key={0}).invalid", attrKey);
            }

            //校验销售属性值是否在类目允许的范围内
            AttributeRule attributeRule = (AttributeRule) byAttrKey.get(attrKey);
            for (SkuAttribute skuAttribute : groupedSkuAttributeOfItemOrSpu.getSkuAttributes()) {
                String attrVal = skuAttribute.getAttrVal();
                List<String> allowedValues = attributeRule.getAttrVals();
                if (!allowedValues.contains(attrVal) && !userDefinedValueAllowed(attributeRule.getAttrMetas())) {//值无效
                    log.error("sku attrVal({}) of attrKey({}) not allowed", attrVal, attrKey);
                    throw new InvalidException("sku.attr(key={0},val={1}).invalid",attrKey, attrVal);
                }
            }
        }
    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品),
     * <p/>
     * 目前的策略是, 根据规则本身来修正或者过滤, 或者添加信息,  也可以根据需要抛出异常
     *
     * @param input  用户提交的数据
     * @param result 经过处理后的数据,可能需要分步处理
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput result) {
        final List<?> rules = getRules(input);

        final List<GroupedSkuAttribute> groupedSkuAttributes = input.getGroupedSkuAttributes();

        //如果没有定义销售属性组, 直接返回
        if(CollectionUtils.isEmpty(groupedSkuAttributes)){
            return;
        }

        //类目没有定义销售属性规则, 那也不允许商品和spu定义销售属性
        if (CollectionUtils.isEmpty(rules) && !CollectionUtils.isEmpty(groupedSkuAttributes)) {
            result.setSkuAttrs(Lists.newArrayList());
            return;
        }


        List<GroupedSkuAttributeWithRule> groupedSkuAttributeWithRules = Lists.newArrayList();

        //校验每个销售属性是否合法
        for (GroupedSkuAttribute groupedSkuAttribute : groupedSkuAttributes) {
            String attrKey = groupedSkuAttribute.getAttrKey();
            for (Object rule : rules) {
                AttributeRule attributeRule = (AttributeRule) rule;
                final Map<AttributeMetaKey, String> attrMetas = attributeRule.getAttrMetas();
                if (Objects.equal(attributeRule.getAttrKey(), attrKey)) {
                    List<String> definedValues = MoreObjects.firstNonNull(attributeRule.getAttrVals(),
                            Collections.emptyList());
                    List<SkuAttributeRule> skuAttributeRules = Lists.newArrayList();
                    for (SkuAttribute skuAttribute : groupedSkuAttribute.getSkuAttributes()) {
                        //如果属性值在预设值范围类,或者属性允许自定义, 则为有效的销售属性
                        String attrVal = skuAttribute.getAttrVal();
                        if (definedValues.contains(attrVal) || userDefinedValueAllowed(attrMetas)) {
                            SkuAttributeRule skuAttributeRule = new SkuAttributeRule();
                            skuAttributeRule.setAttrMetas(attrMetas);
                            skuAttributeRule.setAttrVal(attrVal);
                            skuAttributeRule.setUnit(skuAttribute.getUnit());
                            skuAttributeRule.setImage(skuAttribute.getImage());
                            skuAttributeRule.setShowImage(skuAttribute.getShowImage());
                            skuAttributeRule.setThumbnail(skuAttribute.getThumbnail());
                            skuAttributeRules.add(skuAttributeRule);
                        }
                    }
                    if (!CollectionUtils.isEmpty(skuAttributeRules)) {
                        GroupedSkuAttributeWithRule gsaw = new GroupedSkuAttributeWithRule();
                        gsaw.setAttrKey(attrKey);
                        gsaw.setAttributeRules(skuAttributeRules);
                        groupedSkuAttributeWithRules.add(gsaw);
                    }
                }
            }
        }
        result.setSkuAttrs(groupedSkuAttributeWithRules);

    }

    protected abstract List<CategoryAttribute> getCategoryAttributes(Long categoryId);

    /**
     * 获取本执行器要执行的规则列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        List<CategoryAttribute> categoryAttributes = getCategoryAttributes(data.getCategoryId());
        if (CollectionUtils.isEmpty(categoryAttributes)) {
            return Collections.emptyList();
        }
        return AttributeRuleBuilder.buildSkuAttributeRule(categoryAttributes);
    }
}
