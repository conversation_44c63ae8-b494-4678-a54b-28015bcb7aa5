/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.Function;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.AttributeMetaKey;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.PreservedGroup;
import moonstone.category.model.CategoryAttribute;
import moonstone.common.exception.InvalidException;
import moonstone.component.dto.attribute.AttributeRule;
import moonstone.component.dto.attribute.GroupedOtherAttributeWithRule;
import moonstone.component.dto.attribute.OtherAttributeWithRule;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类目属性级别定义的非销售属性规则
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
@Slf4j
public abstract class OtherAttributeRuleByCategoryExecutor extends RuleExecutor {

    /**
     * 规则引擎在处理数据输入时会调用这个方法(例如用户发布或者更新商品/spu)
     * <p>
     * 类目属性级别的非销售属性规则, 只要有一条规则不符合需求, 则抛出异常说明原因
     *
     * @param input 用户提交的数据
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput baseOutput) throws InvalidException {
        List<?> rules = getRules(input);
        List<GroupedOtherAttribute> groupedOtherAttributesOfItemOrSpu = input.getGroupedOtherAttributes();

        //如果非销售属性和规则都为空, 则直接返回
        if (CollectionUtils.isEmpty(groupedOtherAttributesOfItemOrSpu) && CollectionUtils.isEmpty(rules)) {
            return;
        }

        //change: 注意， 这里应该允许你商品不选择必选的类目属性才对
        if(CollectionUtils.isEmpty(groupedOtherAttributesOfItemOrSpu)){
            return;
        }

        /*
        //如果非销售属性为空, 但有规则, 则查看属性规则中的属性是否都为可选的
       if (CollectionUtils.isEmpty(groupedOtherAttributesOfItemOrSpu) && !CollectionUtils.isEmpty(rules)) {
            for (Object rule : rules) {
                AttributeRule attributeRule = (AttributeRule) rule;
                Map<AttributeMetaKey, String> metas = MoreObjects.firstNonNull(attributeRule.getAttrMetas(), Collections.<AttributeMetaKey, String>emptyMap());
                final String requiredMetaVal = metas.get(AttributeMetaKey.REQUIRED);
                if (StringUtils.hasText(requiredMetaVal) && Boolean.valueOf(requiredMetaVal)) {
                    log.error("missing required other attr({})", attributeRule.getAttrKey());
                    throw new InvalidException(400, "other.attrKey({0}).missing", attributeRule.getAttrKey());
                }
            }
            return;
        }*/

        //如果规则为空, 则所有的非销售属性视为有效, 且所有的非销售属性都是自定义属性
        Map<String, OtherAttribute> byAttrKey = Maps.newHashMap();
        for (GroupedOtherAttribute groupedOtherAttribute : groupedOtherAttributesOfItemOrSpu) {
            for (OtherAttribute otherAttribute : groupedOtherAttribute.getOtherAttributes()) {
                byAttrKey.put(otherAttribute.getAttrKey(), otherAttribute);
            }
        }

        //此时一定有非销售属性, 需要进行判断和处理
        if (!CollectionUtils.isEmpty(rules)) {
            //判断input中的各种属性是否符合规则
            for (Object rule : rules) {
                AttributeRule otherAttributeRule = (AttributeRule) rule;
                String attrKey = otherAttributeRule.getAttrKey();
                OtherAttribute otherAttr = byAttrKey.remove(attrKey);

                if (otherAttr == null) {  //如果商品没有选择对应的类目属性， 应该也视为合法的， 如果选择了对应的类目属性， 则必须符合规则
                    continue;
                }

                String attrVal = otherAttr.getAttrVal();
                Map<AttributeMetaKey, String> metas = MoreObjects.firstNonNull(otherAttributeRule.getAttrMetas(), Collections.emptyMap());
                List<String> allowedValues = otherAttributeRule.getAttrVals();
                for (AttributeMetaKey attributeMetaKey : metas.keySet()) {
                    if (!attributeMetaKey.validate(attrVal, metas.get(attributeMetaKey), allowedValues)) {
                        String metaValue = metas.get(attributeMetaKey);
                        log.error("{} is invalid, cause: rules meta ({}) expect {}, but actual attr value is {}",
                                otherAttributeRule, attributeMetaKey.name(), metaValue, attrVal);
                        throw new InvalidException(400, "invalid.other.attr(key={0}, meta={1}, val={2})",
                                otherAttributeRule.getAttrKey(), attributeMetaKey, attrVal);
                    }
                }
            }
        }

        //处理类目属性之外的所有非销售属性
        List<OtherAttribute> remainderAttributes = Lists.newArrayList(byAttrKey.values());
        if (!CollectionUtils.isEmpty(remainderAttributes)) {
            for (OtherAttribute remainderAttribute : remainderAttributes) {
                String group = remainderAttribute.getGroup();
                if (!PreservedGroup.contains(group)) { //如果不是属于系统预设组, 则认为是用户自定义属性组
                    remainderAttribute.setGroup(PreservedGroup.USER_DEFINED.name());
                }
            }
        }
    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询spu, 或者进入编辑商品/spu的界面) 这里是处理商品/spu的非销售属性
     *
     * @param input  用户提交的数据
     * @param result 经过处理后的数据, 注意, 这需要是一个StrictEditItem或者StrictEditSpu对象
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput result) {
        List<GroupedOtherAttribute> groupedOtherAttributesOfItemOrSpu = input.getGroupedOtherAttributes();

        List<?> rules = getRules(input);

        //如果非销售属性和规则都为空, 则直接返回
        if (CollectionUtils.isEmpty(groupedOtherAttributesOfItemOrSpu) && CollectionUtils.isEmpty(rules)) {
            return;
        }


        Map<String, OtherAttribute> attrKeyMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(groupedOtherAttributesOfItemOrSpu)) {
            for (GroupedOtherAttribute groupedOtherAttribute : groupedOtherAttributesOfItemOrSpu) {
                if (!CollectionUtils.isEmpty(groupedOtherAttribute.getOtherAttributes())) {
                    for (OtherAttribute otherAttribute : groupedOtherAttribute.getOtherAttributes()) {
                        if (!StringUtils.hasText(otherAttribute.getGroup())){
                            otherAttribute.setGroup(groupedOtherAttribute.getGroup());
                        }
                        attrKeyMap.put(otherAttribute.getAttrKey(), otherAttribute);
                    }
                }
            }
        }


        //构建非销售属性规则
        List<OtherAttributeWithRule> otherAttributeWithRules = Lists.newArrayList();
        for (Object rule : rules) {
            AttributeRule otherAttributeRule = (AttributeRule) rule;
            List<String> attrVals = otherAttributeRule.getAttrVals();
            if (CollectionUtils.isEmpty(attrVals)) {
                attrVals = Lists.newArrayList();
                otherAttributeRule.setAttrVals(attrVals);
            }

            OtherAttributeWithRule otherAttributeWithRule = new OtherAttributeWithRule();
            otherAttributeWithRule.setAttributeRule(otherAttributeRule);

            OtherAttribute otherAttribute = attrKeyMap.remove(otherAttributeRule.getAttrKey());
            if (otherAttribute != null) {
                final String attrVal = otherAttribute.getAttrVal();
                otherAttributeWithRule.setAttrVal(attrVal);
                otherAttributeWithRule.setReadOnlyBySeller(otherAttribute.getReadOnlyBySeller());
                //如果允许用户自定义值,且规则中没有这个值, 就merge
                if (userDefinedValueAllowed(otherAttributeRule.getAttrMetas()) && !attrVals.contains(attrVal)) {
                    attrVals.add(attrVal);
                }

            }
            otherAttributeWithRules.add(otherAttributeWithRule);
        }

        for (OtherAttribute otherAttribute : attrKeyMap.values()) {
            OtherAttributeWithRule otherAttributeWithRule = new OtherAttributeWithRule();
            otherAttributeWithRule.setAttrVal(otherAttribute.getAttrVal());
            otherAttributeWithRule.setReadOnlyBySeller(otherAttribute.getReadOnlyBySeller());
            AttributeRule attributeRule = new AttributeRule();

            final String group = otherAttribute.getGroup();

            // 如果非销售属性不在规则中, 暂时视为自定义属性

            // 如果属性原来属于系统预设组, 但是现在不在该组了, 视为自定义属性,
            // 对于spu, 则放到名为spu的group里, 对于商品, 则放到名为user_defined的group里
            //对于BASIC这个基础类目, 特殊处理, 不改变他的组名
            if (PreservedGroup.contains(group) &&
                    !group.equalsIgnoreCase(PreservedGroup.BASIC.name())) {
                attributeRule.setGroup(fallbackGroup());
            } else { //如果不属于系统预设组, 则保留原来的组名吧
                attributeRule.setGroup(group);
            }
            attributeRule.setAttrKey(otherAttribute.getAttrKey());
            otherAttributeWithRule.setAttributeRule(attributeRule);
            otherAttributeWithRules.add(otherAttributeWithRule);
        }

        List<GroupedOtherAttributeWithRule> groupedOtherAttributeWithRules =
                groupOtherAttributeWithRules(otherAttributeWithRules);

        result.setOtherAttrs(groupedOtherAttributeWithRules);
    }


    protected abstract List<CategoryAttribute> getCategoryAttributes(Long categoryId);


    /**
     * 如果属性不应该属于任何归组了, 那么就放到这个归组中
     *
     * @return fallback group name
     */
    protected abstract String fallbackGroup();

    /**
     * 获取本执行器要执行的规则列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        List<CategoryAttribute> categoryAttributes = getCategoryAttributes(data.getCategoryId());
        if (CollectionUtils.isEmpty(categoryAttributes)) {
            return Lists.newArrayList();
        }
        //从类目属性构建所有的属性规则
        List<AttributeRule> attributeRules =
                AttributeRuleBuilder.buildAttributeRuleFromCategoryAttributes(categoryAttributes);

        //排除用户选择作为销售属性的key
        Set<String> skuAttrKeys = data.getSkuAttrKeys();

        List<Object> result = Lists.newArrayListWithCapacity(attributeRules.size());
        for (AttributeRule attributeRule : attributeRules) {
            String attrKey = attributeRule.getAttrKey();
            if (!skuAttrKeys.contains(attrKey)) { //不是sku属性
                result.add(attributeRule);
            }
        }
        return result;
    }

    /**
     * 将非sku属性按照group进行归组
     *
     * @param otherAttributeWithRules 待归组的非sku属性列表
     * @return 归组好的非sku属性
     */
    private List<GroupedOtherAttributeWithRule> groupOtherAttributeWithRules(
            List<OtherAttributeWithRule> otherAttributeWithRules) {

        Multimap<String, OtherAttributeWithRule> byGroup = Multimaps.index(otherAttributeWithRules,
                new Function<OtherAttributeWithRule, String>() {
                    @Override
                    public String apply(OtherAttributeWithRule otherAttributeWithRule) {
                        return otherAttributeWithRule.getAttributeRule().getGroup();
                    }
                });
        List<GroupedOtherAttributeWithRule> result = Lists.newArrayList();
        for (String group : byGroup.keySet()) {
            GroupedOtherAttributeWithRule groupedRule = new GroupedOtherAttributeWithRule();
            groupedRule.setGroup(group);

            List<OtherAttributeWithRule> rules = Lists.newArrayList();
            rules.addAll(byGroup.get(group));
            groupedRule.setOtherAttributeWithRules(rules);
            result.add(groupedRule);
        }
        return result;
    }
}
