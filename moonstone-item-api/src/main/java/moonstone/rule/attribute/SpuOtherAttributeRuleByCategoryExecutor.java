/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.PreservedGroup;
import moonstone.cache.BackCategoryCacher;
import moonstone.category.model.BackCategory;
import moonstone.category.model.CategoryAttribute;
import moonstone.component.attribute.CategoryAttributeNoCacher;
import moonstone.rule.dto.BaseInput;
import moonstone.spu.dto.FullSpu;

import java.util.Collections;
import java.util.List;

/**
 * 本规则校验spu的非销售属性
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-07
 */
@Slf4j
public class SpuOtherAttributeRuleByCategoryExecutor extends OtherAttributeRuleByCategoryExecutor {
    private final BackCategoryCacher backCategoryCacher;

    private final CategoryAttributeNoCacher categoryAttributeNoCacher;

    public SpuOtherAttributeRuleByCategoryExecutor(BackCategoryCacher backCategoryCacher,
                                                   CategoryAttributeNoCacher categoryAttributeNoCacher) {
        this.backCategoryCacher = backCategoryCacher;
        this.categoryAttributeNoCacher = categoryAttributeNoCacher;
    }

    @Override
    protected List<CategoryAttribute> getCategoryAttributes(Long categoryId) {
        try {
            BackCategory backCategory = backCategoryCacher.findBackCategoryById(categoryId);
            if(backCategory.getStatus()<0){
                log.warn("back category(id={}) is deleted logically", categoryId);
                return Collections.emptyList();
            }
        } catch (Exception e) { //may be not found, treat as be delete logically
            log.error("failed to find back category (id={}), cause:{}", categoryId, e.getMessage());
            return Collections.emptyList();
        }
        return categoryAttributeNoCacher.findCategoryAttributeByCategoryId(categoryId);
    }

    /**
     * 如果属性不应该属于任何归组了, 那么就放到这个归组中
     *
     * @return fallback group name
     */
    @Override
    protected String fallbackGroup() {
        return PreservedGroup.SPU.name();
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {

        if(!(input instanceof FullSpu)){
            return false;
        }
        Long categoryId = input.getCategoryId();
        try {
            BackCategory backCategory = backCategoryCacher.findBackCategoryById(categoryId);
            if(backCategory.getStatus()<0){
                log.warn("back category(id={}) is deleted logically", categoryId);
                return false;
            }
        } catch (Exception e) { //may be not found, treat as be delete logically
            log.error("failed to find back category (id={}), cause:{}", categoryId, e.getMessage());
            return false;
        }
        return true;
    }
}
