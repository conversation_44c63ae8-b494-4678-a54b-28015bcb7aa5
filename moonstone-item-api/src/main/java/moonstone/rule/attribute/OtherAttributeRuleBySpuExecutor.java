/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.Function;
import com.google.common.base.Objects;
import com.google.common.base.Supplier;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.google.common.collect.Tables;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.PreservedGroup;
import moonstone.cache.CategoryAttributeCacher;
import moonstone.cache.SpuCacher;
import moonstone.category.model.CategoryAttribute;
import moonstone.common.exception.InvalidException;
import moonstone.component.dto.attribute.AttributeRule;
import moonstone.item.dto.FullItem;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import moonstone.spu.dto.FullSpu;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * spu级别定义的非销售属性规则, 只对商品生效
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-28
 */
@Slf4j
public class OtherAttributeRuleBySpuExecutor extends RuleExecutor {

    private final SpuCacher spuCacher;
    private final CategoryAttributeCacher categoryAttributeCacher;

    public OtherAttributeRuleBySpuExecutor(SpuCacher spuCacher,
                                           CategoryAttributeCacher categoryAttributeCacher) {
        this.spuCacher = spuCacher;
        this.categoryAttributeCacher = categoryAttributeCacher;
    }

    /**
     * 规则引擎在处理数据输入时会调用这个方法 (例如用户发布或者更新商品)
     * <p/>
     * spu级别的非销售属性必须在商品中出现, 且值必须相等
     *
     * @param input 用户提交的数据
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException {
        List<?> rules = getRules(input);
        List<GroupedOtherAttribute> groupedOtherAttributesOfItem = input.getGroupedOtherAttributes();

        //用group和attrKey来找AttrVal
        Table<String, String, String> table = Tables.newCustomTable(
                Maps.newHashMap(),
                new Supplier<Map<String, String>>() {
                    public Map<String, String> get() {
                        return Maps.newHashMap();
                    }
                });

        //构建table, 根据用户输入的数据
        for (GroupedOtherAttribute groupedOtherAttribute : groupedOtherAttributesOfItem) {
            String group = groupedOtherAttribute.getGroup();
            for (OtherAttribute otherAttribute : groupedOtherAttribute.getOtherAttributes()) {
                String attrKey = otherAttribute.getAttrKey();
                String attrVal = otherAttribute.getAttrVal();
                table.put(group, attrKey, attrVal);
            }
        }

        //用rule来校验数据
        for (Object rule : rules) {
            AttributeRule attributeRule = (AttributeRule) rule;
            String group = attributeRule.getGroup();
            String attrKey = attributeRule.getAttrKey();
            String attrVal = attributeRule.getAttrVals().get(0);

            if (!table.containsRow(group)) {
                log.error("other attribute group({}) missing", group);
                throw new InvalidException("attr.group({0}).missing", group);
            }

            if (!table.contains(group, attrKey)) {
                log.error("other attrKey({}) missing", attrKey);
                throw new InvalidException("other.attrKey({0}).missing", attrKey);
            }

            final String actualVal = table.get(group, attrKey);
            if (!Objects.equal(actualVal, attrVal)) {
                log.error("invalid other attribute(key={}, val={}), expect val={}",
                        attrKey, actualVal, attrVal);
                throw new InvalidException("other.attr(key={0},val={1}).invalid", attrKey, actualVal);
            }
        }
    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品),
     * <p/>
     * <p/>
     * 用spu级别的非销售属性覆盖用户的同名属性,并补全缺失的属性, 而非同名属性且不在系统预设组中的当作用户自定义属性处理
     *
     * @param input  用户提交的数据
     * @param result 经过处理后的数据,可能需要分步处理
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput result) {
        List<?> rules = getRules(input);
        List<GroupedOtherAttribute> groupedOtherAttributesOfItem = input.getGroupedOtherAttributes();

        //用group和attrKey来找AttrVal
        Table<String, String, String> table = Tables.newCustomTable(
                Maps.newLinkedHashMap(),
                new Supplier<Map<String, String>>() {
                    public Map<String, String> get() {
                        return Maps.newLinkedHashMap();
                    }
                });

        //构建table, 根据spu规则
        for (Object rule : rules) {
            AttributeRule attributeRule = (AttributeRule) rule;
            table.put(attributeRule.getGroup(), attributeRule.getAttrKey(), attributeRule.getAttrVals().get(0));
        }


        for (GroupedOtherAttribute groupedOtherAttribute : groupedOtherAttributesOfItem) {
            String group = groupedOtherAttribute.getGroup();

            for (OtherAttribute otherAttribute : groupedOtherAttribute.getOtherAttributes()) {
                final String attrKey = otherAttribute.getAttrKey();
                final String attrVal = otherAttribute.getAttrVal();
                if (table.containsRow(group)) {
                    if (!table.contains(group, attrKey)) { //补全缺失的属性,否则优先采用spu定义的属性值
                        table.put(group, attrKey, attrVal);
                    }
                } else { // 如果不在spu定义的组中,且不在系统预设组中,并且不是basic组,视为用户自定义组
                    final String fallBackGroup = group.equalsIgnoreCase(PreservedGroup.BASIC.name()) ? group :
                            PreservedGroup.USER_DEFINED.name();
                    table.put(fallBackGroup, attrKey, attrVal);
                }
            }

        }

        List<GroupedOtherAttribute> groupedOtherAttributes = Lists.newArrayList();
        //重新收集整理属性组
        for (String group : table.rowKeySet()) {
            GroupedOtherAttribute groupedOtherAttribute = new GroupedOtherAttribute();
            groupedOtherAttribute.setGroup(group);
            List<OtherAttribute> otherAttributes = Lists.newArrayList();
            final Map<String, String> row = table.row(group);
            for (String attrKey : row.keySet()) {
                OtherAttribute otherAttribute = new OtherAttribute();
                otherAttribute.setAttrKey(attrKey);
                otherAttribute.setAttrVal(row.get(attrKey));
                otherAttribute.setReadOnlyBySeller(Boolean.TRUE);
                otherAttribute.setGroup(group);
                otherAttributes.add(otherAttribute);

            }
            groupedOtherAttribute.setOtherAttributes(otherAttributes);
            groupedOtherAttributes.add(groupedOtherAttribute);
        }
        input.setGroupedOtherAttributes(groupedOtherAttributes);
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {
        //只支持商品
        if (!(input instanceof FullItem)) {
            return false;
        }
        //如果spu id不存在, 则本规则也不适用
        if (input.getSpuId() == null) {
            return false;
        }

        //如果对应spu的状态不正常, 则本规则也不适用
        FullSpu fullSpu = spuCacher.findFullSpuById(input.getSpuId());
        return fullSpu.getSpu().getStatus() >= 0;
    }

    /**
     * 获取本执行器要执行的规则列表, 获取spu定义的非销售属性
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        final Long spuId = data.getSpuId();
        FullSpu fullSpu = spuCacher.findFullSpuById(spuId);
        List<GroupedOtherAttribute> groupedOtherAttributes = fullSpu.getGroupedOtherAttributes();
        //如果spu没有定义非销售属性, 直接返回空列表
        if (CollectionUtils.isEmpty(groupedOtherAttributes)) {
            return Collections.emptyList();
        }

        // 获取类目属性
        List<CategoryAttribute> categoryAttributes = categoryAttributeCacher.findByCategoryId(fullSpu.getCategoryId());
        List<String> availableAttrKeys = Lists.transform(categoryAttributes, new Function<CategoryAttribute, String>() {
            @Nullable
            @Override
            public String apply(@Nullable CategoryAttribute input) {
                return input.getAttrKey();
            }
        });

        List<Object> otherAttributeRules = Lists.newArrayList();
        for (GroupedOtherAttribute groupedOtherAttribute : groupedOtherAttributes) {
            for (OtherAttribute otherAttribute : groupedOtherAttribute.getOtherAttributes()) {
                AttributeRule attributeRule = new AttributeRule();
                // 原来的类目属性被删除, 则加入SPU分组
                String group = availableAttrKeys.contains(otherAttribute.getAttrKey()) ? groupedOtherAttribute.getGroup() : PreservedGroup.SPU.name();
                attributeRule.setGroup(group);
                attributeRule.setAttrKey(otherAttribute.getAttrKey());
                //spu属性规则一个属性只允许一个值
                attributeRule.setAttrVals(Lists.newArrayList(otherAttribute.getAttrVal()));
                otherAttributeRules.add(attributeRule);
            }
        }
        return otherAttributeRules;
    }
}
