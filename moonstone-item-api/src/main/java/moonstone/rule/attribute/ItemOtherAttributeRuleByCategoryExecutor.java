/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.PreservedGroup;
import moonstone.cache.BackCategoryCacher;
import moonstone.cache.CategoryAttributeCacher;
import moonstone.cache.SpuCacher;
import moonstone.category.model.BackCategory;
import moonstone.category.model.CategoryAttribute;
import moonstone.component.dto.attribute.AttributeRule;
import moonstone.item.dto.FullItem;
import moonstone.rule.dto.BaseInput;
import moonstone.spu.dto.FullSpu;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 本规则校验商品的非销售属性, 走缓存来装载属性
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-07
 */
@Slf4j
public class ItemOtherAttributeRuleByCategoryExecutor extends OtherAttributeRuleByCategoryExecutor {
    private final BackCategoryCacher backCategoryCacher;
    private final SpuCacher spuCacher;
    private final CategoryAttributeCacher categoryAttributeCacher;

    public ItemOtherAttributeRuleByCategoryExecutor(BackCategoryCacher backCategoryCacher,
                                                    SpuCacher spuCacher,
                                                    CategoryAttributeCacher categoryAttributeCacher) {
        this.backCategoryCacher = backCategoryCacher;
        this.spuCacher = spuCacher;
        this.categoryAttributeCacher = categoryAttributeCacher;
    }

    @Override
    protected List<CategoryAttribute> getCategoryAttributes(Long categoryId) {
        return categoryAttributeCacher.findByCategoryId(categoryId);
    }

    /**
     * 如果属性不应该属于任何归组了, 那么就放到这个归组中
     *
     * @return fallback group name
     */
    @Override
    protected String fallbackGroup() {
        return PreservedGroup.USER_DEFINED.name();
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {
        if(!(input instanceof FullItem)){
            return false;
        }
        Long categoryId = input.getCategoryId();
        try {
            BackCategory backCategory = backCategoryCacher.findBackCategoryById(categoryId);
            if(backCategory.getStatus()<0){
                log.warn("back category(id={}) is deleted logically", categoryId);
                return false;
            }
        } catch (Exception e) { //may be not found, treat as be delete logically
            log.error("failed to find back category (id={}), cause:{}", categoryId, e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 获取本执行器要执行的规则列表
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        List<AttributeRule> rules = (List<AttributeRule>) super.getRules(data);
        if (data.getSpuId() == null) {
            return rules;
        }

        Map<String, AttributeRule> ruleByAttrKey = Maps.uniqueIndex(rules, new Function<AttributeRule, String>() {
            @Override
            public String apply(AttributeRule input) {
                return input.getAttrKey();
            }
        });

        // 继承来自 SPU 的运营定义属性
        FullSpu spu = spuCacher.findFullSpuById(data.getSpuId());
        List<GroupedOtherAttribute> groupedOtherAttributes = spu.getGroupedOtherAttributes();
        for (GroupedOtherAttribute groupedOtherAttribute : groupedOtherAttributes) {
            for (OtherAttribute otherAttribute : groupedOtherAttribute.getOtherAttributes()) {
                // SPU 中继承自类目的属性
                if (ruleByAttrKey.containsKey(otherAttribute.getAttrKey())) {
                    // 尝试将SPU中定义的属性值加入规则
                    String attrVal = otherAttribute.getAttrVal();
                    List<String> attrVals = ruleByAttrKey.get(otherAttribute.getAttrKey()).getAttrVals();
                    if (StringUtils.hasText(attrVal) && !attrVals.contains(attrVal)) {
                        attrVals.add(otherAttribute.getAttrVal());
                    }
                    continue;
                }

                // SPU 中自定义的属性, 插入规则中
                AttributeRule rule = new AttributeRule();
                rule.setGroup(PreservedGroup.SPU.name());
                rule.setAttrKey(otherAttribute.getAttrKey());
                rule.setAttrVals(Lists.newArrayList(otherAttribute.getAttrVal()));
                rule.setAttrMetas(Collections.emptyMap());
                rules.add(rule);
            }
        }
        return rules;
    }
}
