/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import moonstone.attribute.dto.AttributeMetaKey;
import moonstone.category.model.CategoryAttribute;
import moonstone.component.dto.attribute.AttributeRule;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-25
 */
public class AttributeRuleBuilder {

    /**
     * 根据categoryAttributes构建sku属性规则
     *
     * @param categoryAttributes 类目属性规则
     * @return sku属性规则
     */
    public static List<AttributeRule> buildSkuAttributeRule(List<CategoryAttribute> categoryAttributes) {
        if(CollectionUtils.isEmpty(categoryAttributes)){
            return Collections.emptyList();
        }
        List<AttributeRule> result = Lists.newArrayList();
        for (CategoryAttribute categoryAttribute : categoryAttributes) {
            if (!CollectionUtils.isEmpty(categoryAttribute.getAttrMetas())) {
                String metaValue = categoryAttribute.getAttrMetas().get(AttributeMetaKey.SKU_CANDIDATE);
                if (Boolean.valueOf(metaValue)) { //说明该属性可以作为sku候选属性
                    AttributeRule attributeRule = makeAttributeRule(categoryAttribute);
                    result.add(attributeRule);
                }
            }
        }
        return result;
    }

    private static AttributeRule makeAttributeRule(CategoryAttribute categoryAttribute) {
        AttributeRule attributeRule = new AttributeRule();
        attributeRule.setAttrKey(categoryAttribute.getAttrKey());
        attributeRule.setAttrMetas(
                MoreObjects.firstNonNull(categoryAttribute.getAttrMetas(),
                        Maps.newLinkedHashMap()));
        attributeRule.setGroup(categoryAttribute.getGroup());

        // don't use reference value here!
        // use copy list for dirty code
        if (CollectionUtils.isEmpty(categoryAttribute.getAttrVals())) {
            attributeRule.setAttrVals(new ArrayList<>());
        } else {
            attributeRule.setAttrVals(new ArrayList<>(categoryAttribute.getAttrVals()));
        }
        return attributeRule;
    }

    /**
     * 将类目属性转成对应的属性规则
     *
     * @param categoryAttributes 类目属性列表
     * @return 对应的属性规则列表
     */
    public static List<AttributeRule> buildAttributeRuleFromCategoryAttributes(List<CategoryAttribute> categoryAttributes) {
        //如果没有定义类目属性, 则返回空列表
        if(CollectionUtils.isEmpty(categoryAttributes)){
            return Collections.emptyList();
        }
        List<AttributeRule> result = Lists.newArrayListWithCapacity(categoryAttributes.size());
        for (CategoryAttribute categoryAttribute : categoryAttributes) {
            AttributeRule attributeRule = makeAttributeRule(categoryAttribute);
            result.add(attributeRule);
        }
        return result;
    }


}
