/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule.attribute;

import com.google.common.base.Supplier;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cache.SpuCacher;
import moonstone.common.exception.InvalidException;
import moonstone.item.dto.FullItem;
import moonstone.rule.RuleExecutor;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.BaseOutput;
import moonstone.spu.dto.FullSpu;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * spu级别定义的销售属性规则
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-28
 */
@Slf4j
public class SkuAttributeRuleBySpuExecutor extends RuleExecutor {

    private final SpuCacher spuCacher;

    public SkuAttributeRuleBySpuExecutor(SpuCacher spuCacher) {
        this.spuCacher = spuCacher;
    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户发布或者更新商品)
     * 商品定义的销售属性(GroupedSkuAttribute)必须在spu定义的销售属性范围(GroupedSkuAttribute)内
     *
     * @param input 用户提交的数据
     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
     */
    @Override
    public void doHandleInboundData(BaseInput input, BaseOutput output) throws InvalidException {
        List<GroupedSkuAttribute> rules = (List<GroupedSkuAttribute>) getRules(input);
        if (CollectionUtils.isEmpty(rules)) { //spu上没有定义规则, 略过检查
            return;
        }
        if (CollectionUtils.isEmpty(input.getGroupedSkuAttributes())) {
            log.error("no sku attributes specified");
            throw new InvalidException("sku.attr.missing");
        }
        Multimap<String, String> byAttrKey = HashMultimap.create();
        for (GroupedSkuAttribute rule : rules) {
            String attrKey = rule.getAttrKey();
            for (SkuAttribute skuAttribute : rule.getSkuAttributes()) {
                byAttrKey.put(attrKey, skuAttribute.getAttrVal());
            }
        }

        //商品定义的销售属性的key val必须在spu定义的范围内
        for (GroupedSkuAttribute groupedSkuAttribute : input.getGroupedSkuAttributes()) {
            String attrKey = groupedSkuAttribute.getAttrKey();
            Collection<String> allowedValues = byAttrKey.get(attrKey);
            for (SkuAttribute skuAttribute : groupedSkuAttribute.getSkuAttributes()) {
                String attrVal = skuAttribute.getAttrVal();
                if (!allowedValues.contains(attrVal)) {
                    log.error("invalid sku attr value:{} of attr key:{}", attrVal, attrKey);
                    throw new InvalidException("sku.attr(key={0},val={1}).invalid", attrKey, attrVal);
                }
            }
        }
    }

    /**
     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品), 如果spu定义了销售属性, 销售属性必须和spu定义的销售属性一致
     * <p/>
     *
     * @param input  用户提交的数据
     * @param output 经过处理后的数据,可能需要分步处理
     */
    @Override
    public void doHandleOutboundData(BaseInput input, BaseOutput output) {
        List<GroupedSkuAttribute> groupedSkuAttributesOfSpu = (List<GroupedSkuAttribute>) getRules(input);
        if (!CollectionUtils.isEmpty(groupedSkuAttributesOfSpu)) {
            List<GroupedSkuAttribute> groupedSkuAttributesOfItem = input.getGroupedSkuAttributes();
            //用item级别的groupedSkuAttribute中的image相关的信息覆盖spu级别的groupedSkuAttribute中对应的image相关信息
            if (!CollectionUtils.isEmpty(groupedSkuAttributesOfItem)) {
                //用attrKey和attrVal来找SkuAttribute
                Table<String, String, SkuAttribute> table = Tables.newCustomTable(
                        Maps.newHashMap(),
                        new Supplier<Map<String, SkuAttribute>>() {
                            public Map<String, SkuAttribute> get() {
                                return Maps.newHashMap();
                            }
                        });
                for (GroupedSkuAttribute groupedSkuAttribute : groupedSkuAttributesOfItem) {
                    String attrKey = groupedSkuAttribute.getAttrKey();
                    for (SkuAttribute skuAttribute : groupedSkuAttribute.getSkuAttributes()) {
                        table.put(attrKey, skuAttribute.getAttrVal(),skuAttribute);
                    }
                }


                for (GroupedSkuAttribute groupedSkuAttribute : groupedSkuAttributesOfSpu) {
                    String attrKey = groupedSkuAttribute.getAttrKey();
                    for (SkuAttribute skuAttribute : groupedSkuAttribute.getSkuAttributes()) {
                        SkuAttribute skuAttributeOfItem = table.get(attrKey, skuAttribute.getAttrVal());
                        skuAttribute.setShowImage(skuAttributeOfItem.getShowImage());
                        if(StringUtils.hasText(skuAttributeOfItem.getImage())){
                            skuAttribute.setImage(skuAttributeOfItem.getImage());
                        }
                        if(StringUtils.hasText(skuAttributeOfItem.getThumbnail())){
                            skuAttribute.setThumbnail(skuAttributeOfItem.getThumbnail());
                        }
                    }
                }

            }
            input.setGroupedSkuAttributes(groupedSkuAttributesOfSpu);

        } else {
            input.setGroupedSkuAttributes(Collections.emptyList());
        }
    }

    /**
     * 判断数据是否适用本规则
     *
     * @param input 数据
     * @return 是否适用本规则
     */
    @Override
    public boolean support(BaseInput input) {
        //只支持商品
        if (!(input instanceof FullItem)) {
            return false;
        }
        //如果spu id不存在, 则本规则也不适用
        if (input.getSpuId() == null) {
            return false;
        }

        //如果对应spu的状态不正常, 则本规则也不适用
        FullSpu fullSpu = spuCacher.findFullSpuById(input.getSpuId());
        return fullSpu.getSpu().getStatus() >= 0;
    }

    /**
     * 获取spu上定义的销售属性规则, 这里直接返回spu上定义的销售属性
     *
     * @param data 待处理的数据
     * @return 要校验的规则列表
     */
    @Override
    protected List<?> getRules(BaseInput data) {
        FullSpu spu = spuCacher.findFullSpuById(data.getSpuId());
        if(spu.getSpu().getStatus()<0){
            log.warn("spu(id={}) has been deleted logically", data.getSpuId());
            return Collections.emptyList();
        }
        return spu.getGroupedSkuAttributes();
    }
}
