package moonstone.membership.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author:g
 * Created on 24/7/18.
 */

@Data
public class MembershipPrice implements Serializable{
    private static final long serialVersionUID = 8256801550026749883L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * sku ID
     */
    private Long skuId;

    /**
     * 会员价
     */
    private Integer membershipPrice;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 状态
     */
    private Integer status;
}
