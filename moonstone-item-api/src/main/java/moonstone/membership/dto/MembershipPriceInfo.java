package moonstone.membership.dto;

import lombok.Data;
import moonstone.item.model.Sku;


@Data
public class MembershipPriceInfo {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 会员价
     */
    private Integer price;

    /**
     * itemId
     */
    private Long itemId;

    /**
     * 店铺名称
     */
    private String itemName;

    /**
     * 是否有规格
     */
    private boolean hasChildren;

    /**
     * 规格字符串
     */
    private String attrs;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku
     */
    private Sku sku;

    /**
     * 微信小程序url
     */
    private String wxaUrl;
}
