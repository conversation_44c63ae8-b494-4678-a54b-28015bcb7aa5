/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.attribute.dto;

import com.google.common.base.Objects;

/**
 * 系统保留属性组名
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-23
 */
public enum  PreservedGroup {
    /**
     * 默认属性组, 指的是类目下的属性, 如果没有归组就属于这个组, 表示运营定义的默认类目属性分组
     */
    DEFAULT,

    /***
     * spu下运营定义默认属性分组, 指的是运营在spu下的自定义属性组
     */
    SPU,

    /**
     * sku属性组
     */
    SKU,

    /**
     * 用户自定义属性组, 商品属性组专用
     */
    USER_DEFINED,

    /**
     * 基本属性组, 所有商品都拥有的属性
     */
    BASIC,

    /**
     * 服务信息组
     */
    SERVICE;

    public static boolean contains(String group){
        for (PreservedGroup preservedGroup : PreservedGroup.values()) {
            if(Objects.equal(preservedGroup.name(), group)){
                return true;
            }
        }
        return false;
    }
}
