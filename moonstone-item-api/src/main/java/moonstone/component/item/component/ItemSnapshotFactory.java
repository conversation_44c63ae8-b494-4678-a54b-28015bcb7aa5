package moonstone.component.item.component;

import com.google.common.base.Strings;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.common.Digestors;
import moonstone.item.dto.FullItem;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.ItemSnapshot;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemSnapshotReadService;
import moonstone.item.service.ItemSnapshotWriteService;
import org.springframework.stereotype.Component;

/**
 * Author:cp
 * Created on 5/31/16.
 */
@Slf4j
@Component
public record ItemSnapshotFactory(ItemReadService itemReadService
        , ItemSnapshotReadService itemSnapshotReadService
        , ItemSnapshotWriteService itemSnapshotWriteService) {

    /**
     * 获取商品当前的快照id,如果已存在则直接返回,否则先创建快照再返回
     *
     * @param itemId 商品id
     * @return 快照id
     */
    public Response<Long> getItemSnapshotId(Long itemId) {
        Response<FullItem> fullItemInfoResp = itemReadService.findFullInfoByItemId(itemId);
        if (!fullItemInfoResp.isSuccess()) {
            log.error("fail to find full item info by itemId:{},cause:{}", itemId, fullItemInfoResp.getError());
            return Response.fail(fullItemInfoResp.getError());
        }

        FullItem fullItem = fullItemInfoResp.getResult();
        Item item = fullItem.getItem();

        //定时改图
        itemReadService.processItemForActivity(item, fullItem.getItemDetail());
        ItemAttribute itemAttribute = new ItemAttribute();
        itemAttribute.setSkuAttrs(fullItem.getGroupedSkuAttributes());
        itemAttribute.setOtherAttrs(fullItem.getGroupedOtherAttributes());
        String itemInfoMd5 = Digestors.itemDigest(item, fullItem.getItemDetail(), itemAttribute);

        if (!Strings.isNullOrEmpty(itemInfoMd5)) {
            Response<ItemSnapshot> itemSnapshotResp = itemSnapshotReadService.findByItemIdAndItemInfoMd5(item.getId(), item.getItemInfoMd5());
            if (!itemSnapshotResp.isSuccess()) {
                log.error("fail to find item snapshot by itemId={} and md5={},cause:{}", item.getId(), item.getItemInfoMd5(), itemSnapshotResp.getError());
                return Response.fail(itemSnapshotResp.getError());
            }

            ItemSnapshot itemSnapshot = itemSnapshotResp.getResult();
            if (itemSnapshot != null) {
                return Response.ok(itemSnapshot.getId());
            }
        }

        Response<Long> createItemSnapshotResp = itemSnapshotWriteService.create(item, fullItem.getItemDetail(), itemAttribute);
        if (!createItemSnapshotResp.isSuccess()) {
            log.error("fail to create item snapshot with item:{},itemDetail:{},itemAttribute:{},cause:{}", item, fullItem.getItemDetail(), itemAttribute, createItemSnapshotResp.getError());
            return Response.fail(createItemSnapshotResp.getError());
        }
        return createItemSnapshotResp;
    }

}
