/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.component.item.component;

import com.google.common.base.Throwables;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SpuCacher;
import moonstone.common.enums.BondedType;
import moonstone.common.exception.InvalidException;
import moonstone.item.dto.FullItem;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.emu.ItemSellOutStatusEnum;
import moonstone.item.emu.ThirdPartyItemType;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemWriteService;
import moonstone.rule.RuleEngine;
import moonstone.shop.model.Shop;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.model.SpuDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Objects;

/**
 * 创建或者编辑商品
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-24
 */
@Component
@Slf4j
public class ItemWriter {

    @Autowired
    private ItemWriteService itemWriteService;

    @Autowired
    private ItemReadService itemReadService;

    @Autowired
    private RuleEngine ruleEngine;

    @Autowired
    private SpuCacher spuCacher;

    @Autowired
    private ItemCacheHolder itemCacheHolder;


    public Response<Long> copy(Item item, Shop shop) {
        return itemWriteService.copy(item, shop);
    }

    /**
     * 创建商品信息
     *
     * @param fullItem 待创建的商品
     * @return 新建商品的id
     */
    public Response<Long> create(FullItem fullItem) {

        Item item = fullItem.getItem();
        if (Objects.equals(item.getIsBonded(), BondedType.CROSS_BORDER_BONDED.getCode()) && !Objects.equals(item.getIsThirdPartyItem(), 1)) {
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                skuWithCustom.getSkuCustom().setCustomTaxHolder(2);
                item.setIsThirdPartyItem(ThirdPartyItemType.MANUAL_PARTY_ITEM.getType());
            }
        }
        if (item.getSpuId() != null) {
            try {
                FullSpu spu = spuCacher.findFullSpuById(item.getSpuId());
                SpuDetail spuDetail = spu.getSpuDetail();
                item.setCategoryId(spu.getCategoryId());
                ItemDetail itemDetail = processItemDetailAgainstSpu(fullItem, spuDetail);
                fullItem.setItemDetail(itemDetail);

            } catch (Exception e) {
                log.error("failed to find spu(id={}), cause:{}", item.getSpuId(), Throwables.getStackTraceAsString(e));
                return Response.fail("spu.find.fail");
            }
        }

        if (validate(fullItem)) {
            return itemWriteService.create(fullItem);
        } else {
            return Response.fail("item.create.fail");
        }

    }

    /**
     * 如有必要, 将spuDetail的信息回填到itemDetail对应部分
     *
     * @param fullItem  原始数据
     * @param spuDetail spu详情
     * @return 处理过的商品详情信息
     */
    private ItemDetail processItemDetailAgainstSpu(FullItem fullItem, SpuDetail spuDetail) {
        ItemDetail itemDetail = fullItem.getItemDetail();
        if (itemDetail == null) {
            itemDetail = new ItemDetail();
        }

        if (CollectionUtils.isEmpty(itemDetail.getImages())) {
            itemDetail.setImages(spuDetail.getImages());
        }
        if (!StringUtils.hasText(itemDetail.getDetail())) {
            itemDetail.setDetail(spuDetail.getDetail());
        }
        if (itemDetail.getPacking() == null) {
            itemDetail.setPacking(spuDetail.getPacking());
        }
        if (!StringUtils.hasText(itemDetail.getService())) {
            itemDetail.setService(spuDetail.getService());
        }
        return itemDetail;
    }

    /**
     * 更新商品信息
     *
     * @param fullItem 所有商品信息
     * @return 是否更新成功
     */
    public Response<Boolean> update(FullItem fullItem) {
        if (validate(fullItem)) {
            return itemWriteService.update(fullItem);
        } else {
            return Response.fail("item.update.fail");
        }

    }

    private boolean validate(FullItem fullItem) throws InvalidException {
        try {
            ruleEngine.handleInboundData(fullItem, null);
            return true;
        } catch (Exception e) {
            log.error("failed to validate fullItem({}), cause:{}",
                    fullItem.getItem(), Throwables.getStackTraceAsString(e));
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            return false;
        }
    }

    /**
     * 变更商品的"售罄状态"
     *
     * @param shopId
     * @param itemId
     * @param sellOutStatus
     * @return
     */
    public boolean updateSellOutStatus(Long shopId, Long itemId, Integer sellOutStatus) {
        var item = itemReadService.findById(itemId).getResult();
        if (item == null) {
            throw new RuntimeException("商品信息不存在");
        }
        if (!Objects.equals(item.getShopId(), shopId)) {
            throw new RuntimeException("当前商品不属于该商家");
        }

        var status = ItemSellOutStatusEnum.parse(sellOutStatus);
        if (status == null) {
            throw new RuntimeException("不合法的状态值");
        }

        var result = itemWriteService.update(buildUpdateObject(item, status)).getResult();
        if (result) {
            itemCacheHolder.invalid(itemId);
        }
        return result;
    }

    private Item buildUpdateObject(Item item, ItemSellOutStatusEnum sellOutStatus) {
        var updateObject = new Item();
        updateObject.setId(item.getId());
        updateObject.setSellOutStatus(sellOutStatus.getCode());

        return updateObject;
    }
}
