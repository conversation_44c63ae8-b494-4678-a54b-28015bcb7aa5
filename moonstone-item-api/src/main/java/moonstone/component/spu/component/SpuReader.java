/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.component.spu.component;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.component.dto.spu.EditSpu;
import moonstone.rule.RuleEngine;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.service.SpuReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 有类目属性规则的时候, 需要用类目属性规则对spu进行校验
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-23
 */
@Component
@Slf4j
public class SpuReader {

    @RpcConsumer
    private SpuReadService spuReadService;

    @Autowired
    private RuleEngine ruleEngine;

    /**
     * 对要编辑的Spu信息, 用最新的属性规则进行校验, 商家通过spu发布商品也会使用这个接口
     *
     *
     * @param spuId spu id
     * @return  经过严格校验的Spu信息
     */
    public Response<EditSpu> findForEdit(Long spuId){
        Response<FullSpu> rFullSpu = spuReadService.findFullInfoBySpuId(spuId);
        if (!rFullSpu.isSuccess()) {
            log.error("failed to find spu(id={}), error code:{}", spuId, rFullSpu.getError());
            return Response.fail(rFullSpu.getError());
        }
        final FullSpu fullSpu = rFullSpu.getResult();

        try {
            EditSpu editSpu = new EditSpu();
            editSpu.setSpu(fullSpu.getSpu());
            editSpu.setSpuDetail(fullSpu.getSpuDetail());
            ruleEngine.handleOutboundData(fullSpu, editSpu);
            return Response.ok(editSpu);
        } catch (Exception e) {
            log.error("failed to find spu(id={}) for edit , cause:{}",
                    spuId, Throwables.getStackTraceAsString(e));
            return Response.fail("spu.find.fail");
        }
    }
}
