/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.brand.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.utils.ImageUrlHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌信息
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
@EqualsAndHashCode(of = {"uniqueName"})
@ToString(of = {"name", "uniqueName"})
public class Brand implements Serializable {

    private static final long serialVersionUID = -3615566439002026797L;

    /**
     * ID
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 唯一索引名, 一般为 name 的小写化
     */
    @Getter
    @Setter
    private String uniqueName;

    /**
     * 英文名
     */
    @Getter
    @Setter
    private String enName;

    /**
     * 首字母
     */
    @Getter
    @Setter
    private String enCap;

    /**
     * 品牌 Logo
     */
    private String logo;

    /**
     * 状态
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 描述
     */
    @Getter
    @Setter
    private String description;

    /**
     * 外部品牌 id
     */
    @Getter
    @Setter
    private String outerId;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    @JsonIgnore
    public String getLogo() {
        return ImageUrlHandler.simplify(this.logo);
    }

    @JsonProperty("logo")
    public String getLogo_() {
        return this.logo;
    }

    @JsonSetter
    public void setLogo(String logo) {
        this.logo = ImageUrlHandler.complete(logo);
    }

}
