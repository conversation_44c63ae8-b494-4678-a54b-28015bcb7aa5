/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SkuCacheHolder {
    private LoadingCache<Long, Sku> skuCachedById;

    private LoadingCache<Long, List<Sku>> skuCachedByItemId;

    @Autowired
    private SkuReadService skuReadService;

    @Value("${cache.duration.in.minutes: 60}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.skuCachedById = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(skuId -> {
                    Response<Sku> rSku = skuReadService.findSkuById(skuId);
                    if (!rSku.isSuccess()) {
                        log.error("failed to find sku(id={}), error code:{}",
                                skuId, rSku.getError());
                        throw new ServiceException("find sku fail,error code: " + rSku.getError());
                    }
                    return rSku.getResult();
                });

        this.skuCachedByItemId = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(1000)
                .build(itemId -> {
                    Response<List<Sku>> findResp = skuReadService.findSkusByItemId(itemId);
                    if (!findResp.isSuccess()) {
                        log.error("fail to find skus by itemId:{},cause:{}",
                                itemId, findResp.getError());
                        throw new ServiceException("skus.find.by.item.id.fail");
                    }
                    return findResp.getResult();
                });
    }

    /**
     * 根据id查找sku的信息
     *
     * @param skuId sku id
     * @return 对应sku信息
     */
    public Sku findSkuById(Long skuId) {
        return skuCachedById.get(skuId);
    }

    /**
     * 根据商品id查找其所有sku
     *
     * @param itemId 商品id
     * @return sku信息
     */
    public List<Sku> findSkusByItemId(Long itemId) {
        return skuCachedByItemId.get(itemId);
    }

    public void invalidate(Long itemId) {
        try {
            for (Sku sku : Objects.requireNonNull(skuCachedByItemId.get(itemId))) {
                skuCachedById.refresh(sku.getId());
            }
        } finally {
            skuCachedByItemId.refresh(itemId);
        }
    }
}
