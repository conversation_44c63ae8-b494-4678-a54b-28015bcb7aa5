/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.service.SpuReadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 针对spu的缓存
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-28
 */
@Component
@Slf4j
public class SpuCacher {

    private LoadingCache<Long, FullSpu> spuCacher;

    @RpcConsumer
    private SpuReadService spuReadService;

    @Value("${cache.duration.in.minutes: 60}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.spuCacher = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .build(spuId -> {
                    Response<FullSpu> rFullSpu = spuReadService.findFullInfoBySpuId(spuId);
                    if (!rFullSpu.isSuccess()) {
                        log.error("failed to find full spu(id={}), error code:{}",
                                spuId, rFullSpu.getError());
                        throw new ServiceException("find full spu fail,error code: " + rFullSpu.getError());
                    }
                    return rFullSpu.getResult();
                });
    }

    /**
     * 根据id查找spu的所有信息
     *
     * @param spuId  spu id
     * @return  对应spu的所有信息, 包括详情, 属性啥的
     */
    public FullSpu findFullSpuById(Long spuId){
        return spuCacher.get(spuId);
    }
}

