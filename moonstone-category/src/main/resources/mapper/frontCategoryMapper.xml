<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="FrontCategory">

    <resultMap id="FrontCategoryMap" type="FrontCategory">
        <id column="id" property="id"/>
        <result column="pid" property="pid"/>
        <result column="name" property="name"/>
        <result column="level" property="level"/>
        <result column="has_children" property="hasChildren"/>
        <result column="logo" property="logo"/>
        <result column="outer_id" property="outerId"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_front_categories
    </sql>

    <sql id="cols_all">
      id, <include refid="cols_exclude_id" />
    </sql>

    <sql id="cols_exclude_id">
      pid, name,level, has_children, logo, outer_id, created_at, updated_at
    </sql>

    <sql id="vals">
        #{pid}, #{name},#{level},#{hasChildren}, #{logo}, #{outerId}, now(), now()
    </sql>

    <insert id="create" parameterType="FrontCategory" keyProperty="id" useGeneratedKeys="true">
      INSERT INTO
      <include refid="tb" />
      (<include refid="cols_exclude_id" />)
      VALUES
      (<include refid="vals" />)
    </insert>

    <delete id="delete" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="findById" parameterType="long" resultMap="FrontCategoryMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="FrontCategoryMap">
        SELECT id,
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach collection="list" separator="," open="("
                 close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="findByPid" parameterType="long" resultMap="FrontCategoryMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE pid = #{pid}
    </select>

<!--    <select id="findInPids" parameterType="list" resultMap="FrontCategoryMap">
        SELECT <include refid="cols_all"/>
        FROM <include refid="tb"/>
        WHERE pid IN
        <foreach collection="list" open="(" separator="," close=")"
                 item="pid">
            #{pid}
        </foreach>
    </select>-->


    <update id="updateHasChildren" parameterType="map">
        UPDATE <include refid="tb" />
        SET has_children = #{hasChildren}
        WHERE id = #{id}
    </update>

    <update id="update" parameterType="FrontCategory">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null"> name = #{name}, </if>
            <if test="logo != null"> logo = #{logo}, </if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>
</mapper>