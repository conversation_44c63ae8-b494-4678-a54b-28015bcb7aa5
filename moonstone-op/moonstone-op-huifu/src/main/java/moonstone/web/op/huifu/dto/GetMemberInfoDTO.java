package moonstone.web.op.huifu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 查询会员信息
 */
@Data
@ApiModel(value = "GetMemberInfoDTO对象", description = "查询会员信息")
public class GetMemberInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "商户系统用户标识，商户系统中唯一编号", required = true)
    @NotEmpty(message = "商户系统用户标识不能为空")
    private String bizUserId;
}
