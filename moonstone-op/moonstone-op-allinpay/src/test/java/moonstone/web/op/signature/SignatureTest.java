package moonstone.web.op.signature;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.BizParameter;
import com.allinpay.sdk.bean.OpenConfig;
import com.allinpay.sdk.bean.OpenResponse;
import com.allinpay.sdk.util.SecretUtils;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.op.allinpay.dto.signature.req.*;
import moonstone.web.op.allinpay.dto.signature.res.*;
import moonstone.web.op.allinpay.util.AllinPayUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.*;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.junit.Assert;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.interfaces.RSAPrivateKey;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
public class SignatureTest {

    @Test
    public void userRegister(){
        UserRegisterDTO userRegisterDTO = new UserRegisterDTO();
        userRegisterDTO.setMerId("***************");
        userRegisterDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        userRegisterDTO.setAccount("***********");
        userRegisterDTO.setUserType("1");
        userRegisterDTO.setIdentity("34128219950612551X");
        userRegisterDTO.setName("刘之涵");
        userRegisterDTO.setIdentityType("0");
//		userRegisterDTO.setCorpName("");
//		userRegisterDTO.setRegCode("");
//		userRegisterDTO.setOrgCode("");
//		userRegisterDTO.setTaxCode("");
//		userRegisterDTO.setContactMobile("");

        try {
            final BizParameter param = AllinPayUtil.getMap(userRegisterDTO);
            final OpenResponse response = get().execute("allinpay.uas.userRegister", param);
            System.out.println("traceId:" + response.getTraceId());
            if ("000000".equals(response.getSubCode())) {
                System.out.println(response.getData());
                UserRegisterResponse jsonObject = JSONObject.parseObject(response.getData(), UserRegisterResponse.class);
            }

            // taskId： 170073265301000001   170073298501000006
            // {"msg":"接口调用成功","code":"10000","data":{"orderId":"1177303877735456768","success":true,"fee":false,"timestamp":"1700732718309","info":{"taskId":"170073265301000001"}},"subCode":"000000","sign":"oY7xfDbzbJNpTD0bxR4IEHMdUmeqRa8JEJs61O5ntGV4hi13EglGEhDm+I7jSe4GniZDE7Rn5nlwpTswXBsOdXpgp0KIadIJAoYLllmtLEFqNDEwsEnzoz7Ew8UhwKY+uMv9+kNJo3ok1EviFvQvrozCLBHiV1jNLF/MibJ/Dk30i931lfPoTItC6urjh26kYOAHAjlP71tU38pkL+18oW/aKf9Js7n9hS3oL0F97EiNlvnlxce/q7Kl3XhkqKruC6odwqkYDbHI5qF0vc5yasXx/JiIA0S/E5kH/TmQ8INEeKlRc8Q0bsME/JnSlTk4L7BW7mMPg6uY+XVH1OcfNA==","subMsg":"交易成功"}
        } catch (final Exception e) {
            log.error("通联client启动失败:error {}",e);
        }
    }


    @Test
    public void queryCertStatus(){
        QueryCertStatusDTO queryCertStatusDTO = new QueryCertStatusDTO();
        queryCertStatusDTO.setMerId("***************");
        queryCertStatusDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        queryCertStatusDTO.setAccount("229*****002");
        queryCertStatusDTO.setTaskId("170073265301000001");

        try {
            final BizParameter param = AllinPayUtil.getMap(queryCertStatusDTO);
            final OpenResponse response = get().execute("allinpay.uas.queryCertStatus", param);
            System.out.println("traceId:" + response.getTraceId());
            if ("000000".equals(response.getSubCode())) {
                System.out.println(response.getData());

                QueryCertStatusResponse jsonObject = JSONObject.parseObject(response.getData(), QueryCertStatusResponse.class);
                System.out.println(JSON.toJSONString(jsonObject));

            }
            // {"msg":"接口调用成功","code":"10000","data":{"orderId":"1177306743019708416","success":true,"fee":false,"timestamp":"*************","info":{"status":"5"}},"subCode":"000000","sign":"eHxxx=","subMsg":"交易成功"}
        } catch (final Exception e) {
            log.error("通联client启动失败:error {}",e);
        }
    }



    @Test
    public void upTemplate() throws Exception {
        String uploadFileReceiveToken = uploadFile("F:\\tmp\\2023年【跨境O2O】门店推广服务协议.pdf");
        System.out.println(uploadFileReceiveToken);
        UpTemplateDTO upTemplateDTO = new UpTemplateDTO();
        upTemplateDTO.setMerId("***************");
        upTemplateDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        upTemplateDTO.setAccount("***********");
        upTemplateDTO.setFname("2023年跨境O2O门店推广服务协议9.pdf");
        upTemplateDTO.setFtype("PDF");
        upTemplateDTO.setFpages(5L);
        upTemplateDTO.setToken(uploadFileReceiveToken);


        //  门店名
        UpTemplateDTO.ElementInfo upTmpElement1 = new UpTemplateDTO.ElementInfo();
        upTmpElement1.setPageNum(1L);
        upTmpElement1.setX("0.22");
        upTmpElement1.setY("0.23");
        upTmpElement1.setType("text");

        // 门店身份证号
        UpTemplateDTO.ElementInfo upTmpElement2 = new UpTemplateDTO.ElementInfo();
        upTmpElement2.setPageNum(1L);
        upTmpElement2.setX("0.28");
        upTmpElement2.setY("0.258");
        upTmpElement2.setType("text");

        // 门店手机号
        UpTemplateDTO.ElementInfo upTmpElement3 = new UpTemplateDTO.ElementInfo();
        upTmpElement3.setPageNum(1L);
        upTmpElement3.setX("0.25");
        upTmpElement3.setY("0.285");
        upTmpElement3.setType("text");

        // 签订日期1
        UpTemplateDTO.ElementInfo upTmpElement4 = new UpTemplateDTO.ElementInfo();
        upTmpElement4.setPageNum(5L);
        upTmpElement4.setX("0.26");
        upTmpElement4.setY("0.333");
        upTmpElement4.setType("text");

        // 签订日期2
        UpTemplateDTO.ElementInfo upTmpElement5 = new UpTemplateDTO.ElementInfo();
        upTmpElement5.setPageNum(5L);
        upTmpElement5.setX("0.73");
        upTmpElement5.setY("0.333");
        upTmpElement5.setType("text");

        List<UpTemplateDTO.ElementInfo> list = new ArrayList<>();
        list.add(upTmpElement1);
        list.add(upTmpElement2);
        list.add(upTmpElement3);
        list.add(upTmpElement4);
        list.add(upTmpElement5);
        upTemplateDTO.setElements(list);

        try {
            final BizParameter param = AllinPayUtil.getMap(upTemplateDTO);
            final OpenResponse response = get().execute("allinpay.uas.upTemplate", param);
            System.out.println("traceId:" + response.getTraceId());
            if ("000000".equals(response.getSubCode())) {
                System.out.println(response.getData());

                UpTemplateResponse jsonObject = JSONObject.parseObject(response.getData(), UpTemplateResponse.class);
                System.out.println(JSON.toJSONString(jsonObject));

            }
            // {"orderId":"1177554519720763392","success":true,"fee":false,"timestamp":"1700792476048","info":{"fid":"3890132155241366434","templateId":"3890132155241366434"}}
            // {"orderId":"1177541518464688128","success":true,"fee":false,"timestamp":"*************","info":{"fid":"2241489583*********","templateId":"2241489583*********"}}
        } catch (final Exception e) {
            log.error("通联client启动失败:error {}",e);
        }
    }



    @Test
    public void genContractFileByTmp() throws Exception {
        GenContractFileByTmpDTO genContractFileByTmpDTO = new GenContractFileByTmpDTO();
        genContractFileByTmpDTO.setMerId("***************");
        genContractFileByTmpDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        genContractFileByTmpDTO.setAccount("***********");
        genContractFileByTmpDTO.setTemplateId("1792730508916117888");

        // {"orderId":"1185225608515297280","success":true,"fee":false,"timestamp":"*************","info":{"fid":"8602906769600349755","templateId":"8602906769600349755"}}
        GenContractFileByTmpDTO.GenContractFileByTmpElements element1 = new GenContractFileByTmpDTO.GenContractFileByTmpElements();
        element1.setValue("刘之涵");
        GenContractFileByTmpDTO.GenContractFileByTmpElements element2 = new GenContractFileByTmpDTO.GenContractFileByTmpElements();
        element2.setValue("34128219990610551X");
        GenContractFileByTmpDTO.GenContractFileByTmpElements element3 = new GenContractFileByTmpDTO.GenContractFileByTmpElements();
        element3.setValue("***********");

        String day = day();
        GenContractFileByTmpDTO.GenContractFileByTmpElements element4 = new GenContractFileByTmpDTO.GenContractFileByTmpElements();
        element4.setValue(day);
        GenContractFileByTmpDTO.GenContractFileByTmpElements element5 = new GenContractFileByTmpDTO.GenContractFileByTmpElements();
        element5.setValue(day);
        List<GenContractFileByTmpDTO.GenContractFileByTmpElements> elements = new ArrayList<>();
        elements.add(element1);
        elements.add(element2);
        elements.add(element3);
        elements.add(element4);
        elements.add(element5);
        genContractFileByTmpDTO.setElements(elements);

//        queryCertStatusDTO.setMerId("***************");
//        queryCertStatusDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
//        queryCertStatusDTO.setAccount("229*****001");
//        queryCertStatusDTO.setTaskId("170073265301000001");

        try {
            final BizParameter param = AllinPayUtil.getMap(genContractFileByTmpDTO);
            final OpenResponse response = get().execute("allinpay.uas.genContractFileByTmp", param);
            System.out.println("traceId:" + response.getTraceId());
            if ("000000".equals(response.getSubCode())) {
                System.out.println(response.getData());

                GenContractFileByTmpResponse jsonObject = JSONObject.parseObject(response.getData(), GenContractFileByTmpResponse.class);
                System.out.println(JSON.toJSONString(jsonObject));

            }
            // {"orderId":"1177541518464688128","success":true,"fee":false,"timestamp":"*************","info":{"fid":"2241489583*********","templateId":"2241489583*********"}}
        } catch (final Exception e) {
            log.error("通联client启动失败:error {}",e);
        }
    }

    private static String day(){
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String formattedDate = currentDate.format(formatter);

        // 输出格式化后的日期
        System.out.println("当前日期：" + formattedDate);
        return formattedDate;
    }
    @Test
    public void downloadContractFile() throws Exception {
        DownloadContractFileDTO genContractFileByTmpDTO = new DownloadContractFileDTO();
        genContractFileByTmpDTO.setMerId("***************");
        genContractFileByTmpDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        genContractFileByTmpDTO.setAccount("***********");
//        genContractFileByTmpDTO.setFid("2241489583*********");
        genContractFileByTmpDTO.setFid("170297802401000027");

        try {
            final BizParameter param = AllinPayUtil.getMap(genContractFileByTmpDTO);
            final OpenResponse response = get().execute("allinpay.uas.downloadContractFile", param);
            System.out.println("traceId:" + response.getTraceId());
            if ("000000".equals(response.getSubCode())) {
                System.out.println(response.getData());

                DownloadContractFileResponse jsonObject = JSONObject.parseObject(response.getData(), DownloadContractFileResponse.class);
                System.out.println(JSON.toJSONString(jsonObject));


                downloadFile(jsonObject.getInfo().getToken());
            }
            // {"orderId":"1177541518464688128","success":true,"fee":false,"timestamp":"*************","info":{"fid":"2241489583*********","templateId":"2241489583*********"}}
        } catch (final Exception e) {
            log.error("通联client启动失败:error {}",e);
        }
    }

    @Test
    public void quickSign() throws Exception {
        QuickSignDTO quickSignDTO = new QuickSignDTO();
        quickSignDTO.setMerId("***************");
        quickSignDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        quickSignDTO.setAccount("***********");
        quickSignDTO.setFid("3835309687891872819");
        quickSignDTO.setFpages(5L);

        QuickSignDTO.SignatoryInfo signatoryInfo = new QuickSignDTO.SignatoryInfo();
        signatoryInfo.setSigner("***********");
        signatoryInfo.setPageNum(5);
        signatoryInfo.setX("0.75");
        signatoryInfo.setY("0.23");

        List<QuickSignDTO.SignatoryInfo> signatoryInfoList = new ArrayList<>();
        signatoryInfoList.add(signatoryInfo);
        quickSignDTO.setSignatory(signatoryInfoList);

//        genContractFileByTmpDTO.setMerId("***************");
//        genContractFileByTmpDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
//        genContractFileByTmpDTO.setAccount("229*****001");
//        genContractFileByTmpDTO.setFid("2241489583*********");

        try {
            final BizParameter param = AllinPayUtil.getMap(quickSignDTO);
            final OpenResponse response = get().execute("allinpay.uas.quickSign", param);
            System.out.println("traceId:" + response.getTraceId());
            if ("000000".equals(response.getSubCode())) {
                System.out.println(response.getData());

                QuickSignResponse jsonObject = JSONObject.parseObject(response.getData(), QuickSignResponse.class);
                System.out.println(JSON.toJSONString(jsonObject));

            }
            // {"orderId":"1177541518464688128","success":true,"fee":false,"timestamp":"*************","info":{"fid":"2241489583*********","templateId":"2241489583*********"}}
        } catch (final Exception e) {
            log.error("通联client启动失败:error {}",e);
        }
    }

    /**
     * 测试环境-文件系统appId
     */
    private static final String APPID = "1596041822008590337";
    /**
     * 测试环境-文件系统证书
     */
    public static final String privateKeyPath = "F:\\\\codeup_supply\\\\mall\\\\showcase\\\\src\\\\main\\\\resources\\\\allinpay\\\\test\\\\signature\\\\1596041822008590337.pfx";
    /**
     * 测试环境-文件系统证书密码
     */
    public static final String pwd = "123456";
//    String pwd = "123456";
//    String privateKeyPath = "F:\\codeup_supply\\mall\\showcase\\src\\main\\resources\\allinpay\\test\\signature\\1596041822008590337(RSA2).pfx";

    /**
     * 测试环境-文件上传地址
     */
    public static final String OPENFILEUPLOADURL = "http://test.allinpay.com/file/open/uploadFile";

    /**
     * 测试环境-文件下载地址
     */
    public static final String DOWNLOADFILEURL = "http://test.allinpay.com/file/open/downloadFile";

    /**
     * 文件下载目标地址
     */
    private static final String FILEPATH = "F:\\tmp\\download\\";

    public static String uploadFile(final String localFilePath) throws Exception {
        String result = null;
        try {
            File file = new File(localFilePath);
            InputStream is = new FileInputStream(file);
            //创建HttpClient
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(OPENFILEUPLOADURL);
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();

            final String timeStamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            /*绑定文件参数，传入文件流和contenttype，此处也可以继续添加其他formdata参数*/
            builder.addBinaryBody("file", is, ContentType.MULTIPART_FORM_DATA, file.getName());
            builder.addTextBody("appId", APPID);
            builder.addTextBody("timestamp", timeStamp);
            //计算MD5开始
            InputStream is1 = new FileInputStream(new File(localFilePath));
            String md5 = getFileMD5(is1);
            builder.addTextBody("md5", md5);
            //计算MD5结束
            //加签开始
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("appId", APPID);
            resMap.put("timestamp", timeStamp);
            resMap.put("md5", md5);
            String source = getSignedValue(resMap);
            RSAPrivateKey privateKey = (RSAPrivateKey) SecretUtils.loadPrivateKey((String) null,
                    privateKeyPath, pwd);
            String sign = SecretUtils.sign(privateKey, source, "SHA256WithRSA");
            builder.addTextBody("sign", sign);
            //加签结束
            HttpEntity entity = builder.build();
            httpPost.setEntity(entity);
            //执行提交
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                //将响应的内容转换成字符串
                String resultBackStr = EntityUtils.toString(responseEntity, Charset.forName("UTF-8"));
                //此处根据服务器返回的参数转换，这里返回的是JSON格式
                System.out.println("上传文件返回结果:" + resultBackStr);
                OpenResponse resp = JSON.parseObject(resultBackStr, OpenResponse.class);
                if ("10000".equals(resp.getCode()) && "000000".equals(resp.getSubCode())) {
                    result = JSON.parseObject(resp.getData()).getString("token");
                }
                Assert.assertTrue(true);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }

        return result;
    }


    public void downloadFile(final String tokenStr) throws Exception {
        try {
            // 创建HttpClient
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(DOWNLOADFILEURL);// 测试环境
            // HttpPost httpPost = new HttpPost("https://cloud.allinpay.com/op/downloadFile");// 生产环境
            List<NameValuePair> parameters = new ArrayList<>();

            // 添加参数
            parameters.add(new BasicNameValuePair("appId", APPID));
            parameters.add(new BasicNameValuePair("timestamp", System.currentTimeMillis() + ""));
            parameters.add(new BasicNameValuePair("token", tokenStr));

            // 加签
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("appId", APPID);
            resMap.put("timestamp", System.currentTimeMillis() + "");
            resMap.put("token", tokenStr);
            String source = getSignedValue(resMap);
            RSAPrivateKey privateKey = (RSAPrivateKey) SecretUtils.loadPrivateKey(null, privateKeyPath, pwd);
            String sign = SecretUtils.sign(privateKey, source, "SHA256WithRSA");
            parameters.add(new BasicNameValuePair("sign", sign));

            // 设置请求体
            httpPost.setEntity(new UrlEncodedFormEntity(parameters));

            // 执行提交
            HttpResponse response = httpClient.execute(httpPost);

            // 处理返回结果
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    InputStream is = responseEntity.getContent();
                    File file = new File(FILEPATH + getFileName(response));
                    file.getParentFile().mkdirs();

                    try (FileOutputStream fileout = new FileOutputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int ch;
                        while ((ch = is.read(buffer)) != -1) {
                            fileout.write(buffer, 0, ch);
                        }
                    }

                    Assert.assertTrue(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
//        try {
//            //创建HttpClient
//            CloseableHttpClient httpClient = HttpClients.createDefault();
//            HttpPost httpPost = new HttpPost(DOWNLOADFILEURL);//测试环境
////            HttpPost httpPost = new HttpPost("https://cloud.allinpay.com/op/downloadFile");//生产环境
//            List<NameValuePair> parameters = new ArrayList<NameValuePair>();
//            NameValuePair appIdV = new BasicNameValuePair("appId", APPID);
//            NameValuePair timestampV = new BasicNameValuePair("timestamp", System.currentTimeMillis() + "");
//            NameValuePair tokenV = new BasicNameValuePair("token", tokenStr);
//            parameters.add(appIdV);
//            parameters.add(timestampV);
//            parameters.add(tokenV);
//            //加签开始
//            Map<String, Object> resMap = new HashMap<>();
//            resMap.put("appId", APPID);
//            resMap.put("timestamp", System.currentTimeMillis() + "");
//            resMap.put("token", tokenStr);
//            String source = getSignedValue(resMap);
//            RSAPrivateKey privateKey = (RSAPrivateKey) SecretUtils.loadPrivateKey((String) null,
//                    privateKeyPath, pwd);
//            String sign = SecretUtils.sign(privateKey, source, "SHA256WithRSA");
//            NameValuePair signV = new BasicNameValuePair("sign", sign);
//            parameters.add(signV);
//
//            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(parameters);
//            httpPost.setEntity(formEntity);
//            //执行提交
//            HttpResponse response = httpClient.execute(httpPost);
//            //收到返回，写文件
//            HttpEntity responseEntity = response.getEntity();
//            if (response.getStatusLine().getStatusCode() == 200) {
//                if (responseEntity != null) {
//                    InputStream is = responseEntity.getContent();
//                    File file = new File(FILEPATH + getFileName(response));
//                    file.getParentFile().mkdirs();
//                    FileOutputStream fileout = new FileOutputStream(file);
//                    /**
//                     * 根据实际运行效果 设置缓冲区大小
//                     */
//                    byte[] buffer = new byte[1024];
//                    int ch = 0;
//                    while ((ch = is.read(buffer)) != -1) {
//                        fileout.write(buffer, 0, ch);
//                    }
//                    is.close();
//                    fileout.flush();
//                    fileout.close();
//                    Assert.assertTrue(true);
//                }
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
    }

    public static OpenClient get(){
        String url = "http://test.allinpay.com/open/gateway";
        String appId = "1596041822008590337";
        String secretKey = "aICp8j5LIVeg2RLOnHm59NHCSwUdDMHJ";
        String pwd = "123456";
        String privateKeyPath = "F:\\codeup_supply\\mall\\showcase\\src\\main\\resources\\allinpay\\test\\signature\\1596041822008590337.pfx";
        String tlPublicKey = "F:\\codeup_supply\\mall\\showcase\\src\\main\\resources\\allinpay\\test\\signature\\TLCert-test.cer";

        final OpenConfig oc = new OpenConfig(url, appId, secretKey, privateKeyPath, pwd, tlPublicKey);

        OpenClient client = new OpenClient(oc);
        return client;
    }


    /**
     * 获取response header中Content-Disposition中的filename值
     *
     * @param response
     * @return
     */
    public static String getFileName(HttpResponse response) {
        Header contentHeader = response.getFirstHeader("Content-Disposition");
        String filename = null;
        if (contentHeader != null) {
            HeaderElement[] values = contentHeader.getElements();
            if (values.length == 1) {
                NameValuePair param = values[0].getParameterByName("filename");
                if (param != null) {
                    try {
                        filename = param.getValue();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return filename;
    }

    public static String getFileMD5(InputStream in) {
        if (in == null) {
            return null;
        }
        MessageDigest digest = null;
        byte buffer[] = new byte[1024];
        int len;
        try {
            digest = MessageDigest.getInstance("MD5");
            while ((len = in.read(buffer, 0, 1024)) != -1) {
                digest.update(buffer, 0, len);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        byte[] bytes = Base64.encodeBase64(digest.digest());
        String callback = new String(bytes);
        return callback;
    }

    private static String getSignedValue(final Map<String, Object> params) {
        final Map<String, Object> copy = new TreeMap<>();
        params.forEach((k, v) -> {
            if (v != null) {
                copy.put(k, v);
            }
        });
        copy.remove("sign");
        final StringBuilder sb = new StringBuilder();
        copy.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
        return sb.length() == 0 ? "" : sb.substring(0, sb.length() - 1);
    }
}
