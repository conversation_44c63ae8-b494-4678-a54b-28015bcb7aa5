package moonstone.web.op.allinpay.constant;

public interface NumberConstant {

	/** 负一 */
	Integer NEGATIVE_ONE = -1;

	/** 零 */
	Integer ZERO = 0;

	/** 一 */
	Integer ONE = 1;

	/** 二 */
	Integer TWO = 2;

	/** 四 */
	Integer FOUR = 4;

	/** 八 */
	Integer EIGHT = 8;

	/** 十 */
	Integer TEN = 10;

	/** 百 */
	Integer HUNDRED = 100;

	/** 千 */
	Integer THOUSAND = 1000;

	/** 最大值*/
	Integer MAX = 65535;

	/** 每天小时数*/
	Integer HOURS_PER_DAY = 24;

	/** 每小时分钟数*/
	Integer MINUTES_PER_HOUR = 60;

	/** 每分钟秒数*/
	Integer SECONDS_PER_MINUTE = 60;

	/** 每小时秒数*/
	Integer SECONDS_PER_HOUR = 60 * 60;

	/** 每天秒数*/
	Integer SECONDS_PER_DAY = 24 * 60 * 60;

	/** 7天秒数*/
	Integer SECONDS_SEVEN_DAYS = 7 * 24 * 60 * 60;

}
