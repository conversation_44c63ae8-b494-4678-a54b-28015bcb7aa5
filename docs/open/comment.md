## 根据商品id查询评论信息

* 请求url
```
GET comment.find.by.item.id
```

* 请求参数示例
```
itemId=4
```

* 请求参数说明

|参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| itemId|整形|商品id     |是|      |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": {
        "total": 5,
        "data": [
          {
            "comment": {
              "id": 103,
              "parentId": -1,
              "belongUserType": 1,
              "userId": 2,
              "userName": "buyer",
              "skuOrderId": 635,
              "itemId": 4,
              "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
              "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
              "skuAttrs": [
                {
                  "attrKey": "颜色分类",
                  "attrVal": "白色",
                  "unit": null,
                  "showImage": null,
                  "thumbnail": null,
                  "image": null
                }
              ],
              "shopId": 1,
              "shopName": "第一个店铺",
              "quality": 5,
              "describe": 5,
              "service": 5,
              "express": 5,
              "context": "sdfsdf",
              "status": 1,
              "extraJson": null,
              "extra": null,
              "hasDisplay": false,
              "createdAt": 1473770404000,
              "updatedAt": 1473770404000
            },
            "replies": []
          },
          {
            "comment": {
              "id": 99,
              "parentId": -1,
              "belongUserType": 1,
              "userId": 2,
              "userName": "buyer",
              "skuOrderId": 630,
              "itemId": 4,
              "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
              "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
              "skuAttrs": [
                {
                  "attrKey": "颜色分类",
                  "attrVal": "白色",
                  "unit": null,
                  "showImage": null,
                  "thumbnail": null,
                  "image": null
                }
              ],
              "shopId": 1,
              "shopName": "第一个店铺",
              "quality": 5,
              "describe": 5,
              "service": 5,
              "express": 5,
              "context": "rtert",
              "status": 1,
              "extraJson": null,
              "extra": null,
              "hasDisplay": false,
              "createdAt": 1473769758000,
              "updatedAt": 1473769758000
            },
            "replies": []
          },
          {
            "comment": {
              "id": 70,
              "parentId": -1,
              "belongUserType": 1,
              "userId": 2,
              "userName": "buyer",
              "skuOrderId": 67,
              "itemId": 4,
              "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
              "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
              "skuAttrs": [
                {
                  "attrKey": "颜色分类",
                  "attrVal": "白色",
                  "unit": null,
                  "showImage": null,
                  "thumbnail": null,
                  "image": null
                }
              ],
              "shopId": 1,
              "shopName": "浙江天猫供应链管理有限公司",
              "quality": 5,
              "describe": 5,
              "service": 5,
              "express": 5,
              "context": "很不错哦",
              "status": 1,
              "extraJson": null,
              "extra": null,
              "hasDisplay": false,
              "createdAt": 1469437942000,
              "updatedAt": 1469437942000
            },
            "replies": [
              {
                "id": 94,
                "parentId": 70,
                "belongUserType": 1,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 67,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": 5,
                "describe": 5,
                "service": 5,
                "express": 5,
                "context": "sfdfasfddddd",
                "status": 1,
                "extraJson": "{\"images\":[{\"imageUrl\":\"//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/08/12/7d8a4bfa-20cc-455f-b650-35802d97499d.png\"}]}",
                "extra": null,
                "hasDisplay": false,
                "createdAt": 1470973437000,
                "updatedAt": 1470973437000
              },
              {
                "id": 95,
                "parentId": 94,
                "belongUserType": 1,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 67,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": 5,
                "describe": 5,
                "service": 5,
                "express": 5,
                "context": "sadfasdfasdf",
                "status": 1,
                "extraJson": "{\"images\":[{\"imageUrl\":\"//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/08/12/86d55cde-c700-4ea0-9e14-e8ac75506a9f.png\"},{\"imageUrl\":\"//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/08/12/33d70567-d937-4940-99f9-7a668cea0b17.png\"}]}",
                "extra": null,
                "hasDisplay": false,
                "createdAt": 1470973452000,
                "updatedAt": 1470973452000
              }
            ]
          },
          {
            "comment": {
              "id": 22,
              "parentId": -1,
              "belongUserType": 1,
              "userId": 2,
              "userName": "buyer",
              "skuOrderId": 61,
              "itemId": 4,
              "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
              "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
              "skuAttrs": [
                {
                  "attrKey": "颜色分类",
                  "attrVal": "白色",
                  "unit": null,
                  "showImage": null,
                  "thumbnail": null,
                  "image": null
                }
              ],
              "shopId": 1,
              "shopName": "浙江天猫供应链管理有限公司",
              "quality": null,
              "describe": null,
              "service": null,
              "express": null,
              "context": "ffafoaiehf;aehfn",
              "status": 1,
              "extraJson": null,
              "extra": null,
              "hasDisplay": false,
              "createdAt": 1468240332000,
              "updatedAt": 1468240332000
            },
            "replies": [
              {
                "id": 23,
                "parentId": 22,
                "belongUserType": 2,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 61,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": null,
                "describe": null,
                "service": null,
                "express": null,
                "context": "别瞎几把评",
                "status": 1,
                "extraJson": null,
                "extra": null,
                "hasDisplay": true,
                "createdAt": 1468240390000,
                "updatedAt": 1468240390000
              },
              {
                "id": 24,
                "parentId": 23,
                "belongUserType": 1,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 61,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": null,
                "describe": null,
                "service": null,
                "express": null,
                "context": "好的",
                "status": 1,
                "extraJson": null,
                "extra": null,
                "hasDisplay": true,
                "createdAt": 1468240414000,
                "updatedAt": 1468240414000
              }
            ]
          },
          {
            "comment": {
              "id": 1,
              "parentId": -1,
              "belongUserType": 1,
              "userId": 2,
              "userName": "buyer",
              "skuOrderId": 1,
              "itemId": 4,
              "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
              "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
              "skuAttrs": [
                {
                  "attrKey": "颜色分类",
                  "attrVal": "白色",
                  "unit": null,
                  "showImage": null,
                  "thumbnail": null,
                  "image": null
                }
              ],
              "shopId": 1,
              "shopName": "浙江天猫供应链管理有限公司",
              "quality": null,
              "describe": null,
              "service": null,
              "express": null,
              "context": "我来添加第一条评价",
              "status": 1,
              "extraJson": null,
              "extra": null,
              "hasDisplay": false,
              "createdAt": 1468201632000,
              "updatedAt": 1468201632000
            },
            "replies": [
              {
                "id": 14,
                "parentId": 1,
                "belongUserType": 1,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 1,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": null,
                "describe": null,
                "service": null,
                "express": null,
                "context": "胜多负少",
                "status": 1,
                "extraJson": null,
                "extra": null,
                "hasDisplay": true,
                "createdAt": 1468231904000,
                "updatedAt": 1468231904000
              },
              {
                "id": 15,
                "parentId": 14,
                "belongUserType": 1,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 1,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": null,
                "describe": null,
                "service": null,
                "express": null,
                "context": "a'a'a'a'a'a'a'a'a'a",
                "status": 1,
                "extraJson": null,
                "extra": null,
                "hasDisplay": true,
                "createdAt": 1468232067000,
                "updatedAt": 1468232067000
              },
              {
                "id": 20,
                "parentId": 15,
                "belongUserType": 1,
                "userId": 2,
                "userName": "buyer",
                "skuOrderId": 1,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "shopId": 1,
                "shopName": "浙江天猫供应链管理有限公司",
                "quality": null,
                "describe": null,
                "service": null,
                "express": null,
                "context": "多舒服个地方官",
                "status": 1,
                "extraJson": null,
                "extra": null,
                "hasDisplay": true,
                "createdAt": 1468235655000,
                "updatedAt": 1468235655000
              }
            ]
          }
        ],
        "hasMore": false
      }
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| id | 整形 | 评论id |  |
| parentId | 整形 | 父评论id | |
| belongUserType | 整形 | 1为买家发起评论,2为商家发起评论|  |
| userId | 整形 | 用户id |  |
| userName | 字符串 | 用户名称 |  |
| skuOrderId | 整形 | sku订单id |  |
| itemId | 整形 | 商品id |  |
| itemName | 字符串 | 商品名称 |  |
| skuAttributes | 字符串 | sku属性, json表示 |  |
| shopId | 整形 | 店铺id |  |
| shopName | 字符串 | 店铺名称 |  |
| quality | 整形 | 质量评分 |  |
| describe | 整形 | 描述评分 |  |
| service | 整形 | 服务评分 |  |
| express | 整形 | 物流评分 |  |
| context | 字符串 | 评价内容 |  |
| status | 整形 | 评价状态 1->正常 -1->删除 |   |
| extra | 字符串 | 额外信息 |  |
| hasDisplay | 布尔 | 是否已晒单 |  |
| createdAt | 日期 | 毫秒数表示的创建时间 |  |
| updatedAt | 日期 | 毫秒数表示的更新时间 |  |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "商品不存在"
     }
    ```