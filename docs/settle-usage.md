# 结算文档
========================

* 修改者列表

	**作者** 张河城(<EMAIL>)

* 修改历史

时间|作者|备注
---| ---| ---
2016年9月21日 | 张河城 | 初稿


## 业务流程

1. 创建店铺时设置平台佣金费率: 在创建店铺时,会创建Commissionrule，rate为默认值，后期由运营在佣金维护表中更新
2. 下订单时保存佣金费率: 从而防止佣金费率修改带来的问题
3. 支付或退款成功: 这时会抛出相应事件
4. 在listener中为每个支付单或退款单创建对应的账务明细
5. job中拉取对账单, 同时会更新Settlement和PayChannelDetail的对账状态, 同时生成SettleOrderDetail或SettleRefundOrderDetail
6. 在第二天, 会生成Seller/PlatformTradeDailySummary,PayChannelDailySummary(都根据对账时间汇总)

-----------------------------------------

## 用户操作

* SellerSettlements: 商家结算查询
* AdminSettlements: 运营结算查询
* SettleExports: 导出
* AdminSettleAbnormalHandlers: 手动操作
    * /api/settle/payment?paymentId=1: 模拟支付成功事件
    * /api/settle/refund?refundId=1: 模拟退款成功事件
    * /api/settle/check-settlement?settlementId=1: 账务明细手动对账
    * /api/settle/check-pay-channel?payChannelDetailId=1: 支付渠道明细手动对账
    * /api/settle/sync-platform-trade-daily?sumAt=2016-08-01: 手动生成平台日汇总
    * /api/settle/sync-seller-trade-daily?sumAt=2016-08-01: 手动生成商家日汇总
    * /api/settle/sync-pay-channel-daily?sumAt=2016-08-01: 手动生成支付渠道日汇总

----------------------------

## 配置

### 全局配置

*  settle.controller.enable: true
*  settle.io.terminus.parana.web.core.component.listener.enable: true
*  settle.job.trans.enable: true
*  settle.job.summary.enable: true
*  settle.service.enable: true

### 服务配置

* SettleServiceAutoConfig
    * SettleFeeConfig
        * settle.seller.pay.gateway.commission.unrefund: true
    * SettleTransConfig

### 事件配置

* SettleListenerConfig
    * SettleFeeConfig

### 任务

* SettleJobConfig
    * settle.job.summary.enable: true
    * settle.commission.rate.default: 0
    * settle.mockpay.commission.rate: 10    
    * TestSummaryJob (test, dev)
        * settle.cron.test.summary.channel: 0 */2 * * * *   
        * settle.cron.test.summary.platform: 0 */2 * * * *    
        * settle.cron.test.summary.seller: 0 */2 * * * *    
    * ProductSummaryJob
        * settle.cron.prod.summary.channel: 0 0 1 * * *
        * settle.cron.prod.summary.platform: 1 0 1 * * *
        * settle.cron.prod.summary.seller: 2 0 1 * * *
* MockpayTransJob
    * pay.job.trans.mockpay.enable: true
    * pay.cron.test.trans.mockpay: 2 */3 * * * ?
* AppAlipayTransJob
    * pay.job.trans.alipay.app.enable: true
    * pay.cron.test.trans.alipay.app: 0 */5 * * * ?
    * pay.cron.prod.trans.alipay.app: 0 0 9 * * ?
* PcAlipayTransJob
    * pay.job.trans.alipay.pc.enable: true
    * pay.cron.test.trans.alipay.pc: 5 */5 * * * ?
    * pay.cron.prod.trans.alipay.pc: 0 0 9 * * ?
* WapAlipayTransJob
    * pay.job.trans.alipay.wap.enable: true
    * pay.cron.test.trans.alipay.wap: 10 */5 * * * ?
    * pay.cron.prod.trans.alipay.wap: 0 0 9 * * ?
* AppUnionpayTransJob
    * pay.job.trans.unionpay.app.enable: true
    * pay.cron.test.trans.unionpay: 30 */5 * * * ?
    * pay.cron.prod.trans.unionpay: 0 0 9 * * ?
* PcUnionpayTransJob
    * pay.job.trans.unionpay.pc.enable: true 
    * settle.cron.prod.trans.unionpay: 35 */3 * * * ?
    * settle.cron.prod.trans.unionpay: 0 0 9 * * ?
* WapUnionpayTransJob
    * pay.job.trans.unionpay.wap.enable: true
    * pay.cron.test.trans.unionpay.wap: 40 */5 * * * ?
    * pay.cron.prod.trans.unionpay.wap: 0 0 9 * * ?
* AppWechatpayTransJob
    * pay.job.trans.wechatpay.app.enable: true
    * pay.cron.test.trans.wechatpay.app: 15 */5 * * * ?
    * pay.cron.prod.trans.wechatpay.app: 0 0 10 * * ?
* JsapiWechatpayTransJob
    * pay.job.trans.wechatpay.jsapi.enable: true    
    * pay.cron.test.trans.wechatpay.jsapi: 20 */5 * * * ?
    * pay.cron.prod.trans.wechatpay.jsapi: 0 0 10 * * ?
* QrWechatpayTransJob
    * pay.job.trans.wechatpay.qr.enable: true     
    * pay.cron.test.trans.wechatpay.qr: 25 */5 * * * ?
    * pay.cron.prod.trans.wechatpay.jsapi: 0 0 10 * * ?
    
---------------------

## 实现细节

### 实体类

 * 账务明细: Settlement
 * 支付渠道明细: PayChannelDetail
 * 订单明细: SettleOrderDetail
 * 退款单明细: SettleRefundOrderDetail
 * 商家汇总: SellerTradeDailySummary
 * 平台汇总: PlatformTradeDailySummary
 * 支付渠道日汇总: PayChannelDailySummary
 * 佣金: CommissionRule
 * 异常: SettleAbnormalTrack

### 下订单

 * 在ShopOrder.commissionRate中保存店铺佣金
 * discount不存在则为null
 * skuOrder.shipFee并没有保存
 * shopOrder.originFee并非商品原价
 * Payment.originFee不包含运费，只是discount的原始值

### 支付成功

* PaymentSettleEvent
    * PaymentSettlementListener
        * SettleRichOrderReadService.findByPaymentId
        * FeeCalculatorRegistry
            * PaySettlementFeeCalculator
        * SettlementWriteService
    * PaymentPayChannelListener
        * payChannelDetailWriteService

### 退款成功

* RefundSettleEvent
    * RefundPayChannelListener
        * payChannelDetailWriteService
    * RefundSettlementListener
        * SettleRichOrderReadService.findByRefundId
        * FeeCalculatorRegistry
            * ShopOrderRefundSettlementFeeCalculator
            * SkuOrderRefundSettlementFeeCalculator
        * SettlementWriteService

### 订单完成

* OrderConfirmEvent
    * SettleOrderConfirmListener: 更新SettleOrderDetail的完成时间

### 拉取账单

* PayTransLoadedEvent
    * PayTransLoadedListener
        * payChannelDetailWriteService.updateCheckStatus
        * settlementWriteService.updateCheckStatus

* CheckedEvent
    * CheckedListener
        *  PaymentCheckedHandler
            * settleRichOrderReadService.findByShopOrderId
            * SettleOrderDetailFeeCalculator
            * settleOrderDetailWriteService
        * RefundCheckedHandler
            * settleRichOrderReadService.findByRefundId
            * RefundShopOrderFeeCalculator
            * RefundSkuOrderTogetherFeeCalculator
            * settleRefundOrderDetailWriteService

### 日汇总

* SummaryPayChannelDailyJob
    * payChannelDailySummaryWriteService.generatePayChannelDailySummary
    
* SummaryPlatformTradeDailyJob
    * platformTradeDailySummaryWriteService.generatePlatformTradeDailySummary
    
* SummarySellerTradeDailyJob
    * sellerTradeDailySummaryWriteService.generateSellerTradeDailySummary

### 异常处理

* SettleAbnormalEvent
    * SettleAbnormalListener
        * settleAbnormalTrackWriteService

---------------------
## 扩展

### 新增支付渠道

要开发对应的DevXxxTransJob和ProdXxxTransJob, 主要工作为开发XxxTransLoader和XxxTransCollector.
示例如下:

```java

@Component
@Primary
@Slf4j
public class ParanaMockpayTransLoader extends MockpayTransLoader {

    @RpcConsumer
    private SettlementReadService settlementReadService;

    @Override
    public List<MockpayTrans> loadTrans(PayTransCriteria criteria, MockPayToken token) throws PayException {
        Response<List<Settlement>> findResp=settlementReadService.findByChannelAndCheckStatus("mockpay", CheckStatus.WAIT_CHECK);
        if(!findResp.isSuccess()){
            log.error("find mockpay unchecked settlement fail, cause={}", findResp.getError());
            return Collections.emptyList();
        }
        List<MockpayTrans> transList = new ArrayList<>();
        for(Settlement settlement : findResp.getResult()){
            MockpayTrans trans = new MockpayTrans();
            trans.setId(settlement.getId());
            trans.setFee(settlement.getActualFee());
            trans.setAccount(Tokens.DEFAULT_ACCOUNT);
            trans.setTradeNo(settlement.getTradeNo());
            trans.setRefundNo(settlement.getRefundNo());
            transList.add(trans);
        }
        return transList;
    }
}


@Component
public class MockpayTransCollector implements TransCollector<MockpayTrans> {

    private final Long commissionRate;

    @Autowired
    public MockpayTransCollector(@Value("${pay.mockpay.commission.rate: 10}") Long commissionRate) {
        this.commissionRate = commissionRate;
    }

    @Override
    public List<PayTrans> collectCommissionAndRate(List<MockpayTrans> transList) throws PayException {
        List<PayTrans> result = new ArrayList<>();
        for(MockpayTrans trans : transList){
            PayTrans payTrans = new PayTrans();
            payTrans.setChannel(Channels.MOCKPAY);
            payTrans.setFee(trans.getFee());
            payTrans.setCommission(NumberUtil.splitByRate(trans.getFee(), commissionRate));
            payTrans.setRate(commissionRate);
            payTrans.setGatewayLogId(trans.getId().toString());
            payTrans.setAccountNo(trans.getAccount());
            payTrans.setTradeNo(trans.getTradeNo());
            payTrans.setRefundNo(trans.getRefundNo());

            result.add(payTrans);
        }
        return result;
    }
}

@Component
public class DevMockpayTransJob extends TransJobTemplate<MockPayToken, MockpayTrans> {

    @Autowired
    public DevMockpayTransJob(TokenProvider<MockPayToken> tokenProvider,
                              EventBus eventBus,
                              TransLoader<MockpayTrans, MockPayToken> transLoader,
                              TransCollector<MockpayTrans> transCollector) {
        super(tokenProvider, eventBus, transLoader, transCollector);
    }

    @Scheduled(cron="${pay.cron.test.trans.mockpay: 2 */3 * * * ?}")
    public void syncMockpayTrans(){
        PayTransCriteria criteria = new PayTransCriteria().startFromToday().endWithNow();
        super.syncTrans(criteria);
    }

}
```

### 公式变更

1. 如果汇总公式发生变化, 则实现新的SummaryRule

```java
public interface SummaryRule {
    /**
     * 平台日汇总合并公式
     * @param forward 正向订单的平台日汇总
     * @param backward 逆向订单的平台日汇总
     * @return 待持久化的平台订单日汇总
     */
    List<PlatformTradeDailySummary> platformDaily(PlatformTradeDailySummary forward, PlatformTradeDailySummary backward);

    /**
     * 商家日汇总合并公式
     * @param forwardList 每个商家的正向订单日汇总
     * @param backwardList 每个商家的逆向订单日汇总
     * @return 待持久化的商家订单日汇总
     */
    List<SellerTradeDailySummary> sellerDaily(List<SellerTradeDailySummary> forwardList, List<SellerTradeDailySummary> backwardList);

    /**
     * 支付渠道日汇总合并公式
     * @param forwardList 正向订单的支付渠道日汇总
     * @param backwardList 逆向订单的支付渠道日汇总
     * @return 待持久化的支付渠道日汇总
     */
    List<PayChannelDailySummary> channelDaily(List<PayChannelDailySummary> forwardList, List<PayChannelDailySummary> backwardList);
}
```

2. 如果明细的金额计算公式有问题, 则重新实现FeeCalculator, 并注册到FeeCalculatorRegistry

```java
public class PaySettlementFeeCalculator implements FeeCalculator<SettleRichOrder> {

    @Override
    public SettleFee calculateFee(SettleRichOrder richOrder) {
        SettleFee settleFee=new SettleFee();

        Long originFee=0L;
        Long skuOrderDiscount=0L;
        for(SkuOrder skuOrder : richOrder.getSkuOrderList()){
            originFee += skuOrder.getOriginFee();
            skuOrderDiscount += MoreObjects.firstNonNull(skuOrder.getDiscount(),0L);
        }

        Long shopOrderDiscount=0L;
        Long shipFee=0L;
        Long shipFeeDiscount=0L;
        for(ShopOrder shopOrder : richOrder.getShopOrderList()){
            shopOrderDiscount += MoreObjects.firstNonNull(shopOrder.getDiscount(), 0);
            shipFee += shopOrder.getShipFee();
            shipFeeDiscount += shopOrder.getOriginShipFee()-shopOrder.getShipFee();
        }

        /**
         * 原价: sum(skuOrder.originFee)
         */
        settleFee.setOriginFee(originFee);
        /**
         * 商家优惠: sum(skuOrder.discount)+sum(shopOrder.discount)
         */
        settleFee.setSellerDiscount(skuOrderDiscount+shopOrderDiscount);
        /**
         * 平台优惠: payment.discount
         */
        Integer platformDiscount = MoreObjects.firstNonNull(richOrder.getPayment().getDiscount(), 0);
        settleFee.setPlatformDiscount(platformDiscount.longValue());
        /**
         * 运费: sum(shopOrder.shipFee)
         */
        settleFee.setShipFee(shipFee);
        /**
         * 运费优惠: sum(shopOrder.originShipFee-shopOrder.shipFee)
         */
        settleFee.setShipFeeDiscount(shipFeeDiscount);

        /**
         * 支付平台佣金: trans
         */
        settleFee.setGatewayCommission(richOrder.getGatewayCommission());

        /**
         * 电商平台佣金: (originFee+shipFee-sellerDiscount) * rate
         * 商家优惠不参与计算, 运费参与运算
         */
        Long platformCommission=0L;
        for(ShopOrder shopOrder : richOrder.getShopOrderList()){

            Long platformCommissionBase= shopOrder.getOriginFee() + shopOrder.getShipFee() - MoreObjects.firstNonNull(shopOrder.getDiscount(), 0);

            platformCommission += SettleFeeUtil.getPlatformCommission(platformCommissionBase, shopOrder.getCommissionRate().longValue());
        }
        settleFee.setPlatformCommission(platformCommission);

        return settleFee;
    }
}

@Configuration
public class SettleFeeConfig {
    @Bean
    @ConditionalOnMissingBean(name = FeeCalculatorKeys.PAY_SETTLEMENT)
    public PaySettlementFeeCalculator paySettlementFeeCalculator(FeeCalculatorRegistry registry){
        PaySettlementFeeCalculator paySettlementFeeCalculator=new PaySettlementFeeCalculator();
        registry.register(FeeCalculatorKeys.PAY_SETTLEMENT, paySettlementFeeCalculator);
        return paySettlementFeeCalculator;
    }
}

```

### listener变更

1. settle.io.terminus.parana.web.core.component.listener.enable: false, 从而关闭所有的结算相关的listener
2. 重写listener, 以监听事件: 
    * PaymentSettleEvent
    * RefundSettleEvent
    * PayTransLoadedEvent
    * CheckedEvent
    * SettleAbnormalEvent
3. 写一个新的SettleListenerConfig

### job变更

1. 将settle.job.summary.enable:false, 以关闭汇总job
2. 将所有的账务拉取Job关闭
3. 重写job

### controller变更

1. settle.controller.enable: false, 以关闭所有的结算相关controller
2. 重写controller


---------------------
## 订单系统中的金额

 * SkuOrder: originFee fee shipFee shipFeeDiscount discount(skuLevel) integral balance
 * ShopOrder: originFee(skuOrder.fee) fee shipFee originShipFee discount(orderLevel) integral balance
 * Payment: originFee(shopOrder.fee - shipFee) fee discount(payLevel) integral balance
 * Refund: fee integral balance


---------------------
## 结算计算公式( 根据订单系统中的金额来计算)

### PayChannelDetail(支付渠道明细)

### PaymentSettlement(支付时的账务明细)

* 原价: sum(skuOrder.originFee)
* 商家优惠: sum(skuOrder.discount)+sum(shopOrder.discount)
* 平台优惠: payment.discount
* 运费: sum(shopOrder.shipFee)
* 运费优惠: sum(shopOrder.originShipFee-shopOrder.shipFee)
* 实收: originFee-sellerDiscount-platformDiscount+shipFee
* 支付平台佣金: trans
* 电商平台佣金: sum ( (originFee+shipFee-sellerDiscount) * shopOrder.commissionRate )

### ShopRefundSettlement(订单级退款)

* 原价: sum(skuOrder.originFee)
* 商家优惠: shopOrder.discount + sum(skuOrder.discount)
* 平台优惠: payment.discount * ( (shopOrder.originFee-discount) / sum(shopOrder.originFee-discount))
* 运费: shopOrder.shipFee 或 0
* 运费优惠: shopOrder.originShipFee-shopOrder.shipFee 或 0
* 实收: originFee-sellerDiscount-platformDiscount+shipFee
* 支付平台佣金: trans， 当支付平台不退时由商家承担， paymentGatewayCommission * (actualFee / payment.fee)
* 电商平台佣金: (originFee+shipFee-sellerDiscount) * shopOrder.commissionRate

### SkuRefundSettlement

* 原价: sum(skuOrder.originFee)
* 商家优惠: sum(skuOrder.discount)+ split(shopOrder.discount), sum(skuOrder.fee) / shopOrder.originFee
* 平台优惠: payment.discount * ( sum(skuOrder.originFee-skuOrder.discount)-split(shopOrder.discount)) / (shopOrder.originFee-shopOrder.discount)
* 运费: shopOrder.shipFee * (refundSkuFeeWithoutShipped / shopOrder.originFee)
* 运费优惠: (shopOrder.originShipFee - shopOrder.shipFee) * (refundSkuFeeWithoutShipped/shopOrder.originFee), refundSkuFeeWithoutShipped: 没有发货的运费汇总
* 实收: originFee-sellerDiscount-platformDiscount+shipFee
* 支付平台佣金: trans， 当支付平台不退时由商家承担, paymentGatewayCommission * (actualFee / payment.fee)
* 电商平台佣金: (originFee+shipFee-sellerDiscount) * rate

### SettleOrderDetail

* 原价: sum(skuOrder.originFee)
* 商家优惠: shopOrder.discount + sum(skuOrder.discount)
* 平台优惠: payment.discount * ( (shopOrder.originFee-discount) / sum(shopOrder.originFee-discount))    
* 运费: shopOrder.shipFee
* 运费优惠: shopOrder.originShipFee-shopOrder.shipFee
* 支付平台佣金: gatewayCommission * (actualFee / payment.fee)
* 电商平台佣金: (originFee+shipFee-sellerDiscount) * rate
* 商家应收: 实收货款+平台优惠-支付平台佣金-平台佣金

### ShopRefundOrderDetail: 等同于shopRefundSettlement

### SkuRefundOrderDetail: 等同于SkuRefundSettlement

### SellerTradeDailySummary

* 原价: 正向汇总 
* 商家优惠: 正向汇总
* 平台优惠: 正向汇总
* 运费: 正向汇总
* 运费优惠: 正向汇总
* 平台实收: 正向汇总-逆向汇总
* 支付平台佣金: 正向汇总
* 电商平台佣金: 正向汇总
* 商家应收: 正向-逆向

### PlatformTradeDailySummary: 等同于SellerTradeDailySummary
