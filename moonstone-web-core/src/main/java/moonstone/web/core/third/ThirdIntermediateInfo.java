package moonstone.web.core.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntermediateInfoMatchingTypeEnum;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.MyLog;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.SkuReadService;
import moonstone.web.core.component.intermediate.IntermediateInfoComponent;
import moonstone.web.core.model.IntermediateInfoData;
import moonstone.web.core.model.dto.IntermediateInfoDTO;
import moonstone.web.core.user.application.IdentitySpecifyController;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * Date: 2019/8/22 19:17
 */
@RestController
@Slf4j
@RequestMapping("/api/third/info")
public class ThirdIntermediateInfo {

    @Resource
    private IntermediateInfoReadService intermediateInfoReadService;

    @Resource
    private SkuReadService skuReadService;

    @Autowired
    IdentitySpecifyController identitySpecifyController;

    @Autowired
    private IntermediateInfoComponent intermediateInfoComponent;

    @MyLog(title = "分销管理-佣金管理", value = "全局佣金信息变更", opBusinessType = OpBusinessType.UPDATE)
    @RequestMapping(value = "/shop-extra", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R setIntermediateInfo(@RequestBody IntermediateInfoDTO intermediateInfoDTO) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return R.error("user.not.login");
        }
        String errorMessage = checkForSetting(intermediateInfoDTO);
        if (StringUtils.isNotBlank(errorMessage)) {
            return R.error(errorMessage);
        }

        try {
            intermediateInfoDTO.getNormal().setType(ThirdIntermediateType.SHOP.value());
            if (intermediateInfoDTO.getActivity() != null) {
                intermediateInfoDTO.getActivity().setType(ThirdIntermediateType.SHOP.value());
            }

            intermediateInfoComponent.save(intermediateInfoDTO, user.getShopId());
            return R.ok();
        } catch (Exception ex) {
            log.error("ThirdIntermediateInfo.setIntermediateInfo error, request={}", JSON.toJSONString(intermediateInfoDTO), ex);
            return R.error(ex.getMessage());
        }
    }

    /**
     * 入参校验
     *
     * @param intermediateInfoDTO
     * @return
     */
    private String checkForSetting(IntermediateInfoDTO intermediateInfoDTO) {
        if (intermediateInfoDTO == null) {
            return "入参缺失";
        }
        if (intermediateInfoDTO.getNormal() == null) {
            return "通常配置信息不能为空";
        }

        var activity = intermediateInfoDTO.getActivity();
        if (activity != null) {
            var dateError = checkForDate(activity.getMatchingStartTimeString(), activity.getMatchingEndTimeString());
            if(!StringUtils.isBlank(dateError)){
                return dateError;
            }
        }

        return null;
    }

    private String checkForDate(String startTime, String endTime) {
        try {
            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                return "活动开始时间和活动结束时间皆不能为空";
            }

            Date start = DateUtils.parseDate(startTime, DateUtil.YMDHMS_FORMAT);
            Date end = DateUtils.parseDate(endTime, DateUtil.YMDHMS_FORMAT);

            if (!start.before(end)) {
                return "活动开始时间必须小于活动结束时间";
            }

            return null;
        } catch (Exception ex) {
            log.error("checkForDate error, startTime={}, endTime={}", startTime, endTime, ex);
            return "活动时间格式不正确";
        }
    }

    /**
     * 包装佣金相关设定数据进入到VO包
     *
     * @param intermediateInfo 佣金原数据
     */
    private IntermediateInfoData prettyIntermediateInfoIntoViewMode(IntermediateInfo intermediateInfo) {
        if (intermediateInfo == null) {
            return null;
        }
        IntermediateInfoData intermediateInfoData = new IntermediateInfoData();
        BeanUtils.copyProperties(intermediateInfo, intermediateInfoData);
        if (intermediateInfo.getExtraFeeCalMatrix() != null && !intermediateInfo.getExtraFeeCalMatrix().isEmpty()) {
            Function<String, Map<Long, BigDecimal>> splitNum = rawMatrix -> {
                Map<Long, BigDecimal> profitSetByLevel = new TreeMap<>();
                String[] numRom = rawMatrix.split(",");
                for (int i = 0; i < numRom.length; i++) {
                    profitSetByLevel.put((long) i + 1, new BigDecimal(numRom[i]).multiply(new BigDecimal("100")));
                }
                return profitSetByLevel;
            };
            String[] matrixRom = intermediateInfo.getExtraFeeCalMatrix().split("@");
            intermediateInfoData.setProfitRateByLevel(splitNum.apply(matrixRom[0]));
            if (matrixRom.length > 1) {
                intermediateInfoData.setStaticProfitByLevel(splitNum.apply(matrixRom[1]));
            }
        }

        intermediateInfoData.setMatchingEndTimeString(DateUtil.toString(intermediateInfo.getMatchingEndTime()));
        intermediateInfoData.setMatchingStartTimeString(DateUtil.toString(intermediateInfo.getMatchingStartTime()));
        return intermediateInfoData;
    }

    @RequestMapping(value = "/shop-extra/{shopId}", method = RequestMethod.GET)
    public R queryIntermediateInfoByShopId(@PathVariable Long shopId) {
        if (ObjectUtils.isEmpty(shopId)) {
            return R.error(-1, "shopId-缺少");
        }
        Either<List<IntermediateInfo>> listResult = findThirdAndType(shopId, ThirdIntermediateType.SHOP.value());
        if (!listResult.isSuccess() || listResult.take() == null) {
            return R.ok().add("data", null);
        }
        listResult.take().forEach(IntermediateInfo::checkFlag);

        var normal = listResult.take().stream()
                .filter(entity -> IntermediateInfoMatchingTypeEnum.NORMAL.getCode().equals(entity.getMatchingType()) ||
                        entity.getMatchingType() == null)
                .findAny()
                .orElse(null);
        var activity = listResult.take().stream()
                .filter(entity -> IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(entity.getMatchingType()))
                .findAny()
                .orElse(null);

        var map = new HashMap<>();
        map.put("normal", prettyIntermediateInfoIntoViewMode(normal));
        map.put("activity", prettyIntermediateInfoIntoViewMode(activity));
        return R.ok().add("data", map);
    }

    private Either<List<IntermediateInfo>> findThirdAndType(Long thirdId, Integer type) {
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findAllByThirdAndType(thirdId, type);
        if (!listResult.isSuccess() || listResult.take().isEmpty()) {
            log.error("findThirdAndType is fail thirdId:{} type:{}", thirdId, type);
            return Either.fail();
        }
        return Either.ok(listResult.take());
    }

    /**
     * 积分商品查询拉新佣金
     */
    public List<JSONObject> findRateByShopIdAndUserIdIntegral(Long thirdId, Long shopId) {
        List<JSONObject> profitForItemList = new ArrayList<>();

        Response<List<Sku>> listResponse = skuReadService.findSkusByItemId(thirdId);
        if (!listResponse.isSuccess() || listResponse.getResult().isEmpty()) {
            log.error("IntermediateInfo-integral is error thirdId:{}", thirdId);
            return profitForItemList;
        }
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(listResponse.getResult().get(0).getId(), ThirdIntermediateType.SKU.value());
        if (!listResult.isSuccess()) {
            log.error("IntermediateInfo-integral is error thirdId:{}", thirdId);
            return profitForItemList;
        }
        if (listResult.take().isEmpty() || Objects.equals(listResult.take().get(0).getIsCommission(), 0)) {
            Either<List<IntermediateInfo>> listResults = intermediateInfoReadService.findByThirdAndType(shopId, ThirdIntermediateType.SHOP.value());
            if (!listResults.isSuccess() || listResults.take().isEmpty()) {
                log.error("IntermediateInfo-integral shop is error thirdId:{}", thirdId);
                return profitForItemList;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("commission", listResults.take().get(0).getCommission());
            profitForItemList.add(jsonObject);
            return profitForItemList;
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("commission", listResult.take().get(0).getCommission());
            profitForItemList.add(jsonObject);
            return profitForItemList;
        }
    }
}
