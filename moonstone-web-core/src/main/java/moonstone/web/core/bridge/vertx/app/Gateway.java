package moonstone.web.core.bridge.vertx.app;

import com.danding.soul.client.common.result.RpcResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;

public interface Gateway {
    RpcResult<?> bridge(@RequestBody ApiParams apiParams) throws IOException;

    @AllArgsConstructor
    @Data
    @NoArgsConstructor
     class ApiParams {
        public String api;
        public String cookie;
        public Object parameter;
    }
}
