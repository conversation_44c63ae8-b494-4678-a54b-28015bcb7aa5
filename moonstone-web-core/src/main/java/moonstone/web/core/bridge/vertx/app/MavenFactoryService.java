package moonstone.web.core.bridge.vertx.app;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.Handler;
import io.vertx.ext.auth.impl.hash.SHA256;
import io.vertx.maven.MavenVerticleFactory;
import io.vertx.maven.ResolverOptions;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.web.core.bridge.vertx.model.VerticleService;
import moonstone.web.core.bridge.vertx.model.VerticleUpdateEvent;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.constants.EnvironmentConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@Slf4j
public class MavenFactoryService extends AbstractVerticle {
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    EnvironmentConfig environmentConfig;
    MavenVerticleFactory mavenVerticleFactory;
    ConcurrentHashMap<String, String> deploymentIdMap = new ConcurrentHashMap<>();

    @Override
    public void start() throws Exception {
        registerServiceFactory();
        // auto deploy service after service start
        vertx.setTimer(500L, ignore -> scanAndDeploy());
    }

    /**
     * scan all the service from mongo service to deploy it
     */
    private void scanAndDeploy() {

        vertx.<List<VerticleService>>executeBlocking(p -> p.complete(mongoTemplate.find(Query.query(Criteria.where("enable").is(true)), VerticleService.class)),
                serviceList -> {
                    for (VerticleService verticleService : serviceList.result()) {
                        if (!verticleService.getApp().contains(environmentConfig.getApp())) {
                            continue;
                        }
                        vertx.deployVerticle("maven:" + verticleService.getName(), res -> {
                            if (res.succeeded()) {
                                deploymentIdMap.put(getDeploymentCacheKey(verticleService.getName(), verticleService.getVersion()), res.result());
                                log.info("deploy {} => {}", verticleService.getName(), res.result());
                            } else {
                                log.error("{} fail to deploy [{}]", LogUtil.getClassMethodName(), verticleService, res.cause());
                                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("[Vertx]ServiceDeployFail"
                                        , verticleService + " Deploy fail"
                                        , res.cause()
                                        , EmailReceiverGroup.DEVELOPER));
                            }
                        });
                    }
                });
    }

    public String getDeploymentCacheKey(String name, String version) {
        return String.format("%s::%s-%s", name, version, environmentConfig.getApp());
    }

    /**
     * update the service to sync with newest version
     *
     * @param verticleUpdate verticle update event
     */
    @VertxEventBusListener(VerticleUpdateEvent.class)
    public void updateVerticle(VerticleUpdateEvent verticleUpdate) {
        String name = verticleUpdate.getName();
        String version = verticleUpdate.getVersion();
        String app = verticleUpdate.getApp();
        if (!app.contains(environmentConfig.getApp())) {
            return;
        }
        String key = getDeploymentCacheKey(name, version);

        Handler<Void> deployAction = nope -> vertx.deployVerticle("maven:" + name)
                .onComplete(res -> {
                    if (res.succeeded()) {
                        deploymentIdMap.put(String.format("%s::%s-%s", name, version, app), res.result());
                    } else {
                        log.error("{} fail to update Verticle => {}", LogUtil.getClassMethodName(), verticleUpdate, res.cause());
                        EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("[Vertx]ServiceDeployFail"
                                , verticleUpdate + " Deploy fail"
                                , res.cause()
                                , EmailReceiverGroup.DEVELOPER));
                    }
                });
        for (Map.Entry<String, String> verticleAndDeploymentId : deploymentIdMap.entrySet()) {
            if (verticleAndDeploymentId.getKey().equals(key)) {
                return;
            }
            if (verticleAndDeploymentId.getKey().startsWith(String.format("%s::%s", name, version))) {
                vertx.undeploy(verticleAndDeploymentId.getValue())
                        .onSuccess(deployAction)
                        .onFailure(error -> EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("[Vertx]ServiceUpdateFail"
                                , "Fail to undeploy " + verticleUpdate + " at [" + verticleAndDeploymentId + "] App[" + environmentConfig.getApp() + "]"
                                , error
                                , EmailReceiverGroup.DEVELOPER)));
                return;
            }
        }
        // deploy anyway if no deployment found
        deployAction.handle(null);
    }

    private void registerServiceFactory() {
        ResolverOptions resolverOptions = new ResolverOptions();
        resolverOptions.setRemoteRepositories(Collections.singletonList("http://mvn.yang800.cn/repository/maven-releases/"));
        mavenVerticleFactory = new MavenVerticleFactory(resolverOptions);
        vertx.getOrCreateContext().put("factory", mavenVerticleFactory);
        vertx.registerVerticleFactory(mavenVerticleFactory);
    }

    @PostMapping("/api/service/stop")
    public APIResp<Boolean> stop(HttpServletRequest request, String name, String app, String version, @RequestParam(required = false) String id) {
        checkSign(request);
        CompletableFuture<Void> undeployJob = new CompletableFuture<>();
        if (id == null) {
            id = deploymentIdMap.get(getDeploymentCacheKey(name, version));
        }
        vertx.undeploy(id)
                .onComplete(res -> {
                    if (res.succeeded()) {
                        undeployJob.complete(res.result());
                    } else {
                        undeployJob.completeExceptionally(res.cause());
                    }
                });
        try {
            undeployJob.join();
            return APIResp.ok(undeployJob.isDone());
        } catch (Exception e) {
            log.error("{} fail to stop Verticle[Id => {}, Name => {}], Version => {}, App => {}]", LogUtil.getClassMethodName(), id, name, app, version, e);
            return APIRespWrapper.error(e.getMessage());
        }
    }

    /**
     * 部署应用
     *
     * @param name       服务名称: local.service:moonstone:1.1.Release::local.service.Moonstone
     * @param app        部署的适用App范围
     * @param version    版本, 并非是上面的真实maven版本, 而是指服务上的版本, 用于热修
     * @param autoDeploy 是否自动部署(应用启动后)
     * @return 如果部署成功的部署Id
     */
    @PostMapping("/api/service/deploy")
    public APIResp<String> deploy(HttpServletRequest request, String name, String app, String version, @RequestParam(defaultValue = "false") Boolean autoDeploy) {
        checkSign(request);
        mongoTemplate.upsert(Query.query(Criteria.where("name").is(name))
                        .addCriteria(Criteria.where("version").is(version))
                , Update.update("app", new HashSet<>(Arrays.asList(app.split(","))))
                        .set("enable", autoDeploy)
                , VerticleService.class);
        if (app.isEmpty() || new HashSet<>(Arrays.asList(app.split(","))).contains(environmentConfig.getApp())) {
            CompletableFuture<String> completableFuture = new CompletableFuture<>();
            vertx.deployVerticle("maven:" + name)
                    .onComplete(res -> {
                        if (res.succeeded()) {
                            deploymentIdMap.put(getDeploymentCacheKey(name, version), res.result());
                            EventSender.publish(new VerticleUpdateEvent(name, app, version, new Date()));
                            completableFuture.complete(res.result());
                        } else {
                            completableFuture.completeExceptionally(res.cause());
                        }
                    });
            completableFuture.handle((deployId, ex) -> {
                if (Objects.isNull(deployId)) {
                    log.error("{} fail to deploy [{}]", LogUtil.getClassMethodName(), name + ":" + version, ex);
                } else {
                    log.info("{} success deploy [{} => {}]", LogUtil.getClassMethodName(), name + ":" + version, deployId);
                }
                return true;
            });
            try {
                return APIResp.ok(completableFuture.join());
            } catch (Exception e) {
                log.error("{} fail to deploy [{}]", LogUtil.getClassMethodName(), name + ":" + version, e);
                return APIRespWrapper.error(e.getMessage());
            }
        } else {
            EventSender.publish(new VerticleUpdateEvent(name, app, version, new Date()));
            return APIResp.ok(Translate.of("已插入数据库"));
        }
    }

    private void checkSign(HttpServletRequest request) {
        if (request.getHeaders("sign") == null) {
            log.warn("{} No Sign Found", LogUtil.getClassMethodName());
            throw new NullPointerException();
        }
        String sign = request.getHeader("sign");
        long turn = System.currentTimeMillis() / 8;
        int year = LocalDate.now().getYear();
        int day = LocalDate.now().getDayOfYear();
        String expect = new SHA256().hash(null, String.format("%s@%s-%s", day, year, turn)).toUpperCase();
        if (!sign.toUpperCase().equals(expect)) {
            log.error("{} expect [{}] but got [{}]", LogUtil.getClassMethodName(), expect, sign);
            throw new IllegalStateException();
        }
    }

    /**
     * 显示所有的服务
     *
     * @param name   名称
     * @param app    适用的环境
     * @param enable 可用
     * @return 服务列表
     */
    @PostMapping("/api/service/list")
    public APIResp<List<VerticleService>> listService(@RequestParam(required = false) String name
            , @RequestParam(required = false) String app
            , @RequestParam(defaultValue = "true") Boolean enable) {
        Query query = new Query();
        if (!ObjectUtils.isEmpty(name)) {
            query.addCriteria(Criteria.where("name").is(name));
        }
        if (!ObjectUtils.isEmpty(app)) {
            query.addCriteria(Criteria.where("enable").is(enable));
        }
        return APIResp.ok(mongoTemplate.find(query, VerticleService.class));
    }
}