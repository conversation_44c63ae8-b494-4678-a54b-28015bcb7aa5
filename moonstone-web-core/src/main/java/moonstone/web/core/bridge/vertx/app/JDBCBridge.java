package moonstone.web.core.bridge.vertx.app;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.zaxxer.hikari.HikariDataSource;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.Date;
import java.sql.*;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class JDBCBridge extends AbstractVerticle {
    @Autowired
    SqlSession sqlSession;
    @Autowired
    HikariDataSource dataSource;
    WorkerExecutor executor = null;
    ConcurrentHashMap<String, List<Object>> metaCache = new ConcurrentHashMap<>();

    Cache<JsonObject, Object> sqlCache = Caffeine.newBuilder().expireAfterWrite(3, TimeUnit.SECONDS).build();

    private Handler<Message<Object>> executeAtBlockingWorker(Consumer<Message<Object>> handler) {
        if (executor == null) {
            executor = vertx.createSharedWorkerExecutor("SQL-EXECUTOR", Runtime.getRuntime().availableProcessors() * 8);
        }
        return msg -> executor.executeBlocking(p -> {
            try {
                CompletableFuture.runAsync(() -> handler.accept(msg)).get(2800, TimeUnit.MILLISECONDS);
            } catch (TimeoutException timeoutException) {
                log.error("TIMEOUT AT SQL [{}]", msg.body(), timeoutException);
            } catch (Exception e) {
                log.error("Fail to Execute XML SQL [{}]", msg.body(), e);
                msg.fail(-1, e.getMessage());
            }
        });
    }

    @Override
    public void start() throws Exception {
        vertx.eventBus()
                .localConsumer("sql#execute", executeAtBlockingWorker(this::executeSql));
        vertx.eventBus()
                .localConsumer("sql.xml#list", executeAtBlockingWorker(this::xmlSqlQuery));
        vertx.eventBus()
                .localConsumer("sql.xml#one", executeAtBlockingWorker(this::xmlSqlQueryOne));
        vertx.eventBus()
                .localConsumer("sql.xml#update", executeAtBlockingWorker(this::xmlSqlUpdate));
        vertx.eventBus()
                .localConsumer("sql.xml#delete", executeAtBlockingWorker(this::xmlSqlDelete));
        vertx.eventBus()
                .localConsumer("sql.xml#insert", executeAtBlockingWorker(this::xmlSqlInsert));
        log.info("JDBC Bridge Start");
    }

    JsonArray trimArray(JsonArray array) {
        JsonArray copy = new JsonArray();
        for (var i : array) {
            if (i instanceof Number) {
                copy.add(((Number) i).longValue());
            } else if (i instanceof JsonArray) {
                copy.add(trimArray((JsonArray) i));
            } else if (i instanceof JsonObject) {
                copy.add(trimLong((JsonObject) i));
            } else copy.add(i);
        }
        return copy;
    }

    JsonObject trimLong(JsonObject object) {
        object = new JsonObject(new HashMap<>(object.getMap()));
        List<String> numberName = new ArrayList<>(8);
        List<String> jsonName = new ArrayList<>(8);
        List<String> jsonArray = new ArrayList<>(8);
        for (var a : object) {
            if (a.getValue() instanceof Number) {
                numberName.add(a.getKey());
            }
            if (a.getValue() instanceof JsonObject) {
                jsonName.add(a.getKey());
            }
            if (a.getValue() instanceof JsonArray) {
                jsonArray.add(a.getKey());
            }
        }
        for (var name : numberName) {
            object.put(name, object.getLong(name));
        }
        for (var json : jsonName) {
            object.put(json, trimLong(object.getJsonObject(json)));
        }
        for (var array : jsonArray) {
            object.put(array, trimArray(object.getJsonArray(array)));
        }

        return object;
    }

    private void xmlSqlInsert(Message<Object> msg) {
        JsonObject body = toJson(msg.body());
        body = trimLong(body);
        String sqlName = body.getString("sql");
        String ns = body.getString("ns");
        Buffer buffer = new JsonObject().put("result", sqlSession.insert(ns + "." + sqlName, body.getJsonObject("query").getMap())).toBuffer();
        msg.reply(buffer.toJson());
    }

    private void xmlSqlDelete(Message<Object> msg) {
        JsonObject body = toJson(msg.body());
        body = trimLong(body);
        String sqlName = body.getString("sql");
        String ns = body.getString("ns");
        Buffer buffer = new JsonObject().put("result", sqlSession.delete(ns + "." + sqlName, body.getJsonObject("query").getMap())).toBuffer();
        msg.reply(buffer.toJson());
    }

    private void xmlSqlUpdate(Message<Object> msg) {
        JsonObject body = toJson(msg.body());
        body = trimLong(body);
        String sqlName = body.getString("sql");
        String ns = body.getString("ns");
        Buffer buffer = new JsonObject().put("result", sqlSession.update(ns + "." + sqlName, body.getJsonObject("query").getMap())).toBuffer();
        msg.reply(buffer.toJson());
    }

    private void xmlSqlQueryOne(Message<Object> msg) {
        JsonObject body = toJson(msg.body());
        Object data = sqlCache.getIfPresent(body);
        if (data == null) {
            body = trimLong(body);
            String sqlName = body.getString("sql");
            String ns = body.getString("ns");
            data = sqlSession.selectOne(ns + "." + sqlName, body.getJsonObject("query").getMap());
        }
        if (data instanceof Number) {
            msg.reply(data);
        } else if (data instanceof String) {
            msg.reply(data);
        } else {
            msg.reply(JsonObject.mapFrom(data));
        }
    }

    JsonObject toJson(Object o) {
        if (o instanceof JsonObject) {
            return (JsonObject) o;
        }
        if (o instanceof Buffer) {
            return ((Buffer) o).toJsonObject();
        }
        if (o instanceof Map) {
            return new JsonObject((Map<String, Object>) o);
        }
        throw new RuntimeException("No JsonObject Found");
    }

    private void xmlSqlQuery(Message<Object> msg) {
        JsonObject body = toJson(msg.body());
        Object data = sqlCache.getIfPresent(body);
        if (data == null) {
            body = trimLong(body);
            String sqlName = body.getString("sql");
            String ns = body.getString("ns");
            JsonArray array = new JsonArray();
            for (Object rs : sqlSession.selectList(ns + "." + sqlName, body.getJsonObject("query").getMap())) {
                array.add(Json.decodeValue(Json.encode(rs)));
            }
            data = array;
        }
        msg.reply(data);
    }

    private <T> void executeSql(Message<Object> sql) {
        JsonObject body = toJson(sql.body());
        body = trimLong(body);
        String rawSql = body.getString("sql");
        //JsonObject query = sql.body().getJsonObject("query");
        JsonArray query = body.getJsonArray("query");
        Promise<Buffer> promise = Promise.promise();
        promise.future()
                .onSuccess(sql::reply)
                .onFailure(e -> log.error("Fail to Execute Sql [{}] Array [{}]", rawSql, query, e))
                .onFailure(e -> sql.fail(-1, e.getMessage()));
        try {
            query(promise, dataSource.getConnection(), rawSql, decodeTheJsonArray(query));
        } catch (Exception e) {
            promise.fail(e);
        }
    }

    List<Arg> decodeTheJsonArray(JsonArray array) {
        List<Arg> args = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            Object o = array.getList().get(i);
            Arg arg = new Arg();
            if (o instanceof String) {
                arg.type = Type.String;
            }
            if (o instanceof java.util.Date) {
                arg.type = Type.Date;
            }
            if (o instanceof BigDecimal) {
                arg.type = Type.BigDecimal;
            }
            if (o instanceof Float || o instanceof Double) {
                arg.type = Type.Double;
            }
            arg.object = o;
            args.add(arg);
        }
        return args;
    }

    public void query(Promise<Buffer> promise, Connection connection, String sql, List<Arg> args) {
        try {
            PreparedStatement statement = connection.prepareStatement(sql);
            for (int j = 0; j < args.size(); j++) {
                Arg arg = args.get(j);
                int i = j + 1;
                if (arg.object == null) {
                    statement.setObject(i, null);
                    continue;
                }
                switch (arg.type) {
                    case URL -> statement.setURL(i, (URL) arg.object);
                    case Object -> statement.setObject(i, arg.object);
                    case Array -> statement.setArray(i, (Array) arg.object);
                    case Blob -> statement.setBlob(i, (Blob) arg.object);
                    case Byte -> statement.setByte(i, (Byte) arg.object);
                    case Date -> statement.setDate(i, (Date) arg.object);
                    case Long -> statement.setLong(i, (Long) arg.object);
                    case Time -> statement.setTime(i, (Time) arg.object);
                    case Float -> statement.setFloat(i, (Float) arg.object);
                    case Double -> statement.setDouble(i, (Double) arg.object);
                    case String -> statement.setString(i, (String) arg.object);
                    case Boolean -> statement.setBoolean(i, (Boolean) arg.object);
                    case Integer -> statement.setInt(i, (Integer) arg.object);
                    case TimeStamp -> statement.setTimestamp(i, new Timestamp(Long.parseLong(arg.object.toString())));
                    case BigDecimal -> statement.setBigDecimal(i, new BigDecimal(arg.object.toString()));
                }
            }
            ResultSet resultSet = statement.executeQuery();
            promise.complete(extractResultSet(sql, resultSet));
        } catch (Exception e) {
            promise.fail(e);
        }
    }

    private Buffer extractResultSet(String sql, ResultSet resultSet) {
        List<String> name = new ArrayList<>();
        List<Integer> type = new ArrayList<>();
        try {
            if (metaCache.contains(sql)) {
                List<Object> cache = metaCache.get(sql);
                if ((Long) cache.get(2) > (System.currentTimeMillis() + 60 * 1000)) {
                    name = (List<String>) cache.get(0);
                    type = (List<Integer>) cache.get(1);
                }
            }
            if (name.isEmpty()) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int count = metaData.getColumnCount();
                for (int i = 0; i < count; i++) {
                    name.add(metaData.getColumnLabel(1 + i));
                    type.add(metaData.getColumnType(1 + i));
                }
            }
            metaCache.putIfAbsent(sql, Arrays.asList(name, type, System.currentTimeMillis()));
            JsonObject jsonObject = null;
            JsonArray array = null;
            while (resultSet.next()) {
                if (jsonObject != null && array == null) {
                    array = new JsonArray();
                    array.add(jsonObject);
                }
                if (array != null) {
                    array.add(jsonObject);
                }
                jsonObject = new JsonObject();
                for (int i = 0; i < name.size(); i++) {
                    Object v = resultSet.getObject(1 + i);
                    String n = name.get(i);
                    jsonObject.put(n, v);
                }
            }
            if (array == null) {
                return jsonObject == null ? Buffer.buffer() : jsonObject.toBuffer();
            }
            return array.toBuffer();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    private List<Arg> extractPlaceHolder(String sql, JsonObject arg) {
        Pattern pattern = Pattern.compile("#\\{([^}]*)}");
        Matcher matcher = pattern.matcher(sql);
        int count = matcher.groupCount();
        List<Arg> args = new ArrayList<>(count);
        //wrong
        while (matcher.find()) {
            String part = matcher.group(0);
            Arg a = new Arg();
            args.add(a);
            if (part.contains(":")) {
                a.type = Type.match(part.split(":")[0]);
                a.name = part.split(":")[1];
            } else {
                a.name = part;
            }
            switch (a.type) {
                case Double -> a.object = arg.getNumber(a.name).doubleValue();
                case Float -> a.object = arg.getNumber(a.name).floatValue();
                case BigDecimal -> a.object = new BigDecimal(arg.getNumber(a.name).toString());
                case Long, TimeStamp -> a.object = arg.getLong(a.name);
                case Integer -> a.object = arg.getInstant(a.name);
                case String, Blob, URL -> a.object = arg.getString(a.name);
                case Time, Date -> a.object = Time.from(Instant.ofEpochMilli(arg.getLong(a.name)));
                case Boolean -> a.object = arg.getBoolean(a.name);
                case Byte -> a.object = arg.getNumber(a.name).byteValue();
                case Object -> a.object = arg.getJsonObject(a.name);
                case Array -> a.object = arg.getJsonArray(a.name);
            }
        }
        return args;
    }

    enum Type {
        Long, String,
        Object, URL, Integer, Date, Boolean, Byte, Array, TimeStamp, Time, Double, Float, BigDecimal, Blob;
        static final Map<String, Type> typeMap = new LinkedHashMap<>();

        static Type match(String value) {
            value = value.toUpperCase(Locale.ROOT);
            if (typeMap.isEmpty()) {
                synchronized (typeMap) {
                    if (typeMap.isEmpty()) {
                        for (Type type : Type.values()) {
                            typeMap.put(type.toString().toUpperCase(Locale.ROOT), type);
                        }
                    }
                }
            }
            if (typeMap.containsKey(value)) {
                return typeMap.get(value);
            }
            for (Type type : typeMap.values()) {
                if (type.toString().toUpperCase(Locale.ROOT).startsWith(value)) {
                    return type;
                }
            }
            return Type.Object;
        }
    }

    static class Arg {
        Object object;
        String name;
        Type type = Type.Object;
    }
}
