package moonstone.web.core.bridge.vertx.app;

import blue.sea.moonstone.bridge.app.HttpDeliveryWrapper;
import blue.sea.moonstone.bridge.model.Request;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.AsyncResult;
import io.vertx.core.MultiMap;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Enumeration;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

@RestController
@Slf4j
public class VertxHttpBridgeController extends AbstractVerticle {
    public final static String ADDRESS = "HTTP.Bridge";

    @RequestMapping("/api/v3/{path}")
    public void gateway(@PathVariable String path, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        InputStream inputStream = request.getInputStream();
        for (int c = inputStream.read(); c != -1; c = inputStream.read()) {
            buff.write(c);
        }
        String api = request.getRequestURI().substring(request.getRequestURI().indexOf(path));
        CompletableFuture<Object> requestDone = new CompletableFuture<>();
        DeliveryOptions deliveryOptions = wrapDeliveryOptFromRequest(api, request);
        // inject the user into request, the user is read by session filter before the request is handle, won't interact with IO
        Optional.ofNullable(UserUtil.getUserId())
                .ifPresent(userId -> deliveryOptions.addHeader(Request.USER(), userId.toString()));
        vertx.eventBus().request(ADDRESS
                , buff.toByteArray()
                , deliveryOptions
                , result -> writeResponse(result, response, api, requestDone)
        );
        try {
            requestDone.get(120, TimeUnit.SECONDS);
        } catch (ExecutionException e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw Translate.exceptionOf(e.getCause().getMessage());
        }
    }

    private void writeResponse(AsyncResult<Message<Object>> result, HttpServletResponse response, String api, CompletableFuture<Object> requestDone) {
        vertx.executeBlocking(promise -> decodeAndFlushIntoResponse(promise, result, response, api),
                res -> {
                    if (res.succeeded()) {
                        requestDone.complete(res.result());
                    } else {
                        requestDone.completeExceptionally(Translate.exceptionOf(res.cause().getMessage()));
                    }
                });
    }

    private void decodeAndFlushIntoResponse(Promise<Object> promise, AsyncResult<Message<Object>> result, HttpServletResponse response, String api) {
        try {
            if (!result.succeeded()) {
                promise.fail(result.cause());
                return;
            }
            byte[] rawData = (byte[]) result.result().body();
            setHeader(result.result().headers(), response);
            if (rawData != null) {
                response.getOutputStream().write(rawData);
            }
            promise.complete();
        } catch (Exception e) {
            log.error("{} fail to handle[path => {}]", LogUtil.getClassMethodName(), api, e);
            promise.fail(e);
        }
    }

    /**
     * wrap the default delivery options for message send
     *
     * @param path    request url
     * @param request http request
     * @return options
     */
    private DeliveryOptions wrapDeliveryOptFromRequest(String path, HttpServletRequest request) {
        DeliveryOptions deliveryOptions = new DeliveryOptions().addHeader(".path", path)
                .setSendTimeout(100 * 1000);
        // add query
        if (request.getQueryString() != null) {
            deliveryOptions.addHeader(".query", request.getQueryString());
        }
        // add http header
        Enumeration<String> headers = request.getHeaderNames();
        while (headers != null && headers.hasMoreElements()) {
            String header = headers.nextElement();
            Enumeration<String> headerValues = request.getHeaders(header);
            while (headerValues != null && headerValues.hasMoreElements()) {
                deliveryOptions.addHeader(header, headerValues.nextElement());
            }
        }
        return deliveryOptions;
    }

    private boolean isNotByteReturnType(MultiMap headers) {
        return !Optional.ofNullable(headers.get(".type"))
                .filter(Predicate.isEqual("byte"))
                .isPresent();
    }

    /**
     * fulfill the header
     *
     * @param headers  信息头部
     * @param response 回调
     */
    private void setHeader(MultiMap headers, HttpServletResponse response) {
        headers.forEach(entry -> {
            if (entry.getKey().startsWith("_") || entry.getKey().startsWith(".")) {
                return;
            }
            response.addHeader(entry.getKey(), entry.getValue());
        });
    }

    @Override
    public void start() throws Exception {
        // won't accept the message from other node
        vertx.eventBus().localConsumer(ADDRESS, this::bridge);
        log.debug("{} Bridge started", LogUtil.getClassMethodName());
    }

    /**
     * bridge the http into message eventbus
     *
     * @param data message
     */
    private void bridge(Message<Buffer> data) {
        vertx.eventBus().request("http:" + data.headers().get(".path"), data.body(),
                HttpDeliveryWrapper.wrapOption(data.headers(), data.address()),
                res -> {
                    if (res.succeeded()) {
                        data.reply(res.result().body(), new DeliveryOptions().setHeaders(res.result().headers()));
                    } else {
                        log.error("{} fail to reply at bridge[Address => {}, ReplyAddress => {}, Header => {}]", LogUtil.getClassMethodName("Bridge Reply"),
                                data.address(), data.replyAddress(), data.headers() == null ? null : data.headers().toString().replace("\n", ", "), res.cause());
                        data.fail(-1, res.cause().getMessage());
                    }
                });
    }

    @Override
    public void stop() throws Exception {
        log.debug("{} Bridge stop", LogUtil.getClassMethodName());
    }
}
