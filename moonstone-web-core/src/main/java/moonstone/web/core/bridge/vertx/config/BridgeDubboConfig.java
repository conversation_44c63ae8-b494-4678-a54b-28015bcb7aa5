package moonstone.web.core.bridge.vertx.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class BridgeDubboConfig {

    //@Bean
    //@ConditionalOnClass(name = "org.apache.dubbo.config.spring.beans.factory.annotation.ServiceClassPostProcessor")
    public Object dubboBridgeToVertx() throws Exception{
        return Class.forName("org.apache.dubbo.config.spring.beans.factory.annotation.ServiceClassPostProcessor")
                .getConstructor(String.class)
                .newInstance("moonstone.web.core.bridge.vertx.app.dubbo");
    }
}
