package moonstone.web.core.bridge.vertx.model;

import blue.sea.moonstone.bridge.app.HttpServletVerticle;
import blue.sea.moonstone.bridge.model.Request;
import io.terminus.common.model.Response;
import io.vertx.core.Future;
import io.vertx.core.Vertx;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.Fuc;
import moonstone.common.utils.R;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.component.ParanaUserWrapper;

import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

public interface BridgeInject {
    ParanaUserWrapper getParanaUserWrapper();

    Vertx getVertx();

    /**
     * execute a io work at vertx worker and reply it to request as Json
     *
     * @param request http request
     * @param action  which action that accept a user
     * @param <T>     return type
     * @return for further use
     */
    default <T> Future<Void> json(Request request, Function<CommonUser, T> action) {
        return getVertx().executeBlocking(promise -> {
            try {
                request.user().map(Long::parseLong).map(userId -> getParanaUserWrapper().wrap(userId, null))
                        .foreach(user -> Fuc.with(UserUtil::putCurrentUser).apply(user));
                request.json(action.apply(UserUtil.getCurrentUser()));
            } catch (Exception e) {
                request.fail(e);
            } finally {
                UserUtil.clearCurrentUser();
                promise.complete();
            }
        });
    }

    default <T> Consumer<Request> json(BiFunction<Request, CommonUser, T> action) {
        return request -> getVertx().executeBlocking(promise -> {
            try {
                request.user().map(Long::parseLong).map(userId -> getParanaUserWrapper().wrap(userId, null))
                        .foreach(user -> Fuc.with(UserUtil::putCurrentUser).apply(user));
                request.json(action.apply(request, UserUtil.getCurrentUser()));
            } catch (Exception e) {
                request.fail(e);
            } finally {
                UserUtil.clearCurrentUser();
                promise.complete();
            }
        });
    }

    default <T, A> Consumer<Request> json(Class<A> argumentType, BiFunction<A, CommonUser, T> action) {
        return request -> getVertx().executeBlocking(promise -> {
            try {
                request.user().map(Long::parseLong).map(userId -> getParanaUserWrapper().wrap(userId, null))
                        .foreach(user -> Fuc.with(UserUtil::putCurrentUser).apply(user));
                request.json(action.apply(HttpServletVerticle.GSON().fromJson(request.reader(), argumentType), UserUtil.getCurrentUser()));
            } catch (Exception e) {
                request.fail(e);
            } finally {
                UserUtil.clearCurrentUser();
                promise.complete();
            }
        });
    }

    default <T> T result(Response<T> response) {
        if (response.isSuccess()) {
            return response.getResult();
        }
        throw Translate.exceptionOf(response.getError());
    }


    default Object result(R response) {
        if (response.get("data") != null) {
            return response.get("data");
        }
        throw Translate.exceptionOf(response.get("msg").toString());
    }

    default <T> T result(Result<T> result) {
        if (result.isSuccess()) {
            return result.getData();
        }
        throw Translate.exceptionOf(result.getErrorMsg());
    }

    default <T> T result(APIResp<T> res) {
        if (res.ok()) {
            return res.getData();
        }
        throw Translate.exceptionOf(res.getErrorMsg());
    }
}