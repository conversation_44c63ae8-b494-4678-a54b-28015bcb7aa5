package moonstone.web.core.bridge.vertx.web;

import blue.sea.moonstone.bridge.app.HttpServletVerticle;
import blue.sea.moonstone.bridge.model.Request;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Profile("dev")
public class Abc extends HttpServletVerticle {
    @Override
    public void start() throws Exception {
        serve("abc", this::tellSession);
    }

    void tellSession(Request request) {
        log.debug("{} tell me {}", LogUtil.getClassMethodName(), request.toString());
        request.asyncSession().putValue("me", request.query().getOrDefault("who", "yes"))
                .onSuccess(res ->
                        request.asyncSession().getValue("me")
                                .onSuccess(request::string));
    }
}
