package moonstone.web.core.bridge.vertx.app.dubbo;

import blue.sea.moonstone.bridge.model.Request;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.dto.UserInfoDTO;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.client.core.UserClient;
import com.danding.ucenter.core.api.user.model.UserModel;
import com.danding.ucenter.core.api.user.query.UserQuery;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.MultiMap;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.user.cache.ThirdPartyUserCache;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.web.core.AppConstants;
import moonstone.web.core.bridge.vertx.app.Gateway;
import moonstone.web.core.bridge.vertx.app.VertxHttpBridgeController;
import moonstone.web.core.component.api.CrossLoginService;
import moonstone.web.core.session.SessionConfig;
import moonstone.web.core.session.SessionManager;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Predicate;

@DubboService
@RestController
@Slf4j
public class SpecialGateWayBridge implements Gateway {

    @Autowired
    private Vertx vertx;
    @Autowired
    private CrossLoginService crossLoginService;
    @Autowired
    private ThirdPartyUserCache thirdPartyUserCache;
    @Autowired
    private SessionManager sessionManager;
    @Autowired
    private SessionConfig sessionConfig;

    Gson GSON = new Gson();


    @SoulClient(path = "/bridge", desc = "桥接")
    @RequestMapping("/bridge")
    public RpcResult<?> bridge(@RequestBody ApiParams apiParams) throws IOException {
        ByteArrayOutputStream response = new ByteArrayOutputStream();
        long start = System.currentTimeMillis();
        try {
            String api = apiParams.getApi();
            if (api == null) {
                return RpcResult.error(Translate.of("请指定调用的API"));
            }
            CompletableFuture<Void> requestDone = new CompletableFuture<>();
            DeliveryOptions optWithHeader = wrapApiPathWithCookie(apiParams.getCookie(), api);
            // inject login user into request
            Optional.ofNullable(UserUtil.getUserId())
                    .ifPresent(userId -> optWithHeader.addHeader(Request.USER(), userId.toString()));
            Optional.ofNullable(apiParams.getCookie())
                    .map(this::extractSessionCookie)
                    .map(sessionManager::getSession)
                    .map(session -> session.getAttribute(AppConstants.SESSION_USER_ID))
                    .ifPresent(userId -> optWithHeader.addHeader(Request.USER(), userId.toString()));
            Optional.ofNullable(UserInfoContext.getInstance().getUserInfo())
                    .map(UserInfoDTO::getUserId)
                    .map(userId -> thirdPartyUserCache.findThirdPartyUser(ThirdPartyUserType.OMS.getType(), userId.toString()).orElseGet(() -> syncShop(userId)))
                    .map(ThirdPartyUser::getUserId)
                    .ifPresent(userId -> optWithHeader.addHeader(Request.USER(), userId.toString()));
            vertx.eventBus().<byte[]>request(VertxHttpBridgeController.ADDRESS
                    , apiParams.getParameter() == null ? null : GSON.toJson(apiParams.parameter).getBytes(StandardCharsets.UTF_8)
                    , optWithHeader
                    , result -> reply(api, result, response, requestDone)
            );
            requestDone.get(120, TimeUnit.SECONDS);
        } catch (TimeoutException t) {
            log.error("{} system time out[{}]", LogUtil.getClassMethodName(), Json.toJson(apiParams), t);
            response.write(GSON.toJson(RpcResult.error(-5, Translate.of("系统超时, 请重试"))).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("{} system fail[{}]", LogUtil.getClassMethodName(), Json.toJson(apiParams), e);
            response.write(GSON.toJson(RpcResult.error(-1, e.getCause() == null ? e.getMessage() : e.getCause().getMessage())).getBytes(StandardCharsets.UTF_8));
        }finally {
            log.debug("{} bridge cost => {}", LogUtil.getClassMethodName(), System.currentTimeMillis() - start);
        }
        return GSON.fromJson(response.toString(), RpcResult.class);
    }

    private ThirdPartyUser syncShop(Long userId) {
        try {
            UserQuery query = new UserQuery();
            query.setId(new Long[]{userId});
            UserModel userModel = UserClient.getInstance().get(query);
            return crossLoginService.syncUser(userId.toString(), userModel.getMobile(), userModel.getUserName()).take();
        } catch (Exception e) {
            log.warn("{} user[Id => {}] not found", LogUtil.getClassMethodName(), userId, e);
            return null;
        }
    }

    private String extractSessionCookie(String cookie) {
        if (cookie == null || cookie.isEmpty()) {
            return null;
        }
        for (String subCookie : cookie.split(";")) {
            if (subCookie.startsWith(sessionConfig.getCookieName() + "=")) {
                return subCookie.substring(sessionConfig.getCookieName().length() + 1);
            }
        }
        return null;
    }

    /**
     * reply the data into request
     *
     * @param api         api
     * @param result      result dto
     * @param response    response
     * @param requestDone the complete job
     */
    private void reply(String api, AsyncResult<Message<byte[]>> result, OutputStream response, CompletableFuture<Void> requestDone) {
        vertx.<Void>executeBlocking(promise -> {
                    try {
                        decodeAndReply(promise, result, response);
                    } catch (Exception e) {
                        log.error("{} fail to handle[path => {}]", LogUtil.getClassMethodName(), api, e);
                        promise.fail(e);
                    }
                },
                jobComplete -> {
                    if (jobComplete.succeeded()) {
                        requestDone.complete(jobComplete.result());
                    } else {
                        requestDone.completeExceptionally(jobComplete.cause());
                    }
                });
    }

    /**
     * decode the result and reply at response
     *
     * @param promise  complete the result
     * @param result   data result
     * @param response response
     * @throws IOException io error
     */
    private void decodeAndReply(Promise<Void> promise, AsyncResult<Message<byte[]>> result, OutputStream response) throws IOException {
        if (!result.succeeded()) {
            promise.fail(result.cause());
            return;
        }
        byte[] rawData = result.result().body();
        boolean isStr = isNotByteReturnType(result.result().headers());
        //setHeader(result.result().headers(), response);
        if (isStr) {
            if (rawData != null) {
                if (new String(rawData).equals("null")) {
                    response.write(GSON.toJson(RpcResult.success(null)).getBytes(StandardCharsets.UTF_8));
                } else {
                    response.write(GSON.toJson(RpcResult.success(GSON.fromJson(new String(rawData), JsonObject.class))).getBytes(StandardCharsets.UTF_8));
                }
            }
        } else {
            response.write(GSON.toJson(RpcResult.success(new String(rawData))).getBytes(StandardCharsets.UTF_8));
        }
        promise.complete();
    }

    private boolean isNotByteReturnType(MultiMap headers) {
        return !Optional.ofNullable(headers.get(".type"))
                .filter(Predicate.isEqual("byte"))
                .isPresent();
    }

    private DeliveryOptions wrapApiPathWithCookie(String cookie, String api) {
        DeliveryOptions options = new DeliveryOptions();
        if (cookie != null) {
            for (String subCookie : cookie.split(";")) {
                options.addHeader("Cookie", subCookie);
            }
        }
        options.addHeader(".path", api);
        return options;
    }
}