package moonstone.web.core.bridge.vertx.model;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Set;

@Data
public class VerticleService {
    @Id
    String id;
    String name;
    /**
     * should deploy at app
     */
    Set<String> app;
    /**
     * enable or disable
     * only deploy enable verticle
     */
    Boolean enable;
    /**
     * tell the version of maven
     */
    String version;
    Long createdAt;
}
