package moonstone.web.core.distribution;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.weShop.model.WeWithdrawDetail;
import moonstone.weShop.service.WeWithdrawDetailReadService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author:  CaiZhy
 * Date:    2019/2/15
 */
@Slf4j
@RestController
@RequestMapping("/api/weWithdrawDetail")
public class CoreWeWithdrawDetails {
    @RpcConsumer
    private WeWithdrawDetailReadService weWithdrawDetailReadService;

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WeWithdrawDetail findById(@PathVariable Long id) {
        try {
            Response<WeWithdrawDetail> response = weWithdrawDetailReadService.findById(id);
            if (!response.isSuccess()) {
                log.error("failed to find weWithdraw detail by id={}, error code: {}", id, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to find weWithdraw detail by id={}, cause: {}", id, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }
}
