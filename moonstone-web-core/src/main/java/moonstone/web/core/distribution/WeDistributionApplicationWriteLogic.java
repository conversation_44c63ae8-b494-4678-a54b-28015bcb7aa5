package moonstone.web.core.distribution;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.weDistributionApplication.enums.WeDistributionApplicationStatus;
import moonstone.weDistributionApplication.model.WeDistributionApplication;
import moonstone.weDistributionApplication.service.WeDistributionApplicationReadService;
import moonstone.weDistributionApplication.service.WeDistributionApplicationWriteService;
import moonstone.weShop.enums.WeShopStatus;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountWriteService;
import moonstone.weShop.service.WeShopWriteService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by <PERSON>aiZhy on 2018/12/12.
 */
@Slf4j
@Component
public class WeDistributionApplicationWriteLogic {
    @RpcConsumer
    private WeShopReadService weShopReadService;

    @RpcConsumer
    private WeShopWriteService weShopWriteService;

    @RpcConsumer
    private WeDistributionApplicationReadService weDistributionApplicationReadService;

    @RpcConsumer
    private WeDistributionApplicationWriteService weDistributionApplicationWriteService;

    @RpcConsumer
    private WeShopShopAccountWriteService weShopShopWriteService;

    @Transactional
    public void agreeApply(WeDistributionApplication weDistributionApplication){
        if (weDistributionApplication.getStatus() != WeDistributionApplicationStatus.AUDITING.getValue()){
            log.error("weDistributionApplication(id={}, status={}) not allowed to agree",
                    weDistributionApplication.getId(), weDistributionApplication.getStatus());
            throw new JsonResponseException("weDistributionApplication.status.not.allowed.to.agree");
        }
        Response<Boolean> response = weDistributionApplicationWriteService.updateStatus(
                weDistributionApplication.getId(), WeDistributionApplicationStatus.AGREED.getValue());
        if (!response.isSuccess() || !response.getResult()){
            log.error("failed to update weDistributionApplication(id={}) status to agree, error code: {}", weDistributionApplication, response.getError());
            throw new JsonResponseException(response.getError());
        }
        //创建微分销店铺关联及账户关系
        createWeShopShop(weDistributionApplication);
        //如果微分销店铺状态为未激活，则变为正常
        Response<WeShop> rWeShop = weShopReadService.findById(weDistributionApplication.getWeShopId());
        if (!rWeShop.isSuccess()) {
            log.error("failed to find weShop by id={}, error code: {}", weDistributionApplication.getWeShopId(), rWeShop.getError());
            throw new JsonResponseException(rWeShop.getError());
        }
        if (rWeShop.getResult().getStatus() == WeShopStatus.NOT_ACTIVE.getValue()){
            Response<Boolean> weShopUpdateResponse = weShopWriteService.updateStatus(weDistributionApplication.getWeShopId(), WeShopStatus.NORMAL.getValue());
            if (!weShopUpdateResponse.isSuccess() || !weShopUpdateResponse.getResult()) {
                log.error("failed to update weShop(id={}) status to {}, error code: {}",
                        weDistributionApplication.getWeShopId(), WeShopStatus.NORMAL.getValue(), weShopUpdateResponse.getError());
                throw new JsonResponseException(weShopUpdateResponse.getError());
            }
        }
    }

    @Transactional
    public void rejectApply(WeDistributionApplication weDistributionApplication, String auditingRemark){
        if (weDistributionApplication.getStatus() != WeDistributionApplicationStatus.AUDITING.getValue()){
            log.error("weDistributionApplication(id={}, status={}) not allowed to reject",
                    weDistributionApplication.getId(), weDistributionApplication.getStatus());
            throw new JsonResponseException("weDistributionApplication.status.not.allowed.to.reject");
        }
        WeDistributionApplication toUpdate = new WeDistributionApplication();
        toUpdate.setId(weDistributionApplication.getId());
        toUpdate.setAuditingRemark(auditingRemark);
        toUpdate.setStatus(WeDistributionApplicationStatus.REJECTED.getValue());
        Response<Boolean> response = weDistributionApplicationWriteService.update(toUpdate);
        if (!response.isSuccess() || !response.getResult()){
            log.error("failed to update weDistributionApplication({}), error code: {}", weDistributionApplication, response.getError());
            throw new JsonResponseException(response.getError());
        }
    }

    private Long createWeShopShop(WeDistributionApplication weDistributionApplication){
        WeShopShopAccount weShopShop = new WeShopShopAccount();
        weShopShop.setWeShopId(weDistributionApplication.getWeShopId());
        weShopShop.setShopId(weDistributionApplication.getShopId());
        weShopShop.setUserId(weDistributionApplication.getUserId());
        weShopShop.setBalance(0L);
        weShopShop.setWithdraw(0L);
        weShopShop.setTotalProfitAmount(0L);
        weShopShop.setRecommendType(weDistributionApplication.getRecommendType());
        weShopShop.setRecommendShopId(weDistributionApplication.getRecommendShopId());
        weShopShop.setRecommendUserId(weDistributionApplication.getRecommendUserId());
        weShopShop.setStatus(1);
        Response<Long> response = weShopShopWriteService.create(weShopShop);
        if (!response.isSuccess()){
            log.error("failed to create weShopShop({}), error code: {}", weShopShop, response.getError());
            throw new JsonResponseException(response.getError());
        }
        return response.getResult();
    }
}
