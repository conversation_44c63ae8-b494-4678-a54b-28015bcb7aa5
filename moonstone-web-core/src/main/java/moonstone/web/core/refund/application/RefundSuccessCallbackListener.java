package moonstone.web.core.refund.application;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.domain.RefundDomain;
import moonstone.order.service.RefundReadService;
import moonstone.web.core.refund.event.RefundSuccessCallbackEvent;
import moonstone.web.core.refund.service.RefundCallBackService;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.Date;
import java.util.concurrent.locks.Lock;

/**
 * <AUTHOR>
 * RefundSuccessCallbackListener, react at refund callback success happen
 * @see RefundCallBackService#refundCallBack(String, Date) refund success, send the application event
 * @see moonstone.order.model.domain.RefundDomain#refundSuccess(String, Date) refund success, change the refund entity
 */
@Slf4j
@Component
@AllArgsConstructor
public class RefundSuccessCallbackListener {
    private final RefundCallBackService refundCallBackService;
    private final RefundReadService refundReadService;

    private final RedissonClient redissonClient;

    /**
     * refund success callback, when refund callback success trigger, send this event.
     * push the refund status flow into next status
     *
     * @param refundSuccessCallbackEvent callback event
     */
    @EventListener(RefundSuccessCallbackEvent.class)
    public void refundSuccessCallback(RefundSuccessCallbackEvent refundSuccessCallbackEvent) {
        Lock lock = redissonClient.getLock(RefundDomain.class.getSimpleName() + "#" + refundSuccessCallbackEvent.getRefundId());
        lock.lock();
        try {
            refundCallBackService.refundCallBack(refundReadService.findById(refundSuccessCallbackEvent.getRefundId()).getResult().getOutId()
                    , Date.from(refundSuccessCallbackEvent.getHappenAt().atZone(ZoneId.systemDefault()).toInstant()));
        } finally {
            lock.unlock();
        }
    }
}
