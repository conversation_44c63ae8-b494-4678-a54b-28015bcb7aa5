package moonstone.web.core.refund.application;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.domain.RefundDomain;
import moonstone.order.service.RefundReadService;
import moonstone.web.core.component.order.OutSideRefundExecutor;
import moonstone.web.core.order.OrderReadLogic;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * cancel the order when refund agree to make sure commodity is safe when refund.
 * @see OutSideRefundExecutor real api to cancel order
 * @see moonstone.order.model.domain.RefundDomain#addActionChain(RefundDomain.Action, Consumer) cancel order slice will be inject by this
 * @see RefundDomain.Action execute at agree action
 */
@Slf4j
@Component
@AllArgsConstructor
public class RefundAgreeActionChainApplication {
    private final OrderReadLogic orderReadLogic;
    private final RefundReadService refundReadService;
    private final OutSideRefundExecutor outSideRefundExecutor;
    private final GongXiaoOrderRefundApp gongXiaoOrderRefundApp;

    /**
     * cancel order when refund is agreed
     * 1. refund agree occupy at self system, money will flow back if refund agree and take action of Refund.
     * 2. order of out system should understand the order will be refund(cancel).
     * 3. if order can't be cancel, the refund will break the Stock Flow Chain.
     * > nobody want customer pay no money but commodity be took
     * > require outer system reply [already cancel] [cancel success] [order not exist]
     *
     * @param refund refund being agreed
     * @throws RuntimeException if out system refund fail, break the refund actionChain
     */
    public void cancelOrderWhenAgreeRefund(Refund refund) {
        for (OrderRefund orderRefund : refundReadService.findOrderIdsByRefundId(refund.getId()).getResult()) {
            if (orderRefund.getOrderLevel() != OrderLevel.SHOP) {
                log.warn("{} refund[Id => {}] order[Id => {}] level[Id => {}] confused about cancel order"
                        , LogUtil.getClassMethodName(), refund.getId(), orderRefund.getId(), orderRefund.getOrderLevel());
                continue;
            }
            ShopOrder shopOrder = (ShopOrder) orderReadLogic.findOrder(orderRefund.getOrderId(), orderRefund.getOrderLevel());
            // won't cancel order if order has been shipped or confirmed
            // > how to recognize the order status ? code won't save order's origin status for now
            // todo add check order origin status, we skip the order origin status check by simplify the refund type and order cancel flow;
            // if it's refund after sale, won't execute the cancel order, that's impossible
            switch (Optional.ofNullable(Refund.RefundType.from(refund.getRefundType())).orElseThrow(() -> Translate.exceptionOf("无法识别退款类型"))) {
                case AFTER_SALE_REFUND:
                case AFTER_SALE_RETURN:
                    log.info("RefundAgreeActionChainApplication.cancelOrderWhenAgreeRefund, orderId={}, refundId={}, refundType={}, 无须供应链取消订单.",
                            orderRefund.getOrderId(), orderRefund.getRefundId(), refund.getRefundType());
                    continue;
                case ON_SALE_REFUND:
                default:
                    break;
            }
            
            var result = outSideRefundExecutor.executeRefund_OM(shopOrder);
            if (!Boolean.TRUE.equals(result.getResult())) {
                //throw Translate.exceptionOf("无法同意退款[订单Id=>%s], 因为仓库订单取消失败", orderRefund.getId());
                log.error("outSideRefundExecutor.executeRefund_OM error, errorMessage={}", result.getErrorMsg(), result.getError());
                throw new RuntimeException("请先在OMS端进行售后");
            }

            // 检测是否需要额外的退款逻辑
            gongXiaoOrderRefundApp.refundOrder(shopOrder);
        }
    }
}
