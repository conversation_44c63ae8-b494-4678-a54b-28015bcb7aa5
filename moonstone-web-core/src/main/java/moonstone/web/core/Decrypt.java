package moonstone.web.core;

import moonstone.common.utils.EncryptHelper;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.PayerInfoRecord;

import javax.swing.*;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.Base64;

public class Decrypt {
    public static void main(String[] args) throws Exception {
        String keyStoreFile = "D:\\project\\mall\\moonstone-web-core\\src\\main\\resources\\online.keystore";

        var payerName = "9kaPE/1aYSkSMct4lVv8sA==";
        var payerNo = "Ji9TD+i2fGjTBPR7ivkwE7JZllTDrUokyAV7dJFd670=";
        var password = "ATbhnuv27v9Kl5AsDgy1KCOOomuJUoDziP7uAqgn6m/Ii/aBwlg6IvlHQqw8jQDY";

        KeyStore store = KeyStore.getInstance(KeyStore.getDefaultType());
        store.load(new FileInputStream(keyStoreFile), "online-secret".toCharArray());
        EncryptHelper.instance.keyStore(store);
        EncryptHelper.instance.secret("online-secret");

        var key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);

        PayerInfo info = new PayerInfoRecord(null, null, payerName.getBytes(StandardCharsets.UTF_8),
                payerNo.getBytes(StandardCharsets.UTF_8),
                password.getBytes(StandardCharsets.UTF_8), null).info();
        System.out.println(info);
        System.out.println(PayerInfo.Helper.decode(key, info));
    }
}
