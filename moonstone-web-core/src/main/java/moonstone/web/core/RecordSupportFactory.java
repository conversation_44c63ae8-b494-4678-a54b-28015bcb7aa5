package moonstone.web.core;

import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.apache.ibatis.reflection.factory.ObjectFactory;

import java.lang.reflect.Constructor;
import java.util.List;
import java.util.Properties;

public class RecordSupportFactory implements ObjectFactory {
    static final DefaultObjectFactory DEFAULT = new DefaultObjectFactory();

    @Override
    public void setProperties(Properties properties) {

    }

    @Override
    public <T> T create(Class<T> type) {
        if (type.isRecord()) {
            for (Constructor<?> constructor : type.getConstructors()) {
                if (constructor.getParameterCount() == 0) {
                    try {
                        return (T) constructor.newInstance();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            return null;
        } else {
            return DEFAULT.create(type);
        }
    }

    @Override
    public <T> T create(Class<T> type, List<Class<?>> constructorArgTypes, List<Object> constructorArgs) {
        if (type.isRecord()) {
            // if is record it should find the constructor first
            try {
                var c = type.getConstructor(constructorArgTypes.toArray(new Class[0]));
                return  (T) c.newInstance(constructorArgs.toArray());
            }catch (NoSuchMethodException noSuchMethodException) {
                // no constructor found, we got problem now, can't we read the properties name?
                return null;
            }
            catch (Exception e){
                throw new RuntimeException(e);
            }
        } else {
            return DEFAULT.create(type, constructorArgTypes, constructorArgs);
        }
    }

    @Override
    public <T> boolean isCollection(Class<T> type) {
        return DEFAULT.isCollection(type);
    }
}
