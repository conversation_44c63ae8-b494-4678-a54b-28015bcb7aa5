package moonstone.web.core.component.profit.view;


import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 订单利润显示
 */
public interface AppOrderProfitView {
    /**
     * @return 获取订单Id
     */
    Long getOrderId();

    /**
     * @return 购买的店铺名称
     */
    String getShopName();

    /**
     * @return 买家名字
     */
    String getBuyerName();

    /**
     * @return 买家的头像Url
     */
    String getBuyerAvatarUrl();

    /**
     * @return 订单内商品信息
     */
    List<AppOrderItemView> getItemList();

    /**
     * @return 卖出的价格
     */
    BigDecimal getSellerPrice();

    /**
     * @return 获取的利润
     */
    BigDecimal getProfit();

    /**
     * @return 订单的创建价格
     */
    Date getCreatedAt();

    /**
     * @return 获取商品数量
     */
    default Integer getItemCount() {
        return getItemList() == null ? 0 : getItemList().size();
    }
}
