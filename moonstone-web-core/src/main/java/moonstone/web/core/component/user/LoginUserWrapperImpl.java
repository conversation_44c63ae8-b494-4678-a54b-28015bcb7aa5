package moonstone.web.core.component.user;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.UserRole;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.user.StoreProxyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;


@Component
@Slf4j
public class LoginUserWrapperImpl implements LoginUserWrapper {
    @Autowired
    private StoreProxyManager storeProxyManager;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;


    @Override
    public <T extends BaseUser> T wrap(T userBase) {
        if (userBase == null) {
            return null;
        }
        try {
            HashSet<String> rolesSet = new HashSet<>();
            if (userBase.getRoles() != null) {
                for (String role : userBase.getRoles()) {
                    if (Objects.nonNull(role)) {
                        rolesSet.add(role);
                    }
                }
            }
            List<Long> proxyBelongShopIdList = storeProxyManager.getProxyShopIdFromUserId(userBase.getId());
            if (!CollectionUtils.isEmpty(proxyBelongShopIdList)) {
                rolesSet.add(UserRole.STORE_PROXY.name());
                // insert shopId for storeProxy
                if (userBase instanceof CommonUser) {
                    if (proxyBelongShopIdList.size() > 1) {
                        EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("警告店铺代理重复店铺Id", String.format("userId [%s] have multi-shopId [%s]", userBase.getId(), JSON.toJSONString(proxyBelongShopIdList)), EmailReceiverGroup.DEVELOPER));
                    }
                    CommonUser commonUser = (CommonUser) userBase;
                    proxyBelongShopIdList.stream().findFirst().ifPresent(commonUser::setShopId);
                }
            }

            weShopCacheHolder.findByUserId(userBase.getId()).ifPresent(weShop -> rolesSet.add(UserRole.WE_DISTRIBUTOR.name()));

            if (userBase instanceof CommonUser) {
                ((CommonUser) userBase).setRoles(new ArrayList<>());
            }
            if (userBase.getRoles() != null) {
                userBase.getRoles().clear();
                userBase.getRoles().addAll(rolesSet);
            }
            return userBase;
        } catch (Exception ex) {
            log.error("{} fail to wrap user", LogUtil.getClassMethodName(), ex);
            log.error("{} fail to wrap user[{}]", LogUtil.getClassMethodName(), userBase, ex);
            return userBase;
        }
    }
}
