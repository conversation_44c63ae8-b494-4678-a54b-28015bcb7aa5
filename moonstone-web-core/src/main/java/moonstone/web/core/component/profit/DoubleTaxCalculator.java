package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.component.item.component.TaxChecker;
import moonstone.web.core.model.dto.tax.PriceAndTax;
import moonstone.web.core.model.dto.tax.TaxSplitRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class DoubleTaxCalculator extends AbstractVerticle {
    @Resource
    private TaxChecker taxChecker;

    public String address() {
        return "trade.herald.service.DoubleTaxSplit";
    }


    /**
     * 执行拆分税
     */
    private final Handler<Message<Buffer>> splitDoubleTax = message -> {
        TaxSplitRequest request = JSON.parseObject(message.body().toString(), TaxSplitRequest.class);

        Handler<Promise<Object>> splitTaxAtWorking = promise -> {
            Either<List<PriceAndTax>> doubleTaxResult = splitDoubleTax(request.getSkuId(), request.getShopId(), request.getThirdPartySystem(), request.getOuterSkuId(), request.getPrice());
            if (doubleTaxResult.isSuccess()) {
                promise.complete(doubleTaxResult.take());
            } else {
                promise.fail(doubleTaxResult.getError());
            }
        };
        Handler<AsyncResult<Object>> replyResult = result -> {
            if (result.succeeded()) {
                message.reply(JSON.toJSONString(result.result()));
            } else {
                message.fail(1, result.cause().getMessage());
            }
        };
        vertx.executeBlocking(splitTaxAtWorking, replyResult);
    };


    @Override
    public void start() throws Exception {
        vertx.eventBus().consumer(address(), splitDoubleTax);
    }

    /**
     * 拆税
     *
     * @param shopId       店铺Id
     * @param thirdPartyId 三方Id
     * @param outerSkuId   三方SkuCode
     * @param priceList    价格列表
     * @return 税费结果
     */
    public Either<List<PriceAndTax>> splitDoubleTax(Long skuId, Long shopId, Integer thirdPartyId, String outerSkuId, List<Long> priceList) {
        List<PriceAndTax> noTax = Arrays.asList(new PriceAndTax(new BigDecimal(priceList.get(0)).divide(new BigDecimal("100"), 2, RoundingMode.DOWN), BigDecimal.ZERO)
                , new PriceAndTax(new BigDecimal(priceList.get(0)).divide(new BigDecimal("100"), 2, RoundingMode.DOWN), BigDecimal.ZERO));
        switch (ThirdPartySystem.fromInt(thirdPartyId)) {
            case Y800_V2, Y800_V3:
                List<PriceAndTax> priceAndTaxList = new ArrayList<>();
                for (Long price : priceList) {
                    Optional<TaxChecker.TaxSplitResult> resultOpt = taxChecker.splitTax(shopId, skuId, outerSkuId, BigDecimal.valueOf(price).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
                    if (resultOpt.isEmpty()) {
                        return Either.error(Translate.of("拆税失败, 请联系客服查询商品来源平台"));
                    }
                    priceAndTaxList.add(new PriceAndTax(resultOpt.get().getFee(), resultOpt.get().getTax()));
                }
                return Either.ok(priceAndTaxList);
            default:
                return Either.error(new Translate("不支持的拆税模式[%s] shopId[%s]", thirdPartyId, shopId).toString());
        }
    }
}
