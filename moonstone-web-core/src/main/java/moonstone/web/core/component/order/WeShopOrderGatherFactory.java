package moonstone.web.core.component.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.api.OrderGatherFactory;
import moonstone.order.model.GatherOrder;
import moonstone.order.model.OrderGather;
import moonstone.order.model.ShopOrder;

import java.util.Objects;
import java.util.Optional;

public class WeShopOrderGatherFactory implements OrderGatherFactory {

    @Override
    public Optional<ShopOrder> gather(OrderGather orderGather, ShopOrder shopOrder) {
        if (!(orderGather instanceof WeShopOrderGather))
            return Optional.of(shopOrder);
        // 采购限制 每个订单都一个独立采购单
        if (!orderGather.getGatheredOrderList().isEmpty())
            return Optional.of(shopOrder);
        WeShopOrderGather weShopOrderGather = (WeShopOrderGather) orderGather;
        Long weShopIdCarryByOrder = Optional.ofNullable(shopOrder.getOutShopId()).map(Long::valueOf).orElse(null);
        if (weShopOrderGather.getWeShopId() == null) {
            weShopOrderGather.setWeShopId(weShopIdCarryByOrder);
        }
        if (!Objects.equals(weShopIdCarryByOrder, weShopOrderGather.getWeShopId()))
            return Optional.of(shopOrder);
        orderGather.getGatheredOrderList().add(shopOrder);
        return Optional.empty();
    }

    @Override
    public OrderGather getTarget() {
        return new WeShopOrderGather(this);
    }

    @Override
    public OrderOutFrom outFrom() {
        return OrderOutFrom.WE_SHOP;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class WeShopOrderGather extends OrderGather {
        Long weShopId;

        WeShopOrderGather(OrderGatherFactory factory) {
            super.factory = factory;
        }

        @Override
        protected void decorate(GatherOrder gatherOrder) {
            gatherOrder.setOutShopId(weShopId == null ? null : weShopId.toString());
        }
    }
}
