package moonstone.web.core.component.pay.allinpay.constants;

public interface TrxStatusCode {

    //：交易成功
    String SUCCESS = "0000";
    //：交易不存在
    String TRADE_NOT_EXIST = "1001";

    // 交易处理中,请查询交易,如果是实时交易(例如刷卡支付,交易撤销,退货),建议每隔一段时间(10秒)查询交易
    String PROCESSING_2008 = "2008";
    String PROCESSING_2000 = "2000";

    // 3开头的错误码代表交易失败
    String REPEATED = "3888"; //-流水号重复
    String CONTROL_FAIL = "3889";//-交易控制失败，具体原因看errmsg
    String MERCHANT_ERROR = "3099";// -渠道商户错误
    String FEE_TOO_SMALL = "3014"; //-交易金额小于应收手续费
    String REAL_NAME_ERROR = "3031";//-校验实名信息失败
    String NOT_PAID = "3088"; //-交易未支付(在查询时间区间内未成功支付,如已影响资金24小时内会做差错退款处理)
    String CANCEL_ERROR = "3089"; //-撤销异常,如已影响资金24小时内会做差错退款处理
    String OTHER_ERROR = "3045";//-其他错误，具体原因看errmsg
    String TRADE_CANCELED = "3050"; //-交易已被撤销
    String OTHER_ERROR_3999 = "3999";//-其他错误，具体原因看errmsg

    static boolean isError(String code) {
        return TRADE_NOT_EXIST.equals(code) || code.startsWith("3");
    }
}
