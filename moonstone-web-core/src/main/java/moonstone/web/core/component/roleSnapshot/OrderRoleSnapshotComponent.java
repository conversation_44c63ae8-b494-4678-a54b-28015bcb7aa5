package moonstone.web.core.component.roleSnapshot;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.service.AccountStatementReadService;
import moonstone.order.service.OrderRoleSnapshotReadService;
import moonstone.order.service.OrderRoleSnapshotWriteService;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.web.core.component.roleSnapshot.event.OrderRoleSnapshotCreateEvent;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class OrderRoleSnapshotComponent {

    @Resource
    private OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    private OrderRoleSnapshotWriteService orderRoleSnapshotWriteService;

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Resource
    private AccountStatementReadService accountStatementReadService;

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    /**
     * 创建单据的角色快照信息
     *
     * @param event
     */
    @EventListener(OrderRoleSnapshotCreateEvent.class)
    public void asyncCreateSnapshot(OrderRoleSnapshotCreateEvent event) {
        if (event == null) {
            log.error("OrderRoleSnapshotComponent.asyncCreateSnapshot error, 入参为空");
            return;
        }

        createSnapshot(event.getOrderId(), event.getOrderType());
    }

    /**
     * 创建单据的角色快照信息
     *
     * @param orderId
     */
    public void createSnapshot(Long orderId, OrderRoleSnapshotOrderTypeEnum orderType) {
        if (orderId == null || orderType == null) {
            log.error("OrderRoleSnapshotComponent.createSnapshot error, 入参皆不能为空");
            return;
        }

        if (alreadyExisted(orderId, orderType)) {
            log.info("OrderRoleSnapshotComponent.createSnapshot, orderId={}, orderType={}, 已存在对应的快照信息, 不再处理",
                    orderId, orderType);
            return;
        }

        //构造
        var list = switch (orderType) {
            case WITHDRAW_APPLY -> buildForWithdrawApply(orderId);
            case ACCOUNT_STATEMENT -> buildForAccountStatement(orderId);
            default -> null;
        };

        //保存
        create(list);
    }

    /**
     * 为账单构造角色快照
     *
     * @param accountStatementId
     * @return
     */
    private List<OrderRoleSnapshot> buildForAccountStatement(Long accountStatementId) {
        var accountStatement = accountStatementReadService.findById(accountStatementId).getResult();
        if (accountStatement == null) {
            log.error("accountStatementId={}, 账单查询为空", accountStatementId);
            return Collections.emptyList();
        }

        var snapshot = convert(accountStatement.getShopId(), accountStatement.getUserId(),
                SubStoreUserIdentityEnum.parse(accountStatement.getUserRole()));
        if (snapshot == null) {
            return Collections.emptyList();
        }

        snapshot.setOrderType(OrderRoleSnapshotOrderTypeEnum.ACCOUNT_STATEMENT.getCode());
        snapshot.setShopOrderId(accountStatementId);
        return Lists.newArrayList(snapshot);
    }

    /**
     * 为提现单构造角色快照
     *
     * @param withdrawProfitApplyId
     * @return
     */
    private List<OrderRoleSnapshot> buildForWithdrawApply(Long withdrawProfitApplyId) {
        var apply = withDrawProfitApplyReadService.findById(withdrawProfitApplyId).getResult();
        if (apply == null) {
            log.error("withdrawProfitApplyId={}, 提现申请单查询为空", withdrawProfitApplyId);
            return Collections.emptyList();
        }

        var snapshot = convert(apply.getSourceId(), apply.getUserId(),
                SubStoreUserIdentityEnum.parse(apply.getUserRole()));
        if (snapshot == null) {
            return Collections.emptyList();
        }

        snapshot.setOrderType(OrderRoleSnapshotOrderTypeEnum.WITHDRAW_APPLY.getCode());
        snapshot.setShopOrderId(apply.getId());
        return Lists.newArrayList(snapshot);
    }

    /**
     * 构造单个用户的角色快照，若不指定报角色类型，则以导购、门店、服务商的顺序逐个尝试
     *
     * @param userId
     * @param userRole
     * @return
     */
    private OrderRoleSnapshot convert(Long shopId, Long userId, SubStoreUserIdentityEnum userRole) {
        if (userId == null) {
            return null;
        }

        if (userRole == null) {
            var result = convertGuider(userId, shopId);
            if (result != null) {
                return result;
            }

            result = convertSubStore(userId, shopId);
            if (result != null) {
                return result;
            }

            return convertServiceProvider(userId, shopId);
        } else {
            return switch (userRole) {
                case STORE_GUIDER -> convertGuider(userId, shopId);
                case SUB_STORE -> convertSubStore(userId, shopId);
                case SERVICE_PROVIDER -> convertServiceProvider(userId, shopId);
                default -> null;
            };
        }
    }

    private OrderRoleSnapshot convertServiceProvider(Long userId, Long shopId) {
        var serviceProvider = serviceProviderCache.findByMongo(userId, shopId);
        if (serviceProvider == null) {
            return null;
        }

        OrderRoleSnapshot snapshot = new OrderRoleSnapshot();

        snapshot.setIsValid(DataValidEnum.VALID.getCode());
        snapshot.setMobile(serviceProvider.getMobile());
        snapshot.setName(serviceProvider.getName());
        snapshot.setShopId(serviceProvider.getShopId());

        snapshot.setUserId(serviceProvider.getUserId());
        snapshot.setUserRole(SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode());
        snapshot.setCreatedBy(0L);
        snapshot.setUpdatedBy(0L);

        snapshot.setProvince(serviceProvider.getProvince());
        snapshot.setCity(serviceProvider.getCity());
        snapshot.setCounty(serviceProvider.getCounty());

        return snapshot;
    }

    private OrderRoleSnapshot convertSubStore(Long userId, Long shopId) {
        var subStore = subStoreReadService.findUserIdAndShopId(userId, shopId).getResult();
        if (subStore == null) {
            return null;
        }

        OrderRoleSnapshot snapshot = new OrderRoleSnapshot();

        snapshot.setIsValid(DataValidEnum.VALID.getCode());
        snapshot.setMobile(subStore.getMobile());
        snapshot.setName(subStore.getName());
        snapshot.setShopId(subStore.getShopId());

        snapshot.setUserId(subStore.getUserId());
        snapshot.setUserRole(SubStoreUserIdentityEnum.SUB_STORE.getCode());
        snapshot.setCreatedBy(0L);
        snapshot.setUpdatedBy(0L);

        snapshot.setProvince(subStore.getProvince());
        snapshot.setCity(subStore.getCity());
        snapshot.setCounty(subStore.getCounty());

        return snapshot;
    }

    private OrderRoleSnapshot convertGuider(Long userId, Long shopId) {
        var list = subStoreTStoreGuiderReadService.findByStoreGuiderUserId(userId, shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        var guider = list.get(0);

        OrderRoleSnapshot snapshot = new OrderRoleSnapshot();
        snapshot.setIsValid(DataValidEnum.VALID.getCode());
        snapshot.setMobile(guider.getStoreGuiderMobile());
        snapshot.setName(guider.getStoreGuiderNickname());
        snapshot.setShopId(shopId);

        snapshot.setUserId(guider.getStoreGuiderId());
        snapshot.setUserRole(SubStoreUserIdentityEnum.STORE_GUIDER.getCode());
        snapshot.setCreatedBy(0L);
        snapshot.setUpdatedBy(0L);

        return snapshot;
    }

    /**
     * 保存到数据库
     *
     * @param list
     */
    private void create(List<OrderRoleSnapshot> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var result = orderRoleSnapshotWriteService.create(list);
        if (!result.isSuccess() || !Boolean.TRUE.equals(result.getResult())) {
            log.error("OrderRoleSnapshotComponent.create, 快照信息保存失败， orderId={}, orderType={}, error={}",
                    list.get(0).getShopOrderId(), list.get(0).getOrderType(), result.getError());
        }
    }

    /**
     * 是否已经存在快照信息
     *
     * @param orderId
     * @param orderType
     * @return
     */
    private boolean alreadyExisted(Long orderId, OrderRoleSnapshotOrderTypeEnum orderType) {
        return !CollectionUtils.isEmpty(orderRoleSnapshotReadService.findMapByShopOrderIds(
                Lists.newArrayList(orderId), orderType));
    }
}
