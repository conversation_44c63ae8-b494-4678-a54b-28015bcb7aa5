package moonstone.web.core.component.order;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.AuthAble;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.component.item.component.TaxChecker;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.order.api.GatherOrderMaker;
import moonstone.order.api.OrderGatherFactory;
import moonstone.order.component.DefaultOrderGatherFactory;
import moonstone.order.component.PaymentAccountUtil;
import moonstone.order.dto.RichGatherOrder;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Component
public class GatherOrderMakerImpl implements GatherOrderMaker {
    private final OrderGatherFactory defaultFactory = new DefaultOrderGatherFactory();
    private final Map<OrderOutFrom, OrderGatherFactory> orderGatherMapByOrderOutFrom = new HashMap<>();
    @Autowired
    private SkuCustomReadService skuCustomReadService;
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private List<OrderGatherFactory> orderGatherFactoryList;
    @Autowired
    private TaxChecker taxChecker;

    @PostConstruct
    public void registerOrderGatherMap() {
        for (OrderGatherFactory orderGatherFactory : orderGatherFactoryList) {
            orderGatherMapByOrderOutFrom.put(orderGatherFactory.outFrom(), orderGatherFactory);
        }
    }

    @Override
    public Either<List<RichGatherOrder>> full(BaseUser buyer, List<ShopOrder> orderList) {
        HashMap<Long, List<ShopOrder>> orderMapByShopId = new HashMap<>();
        List<RichGatherOrder> gatherOrderList = new ArrayList<>();
        // split by shopId
        try {
            for (ShopOrder shopOrder : orderList) {
                if (!orderMapByShopId.containsKey(shopOrder.getShopId()))
                    orderMapByShopId.put(shopOrder.getShopId(), new ArrayList<>());
                orderMapByShopId.get(shopOrder.getShopId()).add(shopOrder);
            }
            // gather order
            for (Long shopId : orderMapByShopId.keySet()) {
                // split by different order gather
                Map<OrderOutFrom, List<OrderGather>> orderGatherByOutFrom = new HashMap<>();
                List<ShopOrder> shopOrderList = orderMapByShopId.get(shopId);
                for (ShopOrder shopOrder : shopOrderList) {
                    OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
                    if (!orderGatherByOutFrom.containsKey(outFrom)) {
                        OrderGather orderGather = orderGatherMapByOrderOutFrom.getOrDefault(outFrom, defaultFactory).getTarget();
                        List<OrderGather> orderGatherList = new ArrayList<>();
                        orderGatherList.add(orderGather);
                        orderGatherByOutFrom.put(outFrom, orderGatherList);
                    }
                    Optional<ShopOrder> leftOrder = Optional.of(shopOrder);
                    for (OrderGather orderGather : orderGatherByOutFrom.get(outFrom)) {
                        if (!leftOrder.isPresent()) break;
                        leftOrder = orderGather.gather(leftOrder.get());
                    }
                    if (leftOrder.isPresent()) {
                        // need a new order gather
                        OrderGather orderGather = orderGatherMapByOrderOutFrom.get(outFrom).getTarget();
                        if (orderGather.gather(leftOrder.get()).isPresent()) {
                            // error no order gain can hold this order
                            log.error("{} can't gather order[{}] by [{}] ", LogUtil.getClassMethodName(), shopOrder.getId(), outFrom);
                            return Either.error(new RuntimeException(String.format("订单[%s]无法被[%s]聚合", shopOrder.getId(), outFrom)));
                        }
                        orderGatherByOutFrom.get(outFrom).add(orderGather);
                    }
                }
                // 生成聚合订单
                for (OrderOutFrom outFrom : orderGatherByOutFrom.keySet()) {
                    for (OrderGather orderGather : orderGatherByOutFrom.get(outFrom)) {
                        gatherOrderList.add(orderGather.react(buyer, shopId, orderId -> skuOrderReadService.findByShopOrderId(orderId).getResult(), skuCacheHolder::findSkuById));
                    }
                }
            }
        } catch (Exception ex) {
            log.error("{} fail to create gather order", LogUtil.getClassMethodName(), ex);
            return Either.error(ex);
        }
        return Either.ok(gatherOrderList);
    }

    @Override
    public void charge(RichGatherOrder richGatherOrder) {
        // 由渠道承担的优惠金额
        long promotionFeeBearByShop = 0L;
        long originFee = 0L;
        long tax = 0L;
        LoadingCache<Long, SkuCustom> skuCustomCacheLoader = Caffeine.newBuilder().build((skuId) -> skuCustomReadService.findBySkuId(skuId));
        for (Long orderId : richGatherOrder.getSkuOrderMapByOrderId().keySet()) {
            boolean skipOrderGatherFee = false;
            for (Payment payment : paymentReadService.findByOrderIdAndOrderLevel(orderId, OrderLevel.SHOP).getResult()) {
                if (!Objects.equals(OrderStatus.PAID.getValue(), payment.getStatus()) || !PaymentAccountUtil.isOwnedWeChatPayment(payment.getPayRequest())) {
                    continue;
                }
                skipOrderGatherFee = true;
                break;
            }
            if (skipOrderGatherFee)
                continue;
            for (SkuOrder skuOrder : richGatherOrder.getSkuOrderMapByOrderId().get(orderId)) {
                // todo: 增加判断优惠是否由渠道承担
                originFee += richGatherOrder.getSkuMapById().get(skuOrder.getSkuId()).getPrice() * skuOrder.getQuantity();
                if (Objects.nonNull(skuOrder.getIsBonded()) && BondedType.fromInt(skuOrder.getIsBonded()).isBonded())
                    continue;
                Sku sku = new Sku();
                BeanUtils.copyProperties(skuCacheHolder.findSkuById(skuOrder.getSkuId()), sku);
                if (!Optional.ofNullable(skuCustomCacheLoader.get(sku.getId())).filter(skuCustom -> Objects.equals(skuCustom.getCustomTaxHolder(), 1)).isPresent())
                    continue;
                sku.setPrice((int) ((skuOrder.getDiscount() + skuOrder.getFee()) / skuOrder.getQuantity()));
                tax += Optional.ofNullable(taxChecker.getTax(sku, skuOrder.getQuantity()))
                        .orElseThrow(() -> new RuntimeException(Translate.of("订单[%s]的单品[%s]税费计算失败", skuOrder.getOrderId(), sku.getId())));
            }
        }
        richGatherOrder.getGatherOrder().setOriginFee(originFee - promotionFeeBearByShop);
        richGatherOrder.getGatherOrder().setAuthStatus(AuthAble.AuthStatus.MASK_CODE.getValue());
        // no promotion now
        richGatherOrder.getGatherOrder().setTax(tax);
        richGatherOrder.getGatherOrder().setFee(originFee + tax);
        if (richGatherOrder.getGatherOrder().getFee() == 0L)
            richGatherOrder.getGatherOrder().setStatus(OrderStatus.PAID.getValue());
    }
}
