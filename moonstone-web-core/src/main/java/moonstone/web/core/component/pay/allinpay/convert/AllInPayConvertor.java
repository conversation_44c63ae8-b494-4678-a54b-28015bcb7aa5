package moonstone.web.core.component.pay.allinpay.convert;

import io.terminus.pay.enums.TradeStatus;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.model.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.Payment;
import moonstone.web.core.component.pay.allinpay.AllInPayToken;
import moonstone.web.core.component.pay.allinpay.constants.TrxStatusCode;
import moonstone.web.core.component.pay.allinpay.util.SybUtil;
import moonstone.web.core.component.user.UserAppInfoComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Pattern;

@Slf4j
@Component
public class AllInPayConvertor {

    @Resource
    private UserAppInfoComponent userAppInfoComponent;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    public static final Pattern URL_PATTERN = Pattern.compile(
            "(ht|f)tp(s?)\\:\\/\\/[0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\/?)([a-zA-Z0-9\\-\\.\\?\\,\\'\\/\\\\&%\\+\\$#_=]*)?");


    private static final String version = "11";
    private static final String RETURN_CODE = "retcode";
    private static final String RETURN_MESSAGE = "retmsg";

    @SneakyThrows
    public Map<String, String> convertPay(PaymentParams paymentParams, AllInPayToken payToken, Long currentUserId,
                                          Payment payment) {
        TreeMap<String, String> params = new TreeMap<String, String>();

        //params.put("orgid", );
        params.put("cusid", payToken.getCusid());
        params.put("appid", payToken.getAppid());
        params.put("version", version);
        params.put("trxamt", payment.getFee().toString());
        params.put("reqsn", payment.getOutId());
        params.put("paytype", "W06");
        params.put("randomstr", SybUtil.getValidatecode(8));
        params.put("body", paymentParams.getSubject());
        params.put("remark", paymentParams.getContent());
        params.put("validtime", expireMinutes == null ? "120" : expireMinutes.toString());
        params.put("acct", paymentParams.getOpenId());
        params.put("notify_url", payToken.getNotifyUrl() + "/" + payment.getChannel() + "/account/" + paymentParams.getSellerNo());
        //params.put("limit_pay", limit_pay);
        params.put("sub_appid", userAppInfoComponent.findAppId(paymentParams.getSellerNo(), paymentParams.getOpenId(), currentUserId));
        //params.put("goods_tag", goods_tag);
        //params.put("benefitdetail", benefitdetail);
        //params.put("chnlstoreid", chnlstoreid);
        //params.put("subbranch", subbranch);
        //params.put("extendparams", extendparams);
        //params.put("cusip", cusip);
        //params.put("fqnum", fqnum);
        //params.put("asinfo", asinfo);
        params.put("signtype", payToken.getSignType());

//        UserCertification userCertification = userCertificationReadService.findDefaultByUserId(currentUserId).getResult().get();
//        params.put("idno", userCertification.getPaperNo());
//        params.put("truename", userCertification.getPaperName());

        params.put("sign", SybUtil.unionSign(params, payToken.getPrivateKey(), payToken.getSignType()));

        return params;
    }

    public TradeRequest convertPayResult(String resultString, AllInPayToken payToken) {
        try {
            var resultMap = handleResult(resultString, payToken);
            if (TrxStatusCode.isError(resultMap.get("trxstatus"))) {
                String error = resultMap.get("errmsg");
                throw new RuntimeException(StringUtils.isNotBlank(error) ? error : "支付下单失败");
            }

            return TradeRequest.ok(convertPayRedirect(resultString, resultMap));
        } catch (Exception ex) {
            log.error("AllInPayConvertor.convertPayResult error, ", ex);
            return TradeRequest.fail(ex.getMessage());
        }
    }

    @SneakyThrows
    public Map<String, String> convertRefund(RefundParams refundParams, AllInPayToken token) {
        TreeMap<String, String> params = new TreeMap<>();

        //params.put("orgid", SybConstants.SYB_ORGID);
        params.put("cusid", token.getCusid());
        params.put("appid", token.getAppid());
        params.put("version", version);
        params.put("trxamt", refundParams.getRefundAmount().toString());
        params.put("reqsn", refundParams.getRefundNo());
        params.put("oldreqsn", refundParams.getTradeNo());
        params.put("oldtrxid", refundParams.getPaymentCode());
        params.put("randomstr", SybUtil.getValidatecode(8));
        params.put("signtype", token.getSignType());

        params.put("sign", SybUtil.unionSign(params, token.getPrivateKey(), token.getSignType()));

        return params;
    }

    public TradeRequest convertRefundResult(String resultString, AllInPayToken token) {
        try {
            var resultMap = handleResult(resultString, token);

            if (refundRequestSuccess(resultMap)) {
                return TradeRequest.ok(new Redirect(PaymentChannelEnum.ALLINPAY.getCode(), false, StringUtils.EMPTY, resultString));
            } else {
                return TradeRequest.fail(resultMap.get("errmsg"));
            }
        } catch (Exception ex) {
            log.error("AllInPayConvertor.convertRefundResult error, ", ex);
            return TradeRequest.fail(ex.getMessage());
        }
    }

    @SneakyThrows
    public Map<String, String> convertQuery(RefundQueryParams queryParams, AllInPayToken token) {
        TreeMap<String, String> params = new TreeMap<>();

        //params.put("orgid", );
        params.put("cusid", token.getCusid());
        params.put("appid", token.getAppid());
        params.put("version", version);
        params.put("reqsn", queryParams.getRefundNo());
        //params.put("trxid", );
        params.put("randomstr", SybUtil.getValidatecode(8));
        params.put("signtype", token.getSignType());

        params.put("sign", SybUtil.unionSign(params, token.getPrivateKey(), token.getSignType()));

        return params;
    }

    public TradeResult convertRefundQueryResult(String resultString, AllInPayToken token) {
        try {
            Map<String, String> resultMap = handleResult(resultString, token);

            if (TrxStatusCode.SUCCESS.equals(resultMap.get("trxstatus"))) {
                return convertRefundSuccessResult(resultMap);
            } else {
                return TradeResult.fail(TradeType.REFUND, PaymentChannelEnum.ALLINPAY.getCode(), getErrorMessage(resultMap));
            }
        } catch (Exception ex) {
            log.error("AllInPayConvertor.convertQueryResult error, ", ex);
            return TradeResult.fail(TradeType.REFUND, PaymentChannelEnum.ALLINPAY.getCode(), ex.getMessage());
        }
    }

    private TradeResult convertRefundSuccessResult(Map<String, String> resultMap) {
        var result = new TradeResult();

        result.setChannel(PaymentChannelEnum.ALLINPAY.getCode());
        result.setType(TradeType.REFUND.value());
        result.setMerchantSerialNo(resultMap.get("reqsn"));
        result.setGatewaySerialNo(resultMap.get("trxid"));
        result.setStatus(TradeStatus.SUCCESS.value());

        return result;
    }

    private String getErrorMessage(Map<String, String> resultMap) {
        var errorMessage = resultMap.get("errmsg");
        var statusCode = resultMap.get("trxstatus");

        if (StringUtils.isNotBlank(errorMessage)) {
            return errorMessage;
        } else if (StringUtils.isNotBlank(statusCode)) {
            return statusCode;
        } else {
            return "交易正在处理中";
        }
    }

    private boolean refundRequestSuccess(Map<String, String> resultMap) {
        List<String> list = List.of(TrxStatusCode.SUCCESS, TrxStatusCode.PROCESSING_2008, TrxStatusCode.PROCESSING_2000);

        return list.contains(resultMap.get("trxstatus"));
    }

    private Redirect convertPayRedirect(String resultString, Map<String, String> resultMap) {
        return new Redirect(PaymentChannelEnum.ALLINPAY.getCode(),
                false,
                null,
                resultMap.get("payinfo"),
                resultString);
    }

    private Map<String, String> handleResult(String result, AllInPayToken payToken) throws Exception {
        Map map = SybUtil.json2Obj(result, Map.class);
        if (map == null) {
            throw new RuntimeException("返回数据错误");
        }
        if (!map.containsKey(RETURN_CODE)) {
            throw new RuntimeException("结果码为空");
        }

        if ("SUCCESS".equals(map.get(RETURN_CODE))) {
            TreeMap tmap = new TreeMap();
            tmap.putAll(map);

            if (SybUtil.validSign(tmap, payToken.getPublicKey(), payToken.getSignType())) {
                return map;
            } else {
                throw new RuntimeException("验证签名失败");
            }
        } else {
            throw new RuntimeException(map.get(RETURN_MESSAGE).toString());
        }
    }

    private boolean invokeH5(Object payInfo) {
        if (payInfo == null) {
            return false;
        }

        String string = payInfo.toString();
        if (StringUtils.isBlank(string)) {
            return false;
        }

        try {
            return URL_PATTERN.matcher(string).matches();
        } catch (Exception ex) {
            log.error("AllInPayYSTConvertor.invokeH5 judge payInfo={} error ", payInfo, ex);
            return false;
        }
    }
    @SneakyThrows
    public TreeMap<String, String> convertPaySwitchOn(PaymentParams paymentParams, AllInPayToken payToken, Payment payment) {
        TreeMap<String, String> params = new TreeMap<String, String>();
        params.put("cusid", payToken.getCusid());
        params.put("appid", payToken.getAppid());
        params.put("version", "12");
        params.put("trxamt", payment.getFee().toString());
        params.put("reqsn", payment.getOutId());
        params.put("notify_url", payToken.getNotifyUrl() + "/" + payment.getChannel() + "/account/" + paymentParams.getSellerNo());
        params.put("body", paymentParams.getSubject());
        params.put("remark", paymentParams.getContent());
        params.put("paytype", "W06");
        params.put("randomstr", SybUtil.getValidatecode(8));
        params.put("validtime", expireMinutes == null ? "120" : expireMinutes.toString());
        params.put("signtype", payToken.getSignType());
        params.put("sign", SybUtil.unionSign(params, payToken.getPrivateKey(), payToken.getSignType()));
        return params;
    }
}
