package moonstone.web.core.component.intermediate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.IntermediateInfoMatchingTypeEnum;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.Either;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.IntermediateInfoWriteService;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.model.IntermediateInfoData;
import moonstone.web.core.model.dto.IntermediateInfoDTO;
import moonstone.web.core.user.application.IdentitySpecifyController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Slf4j
@Component
public class IntermediateInfoComponent {

    @Resource
    private IntermediateInfoReadService intermediateInfoReadService;

    @Resource
    private IntermediateInfoWriteService intermediateInfoWriteService;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuReadService skuReadService;

    @Resource
    private StoreProxyReadService storeProxyReadService;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Resource
    private ShopCacheHolder shopCache;

    @Autowired
    private IdentitySpecifyController identitySpecifyController;

    private LoadingCache<Long, List<JSONObject>> rateItemCache;

    private LoadingCache<Long, List<JSONObject>> rateShopCache;

    private LoadingCache<String, StoreProxy> storeProxyCache;

    private LoadingCache<String, IdentitySpecifyController.IdentityView> identityViewCache;

    @PostConstruct
    public void init() {
        rateItemCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(this::infoRateItemsCache);

        rateShopCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(this::infoRateShopCache);

        storeProxyCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(this::infoStoreProxyCache);

        identityViewCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(this::infoInIdentityCache);
    }

    /**
     * 保存商家平台的佣金配置
     *
     * @param intermediateInfoDTO
     * @param shopId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(IntermediateInfoDTO intermediateInfoDTO, Long shopId) {
        if (intermediateInfoDTO == null || shopId == null || intermediateInfoDTO.getNormal() == null) {
            throw new RuntimeException("入参缺失");
        }

        //通常配置
        intermediateInfoDTO.getNormal().setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode());
        save(intermediateInfoDTO.getNormal(), shopId);

        //活动配置
        updateActivityConfig(shopId, intermediateInfoDTO.getDeleteActivityCommissionConfig(), intermediateInfoDTO.getActivity());
    }

    /**
     * 删除配置
     *
     * @param thirdId
     * @param type
     * @param matchingTypeEnum
     */
    public void delete(Long thirdId, ThirdIntermediateType type, IntermediateInfoMatchingTypeEnum matchingTypeEnum) {
        intermediateInfoWriteService.delete(thirdId, type, matchingTypeEnum);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteActivityForSkuByItem(Long itemId) {
        if (itemId == null) {
            return;
        }

        Response<List<Sku>> listResponse = skuReadService.findSkusByItemId(itemId);
        if (!listResponse.isSuccess()) {
            throw new RuntimeException(listResponse.getError());
        }
        if (CollectionUtils.isEmpty(listResponse.getResult())) {
            return;
        }

        listResponse.getResult().forEach(sku -> delete(sku.getId(), ThirdIntermediateType.SKU, IntermediateInfoMatchingTypeEnum.ACTIVITY));
        delete(itemId, ThirdIntermediateType.ITEM, IntermediateInfoMatchingTypeEnum.ACTIVITY);
    }

    /**
     * 创建或更新佣金配置
     *
     * @param intermediateInfo
     * @param shopId
     * @return
     */
    public void createAndUpdate(IntermediateInfo intermediateInfo, Long shopId) {
        boolean changeTheInstead = shopId != null && ((environmentConfig.isOnline() && shopId == 30) || (!environmentConfig.isOnline() && shopId == 111));
        String profitRateMatrix;
        String staticProfitMatrix;
        if (changeTheInstead) {
            Function<Long, BigDecimal> convert = str -> Optional.ofNullable(str).map(BigDecimal::new).orElse(BigDecimal.TEN).divide(new BigDecimal("100"), 6, RoundingMode.DOWN);
            profitRateMatrix = convert.apply(intermediateInfo.getFirstRate()).toString() + "," + convert.apply(intermediateInfo.getSecondRate()).toString();
            staticProfitMatrix = convert.apply(intermediateInfo.getFirstFee()).toString() + "," + convert.apply(intermediateInfo.getSecondFee()).toString();
            intermediateInfo.setExtraFeeCalMatrix(profitRateMatrix + "@" + staticProfitMatrix);
        }

        ThirdIntermediateType thirdIntermediateType = ThirdIntermediateType.fromInt(intermediateInfo.getType());
        switch (thirdIntermediateType) {
            case SKU -> {
                //sku级别
                updateSkuIntermediateInfo(intermediateInfo);
                rateItemCache.refresh(intermediateInfo.getThirdId());
            }
            case SHOP -> {
                //店铺
                updateShopIntermediateInfo(intermediateInfo);
                rateShopCache.refresh(intermediateInfo.getThirdId());
            }
            case ITEM -> {
                //商品
                updateItemIntermediateInfo(intermediateInfo);
                rateItemCache.refresh(intermediateInfo.getThirdId());
            }
            default -> {
            }
        }
    }

    /**
     * 查询商品sku的佣金配置
     *
     * @param itemId
     * @return
     */
    public List<IntermediateInfo> findSkuByItemId(Long itemId) {
        Response<List<Sku>> skuList = skuReadService.findSkusByItemId(itemId);
        if (!skuList.isSuccess() || skuList.getResult().isEmpty()) {
            return null;
        }
        Long skuId = skuList.getResult().get(0).getId();

        var type = ThirdIntermediateType.SKU.value();
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findAllByThirdAndType(skuId, type);
        if (!listResult.isSuccess() || listResult.take().isEmpty()) {
            log.error("IntermediateInfo find empty itemId:{},type:{} , skuId:{}", itemId, type, skuId);
            return null;
        }

        listResult.take().forEach(IntermediateInfo::checkFlag);
        return listResult.take();
    }

    /**
     * 搜索查询分销比率--含缓存
     */
    public List<JSONObject> findRateByShopIdAndUserId(Long thirdId, Long shopId) {
        List<JSONObject> targetRateSetList = new ArrayList<>();
        Shop shop = shopCache.findShopById(shopId);
        if (shop == null || ObjectUtils.isEmpty(shop.getId()) || StringUtils.isEmpty(UserUtil.getUserId())) {
            return targetRateSetList;
        }
        Map<String, String> extra = shop.getExtra();
        if (extra == null || extra.isEmpty() || !extra.containsKey("salesPattern")
                || extra.get("salesPattern") == null) {
            return targetRateSetList;
        }
        String salesPattern = extra.get("salesPattern");
        boolean tips = false;//皇家判定是否是百分制和万分制
        //身份校验 阶梯模式非经销商和门店  门店非导购和门店
        String level = "first";//使用一级佣金还是二级佣金  first  一级   second  二级

        if (Objects.equals(salesPattern, "ladderDistribution")) {//阶梯分销
            StoreProxy storeProxy = storeProxyCache.get(shopId + "_" + UserUtil.getUserId());
            if (storeProxy == null || ObjectUtils.isEmpty(storeProxy.getId())) {
                return targetRateSetList;
            }
            if (Objects.equals(storeProxy.getLevel(), 1)) {
                level = "second";
            }
        } else if (Objects.equals(salesPattern, "subStore")) {//门店分销
            IdentitySpecifyController.IdentityView identityView = identityViewCache.get(shopId + "_" + UserUtil.getUserId());
            if (identityView == null) {
                return targetRateSetList;
            }
            if (ObjectUtils.isEmpty(identityView.getGuider()) && StringUtils.isEmpty(identityView.getSubStoreOwner())) {
                return targetRateSetList;
            }
            tips = true;
        }
        //单品级别为空 取店铺级别
        List<JSONObject> originItemRateSetList = rateItemCache.get(thirdId);
        if (Objects.requireNonNull(originItemRateSetList).isEmpty()) {
            List<JSONObject> shopJson = rateShopCache.get(shopId);
            if (ObjectUtils.isEmpty(shopJson) || shopJson.isEmpty()) {
                return targetRateSetList;
            }
            JSONObject jsonObjectShop = shopJson.get(0);
            if (tips && Objects.equals(jsonObjectShop.get("version"), "false")
                    && Objects.equals(jsonObjectShop.get("type"), 1)) {//比率和百分制
                BigDecimal firstRate;
                if (Objects.equals(level, "second")) {
                    firstRate = new BigDecimal(jsonObjectShop.get("secondRate") + "");
                } else {
                    firstRate = new BigDecimal(jsonObjectShop.get("firstRate") + "");
                }
                JSONObject jst = new JSONObject();
                jst.putAll(jsonObjectShop);
                jst.put("firstRate", firstRate.multiply(new BigDecimal("100")));
                jst.put("type", jsonObjectShop.get("type"));
                targetRateSetList.add(jst);
            } else {
                JSONObject jst = new JSONObject();
                jst.putAll(jsonObjectShop);
                if (Objects.equals(level, "second")) {
                    jst.put("firstRate", jsonObjectShop.get("secondRate"));
                } else {
                    jst.put("firstRate", jsonObjectShop.get("firstRate"));
                }
                jst.put("type", jsonObjectShop.get("type"));
                targetRateSetList.add(jst);
            }
            return targetRateSetList;
        }
        //取商品级别
        JSONObject itemRateSet = originItemRateSetList.get(0);
        if (itemRateSet.containsKey("isCommission") && Objects.equals(itemRateSet.get("isCommission"), 1)) {
            if (tips && Objects.equals(itemRateSet.get("version"), "false")
                    && Objects.equals(itemRateSet.get("type"), 1)) {//比率和百分制
                BigDecimal firstRate;//new BigDecimal(String.valueOf(jsonObject.get("firstRate") + ""));
                if (Objects.equals(level, "second")) {
                    firstRate = new BigDecimal(itemRateSet.get("secondRate") + "");
                } else {
                    firstRate = new BigDecimal(itemRateSet.get("firstRate") + "");
                }
                JSONObject jst = new JSONObject();
                jst.putAll(itemRateSet);
                jst.put("firstRate", firstRate.multiply(new BigDecimal("100")));
                jst.put("type", itemRateSet.get("type"));
                targetRateSetList.add(jst);
            } else {
                JSONObject jst = new JSONObject();
                jst.putAll(itemRateSet);
                if (Objects.equals(level, "second")) {
                    jst.put("firstRate", itemRateSet.get("secondRate"));
                } else {
                    jst.put("firstRate", itemRateSet.get("firstRate"));
                }
                jst.put("type", itemRateSet.get("type"));
                targetRateSetList.add(jst);
            }
            return targetRateSetList;
        }
        if (itemRateSet.containsKey("isCommission") && Objects.equals(itemRateSet.get("isCommission"), 0)) {
            List<JSONObject> shopJson = rateShopCache.get(shopId);
            if (ObjectUtils.isEmpty(shopJson) || shopJson.isEmpty()) {
                return targetRateSetList;
            }
            JSONObject shopRateSet = shopJson.get(0);
            if (tips && Objects.equals(shopRateSet.get("version"), "false")
                    && Objects.equals(shopRateSet.get("type"), 1)) {//比率和百分制
                BigDecimal firstRate;//new BigDecimal(String.valueOf(jsonObjectShop.get("firstRate") + ""));
                if (Objects.equals(level, "second")) {
                    firstRate = new BigDecimal(shopRateSet.get("secondRate") + "");
                } else {
                    firstRate = new BigDecimal(shopRateSet.get("firstRate") + "");
                }
                JSONObject jst = new JSONObject();
                jst.putAll(shopRateSet);
                jst.put("firstRate", firstRate.multiply(new BigDecimal("100")));
                jst.put("type", shopRateSet.get("type"));
                targetRateSetList.add(jst);
            } else {
                JSONObject jst = new JSONObject();
                jst.putAll(shopRateSet);
                if (Objects.equals(level, "second")) {
                    jst.put("firstRate", shopRateSet.get("secondRate"));
                } else {
                    jst.put("firstRate", shopRateSet.get("firstRate"));
                }
                jst.put("type", shopRateSet.get("type"));
                targetRateSetList.add(jst);
            }
            return targetRateSetList;
        }
        return targetRateSetList;
    }

    /**
     * 目前处理third为itemId
     */
    private void updateSkuIntermediateInfo(IntermediateInfo intermediateInfo) {
        log.info("intermediateInfo is:{}", intermediateInfo);
        Response<List<Sku>> listResponse = skuReadService.findSkusByItemId(intermediateInfo.getThirdId());
        if (!listResponse.isSuccess() || listResponse.getResult().isEmpty()) {
            log.error("商品sku的佣金配置查询失败, intermediateInfo={}", JSON.toJSONString(intermediateInfo));
            throw new RuntimeException(listResponse.getError());
        }

        //保存items级别
        updateItemIntermediateInfo(intermediateInfo);

        //通过item级别保存skus
        for (Sku skus : listResponse.getResult()) {
            Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(skus.getId(), intermediateInfo.getType(),
                    intermediateInfo.getMatchingType());
            if (!listResult.isSuccess()) {
                log.error("IntermediateInfo-sku query failed, intermediateInfo={}", JSON.toJSONString(intermediateInfo));
                throw new RuntimeException(listResult.getError());
            }

            Response<Boolean> response = null;
            if (CollectionUtils.isEmpty(listResult.take())) {
                IntermediateInfo si = new IntermediateInfo();
                BeanUtils.copyProperties(intermediateInfo, si);
                si.setThirdId(skus.getId());
                si.setType(ThirdIntermediateType.SKU.value());
                response = intermediateInfoWriteService.create(si);
            } else {
                Long id = listResult.take().get(0).getId();
                Long thirdId = listResult.take().get(0).getThirdId();
                BeanUtils.copyProperties(intermediateInfo, listResult.take().get(0));
                listResult.take().get(0).setThirdId(thirdId);
                listResult.take().get(0).setId(id);
                listResult.take().get(0).setType(ThirdIntermediateType.SKU.value());
                response = intermediateInfoWriteService.update(listResult.take().get(0));
            }

            if (response == null || !response.isSuccess() || !response.getResult()) {
                throw new RuntimeException(String.format("商品sku的佣金配置保存失败， response=%s", JSON.toJSONString(response)));
            }
            rateItemCache.invalidate(skus.getId());
            rateItemCache.invalidate(skus.getItemId());
        }
    }

    /**
     * 商家平台佣金配置保存
     *
     * @param intermediateInfo
     */
    private void updateShopIntermediateInfo(IntermediateInfo intermediateInfo) {
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(intermediateInfo.getThirdId(),
                intermediateInfo.getType(), intermediateInfo.getMatchingType());
        if (!listResult.isSuccess()) {
            log.error("商家平台佣金配置查询失败, intermediateInfo={}", JSON.toJSONString(intermediateInfo));
            throw new RuntimeException(listResult.getErrorMsg());
        }

        Response<Boolean> response = null;
        if (listResult.take().isEmpty()) {
            response = intermediateInfoWriteService.create(intermediateInfo);
        } else {
            for (IntermediateInfo intermediateInfo1 : listResult.take()) {
                BeanUtils.copyProperties(intermediateInfo, intermediateInfo1);
                response = intermediateInfoWriteService.update(intermediateInfo1);
            }
        }

        if (response == null || !response.isSuccess() || !response.getResult()) {
            throw new RuntimeException(String.format("商家平台佣金配置保存失败， response=%s", JSON.toJSONString(response)));
        }
    }

    /**
     * 商品item的佣金配置保存
     *
     * @param intermediateInfo
     */
    private void updateItemIntermediateInfo(IntermediateInfo intermediateInfo) {
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(intermediateInfo.getThirdId(),
                ThirdIntermediateType.ITEM.value(), intermediateInfo.getMatchingType());
        if (!listResult.isSuccess()) {
            log.error("商品item的佣金配置查询失败, intermediateInfo={}", JSON.toJSONString(intermediateInfo));
            throw new RuntimeException(listResult.getErrorMsg());
        }

        Response<Boolean> response = null;
        if (listResult.take().isEmpty()) {
            IntermediateInfo s = new IntermediateInfo();
            BeanUtils.copyProperties(intermediateInfo, s);
            s.setType(ThirdIntermediateType.ITEM.value());
            response = intermediateInfoWriteService.create(s);
        } else {
            for (IntermediateInfo intermediateInfo1 : listResult.take()) {
                Long id = intermediateInfo1.getId();
                BeanUtils.copyProperties(intermediateInfo, intermediateInfo1);
                intermediateInfo1.setType(ThirdIntermediateType.ITEM.value());
                intermediateInfo1.setId(id);
                response = intermediateInfoWriteService.update(intermediateInfo1);
            }
        }

        if (response == null || !response.isSuccess() || !response.getResult()) {
            throw new RuntimeException(String.format("商品item的佣金配置保存失败， response=%s", JSON.toJSONString(response)));
        }
        rateItemCache.invalidate(intermediateInfo.getThirdId());
    }

    /**
     * 更新活动配置
     *
     * @param shopId
     * @param deleteFlag
     * @param activity
     */
    private void updateActivityConfig(Long shopId, String deleteFlag, IntermediateInfoData activity) {
        if (Boolean.TRUE.toString().equals(deleteFlag)) {
            delete(shopId, ThirdIntermediateType.SHOP, IntermediateInfoMatchingTypeEnum.ACTIVITY);
            return;
        }

        if (activity == null) {
            return;
        }

        activity.setMatchingType(IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode());
        activity.setMatchingStartTime(DateUtil.parseDate(activity.getMatchingStartTimeString()));
        activity.setMatchingEndTime(DateUtil.parseDate(activity.getMatchingEndTimeString()));

        save(activity, shopId);
    }

    /**
     * 更新佣金配置
     *
     * @param intermediateInfo
     * @param shopId
     */
    private void save(IntermediateInfoData intermediateInfo, Long shopId) {
        if (ObjectUtils.isEmpty(intermediateInfo.getThirdId()) || intermediateInfo.getCommission() == null
                || ObjectUtils.isEmpty(intermediateInfo.getFirstRate()) || intermediateInfo.getSecondRate() == null) {
            throw new RuntimeException("必要参数缺少");
        }
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(intermediateInfo.getThirdId(),
                ThirdIntermediateType.SHOP.value(), intermediateInfo.getMatchingType());
        if (!listResult.isSuccess()) {
            throw new RuntimeException("系统异常");
        }

        // 为tantu固定佣金设置
        boolean changeTheInstead = (environmentConfig.isOnline() && shopId == 30) || (!environmentConfig.isOnline() && shopId == 111);
        String profitRateMatrix = null;
        String staticProfitMatrix = null;
        if (intermediateInfo.getProfitRateByLevel() != null) {
            profitRateMatrix = convertLongMapBigDecimalToMatrix(intermediateInfo.getProfitRateByLevel(), true);
        }
        if (intermediateInfo.getStaticProfitByLevel() != null) {
            staticProfitMatrix = convertLongMapBigDecimalToMatrix(intermediateInfo.getStaticProfitByLevel(), false);
        }
        if (changeTheInstead) {
            Function<Long, BigDecimal> convert = str -> Optional.ofNullable(str).map(BigDecimal::new).orElse(BigDecimal.TEN).divide(new BigDecimal("100"), 6, RoundingMode.DOWN);
            profitRateMatrix = convert.apply(intermediateInfo.getFirstRate()).toString() + "," + convert.apply(intermediateInfo.getSecondRate()).toString();
            staticProfitMatrix = convert.apply(intermediateInfo.getFirstFee()).toString() + "," + convert.apply(intermediateInfo.getSecondFee()).toString();
        }
        log.debug("{} isTantu:{} profitRateMatrix:{} staticProfitMatrix:{}", LogUtil.getClassMethodName(), changeTheInstead, profitRateMatrix, staticProfitMatrix);

        if (!CollectionUtils.isEmpty(listResult.take())) {
            for (IntermediateInfo intermediateInfo1 : listResult.take()) {
                if (intermediateInfo.getType().equals(ThirdIntermediateType.SHOP.getValue())) {
                    intermediateInfo.setThirdId(shopId);
                }
                BeanUtils.copyProperties(intermediateInfo, intermediateInfo1);
                intermediateInfo1.setVersion(3);
                if (staticProfitMatrix != null || profitRateMatrix != null) {
                    String matrix = Optional.ofNullable(profitRateMatrix).orElse("") + "@" + Optional.ofNullable(staticProfitMatrix).orElse("");
                    intermediateInfo1.setExtraFeeCalMatrix(matrix);
                }
                if (intermediateInfo.getType().equals(ThirdIntermediateType.SKU.getValue())) {
                    if (!skuReadService.findSkuById(intermediateInfo.getThirdId()).getResult().getShopId().equals(shopId)) {
                        throw new RuntimeException("权限不足");
                    }
                } else if (intermediateInfo.getType().equals(ThirdIntermediateType.ITEM.getValue())) {
                    if (!itemReadService.findById(intermediateInfo.getThirdId()).getResult().getShopId().equals(shopId)) {
                        throw new RuntimeException("权限不足");
                    }
                }
                log.debug("{} change:{}", LogUtil.getClassMethodName(), intermediateInfo1);
                intermediateInfoWriteService.update(intermediateInfo1);
                rateItemCache.invalidate(intermediateInfo1.getThirdId());
                rateShopCache.invalidate(intermediateInfo1.getThirdId());
            }
        } else {
            intermediateInfo.setType(ThirdIntermediateType.SHOP.value());
            intermediateInfo.setThirdId(shopId);
            IntermediateInfo intermediateInfo1 = new IntermediateInfo();
            BeanUtils.copyProperties(intermediateInfo, intermediateInfo1);
            intermediateInfo1.setVersion(3);
            if (staticProfitMatrix != null || profitRateMatrix != null) {
                String matrix = Optional.ofNullable(profitRateMatrix).orElse("") + "@" + Optional.ofNullable(staticProfitMatrix).orElse("");
                intermediateInfo1.setExtraFeeCalMatrix(matrix);
            }

            createAndUpdate(intermediateInfo1, shopId);
        }
    }

    /**
     * 转换 身份级别与用尽关系进入到矩阵中
     * 根据佣金拼凑的位置不同设置不同的数据
     *
     * @param profitSetByLevel 储存身份与佣金关系的表
     */
    private String convertLongMapBigDecimalToMatrix(Map<Long, BigDecimal> profitSetByLevel, boolean percent) {
        long maxLevel = profitSetByLevel.keySet().stream().reduce(Long::max).orElse(0L);
        long minLevel = profitSetByLevel.keySet().stream().reduce(Long::min).orElse(1L);
        StringBuilder stringBuilder = new StringBuilder();
        for (long i = minLevel; i <= maxLevel; i++) {
            if (percent) {
                stringBuilder.append(profitSetByLevel.getOrDefault(i, BigDecimal.ZERO).divide(new BigDecimal("100"), 6, BigDecimal.ROUND_DOWN).toString());
            } else {
                stringBuilder.append(profitSetByLevel.getOrDefault(i, BigDecimal.ZERO).toString());
            }
            stringBuilder.append(",");
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        return stringBuilder.toString();
    }

    /**
     * 商品商品佣金比率
     */
    private List<JSONObject> infoRateItemsCache(Long itemId) {
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonRate = new JSONObject();
        Either<List<IntermediateInfo>> listResults = intermediateInfoReadService.findByThirdAndType(itemId, ThirdIntermediateType.ITEM.value());
        if (listResults.isSuccess() && !listResults.take().isEmpty()) {
            IntermediateInfo intermediateInfo = listResults.take().get(0);
            intermediateInfo.checkFlag();
            if (Objects.equals(intermediateInfo.getFlag(), 0)) {
                jsonRate.put("firstRate", listResults.take().get(0).getFirstFee());
                jsonRate.put("secondRate", listResults.take().get(0).getSecondFee());
            } else {
                jsonRate.put("firstRate", listResults.take().get(0).getFirstRate());
                jsonRate.put("secondRate", listResults.take().get(0).getSecondRate());
            }
            jsonRate.put("isCommission", listResults.take().get(0).getIsCommission());
            jsonRate.put("type", intermediateInfo.getFlag());
            jsonRate.put("version", Objects.equals(listResults.take().get(0).getVersion(), listResults.take().get(0).getNowVersion()) + "");
            jsonRate.put("profitMatrix", intermediateInfo.getExtraFeeCalMatrix());
            list.add(jsonRate);
            return list;
        }

        //目前只考虑单规格的商品
        Response<List<Sku>> skuList = skuReadService.findSkusByItemId(itemId);
        if (!skuList.isSuccess() || skuList.getResult().isEmpty()) {
            return new ArrayList<>();
        }
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(skuList.getResult().get(0).getId(), ThirdIntermediateType.SKU.value());
        if (!listResult.isSuccess() || listResult.take().isEmpty()) {
            return list;
        }
        if (!ObjectUtils.isEmpty(listResult.take().get(0))) {
            IntermediateInfo intermediateInfo = listResult.take().get(0);
            intermediateInfo.checkFlag();
            if (Objects.equals(intermediateInfo.getFlag(), 0)) {
                jsonRate.put("firstRate", listResult.take().get(0).getFirstFee());
                jsonRate.put("secondRate", listResult.take().get(0).getSecondFee());
            } else {
                jsonRate.put("firstRate", listResult.take().get(0).getFirstRate());
                jsonRate.put("secondRate", listResult.take().get(0).getSecondRate());
            }
            jsonRate.put("isCommission", listResult.take().get(0).getIsCommission());
            jsonRate.put("type", intermediateInfo.getFlag());
            jsonRate.put("version", Objects.equals(listResult.take().get(0).getVersion(), listResult.take().get(0).getNowVersion()) + "");
            list.add(jsonRate);
            return list;
        }
        return list;
    }

    /**
     * 店铺商品佣金比率
     */
    private List<JSONObject> infoRateShopCache(Long shopId) {
        JSONObject jsonRate = new JSONObject();
        Either<List<IntermediateInfo>> listResult = intermediateInfoReadService.findByThirdAndType(shopId, ThirdIntermediateType.SHOP.value());
        if (!listResult.isSuccess() || listResult.take().isEmpty()) {
            return Collections.emptyList();
        }
        List<JSONObject> list = new ArrayList<>();
        if (!ObjectUtils.isEmpty(listResult.take().get(0))) {
            IntermediateInfo intermediateInfo = listResult.take().get(0);
            intermediateInfo.checkFlag();

            if (Objects.equals(intermediateInfo.getFlag(), 0)) {
                jsonRate.put("firstRate", listResult.take().get(0).getFirstFee());
                jsonRate.put("secondRate", listResult.take().get(0).getSecondFee());
            } else {
                jsonRate.put("firstRate", listResult.take().get(0).getFirstRate());
                jsonRate.put("secondRate", listResult.take().get(0).getSecondRate());
            }
            jsonRate.put("type", intermediateInfo.getFlag());
            jsonRate.put("version", Objects.equals(listResult.take().get(0).getVersion(), listResult.take().get(0).getNowVersion()) + "");
            jsonRate.put("profitMatrix", intermediateInfo.getExtraFeeCalMatrix());
            list.add(jsonRate);
            return list;
        }
        return list;
    }

    /**
     * @param ids 为shopId+userId
     */
    private StoreProxy infoStoreProxyCache(String ids) {
        Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByShopIdAndUserId(Long.parseLong(ids.substring(0, ids.indexOf("_"))),
                UserUtil.getUserId());
        if (!optionalResult.isSuccess() || !optionalResult.take().isPresent()
                || !Objects.equals(optionalResult.take().get().getStatus(), 3)) {
            return new StoreProxy();
        }
        return optionalResult.take().get();
    }

    /**
     * @param ids 为shopId+userId
     */
    private IdentitySpecifyController.IdentityView infoInIdentityCache(String ids) {
        IdentitySpecifyController.IdentityView identityView = identitySpecifyController.getIdentity(Long.parseLong(ids.substring(0, ids.indexOf("_"))), true);
        if (identityView.getGuider() == null && identityView.getSubStoreOwner() == null) {
            return new IdentitySpecifyController.IdentityView();
        }
        return identityView;
    }
}
