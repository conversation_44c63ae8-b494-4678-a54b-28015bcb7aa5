package moonstone.web.core.component.order;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.Either;
import moonstone.common.model.IsPersistAble;
import moonstone.common.utils.LogUtil;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.HonestFanDataReadService;
import moonstone.order.service.HonestFanDataWriteService;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.model.UserExtraInformation;
import moonstone.user.service.UserExtraInformationReadService;
import moonstone.user.service.UserExtraInformationWriteService;
import moonstone.web.core.component.ItemExLinkedProfitRulerJudgeImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class BirthProfitRecorder {
    @Autowired
    private UserExtraInformationReadService userExtraInformationReadService;
    @Autowired
    private UserExtraInformationWriteService userExtraInformationWriteService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private HonestFanDataReadService honestFanDataReadService;
    @Autowired
    private HonestFanDataWriteService honestFanDataWriteService;
    @Autowired
    private BalanceDetailManager balanceDetailManager;
    @RpcConsumer
    private IntermediateInfoReadService intermediateInfoReadService;
    @Autowired
    private OrderWriteService orderWriteService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ItemExLinkedProfitRulerJudgeImpl itemRulerJudgeBean;

    /**
     * 增加冻结佣金(伪)
     *
     * @param storeProxyId 代理人Id
     * @param userId       用户Id
     * @param shopId       平台Id
     */
    public void increaseFakeProfit(Long storeProxyId, Long userId, Long shopId) {

        IntermediateInfo rate = intermediateInfoReadService.findByThirdAndType(shopId, ThirdIntermediateType.SHOP.getValue())
                .map(Collection::stream)
                .map(Stream::findFirst)
                .orElse(Optional.empty()).orElse(null);
        if (rate == null) {
            log.error("{} storeProxyId:{} shopId:{} no profit set?", LogUtil.getClassMethodName(), storeProxyId, shopId);
            return;
        }
        FakeProfit newProfit = new FakeProfit();
        newProfit.setUserId(storeProxyId);
        newProfit.setSourceId(userId);
        newProfit.setShopId(shopId);
        newProfit.setType(1);
        newProfit.setFakeProfit(rate.getFirstCommission());
        mongoTemplate.insert(newProfit);
        Query query = new Query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("userId").is(storeProxyId)).addCriteria(Criteria.where("type").is(2));
        mongoTemplate.upsert(query, new Update().inc("fakeProfit", rate.getFirstCommission()), FakeProfit.class);
    }

    /**
     * 减少冻结佣金(伪)
     *
     * @param storeProxyId 代理人Id
     * @param userId       用户Id
     * @param shopId       平台Id
     */
    private void decreaseFakeProfit(Long storeProxyId, Long userId, Long shopId) {

        IntermediateInfo rate = intermediateInfoReadService.findByThirdAndType(shopId, ThirdIntermediateType.SHOP.getValue())
                .map(Collection::stream)
                .map(Stream::findFirst)
                .orElse(Optional.empty()).orElse(null);
        if (rate == null) {
            log.error("{} storeProxyId:{} shopId:{} no profit set?", LogUtil.getClassMethodName(), storeProxyId, shopId);
            return;
        }
        // just remove one not two
        FakeProfit removed = mongoTemplate.findAndRemove(Query.query(Criteria.where("shopId").is(shopId))
                        .addCriteria(Criteria.where("userId").is(storeProxyId))
                        .addCriteria(Criteria.where("type").is(1))
                        .addCriteria(Criteria.where("sourceId").is(userId))
                , FakeProfit.class);
        // decrease profit that he owned
        Query query = new Query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("userId").is(storeProxyId)).addCriteria(Criteria.where("type").is(2));
        mongoTemplate.upsert(query, new Update().inc("fakeProfit", -rate.getFirstCommission()), FakeProfit.class);

        //增加拉新数据统计
        Query query1 = new Query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("userId").is(storeProxyId)).
                addCriteria(Criteria.where("sourceId").is(userId)).addCriteria(Criteria.where("type").is(3));
        mongoTemplate.upsert(query1, new Update().inc("fakeProfit", rate.getFirstCommission()), FakeProfit.class);

    }

    @Data
    public static class FakeProfit {
        String _id;
        Long shopId;
        Long userId;
        Long fakeProfit;
        Long sourceId; // 来源哪个用户的
        Integer type; // 2为缓存 1为添加  3查询数据
    }

    /**
     * 增加假的利润设置
     *
     * @param shopOrder 订单数据来源
     * @return 是否添加了利润
     */
    public boolean increaseTheProfitByShopOrder(ShopOrder shopOrder) {
        // first thing check the num of paid order (that counted by mongodb) and increase it
        // then check the count of birthID which is passed auth
        // then we can increase the fucking fake profit
        if (shopOrder.getReferenceId() == null) {
            return false;
        }
        List<Long> skuIdList = Optional.ofNullable(skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult()).map(Collection::stream)
                .map(stream -> stream.filter(skuOrder -> !Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new).getOrDefault("freeGift", "false").equals("true")).map(SkuOrder::getSkuId).distinct().collect(Collectors.toList())).orElse(new ArrayList<>());
        List<Item> items = skuIdList.stream().map(skuReadService::findSkuById).map(Response::getResult).filter(Objects::nonNull).map(Sku::getItemId)
                .map(itemReadService::findById).map(Response::getResult).filter(Objects::nonNull).collect(Collectors.toList());

        if (items.stream().map(itemRulerJudgeBean::allow).anyMatch(Predicate.isEqual(false))) {
            StringBuilder logDataBuilder = new StringBuilder();
            for (Item item : items) {
                logDataBuilder.append(String.format("item:%S code:%s ", item.getId(), item.getItemCode()));
            }
            log.warn("{} not allow exlinked profit by items:{}", LogUtil.getClassMethodName(), logDataBuilder);
            return false;
        } else {
            StringBuilder logDataBuilder = new StringBuilder();
            for (Item item : items) {
                logDataBuilder.append(String.format("item:%S code:%s ", item.getId(), item.getItemCode()));
            }
            log.warn("{} {} allowed", LogUtil.getClassMethodName(), logDataBuilder);
        }


        IntermediateInfo shopProfitSet = intermediateInfoReadService.findByThirdAndType(shopOrder.getShopId(), ThirdIntermediateType.SHOP.getValue())
                .map(Collection::stream)
                .map(Stream::findFirst)
                .orElse(Optional.empty()).orElse(null);
        if (shopProfitSet == null) {
            log.error("{} shopOrderId:{} shopId:{} no profit set?", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getShopId());
            return false;
        }
        Optional<UserExtraInformation> triggerProfitByBirthInfo = userExtraInformationReadService.findByUserIdAndShopId(shopOrder.getBuyerId(), shopOrder.getShopId())
                .orElse(new ArrayList<>())
                .stream()
                .filter(e -> e.getStatus() != -1)
                .filter(info -> info.getRelatedId() == null && info.isAuthed())
                .filter(info -> shopOrder.getRefererId() != null && shopOrder.getRefererId().toString().equals(info.getExtra().get(info.getSTORE_PROXY_INDEX())))
                .findFirst();
        log.debug("{} orderId:{} list:{}", LogUtil.getClassMethodName(), shopOrder.getId(), userExtraInformationReadService.findByUserIdAndShopId(shopOrder.getBuyerId(), shopOrder.getShopId()).orElse(new ArrayList<>()));
        if (!triggerProfitByBirthInfo.isPresent()) {
            return false;
        }
        UserExtraInformation birthInfo = triggerProfitByBirthInfo.get();
        Long refererId = Long.valueOf(birthInfo.getExtra().get(birthInfo.getSTORE_PROXY_INDEX()));
        Long firstCommissionProfit = skuIdList.stream().map(itemRulerJudgeBean::getFirstCommissionProfitBySkuId)
                .map(opt -> opt.orElse(shopProfitSet.getFirstCommission()))
                .reduce(Long::min).orElse(shopProfitSet.getFirstCommission());
        BalanceDetail balanceDetail = new BalanceDetail();
        balanceDetail.setSourceId(birthInfo.getShopId());
        balanceDetail.setRelatedId(shopOrder.getId());
        balanceDetail.setStatus(BalanceDetail.orderRelatedMask.ShopOrder.getValue() | BalanceDetail.maskBit.OrderRelated.getValue() | IsPersistAble.maskBit.PersistAble.getValue() | BalanceDetail.SourceMark.EX_Linked.getBitMark());
        balanceDetail.setChangeFee(firstCommissionProfit);
        balanceDetail.setType(ProfitType.InCome.getValue());
        balanceDetail.setUserId(refererId);

        balanceDetailManager.changeRealProfit(balanceDetail).take();
        birthInfo.setRelatedId(balanceDetail.getId());

        // because low rate occupy so ignore the failed situation;
        boolean updateBirthInfo = userExtraInformationWriteService.update(birthInfo).orElse(false);
        if (!updateBirthInfo) {
            log.error("{} birthInfoUpdate Failed,need rollback? birthInfoId:{} related-BalanceId:{}", LogUtil.getClassMethodName(), birthInfo.getId(), balanceDetail.getId());
            return false;
        }
        if (shopOrder.getExtra() == null) {
            shopOrder.setExtra(new HashMap<>());
        }
        shopOrder.getExtra()
                .put("PAID", birthInfo.getRelatedId().toString());
        shopOrder.getExtra()
                .put("BabyProfit", balanceDetail.getChangeFee().toString());
        Response<Boolean> rUpdate = orderWriteService.updateOrderExtra(shopOrder.getId(), OrderLevel.SHOP, shopOrder.getExtra());
        if (!rUpdate.isSuccess() || !rUpdate.getResult()) {
            log.error("{} shopOrder(id:{}) extra:{} update failed", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getExtra());
        }
        log.debug("{} order:{} make profit:{}", LogUtil.getClassMethodName(), shopOrder, JSON.toJSON(balanceDetail));
        decreaseFakeProfit(shopOrder.getRefererId(), shopOrder.getBuyerId(), shopOrder.getShopId());
        return true;
    }


    /**
     * 查找被冻结的佣金
     *
     * @param userId 用户Id(代理人的用户Id)
     * @param shopId 平台Id
     * @return 返回佣金
     */
    public Either<Long> queryFrozeProfit(Long userId, Long shopId) {
        try {
            return Either.ok(
                    mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId))
                            .addCriteria(Criteria.where("userId").is(userId))
                            .addCriteria(Criteria.where("type").is(2)), FakeProfit.class))
                    .map(FakeProfit::getFakeProfit);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    /**
     * 查找拉新的佣金统计
     *
     * @param userId 用户Id(来源的用户Id)
     * @param shopId 平台Id
     * @return 返回佣金
     */
    public Either<Long> queryUnFrozeProfit(Long userId, Long shopId) {
        try {
            return Either.ok(
                    mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId))
                            .addCriteria(Criteria.where("sourceId").is(userId))
                            .addCriteria(Criteria.where("type").is(3)), FakeProfit.class))
                    .map(FakeProfit::getFakeProfit);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    /**
     * 退款减去拉新佣金统计
     *
     * @param sourceId     提供佣金的人
     * @param storeProxyId 用户Id(代理人的用户Id)佣金持有人
     * @param shopId       平台Id
     * @param fakeProfit   退款金额
     */
    public void decreaseRefundFakeProfit(Long sourceId, Long storeProxyId, Long shopId, Long fakeProfit) {
        try {
            Query query1 = new Query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("userId").is(storeProxyId)).
                    addCriteria(Criteria.where("sourceId").is(sourceId)).addCriteria(Criteria.where("type").is(3));
            mongoTemplate.upsert(query1, new Update().inc("fakeProfit", -fakeProfit), FakeProfit.class);
        } catch (Exception ex) {
            ex.printStackTrace();

        }
    }
}
