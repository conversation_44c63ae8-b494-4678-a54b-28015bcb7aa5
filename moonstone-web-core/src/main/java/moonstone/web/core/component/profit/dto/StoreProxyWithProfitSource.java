package moonstone.web.core.component.profit.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.order.model.BalanceDetail;
import moonstone.user.model.StoreProxy;
import org.springframework.beans.BeanUtils;

/**
 * 利润生成使用的DTO
 */
public class StoreProxyWithProfitSource extends StoreProxy {
    @Setter
    @Getter
    BalanceDetail.SourceMark balanceSource;
    public StoreProxyWithProfitSource(){}
    public StoreProxyWithProfitSource(StoreProxy storeProxy)
    {
        BeanUtils.copyProperties(storeProxy,this);
    }
    protected StoreProxyWithProfitSource(StoreProxy storeProxy, BalanceDetail.SourceMark sourceMark)
    {
        BeanUtils.copyProperties(storeProxy,this);
        setBalanceSource(sourceMark);
    }
    public static StoreProxyWithProfitSource forReachProfit(StoreProxy storeProxy)
    {
        return new StoreProxyWithProfitSource(storeProxy, BalanceDetail.SourceMark.Reach);
    }
    public static StoreProxyWithProfitSource forTransmitProfit(StoreProxy storeProxy)
    {
        return new StoreProxyWithProfitSource(storeProxy, BalanceDetail.SourceMark.Transmit);
    }
    public static StoreProxyWithProfitSource forLinkedProfit(StoreProxy storeProxy)
    {
        return new StoreProxyWithProfitSource(storeProxy, BalanceDetail.SourceMark.Linked);
    }

    public static StoreProxyWithProfitSource forExLinkedProfit(StoreProxy storeProxy) {
        return new StoreProxyWithProfitSource(storeProxy, BalanceDetail.SourceMark.EX_Linked);
    }
}
