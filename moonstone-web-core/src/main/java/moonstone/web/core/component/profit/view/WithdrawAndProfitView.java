package moonstone.web.core.component.profit.view;

import lombok.Data;
import moonstone.order.model.WithdrawAccount;
import moonstone.web.core.model.dto.WithdrawLimitView;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WithdrawAndProfitView {
    //  密码已经设置完毕
    boolean passwordSet;
    //  目前可用余额
    BigDecimal money = BigDecimal.ZERO;
    //  待确定收益
    BigDecimal foreseeProfit = BigDecimal.ZERO;
    //  已确定收益
    BigDecimal collectProfit = BigDecimal.ZERO;
    //  已提现收益
    BigDecimal withdrawnProfit = BigDecimal.ZERO;
    // 可提现额度
    WithdrawLimitView withdrawLimitView;
    // 默认提现帐号
    WithdrawAccount withdrawAccount;
    // 待选提现帐号
    List<WithdrawAccount> withdrawAccountList;
}
