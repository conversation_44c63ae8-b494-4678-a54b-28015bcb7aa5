package moonstone.web.core.component;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.EventBus;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.LogUtil;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.web.core.component.profit.WeShopSkuTaxCalculatorBase;
import moonstone.web.core.model.dto.tax.ProfitAndTax;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeShopSkuTaxPreviewVerticle extends AbstractVerticle implements Handler<Message<Buffer>> {

    @Autowired
    WeShopSkuTaxCalculatorBase weShopSkuTaxCalculatorBase;
    @Autowired
    SkuCacheHolder skuCacheHolder;
    @Autowired
    ItemCacheHolder itemCacheHolder;
    @Autowired
    SkuCustomReadService skuCustomReadService;

    @Override
    public void start(Promise<Void> startPromise) {
        EventBus eventBus = vertx.eventBus();
        eventBus.consumer(WeShopSkuTaxPreviewVerticle.class.getName(), this)
                .exceptionHandler(exception -> log.error("{} {}", LogUtil.getClassMethodName("WeShopSkuTaxPreview"), LogUtil.express(exception)));
        startPromise.complete();
    }

    @Override
    public void handle(Message<Buffer> event) {
        // 日后扩展
        previewWeShopSkuTaxAndPrice(event);
    }

    private void previewWeShopSkuTaxAndPrice(Message<Buffer> event) {
        String message = event.body().toString();
        log.debug("{} try to preview the tax and profit for [{}]", LogUtil.getClassMethodName(), message);
        Long skuId = Long.parseLong(message.split("_")[0]);
        Long price = Long.parseLong(message.split("_")[1]);

        Sku sku = skuCacheHolder.findSkuById(skuId);

        boolean sellerBearTax = Boolean.parseBoolean(message.split("_")[2]);
        Set<ThirdPartySystem> thirdPartySystemSet = new HashSet<>();
        for (String pushSystem : Optional.ofNullable(sku.getTags()).map(tags -> tags.get(SkuTagIndex.pushSystem.name()))
                .orElse("").split(SkuTagIndex.pushSystem.getSplitter())) {
            if (ObjectUtils.isEmpty(pushSystem)) {
                continue;
            }
            thirdPartySystemSet.add(ThirdPartySystem.fromInt(Integer.parseInt(pushSystem)));
        }
        Optional<ThirdPartySystem> thirdPartySystem = thirdPartySystemSet.contains(ThirdPartySystem.GongXiao) ? Optional.of(ThirdPartySystem.GongXiao) :
                thirdPartySystemSet.stream().findFirst();
        StopWatch timeCostWatch = new StopWatch("vertx-inner-watch");
        timeCostWatch.start("profit-calculate");
        vertx.<ProfitAndTax>executeBlocking(promise -> {
                    if (BondedType.fromInt(itemCacheHolder.findItemById(sku.getItemId()).getIsBonded()).isBonded()) {
                        // 跨境商品进行税费计算
                        promise.complete(weShopSkuTaxCalculatorBase.profitCalculate(sku.getId(),
                                sku.getShopId(),
                                thirdPartySystem.orElse(null),
                                sku.getOuterSkuId(),
                                price,
                                sku.getPrice().longValue(),
                                null,
                                sellerBearTax,
                                Optional.ofNullable(skuCustomReadService.findBySkuId(skuId))
                                        .map(SkuCustom::getCustomTaxHolder).filter(Predicate.isEqual(2)).isPresent()
                        ));
                    } else {
                        // 非跨境商品 直接扣减价格
                        ProfitAndTax profitAndTax = new ProfitAndTax();
                        profitAndTax.setProfit(price - sku.getPrice());
                        promise.complete(profitAndTax);
                    }
                    timeCostWatch.stop();
                    log.debug("{} time-watch [{}]", LogUtil.getClassMethodName(), timeCostWatch);
                }
                , result -> event.replyAndRequest(JsonObject.mapFrom(result.result())));
    }
}
