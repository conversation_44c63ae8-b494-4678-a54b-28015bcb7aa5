package moonstone.web.core.component.item;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.api.IndexedItemProcessor;
import moonstone.search.dto.IndexedItem;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.component.item.model.SubStoreAreaModel;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class SubStoreItemLimitIndexedItemProcessor implements IndexedItemProcessor {
    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    ServiceProviderCache serviceProviderCache;

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Override
    public IndexedItem process(IndexedItem indexedItem) {
        try {
            Long shopId = indexedItem.getShopId();

            // subStoreAreaModel
            var models = findSubStoreAreaModel(shopId, indexedItem.getId());
            if (CollectionUtils.isEmpty(models)) {
                return indexedItem;
            }

            // serviceProvider
            var serviceProviders = findServiceProviders(models);
            if (CollectionUtils.isEmpty(serviceProviders)) {
                return indexedItem;
            }
            indexedItem.setSsid(new ArrayList<>(serviceProviders.stream().map(ServiceProvider::getId).toList()));

            // subStore
            var subStores = findSubStores(serviceProviders, shopId);
            if (CollectionUtils.isEmpty(subStores)) {
                return indexedItem;
            }

            var ids = subStores.stream()
                    .filter(subStore -> subStore.getStatus() > 0)
                    .map(subStore -> subStore.getId().toString())
                    .toList();
            if (!CollectionUtils.isEmpty(ids)) {
                indexedItem.getSsid().addAll(ids);
            }

            return indexedItem;
        } catch (Exception e) {
            return indexedItem;
        }
    }

    private List<SubStore> findSubStores(List<ServiceProvider> serviceProviders, Long shopId) {
        if (CollectionUtils.isEmpty(serviceProviders)) {
            return Collections.emptyList();
        }

        // userRelationEntity
        var relations = findUserRelationEntity(serviceProviders);
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }

        return subStoreReadService.findByUserIds(relations.stream().map(UserRelationEntity::getUserId).toList(), shopId).getResult();
    }

    /**
     * 查询服务商与门店的关系对象
     *
     * @param serviceProviders
     */
    private List<UserRelationEntity> findUserRelationEntity(List<ServiceProvider> serviceProviders) {
        if (CollectionUtils.isEmpty(serviceProviders)) {
            return Collections.emptyList();
        }

        var criteria = new UserRelationEntityCriteria();
        criteria.setPageNo(1);
        criteria.setPageSize(Integer.MAX_VALUE);
        criteria.setRelationIds(serviceProviders.stream().map(ServiceProvider::getUserId).toList());
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        criteria.setRelationIdA(List.of(serviceProviders.get(0).getShopId()));

        return userRelationEntityReadService.pageList(criteria).getResult();
    }

    /**
     * 查询授权下的服务商
     *
     * @param models
     * @return
     */
    private List<ServiceProvider> findServiceProviders(List<SubStoreAreaModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }

        List<String> serviceProviderIds = models.stream()
                .map(SubStoreAreaModel::getServiceProviderId)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(Collection::stream)
                .toList();
        if (CollectionUtils.isEmpty(serviceProviderIds)) {
            return Collections.emptyList();
        }

        return serviceProviderCache.findByIds(serviceProviderIds);
    }

    /**
     * 查询包含 itemId 的授权
     *
     * @param shopId
     * @param itemId
     * @return
     */
    private List<SubStoreAreaModel> findSubStoreAreaModel(Long shopId, Long itemId) {
        var query = Query.query(Criteria.where("shopId").is(shopId));
        query.addCriteria(Criteria.where("itemId").is(itemId));

        return mongoTemplate.find(query, SubStoreAreaModel.class);
    }
}
