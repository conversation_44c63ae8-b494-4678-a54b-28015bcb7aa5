package moonstone.web.core.component;

import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.AppConstants;
import moonstone.web.core.component.api.CrossLoginService;
import moonstone.web.core.config.JwtConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * JWT Cross login, must dispatch after the session fetcher
 *
 * <AUTHOR>
 * @see moonstone.web.core.session.SessionManager  session manager
 * @see moonstone.web.core.session.HeraldRequestWithSession the session used in this project
 */
@Slf4j
@Component
public class JwtCrossLogin extends HandlerInterceptorAdapter {
    @Autowired
    private CrossLoginService crossLogin;
    @Autowired
    private JwtConfig jwtConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (Objects.isNull(request.getCookies())) {
            return true;
        }
        Jws<Claims> validateCrossLoginCookie = findJwtForCrossLogin(request.getCookies());
        if (Objects.nonNull(validateCrossLoginCookie)) {
            loginByJwt(validateCrossLoginCookie, request, response);
        } else {
            logoutIfLoginByOms(request.getSession(false));
        }
        deleteInvalidateJwtToken(request, response);
        // allow the request
        return true;
    }

    /**
     * delete invalidate jwt token
     *
     * @param request  request that carry jwt token
     * @param response response to control the jwt token
     */
    private void deleteInvalidateJwtToken(HttpServletRequest request, HttpServletResponse response) {
        if (Objects.isNull(request.getCookies())) {
            return;
        }
        for (Cookie cookie : request.getCookies()) {
            if (jwtConfig.getJwtCookieName().equals(cookie.getName())) {
                if (!StringUtils.hasText(jwtConfig.getJwtCookieName())) {
                    cookie.setMaxAge(0);
                    response.addCookie(cookie);
                    continue;
                }
                try {
                    JwtParser jwtParser = Jwts.parser().setSigningKey(jwtConfig.prepareKey(cookie.getValue(), jwtConfig.getJwtKey()));
                    Jws<Claims> claimsJws = (Jws<Claims>) jwtParser.parse(cookie.getValue());
                    Date now = new Date();
                    // skip the expire or wrong date jwt
                    if (claimsJws.getBody().getExpiration().before(now) || claimsJws.getBody().getIssuedAt().after(now)) {
                        cookie.setMaxAge(0);
                        response.addCookie(cookie);
                    }
                } catch (SignatureException ignore) {
                    cookie.setMaxAge(0);
                    response.addCookie(cookie);
                }
                catch (Exception exception) {
                    log.error("{} fail to parse Jws from [{}]", LogUtil.getClassMethodName(), cookie.getValue(), exception);
                    cookie.setMaxAge(0);
                    response.addCookie(cookie);
                }
            }
        }
    }

    /**
     * find the jwt designed to carry the cross login cookie
     * validate the cookie
     *
     * @param cookies all cookies that request carry
     * @return the jwt
     */
    private Jws<Claims> findJwtForCrossLogin(Cookie[] cookies) {
        List<Cookie> jwtCookie = new LinkedList<>();
        for (Cookie cookie : cookies) {
            if (StringUtils.hasText(jwtConfig.getJwtCookieName()) && jwtConfig.getJwtCookieName().equals(cookie.getName())) {
                jwtCookie.add(cookie);
            }
        }
        Jws<Claims> newestJws = null;
        for (Cookie cookie : jwtCookie) {
            try {
                JwtParser jwtParser = Jwts.parser().setSigningKey(jwtConfig.prepareKey(cookie.getValue(), jwtConfig.getJwtKey()));
                Jws<Claims> claimsJws = (Jws<Claims>) jwtParser.parse(cookie.getValue());
                Date now = new Date();
                // skip the expire or wrong date jwt
                if (claimsJws.getBody().getExpiration().before(now) || claimsJws.getBody().getIssuedAt().after(now)) {
                    continue;
                }
                // find the newest created jwt
                if (newestJws == null) {
                    newestJws = claimsJws;
                } else {
                    if (claimsJws.getBody().getIssuedAt().after(newestJws.getBody().getIssuedAt())) {
                        newestJws = claimsJws;
                    }
                }
            } catch (ExpiredJwtException expiredJwtException) {
                if (System.currentTimeMillis() % 5432123 == 0) {
                    log.error("{} fail to check the expired jws [{}]", LogUtil.getClassMethodName(), cookie.getValue(), expiredJwtException);
                }
            } catch (SignatureException signatureException) {
                if (System.currentTimeMillis() % 5432123 == 0) {
                    log.error("{} fail to check the sign of jws [{}]", LogUtil.getClassMethodName(), cookie.getValue(), signatureException);
                }
            } catch (Exception e) {
                log.error("{} fail to parse Jws from [{}]", LogUtil.getClassMethodName(), cookie.getValue(), e);
            }
        }
        return newestJws;
    }

    /**
     * cross login than set userId and shopId into session for frontend use
     *
     * @param jws     the jwt jws
     * @param request origin request to fetch the session
     */
    private void loginByJwt(Jws<Claims> jws, HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        try {
            if (jws.getBody().containsKey(CrossLoginService.ClaimsIndex.user.name())) {
                crossLogin.crossLogin(jws)
                        // set userId into session
                        .ifSuccess(paranaUser -> {
                            session.setAttribute(AppConstants.SESSION_USER_ID, paranaUser.getId());
                            session.setAttribute(AppConstants.SESSION_LOGIN_BY_OMS, Boolean.TRUE.toString());
                        }).map(CommonUser::getShopId)
                        // set shopId if validate as seller
                        .ifSuccess(shopId -> {
                            session.setAttribute(AppConstants.SESSION_SHOP_ID, shopId);
                            Cookie addShopId = new Cookie(AppConstants.COOKIE_SHOP_ID, shopId.toString());
                            addShopId.setPath("/");
                            response.addCookie(addShopId);
                        });
            } else {
                // check if login by oms, if true, remove the login session
                logoutIfLoginByOms(session);
            }
        } catch (Exception exception) {
            log.error("{} fail to parse jws to cross login with jws[{}]"
                    , LogUtil.getClassMethodName(), jws.getBody(), exception);
        }
    }

    /**
     * logout the session if session is login by oms
     *
     * @param session http session -> heraldSession -> redis
     * @see moonstone.web.core.session.HeraldSession
     */
    private void logoutIfLoginByOms(HttpSession session) {
        if (Objects.isNull(session)) {
            return;
        }
        if (Boolean.TRUE.toString().equals(session.getAttribute(AppConstants.SESSION_LOGIN_BY_OMS))) {
            session.invalidate();
        }
    }
}
