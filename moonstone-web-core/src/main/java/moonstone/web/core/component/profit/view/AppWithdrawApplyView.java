package moonstone.web.core.component.profit.view;

import java.math.BigDecimal;
import java.util.Date;

public interface AppWithdrawApplyView {
    /**
     * 支付时间
     *
     * @return 获取支付时间
     */
    Date getPaidAt();

    /**
     * @return 获取收益帐号名
     */
    String getAccount();


    /**
     * @return 是否提现完成
     */
    Boolean isComplete();

    /**
     * @return 是否出错
     */
    Boolean isError();

    /**
     * 获取状态
     *
     * @return 提现状态文字
     */
    String getStatus();

    /**
     * 错误原因 仅仅在isError时出现
     *
     * @return 错误原因
     */
    String getReason();

    /**
     * @return 创建时间 也就是发起提现的时间
     */
    Date getCreatedAt();

    /**
     * 提现金额
     *
     * @return 获取提现金额
     */
    BigDecimal getMoney();

    String getUserType();
}
