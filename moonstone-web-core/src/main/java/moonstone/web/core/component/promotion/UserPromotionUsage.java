package moonstone.web.core.component.promotion;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.UserPromotion;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.promotion.service.UserPromotionWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户营销使用(冻结/扣减)
 * Author:cp
 * Created on 6/29/16.
 */
@Component
@Slf4j
public class UserPromotionUsage {

    @RpcConsumer
    private UserPromotionReadService userPromotionReadService;

    @RpcConsumer
    private UserPromotionWriteService userPromotionWriteService;

    @Autowired
    private PromotionCacher promotionCacher;

    public boolean isUserPromotion(Long promotionId) {
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);
        return PromotionType.isUserPromotion(promotion.getType());
    }

    public boolean frozen(Long buyerId, Long promotionId, int delta) {
        List<UserPromotion> userPromotions = findUserPromotions(buyerId, promotionId);

        UserPromotion toFrozen = null;
        for (UserPromotion userPromotion : userPromotions) {
            if (!userPromotion.inProcess()) {
                continue;
            }
            //这里暂时不考虑分摊
            if (userPromotion.getAvailableQuantity() < delta) {
                continue;
            }
            toFrozen = userPromotion;
            break;
        }

        if (toFrozen == null) {
            log.error("buyer(id={}) no available promotions when frozen promotion(id={}),delta:{}",
                    buyerId, promotionId, delta);
            return false;
        }

        Response<Boolean> frozenResp = userPromotionWriteService.frozen(toFrozen.getId(), delta);
        if (!frozenResp.isSuccess()) {
            log.error("fail to frozen promotion(id={}),delta:{},cause:{}",
                    toFrozen.getId(), delta, frozenResp.getError());
            return false;
        }

        return frozenResp.getResult();
    }

    public boolean used(Long buyerId, Long promotionId, int delta) {
        List<UserPromotion> userPromotions = findUserPromotions(buyerId, promotionId);

        UserPromotion toUse = null;
        for (UserPromotion userPromotion : userPromotions) {
            if (!userPromotion.inProcess()) {
                continue;
            }
            if (userPromotion.getFrozenQuantity() < delta) {
                continue;
            }
            toUse = userPromotion;
            break;
        }

        if (toUse == null) {
            log.error("buyer(id={}) no available promotions when used promotion(id={}),delta:{}",
                    buyerId, promotionId, delta);
            return false;
        }

        Response<Boolean> useResp = userPromotionWriteService.used(toUse.getId(), delta);
        if (!useResp.isSuccess()) {
            log.error("fail to use promotion(id={}),delta:{},cause:{}",
                    toUse.getId(), delta, useResp.getError());
            return false;
        }

        return useResp.getResult();
    }

    private List<UserPromotion> findUserPromotions(Long buyerId, Long promotionId) {
        Response<List<UserPromotion>> findResp = userPromotionReadService.findByUserIdAndPromotionId(buyerId, promotionId);
        if (!findResp.isSuccess()) {
            log.error("fail to find user promotion by buyerId:{},promotionId:{},cause:{}",
                    buyerId, promotionId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        return findResp.getResult();
    }

    public boolean unfrozen(Long buyerId, Long promotionId, int delta) {
        List<UserPromotion> userPromotions = findUserPromotions(buyerId, promotionId);

        UserPromotion toFrozen = null;
        for (UserPromotion userPromotion : userPromotions) {
            if (!userPromotion.inProcess()) {
                continue;
            }
            //这里暂时不考虑分摊
            if (userPromotion.getFrozenQuantity() < delta) {
                continue;
            }
            toFrozen = userPromotion;
            break;
        }

        if (toFrozen == null) {
            log.error("buyer(id={}) no available promotions when frozen promotion(id={}),delta:{}",
                    buyerId, promotionId, delta);
            return false;
        }

        Response<Boolean> frozenResp = userPromotionWriteService.unfrozen(toFrozen.getId(), delta);
        if (!frozenResp.isSuccess()) {
            log.error("fail to unfrozen promotion(id={}),delta:{},cause:{}",
                    toFrozen.getId(), delta, frozenResp.getError());
            return false;
        }

        return frozenResp.getResult();
    }
}
