package moonstone.web.core.component.pay.allinpay;

import io.terminus.pay.model.Token;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AllInPayToken extends Token {

    /**
     * 通联支付分配的商户号
     */
    private String cusid;

    /**
     * 通联支付分配的appid
     */
    private String appid;

    /**
     * RSA 或者 SM2
     *
     * @see moonstone.web.core.component.pay.allinpay.enums.AllInPaySignTypeEnum
     */
    private String signType;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;
}
