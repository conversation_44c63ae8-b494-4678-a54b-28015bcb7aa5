package moonstone.web.core.component.order.view;

import lombok.Getter;
import lombok.Setter;
import moonstone.order.model.ShopOrder;
import org.springframework.beans.BeanUtils;

public class ShopOrderVO extends ShopOrder {
    /**
     * 用户的微信昵称
     */
    @Getter
    @Setter
    String wxUserName;

    /**
     * 门店名
     */
    @Getter
    @Setter
    private String subShopName;

    public ShopOrderVO() {

    }

    public ShopOrderVO(ShopOrder order) {
        BeanUtils.copyProperties(order, this);
    }
}
