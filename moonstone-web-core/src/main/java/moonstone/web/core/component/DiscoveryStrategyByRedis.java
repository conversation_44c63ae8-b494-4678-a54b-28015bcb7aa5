package moonstone.web.core.component;

import com.google.common.collect.ImmutableMap;
import com.hazelcast.cluster.Address;
import com.hazelcast.config.DiscoveryConfig;
import com.hazelcast.config.DiscoveryStrategyConfig;
import com.hazelcast.spi.discovery.DiscoveryNode;
import com.hazelcast.spi.discovery.integration.DiscoveryMode;
import com.hazelcast.spi.discovery.integration.DiscoveryServiceSettings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Configuration
public class DiscoveryStrategyByRedis {
    @Autowired
    private HazelcastFinderStrategyFactoryByRedis hazelcastFinderStrategyFactoryByRedis;

    @Bean
    DiscoveryServiceSettings discoveryServiceSettings() {
        DiscoveryServiceSettings discoveryServiceSettings = new DiscoveryServiceSettings();
        discoveryServiceSettings.setConfigClassLoader(getClass().getClassLoader());
        discoveryServiceSettings.setDiscoveryNode(new DiscoveryNode() {
            @Override
            public Address getPrivateAddress() {
                try {
                    return new Address(InetAddress.getLocalHost(), 6801);
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                    return new Address();
                }
            }

            @Override
            public Address getPublicAddress() {
                return getPrivateAddress();
            }

            @Override
            public Map<String, Object> getProperties() {
                return ImmutableMap.of("strategy", "redis");
            }
        });
        discoveryServiceSettings.setDiscoveryMode(DiscoveryMode.Member);
        DiscoveryConfig discoveryConfig = new DiscoveryConfig();
        DiscoveryStrategyConfig strategyConfig = new DiscoveryStrategyConfig(hazelcastFinderStrategyFactoryByRedis);
        discoveryConfig.setDiscoveryStrategyConfigs(Collections.singletonList(strategyConfig));
        discoveryConfig.setNodeFilter(Objects::nonNull);
        discoveryServiceSettings.setDiscoveryConfig(discoveryConfig);
        return discoveryServiceSettings;
    }
}
