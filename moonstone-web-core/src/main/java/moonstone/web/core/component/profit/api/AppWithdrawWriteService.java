package moonstone.web.core.component.profit.api;

import io.terminus.common.model.Response;
import moonstone.web.core.model.dto.WithdrawFeeRequest;

/**
 * 用于App提现操作
 */
public interface AppWithdrawWriteService {
    /**
     * 为App提现
     *
     * @param userId   用户Id
     * @param withdraw 提现的参数
     * @return 提现是否成功
     */
    Response<Boolean> withdraw(Long userId, WithdrawFeeRequest withdraw);
}
