package moonstone.web.core.component.profit.view;

import lombok.Data;
import moonstone.web.core.component.profit.dto.OrderProfitRecord;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

@Data
public class AppOrderProfitViewImpl implements AppOrderProfitView {
    Long orderId;
    String shopName;
    String buyerName;
    String buyerAvatarUrl;
    List<AppOrderItemView> itemList;
    BigDecimal sellerPrice;
    BigDecimal profit;
    Date createdAt;

    public static AppOrderProfitView from(OrderProfitRecord record, Function<Long, String> userAvatarTaker) {
        AppOrderProfitViewImpl view = new AppOrderProfitViewImpl();
        BeanUtils.copyProperties(record, view);
        view.setShopName(record.getOrderFrom());
        view.setItemList(new ArrayList<>(record.getOrderProfitRecordList()));
        view.setSellerPrice(new BigDecimal(record.getOrderSellPrice()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
        view.setBuyerAvatarUrl(userAvatarTaker.apply(record.getBuyerId()));
        return view;
    }

}
