package moonstone.web.core.component;

import moonstone.common.enums.BondedType;
import moonstone.order.api.OrderNeedGatherJudge;
import moonstone.order.api.OrderPushStatusPicker;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderAuthRequireJudge;
import moonstone.order.service.PushOrderJudge;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class DefaultOrderPushStatusPicker implements OrderPushStatusPicker {
    @Autowired
    private PushOrderJudge pushOrderJudge;
    @Autowired
    private OrderAuthRequireJudge orderAuthRequireJudge;
    @Autowired
    private OrderNeedGatherJudge orderNeedGatherJudge;

    @Override
    public SkuOrderPushStatus pickPushStatus(SkuOrder skuOrder) {
        if (!Objects.equals(1, skuOrder.getIsThirdPartyItem())) {
            /// 非第三方商品无需推送,自建商品的概念
            return SkuOrderPushStatus.NOT_NEED;
        }
        if (orderNeedGatherJudge.needGather(skuOrder)) {
            if (orderAuthRequireJudge.require(skuOrder)) {
                /// 需要审核 注意审核后将修改推送状态
                return SkuOrderPushStatus.WAIT_GATHER_AND_NEED_AUTH;
            }
            return SkuOrderPushStatus.WAIT_GATHER;
        } else {
            if (orderAuthRequireJudge.require(skuOrder)) {
                /// 需要审核 注意审核后将修改推送状态
                return SkuOrderPushStatus.WAITING_SELLER_AUTH;
            }
        }
        /// 暂时关闭支付单推送
        if (!pushOrderJudge.allow(skuOrder)) {
            return SkuOrderPushStatus.WAITING;
        }
        /// *设置推送状态*
        if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) {
            /// 保税商品需要支付单推送
            return SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS;
        }
        /// 完税商品直接推送
        return SkuOrderPushStatus.WAITING;
    }
}
