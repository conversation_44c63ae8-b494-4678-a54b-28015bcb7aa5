package moonstone.web.core.component.pay.allinpayyst.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.bean.BizParameter;
import com.allinpay.sdk.bean.OpenResponse;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.model.*;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.UserUtil;
import moonstone.order.api.payoperation.agentpay.AgentPayRequestResult;
import moonstone.order.api.payoperation.fundstransfer.FundsTransferRequestResult;
import moonstone.order.api.payoperation.withdraw.WithdrawRequestResult;
import moonstone.order.enu.AgentPayOrderReceiverTypeEnum;
import moonstone.order.enu.AgentPayPaymentStatusEnum;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.user.service.UserBindAllinPayService;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTToken;
import moonstone.web.core.component.pay.allinpayyst.bo.OrderAmountReceiverBO;
import moonstone.web.core.component.pay.allinpayyst.bo.OrderRefundBO;
import moonstone.web.core.component.pay.allinpayyst.bo.RedirectBO;
import moonstone.web.core.component.pay.allinpayyst.util.AllInPayYSTOpenClient;
import moonstone.web.core.component.user.UserAppInfoComponent;
import moonstone.web.core.constants.EnvironmentConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Component
public class AllInPayYSTConvertor {

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private AgentPayOrderReadService agentPayOrderReadService;

    @Resource
    private AgentPayOrderDetailReadService agentPayOrderDetailReadService;

    @Resource
    private AgentPayPaymentReadService agentPayPaymentReadService;

    @Resource
    private WithdrawPaymentReceiverReadService withdrawPaymentReceiverReadService;

    @Resource
    private UserBindAllinPayService userBindAllinPayService;

    @Resource
    private EnvironmentConfig environmentConfig;

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Resource
    private UserAppInfoComponent userAppInfoComponent;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    @Value("${agentPay.payment.callbackUrl}")
    private String agentPayCallbackUrl;

    @Value("${fundsTransfer.payment.callbackUrl}")
    private String fundsTransferCallbackUrl;

    @Value("${withdraw.apply.onlinePay.callbackUrl}")
    private String withdrawCallbackUrl;

    @Value("${enterpriseWithdraw.callbackUrl}")
    private String enterpriseWithdrawCallbackUrl;

    public static final String GATEWAY_OK = "10000";
    public static final String BUSINESS_OK = "OK";

    public static final Pattern URL_PATTERN = Pattern.compile(
            "(ht|f)tp(s?)\\:\\/\\/[0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\/?)([a-zA-Z0-9\\-\\.\\?\\,\\'\\/\\\\&%\\+\\$#_=]*)?");

    public BizParameter convertPay(PaymentParams paymentParams, AllInPayYSTToken payToken, Long userId) {
        // 查询必要的业务对象
        Long shopId = Long.parseLong(paymentParams.getSellerNo());
        var payment = paymentReadService.findByOutId(paymentParams.getTradeNo()).getResult();

        var bizParameter = new BizParameter();
        bizParameter.put("bizOrderNo", payment.getOutId());

        var buyerAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(shopId, userId);
        if (buyerAllInPay == null || StringUtils.isBlank(buyerAllInPay.getBizUserId())) {
            throw new RuntimeException("当前用户尚未注册通商云");
        }
        bizParameter.put("payerId", buyerAllInPay.getBizUserId());

        // 收款方列表, 一个是商家在通联的小B用户， 其它是参与分佣的用户
        final JSONArray receiverList = new JSONArray();
        buildAmountReceivers(payment, payToken, shopId).forEach(entity -> receiverList.add(JSON.toJSON(entity)));
        bizParameter.put("recieverList", receiverList);

        bizParameter.put("tradeCode", "3003");
        bizParameter.put("amount", payment.getFee());
        bizParameter.put("fee", 0L);
        bizParameter.put("validateType", 0L);
        bizParameter.put("backUrl", payToken.getNotifyUrl() + "/" + payment.getChannel() + "/account/" + paymentParams.getSellerNo());
        bizParameter.put("orderExpireDatetime", getExpireTime(payment.getId()));
        bizParameter.put("payMethod", buildPayMethod(paymentParams, payToken, payment, userId));
        bizParameter.put("industryCode", "2522");
        bizParameter.put("industryName", "其他");
        bizParameter.put("source", 1L);
        bizParameter.put("goodsName", paymentParams.getSubject());

        return bizParameter;
    }

    public TradeRequest convertPayResult(OpenResponse response) {
        try {
            if (!GATEWAY_OK.equals(response.getCode())) {
                return fail(PaymentChannelEnum.ALLINPAY_YST.getCode(), String.format("[%s]%s", response.getCode(), response.getMsg()),
                        JSON.toJSONString(response));
            }
            if (!BUSINESS_OK.equals(response.getSubCode())) {
                return fail(PaymentChannelEnum.ALLINPAY_YST.getCode(), String.format("[%s]%s", response.getSubCode(), response.getSubMsg()),
                        JSON.toJSONString(response));
            }

            return TradeRequest.ok(convertPayRedirect(response.getData()));
        } catch (Exception ex) {
            log.error("AllInPayYSTConvertor.convertPayResult error, ", ex);
            return TradeRequest.fail(ex.getMessage());
        }
    }

    public BizParameter convertStandardRefund(RefundParams refundParams, AllInPayYSTToken token) {
        var refund = refundReadService.findByOutId(refundParams.getRefundNo()).getResult();

        var bizParameter = new BizParameter();

        var buyerAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(refund.getShopId(), refund.getBuyerId());
        if (buyerAllInPay == null || StringUtils.isBlank(buyerAllInPay.getBizUserId())) {
            throw new RuntimeException("买家尚未注册云商通");
        }
        bizParameter.put("bizUserId", buyerAllInPay.getBizUserId());
        bizParameter.put("refundAccount", "TLT");

        bizParameter.put("bizOrderNo", refundParams.getRefundNo());
        bizParameter.put("oriBizOrderNo", refundParams.getTradeNo());
        bizParameter.put("amount", refundParams.getRefundAmount());
        bizParameter.put("refundType", "D0");
        bizParameter.put("backUrl", token.getRefundNotifyUrl() + "/" + PaymentChannelEnum.ALLINPAY_YST.getCode() +
                "/account/" + refundParams.getSellerNo());

        // 是否全额退
        boolean isFullRefund = refundParams.getRefundAmount().equals(refundParams.getTotalFee());
        if (!isFullRefund) {
            // 不是全额退，要上送 refundList
            var list = List.of(new OrderRefundBO(token.getBizUserId(), refundParams.getRefundAmount()));
            bizParameter.put("refundList", JSONArray.parseArray(JSON.toJSONString(list)));
        }

        return bizParameter;
    }

    public TradeRequest convertRefundResult(OpenResponse response) {
        try {
            if (!GATEWAY_OK.equals(response.getCode())) {
                return TradeRequest.fail(String.format("[%s]%s", response.getCode(), response.getMsg()));
            }
            if (!BUSINESS_OK.equals(response.getSubCode())) {
                return TradeRequest.fail(String.format("[%s]%s", response.getSubCode(), response.getSubMsg()));
            }

            var result = JSON.parseObject(response.getData());
            if ("fail".equals(result.get("payStatus").toString())) {
                return TradeRequest.fail(result.get("payFailMessage").toString());
            }

            return TradeRequest.ok(new Redirect(PaymentChannelEnum.ALLINPAY_YST.getCode(), false,
                    StringUtils.EMPTY, JSON.toJSONString(response)));
        } catch (Exception ex) {
            log.error("AllInPayYSTConvertor.convertRefundResult error, ", ex);
            return TradeRequest.fail(ex.getMessage());
        }
    }

    public TradeResult convertRefundCallback(JSONObject bizContent) {
        TradeResult result = new TradeResult();

        result.setChannel(PaymentChannelEnum.ALLINPAY_YST.getCode());
        try {
            result.setTradeAt(DateUtil.parseDate(bizContent.getString("payDatetime")));
        } catch (Exception e) {
            result.setTradeAt(new Date());
            log.error("AllInPayYSTConvertor.convertRefundCallback, fail to parse tradeAt={}",
                    bizContent.getString("payDatetime"), e);
        }

        result.setMerchantSerialNo(bizContent.getString("bizOrderNo"));
        result.setGatewaySerialNo(bizContent.getString("orderNo"));
        result.setPaySuccess(true);
        result.setCallbackResponse("success");

        return result;
    }

    public BizParameter convertRefundQuery(RefundQueryParams queryParams) {
        var bizParameter = new BizParameter();
        bizParameter.put("bizOrderNo", queryParams.getRefundNo());

        return bizParameter;
    }

    public TradeResult convertRefundQueryResult(OpenResponse response) {
        String channel = PaymentChannelEnum.ALLINPAY_YST.getCode();

        if (!GATEWAY_OK.equals(response.getCode())) {
            return TradeResult.fail(TradeType.REFUND, channel, String.format("[%s]%s", response.getCode(), response.getMsg()));
        }
        if (!BUSINESS_OK.equals(response.getSubCode())) {
            return TradeResult.fail(TradeType.REFUND, channel, String.format("[%s]%s", response.getSubCode(), response.getSubMsg()));
        }

        var result = JSON.parseObject(response.getData());
        if (!"4".equals(result.getString("orderStatus"))) {
            return TradeResult.fail(TradeType.REFUND, channel,
                    String.format("[%s]%s", result.getString("orderStatus"), result.getString("errorMessage")));
        }

        return convertRefundCallback(result);
    }

    public BizParameter convertAgentPayRequest(AgentPayPayment agentPayPayment, AllInPayYSTToken token) {
        var bizParameter = new BizParameter();

        var agentPayOrder = agentPayOrderReadService.findById(agentPayPayment.getAgentPayOrderId()).getResult();
        var details = agentPayOrderDetailReadService.findByAgentPayOrderId(agentPayOrder.getId()).getResult();
        var payment = paymentReadService.findById(agentPayOrder.getRelatedPaymentId()).getResult();

        bizParameter.put("bizOrderNo", agentPayPayment.getOrderNo());
        bizParameter.put("collectPayList", buildCollectPayList(payment, agentPayOrder));

        bizParameter.put("bizUserId", details.stream()
                .filter(entity -> AgentPayOrderReceiverTypeEnum.ENTERPRISE_USER.getCode().equals(entity.getReceiverType()))
                .findAny()
                .map(AgentPayOrderDetail::getReceiverId)
                .orElseThrow());

        bizParameter.put("accountSetNo", token.getAccountSetNo());
        bizParameter.put("backUrl", agentPayCallbackUrl + "/" + agentPayOrder.getId() + "/payCallback/" + agentPayPayment.getId());
        bizParameter.put("amount", agentPayPayment.getAmount());
        bizParameter.put("fee", 0L);
        bizParameter.put("splitRuleList", buildSplitRuleList(details, token));

        bizParameter.put("tradeCode", "4003");

        return bizParameter;
    }

    public AgentPayRequestResult convertAgentPayRequestResult(BizParameter bizParameter, OpenResponse response) {
        boolean success = true;
        if (!GATEWAY_OK.equals(response.getCode())) {
            success = false;
        }
        if (!BUSINESS_OK.equals(response.getSubCode())) {
            success = false;
        }

        var result = JSON.parseObject(response.getData());
        if (result != null && "fail".equals(result.getString("payStatus"))) {
            success = false;
        }

        return new AgentPayRequestResult(JSON.toJSONString(bizParameter), JSON.toJSONString(response),
                success, result != null ? result.getString("orderNo") : StringUtils.EMPTY);
    }

    public boolean convertVspTermidServiceResult(OpenResponse response, AllInPayYSTToken token) {
        if (!GATEWAY_OK.equals(response.getCode())) {
            return false;
        }
        if (!BUSINESS_OK.equals(response.getSubCode())) {
            return false;
        }

        var result = JSON.parseObject(response.getData());
        if (result != null && !BUSINESS_OK.equals(result.getString("result"))) {
            return false;
        }

        return true;
    }

    public BizParameter convertNonstandardRefund(RefundParams refundParams, List<AgentPayOrder> list,
                                                 Map<Long, List<AgentPayOrderDetail>> detailMap, AllInPayYSTToken token) {
        var refund = refundReadService.findByOutId(refundParams.getRefundNo()).getResult();

        var bizParameter = new BizParameter();

        bizParameter.put("bizOrderNo", refundParams.getRefundNo());
        bizParameter.put("oriBizOrderNo", refundParams.getTradeNo());

        var buyerAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(refund.getShopId(), refund.getBuyerId());
        if (buyerAllInPay == null || StringUtils.isBlank(buyerAllInPay.getBizUserId())) {
            throw new RuntimeException("买家尚未注册云商通");
        }
        bizParameter.put("bizUserId", buyerAllInPay.getBizUserId());

        bizParameter.put("refundType", "D0");
        bizParameter.put("orderRefundList", buildNonstandardOrderRefundList(list, detailMap));
        bizParameter.put("backUrl", token.getRefundNotifyUrl() + "/" + PaymentChannelEnum.ALLINPAY_YST.getCode() +
                "/account/" + refundParams.getSellerNo());
        bizParameter.put("amount", refundParams.getRefundAmount());

        return bizParameter;
    }

    public BizParameter convertFundsTransferRequest(FundsTransferPayment payment, AllInPayYSTToken token) {

        var bizParameter = new BizParameter();

        bizParameter.put("bizOrderNo", payment.getOrderNo());
        bizParameter.put("amount", payment.getAmount());

        var TLT_TRANSFER_REFUND_VSP = new HashMap<String, Object>();
        TLT_TRANSFER_REFUND_VSP.put("transferAmount", payment.getAmount());
        TLT_TRANSFER_REFUND_VSP.put("vspCusid", token.getVspCusid());
        var payMethod = new HashMap<String, Object>();
        payMethod.put("TLT_TRANSFER_REFUND_VSP", TLT_TRANSFER_REFUND_VSP);
        bizParameter.put("payMethod", payMethod);

        bizParameter.put("backUrl", fundsTransferCallbackUrl + "/" + payment.getPayChannel() + "/payCallback/" + payment.getId());

        return bizParameter;
    }

    public FundsTransferRequestResult convertFundsTransferRequestResult(BizParameter bizParameter, OpenResponse response) {
        boolean success = true;
        if (!GATEWAY_OK.equals(response.getCode())) {
            success = false;
        }
        if (!BUSINESS_OK.equals(response.getSubCode())) {
            success = false;
        }

        var result = JSON.parseObject(response.getData());
        if (result != null && "fail".equals(result.getString("payStatus"))) {
            success = false;
        }

        return new FundsTransferRequestResult(JSON.toJSONString(bizParameter), JSON.toJSONString(response), success,
                result != null ? result.getString("orderNo") : StringUtils.EMPTY);
    }

    public BizParameter convertWithdrawRequest(WithdrawPayment payment, AllInPayYSTToken token, AllInPayYSTOpenClient client) {
        var receiver = withdrawPaymentReceiverReadService.findByWithdrawPaymentId(payment.getId()).getResult();

        var bizParameter = new BizParameter();
        bizParameter.put("bizOrderNo", payment.getOrderNo());

        var userAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(payment.getShopId(), receiver.getUserId());
        if (userAllInPay == null || StringUtils.isBlank(userAllInPay.getBizUserId())) {
            throw new RuntimeException("提现用户尚未注册云商通");
        }
        bizParameter.put("bizUserId", userAllInPay.getBizUserId());

        bizParameter.put("accountSetNo", token.getAccountSetNo());
        bizParameter.put("amount", payment.getAmount());

        bizParameter.put("fee", 0L);
        bizParameter.put("validateType", 0L);
        bizParameter.put("backUrl", withdrawCallbackUrl + "/" + payment.getWithdrawApplyId() + "/payCallbackPost/" + payment.getId());
        log.info("提现转账卡号 {}", receiver.getAccountNo());
        bizParameter.put("bankCardNo", client.encrypt(receiver.getAccountNo()));

        bizParameter.put("bankCardPro", userAllInPay.getPayAcctType());
        bizParameter.put("withdrawType", "D0");
        bizParameter.put("industryCode", "1910");
        bizParameter.put("industryName", "其他");

        bizParameter.put("source", 2);

        return bizParameter;
    }

    public WithdrawRequestResult convertWithdrawRequestResult(BizParameter bizParameter, OpenResponse response) {
        boolean success = true;
        String errorMessage = null;
        if (!GATEWAY_OK.equals(response.getCode())) {
            success = false;
            errorMessage = response.getMsg();
        }
        if (!BUSINESS_OK.equals(response.getSubCode())) {
            success = false;
            errorMessage = response.getSubMsg();
        }

        var result = JSON.parseObject(response.getData());
        if (result != null && "fail".equals(result.getString("payStatus"))) {
            success = false;
            errorMessage = result.getString("payFailMessage");
        }

        return new WithdrawRequestResult(JSON.toJSONString(bizParameter), JSON.toJSONString(response), success,
                result != null ? result.getString("orderNo") : StringUtils.EMPTY, errorMessage);
    }

    public BizParameter convertWithdrawRequest(EnterpriseWithdraw withdraw, AllInPayYSTToken token, AllInPayYSTOpenClient client) {
        BizParameter bizParameter = new BizParameter();

        bizParameter.put("bizOrderNo", withdraw.getOrderNo());
        bizParameter.put("bizUserId", token.getBizUserId());
        bizParameter.put("accountSetNo", token.getAccountSetNo());
        bizParameter.put("amount", withdraw.getTotalOrderAmount());

        bizParameter.put("fee", 0L);
        bizParameter.put("validateType", 0L);
        bizParameter.put("backUrl", enterpriseWithdrawCallbackUrl + "/" + withdraw.getPayChannel() + "/payCallback/" + withdraw.getId());
        log.info("提现转账卡号 {}", token.getEnterpriseWithdrawAccount());
        bizParameter.put("bankCardNo", client.encrypt(token.getEnterpriseWithdrawAccount()));

        bizParameter.put("bankCardPro", 1L);
        bizParameter.put("withdrawType", "D0");
        bizParameter.put("industryCode", "1910");
        bizParameter.put("industryName", "其他");

        bizParameter.put("source", 2);
        return bizParameter;
    }

    private JSONArray buildNonstandardOrderRefundList(List<AgentPayOrder> list, Map<Long, List<AgentPayOrderDetail>> detailMap) {

        var agentPayOrderIds = list.stream().map(AgentPayOrder::getId).toList();
        var paymentMap = agentPayPaymentReadService.findMapByAgentPayOrderIds(agentPayOrderIds);

        var orderRefundList = new JSONArray();
        for (var agentPayOrder : list) {
            var agentPayPayment = paymentMap.get(agentPayOrder.getId()).stream()
                    .filter(entity -> AgentPayPaymentStatusEnum.PAID.getCode().equals(entity.getStatus()))
                    .findAny()
                    .orElseThrow();

            orderRefundList.add(buildNonstandardOrderRefund(detailMap.get(agentPayOrder.getId()), agentPayPayment));
        }

        return orderRefundList;
    }

    private JSONObject buildNonstandardOrderRefund(List<AgentPayOrderDetail> details, AgentPayPayment agentPayPayment) {
        var current = new JSONObject();

        current.put("splitBizOrderNo", agentPayPayment.getOrderNo());
        current.put("amount", agentPayPayment.getAmount());

        current.put("bizUserId", details.stream()
                .filter(entity -> AgentPayOrderReceiverTypeEnum.ENTERPRISE_USER.getCode().equals(entity.getReceiverType()))
                .map(AgentPayOrderDetail::getReceiverId)
                .findAny()
                .orElseThrow());

        var splitList = details.stream()
                .filter(entity -> !AgentPayOrderReceiverTypeEnum.ENTERPRISE_USER.getCode().equals(entity.getReceiverType()))
                .toList();
        current.put("splitRefundList", buildNonstandardSplitRefundList(splitList));
        current.put("totalSplitAmount", splitList.stream().mapToLong(AgentPayOrderDetail::getAmount).sum());

        return current;
    }

    private JSONArray buildNonstandardSplitRefundList(List<AgentPayOrderDetail> splitList) {
        JSONArray splitRuleList = new JSONArray();

        // 小C
        var subStore = splitList.stream()
                .filter(entity -> AgentPayOrderReceiverTypeEnum.SUB_STORE.getCode().equals(entity.getReceiverType()))
                .findAny()
                .orElse(null);
        if (subStore != null && subStore.getAmount() > 0) {
            HashMap<String, Object> map1 = new HashMap<>();
            map1.put("bizUserId", subStore.getReceiverId());
            map1.put("amount", subStore.getAmount());
            splitRuleList.add(new JSONObject(map1));
        }

        // 大B
        var enterprise = splitList.stream()
                .filter(entity -> AgentPayOrderReceiverTypeEnum.ENTERPRISE.getCode().equals(entity.getReceiverType()))
                .findAny()
                .orElse(null);
        if (enterprise != null && enterprise.getAmount() > 0) {
            HashMap<String, Object> map1 = new HashMap<>();
            map1.put("bizUserId", enterprise.getReceiverId());
            map1.put("amount", enterprise.getAmount());
            map1.put("accountSetNo", "100001");
            splitRuleList.add(new JSONObject(map1));
        }

        return splitRuleList;
    }

    private JSONArray buildSplitRuleList(List<AgentPayOrderDetail> details, AllInPayYSTToken token) {
        JSONArray splitRuleList = new JSONArray();

        // 小C
        var subStore = details.stream()
                .filter(entity -> AgentPayOrderReceiverTypeEnum.SUB_STORE.getCode().equals(entity.getReceiverType()))
                .findAny()
                .orElse(null);
        if (subStore != null && subStore.getAmount() > 0) {
            HashMap<String, Object> map1 = new HashMap<>();
            map1.put("bizUserId", subStore.getReceiverId());
            map1.put("amount", subStore.getAmount());
            map1.put("fee", 0L);
            splitRuleList.add(new JSONObject(map1));
        }

        // 大B
        var enterprise = details.stream()
                .filter(entity -> AgentPayOrderReceiverTypeEnum.ENTERPRISE.getCode().equals(entity.getReceiverType()))
                .findAny()
                .orElse(null);
        if (enterprise != null && enterprise.getAmount() > 0) {
            HashMap<String, Object> map1 = new HashMap<>();
            map1.put("bizUserId", enterprise.getReceiverId());
            map1.put("amount", enterprise.getAmount());
            map1.put("fee", 0L);
            map1.put("accountSetNo", "100001");
            splitRuleList.add(new JSONObject(map1));
        }

        return splitRuleList;
    }

    private JSONArray buildCollectPayList(Payment payment, AgentPayOrder agentPayOrder) {
        JSONArray collectPayList = new JSONArray();

        final HashMap<String, Object> collect1 = new HashMap<>();
        collect1.put("bizOrderNo", payment.getOutId());
        collect1.put("amount", agentPayOrder.getAmount());
        collectPayList.add(new JSONObject(collect1));

        return collectPayList;
    }

    public RedirectBO convertPayRedirect(String resultString) {
        var resultMap = JSON.parseObject(resultString, Map.class);

        boolean invokeH5 = invokeH5(resultMap.get("payInfo"));

        var miniprogramPayInfo_VSP = resultMap.get("miniprogramPayInfo_VSP");
        boolean invokeMiniProgram = miniprogramPayInfo_VSP != null;

        return new RedirectBO(PaymentChannelEnum.ALLINPAY_YST.getCode(),
                false,
                null,
                invokeMiniProgram ? miniprogramPayInfo_VSP : resultMap.get("payInfo"),
                resultString,
                invokeH5,
                invokeMiniProgram);
    }

    private boolean invokeH5(Object payInfo) {
        if (payInfo == null) {
            return false;
        }

        String string = payInfo.toString();
        if (StringUtils.isBlank(string)) {
            return false;
        }

        try {
            return URL_PATTERN.matcher(string).matches();
        } catch (Exception ex) {
            log.error("AllInPayYSTConvertor.invokeH5 judge payInfo={} error ", payInfo, ex);
            return false;
        }
    }

    private Map<String, Object> buildPayMethod(PaymentParams paymentParams, AllInPayYSTToken payToken, Payment payment, Long currentUserId) {
        var currentAppType = UserUtil.getCurrentAppType();
        if (AppTypeEnum.ALIPAY.equals(currentAppType)) {
            return buildPayMethodByAlipay(paymentParams, payToken, payment, currentUserId);
        }

        var payMethod = new HashMap<String, Object>();

        if (!environmentConfig.isOnline() && ystUseScanWeiXin(paymentParams.getSellerNo())) {
            var SCAN_WEIXIN = new HashMap<String, Object>();
            SCAN_WEIXIN.put("amount", payment.getFee());
            SCAN_WEIXIN.put("limitPay", StringUtils.EMPTY);
            payMethod.put("SCAN_WEIXIN", SCAN_WEIXIN);
            return payMethod;
        }

        if (ystUseMiniProgram(paymentParams.getSellerNo()) && StringUtils.isNotBlank(payToken.getVspMerchantid())) {
            // WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG  收银宝微信小程序收银台支付 且 集团
            var WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG = new HashMap<String, Object>();
            WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG.put("amount", payment.getFee());
            WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG.put("limitPay", StringUtils.EMPTY);
            WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG.put("vspCusid", payToken.getVspCusid());

            payMethod.put("WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG", WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG);
        } else if (ystUseMiniProgram(paymentParams.getSellerNo())) {
            // WECHATPAY_MINIPROGRAM_CASHIER_VSP  收银宝微信小程序收银台支付
            var WECHATPAY_MINIPROGRAM_CASHIER_VSP = new HashMap<String, Object>();
            WECHATPAY_MINIPROGRAM_CASHIER_VSP.put("amount", payment.getFee());
            WECHATPAY_MINIPROGRAM_CASHIER_VSP.put("limitPay", StringUtils.EMPTY);
            payMethod.put("WECHATPAY_MINIPROGRAM_CASHIER_VSP", WECHATPAY_MINIPROGRAM_CASHIER_VSP);
        } else if (StringUtils.isNotBlank(payToken.getVspMerchantid())) {
            // 集团模式
            var WECHATPAY_MINIPROGRAM_ORG = new HashMap<String, Object>();
            WECHATPAY_MINIPROGRAM_ORG.put("vspCusid", payToken.getVspCusid());
            WECHATPAY_MINIPROGRAM_ORG.put("subAppid", userAppInfoComponent.findAppId(paymentParams.getSellerNo(), paymentParams.getOpenId(), currentUserId));
            WECHATPAY_MINIPROGRAM_ORG.put("limitPay", StringUtils.EMPTY);
            WECHATPAY_MINIPROGRAM_ORG.put("amount", payment.getFee());
            WECHATPAY_MINIPROGRAM_ORG.put("acct", paymentParams.getOpenId());
            payMethod.put("WECHATPAY_MINIPROGRAM_ORG", WECHATPAY_MINIPROGRAM_ORG);
        } else {
            var WECHATPAY_MINIPROGRAM = new HashMap<String, Object>();
            WECHATPAY_MINIPROGRAM.put("limitPay", StringUtils.EMPTY);
            WECHATPAY_MINIPROGRAM.put("amount", payment.getFee());
            WECHATPAY_MINIPROGRAM.put("acct", paymentParams.getOpenId());
            WECHATPAY_MINIPROGRAM.put("subAppid", userAppInfoComponent.findAppId(paymentParams.getSellerNo(), paymentParams.getOpenId(), currentUserId));
            payMethod.put("WECHATPAY_MINIPROGRAM", WECHATPAY_MINIPROGRAM);
        }

        return payMethod;
    }

    private Map<String, Object> buildPayMethodByAlipay(PaymentParams paymentParams, AllInPayYSTToken payToken, Payment payment, Long currentUserId) {
        var payMethod = new HashMap<String, Object>();

        if (StringUtils.isNotBlank(payToken.getVspMerchantid())) {
            // 集团模式
            var ALIPAY_SERVICE_ORG = new HashMap<String, Object>();
            ALIPAY_SERVICE_ORG.put("vspCusid", payToken.getVspCusid());
            ALIPAY_SERVICE_ORG.put("acct", paymentParams.getOpenId());
            ALIPAY_SERVICE_ORG.put("amount", payment.getFee());

            payMethod.put("ALIPAY_SERVICE_ORG", ALIPAY_SERVICE_ORG);
        } else {
            var ALIPAY_SERVICE = new HashMap<String, Object>();
            ALIPAY_SERVICE.put("acct", paymentParams.getOpenId());
            ALIPAY_SERVICE.put("amount", payment.getFee());

            payMethod.put("ALIPAY_SERVICE", ALIPAY_SERVICE);
        }

        return payMethod;
    }

    private boolean ystUseMiniProgram(String shopIdString) {
        var shop = shopCacheHolder.findShopById(Long.parseLong(shopIdString));
        var extra = shop.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }

        return Boolean.TRUE.toString().equalsIgnoreCase(extra.get(ShopExtra.ystUseMiniProgram.getCode()));
    }

    private boolean ystUseScanWeiXin(String shopIdString) {
        var shop = shopCacheHolder.findShopById(Long.parseLong(shopIdString));
        var extra = shop.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }

        return Boolean.TRUE.toString().equalsIgnoreCase(extra.get(ShopExtra.ystUseScanWeiXin.getCode()));
    }

    private String getExpireTime(Long paymentId) {
        List<OrderPayment> ids = paymentReadService.findOrderIdsByPaymentId(paymentId).getResult();
        var shopOrder = shopOrderReadService.findById(ids.get(0).getOrderId()).getResult();
        var orderCreateTime = shopOrder.getCreatedAt();

        return DateUtil.toString(new Date(orderCreateTime.getTime() + (expireMinutes == null ? 120 : expireMinutes) * 60 * 1000L),
                DateUtil.YMDHMS_FORMAT);
    }

    /**
     * 构建托管代收中，收款方列表，其中必包含商家自身
     *
     * @param payment
     * @return
     */
    private List<OrderAmountReceiverBO> buildAmountReceivers(Payment payment, AllInPayYSTToken payToken, Long shopId) {
        var result = new ArrayList<OrderAmountReceiverBO>();

        // 全归商家
        var shopOwner = new OrderAmountReceiverBO();
        shopOwner.setBizUserId(payToken.getBizUserId());
        shopOwner.setAmount(payment.getFee());

        result.add(shopOwner);
        return result;
    }

    private TradeRequest fail(String channel, String error, String rawRequestString) {
        var request = new TradeRequest();
        request.setError(error);
        request.setResult(new Redirect(channel, false, null, rawRequestString));
        return request;
    }
}
