package moonstone.web.core.component.order;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.user.model.StoreIntegral;
import moonstone.web.core.component.cache.StoreIntegralCache;
import moonstone.web.core.integral.IntegralAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/7/20 11:57
 */
@Component
@Slf4j
public class IntegralPayFactory {

    @Autowired
    private PaymentReadService paymentReadService;

    @Autowired
    private PaymentWriteService paymentWriteService;

    @Autowired
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private OrderWriteService orderWriteService;

    @Autowired
    private StoreIntegralCache storeIntegralCache;

    @Autowired
    private ItemReadService itemReadService;

    @Autowired
    private PaymentLogic paymentLogic;

    @Autowired
    private IntegralAccount integralAccount;


    public Either<Boolean> onIntegralPayment(Long orderId, Long userId, Long integralFee, String outId, List<Long> orderIds) {

        log.info("[onIntegralPayment] orderId:{} userId:{} integralFee:{} outId:{} orderIds:{}", orderId, userId, integralFee, outId, orderIds);


        Response<Payment> rPayment = paymentReadService.findById(orderId);
        if (!rPayment.isSuccess()) {
            log.error("failed to find Payment(id={}), error code:{}", orderId, rPayment.getError());
            return Either.ok(false);
        }
        final Payment payment = rPayment.getResult();
        if (Objects.equal(payment.getStatus(), OrderStatus.PAID.getValue())) { //已经支付成功事件, 直接返回吧
            log.error("failed to find Payment(id={}), error code:{}", orderId, rPayment.getError());
            return Either.ok(false);
        }
        //进行积分账户扣除积分
        Either<Boolean> booleanResult = updateIntegralAcconut(orderId, userId, integralFee, outId, orderIds);

        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.info("[failed to integral for payment] IntegralFe:{} OrderId:{} UserId:{}",
                    integralFee, orderIds, userId);
            return Either.ok(false);
        }

        //皇家购买新客礼
        infoFirstBuy(orderId, userId);

        Response<List<OrderPayment>> rOrderPayments = paymentReadService.findOrderIdsByPaymentId(orderId);
        if (!rOrderPayments.isSuccess()) {
            log.error("failed to find orderIds for payment(id={}), error code:{}", orderId, rOrderPayments.getError());
            return Either.fail();
        }
        List<OrderPayment> orderPayments = rOrderPayments.getResult();

        payment.setPushStatus(PaymentPushStatus.NO_NEED_PUSH.getValue());
        for (OrderPayment orderPayment : orderPayments) {
            if (orderPayment.getOrderType() == 1) {
                // 店铺级别
                ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId()).getResult();
                List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
                for (SkuOrder skuOrder : skuOrderList) {
                    if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) {
                        payment.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                        orderWriteService.updateOrderExtra(skuOrder.getId(), OrderLevel.SKU, skuOrder.getExtra());
                    }
                }
            } else if (orderPayment.getOrderType() == 2) {
                // sku级别
                SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId()).getResult();
                if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) {
                    payment.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                    orderWriteService.updateOrderExtra(skuOrder.getId(), OrderLevel.SKU, skuOrder.getExtra());

                }
            }
        }
        Response<Boolean> paymentWriteBoolean = paymentWriteService.update(payment);

        if (!paymentWriteBoolean.isSuccess() || !paymentWriteBoolean.getResult()) {
            log.info("[failed to integral for payment] IntegralFe:{} OrderId:{} UserId:{}",
                    integralFee, orderIds, userId);
            return Either.ok(false);
        }
        if (CollectionUtils.isEmpty(orderPayments)) {
            return Either.fail();
        }
        log.info("orderStatusUpdater:{}", OrderEvent.PAY.toOrderOperation());
        //orderStatusUpdater.update(orderPayments, OrderEvent.PAY.toOrderOperation());
        //自己调方法通知自己
        Either<Boolean> acconutPayment = updateIntegralAcconutPayment(outId);

        if (!acconutPayment.isSuccess() || !acconutPayment.take()) {
            log.info("[failed to integral for payment] IntegralFe:{} OrderId:{} UserId:{}",
                    integralFee, orderIds, userId);
            return Either.ok(false);
        }
        return Either.ok(true);
    }

    private void infoFirstBuy(Long paymentId, Long userId) {
        Response<List<OrderPayment>> rOrderPayments = paymentReadService.findOrderIdsByPaymentId(paymentId);
        log.info("paymentId1:{} userId:{}", paymentId, userId);
        if (!rOrderPayments.isSuccess()) {
            log.error("failed to find orderIds for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
            return;
        }
        List<OrderPayment> orderPayments = rOrderPayments.getResult();
        for (OrderPayment orderPayment : orderPayments) {
            if (orderPayment.getOrderType() == 1) {
                // 店铺级别
                ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId()).getResult();
                List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
                for (SkuOrder skuOrder : skuOrderList) {
                    if (skuOrder.getOutFrom() != null && skuOrder.getOutFrom().equals(OrderOutFrom.SUB_STORE.Code())) {
                        log.info("skuOrder.getOutFrom：{} itemId:{} userId:{} shopId:{}", skuOrder.getOutFrom(), skuOrder.getItemId(), userId, skuOrder.getShopId());
                        //积分账户记录新客礼首次购买
                        infoUpdateIntegral(skuOrder.getItemId(), userId, skuOrder.getShopId());
                        storeIntegralCache.clearCache(skuOrder.getShopId());
                    }
                }
            } else if (orderPayment.getOrderType() == 2) {
                // sku级别
                SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId()).getResult();
                if (skuOrder.getOutFrom() != null && skuOrder.getOutFrom().equals(OrderOutFrom.SUB_STORE.Code())) {
                    log.info("skuOrder.getOutFrom：{} itemId:{} userId:{} shopId:{}", skuOrder.getOutFrom(), skuOrder.getItemId(), userId, skuOrder.getShopId());
                    //积分账户记录新客礼首次购买
                    infoUpdateIntegral(skuOrder.getItemId(), userId, skuOrder.getShopId());
                    storeIntegralCache.clearCache(skuOrder.getShopId());
                }

            }
        }
    }

    private void infoUpdateIntegral(Long itemId, Long userId, Long shopId) {
        log.info("itemId:{} userId:{} shopId:{}", itemId, userId, shopId);
        Response<Item> items = itemReadService.findById(itemId);
        if (!items.isSuccess() || items.getResult() == null) {
            log.error("find items error id:{}", itemId);
            return;
        }
        log.info("itemId:{} userId:{} shopId:{}", itemId, userId, shopId);
        if (items.getResult().getType() != null
                && items.getResult().getType().equals(4)) {
            Map<String, String> maps = new HashMap<>();
            maps.put("userId", String.valueOf(userId));
            maps.put("shopId", String.valueOf(shopId));
            maps.put("firstBuy", "true");
            integralAccount.initializeIntegralAccount(maps, 9);
        }
    }

    private Either<Boolean> updateIntegralAcconutPayment(String outId) {
        Payment payment = new Payment();
        payment.setPaidAt(new Date());
        payment.setOutId(outId);
        payment.setPaySerialNo(outId);
        payment.setPayResponse("");
        log.info("[Payments](callBack) construct payment:{}", JSON.toJSONString(payment));
        Boolean b = paymentLogic.postPay(payment);
        return Either.ok(b);
    }

    private Either<Boolean> updateIntegralAcconut(Long orderId, Long userId, Long integralFee, String outId, List<Long> orderIds) {
        log.info("updateIntegralAcconut orderIds:{}", orderIds);
        Map<String, Long> map = getLonginShopAndTradeNum(orderIds);
        if (map.isEmpty() || !map.containsKey("shopId")) {
            log.error("[IntegralGoods-useGrade] userId:{} ", userId);
            return Either.ok(false);
        }
        //todo 由于积分价格乘以100 所以支付的时候缩小100
        integralFee = integralFee / 100;
        //扣除积分
        Map<String, String> maps = new HashMap<>();
        maps.put("userId", String.valueOf(userId));
        maps.put("shopId", String.valueOf(map.get("shopId")));
        maps.put("integralFee", String.valueOf(integralFee));
        maps.put("tradeId", String.valueOf(orderIds.get(0)));
        Either<StoreIntegral> storeIntegralResult = integralAccount.initializeIntegralAccount(maps, 8);
        if (!storeIntegralResult.isSuccess() || storeIntegralResult.take() == null || ObjectUtils.isEmpty(storeIntegralResult.take().getId())) {
            log.error("[] updateIntegralAcconut:{}", maps);
            return Either.ok(false);
        }
        return Either.ok(true);
    }

    private Map<String, Long> getLonginShopAndTradeNum(List<Long> orderIds) {
        Map<String, Long> map = new HashMap<>();

        Response<List<ShopOrder>> shopOrder = shopOrderReadService.findByIds(orderIds);
        if (!shopOrder.isSuccess()) {
            log.error("[getLonginShopAndTradeNum] orderIds:{} ", orderIds);
            throw new JsonResponseException(new Translate("查找shopId失败").toString());
        }
        for (ShopOrder shopOrders : shopOrder.getResult()) {
            map.put("shopId", shopOrders.getShopId());
        }
        Response<List<SkuOrder>> skuOrder = skuOrderReadService.findByShopOrderIds(orderIds);
        if (!skuOrder.isSuccess()) {
            log.error("[getLonginShopAndTradeNum] orderIds:{} ", orderIds);
            throw new JsonResponseException(new Translate("查找skuOrder失败").toString());
        }
        return map;
    }
}
