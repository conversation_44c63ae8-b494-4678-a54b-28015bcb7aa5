package moonstone.web.core.component.publisher;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import org.springframework.stereotype.Component;

/**
 * 失效缓存发布器
 * Author:cp
 * Created on 8/1/16.
 */
@Component
@Slf4j
public class CacheInvalidatePublisher {

    public void invalidate(CacheInvalidateContext cacheInvalidateContext) {
        EventSender.publish(cacheInvalidateContext);
    }

}
