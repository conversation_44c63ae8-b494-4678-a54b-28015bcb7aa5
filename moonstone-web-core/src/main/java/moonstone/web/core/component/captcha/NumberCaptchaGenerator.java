package moonstone.web.core.component.captcha;


import com.github.cage.Cage;
import com.github.cage.IGenerator;
import com.github.cage.image.EffectConfig;
import com.github.cage.image.Painter;

import javax.annotation.PostConstruct;

public class NumberCaptchaGenerator extends CaptchaGenerator {

    private IGenerator<String> tokenGenerator;

    @PostConstruct
    public void init() {
        Painter painter = new <PERSON>(150, 70, null, null, new EffectConfig(true, true, true, true, null), null);
        tokenGenerator = new NumberTokenGenerator(4);
        cage = new Cage(painter, null, null, null, Cage.DEFAULT_COMPRESS_RATIO, tokenGenerator, null);
    }

    @Override
    public String generateCaptchaToken() {
        return tokenGenerator.next();
    }
}
