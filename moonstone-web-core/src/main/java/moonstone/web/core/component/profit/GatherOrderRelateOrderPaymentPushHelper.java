package moonstone.web.core.component.profit;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.PaymentWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class GatherOrderRelateOrderPaymentPushHelper {
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private PaymentWriteService paymentWriteService;

    /**
     * 处理采购单对应的子订单的支付单的推送状态
     *
     * @param gatherOrderId 采购单号
     */
    public void continuePaymentPushStatus(Long gatherOrderId) {
        // query shopOrder first
        Either<List<ShopOrder>> shopOrderListRes = shopOrderReadService.findByGatherOrderId(gatherOrderId);
        if (!shopOrderListRes.isSuccess()) {
            log.error("{} fail to query shopOrder by {}", LogUtil.getClassMethodName(), gatherOrderId);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("Gather's Order Payment Declare fail"
                    , String.format("fail to find shopOrder by %s, so no payment query", gatherOrderId), EmailReceiverGroup.DEVELOPER));
            return;
        }
        for (ShopOrder shopOrder : shopOrderListRes.take()) {
            for (Payment payment : paymentReadService.findByOrderIdAndOrderLevel(shopOrder.getId(), OrderLevel.SHOP).getResult()) {
                if (payment.getPushStatus() == PaymentPushStatus.WAIT_GATHER.getValue()) {
                    Payment update = new Payment();
                    update.setId(payment.getId());
                    update.setPushStatus(payment.getPushStatus());
                    if (!Optional.ofNullable(paymentWriteService.update(update).getResult()).orElse(false)) {
                        log.error("{} fail to update gatherOrder[{}] push-status", LogUtil.getClassMethodName(), gatherOrderId);
                        EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("OrderPayment", String.format("GatherOrder[%s] order[%s] fail to update payment push status", gatherOrderId, shopOrder.getId()), EmailReceiverGroup.DEVELOPER));
                    }
                }
            }
        }
        for (SkuOrder skuOrder : skuOrderReadService.findByGatherOrderId(gatherOrderId).take()) {
            for (Payment payment : paymentReadService.findByOrderIdAndOrderLevel(skuOrder.getId(), OrderLevel.SKU).getResult()) {
                if (payment.getPushStatus() == PaymentPushStatus.WAIT_GATHER.getValue()) {
                    Payment update = new Payment();
                    update.setId(payment.getId());
                    update.setPushStatus(payment.getPushStatus());
                    if (!Optional.ofNullable(paymentWriteService.update(update).getResult()).orElse(false)) {
                        log.error("{} fail to update gatherOrder[{}] push-status", LogUtil.getClassMethodName(), gatherOrderId);
                        EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("OrderPayment", String.format("GatherOrder[%s] sku-order[%s] fail to update payment push status", gatherOrderId, skuOrder.getId()), EmailReceiverGroup.DEVELOPER));
                    }
                }
            }
        }
    }
}
