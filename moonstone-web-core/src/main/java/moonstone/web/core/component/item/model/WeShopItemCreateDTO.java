package moonstone.web.core.component.item.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;

import java.util.Map;

/**
 * 当需要修改或者创建分销商品的时候 使用的实体,对某个商品进行创建WeShopItem与对应的WeShopSku
 */
@Data
public class WeShopItemCreateDTO {
    Item item;
    // Sku的价格修改方式
    Map<Sku, PriceModify> priceModifyMapBySku;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceModify {
        // 修改类型
        Integer type;
        // 价格 (由修改类型变化)
        Long price;

        Boolean taxBear = false;
    }
}
