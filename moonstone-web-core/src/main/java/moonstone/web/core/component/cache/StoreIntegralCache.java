package moonstone.web.core.component.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.model.IsFirstBuyer;
import moonstone.user.model.StoreIntegral;
import moonstone.user.service.StoreIntegralReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/9 17:47
 */
@Component
@Slf4j
public class StoreIntegralCache {

    @RpcConsumer
    private StoreIntegralReadService storeIntegralReadService;


    private final LoadingCache<Long, List<IsFirstBuyer>> storeIntegralCache
            = Caffeine.newBuilder()
            .expireAfterWrite(300, TimeUnit.SECONDS)
            .maximumSize(10000)
            .build(this::infoStoreIntegralCache);


    /**
     * 获取list
     *
     * @param shopId 所制定的店铺Id
     * @return 是否为第一次购买
     */
    public List<IsFirstBuyer> getList(Long shopId) {

        return storeIntegralCache.get(shopId);
    }

    /**
     * 击穿缓存
     */
    public void clearCache(Long shopId){
        storeIntegralCache.refresh(shopId);
    }

    private List<IsFirstBuyer> infoStoreIntegralCache(Long shopId) {
        //目前只考虑单规格的商品
        Response<List<StoreIntegral>> listResponse = storeIntegralReadService.findByShopId(shopId);
        if (!listResponse.isSuccess() || listResponse.getResult().isEmpty()) {
            return new ArrayList<>();
        }
        List<IsFirstBuyer> isFirstBuyers = new ArrayList<>();
        for (StoreIntegral storeIntegral : listResponse.getResult()) {
            IsFirstBuyer isFirstBuyer = new IsFirstBuyer();
            BeanUtils.copyProperties(storeIntegral, isFirstBuyer);
            isFirstBuyer.setIsFlag(storeIntegral.getExtra() != null && storeIntegral.getExtra().containsKey("firstBuy")
                    && storeIntegral.getExtra().get("firstBuy").equals("true"));
            isFirstBuyers.add(isFirstBuyer);
        }
        return isFirstBuyers;
    }
}
