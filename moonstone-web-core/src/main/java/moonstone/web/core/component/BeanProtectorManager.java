package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.BeanProtector;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * POJO 的Bean保护者注册器
 */
@Slf4j
@Component
public class BeanProtectorManager {
    private final ConcurrentHashMap<Object, BeanProtector<Object>> beanProtectorConcurrentHashMap = new ConcurrentHashMap<>();

    /**
     * 判断某个bean是否被保护
     *
     * @param bean bean
     * @return 是否被保护
     */
    public boolean isBeenProtected(Object bean) {
        return bean != null && bean.toString().startsWith("BeanProtectorShell");
    }

    /**
     * 创建一个新的Bean保护者
     *
     * @param bean bean
     * @return 保护者
     */
    public <T> T newProtector(T bean) {
        BeanProtector<T> beanProtector = new BeanProtector<>(bean);
        beanProtectorConcurrentHashMap.put(bean, (BeanProtector<Object>) beanProtector);
        return beanProtector.getProtector();
    }

    /**
     * 获取某个Bean的保护者
     *
     * @param bean Bean
     * @return 保护者
     */
    public <T> Optional<BeanProtector<T>> getProtector(T bean) {
        return Optional.ofNullable((BeanProtector<T>) beanProtectorConcurrentHashMap.get(bean));
    }

    /**
     * 获取所有的该类型的Bean的保护者
     *
     * @param type 类型
     * @return 列表
     */
    public <T> List<BeanProtector<T>> getProtectorList(Class<T> type) {
        List<BeanProtector<T>> beanProtectors = new ArrayList<>();
        for (Object bean : beanProtectorConcurrentHashMap.keySet()) {
            if (type.isInstance(bean)) {
                beanProtectors.add((BeanProtector<T>) beanProtectorConcurrentHashMap.get(bean));
            }
        }
        return beanProtectors;
    }
}
