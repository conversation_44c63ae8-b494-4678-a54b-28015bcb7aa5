package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.web.core.component.profit.api.AppWithdrawWriteService;
import moonstone.web.core.model.dto.WithdrawFeeRequest;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
@AllArgsConstructor
public class AppWithdrawWriterServiceImpl implements AppWithdrawWriteService {
    private final BalanceDetailManager balanceDetailManager;
    private final WithdrawAccountReadService withdrawAccountReadService;

    /**
     * 提现, 读取提现帐号 然后设置类型
     *
     * @param userId   用户Id
     * @param withdraw 提现的参数
     * @return 提现申请中无报错
     */
    @Override
    public Response<Boolean> withdraw(Long userId, WithdrawFeeRequest withdraw) {
        String uuid = UUID.randomUUID().toString();
        Optional<WithdrawAccount> accountOpt = withdrawAccountReadService.findById(withdraw.getWithdrawAccount()).orElseGet(Optional::empty);
        if (!accountOpt.isPresent()) {
            return Response.fail(new Translate("提现帐号不存在, 请确定后操作").toString());
        }
        WithdrawAccount account = accountOpt.get();
        Either<Long> result = balanceDetailManager.withdraw(userId, withdraw.getFee(), withdraw.getSourceId(), WithDrawProfitApply.WithdrawPaidType.from(account.getType()).orElse(null), account);
        if (result.isSuccess()) {
            return Response.ok(true);
        }
        log.error("{} errorCode[{}] for user[{}] to withdraw[{}] reason[{}]", LogUtil.getClassMethodName(), uuid, userId, JSON.toJSON(withdraw), result.getError());
        return Response.fail(String.format("错误:%s, 错误码 [%s]", result.getError(), uuid));
    }
}
