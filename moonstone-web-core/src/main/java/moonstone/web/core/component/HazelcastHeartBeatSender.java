package moonstone.web.core.component;

import com.hazelcast.config.Config;
import moonstone.common.utils.EventSender;
import moonstone.web.core.events.HazelcastHeartBeatByRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.net.InetAddress;

@Component
public class HazelcastHeartBeatSender {
    @Autowired
    private Config config;

    @Scheduled(cron = "0 0/30 * * * ?")
    public void sendHeartBeat() throws Exception {
        EventSender.publish(new HazelcastHeartBeatByRedis(InetAddress.getLocalHost().getHostAddress(), config.getNetworkConfig().getPort()));
    }

    @EventListener(HazelcastHeartBeatByRedis.class)
    public void respond(HazelcastHeartBeatByRedis hazelcastHeartBeatByRedis) throws Exception {
        if (hazelcastHeartBeatByRedis.getPing()) {
            EventSender.publish(new HazelcastHeartBeatByRedis(InetAddress.getLocalHost().getHostAddress(), config.getNetworkConfig().getPort(), false));
        }
    }
}
