package moonstone.web.core.component.weShop;

import lombok.AllArgsConstructor;
import moonstone.cache.WeShopCacheHolder;
import moonstone.weShop.model.WeShop;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WeShopReadHelper {
    private final WeShopCacheHolder weShopCacheHolder;

    public WeShop findWeShopByUserIdAndShopId(Long userId, Long shopId) {
        return weShopCacheHolder.findByUserIdAndShopId(userId, shopId).orElseGet(() -> weShopCacheHolder.findByUserId(userId).orElse(null));
    }
}