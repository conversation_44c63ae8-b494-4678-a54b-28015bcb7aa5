package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.pay.enums.TradeStatus;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.api.IRecordInsert;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.*;
import moonstone.common.exception.ServiceRuntimeException;
import moonstone.common.model.*;
import moonstone.common.utils.UUID;
import moonstone.common.utils.*;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.api.BankAccountJudge;
import moonstone.order.api.LinkedProfitRateGainer;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OutComeDetail;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.*;
import moonstone.order.model.*;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.service.*;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.IsFreezeAble;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserWxReadService;
import moonstone.weShop.model.WeShop;
import moonstone.web.core.component.RecordManager;
import moonstone.web.core.component.api.ProfitMakerAPIPrototype;
import moonstone.web.core.component.api.WithdrawPayService;
import moonstone.web.core.component.cache.DeveloperEmailAddressCache;
import moonstone.web.core.component.profit.dto.StoreProxyWithProfitSource;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.profit.WithdrawApplyEvent;
import moonstone.web.core.events.profit.WithdrawFailEvent;
import moonstone.web.core.events.profit.WithdrawSuccessEvent;
import moonstone.web.core.events.trade.app.OrderProfitActionRecordApp;
import moonstone.web.core.model.dto.record.ForeseeProfitSumToday;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.util.StoreProxyAuthProxy;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BalanceDetailManagerImpl implements BalanceDetailManager {
    private final MatrixCalculator<BigDecimal> profitCalculator = new MatrixCalculator<>(BigDecimal::new);
    private final BigDecimal HUNDRED_BD = new BigDecimal("100");
    @Resource
    BalanceDetailReadService balanceDetailReadService;
    @Resource
    BalanceDetailWriteService balanceDetailWriteService;
    @Resource
    ShopOrderReadService shopOrderReadService;
    @Resource
    SkuOrderReadService skuOrderReadService;
    @Resource
    SkuReadService skuReadService;
    @Resource
    SubStoreReadService subStoreReadService;
    @Resource
    WithDrawProfitApplyWriteService withDrawProfitApplyWriteService;
    @Resource
    UserWxReadService userWxReadService;
    @Resource
    StoreProxyReadService storeProxyReadService;
    @Resource
    ShopReadService shopReadService;
    @Resource
    ShopCacheHolder shopCacheHolder;
    @Autowired
    StoreProxyManager storeProxyManager;
    @Autowired
    DeveloperEmailAddressCache developerEmailAddressCache;
    @Autowired
    EnvironmentConfig environmentConfig;
    @Autowired
    List<SimpleRulerJudgeBean<WithDrawProfitApply>> withdrawAllowRuler;
    @Autowired
    WithdrawPayService withdrawPayService;
    @Autowired
    BalanceDetailManagerImpl self;
    @Resource
    IntermediateInfoReadService intermediateInfoReadService;
    @Resource
    LinkedProfitRateGainer linkedProfitRateGainer;
    @Resource
    BankAccountJudge bankAccountJudge;
    @Resource
    ShopWxaReadService shopWxaReadService;
    @Resource
    WeShopCacheHolder weShopCacheHolder;
    @Resource
    SubProxyDataReadService subProxyDataReadService;
    @Resource
    SubProxyDataWriteService subProxyDataWriteService;
    @Resource
    HonestFanDataReadService honestFanDataReadService;
    @Resource
    HonestFanDataWriteService honestFanDataWriteService;
    @Resource
    NormalFanDataReadService normalFanDataReadService;
    @Resource
    NormalFanDataWriteService normalFanDataWriteService;
    @Resource
    WithdrawPrincipleManager withdrawPrincipleManager;
    @Autowired
    OrderProfitRecordManager orderProfitRecordManager;
    @Autowired
    RecordManager recordManager;
    @Autowired
    ServiceProviderCache serviceProviderCache;

    @Autowired
    private GuiderCache guiderCache;

    @Autowired
    private BalanceDetailManager balanceDetailManager;

    /**
     * 分离出这个利润修改slice
     */
    @Autowired
    RealProfitChangeSlice realProfitChangeSlice;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    List<ProfitMakerAPIPrototype> profitMakerApiPrototypeList;
    @Autowired
    OrderProfitActionRecordApp orderProfitActionRecordApp;

    @Resource
    private AccountStatementWithdrawRelationWriteService accountStatementWithdrawRelationWriteService;

    @Resource
    private AccountStatementReadService accountStatementReadService;

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private AgentPayOrderReadService agentPayOrderReadService;

    Map<OrderOutFrom, ProfitMakerAPIPrototype> profitMakerApiPrototypeMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void loadProfitMakerApiPrototype() {
        for (ProfitMakerAPIPrototype profitMakerApiPrototype : profitMakerApiPrototypeList) {
            try {
                if (profitMakerApiPrototype.suitOrderOutFrom() != null) {
                    profitMakerApiPrototypeMap.putIfAbsent(profitMakerApiPrototype.suitOrderOutFrom(), profitMakerApiPrototype);
                }
            } catch (Exception ex) {
                log.error("{} fail to load the profitMakerAPIPrototype", LogUtil.getClassMethodName());
            }
        }
    }

    @Override
    public Response<List<InComeDetail>> getCashByStoreProxy(Long userId, Long sourceId) {
        return getCash(userId, sourceId);
    }

    /**
     * 注意返回的类型 是inComeDetail
     *
     * @param userId   钱包主人Id
     * @param sourceId 店铺Id
     * @return 钱包列表
     */
    @Override
    public Response<List<InComeDetail>> getCash(Long userId, Long sourceId) {
        try {
            return Response.ok(realProfitChangeSlice.getCash(userId, sourceId));
        } catch (Exception e) {
            log.error("{} fail to get Cash", LogUtil.getClassMethodName(), e);
            return Response.fail(Translate.of("系统错误"));
        }
    }

    @Override
    public Either<Long> withdraw(long userId, long cash, long shopId, WithDrawProfitApply.WithdrawPaidType type, WithdrawAccount account) throws RuntimeException {
        return withdraw(userId, userId, null, cash, shopId, null, type, account);
    }

    @Override
    public Either<Long> withdraw(long userId, long profitBelongUserId, Integer userRole, long cash, long shopId, List<Long> accountStatementIdList,
                                 WithDrawProfitApply.WithdrawPaidType type, WithdrawAccount account) {
        try {
            return Either.ok(self.withDraw(userId, profitBelongUserId, userRole, cash, shopId, shopId, accountStatementIdList, type, account));
        } catch (Exception exception) {
            log.error("{} fail to withdraw for [UserId => {}, Cash => {}, ShopId => {}, Type => {}, Account =>{}]", LogUtil.getClassMethodName(),
                    userId, cash, shopId, type.getName(), account, exception);
            return Either.error(exception);
        }
    }

    /**
     * 提现功能
     * /// withDraw 提现
     * /// 创建申请并且修改明细
     * /// *返回* 创建的提现申请Id
     * /// *抛出* 运行态错误用于事务处理
     *
     * @param userId   用户Id
     * @param cash     金额
     * @param shopId   店铺Id
     * @param sourceId 店铺Id
     * @param type     提现类型
     * @param account  帐号
     * @return 提现Id
     * @throws RuntimeException 提现失败
     */
    @Transactional(rollbackFor = Exception.class)
    Long withDraw(long userId, long profitBelongUserId, Integer userRole, long cash, long shopId, long sourceId,
                  List<Long> accountStatementIdList, WithDrawProfitApply.WithdrawPaidType type, WithdrawAccount account) throws RuntimeException {
        String withdrawAccount = account.getAccount();
        val cashQueryRes = getCash(profitBelongUserId, sourceId);
        if (!cashQueryRes.isSuccess()) {
            throw Translate.exceptionOf(cashQueryRes.getError());
        }
        /// 保证金额是够的
        boolean enoughCash = cashQueryRes.getResult().stream()
                .filter(InComeDetail::isPresent)
                .allMatch(dto -> dto.getFee() >= cash);
        if (!enoughCash) {
            log.warn("{} try to withDraw without enough cash,userId:{}", LogUtil.getClassMethodName("withdraw"), profitBelongUserId);
            throw Translate.exceptionOf("余额不足");
        }
        String withdrawModeByCode = Optional.ofNullable(shopReadService.findById(shopId).getResult())
                .map(Shop::getExtra).orElse(new HashMap<>(0)).getOrDefault(WithdrawBy.INDEX.getName(), WithdrawBy.DanDing.getName());
        WithdrawPrinciple withdrawPrinciple = withdrawPrincipleManager.findByShopId(sourceId).orElseGet(WithdrawPrinciple::new);
        long serviceFee = BigDecimal.valueOf(cash).divide(new BigDecimal("100"), RoundingMode.DOWN)
                .multiply(withdrawPrinciple.getRateServiceFee()).add(withdrawPrinciple.getStaticServiceFee())
                .longValue();
        /// 体现实体创建
        WithDrawProfitApply withDrawProfitApply = new WithDrawProfitApply();
        withDrawProfitApply.setUserRole(userRole);
        withDrawProfitApply.setFee(cash);
        withDrawProfitApply.setServiceFee(serviceFee);
        withDrawProfitApply.setUserId(userId);
        withDrawProfitApply.setStatus(1);
        withDrawProfitApply.setSourceId(sourceId);
        withDrawProfitApply.getExtra().put("sellerNo", "" + shopId);
        withDrawProfitApply.setWithdrawAccount(withdrawAccount);
        withDrawProfitApply.setWithdrawAccountId(account.getId());
        withDrawProfitApply.initAuth();
        withDrawProfitApply.setPaidTypeRaw(type);
        // 复制帐号的信息
        withDrawProfitApply.getExtra().putAll(account.getExtra());
        // 设置提现模式
        withDrawProfitApply.getExtra().put(WithdrawBy.INDEX.getName()
                , withdrawModeByCode);
        for (SimpleRulerJudgeBean<WithDrawProfitApply> judge : withdrawAllowRuler) {
            if (!judge.allow(withDrawProfitApply)) {
                throw new JsonResponseException(new Translate("触发限额规定").toString());
            }
        }
        //服务商信息
        serviceProviderCache.findByShopIdAndUserId(shopId, userId)
                .flatMap(sp -> serviceProviderCache.findByShopIdAndUserId(sp.getShopId(), sp.getSupperUserId()))
                .ifPresent(sp -> {
                    ServiceProvider serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(sp.getUserId(), sp.getShopId());
                    if (serviceProvider == null) {
                        return;
                    }
                    withDrawProfitApply.getExtra().put("serviceId", serviceProvider.getId());
                    withDrawProfitApply.getExtra().put("serviceName", serviceProvider.getName());
                });

        /*
         * 所有对公转账走线下转账
         */
        if (withDrawProfitApply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.BANK)) {
            withDrawProfitApply.getExtra().put(WithdrawBy.INDEX.getName(), WithdrawBy.DanDing.getName());
            withdrawModeByCode = WithdrawBy.DanDing.getName();
        }
        // 默认采取线下转账
        withDrawProfitApply.setOfflineWithdraw();
        // 微信或者云帐号则采取线上
        if (withDrawProfitApply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.WECHAT)
                || WithdrawBy.YunAccount.getName().equals(withdrawModeByCode)
                || ShopFunctionSlice.build(shopCacheHolder.findShopById(shopId)).isWithdrawRequireCertification()) {
            withDrawProfitApply.setOnlineWithdraw();
        }
        if (withdrawModeByCode.equals(WithdrawBy.YunAccount.getName())) {
            if (ObjectUtils.isEmpty(account.getRealName()) || ObjectUtils.isEmpty(account.getIdNo())) {
                throw new ServiceRuntimeException(new Translate("云帐号提现必须提供身份证信息").toString());
            }
        }

        val rPersist = withDrawProfitApplyWriteService.create(withDrawProfitApply);
        if (!rPersist.isSuccess()) {
            log.error("{} (withDraw) failed to persist entity userId:{} cash:{} cause:{}", LogUtil.getClassMethodName(), userId, cash, rPersist.getError());
            throw Translate.exceptionOf(rPersist.getError());
        }
        /// 创建明细
        OutComeDetail outComeDetail = new OutComeDetail();
        outComeDetail.setUserId(profitBelongUserId);
        outComeDetail.setSourceId(sourceId);
        outComeDetail.setChangeFee(cash);
        outComeDetail.setRelatedId(withDrawProfitApply.getId());
        outComeDetail.setStatus(outComeDetail.getStatus() | OutComeDetail.maskBit.WithDrawRelated.getValue() | IsPersistAble.maskBit.PersistAble.getValue() | BalanceDetail.presentMaskBit.Present.getValue());

        val rChange = changeRealProfit(outComeDetail);
        if (!rChange.isSuccess()) {
            log.error("{} unknown error happened,cause:", LogUtil.getClassMethodName(), rChange.getError());
            throw new RuntimeException("withDraw.failed");
        }

        for (SimpleRulerJudgeBean<WithDrawProfitApply> judge : withdrawAllowRuler) {
            if (!judge.allow(withDrawProfitApply)) {
                throw new JsonResponseException(new Translate("最终触发提现限额配置").toString());
            }
        }

        //保存提现申请单与账单的关联关系
        saveAccountStatementRelation(shopId, withDrawProfitApply, accountStatementIdList);

        //站内信，提现申请通知指定角色用户
        EventSender.sendApplicationEvent(new WithdrawApplyEvent(withDrawProfitApply.getId()));

        return withDrawProfitApply.getId();
    }

    /**
     * 保存提现申请单与账单的关联关系
     *
     * @param shopId
     * @param withDrawProfitApply
     * @param accountStatementIdList
     */
    private void saveAccountStatementRelation(long shopId, WithDrawProfitApply withDrawProfitApply, List<Long> accountStatementIdList) {
        if (CollectionUtils.isEmpty(accountStatementIdList)) {
            return;
        }

        Date now = new Date();
        var list = accountStatementIdList.stream()
                .filter(Objects::nonNull)
                .map(accountStatementId -> {
                    AccountStatementWithdrawRelation relation = new AccountStatementWithdrawRelation();
                    relation.setAccountStatementId(accountStatementId);
                    relation.setIsValid(DataValidEnum.VALID.getCode());
                    relation.setShopId(shopId);
                    relation.setWithdrawApplyId(withDrawProfitApply.getId());
                    relation.setWithdrawApplyStatus(AccountStatementWithdrawStatusEnum.WAITING_FOR_AUTH.getCode());

                    return relation;
                }).collect(Collectors.toList());

        var response = accountStatementWithdrawRelationWriteService.batchInsert(list);
        if (!response.isSuccess() || !response.getResult()) {
            throw new RuntimeException("提现申请单与账单关联关系创建失败");
        }
    }

    @Override
    public Either<Long> changeRealProfit(@NotNull BalanceDetail balanceDetail) throws RuntimeException {
        String lockName = String.format("[%s]-{%s}", BalanceDetail.class.getSimpleName(), balanceDetail.getUserId());
        String uuid = UUID.randomUUID().toString();
        log.info("{} action[UUID => {}] Trying reach the lock area [{}], Change[relatedId => {}, status => {}, type => {}]", LogUtil.getClassMethodName()
                , uuid, lockName, balanceDetail.getRelatedId(), balanceDetail.getStatus(), balanceDetail.getType());
        Lock lock = redissonClient.getLock(lockName);
        int totalTime = 0;
        for (int retry = 0; retry < 10; retry++) {
            try {
                Thread.sleep(150 * retry);
                totalTime += 150*retry;
            } catch (Exception ignore) {
            }
            try {
                if (!lock.tryLock(30, TimeUnit.SECONDS)) {
                    log.info("当前用户id {} 循环次数 {} 总共循环时间 {}ms",balanceDetail.getUserId(),retry,totalTime);
                    continue;
                }
            } catch (Exception exception) {
                log.error("{} fail to acquire lock, app is dying", LogUtil.getClassMethodName(), exception);
                return Either.error(new RuntimeException(exception));
            }
            try {
                return Either.ok(realProfitChangeSlice.changeRealProfitWithoutLock(balanceDetail));
            } catch (Exception exception) {
                if (!exception.getMessage().contains("reject.change.0")) {
                    log.error("realProfitChangeSlice.changeRealProfitWithoutLock error, relatedId={}, type={}",
                            balanceDetail.getRelatedId(), balanceDetail.getType(), exception);
                }

                return Either.error(new RuntimeException(exception.getMessage(), exception));
            } finally {
                lock.unlock();
                log.info("{} action[UUID => {}] leave lock area", LogUtil.getClassMethodName(), uuid);
            }
        }
        return Either.error(new IllegalStateException("lock acquire lock [" + lockName + "] timeout"));
    }

    /**
     * persistProfit 持久化利润,由shopOrderId获取订单信息然后自动持久化利润信息
     * shopOrder 主订单
     * present 是否为可用利润,仅在确认收货时调用是true哦!
     */


    /**
     * 发送提现成功的信息
     *
     * @param apply 提现
     */
    @Override
    public void sendWithdrawFailEvent(WithDrawProfitApply apply, String reason) {
        WithdrawFailEvent event = new WithdrawFailEvent();
        if (apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.WECHAT)) {
            event.setAccount(new Translate("微信").toString());
        } else if (apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.ALIPAY)) {
            event.setAccount(new Translate("支付宝").toString());
        } else if (apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.SelfBank) || apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.BANK)) {
            String bankName = bankAccountJudge.judge(apply.getWithdrawAccount()).getName();
            String lastSubAccount = apply.getWithdrawAccount().substring(apply.getWithdrawAccount().length() - 4);
            event.setAccount(new Translate("%s(%s)", bankName, lastSubAccount).toString());
        } else {
            event.setAccount(new Translate("其他").toString());
        }

        event.setShopId(apply.getSourceId());
        event.setUserId(apply.getUserId());
        event.setProfit(new BigDecimal(apply.getFee()).divide(new BigDecimal("100"), RoundingMode.DOWN));
        event.setTime(apply.getCreatedAt());
        event.setAccount(apply.getWithdrawAccount());
        event.setApplyId(apply.getId());
        event.setReason(reason);
        EventSender.sendApplicationEvent(event);
    }

    /**
     * 发送提现成功的信息
     *
     * @param apply 提现
     */
    @Override
    public void sendWithdrawDoneEvent(WithDrawProfitApply apply) {
        WithdrawSuccessEvent event = new WithdrawSuccessEvent();
        if (apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.WECHAT)) {
            event.setAccount(new Translate("微信").toString());
        } else if (apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.ALIPAY)) {
            event.setAccount(new Translate("支付宝").toString());
        } else if (apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.SelfBank) || apply.getPaidType().equals(WithDrawProfitApply.WithdrawPaidType.BANK)) {
            String bankName = bankAccountJudge.judge(apply.getWithdrawAccount()).getName();
            String lastSubAccount = apply.getWithdrawAccount().substring(apply.getWithdrawAccount().length() - 4);
            event.setAccount(new Translate("%s(%s)", bankName, lastSubAccount).toString());
        } else {
            event.setAccount(new Translate("其他").toString());
        }

        event.setShopId(apply.getSourceId());
        event.setUserId(apply.getUserId());
        event.setProfit(new BigDecimal(apply.getFee()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
        event.setTime(apply.getCreatedAt());
        event.setAccount(apply.getWithdrawAccount());
        event.setApplyId(apply.getId());
        event.setPaidAt(Date.from(Instant.now()));
        EventSender.sendApplicationEvent(event);
    }

    @Override
    public boolean isProfitClean(Long shopId, Long userId) {
        var cash = getCash(userId, shopId).getResult();
        if (cash.isEmpty()) {
            return true;
        }

        return cash.stream().map(BalanceDetail::getFee).allMatch(Predicate.isEqual(0L));
    }

    @Override
    public boolean isGuiderProfitClean(Long shopId, Long guiderUserId) {
        //查询所有可进行佣金结算的订单
        var orderList = findNotSettleOrder(shopId, guiderUserId);
        if (CollectionUtils.isEmpty(orderList)) {
            return true;
        }

        //若存在非“已确认收货”的订单，则佣金结算没有完成
        if (orderList.stream().anyMatch(order -> order.getStatus() != OrderStatus.CONFIRMED.getValue())) {
            return false;
        }

        return balanceDetailReadService.countNotPaid(orderList.stream()
                .filter(order -> OrderStatus.CONFIRMED.getValue() == order.getStatus())
                .map(OrderBase::getId)
                .collect(Collectors.toList())) <= 0;
    }

    private List<ShopOrder> findNotSettleOrder(Long shopId, Long guiderUserId) {
        var notSettleList = OrderStatus.getNotSettleStatus();

        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setShopId(shopId);
        orderCriteria.setRefererId(guiderUserId);
        orderCriteria.setStatus(notSettleList.stream().map(OrderStatus::getValue).collect(Collectors.toList()));
        orderCriteria.setPageNo(1);
        orderCriteria.setSize(Integer.MAX_VALUE);

        return shopOrderReadService.pagingShopOrder(orderCriteria).getData();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void payWithdraw(long shopId, User applier, WithDrawProfitApply withDrawProfitApply) {
        log.debug("{} try to pay withdraw for shopId:{} applierUserId:{} apply:{}", LogUtil.getClassMethodName(), shopId, applier.getId(), withDrawProfitApply);
        long oldSource = withDrawProfitApply.getSourceId();
        List<UserWx> applierUserWxCheckList = userWxReadService.queryByUserId(applier.getId()).getResult();
        try {
            /// 没有意义
            if (CollectionUtils.isEmpty(applierUserWxCheckList)) {
                throw new RuntimeException(String.format("[BalanceDetailManager](realWithDraw) fail to find UserWx by supplier:%d,applier:%d", shopId, applier.getId()));
            }
            String appId = shopWxaReadService.findAppIdByShopId(shopId, ShopWxaStatus.RELEASED.getValue()).orElse(null);
            UserWx applierWx =
                    applierUserWxCheckList.stream().filter(userWx -> userWx.getAppId().equals(appId)).findFirst().orElse(null);
            if (applierWx == null) {
                log.error("{} 提现失败,请检查支付配置 appId:{}", LogUtil.getClassMethodName(new Translate("微信信息查询失败").toString()), appId);
                throw new JsonResponseException("提现失败,无法获取到微信信息");
            }
            /// 理论上只取第一个也就是有关的微信用户信息
            log.info("[BalanceDetailManager](realWithDraw) try to really withdraw from applierWx:{} by withDrawProfitApplyId:{}", applierWx.getId(), withDrawProfitApply.getId());
            // 1 是测试店铺
            boolean allowWithdraw = environmentConfig.isOnline()
                    || shopId == 1
                    || shopId == 82
                    || shopId == 133;
            if (!allowWithdraw) {
                withDrawProfitApply.setWithDrawAt(Date.from(Instant.now()));
                withDrawProfitApply.setPaySerialNo("MP" + new DateTime(new Date()).toString("yyyyMMddHHmmss") + withDrawProfitApply.getId());
                withDrawProfitApply.setStatus(withDrawProfitApply.getStatus() | WithDrawProfitApply.WithdrawExtraStatus.PAID.getMaskBit());
            } else {
                withDrawProfitApply.getExtra().putIfAbsent("sellerNo", shopId + "");
                if (withDrawProfitApply.getExtra().getOrDefault("sellerNo", "0").equals("0")) {
                    withDrawProfitApply.getExtra().put("sellerNo", shopId + "");
                }
                withDrawProfitApply.setSourceId(shopId);
                log.debug("{} 开始提现, Id[{}] 使用支付数据[店铺=>{}]", LogUtil.getClassMethodName(), withDrawProfitApply.getId(), withDrawProfitApply.getSourceId());
                /// 真实环境提现  仅仅支持微信哦
                withDrawProfitApply.getExtra().computeIfAbsent("sellerNo", k -> shopId + "");
                val result = withdrawPayService.pay(applierWx, withDrawProfitApply);
                log.debug("{} with-draw-result:{}", LogUtil.getClassMethodName(), result);
                /// 提现失败则直接回滚
                if (!result.isSuccess()) {
                    sendWithdrawFailEvent(withDrawProfitApply, new Translate("微信提现失败").toString());
                    throw new RuntimeException(result.getError());
                }
                withDrawProfitApply.setPaySerialNo(result.getResult().getGatewaySerialNo());
                withDrawProfitApply.getExtra().put("merchantNo", result.getResult().getMerchantSerialNo());
                // 是否已经完成支付,如果是同步的,则SUCCESS直接进行,不然则通过回调调用提醒支付完成
                if (Objects.equals(result.getResult().getStatus(), TradeStatus.SUCCESS.value())) {
                    withDrawProfitApply.setWithDrawAt(result.getResult().getTradeAt());
                    sendWithdrawDoneEvent(withDrawProfitApply);
                    withDrawProfitApply.setStatus(withDrawProfitApply.getStatus() | WithDrawProfitApply.WithdrawExtraStatus.PAID.getMaskBit());
                    withDrawProfitApply.setStatus(withDrawProfitApply.getStatus() & ~WithDrawProfitApply.WithdrawExtraStatus.WAITING.getMaskBit());
                } else {
                    withDrawProfitApply.setStatus(withDrawProfitApply.getStatus() | WithDrawProfitApply.WithdrawExtraStatus.WAITING.getMaskBit());
                }
            }
        } catch (Exception ex) {
            if (Objects.isNull(ex.getMessage()) || !ex.getMessage().contains("不足支付订单金额")) {
                withDrawProfitApply.getExtra().put("error", Optional.ofNullable(ex.getMessage()).orElse(Translate.of("未知错误")));
                withDrawProfitApply.setStatus(WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit());
            }
            log.error("BalanceDetailManagerImpl.payWithdraw sendSms email={}, errorCode={}",
                    developerEmailAddressCache.getAllInJson(), ex.getMessage(), ex);
            throw ex;
        } finally {
            withDrawProfitApply.setSourceId(oldSource);
            withDrawProfitApplyWriteService.update(withDrawProfitApply);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean rejectWithDraw(Long operatorId, WithDrawProfitApply apply, String reason) {
        if (!apply.reject()) {
            throw new JsonResponseException(new Translate("审核失败,请刷新重试").toString());
        }

        long actualChangeFee = processRefunded(apply);

        apply.getExtra().put("reason", reason);
        apply.getExtra().put("rejectOP", operatorId.toString());
        InComeDetail inComeDetail = new InComeDetail();
        inComeDetail.setUserId(findProfitBelongUserId(apply));
        inComeDetail.setChangeFee(actualChangeFee);
        inComeDetail.setExtra(apply.getExtra());
        inComeDetail.setStatus(inComeDetail.getStatus() | BalanceDetail.maskBit.WithDrawRelated.getValue() | InComeDetail.presentMaskBit.Present.getValue() | IsPersistAble.maskBit.PersistAble.getValue());
        inComeDetail.setRelatedId(apply.getId());
        inComeDetail.setSourceId(apply.getSourceId());
        if (changeRealProfit(inComeDetail).isSuccess()) {
            boolean res = Optional.ofNullable(withDrawProfitApplyWriteService.update(apply).getResult()).orElseThrow(() -> new RuntimeException("[BalanceDetailManagerImpl](reject) fail to update apply"));
            if (res) {
                sendWithdrawFailEvent(apply, reason);
            }
            return res;
        }
        return false;
    }

    /**
     * 提现申请单下的某个订单利润，若对应的订单已完成退款：<br/>
     * 1）若当前是门店利润，则拒绝提现时，不返还该订单利润到可提现余额中，并添加一条退款支出流水；<br/>
     * 2）若当前是服务商/导购利润，由于其是基于月度账单进行提现的，所以拒绝提现时，依旧全额退还到可提现余额中
     *
     * @param apply
     * @return 拒绝该提现申请时实际要退还的可提现余额
     */
    private long processRefunded(WithDrawProfitApply apply) {
        if (SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode().equals(apply.getUserRole()) ||
                SubStoreUserIdentityEnum.STORE_GUIDER.getCode().equals(apply.getUserRole())) {
            return apply.getFee();
        }

        // 门店
        // 查询该提现单关联的已完成退款的利润明细
        var refundedProfitList = findRefundedProfitList(apply.getId());
        if (CollectionUtils.isEmpty(refundedProfitList)) {
            return apply.getFee();
        }

        // 构造并保存退款支出流水
        saveRefundRecord(refundedProfitList);

        return apply.getFee() - refundedProfitList.stream().mapToLong(BalanceDetail::getChangeFee).sum();
    }

    /**
     * 构造并保存退款支出流水
     *
     * @param refundedProfitList
     */
    private void saveRefundRecord(List<BalanceDetail> refundedProfitList) {
        if (CollectionUtils.isEmpty(refundedProfitList)) {
            return;
        }

        // 查询退款单, key=shopOrderId, value=refundId
        var orderRefundMap = findOrderRefundMap(refundedProfitList.stream().map(BalanceDetail::getRelatedId).toList());
        if (CollectionUtils.isEmpty(orderRefundMap)) {
            return;
        }

        var currentFee = getCurrentPresentCash(refundedProfitList.get(0).getUserId(), refundedProfitList.get(0).getSourceId());

        // 构造
        var list = refundedProfitList.stream()
                .map(profit -> convert(profit, orderRefundMap.get(profit.getRelatedId()), currentFee)).toList();

        // 保存
        creates(list);
    }

    private long getCurrentPresentCash(Long userId, Long shopId) {
        var cash = realProfitChangeSlice.getCash(userId, shopId);
        return cash.stream().filter(BalanceDetail::isPresent).mapToLong(BalanceDetail::getFee).findAny().orElse(0L);
    }

    private void creates(List<BalanceDetail> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var response = balanceDetailWriteService.creates(list);
        if (!response.isSuccess() || !response.getResult()) {
            throw new RuntimeException("退款利润流水创建失败");
        }
    }

    private BalanceDetail convert(BalanceDetail profit, Long refundId, Long currentFee) {
        var target = new BalanceDetail();
        BeanUtils.copyProperties(profit, target);

        target.setType(ProfitType.OutCome.getValue());
        target.setFee(currentFee);

        target.setExtra(new HashMap<>());
        target.getExtra().put(BalanceDetailExtraIndexEnum.RelatedOrder.getCode(), profit.getRelatedId() + "");
        target.getExtra().put(BalanceDetailExtraIndexEnum.OrderLevel.getCode(), profit.getOrderLevel().orElse(OrderLevel.SKU).name());
        target.getExtra().put(BalanceDetailExtraIndexEnum.orderId.getCode(), profit.getRelatedId().toString());

        target.setRelatedId(refundId);
        target.setStatus(target.getStatus()
                & (~BalanceDetail.orderRelatedMask.ShopOrder.getValue())
                & (~BalanceDetail.maskBit.OrderRelated.getValue())
                | OutComeDetail.maskBit.RefundRelated.getValue()
                | IsPersistAble.maskBit.PersistAble.getValue());

        return target;
    }

    private Map<Long, Long> findOrderRefundMap(List<Long> shopOrderIds) {
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return Collections.emptyMap();
        }

        var response = refundReadService.findRefundedByOrderIds(shopOrderIds, OrderLevel.SHOP);
        if (!response.isSuccess()) {
            throw new RuntimeException("orderRefund查询失败");
        }
        if (CollectionUtils.isEmpty(response.getResult())) {
            return Collections.emptyMap();
        }

        return response.getResult().stream().collect(Collectors.toMap(OrderRefund::getOrderId, OrderRefund::getRefundId, (k1, k2) -> k1));
    }

    /**
     * 查询该提现单关联的已完成退款的利润明细
     *
     * @param applyId
     * @return
     */
    private List<BalanceDetail> findRefundedProfitList(Long applyId) {
        var response = balanceDetailReadService.findRefundedProfitList(applyId);
        if (!response.isSuccess()) {
            throw new RuntimeException(String.format("查询该提现单关联的已完成退款的利润明细失败[applyId=%s]", applyId));
        }

        return response.getResult();
    }

    /**
     * 获取提现单对应的利润所属用户id
     *
     * @param apply
     * @return
     */
    private Long findProfitBelongUserId(WithDrawProfitApply apply) {
        var guider = guiderCache.findByShopIdAndUserId(apply.getSourceId(), apply.getUserId());
        if (guider.isEmpty()) {
            return apply.getUserId();
        }

        var profitBelongUserId = accountStatementReadService.findProfitBelongUserId(apply.getId()).getResult();
        if (profitBelongUserId == null) {
            throw new JsonResponseException(new Translate("导购的利润所属用户查询失败！").toString());
        }

        return profitBelongUserId;
    }

    /**
     * 计算待收益利润
     *
     * @param shopOrder 订单实体
     * @return 计算好的利润
     */
    private Optional<List<BalanceDetail>> makeProxyUnPresentProfit(ShopOrder shopOrder) {
        log.debug("{} shopOrderId:{} refererId:{} outFrom:{} buyerId:{}", LogUtil.getClassMethodName("try-make-profit"), shopOrder.getId(), shopOrder.getRefererId(), shopOrder.getOutFrom(), shopOrder.getBuyerId());
        if (shopOrder.getReferenceId() == null) {
            log.error("{}", LogUtil.getClassMethodName("not-reference-found"));
            return Optional.empty();
        }
        long guiderId = shopOrder.getReferenceId();
        val oGuider = storeProxyManager.getStoreProxyByShopIdAndUserId(shopOrder.getShopId(), guiderId);
        boolean trueGuider = oGuider.isPresent();
        StoreProxy guider = trueGuider ? oGuider.get() : storeProxyManager.pullOutInvitor(shopOrder.getShopId(), guiderId);
        if (!StoreProxyAuthProxy.build(guider).isAuthed() || guider.isFreeze()) {
            log.error("{} shopOrderId:{} guider:{}", LogUtil.getClassMethodName("profit-skip-proxy"), shopOrder.getId(), guider);
            return Optional.empty();
        }
        List<StoreProxyWithProfitSource> profitGainProxy = new ArrayList<>();
        // 添加自己为第一层利润获取者
        profitGainProxy.add(StoreProxyWithProfitSource.forReachProfit(guider));
        // 获取上层渠道加入到利润获取者
        pullOutSupper(guider).stream().map(StoreProxyWithProfitSource::forTransmitProfit).forEach(profitGainProxy::add);
        // 获取用户的拉新导购或者自己进入到利润获取者
        Map<String, String> shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(shopOrder.getShopId())).map(Shop::getExtra).orElse(new HashMap<>(8));
        if ("1".equals(shopExtra.getOrDefault(ShopExtra.openFans.getCode(), "1"))) {
            storeProxyManager.getHonestedProxy(shopOrder.getBuyerId(), shopOrder.getShopId(), trueGuider ? guider : null)
                    .map(StoreProxyWithProfitSource::forLinkedProfit)
                    .ifPresent(profitGainProxy::add);
        }
        log.debug("{} profit-gain-proxy:{}", LogUtil.getClassMethodName(), profitGainProxy);
        // 设置缓存
        log.debug("{} shopOrderId:{} make profit for profit-gain-proxy list size:{}\r value:{}"
                , LogUtil.getClassMethodName()
                , shopOrder.getId()
                , profitGainProxy.size()
                , Arrays.toString(profitGainProxy.toArray()));
        boolean shopAllowSelfBuy = "true".equals(shopExtra.getOrDefault(ShopExtra.allowSelfBuyProfit.getCode(), "true"));
        Predicate<StoreProxy> isAuthed = storeProxy -> StoreProxyAuthProxy.build(storeProxy).isAuthed();
        // 筛选有效用户且计算利润
        List<BalanceDetail> calProfitList = profitGainProxy.stream()
                /// 再次筛选去除冻结或者未授权用户
                .filter(IsFreezeAble::notFreeze)
                .filter(isAuthed)
                .map(generateProfitGainer(shopOrder))
                .filter(Optional::isPresent)
                .map(Optional::get)
                // 拒绝自购出现的利润
                .filter(detail -> !Objects.equals(detail.getUserId(), shopOrder.getBuyerId())
                        || !Objects.equals(detail.getSourceId(), 33L) || shopAllowSelfBuy)
                .collect(Collectors.toList());
        return calProfitList.isEmpty() ? Optional.empty() : Optional.of(calProfitList);
    }

    /**
     * 根据订单生成由代理获取佣金的函数
     *
     * @param shopOrder 订单
     * @return 由代理生成佣金
     */
    private Function<StoreProxyWithProfitSource, Optional<BalanceDetail>> generateProfitGainer(ShopOrder shopOrder) {
        log.debug("{} init profit gainer for ShopOrderId:{}", LogUtil.getClassMethodName(), shopOrder.getId());
        return (proxy) -> {
            InComeDetail inComeDetail = new InComeDetail();
            inComeDetail.setUserId(proxy.getUserId());
            inComeDetail.setSourceId(proxy.getShopId());
            inComeDetail.setType(ProfitType.InCome.getValue());
            inComeDetail.setStatus(1 | OrderRelated.orderRelatedMask.ShopOrder.getValue()
                    | BalanceDetail.maskBit.OrderRelated.getValue()
                    | IsPersistAble.maskBit.PersistAble.getValue()
                    // 设置状态
                    | proxy.getBalanceSource().getBitMark()
            );
            // 设置其佣金来源
            inComeDetail.setFromSource(proxy.getBalanceSource());
            inComeDetail.setRelatedId(shopOrder.getId());
            // 设置佣金
            switch (proxy.getBalanceSource()) {
                case Linked: {
                    //boolean withExtraProfit = !honestFanDataReadService.isAFan(shopOrder.getBuyerId(), shopOrder.getShopId()).orElse(true);
                    inComeDetail.setChangeFee(linkedProfitRateGainer.getLinkedFeeFromOrder(shopOrder, false));
                    break;
                }
                case Transmit:
                case Reach:
                    calProxyProfitByLevelAndOrder(shopOrder, proxy.getLevel(), proxy.getBalanceSource().equals(BalanceDetail.SourceMark.Transmit)).ifPresent(inComeDetail::setChangeFee);
                default:
            }
            if (inComeDetail.getChangeFee() == null) {
                return Optional.empty();
            }
            log.info("{} inComeDetail:{} for proxy:{}", LogUtil.getClassMethodName("make-income-detail"), JSON.toJSON(inComeDetail), proxy.getUserName());
            return Optional.of((BalanceDetail) inComeDetail);
        };
    }

    <T> T identity(T t) {
        return t;
    }

    /**
     * 为阶梯分销计算佣金逻辑
     * 首先获取利润比与固定利润,然后获取sku列表,根据每个sku的原价计算利润,然后与skuOrder中的数量相乘再总和即是真实利润
     * 默认利润比为0L
     *
     * @param shopOrder     主订单
     * @param isSupperProxy 是否为上级利润计算
     */
    private OptionalLong calProxyProfitByLevelAndOrder(ShopOrder shopOrder, Integer level, boolean isSupperProxy) {
        List<SkuOrder> skuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            return OptionalLong.empty();
        }
        Map<Long, IntermediateInfo> skuIdMapRate = skuOrders.stream().map(SkuOrder::getSkuId)
                .map(skuId -> intermediateInfoReadService.findByThirdAndType(skuId, ThirdIntermediateType.SKU.getValue()).orElse(Collections.emptyList()))
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .filter(info -> Objects.equals(1, info.getIsCommission()))
                .collect(Collectors.toMap(IntermediateInfo::getThirdId, this::identity));
        IntermediateInfo shopRateInfo = intermediateInfoReadService.findByThirdAndType(shopOrder.getShopId(), ThirdIntermediateType.SHOP.getValue()).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        if (shopRateInfo == null) {
            log.error("{} no shop-level profit-set for shopId({})", LogUtil.getClassMethodName(), shopOrder.getShopId());
            shopRateInfo = new IntermediateInfo();
            shopRateInfo.setFirstFee(0L);
            shopRateInfo.setFirstRate(0L);
            shopRateInfo.setSecondFee(0L);
            shopRateInfo.setSecondRate(0L);
        }
        long profit = 0L;
        try {
            Map<Long, Sku> skuCacheMap = new HashMap<>(8);
            for (SkuOrder skuOrder : skuOrders) {
                // 抽离利润计算逻辑, 这里只做总和与数据统计
                if ("true".equals(Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new).getOrDefault("freeGift", "false"))) {
                    continue;
                }
                IntermediateInfo rateInfo = skuIdMapRate.getOrDefault(skuOrder.getSkuId(), shopRateInfo);
                rateInfo.fillNullWithZero();
                log.debug("{} skuOrder:{} skuId:{} rateInfo:{}", LogUtil.getClassMethodName("cal-profit-by-rate"), skuOrder, skuOrder.getSkuId(), rateInfo);
                Sku sku = skuCacheMap.getOrDefault(skuOrder.getSkuId(), skuReadService.findSkuById(skuOrder.getSkuId()).getResult());
                skuCacheMap.put(sku.getId(), sku);
                BigDecimal skuOrderProfit;

                // 默认优先身份佣金
                if (!ObjectUtils.isEmpty(rateInfo.getExtraFeeCalMatrix())) {
                    List<BigDecimal> itemPriceList = new ArrayList<>();
                    for (int i = 1; i < level; i++) {
                        itemPriceList.add(null);
                    }
                    itemPriceList.add(BigDecimal.valueOf(sku.getPrice()).divide(HUNDRED_BD, RoundingMode.DOWN));
                    skuOrderProfit = ObjectUtils.isEmpty(rateInfo.getExtraFeeCalMatrix()) ? BigDecimal.ZERO :
                            profitCalculator.calWithMatrixByLine(rateInfo.getExtraFeeCalMatrix(), itemPriceList.toArray(new BigDecimal[]{}))
                                    .orElse(BigDecimal.ZERO)
                                    .multiply(HUNDRED_BD)
                    ;
                } else {
                    BigDecimal stepProfit = new BigDecimal((isSupperProxy ? rateInfo.getSecondRate() : rateInfo.getFirstRate()) * sku.getPrice());
                    BigDecimal extraProfit = new BigDecimal((isSupperProxy ? rateInfo.getSecondFee() : rateInfo.getFirstFee()));
                    if (rateInfo.getVersion() == rateInfo.getNowVersion()) {
                        // 新版本为万分制  直接/10000即可
                        stepProfit = (stepProfit).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
                    } else {
                        // 老版本为百分制  直接/100即可
                        stepProfit = (stepProfit).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    }
                    skuOrderProfit = stepProfit.add(extraProfit);
                }
                profit += skuOrderProfit.multiply(new BigDecimal(skuOrder.getQuantity())).setScale(0, RoundingMode.HALF_UP).longValue();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} cal failed skuIds:{},{}", LogUtil.getClassMethodName(), Arrays.toString(skuOrders.stream().map(SkuOrder::getSkuId).toArray()), ex.getStackTrace()[0].getLineNumber());
            return OptionalLong.empty();
        }
        return OptionalLong.of(profit);
    }

    /**
     * 拉取上级代理
     *
     * @param proxy 该级代理
     */
    private List<StoreProxy> pullOutSupper(StoreProxy proxy) {
        /// 拉出上级代理　仅一位
        List<StoreProxy> storeProxies = new ArrayList<>();
        if (proxy.getSupperId() != null) {
            Optional<StoreProxy> rSupper = storeProxyManager.getStoreProxyByShopIdAndUserId(proxy.getShopId(), proxy.getSupperId());
            rSupper.ifPresent(storeProxies::add);
        }
        log.debug("{} proxy:{} supperList:{}", LogUtil.getClassMethodName(), proxy, JSON.toJSONString(storeProxies));
        return storeProxies;
    }

    @Override
    public List<InComeDetail> initCash(long userId, long sourceId) {
        return realProfitChangeSlice.initCash(userId, sourceId);
    }

    /**
     * 修改下级代理信息,包括创建代理与修改代理的订单数量以及交易总额和收益总额
     *
     * @param orderId  订单id
     * @param proxyId  代理的userId
     * @param shopId   订单发生的商店id
     * @param orderFee 订单金额
     * @param profit   利润
     */
    private void changeProxyOrderAndTradeInfoSum(long orderId, long proxyId, long shopId, long orderFee, Long profit) {
        log.info("{} proxyId={},shopId={}", LogUtil.getClassMethodName(), proxyId, shopId);
        val rEntity = subProxyDataReadService.findByUserId(proxyId, shopId);
        if (!rEntity.isSuccess()) {
            log.error("{} proxyId:{} shopId:{}", LogUtil.getClassMethodName(), profit, shopId);
            return;
        }
        /// 下级代理人
        if (rEntity.take().isEmpty()) {
            SubProxySum subProxySum = new SubProxySum();
            subProxySum.setShopId(shopId);
            subProxySum.setFanNum(1L);
            subProxySum.setIncomeSum(profit > 0 ? profit : 0);
            subProxySum.setTradeFeeSum(orderFee > 0 ? orderFee : 0);
            subProxySum.setUserId(proxyId);
            subProxySum.setOrderNum(orderFee > 0 ? 1L : 0L);
            subProxySum.setSupperProxyId(storeProxyReadService.findByShopIdAndUserId(shopId, proxyId).orElse(Optional.empty()).map(StoreProxy::getSupperId).orElse(null));
            log.debug("{} proxyId={},shopId={},ss={}", LogUtil.getClassMethodName(), proxyId, shopId, subProxySum.getShopId() + "-" + subProxySum.getSupperProxyId());
            val rCreate = subProxyDataWriteService.create(subProxySum);
            log.debug("{} proxyId={},shopId={},rCreate={}", LogUtil.getClassMethodName(), proxyId, shopId, rCreate);
            if (!rCreate.isSuccess()) {
                log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName("create-subProxy"), shopId, proxyId);
                return;
            }
        } else {
            val rU2 = subProxyDataWriteService.increaseTrade(proxyId, shopId, orderFee);
            val rU = subProxyDataWriteService.increaseIncome(proxyId, shopId, profit);
            // 修改代理单数 如果金额为0 则不变，大于0 则加一 反之减一
            subProxyDataWriteService.changeOrderNum(proxyId, shopId, Long.compare(orderFee, 0L)).logException(ex -> log.error("{} ex:{} orderId:{} proxyId:{} shopId:{}", LogUtil.getClassMethodName(), ex, orderId, proxyId, shopId));
            log.debug("{} updateSumData tradeResult:{} incomeResult:{} proxyId:{} shopId:{}", LogUtil.getClassMethodName(), rU2.isSuccess(), rU.isSuccess(), proxyId, shopId);
            if (!(rU.isSuccess() && rU2.isSuccess())) {
                log.error("{} shopOrderId:{} proxyId:{}", LogUtil.getClassMethodName("update-profit"), orderId, proxyId);
                return;
            }
        }
        subProxyDataWriteService.sumFan(proxyId, shopId);
    }

    /**
     * 修改下级代理信息,包括创建代理与修改代理的订单数量以及交易总额和收益总额
     *
     * @param orderId  订单id
     * @param buyerId  购买人id
     * @param proxyId  代理的userId
     * @param shopId   订单发生的商店id
     * @param orderFee 订单金额
     * @param profit   利润
     */
    private void changeNormalFanOrderAndTradeInfoSum(long orderId, long buyerId, long proxyId, long shopId, long orderFee, Long profit) {
        if (honestFanDataReadService.findByUserId(buyerId, shopId).orElse(Optional.empty()).map(HonestFanSum::getProxyId).orElse(-1L).equals(proxyId)) {
            return;
        }
        log.debug("{} orderId:{} buyerId:{} proxyId:{} shopId:{} orderFee:{} profit:{}", LogUtil.getClassMethodName(),
                orderId, buyerId, proxyId, shopId, orderFee, profit);
        val rFanData = normalFanDataReadService.findByUserIdAndProxyId(buyerId, shopId, proxyId);
        log.debug("{} old  Data:{}", LogUtil.getClassMethodName(), rFanData.orElse(Optional.empty()).map(JSON::toJSONString).orElse("null"));
        if (!rFanData.isSuccess()) {
            log.error("{} orderId:{} buyerId:{}  proxyId:{}  shopId:{}  orderFee:{}  profit:{}", LogUtil.getClassMethodName("find-normal-fan"), orderId, buyerId, proxyId, shopId, orderFee, profit);
            return;
        }
        if (rFanData.take().isEmpty()) {
            NormalFanData fanData = new NormalFanData();
            fanData.setTradeTimes(orderFee > 0 ? 1L : 0L);
            fanData.setIncomeSum(profit > 0 ? profit : 0);
            fanData.setTradeFeeSum(orderFee > 0 ? orderFee : 0);
            fanData.setProxyId(proxyId);
            fanData.setShopId(shopId);
            fanData.setUserId(buyerId);
            val rCreate = normalFanDataWriteService.createNormalFanData(fanData);
            if (!rCreate.isSuccess()) {
                log.error("{} entity:{} orderId:{}", LogUtil.getClassMethodName("create-normal-fan"), fanData, orderId);
            }
        } else {
            val rUpdate = normalFanDataWriteService.increaseData(buyerId, shopId, proxyId, orderFee, profit);
            if (!rUpdate.isSuccess()) {
                log.error("{} orderId:{} buyerId:{}  proxyId:{}  shopId:{}  orderFee:{}  profit:{}", LogUtil.getClassMethodName("update-normal-fan"), orderId, buyerId, proxyId, shopId, orderFee, profit);
            }
        }
    }

    /**
     * 计算订单利润,并不存入数据库
     *
     * @param shopOrder 订单信息
     * @param outFrom   订单来源
     * @param present   利润是否可使用状态(如果是下单则为false,确认收货为true)
     */
    @Override
    public List<BalanceDetail> calculateProfit(ShopOrder shopOrder, OrderOutFrom outFrom, boolean present) {
        if (shopOrder == null) {
            log.error("{} outFrom[{}] present[{}] null order", LogUtil.getClassMethodName(), outFrom, present);
            throw new IllegalArgumentException("require.noNull.shopOrder");
        }
        log.debug("{} shopOrderId[{}] refererId[{}] outFrom[{}] present[{}]", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getReferenceId(), outFrom, present);
        if (outFrom == null || (shopOrder.getReferenceId() == null && shopOrder.getOutShopId() == null)) {
            log.warn("{} outFrom[{}] refererId[{}] shopOrderId[{}]", LogUtil.getClassMethodName(), outFrom, shopOrder.getReferenceId(), shopOrder.getId());
            return new ArrayList<>();
        }
        ProfitMakerAPIPrototype profitMaker = profitMakerApiPrototypeMap.get(outFrom);
        if (profitMaker != null) {
            if (!present) {
                return profitMaker.earnForeseeProfit(shopOrder, null);
            } else {
                return profitMaker.convertForeseeProfitIntoPresentProfit(shopOrder, null);
            }
        } else {
            if (outFrom == OrderOutFrom.LEVEL_Distribution) {
                return present ? makeProxyPresentProfit(shopOrder)
                        : makeProxyUnPresentProfit(shopOrder).orElseGet(ArrayList::new);
            } else {
                log.info("[makeProfit-default] {} shopOrderId:{} outFrom:{}", LogUtil.getClassMethodName("leave-to-others"), shopOrder.getId(), outFrom);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 生成处理记录代理统计数据变化的函数
     *
     * @param shopOrder 该收益变化来源的订单
     * @return 记录函数
     */
    @Override
    public Consumer<BalanceDetail> generateRecordExecutor(ShopOrder shopOrder) {
        if (Objects.isNull(shopOrder.getReferenceId()) && Objects.isNull(shopOrder.getOutShopId())) {
            log.warn("{} Order[Id => {}, OutFrom => {}] bring no referenceId", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getOutFrom(), new Throwable("Trace The Method Execute"));
            return profit -> {
            };
        }
        final long proxyId = Optional.ofNullable(shopOrder.getReferenceId())
                .orElseGet(() -> Long.parseLong(shopOrder.getOutShopId()));
        final long buyerId = shopOrder.getBuyerId();
        final OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
        log.debug("{} shopOrderId:{} proxyId={},buyerId={}", LogUtil.getClassMethodName(), shopOrder.getId(), proxyId, buyerId);
        // 退款支出模式下 判断是否为今天的利润, 如果是今天的 才允许扣减 不然不扣减
        Function<BalanceDetail, Long> profitCalculate = profit -> {
            if (Objects.equals(profit.getType(), 1)) {
                return profit.getChangeFee();
            }
            boolean isTodayProfit = Objects.isNull(profit.getCreatedAt()) || LocalDateTime.ofInstant(profit.getCreatedAt().toInstant(), ZoneId.systemDefault())
                    .toLocalDate().isEqual(LocalDate.now());
            return isTodayProfit ? -profit.getChangeFee() : 0L;
        };
        switch (outFrom) {
            case LEVEL_Distribution: {
                Consumer<BalanceDetail> recordStoreProxyData = profit -> {
                    try {   // 万能try-catch
                        recordProxyFanProfit(shopOrder, profit);
                    } catch (Exception ex) {
                        log.error("{} fail to record fan profit for order[{}] shop[{}] owner[{}] profit[{}]", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getShopId(), profit.getUserId(), profit.getChangeFee(), ex);
                        EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("recordProxyFanProfit", new Translate("订单[%s]生成粉丝佣金记录失败,店铺[%s] 佣金拥有人[%s] 金额[%s]", shopOrder.getId(), shopOrder.getShopId(), profit.getUserId(), profit.getChangeFee()).toString(), ex, EmailReceiverGroup.DEVELOPER));
                    }
                };
                return recordStoreProxyData.andThen(balanceDetail ->
                        Optional.ofNullable(balanceDetail)
                                .map(profitCalculate)
                                .ifPresent(profit -> recordTodayProfit(shopOrder, profit, outFrom)));
            }
            case SUB_STORE:
            case WE_SHOP:
            default:
                return balanceDetail -> Optional.ofNullable(balanceDetail)
                        .map(profitCalculate)
                        .ifPresent(profit -> recordTodayProfit(shopOrder, profit, outFrom));
        }
    }

    /**
     * 为某个订单的利润记录其下级带来的利润信息
     * 会查询忠粉数据进行查询,代码很臭 请小心阅读与修改
     *
     * @param shopOrder     订单
     * @param balanceDetail 利润
     */
    private void recordProxyFanProfit(ShopOrder shopOrder, BalanceDetail balanceDetail) {
        Long changeFee = balanceDetail.getType() == ProfitType.InCome.getValue() ? shopOrder.getOriginFee() : -shopOrder.getOriginFee();
        Long changeProfitFee = balanceDetail.getType() == ProfitType.InCome.getValue() ? balanceDetail.getChangeFee() : -balanceDetail.getChangeFee();
        log.debug("{} detail:{} changeFee:{} changeProfitFee:{}", LogUtil.getClassMethodName(), JSON.toJSON(balanceDetail), changeFee, changeProfitFee);
        if (balanceDetail.isReach()) {
            // 修改普通粉丝信息
            changeNormalFanOrderAndTradeInfoSum(shopOrder.getId(), shopOrder.getBuyerId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeFee, changeProfitFee);
            // 修改忠粉信息
            ifHonestFanThenModifyIncome(shopOrder.getId(), shopOrder.getBuyerId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeFee, changeProfitFee);
            // 修改代理信息
            changeProxyOrderAndTradeInfoSum(shopOrder.getId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeFee, changeProfitFee);
        }
        if (balanceDetail.isTransmit()) {
            // 修改上级数据
            changeSuperProxyOrderAndProfitInfoSum(shopOrder.getId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeFee, changeProfitFee);
            // 设置本人代理的贡献数据
            contributeProfit(shopOrder.getReferenceId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeProfitFee);
        }
        if (balanceDetail.isLinked()) {
            boolean owned = Objects.equals(balanceDetail.getUserId(),
                    storeProxyManager.getStoreProxyByShopIdAndUserId(shopOrder.getShopId(), shopOrder.getRefererId())
                            .map(StoreProxy::getSupperId)
                            .orElse(null)
            );
            // 修改忠粉信息
            changeHonestInCome(shopOrder.getId(), shopOrder.getBuyerId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeFee, changeProfitFee);
            owned = owned | Objects.equals(balanceDetail.getUserId(), shopOrder.getReferenceId());
            if (!owned) {
                // 当该数据仅仅为单独的拉新佣金时，则添加所有数据
                changeProxyOrderAndTradeInfoSum(shopOrder.getId(), balanceDetail.getUserId(), shopOrder.getShopId(), changeFee, changeProfitFee);
            } else {
                // 当该收益有任何相关收益时，如一级或者二级佣金 则不添加该收益数据
                changeProxyOrderAndTradeInfoSum(shopOrder.getId(), balanceDetail.getUserId(), shopOrder.getShopId(), 0L, changeProfitFee);
            }
        }
    }


    /**
     * 某个用户为某个用户贡献利润，并且将其记录
     *
     * @param contributeUserId 主要为订单来源的推荐人
     * @param benefitUserId    通常为推荐人的上级
     * @param shopId           平台id
     * @param changeProfitFee  贡献出多少利润
     */
    private void contributeProfit(Long contributeUserId, Long benefitUserId, Long shopId, Long changeProfitFee) {
        if (subProxyDataReadService.findByUserId(contributeUserId, shopId).orElse(Optional.empty()).isEmpty()) {
            SubProxySum initEntity = new SubProxySum();
            initEntity.setUserId(contributeUserId);
            initEntity.setShopId(shopId);
            initEntity.setSupperProxyId(benefitUserId);
            subProxyDataWriteService.create(initEntity);
        }
        subProxyDataWriteService.modifyContribute(contributeUserId, benefitUserId, shopId, changeProfitFee);
    }

    private void ifHonestFanThenModifyIncome(Long orderId, Long buyerId, Long proxyId, Long shopId, Long changeFee, Long changeProfitFee) {
        boolean ifHonest = honestFanDataReadService.findByUserId(buyerId, shopId).orElse(Optional.empty())
                .filter(fan -> fan.getProxyId().equals(proxyId)).isPresent();
        if (!ifHonest) {
            return;
        }
        honestFanDataWriteService.increaseData(buyerId, shopId, 0L, changeProfitFee, 0L).logException(ex -> {
            ex.printStackTrace();
            log.error("{} orderId:{} buyerId:{} proxyId:{} shopId:{} changeFee:({}:0) changeProfitFee:{}", LogUtil.getClassMethodName(), orderId, buyerId, proxyId, shopId, changeFee, changeProfitFee);
        });
    }

    private void changeHonestInCome(Long orderId, Long userId, Long proxyId, Long shopId, Long originFee, Long honestIncome) {
        Function<NormalFanData, HonestFanSum> upgradeFan = (normalFan) -> {
            HonestFanSum honestFanSum = new HonestFanSum();
            honestFanSum.setProxyId(proxyId);
            honestFanSum.setUserId(userId);
            honestFanSum.setShopId(shopId);
            honestFanSum.setHonestIncomeSum(0L);
            honestFanSum.setTradeFeeSum(normalFan.getTradeFeeSum() > originFee ? normalFan.getTradeFeeSum() - originFee : 0);
            honestFanSum.setTradeTimes(normalFan.getTradeTimes() > 1 ? normalFan.getTradeTimes() - 1 : 0);
            honestFanSum.setIncomeSum(normalFan.getIncomeSum());
            return honestFanSum;
        };
        normalFanDataReadService.findByUserIdAndProxyId(userId, shopId, proxyId).orElse(Optional.empty())
                .map(upgradeFan)
                .ifPresent(entity -> {
                    Either<Boolean> rRemove = normalFanDataWriteService.remove(userId, shopId, proxyId);
                    Either<Boolean> rCreate = honestFanDataWriteService.create(entity);
                    if (!rRemove.isSuccess() || !rRemove.take()) {
                        log.error("{} remove normal userId:{} shopId:{} proxyId:{}", LogUtil.getClassMethodName(), userId, shopId, proxyId);
                    }
                    if (!rCreate.isSuccess() || !rCreate.take()) {
                        log.error("{} create honest entity:{}", LogUtil.getClassMethodName(), entity);
                    }
                });
        val rOptFanData = honestFanDataReadService.findByUserId(userId, shopId);
        if (!rOptFanData.isSuccess()) {
            log.error("{} orderId:{} buyerId:{} shopId:{} originFee:{} honestIncome:{}", LogUtil.getClassMethodName(), orderId, userId, shopId, originFee, honestIncome);
            return;
        }
        if (rOptFanData.take().isEmpty()) {
            HonestFanSum fanSum = new HonestFanSum();
            fanSum.setShopId(shopId);
            fanSum.setUserId(userId);
            fanSum.setProxyId(proxyId);
            fanSum.setTradeTimes(1L);
            fanSum.setTradeFeeSum(originFee > 0 ? originFee : 0);
            fanSum.setIncomeSum(0L);
            fanSum.setHonestIncomeSum(honestIncome);
            val rCreate = honestFanDataWriteService.create(fanSum);
            if (!rCreate.isSuccess()) {
                log.error("{} orderId:{} buyerId:{} shopId:{} originFee:{} honestIncome:{}", LogUtil.getClassMethodName("update-sum-data"), orderId, userId, shopId, originFee, honestIncome);
            }
        } else {
            val rUpdate = honestFanDataWriteService.increaseData(userId, shopId, originFee, 0L, honestIncome);
            if (!rUpdate.isSuccess()) {
                log.error("{} orderId:{} buyerId:{} shopId:{} originFee:{} honestIncome:{}", LogUtil.getClassMethodName("update-sum-data"), orderId, userId, shopId, originFee, honestIncome);
            }
        }
    }

    /**
     * 修改代理的收益记录
     *
     * @param orderId   这次修改发起的订单号
     * @param userId    用户Id
     * @param shopId    店铺Id
     * @param orderFee  订单金额
     * @param profitFee 利润
     */
    private void changeSuperProxyOrderAndProfitInfoSum(Long orderId, Long userId, Long shopId, Long orderFee, Long profitFee) {
        val rOptNormalProxySum = subProxyDataReadService.findByUserId(userId, shopId);
        if (!rOptNormalProxySum.isSuccess()) {
            log.error("{} orderId:{} userId:{} shopId:{} originFee:{}", LogUtil.getClassMethodName(), orderId, userId, shopId, profitFee);
            return;
        }
        if (rOptNormalProxySum.take().isEmpty()) {
            SubProxySum subProxySum = new SubProxySum();
            subProxySum.setFanNum(1L);
            subProxySum.setIncomeSum(profitFee > 0 ? profitFee : 0);
            subProxySum.setOrderNum(orderFee > 0 ? 1L : 0L);
            subProxySum.setUserId(userId);
            subProxySum.setShopId(shopId);
            subProxySum.setTradeFeeSum(orderFee);
            subProxySum.setSupperProxyId(storeProxyReadService.findByShopIdAndUserId(shopId, userId).orElse(Optional.empty()).map(StoreProxy::getSupperId).orElse(null));
            val rCreate = subProxyDataWriteService.create(subProxySum);
            if (!rCreate.isSuccess()) {
                log.error("{} orderId:{} userId:{} shopId:{} originFee:{}", LogUtil.getClassMethodName("create-profit-transmit"), orderId, userId, shopId, profitFee);
            }
        } else {
            val rUpdate = subProxyDataWriteService.increaseIncome(userId, shopId, profitFee);
            subProxyDataWriteService.increaseTrade(userId, shopId, orderFee);
            subProxyDataWriteService.changeOrderNum(userId, shopId, Long.compare(orderFee, 0));
            if (!rUpdate.isSuccess()) {
                log.error("{} orderId:{} userId:{} shopId:{} originFee:{}", LogUtil.getClassMethodName("update-profit-transmit"), orderId, userId, shopId, profitFee);
            }
        }
    }

    /**
     * 赚取待定收益利润
     *
     * @param shopOrder    订单
     * @param orderOutFrom 订单来源
     * @return 被存储的佣金
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BalanceDetail> earnForeseeProfit(ShopOrder shopOrder, OrderOutFrom orderOutFrom) {
        List<BalanceDetail> profitList = calculateProfit(shopOrder, orderOutFrom, false);
        if (CollectionUtils.isEmpty(profitList)) {
            log.warn("{} empty profit found from shopOrder[{}] outFrom[{}]", LogUtil.getClassMethodName(), shopOrder.getId(), orderOutFrom);
            return profitList;
        }
        if (!profitList.stream().filter(Objects::nonNull).allMatch(this::expectProfitNotExists)) {
            log.warn("{} we expect the order[{}] profit is new,but seems not,check it out", LogUtil.getClassMethodName(), shopOrder.getId());
        }

        //  检测 拒绝利润超过50%
        Long totalProfit = profitList.stream().filter(BalanceDetail::isPersistAble)
                .map(BalanceDetail::getChangeFee).reduce(Long::sum).orElse(0L);

        if (totalProfit * 2 >= shopOrder.getFee()) {
            throw Translate.exceptionOf("违规利润, 请联系客服进行修正, 法律规定反佣不得超过50%%, Profit -> [%s] Order -> [%s]", totalProfit, shopOrder.getFee());
        }

        final Stream<BalanceDetail> realActionStream = profitList.stream().filter(Objects::nonNull).filter(this::expectProfitNotExists);

        //  筛选数据进行存储和记录
        List<BalanceDetail> resultList = realActionStream.peek(profit -> self.changeRealProfit(profit).take()).peek(generateRecordExecutor(shopOrder))
                .peek(balanceDetail -> balanceDetail.setCreatedAt(new Date()))
                .collect(Collectors.toList());
        // 记录订单利润数据
        try {
            orderProfitRecordManager.createProfitRecord(shopOrder);
        } catch (Exception ex) {
            log.error("{} fail to create update profit for order[{}]", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("ProfitRecord", new Translate("订单[%s] 利润记录创建失败[%s]", shopOrder.getId(), JSON.toJSON(resultList.stream().map(EntityBase::getId).toArray())).toString(), ex, EmailReceiverGroup.DEVELOPER));
        }
        EventSender.send("order-cache-handler", Map.of("orderId", shopOrder.getId()), r -> {
        });
        return resultList;
    }

    /**
     * 记录今日佣金变化,该数据不能用于统计,仅仅用于今日
     * 当退款时,如果订单并非今日订单,则不记录,退款是profit的值需要手动置负
     *
     * @param shopOrder    订单
     * @param profit       变化额度
     * @param orderOutFrom 订单类型
     */
    private void recordTodayProfit(ShopOrder shopOrder, Long profit, OrderOutFrom orderOutFrom) {
        if (shopOrder.getCreatedAt().before(DateUtil.withTimeAtStartOfDay(new Date()))) {
            return;
        }
        try {
            Long userId;
            Long shopIdOrWeShopId = shopOrder.getShopId();
            switch (orderOutFrom) {
                case WE_SHOP -> {
                    shopIdOrWeShopId = Long.parseLong(shopOrder.getOutShopId());
                    userId = weShopCacheHolder.findByWeShopId(shopIdOrWeShopId).map(WeShop::getUserId).orElse(null);
                }
                case LEVEL_Distribution -> userId = storeProxyManager.getStoreProxyByShopIdAndUserId(shopIdOrWeShopId, shopOrder.getReferenceId()).map(StoreProxy::getUserId).orElse(null);
                case SUB_STORE -> userId = Optional.ofNullable(shopOrder.getOutShopId())
                        .map(Long::parseLong).map(subStoreReadService::findById).map(Response::getResult)
                        .map(SubStore::getUserId).orElse(null);
                default -> userId = shopCacheHolder.findShopById(shopIdOrWeShopId).getUserId();
            }
            IRecordInsert iRecordInsert = ForeseeProfitSumToday.build(userId, orderOutFrom, shopIdOrWeShopId).num(profit);
            log.debug("{} today foreseeProfitSum orderId => [{}] userId => [{}] key => [{}] profit => [{}]", LogUtil.getClassMethodName(), shopOrder.getId(), userId, iRecordInsert.key(), iRecordInsert.num());
            recordManager.increaseRecord(userId, iRecordInsert);
        } catch (Exception ex) {
            log.error("{} fail to record the profit from order[{}] outFrom [{}]", LogUtil.getClassMethodName(), shopOrder.getId(), orderOutFrom, ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("recordTodayProfit", new Translate("记录订单[%s]来源[%s]利润[%s]失败", shopOrder.getId(), profit, orderOutFrom.Code()).toString(), ex, EmailReceiverGroup.DEVELOPER));
        }
    }

    /**
     * 期望原有的收益不存在
     *
     * @param profitDetail 收益
     * @return 是否不存在
     */
    @Override
    public boolean expectProfitNotExists(BalanceDetail profitDetail) {
        BalanceDetailCriteria existsCriteria = new BalanceDetailCriteria();
        existsCriteria.setSourceId(profitDetail.getSourceId());
        existsCriteria.setUserId(profitDetail.getUserId());
        existsCriteria.setRelatedId(profitDetail.getRelatedId());
        existsCriteria.setType(profitDetail.getType());
        existsCriteria.setStatus(profitDetail.getStatus());

        boolean exists = Optional.ofNullable(balanceDetailReadService.count(existsCriteria).getResult()).orElse(1L) > 0;
        if (exists) {
            existsCriteria.setPageSize(500);
            List<BalanceDetail> matchProfit = Optional.ofNullable(balanceDetailReadService.paging(existsCriteria.toMap()).getResult().getData())
                    .orElseGet(ArrayList::new);
            boolean profitFeeMatch = profitDetail.getChangeFee() != null && matchProfit.stream().map(BalanceDetail::getChangeFee).allMatch(profitDetail.getChangeFee()::equals);
            log.warn("{} count[{}] chang-fee-match[{}]\ntestProfit [{}]\nmatched profit[{}]", LogUtil.getClassMethodName()
                    , matchProfit.size() == 500 ? ">500" : matchProfit.size(), profitFeeMatch, JSON.toJSONString(profitDetail), JSON.toJSONString(matchProfit));
        }
        return !exists;
    }

    /**
     * 持久化已有的与订单相关的利润
     *
     * @param order 主订单信息
     */
    @Override
    public List<BalanceDetail> persistForeseeProfit(ShopOrder order) {

        if (order == null) {
            log.error("{} null order?", LogUtil.getClassMethodName());
            return new ArrayList<>();
        }

        // 是否要等待托管代付完成
        if (waitingForAgentPay(order)) {
            throw new RuntimeException("对应的托管代付尚未完成");
        }
        String lockKey = "PERSIST_FORESEE_PROFIT_WITH_LOCK" + order.getId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                return balanceDetailManager.persistForeseeProfitWithLock(order);
            }
        } catch (Exception e) {
            log.error("计算订单利润佣金失败 失败原因 {}", e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return new ArrayList<>();
    }


    @Override
    public List<BalanceDetail> persistForeseeProfitWithLock(ShopOrder order) {
        OrderOutFrom outFrom;
        try {
            outFrom = OrderOutFrom.fromCode(order.getOutFrom());
        } catch (Exception ex) {
            log.error("{} shopOrderId:{} outFrom:{}", LogUtil.getClassMethodName("parse-outFrom"), order.getId(), order.getOutFrom());
            return new ArrayList<>();
        }
        List<BalanceDetail> profitList = calculateProfit(order, outFrom, true);
        if (CollectionUtils.isEmpty(profitList)) {
            log.warn("{} found no profit for order [{}]", LogUtil.getClassMethodName(), order.getId());
            return new ArrayList<>();
        }
        if (!profitList.stream().filter(Objects::nonNull).allMatch(this::expectProfitNotExists)) {
            log.warn("{} we expect the order[{}] profit is new,but seems not,check it out", LogUtil.getClassMethodName(), order.getId());
        }

        // 筛选数据进行返回
        List<BalanceDetail> balanceDetailList = profitList.stream().filter(this::expectProfitNotExists).peek(profit -> self.changeRealProfit(profit).take())
                .peek(balanceDetail -> balanceDetail.setCreatedAt(new Date()))
                .collect(Collectors.toList());
        try {
            orderProfitRecordManager.ensureOrderProfit(order.getId(), balanceDetailList);
        } catch (Exception ex) {
            log.error("{} fail to record update profit for order[{}]", LogUtil.getClassMethodName(), order.getId(), ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("ProfitRecord", new Translate("订单[%s] 利润记录更新失败[%s]", order.getId(), JSON.toJSON(balanceDetailList.stream().map(EntityBase::getId).toArray())).toString(), ex, EmailReceiverGroup.DEVELOPER));
        }
        orderProfitActionRecordApp.confirmProfitComplete(order.getId());
        return balanceDetailList;
    }

    /**
     * 是否要等待托管代付完成
     *
     * @param shopOrder
     * @return
     */
    private boolean waitingForAgentPay(ShopOrder shopOrder) {
        var payment = paymentReadService.findPayment(shopOrder.getId());
        if (!PaymentChannelEnum.needAgentPay(payment.getChannel())) {
            return false;
        }

        var list = agentPayOrderReadService.findByRelatedOrderId(shopOrder.getId()).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }

        return list.stream().noneMatch(entity -> AgentPayOrderStatusEnum.PAID.getCode().equals(entity.getStatus()));
    }

    /**
     * 持久化已有的利润,该利润来源直接来源于预收益,不会被冻结影响
     *
     * @param order 订单
     * @throws RuntimeException 数据库读取失败时的错误
     */
    private List<BalanceDetail> makeProxyPresentProfit(ShopOrder order) {
        List<BalanceDetail> profitList = new ArrayList<>();
        if (order == null) {
            log.error("{} null order?", LogUtil.getClassMethodName());
            throw new IllegalArgumentException("require.noNull.order");
        }
        if (order.getReferenceId() == null) {
            log.warn("{} orderId[{}] outFrom[{}]", LogUtil.getClassMethodName("no-referer-id"), order.getId(), order.getOutFrom());
            return new ArrayList<>();
        }
        val rProxyEntity = storeProxyReadService.findByShopIdAndUserId(order.getShopId(), order.getRefererId());
        if (!(rProxyEntity.isSuccess() && rProxyEntity.take().isPresent())) {
            if (CollectionUtils.isEmpty(balanceDetailReadService.findByRelatedIdAndSourceId(order.getId(), order.getShopId()).getResult())) {
                log.warn("{} NO PROFIT FOR ORDER[Id => {}, Reference => {}, Shop => {}, OutFrom => {}] because no proxy guide this order"
                        , LogUtil.getClassMethodName(), order.getId(), order.getRefererId(), order.getShopId(), order.getOutFrom());
                return new ArrayList<>();
            }
            log.error("{} orderId:{} shopId:{} referenceId:{} find no **PROXY**", LogUtil.getClassMethodName("find-proxy-fail"), order.getId(), order.getShopId(), order.getReferenceId(), rProxyEntity.getError());
            throw new IllegalStateException(Translate.exceptionOf("查找阶级分销的代理失败, 店铺[Id => %s] 用户[Id => %s]", order.getShopId(), order.getReferenceId()));
        }
        log.info("{} orderId:{} real-reach-proxyId:{}", LogUtil.getClassMethodName("storeProxy-persist-profit"), order.getId(), rProxyEntity.take().get());
        List<StoreProxyWithProfitSource> profitGainProxy = new ArrayList<>();
        pullOutSupper(rProxyEntity.take().get()).stream().map(StoreProxyWithProfitSource::forTransmitProfit).forEach(profitGainProxy::add);
        profitGainProxy.add(StoreProxyWithProfitSource.forReachProfit(rProxyEntity.take().get()));
        // add a baby-linked profit for the reach store proxy
        profitGainProxy.add(StoreProxyWithProfitSource.forExLinkedProfit(rProxyEntity.take().get()));
        val shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(order.getShopId())).map(Shop::getExtra).orElse(new HashMap<>(8));
        boolean openFans = "true".equals(shopExtra.getOrDefault(ShopExtra.openFans.getCode(), "false"))
                || "1".equals(shopExtra.getOrDefault(ShopExtra.openFans.getCode(), "0"));
        //  拉取忠诚佣金
        if (openFans) {
            storeProxyManager.getHonestedProxy(order.getBuyerId(), order.getShopId(), rProxyEntity.take().get())
                    .map(StoreProxyWithProfitSource::forLinkedProfit)
                    .ifPresent(profitGainProxy::add);
        }
        //  查找订单相关的利润收益
        Function<StoreProxyWithProfitSource, InComeDetail> foundOrderRelatedInComeDetail = (proxy) -> {
            val incomeList = balanceDetailReadService.findByRelatedIdAndTypeAndSourceAndStatusMask(
                    order.getId()
                    , proxy.getUserId()
                    , proxy.getShopId()/// sourceId
                    , ProfitType.InCome.getValue()
                    , InComeDetail.orderRelatedMask.ShopOrder.getValue()
                            | BalanceDetail.maskBit.OrderRelated.getValue()      /// 是订单产生的
                            | IsPersistAble.maskBit.PersistAble.getValue()      /// 已经写入到数据库的
                            | proxy.getBalanceSource().getBitMark()             /// 利润类别
            ).getResult();
            if (CollectionUtils.isEmpty(incomeList)) {
                log.error("{} fail make profit userId:{} shopId:{} orderId:{} balanceSource[{}] shopExtra[{}]", LogUtil.getClassMethodName("fail-find-income"), proxy.getUserId(), proxy.getShopId(), order.getId(), proxy.getBalanceSource(), shopExtra);
                return null;
            }
            if (incomeList.size() != 1) {
                log.warn("{} userId:{} shopId:{} orderId:{}", LogUtil.getClassMethodName("income-list-wrong"), proxy.getUserId(), proxy.getShopId(), order.getId());
            }
            return new InComeDetail(incomeList.get(0));
        };

        profitGainProxy.stream().map(foundOrderRelatedInComeDetail)
                .filter(Objects::nonNull)
                .peek(inComeDetail -> inComeDetail.setStatus(inComeDetail.getStatus() | IsPresent.presentMaskBit.Present.getValue()))
                .forEach(profitList::add);
        return profitList;
    }

    /**
     * 查找利润列表
     *
     * @param userId         用户Id
     * @param sourceId       小程序(平台) 辨识实体主键,目前用shopId判定,当公众平台上线后全体使用projectId
     * @param betweenStartAt 从某天开始
     * @param betweenEndAt   到某天接受
     * @param exceptStatus   排除的收益`status`
     * @param includeStatus  必须包裹的收益`status`
     */
    @Override
    public Optional<ProfitDataVO> queryProfitList(long userId, long sourceId, Date betweenStartAt, Date betweenEndAt, List<Integer> exceptStatus, List<Integer> includeStatus) {
        /// ready For IO
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setUserId(userId);
        criteria.setSourceId(sourceId);
        criteria.setCreatedStartAt(betweenStartAt);
        criteria.setCreatedEndAt(betweenEndAt);
        if (!CollectionUtils.isEmpty(exceptStatus)) {
            criteria.setNotStatusBitMarks(exceptStatus);
        }
        if (!CollectionUtils.isEmpty(includeStatus)) {
            criteria.setStatusBitMarks(includeStatus);
        }
        log.debug("{} criteria:{}", LogUtil.getClassMethodName(), criteria);
        val rList = balanceDetailReadService.list(criteria);
        if (!rList.isSuccess()) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName("paging-error"), criteria);
            return Optional.empty();
        }
        log.debug("{} balanceDetailSize:{}", LogUtil.getClassMethodName(), rList.getResult().size());
        /// pack vo
        Optional<ProfitDataVO> packResult = packProfitListIntoProfitDataVO(rList.getResult());
        if (packResult.isEmpty()) {
            log.error("{} userId:{} sourceId:{} ", LogUtil.getClassMethodName("fail-list-detail"), userId, sourceId);
        }
        return packResult;
    }

    /**
     * 打包数据,将收益数据打包为一个vo层数据
     * 订单数量与订单总金额只涉及成功下单后的数据(退款不影响)
     * 而收益利润那边则将会被退款影响
     *
     * @param balanceDetails 收益数据来源
     * @return 成功打包的VO层数据
     */
    private Optional<ProfitDataVO> packProfitListIntoProfitDataVO(List<BalanceDetail> balanceDetails) {

        ProfitDataVO vo = new ProfitDataVO();
        List<InComeDetail> inComeDetails = new ArrayList<>();
        List<OutComeDetail> outComeDetails = new ArrayList<>();
        balanceDetails.forEach(detail -> {
            if (detail.getType().equals(ProfitType.InCome.getValue())) {
                inComeDetails.add(new InComeDetail(detail));
            }
            if (detail.getType().equals(ProfitType.OutCome.getValue())) {
                outComeDetails.add(new OutComeDetail(detail));
            }
        });
        HashSet<Long> orderIdSet = new HashSet<>();
        long validProfit = 0;
        long forecastProfit = 0;
        long orderNum;
        long feeSum = 0;
        /// sum balanceDetail
        for (InComeDetail detail : inComeDetails) {
            if (detail.isPersistAble()) {
                if (detail.isPresent()) {
                    validProfit += detail.getChangeFee();
                } else {
                    forecastProfit += detail.getChangeFee();
                }
                if (detail.isOrderRelated()) {
                    if (OutComeDetail.orderRelatedMask.ShopOrder.getValue() == (detail.getStatus() & OutComeDetail.orderRelatedMask.ShopOrder.getValue())) {
                        orderIdSet.add(detail.getRelatedId());
                    }
                }
            }
        }
        for (OutComeDetail detail : outComeDetails) {
            if (detail.isPersistAble()) {
                if (detail.isPresent()) {
                    validProfit -= detail.getChangeFee();
                } else {
                    forecastProfit -= detail.getChangeFee();
                }
            }
        }
        log.debug("{} shopOrderId:{}", LogUtil.getClassMethodName(), orderIdSet);
        /// sum orderDetail
        Response<List<ShopOrder>> rShopOrders = shopOrderReadService.findByIds(new ArrayList<>(orderIdSet));
        if (!rShopOrders.isSuccess()) {
            log.error("{} validProfit:{} forecastProfit:{} shopOrderIds:{}", LogUtil.getClassMethodName("fail-list-detail"), validProfit, forecastProfit, orderIdSet);
            return Optional.empty();
        }
        orderNum = rShopOrders.getResult().size();
        for (ShopOrder order : rShopOrders.getResult()) {
            feeSum += order.getFee();
        }
        if (forecastProfit < 0) {
            forecastProfit = 0;
        }
        vo.setPaymentSum(feeSum);
        vo.setCount(orderNum);
        vo.setSettledProfit(validProfit);
        vo.setUnsettledProfit(forecastProfit);
        return Optional.of(vo);
    }

    /**
     * 对利润标记可忽略(用于显示层的忽略)
     *
     * @param inComeDetail 需要被打上可忽略的标记
     */
    @Override
    public void markWithIgnoreAble(InComeDetail inComeDetail) {
        BalanceDetail update = new BalanceDetail();
        if (inComeDetail.getId() == null || inComeDetail.getStatus() == null) {
            log.error("{} cant mark null detail:{}", LogUtil.getClassMethodName(), JSON.toJSONString(inComeDetail));
            return;
        }
        update.setId(inComeDetail.getId());
        update.setStatus(inComeDetail.getStatus() | IgnoreAble.IgnoreAbleDefaultMaskbit.IgnoreAble.getBit());
        Response<Boolean> rUpdate = balanceDetailWriteService.update(update);
        if (!rUpdate.isSuccess() || !rUpdate.getResult()) {
            log.error("{} mark ignoreAble failed with balanceDetail:{}", LogUtil.getClassMethodName(), JSON.toJSONString(inComeDetail));
        }
    }

    @Override
    public Either<BalanceDetail> getEarned(Long userId, Long shopId) {
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(shopId);
        criteria.setUserId(userId);
        criteria.setType(ProfitType.Earned.getValue());
        try {
            Optional<BalanceDetail> optEarned = balanceDetailReadService.paging(criteria.toMap()).getResult().getData().stream().findFirst();
            if (optEarned.isPresent()) {
                return Either.ok(optEarned.get());
            }
            BalanceDetail earned = new BalanceDetail();
            earned.setSourceId(shopId);
            earned.setUserId(userId);
            earned.setType(ProfitType.Earned.getValue());
            earned.setFee(0L);
            earned.setChangeFee(0L);
            earned.setStatus(IsPresent.presentMaskBit.Present.getValue());
            balanceDetailWriteService.create(earned);
            return Either.ok(earned);
        } catch (Exception ex) {
            log.error("{} get earned failed:{}", LogUtil.getClassMethodName(), ex.getMessage());
            return Either.error(ex);
        }
    }
}
