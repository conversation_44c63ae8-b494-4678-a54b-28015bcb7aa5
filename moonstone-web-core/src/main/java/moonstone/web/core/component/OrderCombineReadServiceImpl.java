package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.item.dto.OrderCombine;
import moonstone.item.service.OrderCombineReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class OrderCombineReadServiceImpl implements OrderCombineReadService {
    @Autowired
    private MongoTemplate mongoTemplate;

    List<OrderCombine> orderCombineList = new ArrayList<>();

    @PostConstruct
    public void init() {
        orderCombineList.add(new OrderCombine(934L, 829L, 10, 882L, 1));
        orderCombineList.add(new OrderCombine(931L, 784L, 10, 882L, 1));
        orderCombineList.add(new OrderCombine(927L, 783L, 10, 882L, 1));
        orderCombineList.add(new OrderCombine(800L, 695L, 10, 882L, 1));
    }

    @Override
    public Either<List<OrderCombine>> findByShopId(Long shopId) {
        try {
            List<OrderCombine> orderCombineHolderList = mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId)), OrderCombine.class);
            if (CollectionUtils.isEmpty(orderCombineHolderList))
                return Either.ok(orderCombineList);
            return Either.ok(orderCombineHolderList);
        } catch (Exception ex) {
            log.error("{} fail to query by shopId[{}] for orderCombine", LogUtil.getClassMethodName(), shopId, ex);
            return Either.error(ex);
        }
    }
}
