package moonstone.web.core.component.pay.xinbada.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class SignResult {
    @JsonProperty("contract_uuid")
    String contractUuid;
    @JsonProperty("signing_status")
    Integer signingStatus;

    public boolean isSign() {
        return Objects.equals(signingStatus, 1);
    }
}
