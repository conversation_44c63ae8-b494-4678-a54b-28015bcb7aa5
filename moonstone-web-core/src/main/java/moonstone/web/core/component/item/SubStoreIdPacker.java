package moonstone.web.core.component.item;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.api.IndexedItemProcessor;
import moonstone.search.dto.IndexedItem;
import moonstone.web.core.component.ProxyItemManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SubStoreIdPacker implements IndexedItemProcessor {
    @Autowired
    private ProxyItemManager proxyItemManager;

    @Override
    public IndexedItem process(IndexedItem indexedItem) {
        List<Long> proxyIdList = proxyItemManager.findProxyIdFromItemId(indexedItem.getId());
        indexedItem.setSubStoreIds(proxyIdList);
        return indexedItem;
    }
}
