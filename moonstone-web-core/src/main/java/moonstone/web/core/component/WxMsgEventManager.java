package moonstone.web.core.component;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.WechatOrderMsgEvent;
import moonstone.event.WechatPrepayEvent;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.web.core.events.msg.WxMsgPreSendMsg;
import moonstone.web.core.events.msg.WxMsgSendMsg;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;

@Service
@Slf4j
@RpcProvider
@Deprecated
public class WxMsgEventManager {
    @Autowired
    private MongoTemplate mongoTemplate;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WxMsgFormId {
        Long userId;
        String appId;
        String formId;
        Long relationId;
        List<Long> relatedIds;
        String type;
        Date lastSend;
        Boolean lastSendSuccess;
        Long sendCount = 0L;
        Long failedCount = 0L;

        public WxMsgFormId(Long userId, String appId, String formId, Long relationId, List<Long> relatedIds, String type) {
            this.userId = userId;
            this.appId = appId;
            this.formId = formId;
            this.relationId = relationId;
            this.relatedIds = relatedIds;
            this.type = type;
        }
    }


    @EventListener
    public void listenOn(WechatOrderMsgEvent event) {
        Long relationId = event.getRelatedIds().stream().reduce(Long::min).orElse(event.getRelatedIds().get(0));
        saveFormId(event.getUserId(), event.getAppId(), event.getFormId(), relationId, event.getRelatedIds(), ShopOrder.class);
    }

    @EventListener
    public void listenOn(WechatPrepayEvent event) {
        saveFormId(event.getUserId(), event.getAppId(), event.getPrepayId(), event.getPaymentId(), Collections.singletonList(event.getPaymentId()), Payment.class);
    }

    /**
     * 保存小程序返回的formId以提供进一步的服务
     *
     * @param userId 系统内的用户id
     * @param appId  小程序id
     * @param formId formId
     */
    public Either<Boolean> saveFormId(Long userId, String appId, String formId, Long relationId, List<Long> relatedIds, Class<?> type) {
        try {
            log.debug("{} userId:{} appId:{} formId:{} relationId:{}", LogUtil.getClassMethodName(), userId, appId, formId, relationId);
            Query query = Query.query(Criteria.where("userId").is(userId)
                    .andOperator(Criteria.where("appId").is(appId)
                            .andOperator(Criteria.where("type").is(type.getSimpleName())
                                    .andOperator(Criteria.where("relationId").is(relationId)))));
            long count = mongoTemplate.count(query, WxMsgFormId.class.getSimpleName());
            if (count > 0) {
                mongoTemplate.updateFirst(query, Update.update("formId", formId), WxMsgFormId.class.getSimpleName());
            } else {
                mongoTemplate.insert(new WxMsgFormId(userId, appId, formId, relationId, relatedIds, type.getSimpleName()), WxMsgFormId.class.getSimpleName());
            }
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} userId:{} appId:{} formId:{} relationId:{} type:{}", LogUtil.getClassMethodName(), userId, appId, formId, relationId, type, ex);
            return Either.error(ex);
        }
    }

    public Either<List<WxMsgFormId>> getList(Long userId, Long relationId, Class<?> type) {
        try {
            Query query = Query.query(Criteria.where("userId").is(userId)
                    .andOperator(Criteria.where("type").is(type.getSimpleName())
                            .andOperator(Criteria.where("relationId").is(relationId))));
            return Either.ok(mongoTemplate.find(query, WxMsgFormId.class, WxMsgFormId.class.getSimpleName()));
        } catch (Exception ex) {
            log.error("{} userId:{} relationId:{} type:{} method:{}", LogUtil.getClassMethodName(), userId, relationId, type, ex);
            return Either.error(ex);
        }
    }

    public Either<Optional<WxMsgFormId>> getOne(Long userId, String appId, Long relationId, Class<?> type) {
        try {
            Query query = Query.query(Criteria.where("userId").is(userId)
                    .andOperator(Criteria.where("appId").is(appId)
                            .andOperator(Criteria.where("type").is(type.getSimpleName())
                                    .andOperator(Criteria.where("relationId").is(relationId)))));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, WxMsgFormId.class, WxMsgFormId.class.getSimpleName())));
        } catch (Exception ex) {
            log.error("{} userId:{} appId:{} relationId:{} type:{}", LogUtil.getClassMethodName(), userId, appId, relationId, type, ex);
            return Either.error(ex);
        }
    }

    /**
     * 根据预发送短信进行组装信息并且发送
     * 首先自动根据preSendMsg与系统内存储的formId与appId等信息自动组建信息
     * 由于微信模板具有其不可定性，因此使用constructor进行对其自定义化装配详细显示内容
     *
     * @param wxMsgPreSendMsg 微信预发送信息
     * @param constructor     装饰器
     * @return 是否发送成功
     */
    public Either<Boolean> constructAndSend(WxMsgPreSendMsg wxMsgPreSendMsg, Consumer<WxMsgSendMsg> constructor) {
        return construct(wxMsgPreSendMsg, constructor).map(Objects::nonNull);
    }

    /**
     * 根据预发送短信进行组装信息
     *
     * @param wxMsgPreSendMsg 微信预发送信息
     * @param constructor     装饰器
     * @return 组装完毕的微信可发送信息模板
     */
    Either<List<WxMsgSendMsg>> construct(WxMsgPreSendMsg wxMsgPreSendMsg, Consumer<WxMsgSendMsg> constructor) {
        List<WxMsgFormId> wxMsgFormIds = getList(wxMsgPreSendMsg.getUserId(), wxMsgPreSendMsg.getRelationId(), wxMsgPreSendMsg.getRelationType()).take();
        log.debug("{} formIds:{}", LogUtil.getClassMethodName(), Arrays.toString(wxMsgFormIds.toArray()));
        List<WxMsgSendMsg> wxMsgSendMsgList = new ArrayList<>();
        try {
            for (WxMsgFormId wxMsgFormId : wxMsgFormIds) {
                WxMsgSendMsg wxMsgSendMsg = new WxMsgSendMsg();
                wxMsgSendMsg.setSource(wxMsgFormId);
                wxMsgSendMsg.setData(new HashMap<>());
                BeanUtils.copyProperties(wxMsgPreSendMsg, wxMsgSendMsg);
                BeanUtils.copyProperties(wxMsgFormId, wxMsgSendMsg);
                constructor.accept(wxMsgSendMsg);
                log.debug("{} wxMsgSendMsg:{}", LogUtil.getClassMethodName(), wxMsgSendMsg);
                wxMsgSendMsgList.add(wxMsgSendMsg);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} pre-send-msg:{}", LogUtil.getClassMethodName(), wxMsgPreSendMsg, ex);
            return Either.error(ex, Translate.of("组装微信信息失败"));
        }
        return Either.ok(wxMsgSendMsgList);
    }

    public Either<Boolean> setSendResult(WxMsgSendMsg msgSendMsg, boolean success) {
        Query query = new Query(Criteria.where("appId").is(msgSendMsg.getSource().getAppId())
                .andOperator(Criteria.where("userId").is(msgSendMsg.getSource().getUserId())
                        .andOperator(Criteria.where("relationId").is(msgSendMsg.getSource().getRelationId())
                                .andOperator(Criteria.where("type").is(msgSendMsg.getSource().getType()))
                        )));
        Update update = Update.update("lastSendSuccess", success).currentDate("lastSend").inc("sendCount", 1).inc("failedCount", success ? 0 : 1);
        try {
            return Either.ok(mongoTemplate.updateFirst(query, update, WxMsgFormId.class.getSimpleName()).getModifiedCount() > 0);
        } catch (Exception ex) {
            log.error("{} fail to set SendResult", LogUtil.getClassMethodName(), ex);
            return Either.error(ex);
        }
    }
}
