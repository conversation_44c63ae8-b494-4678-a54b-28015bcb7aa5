package moonstone.web.core.component;

import moonstone.cache.WeShopItemRecordCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.web.core.model.Record;
import moonstone.web.core.model.dto.record.ShareTimeToday;
import moonstone.web.core.model.enu.RecordDateLimitType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class WeShopItemRecordCacheHolderImpl implements WeShopItemRecordCacheHolder {
    @Autowired
    private RecordManager recordManager;

    @Override
    public Optional<Long> getSharedTimeFromItemAndWeShopId(Long itemId, Long weShopId) {
        return recordManager.findRecord(null, ShareTimeToday.build(weShopId, OrderOutFrom.WE_SHOP, itemId), RecordDateLimitType.day)
                .stream().findFirst().map(Record::getNum);
    }
}
