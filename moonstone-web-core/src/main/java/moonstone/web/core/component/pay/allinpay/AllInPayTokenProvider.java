package moonstone.web.core.component.pay.allinpay;

import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.exception.PayException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class AllInPayTokenProvider implements TokenProvider<AllInPayToken> {

    private final Map<String, AllInPayToken> tokenMap = new ConcurrentHashMap<>();

    @Override
    public AllInPayToken findToken(String accountNo) throws PayException {
        return tokenMap.get(accountNo);
    }

    @Override
    public List<String> listAllAccounts() {
        return new ArrayList<>(tokenMap.keySet());
    }

    @Override
    public void register(String accountNo, AllInPayToken token) throws PayException {
        tokenMap.put(accountNo, token);
    }
}
