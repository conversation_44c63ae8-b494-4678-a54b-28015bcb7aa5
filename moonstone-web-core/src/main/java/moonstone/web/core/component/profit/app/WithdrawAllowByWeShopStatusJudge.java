package moonstone.web.core.component.profit.app;

import lombok.AllArgsConstructor;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.model.SimpleRulerJudgeBean;
import moonstone.common.utils.Translate;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.weShop.enums.WeShopStatus;
import moonstone.weShop.model.WeShop;
import org.springframework.stereotype.Component;

import java.util.Map;

@AllArgsConstructor
@Component
public class WithdrawAllowByWeShopStatusJudge implements SimpleRulerJudgeBean<WithDrawProfitApply> {
    ShopCacheHolder shopCacheHolder;
    WeShopCacheHolder weShopCacheHolder;

    @Override
    public boolean allow(WithDrawProfitApply aimTarget) {
        Long shopId = aimTarget.getSourceId();
        if (shopId == 0) {
            return true;
        }
        // check the sale pattern
        Map<String, String> extra = shopCacheHolder.findShopById(shopId).getExtra();
        SalePattern pattern = SalePattern.from(extra.getOrDefault(ShopExtra.SalesPattern.getCode(), SalePattern.Common.getCode()));
        if (pattern != SalePattern.WeShop) {
            return true;
        }
        // check if weShop status is correct
        WeShop weShop = weShopCacheHolder.findByUserIdAndShopId(aimTarget.getUserId(), shopId).orElseThrow(() -> Translate.exceptionOf("店铺[用户Id -> %s 平台Id -> %s]不存在", aimTarget.getUserId(), shopId));
        if (weShop.getStatus() != WeShopStatus.NORMAL.getValue() && weShop.getStatus() != WeShopStatus.DIS_COOP.getValue()) {
            throw Translate.exceptionOf("微店未激活, 或者已彻底关闭");
        }
        return true;
    }
}