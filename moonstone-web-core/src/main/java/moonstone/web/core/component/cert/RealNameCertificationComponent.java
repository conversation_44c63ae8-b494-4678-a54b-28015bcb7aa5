package moonstone.web.core.component.cert;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 实名认证
 */
@Slf4j
@Component
public class RealNameCertificationComponent {

    @Resource
    private JedisPool jedisPool;

    @Value("${aliyun.realNameCertification.appKey}")
    private String appKey;

    @Value("${aliyun.realNameCertification.appSecret}")
    private String appSecret;

    private static final String MAC_ALGORITHM = "HmacSHA256";

    /**
     * 阿里云实名认证服务接口地址
     */
    private static final String URL = "https://eid.shumaidata.com/eid/check";

    private static final String KEY_PREFIX = "parana:userCertification:realNameCertification:";

    /**
     * 实名认证校验
     *
     * @param name     姓名
     * @param idNumber 身份证号
     * @return true - 校验通过，false - 校验不通过
     */
    public boolean realNameCertificationCheck(String name, String idNumber) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(idNumber)) {
            return false;
        }

        //查询缓存
        var result = findInCache(name, idNumber);
        if (result != null) {
            return result;
        }

        //调用aliyun接口
        result = checkByAliYun(name, idNumber);

        //记录缓存
        addToCache(name, idNumber, result);

        //返回
        return result;
    }

    /**
     * 调用 aliyun http接口进行验证
     *
     * @param name
     * @param idNumber
     * @return
     * @throws Exception
     */
    private Boolean checkByAliYun(String name, String idNumber) {
        OkHttpClient client = new OkHttpClient.Builder().build();

        FormBody.Builder formBuilder = new FormBody.Builder();
        formBuilder.add("name", name);
        formBuilder.add("idcard", idNumber);
        FormBody body = formBuilder.build();

        try {
            Request request = new Request.Builder()
                    .url(URL)
                    .addHeader("x-ca-key", appKey)
                    .addHeader("x-ca-signature-method", MAC_ALGORITHM)
                    .addHeader("x-ca-signature-headers", "x-ca-key,x-ca-signature-method")
                    .addHeader("x-ca-signature", buildSignString(name, idNumber))
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            log.info("name={}, idNumber={}, 返回状态码={}, message={}", name, idNumber, response.code(), response.message());

            return convert(response.body().string());
        } catch (Exception ex) {
            log.error("RealNameCertificationComponent.checkByAliYun error, name={}, idNumber={} ", name, idNumber, ex);
            return false;
        }
    }

    /**
     * 构造签名字符串
     *
     * @param name
     * @param idNumber
     * @return
     * @throws Exception
     */
    private String buildSignString(String name, String idNumber) throws NoSuchAlgorithmException, InvalidKeyException {
        String stringToSign = "POST" + "\n" +
                "\n" +
                "\n" +
                "application/x-www-form-urlencoded" + "\n" +
                "\n" +
                "x-ca-key:" + appKey + "\n" +
                "x-ca-signature-method:" + MAC_ALGORITHM + "\n" +
                "/eid/check?idcard=" + idNumber + "&name=" + name;

        byte[] keyBytes = appSecret.getBytes(StandardCharsets.UTF_8);

        Mac mac = Mac.getInstance(MAC_ALGORITHM);
        mac.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, MAC_ALGORITHM));
        byte[] md5Result = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));

        return Base64.encodeBase64String(md5Result);
    }

    /**
     * 在缓存中查询历史验证结果
     *
     * @param name
     * @param idNumber
     * @return
     */
    private Boolean findInCache(String name, String idNumber) {
        try (Jedis jedis = jedisPool.getResource()) {
            var result = jedis.get(buildKey(name, idNumber));
            if (result == null) {
                return null;
            }

            return Boolean.TRUE.toString().equals(result);
        } catch (Exception ex) {
            log.error("RealNameCertificationComponent.findByRedis error: ", ex);
            return null;
        }
    }

    /**
     * 验证结果 记录到缓存中
     *
     * @param name
     * @param idNumber
     * @param isValid
     */
    private void addToCache(String name, String idNumber, boolean isValid) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.set(buildKey(name, idNumber),
                    Boolean.toString(isValid),
                    "NX",
                    "EX",
                    isValid ? 60 * 60 * 24 * 30L : 60 * 5L);

        } catch (Exception ex) {
            log.error("RealNameCertificationComponent.findByRedis error: ", ex);
        }
    }

    private String buildKey(String name, String idNumber) {
        return KEY_PREFIX + name + "_" + idNumber;
    }

    /**
     * 解析请求返回体，获得认证结果
     *
     * @param bodyString
     * @return
     */
    private Boolean convert(String bodyString) {
        if (StringUtils.isBlank(bodyString)) {
            throw new RuntimeException("请求结果为空");
        }

        log.info("RealNameCertificationComponent.convert, AliYun实名信息验证接口返回body={}", bodyString);
        var root = JSON.parseObject(bodyString);
        var code = root.getString("code");
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("请求结果的 code 字段为空");
        }
        if (!"0".equals(code)) {
            return false;
        }

        var result = root.getJSONObject("result");
        if (result == null) {
            throw new RuntimeException("请求结果的 result 字段为空");
        }

        var res = result.getString("res");
        if (StringUtils.isBlank(res)) {
            throw new RuntimeException("请求结果的 result.res 字段为空");
        }

        return "1".equals(res);
    }
}
