package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.LogUtil;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileReadService;
import moonstone.web.core.component.profit.dto.OrderProfitRecord;
import moonstone.web.core.component.profit.dto.OrderProfitViewCriteria;
import moonstone.web.core.component.profit.view.AppOrderProfitView;
import moonstone.web.core.component.profit.view.AppOrderProfitViewImpl;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class AppOrderProfitReader {
    private final OrderProfitRecordManager orderProfitRecordManager;
    private final UserProfileReadService userProfileReadService;

    /**
     * 查询利润订单列表
     *
     * @param criteria 查询条件
     * @return 列表
     */
    public APIResp<List<AppOrderProfitView>> paging(OrderProfitViewCriteria criteria) {
        try {
            Paging<OrderProfitRecord> recordPaging = orderProfitRecordManager.findProfit(criteria);
            List<AppOrderProfitView> viewList = recordPaging.getData().stream()
                    .map(record -> AppOrderProfitViewImpl.from(record, userId -> Optional.ofNullable(userProfileReadService.findById(userId).getResult()).map(UserProfile::getAvatar_).orElse(null)))
                    .collect(Collectors.toList());

            return APIRespWrapper.wrapPaging(Response.ok(new Paging<>(recordPaging.getTotal(), viewList)), criteria.getPageNo(), criteria.getPageSize());
        } catch (Exception ex) {
            log.error("{} fail to paging profitView [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(criteria), ex);
            return APIResp.error(ex.getMessage());
        }
    }
}
