package moonstone.web.core.component.profit;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.component.item.component.TaxChecker;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.web.core.model.dto.tax.PriceAndTax;
import moonstone.web.core.model.dto.tax.ProfitAndTax;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeShopSkuTaxCalculatorBase {
    @Autowired
    SkuReadService skuReadService;
    @Autowired
    DoubleTaxCalculator doubleTaxCalculator;

    @Autowired
    TaxChecker taxChecker;
    @Autowired
    SkuCustomReadService skuCustomReadService;

    /**
     * 计算WeShop产生的利润
     *
     * @param shopId      店铺Id 将与System结合计算税费
     * @param system      外部系统 决定拆单的方向
     * @param outerSkuId  外部单品Code
     * @param finalPrice  最终出售的价格
     * @param originPrice 原价
     * @param initTax     税费,如果不为null和0 就会跳过计算税费逻辑,直接使用它
     * @return 利润
     */
    public ProfitAndTax profitCalculate(Long skuId,
                                        Long shopId,
                                        ThirdPartySystem system,
                                        String outerSkuId,
                                        Long finalPrice,
                                        Long originPrice,
                                        Long initTax,
                                        boolean sellerBearTax,
                                        boolean platformBearTax
    ) {
        StopWatch stopWatch = new StopWatch(getClass().getSimpleName());
        stopWatch.start("profitCalculate");
        Sku sku = matchSku(skuId, shopId, outerSkuId, originPrice, system);
        if (platformBearTax) {
            Long originTax = calculateTaxIgnoreTaxHolder(sku, originPrice.intValue());
            Long plusTax = calculateTaxIgnoreTaxHolder(sku, finalPrice.intValue());
            Long sellerBearPart = plusTax - originTax;
            return new ProfitAndTax(finalPrice - originPrice - sellerBearPart, sellerBearPart);
        }
        if (sellerBearTax) {
            if (Objects.isNull(outerSkuId)) {
                log.warn("{} shopId[{}] system[{}] outerSkuCode[{}] finalPrice[{}] set tax[{}], so bear it all"
                        , LogUtil.getClassMethodName(), shopId, system, "null", finalPrice, initTax);
                return new ProfitAndTax(finalPrice - originPrice - Optional.ofNullable(initTax).orElse(0L), Optional.ofNullable(initTax).orElse(0L));
            }
            if (initTax == null || initTax == 0) {
                try {
                    List<PriceAndTax> priceAndTaxList = system == ThirdPartySystem.GongXiao ?
                            Stream.of(finalPrice, originPrice).map(calculateTaxForGongXiao(sku)).collect(Collectors.toList())
                            : doubleTaxCalculator.splitDoubleTax(skuId, shopId, system.Id(), outerSkuId, Arrays.asList(finalPrice, originPrice))
                            .orElseGet(ArrayList::new);
                    Long tax = priceAndTaxList.get(0).getTax().multiply(new BigDecimal("100")).longValue();
                    return new ProfitAndTax(finalPrice - originPrice - tax
                            , tax);
                } catch (Exception ex) {
                    log.error("{} fail to profit cal for system[{}] shopId[{}] outerSkuId[{}] finalPrice[{}]", LogUtil.getClassMethodName(), system, shopId, outerSkuId, finalPrice, ex);
                    throw new RuntimeException(new Translate("计算利润失败,单品来源店铺[%s] 外部单品Code[%s] 系统[%s] 最终价格[%s] 原价[%s]", shopId, outerSkuId, system, finalPrice, originPrice).toString(), ex);
                } finally {
                    stopWatch.stop();
                    log.debug("{} cost time [{}]", LogUtil.getClassMethodName(), stopWatch);
                }
            }
        }
        try {
            sku.setPrice(finalPrice.intValue());
            Supplier<Long> finalTaxSupplier = () -> taxChecker.getTax(sku, 1);
            Long finalTax = Optional.ofNullable(initTax).filter(tax -> tax > 0).orElseGet(finalTaxSupplier);
            return new ProfitAndTax(finalPrice - originPrice, finalTax);
        } catch (Exception ex) {
            log.error("{} fail to split for sku[{}] at price[{}] with decidePrice[{}] seller-bear [{}] shop[{}] tax", LogUtil.getClassMethodName(), outerSkuId, originPrice, finalPrice, sellerBearTax, shopId, ex);
            throw new RuntimeException(Translate.of("拆税失败"));
        } finally {
            stopWatch.stop();
            log.debug("{} cost time [{}]", LogUtil.getClassMethodName(), stopWatch);
        }
    }

    private Function<Long, PriceAndTax> calculateTaxForGongXiao(Sku sku) {
        return price -> {
            PriceAndTax priceAndTax = new PriceAndTax();
            BigDecimal tax = new BigDecimal(taxChecker.getTax(sku, 1)).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
            taxChecker.splitTaxAtSelf(tax::multiply, new BigDecimal(price))
                    .ifPresent(result -> {
                        priceAndTax.setPrice(result.getFee());
                        priceAndTax.setTax(result.getTax());
                    });
            return priceAndTax;
        };
    }

    /**
     * 无视系统内的设置 强制计算税金
     *
     * @param sku   单品
     * @param price 价格
     * @return 税金
     */
    private Long calculateTaxIgnoreTaxHolder(Sku sku, int price) {
        Sku copy = new Sku();
        BeanUtils.copyProperties(sku, copy);
        copy.setPrice(price);
        SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
        // ignore the tax holder
        skuCustom.setCustomTaxHolder(1);
        Double taxRate = taxChecker.getRate(Integer.parseInt(sku.getTags().get(SkuTagIndex.pushSystem.name()).split(",")[0]), copy, skuCustom);
        return BigDecimal.valueOf(taxRate).multiply(BigDecimal.valueOf(price)).longValue();
    }

    private Sku matchSku(Long skuId, Long shopId, String outerSkuId, Long originPrice, ThirdPartySystem system) {
        Sku originSku;
        if (Objects.nonNull(skuId)) {
            originSku = skuReadService.findSkuById(skuId).getResult();
        } else {
            originSku = skuReadService.findSkuByOuterSkuId(shopId, outerSkuId).getResult().stream()
                    // 查找出正确匹配的商品
                    // 推送系统匹配
                    .filter(skuWithTag -> Optional.ofNullable(skuWithTag.getTags()).map(tags -> tags
                            .get(SkuTagIndex.pushSystem.name())).map(str -> str.split(",")[0]).map(Integer::parseInt).map(ThirdPartySystem::fromInt)
                            .filter(system::equals).isPresent())
                    // 状态匹配
                    .filter(skuWithStatus -> skuWithStatus.getStatus() > 0)
                    .filter(skuWithPrice -> Objects.equals(skuWithPrice.getPrice(), originPrice.intValue()))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException(Translate.of("商品不存在")));
        }
        Sku matchOne = new Sku();
        BeanUtils.copyProperties(originSku, matchOne);
        return matchOne;
    }
}
