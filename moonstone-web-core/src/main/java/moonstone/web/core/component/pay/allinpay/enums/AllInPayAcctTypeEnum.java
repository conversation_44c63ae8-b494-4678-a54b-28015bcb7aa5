package moonstone.web.core.component.pay.allinpay.enums;

import moonstone.common.enums.AppTypeEnum;

/**
 *
 * weChatPublic-微信公众号
 * weChatMiniProgram -微信小程序
 * aliPayService -openjdk-17-agent-1.38.1-v1.2生活号
 * unionPayjs -银联JS
 * 操作类型是“set”必须上送
 *
 */
public enum AllInPayAcctTypeEnum {
    weChatPublic("weChatPublic", "微信公众号"),
    weChatMiniProgram("weChatMiniProgram", "微信小程序"),
    aliPayService("aliPayService", "支付宝生活号"),
    unionPayjs("unionPayjs", "银联JS"),
    ;

    private final String code;
    private final String description;

    AllInPayAcctTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static AllInPayAcctTypeEnum get(AppTypeEnum appType) {
        switch (appType){
            case WECHAT:
                return AllInPayAcctTypeEnum.weChatMiniProgram;
            case ALIPAY:
                return AllInPayAcctTypeEnum.aliPayService;
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
