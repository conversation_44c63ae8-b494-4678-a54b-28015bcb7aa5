package moonstone.web.core.component.pay;

import io.terminus.pay.constants.Channels;

/**
 * 扩展一下原有的支付渠道
 *
 * <AUTHOR>
 */
public class PayChannelsConstants extends Channels {
    /**
     * 云支付
     */
    public final static String YUNACCOUNT = "yunaccount";
    /**
     * 积分支付
     */
    public final static String INTEGRAL_PAY = "Integral-pay";
    /**
     * 第三方提现
     */
    public final static String XIN_BA_DA_PAY = "xin_ba_da";
    /**
     * 易生支付
     */
    public final static String EASY_PAY = "hbecard";
    /**
     * 汇付支付
     */
    public final static String HUIFU_PAY = "huifu";

    public static class YunAccount {
        /**
         * 云支付银行卡提现
         */
        public final static String BANK = YUNACCOUNT + "-bank";
        /**
         * 云支付微信提现
         */
        public final static String WECHAT = YUNACCOUNT + "-wechat";
        /**
         * 云支付支付宝
         */
        public final static String ALIPAY = YUNACCOUNT + "-alipay";
    }
}
