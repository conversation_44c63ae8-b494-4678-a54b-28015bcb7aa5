package moonstone.web.core.component;

import io.vertx.core.Vertx;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.exports.common.Exporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Slf4j
@Component
public class ExporterHelper {
    @Autowired
    Vertx vertx;
    @Autowired
    Exporter exporter;

    public static final String ADDRESS = "exporter#export";

    @PostConstruct
    public void register() {
        vertx.eventBus().<JsonObject>consumer(ADDRESS).handler(this::export);
    }

    private void export(Message<JsonObject> exportRequest) {
        JsonObject request = exportRequest.body();
        String className = request.getString("class");
        List<?> data = request.getJsonArray("data").getList();
        vertx.createSharedWorkerExecutor("EXPORTER").executeBlocking(
                p -> {
                    p.complete(exporter.exportWithTimeOut(className, data, 29000));
                }
        ).onSuccess(exportRequest::reply)
                .onFailure(e -> exportRequest.fail(-1, e.getMessage()))
                .onFailure(e -> log.error("Fail to Export [{}] Size {} to {}", className, data.size(), exportRequest.replyAddress(), e));
    }

}
