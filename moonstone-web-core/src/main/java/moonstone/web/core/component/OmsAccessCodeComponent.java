package moonstone.web.core.component;

import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OmsAccessCodeComponent {
    @Autowired
    private OmsApiGate omsApiGate;
    @Value("${Y800.partnerCode}")
    private String appCode;

    /**
     * query oms accessCode from OmsUserId
     *
     * @param omsUserId omsUserId
     * @return maybe a list or singleton
     */
    public Either<List<String>> getAccessCode(Long omsUserId) {
        String url = omsApiGate.getApiGate() + OmsApiGate.Api.getAccessCode.getUrl() + "?userId=" + omsUserId + "&appId=" + appCode;
        try {
            HttpRequest accessCode = HttpRequest.get(url);
            if (accessCode.ok()) {
                JSONObject result = JSONObject.parseObject(accessCode.body());
                JSONObject data = result.getJSONObject(ResultIndex.result.name());
                return Either.ok(data.getJSONArray(ResultIndex.codes.name()).toJavaList(String.class));
            }
            return Either.error(Translate.of("HTTP fail [code => %s, message => {}]", accessCode.code(), accessCode.message()));
        } catch (Exception exception) {
            log.error("{} fail to query AccessCode for [{}] url => {}", LogUtil.getClassMethodName(),
                    omsUserId, url, exception);
            return Either.error(exception);
        }
    }

    /**
     * query userId in oms by accessCode
     * limit by appId (may cause bug, because the appId is define on oms)
     *
     * @param accessCode accessCode
     * @return userId in oms
     * @see ThirdPartyUserShop#getThirdPartyCode() accessCode
     */
    public Either<Long> queryOuterUserIdByAccessCode(String accessCode) {
        try {
            HttpRequest userIdQuery = HttpRequest.get(omsApiGate.getApiGate() + OmsApiGate.Api.queryByAccessCode.getUrl() + "?accessCode=" + accessCode);
            if (!userIdQuery.ok()) {
                throw new RuntimeException(Translate.of("HTTP请求失败"));
            }
            JSONObject user = JSONObject.parseObject(userIdQuery.body()).getJSONObject(OmsApiGate.ResultIndex.result.name());
            return Either.ok(user.getLong(ResultIndex.userId.name()));
        } catch (Exception exception) {
            log.error("{} fail to query oms user from sale system by accessCode[{}]",
                    LogUtil.getClassMethodName(), accessCode, exception);
            return Either.error(Translate.of("查询用户失败"));
        }
    }

    enum ResultIndex {
        /**
         * result
         */
        result,
        /**
         * codes
         */
        codes,
        /**
         * userId
         */
        userId
    }
}
