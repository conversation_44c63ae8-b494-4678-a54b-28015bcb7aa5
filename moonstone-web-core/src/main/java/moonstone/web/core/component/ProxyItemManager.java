package moonstone.web.core.component;

import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.cache.TaxCacher;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.MatrixCalculator;
import moonstone.common.utils.Translate;
import moonstone.component.dto.item.TaxAndOrigin;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.user.model.StoreProxy;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.model.ProxyItemMask;
import moonstone.web.core.model.dto.ItemWithProfit;
import moonstone.web.core.user.StoreProxyManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ProxyItemManager {

    private final MatrixCalculator<BigDecimal> profitCalculator = new MatrixCalculator<>(BigDecimal::new);
    @Autowired
    IntermediateInfoReadService intermediateInfoReadService;
    BigDecimal HUNDRED_BD = new BigDecimal("100");
    @Autowired
    private ItemCacheHolder itemCacheHolder;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private StoreProxyManager storeProxyManager;
    @Autowired
    private TaxCacher taxCacher;

    public List<Item> findOnSellItem(Long shopId, Long userId) {
        return Optional.ofNullable(mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId))
                        .addCriteria(Criteria.where("storeProxyId").is(userId))
                        .addCriteria(Criteria.where("status").is(1))
                , ProxyItemMask.class))
                .orElseGet(ArrayList::new)
                .stream().map(ProxyItemMask::getItemId)
                .map(itemCacheHolder::findItemById).map(item -> packItemDetailAndProfit(userId, shopId, item, null)).collect(Collectors.toList());
    }

    public List<Long> findProxyIdFromItemId(Long itemId) {
        return Optional.ofNullable(mongoTemplate.find(Query.query(Criteria.where("itemId").is(itemId)).addCriteria(Criteria.where("status").is(1)), ProxyItemMask.class))
                .orElseGet(ArrayList::new)
                .stream().map(ProxyItemMask::getStoreProxyId)
                .collect(Collectors.toList());
    }

    public List<Item> querySelectItemList(Boolean onSell, Long userId, Long currentShopId) {
        Query query = Query.query(Criteria.where("shopId").is(currentShopId))
                .addCriteria(Criteria.where("storeProxyId").is(userId));
        if (onSell != null) {
            if (onSell) {
                query.addCriteria(Criteria.where("status").is(1));
            } else {
                query.addCriteria(Criteria.where("status").is(0));
            }
        }
        return Optional.ofNullable(mongoTemplate.find(query
                , ProxyItemMask.class))
                .orElseGet(ArrayList::new)
                .stream().map(ProxyItemMask::getItemId)
                .map(itemCacheHolder::findItemById).map(item -> packItemDetailAndProfit(userId, currentShopId, item, onSell)).collect(Collectors.toList());
    }

    /**
     * 计算利润 需要重构 代码已经非常臃肿复杂了
     *
     * @param userId 用户Id
     * @param shopId 店铺Id
     * @param item   商品
     * @return 商品与价格
     */
    private ItemWithProfit packItemDetailAndProfit(Long userId, Long shopId, Item item, Boolean onSell) {
        ItemWithProfit itemWithProfit = new ItemWithProfit();
        BeanUtils.copyProperties(item, itemWithProfit);
        // pack origin and tax
        packWithTaxAndOrigin(itemWithProfit);

        if (onSell == null) {
            itemWithProfit.setOnSell(findProxyIdFromItemId(item.getId()).contains(userId));
        } else {
            itemWithProfit.setOnSell(onSell);
        }

        // pack profit
        IntermediateInfo profitRate;
        int level = storeProxyManager.getStoreProxyByShopIdAndUserId(shopId, userId).map(StoreProxy::getLevel).orElse(2);

        for (Sku sku : skuCacheHolder.findSkusByItemId(item.getId())) {
            profitRate = intermediateInfoReadService.findByThirdAndType(sku.getId(), ThirdIntermediateType.SKU.getValue())
                    .orElse(new ArrayList<>())
                    .stream().filter(intermediateInfo -> Objects.equals(1, intermediateInfo.getIsCommission()) && intermediateInfo.getStatus() == 1).findFirst().orElse(null);
            if (profitRate == null) {
                profitRate = intermediateInfoReadService.findByThirdAndType(item.getShopId(), ThirdIntermediateType.SHOP.getValue()).orElse(new ArrayList<>())
                        .stream().findFirst().orElse(null);
            }
            if (profitRate == null) {
                continue;
            }
            BigDecimal skuOrderProfit;
            // 默认优先身份佣金
            if (!ObjectUtils.isEmpty(profitRate.getExtraFeeCalMatrix())) {
                List<BigDecimal> itemPriceList = new ArrayList<>();
                for (int i = 1; i < level; i++) {
                    itemPriceList.add(null);
                }
                itemPriceList.add(BigDecimal.valueOf(sku.getPrice()).divide(HUNDRED_BD, RoundingMode.DOWN));
                skuOrderProfit = ObjectUtils.isEmpty(profitRate.getExtraFeeCalMatrix()) ? BigDecimal.ZERO :
                        profitCalculator.calWithMatrixByLine(profitRate.getExtraFeeCalMatrix(), itemPriceList.toArray(new BigDecimal[]{}))
                                .orElse(BigDecimal.ZERO)
                                .multiply(HUNDRED_BD);
            } else {
                BigDecimal stepProfit = new BigDecimal((level == 1 ? profitRate.getSecondRate() : profitRate.getFirstRate()) * sku.getPrice());
                BigDecimal extraProfit = new BigDecimal((level == 1 ? profitRate.getSecondFee() : profitRate.getFirstFee()));
                if (profitRate.getVersion() == profitRate.getNowVersion()) {
                    // 新版本为万分制  直接/10000即可
                    stepProfit = (stepProfit).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
                } else {
                    // 老版本为百分制  直接/100即可
                    stepProfit = (stepProfit).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                }
                skuOrderProfit = stepProfit.add(extraProfit);

            }
            // 取利润的最小值 避免与用户期待不符合
            if (itemWithProfit.getProfit() != null && itemWithProfit.getProfit() > skuOrderProfit.longValue()) {
                continue;
            }
            itemWithProfit.setProfit(skuOrderProfit.longValue());
        }

        return itemWithProfit;
    }

    /**
     * 添加显示税费
     *
     * @param itemWithProfit 利用Id和isBond
     */
    private void packWithTaxAndOrigin(ItemWithProfit itemWithProfit) {
        if (itemWithProfit.getIsBonded() == 1) {
            for (Sku sku : skuCacheHolder.findSkusByItemId(itemWithProfit.getId())) {
                TaxAndOrigin taxAndOrigin = taxCacher.getOneTaxAndOrigin(sku);
                itemWithProfit.setTax(taxAndOrigin.getTax());
                itemWithProfit.setCountry(taxAndOrigin.getOrigin());
                if (itemWithProfit.getTax() != null) {
                    return;
                }
            }
        }
    }

    public void addItemList(Long currentShopId, Long userId, List<Long> itemIds) {
        for (Long itemId : itemIds) {
            Query query = Query.query(Criteria.where("shopId").is(currentShopId)).addCriteria(Criteria.where("itemId").is(itemId)).addCriteria(Criteria.where("storeProxyId").is(userId));
            if (mongoTemplate.exists(query, ProxyItemMask.class)) {
                throw new JsonResponseException(new Translate("商品[%s => %s]已经在你的清单中或者已上架请勿重复操作", itemId, itemCacheHolder.findItemById(itemId).getName()).toString());
            }
            mongoTemplate.upsert(query, Update.update("status", 0), ProxyItemMask.class);
        }
    }

    public void removeItemList(Long currentShopId, Long userId, List<Long> itemIds) {
        for (Long itemId : itemIds) {
            mongoTemplate.remove(Query.query(Criteria.where("itemId").is(itemId))
                    .addCriteria(Criteria.where("status").is(0))
                    .addCriteria(Criteria.where("storeProxyId").is(userId)), ProxyItemMask.class);
        }
    }

    public void setOnSell(Long userId, List<Long> itemIds, boolean sell) {
        for (Long itemId : itemIds) {
            mongoTemplate.updateFirst(Query.query(Criteria.where("itemId").is(itemId))
                            .addCriteria(Criteria.where("status").is(sell ? 0 : 1))
                            .addCriteria(Criteria.where("storeProxyId").is(userId))
                    , Update.update("status", sell ? 1 : 0)
                    , ProxyItemMask.class);
        }
        for (Long itemId : itemIds) {
            EventSender.publish(new ItemUpdateEvent(itemId));
        }
    }
}
