package moonstone.web.core.component.pay.xinbada.domain.slice;

import io.terminus.pay.model.BusinessPayParams;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.OutSystemIdProvider;
import moonstone.common.utils.Translate;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.user.model.AccountCertification;
import moonstone.user.service.AccountCertificationReadService;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.component.pay.xinbada.domain.XinBaDaPayToken;
import moonstone.web.core.component.pay.xinbada.domain.dto.PayRequest;
import moonstone.web.core.component.pay.xinbada.domain.dto.PayTask;
import moonstone.web.core.constants.ParanaConfig;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class WithdrawPayConvertSlice {
    private final WithDrawProfitApplyReadService withDrawProfitApplyReadService;
    private final WithdrawAccountReadService withdrawAccountReadService;
    private final AccountCertificationReadService accountCertificationReadService;
    private final OutSystemIdProvider outSystemIdProvider;
    private final ParanaConfig paranaConfig;
    private final XinBaDaPayAccountHelperSlice xinBaDaPayAccountHelperSlice;

    /**
     * 转换提现申请为支付请求
     *
     * @param payParams 提现支付请求
     * @return 支付任务
     */
    public PayRequest convertWithdrawIntoRequest(BusinessPayParams payParams) {
        String tradeNo = payParams.getTradeNo();
        Long withdrawApplyId = outSystemIdProvider.decode(tradeNo).getId();
        // match the apply
        WithDrawProfitApply apply = withDrawProfitApplyReadService.findById(withdrawApplyId).getResult();
        Long userId = apply.getUserId();
        Long shopId = apply.getSourceId();
        // take out the certification
        AccountCertification certification = accountCertificationReadService.findByUserIdAndShopId(userId, shopId).take();
        if (!certification.authStatus().isAuthed()) {
            throw new RuntimeException("实名审核未通过");
        }
        PayRequest request = new PayRequest();
        request.setTo_launch_date(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        request.setTrade_mark(Translate.of("宠食推广费"));
        WithdrawAccount withdrawAccount = withdrawAccountReadService.findById(apply.getWithdrawAccountId()).take().orElseThrow(() -> new RuntimeException("提现帐号丢失"));
        request.setTask_list(Collections.singletonList(buildTask(apply, withdrawAccount, certification)));
        request.setCall_back(PayRequest.CallBackUrl.build(paranaConfig.getAppUrlBackend() + "/api/order/paid/" + PayChannelsConstants.XIN_BA_DA_PAY + "/account/ignore"));
        XinBaDaPayToken xinBaDaPayToken = xinBaDaPayAccountHelperSlice.getPayToken(shopId).take();
        request.setCustomer_account_uuid(xinBaDaPayAccountHelperSlice.getCustomAccountUuid(xinBaDaPayToken).take());
        request.setServer_user_uuid(xinBaDaPayAccountHelperSlice.getServerUserUuid(xinBaDaPayToken).take());
        request.setSecret(xinBaDaPayToken.secret());
        request.setAccount(xinBaDaPayToken.xinBaDaUserUuid());
        request.setRequestUUID(apply.requestUUID());
        // 0 => 让用户自己去支付
        // 1 => 自动支付
        request.setIs_to_confirm_order(1);
        return request;
    }

    /**
     * 打包一个提现请求
     *
     * @param apply           提现申请
     * @param withdrawAccount 提现帐号
     * @param certification   实名审核
     * @return 支付任务
     */
    private PayTask buildTask(WithDrawProfitApply apply, WithdrawAccount withdrawAccount, AccountCertification certification) {
        PayTask payTask = new PayTask();
        payTask.setName(certification.getName().trim());
        payTask.setSocial_no(certification.getIdentityCode().trim());
        payTask.setMobile_no(certification.getMobile().trim());
        payTask.setCard_no(withdrawAccount.getAccount().trim());
        payTask.setAmount(BigDecimal.valueOf(apply.getFee() - Optional.ofNullable(apply.getServiceFee()).orElse(0L)).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
        if (Objects.equals(WithDrawProfitApply.WithdrawPaidType.ALIPAY.getType(), withdrawAccount.getType())) {
            payTask.setBank_branch_name("支付宝");
        }
        return payTask;
    }
}
