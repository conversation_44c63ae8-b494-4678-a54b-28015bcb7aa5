package moonstone.web.core.component;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@Getter
public class OmsApiGate {
    @Value("${oms.api.admin:http://npc.daily.yang800.com/backend/}")
    private String apiGate;

    @AllArgsConstructor
    @Getter
    public enum Api {
        /**
         * 查询用户详情
         */
        userDetail("/docking/api/user/details"),
        /**
         * 查询accessCode
         */
        getAccessCode("/docking/api/user/getByAppIdAndUserId"),
        /**
         * 查询accessCode
         */
        queryByAccessCode("/docking/api/user/getByAccessCode"),
        /**
         * 由用户Id查询用户数据
         */
        queryUserById("/docking/api/uc/getByUserId"),
        /**
         * 注册
         *
         * @see <a href="http://yapi.yang800.cn/project/182/interface/api/34186">YAPI</a>
         */
        register("/docking/api/uc/register"),
        /**
         * 根据用户信息查询
         *
         * @see <a href="http://yapi.yang800.cn/project/182/interface/api/34357">文档</a>
         */
        queryUserBy("/docking/api/uc/getByLoginName");
        private final String url;
    }

    enum ResultIndex {
        /**
         * 成功
         */
        success,
        /**
         * 错误
         */
        errorMessage,
        /**
         * 结果
         */
        result
    }
}
