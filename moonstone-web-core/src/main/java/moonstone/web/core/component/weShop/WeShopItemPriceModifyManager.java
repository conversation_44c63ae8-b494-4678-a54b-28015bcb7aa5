package moonstone.web.core.component.weShop;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.json.JsonObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.*;
import moonstone.common.api.APIResp;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.PagingCriteria;
import moonstone.common.utils.*;
import moonstone.component.item.component.TaxChecker;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.search.dto.IndexedWeShopItem;
import moonstone.search.dto.SearchedWeShopItem;
import moonstone.search.weShopItem.WeShopItemSearchReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.weShop.service.WeShopSkuWriteService;
import moonstone.web.core.component.RecordManager;
import moonstone.web.core.component.WeShopItemWriteLogic;
import moonstone.web.core.component.WeShopSkuTaxPreviewVerticle;
import moonstone.web.core.component.item.WeShopItemPreviewPacker;
import moonstone.web.core.component.item.model.PriceModifyDTO;
import moonstone.web.core.component.item.model.WeShopItemCreateDTO;
import moonstone.web.core.component.item.model.WeShopItemPreview;
import moonstone.web.core.component.item.model.WeShopSkuPreview;
import moonstone.web.core.component.item.model.enu.SkuPriceModifyType;
import moonstone.web.core.component.qrCode.WeShopItemShareImageCacheHolder;
import moonstone.web.core.events.weShopItem.WeShopItemDeletedEvent;
import moonstone.web.core.events.weShopItem.WeShopItemDumpEvent;
import moonstone.web.core.events.weShopItem.WeShopItemUpdatedEvent;
import moonstone.web.core.events.weShopItem.WeShopSkuUpdateEvent;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.model.dto.record.ShareTimeToday;
import moonstone.web.core.model.dto.tax.ProfitAndTax;
import moonstone.web.core.shop.application.DefaultWeShopSupplier;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.List;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Predicate;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeShopItemPriceModifyManager extends AbstractVerticle {
    @Autowired
    WeShopSkuWriteService weShopSkuWriteService;
    @Autowired
    ShopWxaProjectReadService shopWxaProjectReadService;
    @Autowired
    ShopWxaReadService shopWxaReadService;
    @Autowired
    WeShopItemReadService weShopItemReadService;
    @Autowired
    SkuReadService skuReadService;
    @Autowired
    SkuCustomReadService skuCustomReadService;
    @Autowired
    WeShopSkuReadService weShopSkuReadService;
    @Autowired
    WeShopItemSearchReadService weShopItemSearchReadService;
    @Autowired
    WeShopShopAccountReadService weShopShopAccountReadService;
    @Autowired
    TaxChecker taxChecker;
    @Autowired
    WeShopItemPreviewPacker weShopItemPreviewPacker;
    @Autowired
    RecordManager recordManager;
    @Autowired
    WeShopItemShareImageCacheHolder weShopItemShareImageCacheHolder;
    @Autowired
    WeShopReadHelper weShopReadHelper;
    @Autowired
    WeShopItemWriteLogic weShopItemWriteLogic;
    @Autowired
    WeShopCacheHolder weShopCacheHolder;
    @Autowired
    WeShopSkuCacheHolder weShopSkuCacheHolder;
    @Autowired
    WeShopItemCacher weShopItemCacher;
    @Autowired
    ItemCacheHolder itemCacheHolder;
    @Autowired
    SkuCacheHolder skuCacheHolder;
    @Autowired
    DefaultWeShopSupplier defaultWeShopSupplier;
    @Autowired
    SourceShopQuerySlice sourceShopQuerySlice;
    Cache<String, ProfitAndTax> profitAndTaxCalculateCache = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(1000).build();

    ThreadLocal<Integer> count = ThreadLocal.withInitial(() -> 1);

    public Response<Boolean> selectItem(Long shopId, Long itemId, Boolean taxBear) {
        try {
            WeShop weShop = requireWeShopOpen(shopId);
            Item item = itemCacheHolder.findItemById(itemId);
            Map<Sku, WeShopItemCreateDTO.PriceModify> skuModifyMap = new HashMap<>(8);
            for (Sku sku : skuCacheHolder.findSkusByItemId(itemId)) {
                skuModifyMap.put(sku, new WeShopItemCreateDTO.PriceModify(1, 0L, taxBear));
            }
            WeShopItemCreateDTO itemCreateDTO = new WeShopItemCreateDTO();
            itemCreateDTO.setItem(item);
            itemCreateDTO.setPriceModifyMapBySku(skuModifyMap);
            List<Long> weShopItemIdList = weShopItemWriteLogic.createWeShopItemOrUpdate(weShop.getId(), Collections.singletonList(itemCreateDTO), false).take();
            weShopItemIdList.forEach(weShopItemId -> EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId)));
            return Response.ok(!weShopItemIdList.isEmpty());
        } catch (Exception ex) {
            log.error("{} fail to selectItem [{}]", LogUtil.getClassMethodName(), itemId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    public Response<WeShopItemPreview> previewPriceModifyForItem(Long weShopItemId, Long itemId, Integer type, Long price, @RequestParam(defaultValue = "false") Boolean taxBear) {
        PriceModifyDTO priceModifyDTO = new PriceModifyDTO();
        List<Sku> skuList = weShopItemId == null ? skuCacheHolder.findSkusByItemId(itemId)
                : Collections.singletonList(skuReadService.findSkuById(weShopSkuReadService.findByWeShopItemId(weShopItemId).getResult().get(0).getSkuId()).getResult());
        priceModifyDTO.setSkuPriceModifyList(new ArrayList<>());
        for (Sku sku : skuList) {
            priceModifyDTO.getSkuPriceModifyList().add(new PriceModifyDTO.SkuPriceModifyDTO(sku.getId(), type, price, taxBear, null));
        }
        Response<List<WeShopItemPreview>> previewListRes = previewPriceModify(priceModifyDTO);
        if (previewListRes.isSuccess()) {
            return Response.ok(previewListRes.getResult().isEmpty() ? null : previewListRes.getResult().get(0));
        }
        return Response.fail(previewListRes.getError());
    }

    public Response<Boolean> addItemIntoListForItem(Long shopId, Long itemId, Integer type, Long price, @RequestParam(defaultValue = "false") Boolean taxBear) {
        PriceModifyDTO priceModifyDTO = new PriceModifyDTO();
        List<Sku> skuList = skuCacheHolder.findSkusByItemId(itemId);
        priceModifyDTO.setSkuPriceModifyList(new ArrayList<>());
        for (Sku sku : skuList) {
            priceModifyDTO.getSkuPriceModifyList().add(new PriceModifyDTO.SkuPriceModifyDTO(sku.getId(), type, price, taxBear, null));
        }
        return addWeShopItemWithPriceModify(shopId, priceModifyDTO);
    }

    public Response<Boolean> modifyPriceForItem(Long shopId, Long itemId, Integer type, Long price, @RequestParam(defaultValue = "false") Boolean taxBear) {
        PriceModifyDTO priceModifyDTO = new PriceModifyDTO();
        List<Sku> skuList = skuCacheHolder.findSkusByItemId(itemId);
        priceModifyDTO.setSkuPriceModifyList(new ArrayList<>());
        for (Sku sku : skuList) {
            priceModifyDTO.getSkuPriceModifyList().add(new PriceModifyDTO.SkuPriceModifyDTO(sku.getId(), type, price, taxBear, null));
        }
        return modifyItemPrice(shopId, priceModifyDTO);
    }

    /**
     * 查找已有的商品列表
     * itemSearchReadService 直接连接elasticSearch 是分布式的,不需要做额外处理. 但是需要获取自己允许访问的店铺与商品列表,需要额外组装数据
     *
     * @param argParam 搜索参数
     *                 param name     商品名称
     *                 param fcid     类目聚合,前台类目id聚合
     *                 param pageNo   第几页
     *                 param pageSize 页面大小
     * @return 商品信息
     */
    public Response<Paging<SearchedWeShopItem>> listItem(Map<String, String> argParam) {
        try {
            Long shopId = null;
            if (argParam.containsKey(QueryParam.shopId.name())) {
                shopId = NumberUtil.parseNumber(argParam.get(QueryParam.shopId.name()), Long.TYPE).orElse(0L);
            }
            WeShop weShop = requireWeShopOpen(shopId);
            // 意义不明的 类目id,那群该死的家伙!
            Map<String, String> queryParam = argParam.entrySet().stream().filter(entry -> StringUtils.hasText(entry.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            queryParam.put("weShopId", weShop.getId().toString());
            int pageNo = Integer.parseInt(queryParam.getOrDefault(QueryParam.pageNo.name(), "1"));
            int pageSize = Integer.parseInt(queryParam.getOrDefault(QueryParam.pageSize.name(), "100"));
            String defaultTemplateName = "search.mustache";
            Integer status = Optional.ofNullable(queryParam.get(QueryParam.status.name())).filter(StringUtils::hasLength).map(Integer::parseInt).orElse(null);
            if (!"1".equals(queryParam.getOrDefault(QueryParam.all.name(), "0")) && Objects.isNull(status)) {
                status = 0;
                queryParam.put(QueryParam.status.name(), status.toString());
            }
            // 限制搜索的店铺范围
            Optional<String> limitShopIds = findLimitShopId(weShop);
            limitShopIds.ifPresent(shopIds -> queryParam.put(QueryParam.shopIds.name(), shopIds));
            Paging<SearchedWeShopItem> searchedItemList = weShopItemSearchReadService.searchInWeShopWithAggs(pageNo, pageSize, defaultTemplateName, queryParam, SearchedWeShopItem.class).getResult().getEntities();
            boolean containInvalidateData = false;
            for (SearchedWeShopItem searchedWeShopItem : searchedItemList.getData()) {
                if (weShopItemCacher.findWeShopItemById(searchedWeShopItem.getId()) == null) {
                    EventSender.publish(new WeShopItemDeletedEvent(searchedWeShopItem.getId()));
                    containInvalidateData = true;
                }
            }
            try {
                if (containInvalidateData) {
                    // 延时400
                    if (count.get() < 2) {
                        count.set(count.get() + 1);
                        Thread.sleep(400);
                        return listItem(argParam);
                    }
                }
            } finally {
                count.remove();
            }
            decorateStatusForOther(searchedItemList.getData(), shopId);
            return Response.ok(searchedItemList);
        } catch (Exception ex) {
            log.error("{} fail to list item[{}]", LogUtil.getClassMethodName(), argParam, ex);
            return Response.fail(ex.getMessage());
        }
    }

    /**
     * 为其他小程序进行清洗状态
     *
     * @param searchedWeShopItems 小程序搜索结果
     * @param shopId              店铺Id    判断是否为供销
     */
    private void decorateStatusForOther(List<SearchedWeShopItem> searchedWeShopItems, Long shopId) {
        if (shopId == null) {
            return;
        }
        if (sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).intoOptional().filter(Predicate.isEqual(shopId)).isPresent()) {
            return;
        }
        for (SearchedWeShopItem searchedWeShopItem : searchedWeShopItems) {
            if (searchedWeShopItem.getStatus() < 0) {
                searchedWeShopItem.setStatus(0);
            }
        }
    }

    /**
     * 查找是否有限制的店铺设置
     *
     * @param weShop 微店
     * @return 限制的店铺Id
     */
    private Optional<String> findLimitShopId(WeShop weShop) {
        String shopIds = Optional.ofNullable(weShopShopAccountReadService.findByWeShopId(weShop.getId()).getResult())
                .map(Collection::stream).orElse(Stream.empty())
                .filter(account -> Objects.equals(1, account.getStatus()))
                .map(WeShopShopAccount::getShopId).filter(Objects::nonNull).distinct()
                .collect(Collector.of(StringBuilder::new, (a, b) -> {
                    a.append(b);
                    a.append("_");
                }, StringBuilder::append)).toString();
        return Optional.of(shopIds).filter(StringUtils::hasLength);
    }

    /**
     * 根据Id 获取预览的效果
     *
     * @param weShopItemIdList id
     * @return 内容
     */
    public Response<List<WeShopItemPreview>> previewFor(List<Long> weShopItemIdList) {
        try {
            List<WeShopItemPreview> previewList = new ArrayList<>();
            for (Long weShopItemId : weShopItemIdList) {
                WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
                Item item = itemCacheHolder.findItemById(weShopItem.getItemId());
                WeShopItemPreview weShopItemPreview = weShopItemPreviewPacker.wrapBaseAttributeFromItem(item);
                weShopItemPreview.setWeShopSkuPreviewList(new ArrayList<>());
                BeanUtils.copyProperties(item, weShopItemPreview);
                List<Sku> skuList = weShopSkuReadService.findByWeShopItemId(weShopItemId)
                        .map(weShopSkus -> weShopSkus.stream().map(WeShopSku::getSkuId).map(skuReadService::findSkuById).map(Response::getResult).collect(Collectors.toList()))
                        .orElseGet(() -> skuCacheHolder.findSkusByItemId(weShopItem.getItemId()));
                for (Sku sku : skuList) {
                    WeShopSku weShopSku = weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShopItem.getWeShopId(), sku.getId()).orElse(null);
                    if (weShopSku == null) {
                        continue;
                    }
                    Optional.ofNullable(sku.getExtraPrice()).map(extraPrice -> extraPrice.get(SkuExtraIndex.suggest.getCode())).map(Integer::longValue).ifPresent(weShopItemPreview::setSuggestPrice);
                    Optional.ofNullable(sku.getPrice()).map(Integer::longValue).ifPresent(weShopItemPreview::setOriginPrice);
                    WeShopSkuPreview weShopSkuPreview = new WeShopSkuPreview();
                    weShopItemPreview.getWeShopSkuPreviewList().add(weShopSkuPreview);
                    BeanUtils.copyProperties(weShopSku, weShopSkuPreview);

                    weShopSkuPreview.setName(Optional.ofNullable(sku.getName()).orElse(item.getName()));
                    weShopSkuPreview.setOriginPrice(sku.getPrice().longValue());
                    weShopSkuPreview.setImage(Optional.ofNullable(sku.getImage_()).orElse(item.getMainImage_()));
                    weShopSkuPreview.setPrice(weShopItemPreview.getOriginPrice());
                    weShopSkuPreview.setSpecification(sku.getSpecification());

                    weShopSkuPreview.setProfit(Optional.ofNullable(weShopSkuPreview.getProfit()).orElse(0L));
                    if (weShopSku.isPriceSet()) {
                        weShopSkuPreview.setPrice(Optional.ofNullable(weShopSku.getPrice()).orElseGet(() -> sku.getPrice() + weShopSku.getDiffPrice()));
                    }
                }
                previewList.add(weShopItemPreview);
            }
            return Response.ok(previewList);
        } catch (Exception ex) {
            log.error("{} fail to preview weShopItem price modify [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(weShopItemIdList), ex);
            return Response.fail(Translate.of("查询商品列表失败"));
        }
    }

    /**
     * 搜索无效的商品
     *
     * @param criteria 搜索条件
     * @return 结果
     */
    public Response<Paging<IndexedWeShopItem>> listInvalidateItem(Long shopId, PagingCriteria criteria) {
        WeShop weShop = requireWeShopOpen(shopId);
        Paging<WeShopItem> weShopItemPaging = weShopItemReadService.paging(null, weShop.getId(), null, null, -3, null, criteria.getPageNo(), criteria.getPageSize()).getResult();
        return Response.ok(new Paging<>(weShopItemPaging.getTotal(), packIndexedView(weShopItemPaging.getData())));
    }

    private List<IndexedWeShopItem> packIndexedView(List<WeShopItem> data) {
        List<IndexedWeShopItem> resultList = new ArrayList<>();
        for (WeShopItem weShopItem : data) {
            IndexedWeShopItem indexedWeShopItem = new IndexedWeShopItem();
            BeanUtils.copyProperties(weShopItem, indexedWeShopItem);
            Item item = itemCacheHolder.findItemById(weShopItem.getItemId());
            BeanUtils.copyProperties(item, indexedWeShopItem);
            indexedWeShopItem.setId(weShopItem.getId());
            indexedWeShopItem.setMainImage(item.getMainImage_());
            indexedWeShopItem.setStatus(weShopItem.getStatus());
            resultList.add(indexedWeShopItem);
        }
        return resultList;
    }

    /**
     * 调用Vertx进行远程预览利润和佣金
     *
     * @param skuId            单品Id
     * @param skuPrice         单品价格
     * @param weShopSkuPreview 修改的实体
     * @return 最后的结果
     */
    public Future<Boolean> previewProfit(Long skuId, Long skuPrice, boolean taxSellerBear, WeShopSkuPreview weShopSkuPreview) {
        StopWatch vertxOutWatch = new StopWatch("vertx-out-watch");
        vertxOutWatch.start("preview-profit-by-vertx");
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        String profitRequestBody = String.format("%s_%s_%s", skuId, skuPrice, taxSellerBear);
        ProfitAndTax existsCache = profitAndTaxCalculateCache.getIfPresent(profitRequestBody);
        if (Objects.nonNull(existsCache)) {
            weShopSkuPreview.setProfit(existsCache.getProfit());
            weShopSkuPreview.setTax(existsCache.getTax());
            future.complete(true);
            return future;
        }
        vertx.eventBus().<JsonObject>request(WeShopSkuTaxPreviewVerticle.class.getName(), profitRequestBody, result -> {
            if (result.failed()) {
                log.debug("{} Vertx Verticle error reply", LogUtil.getClassMethodName(), result.cause());
                future.completeExceptionally(result.cause());
                return;
            }
            log.debug("{} Vertx Verticle reply with [{}]", LogUtil.getClassMethodName(), result.result());
            try {
                ProfitAndTax profitAndTax = result.result().body().mapTo(ProfitAndTax.class);
                if (profitAndTax == null) {
                    future.completeExceptionally(new RuntimeException(Translate.of("计算税费与利润失败")));
                    return;
                }
                weShopSkuPreview.setProfit(profitAndTax.getProfit());
                weShopSkuPreview.setTax(profitAndTax.getTax());
                profitAndTaxCalculateCache.put(profitRequestBody, profitAndTax);
                future.complete(true);
            } catch (Exception ex) {
                log.error("{} fail to previewProfit(sku => {}) cross eventBus", LogUtil.getClassMethodName(), skuId, ex);
                future.completeExceptionally(ex);
            }
            vertxOutWatch.stop();
            log.debug("{} vertx calculate profit time cost [{}]", LogUtil.getClassMethodName(), vertxOutWatch);
        });
        return future;
    }

    /**
     * 批量预览改价结果, 没有真实改价
     *
     * @param priceModifyList 改价参数
     * @return 结果
     */
    public Response<List<WeShopItemPreview>> previewPriceModifyBatch(@RequestBody List<PriceModifyDTO> priceModifyList) {
        if (CollectionUtils.isEmpty(priceModifyList)) {
            return Response.fail(new Translate("请选择商品与规格与价格").toString());
        }
        List<WeShopItemPreview> previewList = new ArrayList<>();
        for (PriceModifyDTO priceModifyDTO : priceModifyList) {
            Response<List<WeShopItemPreview>> previewRes = previewPriceModify(priceModifyDTO);
            if (previewRes.isSuccess()) {
                previewList.addAll(previewRes.getResult());
            } else {
                return Response.fail(previewRes.getError());
            }
        }

        return Response.ok(previewList);
    }

    /**
     * 批量预览改价结果, 没有真实改价
     *
     * @param modify 改价列表字符串形式
     * @return 结果
     */
    public Response<List<WeShopItemPreview>> previewPriceModifyBatch(String modify) {
        if (ObjectUtils.isEmpty(modify)) {
            return Response.fail(new Translate("请选择商品与规格与价格").toString());
        }
        List<WeShopItemPreview> previewList = new ArrayList<>();
        for (String priceModify : modify.split(";")) {
            Response<List<WeShopItemPreview>> previewOpt = previewPriceModify(new PriceModifyDTO(priceModify, null));
            if (previewOpt.isSuccess()) {
                previewList.addAll(previewOpt.getResult());
            } else {
                return Response.fail(previewOpt.getError());
            }
        }

        return Response.ok(previewList);
    }

    /**
     * 批量预览改价结果, 没有真实改价
     *
     * @param priceModifyDTOArray 改价列表字符串形式
     * @return 结果
     */
    public Response<List<WeShopItemPreview>> previewPriceModifyBatch(@RequestBody PriceModifyDTO[] priceModifyDTOArray) {
        if (ObjectUtils.isEmpty(priceModifyDTOArray)) {
            return Response.fail(new Translate("请选择商品与规格与价格").toString());
        }
        List<WeShopItemPreview> previewList = new ArrayList<>();
        for (PriceModifyDTO priceModifyDTO : priceModifyDTOArray) {
            previewList.addAll(previewPriceModify(priceModifyDTO).getResult());
        }

        return Response.ok(previewList);
    }

    /**
     * 尝试改价,是单个商品,如果是多个商品,请多次调用,避免堵塞
     *
     * @param modify 改价参数,skuId-type-price规则修改
     * @return 预览的商品数据
     */
    public Response<List<WeShopItemPreview>> previewPriceModify(@RequestBody PriceModifyDTO modify) {
        // WeShop weShop = requireWeShopOpen();
        if (CollectionUtils.isEmpty(modify.getSkuPriceModifyList())) {
            return Response.fail(new Translate("请选择商品后操作").toString());
        }
        Map<Long, List<PriceModifyDTO.SkuPriceModifyDTO>> modifyByItemIdMap = new HashMap<>(8);
        for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : modify.getSkuPriceModifyList()) {
            // 无意义的冗余, 由于WeShopItemId 无法将 skuId成功传递
            if (skuPriceModifyDTO.getWeShopItemId() != null && skuPriceModifyDTO.getSkuId() == null) {
                extractSkuIdFromWeShopItemId(skuPriceModifyDTO.getWeShopItemId())
                        .ifPresent(skuPriceModifyDTO::setSkuId);
            }
            Long itemId = Optional.ofNullable(skuPriceModifyDTO.getItemId()).orElseGet(() -> skuCacheHolder.findSkuById(skuPriceModifyDTO.getSkuId()).getItemId());
            if (!modifyByItemIdMap.containsKey(itemId)) {
                modifyByItemIdMap.put(itemId, new ArrayList<>());
            }
            modifyByItemIdMap.get(itemId).add(skuPriceModifyDTO);
        }

        List<WeShopItemPreview> previewResultList = new ArrayList<>();

        HashMap<WeShopSkuPreview, Future<Boolean>> previewTaskList = new HashMap<>(8);
        for (Long itemId : modifyByItemIdMap.keySet()) {

            WeShopItemPreview weShopItemPreview = weShopItemPreviewPacker.wrapBaseAttributeFromItem(itemCacheHolder.findItemById(itemId));
            previewResultList.add(weShopItemPreview);
            boolean configure = false;
            for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : modifyByItemIdMap.get(itemId)) {
                WeShopSkuPreview weShopSkuPreview = new WeShopSkuPreview();
                weShopItemPreview.getWeShopSkuPreviewList().add(weShopSkuPreview);

                Sku sku = Optional.ofNullable(skuPriceModifyDTO.getSkuId()).map(skuCacheHolder::findSkuById)
                        .orElseGet(() -> skuCacheHolder.findSkusByItemId(itemId).stream().filter(onSellSku -> onSellSku.getStatus() > 0).findFirst().orElseThrow(() -> new RuntimeException(Translate.of("商品已经下架"))));
                // 只选择显示第一个
                if (!configure) {
                    configure = true;
                    Optional.ofNullable(sku.getExtraPrice()).map(extraPrice -> extraPrice.get(SkuExtraIndex.suggest.getCode())).map(Integer::longValue).ifPresent(weShopItemPreview::setSuggestPrice);
                    Optional.ofNullable(sku.getPrice()).map(Integer::longValue).ifPresent(weShopItemPreview::setOriginPrice);
                }
                weShopSkuPreview.setSkuId(sku.getId());
                weShopSkuPreview.setSpecification(sku.getSpecification());

                Item item = itemCacheHolder.findItemById(sku.getItemId());
                weShopSkuPreview.setName(Optional.ofNullable(sku.getName()).orElse(item.getName()));
                weShopSkuPreview.setOriginPrice(sku.getPrice().longValue());
                weShopSkuPreview.setPrice(sku.getPrice().longValue());
                weShopSkuPreview.setImage(Optional.ofNullable(sku.getImage_()).orElse(item.getMainImage_()));

                if (Objects.nonNull(skuPriceModifyDTO.getType()) && Objects.nonNull(skuPriceModifyDTO.getPrice())) {
                    Long previewPrice = skuPriceModifyDTO.getType().equals(SkuPriceModifyType.SET.getValue()) ? skuPriceModifyDTO.getPrice() : skuPriceModifyDTO.getPrice() + sku.getPrice();
                    weShopSkuPreview.setPrice(previewPrice);
                    if (Optional.ofNullable(item.getIsBonded()).map(BondedType::fromInt).filter(BondedType::isBonded).isPresent()) {
                        previewTaskList.put(weShopSkuPreview, previewProfit(skuPriceModifyDTO.getSkuId(), previewPrice, skuPriceModifyDTO.getTaxBear(), weShopSkuPreview));
                    } else {
                        weShopSkuPreview.setProfit(previewPrice - sku.getPrice());
                        weShopSkuPreview.setTax(0L);
                    }
                } else {
                    weShopSkuPreview.setProfit(0L);
                    weShopSkuPreview.setProfit(sku.getPrice().longValue());
                    weShopSkuPreview.setTax(taxChecker.getTax(sku, 1));
                }
            }
        }
        for (WeShopSkuPreview preview : previewTaskList.keySet()) {
            Future<Boolean> trySetProfit = previewTaskList.get(preview);
            try {
                trySetProfit.get(300, TimeUnit.MILLISECONDS);
            } catch (Exception timeout) {
                log.error("{} preview profit [skuId => {}] timeout", LogUtil.getClassMethodName(), preview.getSkuId());
                try {
                    trySetProfit.get(15, TimeUnit.SECONDS);
                } catch (Exception ex) {
                    log.error("{} can't preview the profit for [sku => {}] serve timeout", LogUtil.getClassMethodName(), preview.getSkuId());
                }
            }
        }
        return Response.ok(previewResultList);
    }

    /**
     * 从已经选择的WeShopItem中提取中可以使用的SkuId, 前提为specification唯一
     *
     * @param weShopItemId 选择的商品
     * @return 查找到的SkuId
     * @see Sku#getSpecification() 批次/型号需要唯一
     */
    private Optional<Long> extractSkuIdFromWeShopItemId(Long weShopItemId) {
        WeShopItem weShopItem = weShopItemReadService.findById(weShopItemId).getResult();
        if (weShopItem != null) {
            List<WeShopSku> skuList = weShopSkuReadService.findByWeShopItemId(weShopItemId).orElseGet(ArrayList::new);
            WeShopSku matchedSku = null;
            int matchCount = 0;
            for (WeShopSku weShopSku : skuList) {
                if (Objects.equals(skuCacheHolder.findSkuById(weShopSku.getSkuId()).getSpecification(), weShopItem.getSpecification())) {
                    matchedSku = weShopSku;
                    matchCount++;
                }
            }
            if (matchCount == 1) {
                return Optional.ofNullable(matchedSku.getSkuId());
            }
        }
        return Optional.empty();
    }


    public Response<Boolean> addWeShopItemWithPriceModifyBatch(Long shopId, @RequestBody List<PriceModifyDTO> priceModifyList) {
        if (CollectionUtils.isEmpty(priceModifyList)) {
            return Response.fail(new Translate("请选择商品与规格与价格").toString());
        }
        for (PriceModifyDTO priceModifyDTO : priceModifyList) {
            Response<Boolean> result = addWeShopItemWithPriceModify(shopId, priceModifyDTO);
            if (!result.isSuccess()) {
                return result;
            }
        }

        return Response.ok(true);
    }

    /**
     * 添加商品进入到自己的清单内
     *
     * @param modify 修改价格的参数
     * @return 修改结果
     */
    public Response<Boolean> addWeShopItemWithPriceModify(Long shopId, @RequestBody PriceModifyDTO modify) {
        try {
            // 判断店铺存在
            WeShop weShop = requireWeShopOpen(shopId);
            // 判断商品已经选择
            if (modify.getSkuPriceModifyList().isEmpty()) {
                return Response.fail(new Translate("请选择商品").toString());
            }
            // 设置一个承接者
            Map<Long, WeShopItemCreateDTO> weShopItemMapByItemId = new HashMap<>(8);
            if (modify.getSkuPriceModifyList().stream().map(PriceModifyDTO.SkuPriceModifyDTO::getSkuId).distinct().count() != modify.getSkuPriceModifyList().size()) {
                log.error("{} illegal request [{}] weShop[{}]", LogUtil.getClassMethodName(), JSON.toJSONString(modify), weShop.getId());
                return Response.fail(new Translate("非法调用, 重复单品").toString());
            }
            // 填装DTO, 利用hash equal特性
            for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : modify.getSkuPriceModifyList()) {
                //设置在获取不到skuId的情况下，可以用商品ItemId
                if (EmptyUtils.isEmpty(skuPriceModifyDTO.getSkuId()) && EmptyUtils.isNotEmpty(skuPriceModifyDTO.getItemId())) {
                    Response<List<Sku>> skus = skuReadService.findSkusByItemId(skuPriceModifyDTO.getItemId());
                    if (skus.isSuccess() && EmptyUtils.isNotEmpty(skus.getResult())) {
                        if (skus.getResult().stream().noneMatch(entity -> Objects.equals(entity.getStatus(), 1))) {
                            return Response.fail("该商品已下架");
                        }
                        Long skuId = skus.getResult().stream().filter(entity -> Objects.equals(entity.getStatus(), 1))
                                .findFirst().map(Sku::getId).orElse(-1L);
                        skuPriceModifyDTO.setSkuId(skuId);
                    }
                }
                if (skuPriceModifyDTO.getSkuId() != null && (skuPriceModifyDTO.getPrice() == null || skuPriceModifyDTO.getPrice() == 0)) {
                    if (weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShop.getId(), skuPriceModifyDTO.getSkuId()).filter(selected -> selected.getStatus() >= 0).isPresent()) {
                        return Response.fail(Translate.of("已存在"));
                    }
                }

                Sku sku = skuCacheHolder.findSkuById(skuPriceModifyDTO.getSkuId());
                if (sku.getStatus() <= 0) {
                    continue;
                }
                Item item = itemCacheHolder.findItemById(sku.getItemId());
                if (item.getStatus() <= 0) {
                    return Response.fail(new Translate("商品[%s]已不可用,请重试", item.getName()).toString());
                }
                WeShopItemCreateDTO weShopItemCreateDTO = weShopItemMapByItemId.get(item.getId());
                if (weShopItemCreateDTO == null) {
                    weShopItemCreateDTO = new WeShopItemCreateDTO();
                    weShopItemMapByItemId.put(item.getId(), weShopItemCreateDTO);
                }
                if (weShopItemCreateDTO.getPriceModifyMapBySku() == null) {
                    weShopItemCreateDTO.setPriceModifyMapBySku(new HashMap<>(8));
                }
                weShopItemCreateDTO.setItem(item);
                WeShopItemCreateDTO.PriceModify priceModify = new WeShopItemCreateDTO.PriceModify();
                BeanUtils.copyProperties(skuPriceModifyDTO, priceModify);
                weShopItemCreateDTO.getPriceModifyMapBySku().put(sku, priceModify);
            }
            Either<List<Long>> addItemRes = weShopItemWriteLogic.createWeShopItemOrUpdate(weShop.getId(), new ArrayList<>(weShopItemMapByItemId.values()), false)
                    .ifSuccess(weShopItemIdList -> weShopItemIdList.forEach(weShopItemId -> EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId))));
            // 如果为空则认为是上架失败
            Either<Boolean> result = addItemRes
                    .map(List::isEmpty)
                    .map(Boolean.FALSE::equals);

            for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : modify.getSkuPriceModifyList()) {
                Sku sku = skuCacheHolder.findSkuById(skuPriceModifyDTO.getSkuId());
                EventSender.publish(new WeShopSkuUpdateEvent(null, weShop.getId(), sku.getItemId()));
            }
            List<Future<Boolean>> complete = new LinkedList<>();
            for (Long weShopItemId : addItemRes.orElseGet(ArrayList::new)) {
                weShopItemShareImageCacheHolder.invalidateForWeShopItemId(weShopItemId);
                CompletableFuture<Boolean> done = new CompletableFuture<>();
                EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId));
                EventSender.send(new WeShopItemDumpEvent(weShopItemId), r -> done.complete(r.succeeded()));
                complete.add(done);
            }
            for (Future<Boolean> done : complete) {
                done.get(5, TimeUnit.SECONDS);
            }
            Thread.sleep(1200);
            if (result.isSuccess()) {
                return Response.ok(true);
            }
            return Response.fail(result.getErrorMsg());
        } catch (Exception ex) {
            log.error("{} fail to add item into List with [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(modify), ex);
            return Response.fail(ex.getMessage());
        }
    }


    public Response<Boolean> modifyItemPriceBatch(Long shopId, @RequestBody List<PriceModifyDTO> priceModifyDTOList) {
        if (CollectionUtils.isEmpty(priceModifyDTOList)) {
            return Response.fail(new Translate("请选择商品与规格与价格").toString());
        }
        for (PriceModifyDTO priceModifyDTO : priceModifyDTOList) {
            Response<Boolean> result = modifyItemPrice(shopId, priceModifyDTO);
            if (!result.isSuccess()) {
                return result;
            }
        }
        return Response.ok(true);
    }

    /**
     * 修改微店利润
     *
     * @param modify 利润修改
     * @return 是否成功修改
     */
    public Response<Boolean> modifyItemPrice(Long shopId, @RequestBody PriceModifyDTO modify) {
        try {
            WeShop weShop = requireWeShopOpen(shopId);
            if (CollectionUtils.isEmpty(modify.getSkuPriceModifyList())) {
                return Response.fail(new Translate("请选择商品").toString());
            }
            // 修改不报去重错误
            Map<Long, WeShopItemCreateDTO> weShopItemMapByItemId = new HashMap<>(8);
            List<PriceModifyDTO.SkuPriceModifyDTO> priceModifyList = new ArrayList<>();
            Set<Long> priceModifyDistrictSet = new HashSet<>();
            for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : modify.getSkuPriceModifyList()) {
                if (!priceModifyDistrictSet.contains(skuPriceModifyDTO.getSkuId())) {
                    priceModifyList.add(skuPriceModifyDTO);
                    priceModifyDistrictSet.add(skuPriceModifyDTO.getSkuId());
                }
                if (Objects.isNull(skuPriceModifyDTO.getSkuId())) {
                    continue;
                }
                if (!weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShop.getId(), skuPriceModifyDTO.getSkuId()).isPresent()) {
                    log.error("{} not weShop[{}] owned sku[{}]", LogUtil.getClassMethodName(), weShop.getId(), skuPriceModifyDTO.getSkuId());
                    return Response.fail(new Translate("请将单品[%s]加入清单后再试", skuCacheHolder.findSkuById(skuPriceModifyDTO.getSkuId()).getName()).toString());
                }
            }
            // 判断是否已经拥有
            // 填装DTO, 利用hash equal特性
            for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : priceModifyList) {
                Sku sku = Optional.ofNullable(skuPriceModifyDTO.getSkuId()).map(skuCacheHolder::findSkuById)
                        .orElseGet(() -> skuCacheHolder.findSkusByItemId(skuPriceModifyDTO.getItemId()).stream()
                                .filter(onSellSku -> onSellSku.getStatus() > 0).findFirst().orElseThrow(() -> new RuntimeException(Translate.of("商品[%s]查找失败", skuPriceModifyDTO.getItemId()))));
                SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
                // 如果商家保税则不允许设置消费者承担税费
                if (Objects.nonNull(skuCustom) && Objects.equals(2, skuCustom.getCustomTaxHolder())) {
                    skuPriceModifyDTO.setTaxBear(true);
                }
                if (sku.getStatus() <= 0) {
                    continue;
                }
                Item item = itemCacheHolder.findItemById(sku.getItemId());
                if (item.getStatus() <= 0) {
                    return Response.fail(new Translate("商品[%s]已不可用,请重试", item.getName()).toString());
                }
                WeShopItemCreateDTO weShopItemCreateDTO = weShopItemMapByItemId.get(item.getId());
                if (weShopItemCreateDTO == null) {
                    weShopItemCreateDTO = new WeShopItemCreateDTO();
                    weShopItemMapByItemId.put(item.getId(), weShopItemCreateDTO);
                }
                if (weShopItemCreateDTO.getPriceModifyMapBySku() == null) {
                    weShopItemCreateDTO.setPriceModifyMapBySku(new HashMap<>(8));
                }
                weShopItemCreateDTO.setItem(item);
                WeShopItemCreateDTO.PriceModify priceModify = new WeShopItemCreateDTO.PriceModify();
                BeanUtils.copyProperties(skuPriceModifyDTO, priceModify);
                weShopItemCreateDTO.getPriceModifyMapBySku().put(sku, priceModify);
            }
            // notify the dump and sync the result for fluent
            Either<List<Long>> createOrUpdateRes = weShopItemWriteLogic.createWeShopItemOrUpdate(weShop.getId(), new ArrayList<>(weShopItemMapByItemId.values()), true);
            Response<Boolean> result = createOrUpdateRes
                    // 如果为空则认为是上架失败
                    .map(List::isEmpty)
                    .map(Boolean.FALSE::equals)
                    .map(Response::ok).orElse(Response.fail(createOrUpdateRes.getErrorMsg()));

            for (PriceModifyDTO.SkuPriceModifyDTO skuPriceModifyDTO : modify.getSkuPriceModifyList()) {
                Sku sku = Optional.ofNullable(skuPriceModifyDTO.getSkuId()).map(skuCacheHolder::findSkuById)
                        .orElseGet(() -> skuCacheHolder.findSkusByItemId(skuPriceModifyDTO.getItemId()).stream()
                                .filter(onSellSku -> onSellSku.getStatus() > 0).findFirst().orElseThrow(() -> new RuntimeException(Translate.of("商品[%s]查找失败", skuPriceModifyDTO.getItemId()))));
                EventSender.publish(new WeShopSkuUpdateEvent(null, weShop.getId(), sku.getItemId()));
            }
            List<Long> weShopItemIds = createOrUpdateRes.orElseGet(ArrayList::new);
            List<Future<Boolean>> complete = new ArrayList<>(weShopItemIds.size());
            for (Long weShopItemId : weShopItemIds) {
                weShopItemShareImageCacheHolder.invalidateForWeShopItemId(weShopItemId);
                CompletableFuture<Boolean> done = new CompletableFuture<>();
                EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId));
                EventSender.send(new WeShopItemDumpEvent(weShopItemId), r -> done.complete(r.succeeded()));
                complete.add(done);
            }
            for (Future<Boolean> done : complete) {
                done.get(5, TimeUnit.SECONDS);
            }
            Thread.sleep(200);
            return result;
        } catch (Exception ex) {
            log.error("{} fail to modify price for weShopItem with [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(modify), ex);
            return Response.fail(ex.getMessage());
        }
    }

    public Response<Boolean> deleteWeShopItem(Long shopId, Long[] weShopItemIds, @RequestBody DeleteListDTO list) {
        try {
            WeShop weShop = requireWeShopOpen(shopId);
            if (weShopItemIds == null) {
                weShopItemIds = list.getWeShopItemIds();
            }
            for (Long weShopItemId : weShopItemIds) {
                WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
                if (weShopItem == null) {
                    return Response.fail(new Translate("商品未找到或以删除").toString());
                }
                if (!weShopItem.getWeShopId().equals(weShop.getId())) {
                    return Response.fail(new Translate("商品不属于你,请重试").toString());
                }
                List<WeShopSku> weShopSkuList = weShopSkuReadService.findByWeShopIdAndItemId(weShop.getId(), weShopItem.getItemId()).take();

                weShopItemWriteLogic.delete(weShop.getId(), weShopItemId);
                weShopSkuList.forEach(weShopSku -> EventSender.publish(new WeShopSkuUpdateEvent(weShopSku.getId(), weShopSku.getWeShopId(), weShopSku.getItemId())));
            }
            List<Future<Boolean>> complete = new ArrayList<>(weShopItemIds.length);
            for (Long weShopItemId : weShopItemIds) {
                CompletableFuture<Boolean> done = new CompletableFuture<>();
                EventSender.publish(new WeShopItemDeletedEvent(weShopItemId));
                EventSender.send(new WeShopItemDumpEvent(weShopItemId), r -> done.complete(r.succeeded()));
                complete.add(done);
            }
            for (Future<Boolean> done : complete) {
                done.get(2, TimeUnit.SECONDS);
            }
            return Response.ok(true);
        } catch (Exception ex) {
            log.error("{} fail to modify price for weShopItem with [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(list), ex);
            return Response.fail(ex.getMessage());
        }
    }

    public Response<Boolean> switchStatus(Long shopId, Long[] weShopItemIds, boolean onSell) {
        try {
            WeShop weShop = requireWeShopOpen(shopId);
            for (Long weShopItemId : weShopItemIds) {
                WeShopItem weShopItem = weShopItemReadService.findById(weShopItemId).getResult();
                List<WeShopSku> weShopSkuList = weShopSkuReadService.findByWeShopIdAndItemId(weShop.getId(), weShopItem.getItemId()).take();
                if (!weShopItem.getWeShopId().equals(weShop.getId())) {
                    return Response.fail(new Translate("商品不属于你,请重试").toString());
                }
                if (Objects.equals(weShopItem.getStatus(), onSell ? 1 : -1)) {
                    continue;
                }
                if (weShop.getStatus() != 1 && onSell) {
                    throw Translate.exceptionOf("店铺未通过审核, 无法上架商品");
                }
                if (onSell) {
                    for (WeShopSku weShopSku : weShopSkuReadService.findByWeShopItemId(weShopItemId).getResult()) {
                        if (!weShopSku.isPriceSet()) {
                            WeShopSku initDiffPrice = new WeShopSku();
                            initDiffPrice.setId(weShopSku.getId());
                            initDiffPrice.setDiffPrice(0L);
                            weShopSkuWriteService.update(initDiffPrice);
                        }
                    }
                }
                weShopItemWriteLogic.updateStatus(weShop.getId(), weShopItemId, onSell ? 1 : -1);
                CompletableFuture<Boolean> done = new CompletableFuture<>();
                weShopSkuList.forEach(weShopSku -> EventSender.publish(new WeShopSkuUpdateEvent(weShopSku.getId(), weShopSku.getWeShopId(), weShopSku.getItemId())));
                EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId));
                EventSender.send(new WeShopItemDumpEvent(weShopItemId), res -> done.complete(res.succeeded()));
                done.get(2, TimeUnit.SECONDS);
            }
            Thread.sleep(1200);
            return Response.ok(true);
        } catch (TimeoutException timeoutException) {
            log.error("{} time out of vertx", LogUtil.getClassMethodName(), timeoutException);
            return Response.ok(true);
        } catch (Exception ex) {
            log.error("{} fail to switch status with [{}] onSell [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(weShopItemIds), onSell, ex);
            return Response.fail(ex.getMessage());
        }
    }

    /**
     * 要求用户有开启店铺
     */
    private WeShop requireWeShopOpen(Long shopId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new JsonResponseException("user.not.login");
        }
        Optional<WeShop> weShop = Optional.ofNullable(weShopReadHelper.findWeShopByUserIdAndShopId(commonUser.getId(), shopId));
        if (!weShop.isPresent()) {
            throw new JsonResponseException(new Translate("请申请开启店铺").toString());
        }
        return weShop.get();
    }

    public void share(Long weShopItemId) {
        WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
        recordManager.increaseRecord(null, ShareTimeToday.build(weShopItem.getWeShopId(), OrderOutFrom.WE_SHOP, weShopItem.getItemId()));
    }

    public void renderShareImage(@RequestParam(required = false) Long itemId, @RequestParam(required = false) Long weShopItemId, @RequestParam(required = false) Long weShopId, @RequestParam(required = false) Long projectId, HttpServletResponse response) {
        try {
            response.setHeader("Cache-Control", "no-cache, must-revalidate");
            response.setContentType("image/png");
            // share item
            if (Objects.nonNull(weShopItemId) && Objects.nonNull(weShopItemCacher.findWeShopItemById(weShopItemId))) {
                ImageIO.write(weShopItemShareImageCacheHolder.getShareImageForWeShopItemId(weShopItemId, projectId), "png", response.getOutputStream());
                return;
            }
            // judge share item or shop
            if (Objects.nonNull(itemId)) {
                CommonUser operator = UserUtil.getCurrentUser();
                // has login ?
                if (Optional.ofNullable(operator).map(CommonUser::getWeShopId).isPresent()) {
                    try {
                        weShopItemId = weShopItemCacher.findWeShopItemByWeShopIdAndItemId(operator.getWeShopId(), itemId).getId();
                    } catch (Exception ignore) {
                    }
                }
                if (Objects.isNull(weShopItemId)) {
                    if (Objects.isNull(weShopId)) {
                        long shopId = Optional.ofNullable(projectId)
                                .map(shopWxaProjectReadService::findById).map(Response::getResult).map(ShopWxaProject::getShopWxaId)
                                .map(shopWxaReadService::findById).map(Response::getResult).map(ShopWxa::getShopId)
                                .orElseGet(() -> itemCacheHolder.findItemById(itemId).getShopId());
                        weShopId = defaultWeShopSupplier.getDefaultWeShop(shopId).take();
                    }
                    log.debug("{} draw share image for Item[Id => {}, Project => {}, weShopId => {}]", LogUtil.getClassMethodName(), itemId, projectId, weShopId);
                    weShopItemId = weShopItemCacher.findWeShopItemByWeShopIdAndItemId(weShopId, itemId).getId();
                }
                ImageIO.write(weShopItemShareImageCacheHolder.getShareImageForWeShopItemId(weShopItemId, projectId), "png", response.getOutputStream());
                return;
            } else if (Objects.nonNull(weShopId) && Objects.nonNull(weShopCacheHolder.findByWeShopId(weShopId))) {
                // share shop image
                ImageIO.write(weShopItemShareImageCacheHolder.createWeShopShareImage(weShopId, projectId), "png", response.getOutputStream());
                return;
            }
            BufferedImage bufferedImage = new BufferedImage(500, 500, BufferedImage.TYPE_INT_ARGB);
            Graphics graphics = bufferedImage.getGraphics();
            graphics.setColor(Color.RED);
            graphics.drawString(Translate.of("illegal arg, check it out and retry it"), 150, 250);
            ImageIO.write(bufferedImage, "png", response.getOutputStream());
        } catch (Exception ex) {
            log.error("{} fail to render shareImage for item [{}]", LogUtil.getClassMethodName(), weShopItemId, ex);
            throw new RuntimeException(ex);
        }
    }

    /**
     * delete the invalidate item in weShop List
     *
     * @param weShopId weShopId
     * @return if we success
     */
    public APIResp<Boolean> deleteInvalidateItem(Long weShopId) {
        if (Objects.isNull(weShopId)) {
            return APIResp.error(Translate.of(""));
        }
        CompletableFuture<Boolean> complete = new CompletableFuture<>();
        vertx.eventBus().request(WeShopVertxEvent.DELETE_INVALIDATE_ITEM.name(), weShopId, r -> complete.complete(r.succeeded()));
        try {
            return APIResp.ok(complete.get(2, TimeUnit.SECONDS));
        } catch (Exception exception) {
            log.error("{} fail to delete invalidate item for weShopId[{}]", LogUtil.getClassMethodName(), weShopId, exception);
            return APIResp.error(Translate.of("System Error"));
        }
    }

    /**
     * 获取已拥有的清单数量
     *
     * @return 已经拥有的清单数量
     */
    public Response<Long> countOwnedListItem(Long weShopId) {
        Response<Long> countRes = weShopItemReadService.countByWeShopIdAndStatus(weShopId, 0);
        if (countRes.isSuccess()) {
            if (countRes.getResult() == null) {
                return Response.ok(0L);
            }
        }
        return countRes;
    }

    public Boolean appShare(Long weShopItemId) {
        WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
        return recordManager.increaseRecord(null, ShareTimeToday.build(weShopItem.getWeShopId(), OrderOutFrom.WE_SHOP, weShopItem.getItemId()));
    }

    enum QueryParam {
        /**
         * 查询的指定字段
         */
        shopId, shopIds, all, status, pageNo, pageSize
    }

    @Data
    public static class DeleteListDTO {
        Long[] weShopItemIds;
    }
}
