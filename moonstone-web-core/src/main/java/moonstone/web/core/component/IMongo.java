package moonstone.web.core.component;

import blue.sea.moonstone.bridge.app.ShareDataHelper;
import io.vertx.core.Vertx;
import io.vertx.ext.mongo.MongoClient;

public enum IMongo {
    instance;
    MongoClient mongoClient = null;

    public MongoClient current(){
        return client(Vertx.currentContext().owner());
    }

    public MongoClient client(Vertx vertx){
        if (mongoClient == null) {
            synchronized (this) {
                if (mongoClient == null) {
                    mongoClient = MongoClient.createShared(vertx,
                            ShareDataHelper.getMongoConfig(vertx));
                }
            }
        }
        return mongoClient;
    }
}
