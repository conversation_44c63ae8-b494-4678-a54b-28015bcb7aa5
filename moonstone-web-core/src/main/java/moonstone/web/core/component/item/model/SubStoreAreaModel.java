package moonstone.web.core.component.item.model;

import lombok.Data;
import moonstone.common.utils.UUID;
import org.springframework.data.annotation.Id;

import java.util.List;

@Data
public class SubStoreAreaModel {
	@Id
	String id;
	List<String> area;
	List<String> serviceProviderId;
	List<Long> itemId;
	List<String> item;
	Long shopId;
	Boolean checked;
	// TODO: add enable to apply the rule
	Boolean enable;
	// use uuid as version to specify
	String version = UUID.randomUUID().toString();

	Integer getCount() {
		if (itemId == null) {
			return 0;
		}
		return itemId.size();
	}
}
