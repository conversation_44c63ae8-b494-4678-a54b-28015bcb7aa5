package moonstone.web.core.component.pay.allinpayyst;

import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.BizParameter;
import com.allinpay.sdk.bean.OpenConfig;
import com.allinpay.sdk.bean.OpenResponse;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.web.op.allinpay.AllinPayConfigConstant;
import moonstone.web.op.allinpay.AllinPayFactory;
import moonstone.web.op.allinpay.OpenClientProxy;
import moonstone.web.op.allinpay.util.NacosAutoConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class AllinPayFactoryImpl implements AllinPayFactory {

	private static final Map<Long, OpenClientProxy> clientMap = new HashMap<>();

	private static final Map<Long, OpenConfig> clientConfigMap = new HashMap<>();

	/**
	 * 十分依赖 {@link AllInPayYSTTokenProvider} 的tokenConfig启用、停用、修改功能
	 */
	@Resource
	private AllInPayYSTTokenProvider allInPayYSTTokenProvider;

	@Resource
	private NacosAutoConfiguration configuration;

	/**
	 * 执行通联的接口(execute)
	 *
	 * @param shopId
	 * @param methodName 接口名称
	 * @param methodDesc 接口描述
	 * @param param      入参
	 * @return 接口返回结果
	 */
	@Override
	public Result<String> execute(Long shopId, String methodName, String methodDesc, BizParameter param){
		OpenClient openClient;
		try {
			openClient = get(shopId);
		}catch (Exception e){
			return Result.fail("创建OpenClient失败");
		}
		return execute(openClient, methodName, methodDesc, param);
	}

	/**
	 * 应使用同一个openClient加密与发起请求
	 * @param openClient
	 * @param methodName
	 * @param methodDesc
	 * @param param
	 * @return
	 */
	@Override
	public Result<String> execute(OpenClient openClient, String methodName, String methodDesc, BizParameter param) {
		log.info("【通联】{} 入参 : {}", methodDesc, JSONObject.toJSONString(param));
		OpenResponse response;
		try {
			response  = openClient.execute(methodName, param);
		} catch (Exception e) {
			log.warn(methodDesc + "失败,msg={}",e.getMessage());
			return Result.fail("通联: " + methodDesc + "调用异常");
		}
		log.info("【通联】{} 响应 : {}", methodDesc, JSONObject.toJSONString(response));

		// 一级结构错误码
		boolean codeOk = AllinPayConfigConstant.CODE_OK.equals(response.getCode());
		if(!codeOk){
			return Result.fail("通联: " + response.getMsg());
		}

		// 二级结构错误码
		boolean subCodeOk = StringUtils.isBlank(response.getSubCode()) ||
				AllinPayConfigConstant.SUBCODE_OK.equals(response.getSubCode());
		if (!subCodeOk) {
			return Result.fail("通联: " + response.getSubMsg());
		}
		return Result.data(response.getData());
	}


	@Override
	public OpenClient get(Long shopId){
		if(shopId == null){
			log.error("错误的商家配置 shopId为空");
			throw new RuntimeException("错误的商家配置");
		}
//		if(179L == shopId){
//			final String url = configuration.getValue(AllinPayConfigConstant.URL);
//			final String appId = configuration.getValue(AllinPayConfigConstant.APPID);
//			final String secretKey = configuration.getValue(AllinPayConfigConstant.SECRET_KEY);
//			final String pwd = configuration.getValue(AllinPayConfigConstant.PWD);
//			final String privateKeyPath = configuration.getValue(AllinPayConfigConstant.PRIVATE_KEY_PATH);
//
//			final String tlPublicKey = configuration.getValue(AllinPayConfigConstant.PUBLIC_KEY);
//
//			try {
//				final OpenConfig oc = new OpenConfig(url, appId, secretKey, privateKeyPath, pwd, tlPublicKey);
//
//				OpenClientProxy client = new OpenClientProxy(oc, System.currentTimeMillis());
//				clientMap.put(shopId, client);
//				clientConfigMap.put(shopId, oc);
//				return client;
//			} catch (final Exception e) {
//				log.error("通联client启动失败:error {}",e);
//			}
//		}
		/** 1、检查当前配置是否启用 client配置信息会被停用or修改  这里需要检查状态*/
		// 获取缓存的client配置信息
		AllInPayYSTToken allInPayYSTToken = allInPayYSTTokenProvider.findToken(String.valueOf(shopId));
		if(allInPayYSTToken == null){
			log.error("获取不到商家的通联配置 {}", shopId);
			throw new RuntimeException("获取不到商家的通联配置");
		}
		// 页面停用支付配置 会维护一个空值对象
		if(allInPayYSTToken.getAppId() == null || allInPayYSTToken.getSecretKey() == null){
			log.error("通联配置信息不全 {}", shopId);
			throw new RuntimeException("通联配置信息不全或已停用");
		}

		/** 2、检查缓存是否存在 不存在则创建  存在则检查有效性 */
		OpenClientProxy openClientCache = clientMap.get(shopId);
		if(openClientCache != null){
			// 校验有效性
			if(isLatestClient(allInPayYSTToken, openClientCache)){
				return openClientCache;
			}
			// 刷新openClient
			return createOpenClient(shopId, allInPayYSTToken);
		}
		return createOpenClient(shopId, allInPayYSTToken);
	}

	private boolean isLatestClient(AllInPayYSTToken allInPayYSTToken, OpenClientProxy openClientCache) {
		// true : 未发生变更（配置的修改时间 <= client的创建时间）
		return allInPayYSTToken.getTokenLoadTime() <= openClientCache.getCreateTime();
	}

	private synchronized OpenClient createOpenClient(Long shopId, AllInPayYSTToken allInPayYSTToken) {
		OpenClientProxy openClientCache = clientMap.get(shopId);
		// 再查询 等待期间其他线程或许已经创建了client & 未发生变更（配置时间 <= client的创建时间）
		if(openClientCache != null && isLatestClient(allInPayYSTToken, openClientCache)){
			// 如果（缓存存在 并且 是最新的client）便返回cache
			return openClientCache;
		}

		try {
			final String url = configuration.getValue(AllinPayConfigConstant.URL);

			final OpenConfig oc = new OpenConfig(url, allInPayYSTToken.getAppId(), allInPayYSTToken.getSecretKey(), allInPayYSTToken.getCertPath(), allInPayYSTToken.getCertPassword(), allInPayYSTToken.getTlCertPath());
			OpenClientProxy client = new OpenClientProxy(oc, System.currentTimeMillis());
			clientMap.put(shopId, client);
			clientConfigMap.put(shopId, oc);

			log.info("通联OpenClient创建成功 {}", shopId);
			return client;
		}catch (Exception e){
			log.error("通联OpenClient创建失败 shopId: {} 配置信息: {}", shopId, JSONObject.toJSONString(allInPayYSTToken));
			log.error("通联OpenClient创建失败 异常堆栈", e);
			// 抛出异常，调用者屏蔽
			throw e;
		}
	}


//	if(177L == shopId){
//		final String url = configuration.getValue(AllinPayConfigConstant.URL);
//		final String appId = configuration.getValue(AllinPayConfigConstant.APPID);
//		final String secretKey = configuration.getValue(AllinPayConfigConstant.SECRET_KEY);
//		final String pwd = configuration.getValue(AllinPayConfigConstant.PWD);
//		final String privateKeyPath = configuration.getValue(AllinPayConfigConstant.PRIVATE_KEY_PATH);
//
//		final String tlPublicKey = configuration.getValue(AllinPayConfigConstant.PUBLIC_KEY);
//
//		try {
//			final OpenConfig oc = new OpenConfig(url, appId, secretKey, privateKeyPath, pwd, tlPublicKey);
//
//			OpenClientProxy client = new OpenClientProxy(oc, System.currentTimeMillis());
//			clientMap.put(shopId, client);
//			return client;
//		} catch (final Exception e) {
//			log.error("通联client启动失败:error {}",e);
//		}
//	}


	public static void main(String[] args) {
		String url = "https://open.allinpay.com/gateway";
		String appId = "1722826522273722369";
		String secretKey = "woHlJKj46cDC9MezN6ykZomwHlNdUSi8";
		String pwd = "xianzhio2o";
		String privateKeyPath = "F:\\codeup_supply\\mall\\showcase\\src\\main\\resources\\allinpay\\prod\\1722826522273722369(RSA2).pfx";
		String tlPublicKey = "F:\\codeup_supply\\mall\\showcase\\src\\main\\resources\\allinpay\\prod\\TLCert.cer";

		try {
			final OpenConfig oc = new OpenConfig(url, appId, secretKey, privateKeyPath, pwd, tlPublicKey);

			OpenClientProxy client = new OpenClientProxy(oc, System.currentTimeMillis());
			System.out.println("");
		} catch (final Exception e) {
			log.error("通联client启动失败:error {}",e);
		}
	}

	public OpenConfig getConfig(Long shopId){
		return clientConfigMap.get(shopId);
	}
}
