package moonstone.web.core.component.pay.allinpay.enums;

public enum AllInPaySignTypeEnum {
    RSA("RSA", ""),
    SM2("SM2", ""),
    ;

    private final String code;
    private final String description;

    AllInPaySignTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
