package moonstone.web.core.component.item;

import io.terminus.common.model.Response;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.utils.Translate;
import moonstone.countryImage.model.CountryImage;
import moonstone.countryImage.service.CountryImageReadService;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.web.core.component.item.model.WeShopItemPreview;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Component
public class WeShopItemPreviewPacker {
    @Autowired
    private CountryImageReadService countryImageReadService;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private SkuCustomReadService skuCustomReadService;

    /**
     * 为商品打包预览显示类
     *
     * @param item 商品
     * @return 显示类
     */
    public WeShopItemPreview wrapBaseAttributeFromItem(Item item) {
        WeShopItemPreview weShopItemPreview = new WeShopItemPreview();
        weShopItemPreview.setItemId(item.getId());
        weShopItemPreview.setItemName(item.getName());
        weShopItemPreview.setUnit(item.getMainImage());
        weShopItemPreview.setIsBonded(item.getIsBonded());
        weShopItemPreview.setType(Optional.ofNullable(item.getIsBonded()).filter(Predicate.isEqual(1))
                .map(one -> Translate.of("跨境保税")).orElse(Translate.of("国内贸易")));
        weShopItemPreview.setLowPrice(Optional.ofNullable(item.getLowPrice()).orElse(0).longValue());
        weShopItemPreview.setHighPrice(Optional.ofNullable(item.getHighPrice()).orElse(0).longValue());
        weShopItemPreview.setSuggestPrice(item.getHighPrice().longValue());
        skuCacheHolder.findSkusByItemId(item.getId()).stream().map(Sku::getExtraPrice).filter(Objects::nonNull)
                .map(extraPrice -> extraPrice.get("originPrice")).filter(Objects::nonNull)
                .findFirst().map(Integer::longValue).ifPresent(weShopItemPreview::setOriginPrice);
        weShopItemPreview.setUnit(Optional.ofNullable(item.getExtra()).orElseGet(HashMap::new).getOrDefault("unit", Translate.of("件")));
        weShopItemPreview.setUrl(item.getMainImage_());
        List<SkuCustom> skuCustomList = Optional.ofNullable(skuCustomReadService.findBySkuIds(skuCacheHolder.findSkusByItemId(item.getId()).stream().map(Sku::getId).collect(Collectors.toList()))
                .getResult()).orElseGet(ArrayList::new);
        skuCustomList.stream().filter(validate -> validate.getStatus() > 0).findFirst()
                .map(SkuCustom::getCustomTaxHolder)
                .ifPresent(weShopItemPreview::setCustomTaxHolder);
        Optional<CountryImage> countryImage = skuCustomList
                .stream().filter(skuCustom -> skuCustom.getCustomOriginId() != null)
                .map(SkuCustom::getCustomOriginId)
                .map(countryImageReadService::findByCountryId)
                .map(Response::getResult)
                .filter(Objects::nonNull)
                .findFirst();

        countryImage.map(CountryImage::getCountryName).ifPresent(weShopItemPreview::setNation);
        countryImage.map(CountryImage::getImageUrl).ifPresent(weShopItemPreview::setNationUrl);

        weShopItemPreview.setWeShopSkuPreviewList(new ArrayList<>());

        return weShopItemPreview;
    }

}
