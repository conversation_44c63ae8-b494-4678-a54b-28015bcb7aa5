package moonstone.web.core.component.integral;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.integral.constrant.IntegralActivityLimitType;
import moonstone.integral.dto.IntegralActivityRecord;
import moonstone.integral.model.IntegralActivity;
import moonstone.integral.service.IntegralActivityWriteService;
import moonstone.user.model.StoreIntegral;
import moonstone.user.service.StoreIntegralReadService;
import moonstone.user.service.StoreIntegralRecordReadService;
import moonstone.web.core.component.cache.IntegralActivityCacheHolder;
import moonstone.web.core.events.Integral.IntegralActivityUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;

@Service
@Slf4j
public class IntegralActivityManager {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IntegralActivityCacheHolder integralActivityCacheHolder;
    @Autowired
    private IntegralActivityWriteService integralActivityWriteService;

    @Autowired
    private StoreIntegralRecordReadService storeIntegralRecordReadService;
    @Autowired
    private StoreIntegralReadService storeIntegralReadService;

    /*
     public void deleteOldData() {
     Date thereYearAgo = new DateTime(LocalDate.now().getYear() - 3, LocalDate.now().getMonthValue(), LocalDate.now().getDayOfYear()
     , 0, 0).toDate();
     mongoTemplate.remove(new Query(Criteria.where("createdAt").lte(thereYearAgo)), IntegralActivityRecord.class);
     log.warn("{} remove the old data of there year ago [{}]", LogUtil.getClassMethodName(), IntegralActivityRecord.class);
     }
     */

    /**
     * 检测活动是否超时
     *
     * @param activity 活动
     * @return 是否有效
     */
    public boolean checkActivityValid(IntegralActivity activity) {
        if (activity.getLongTerm() != null && activity.getLongTerm()) return true;
        Date now = new Date();
        boolean inDate = !Optional.ofNullable(activity.getStartAt()).orElse(now).after(now) && !Optional.ofNullable(activity.getEndAt()).map(DateUtil::withTimeAtEndOfDay).orElse(now).before(now);
        if (inDate) return true;
        // 清理超时活动

        boolean outOfDate = Optional.ofNullable(activity.getEndAt()).orElse(now).before(now);
        if (outOfDate) {
            IntegralActivity updateOutOfDate = new IntegralActivity();
            updateOutOfDate.setId(activity.getId());
            updateOutOfDate.setStatus(0);
            integralActivityWriteService.update(updateOutOfDate);

            // 失效缓存
            EventSender.publish(new IntegralActivityUpdateEvent(activity.getId(), null, activity.getShopId()));
            integralActivityCacheHolder.invalidate(activity.getId(), activity.getShopId());
        }
        return false;
    }

    /**
     * 判断是否第一次访问这个活动
     * 会修改活动的参加次数
     *
     * @param integralActivity 活动 [id]
     * @param userId           用户Id
     * @return 是否第一次
     */
    public boolean tryFirstTrigger(IntegralActivity integralActivity, long userId) {
        if (integralActivity.getId() == null)
            throw new RuntimeException(new Translate("用户[%s]访问的积分活动Id必须不为空", userId).toString());
        if (!checkActivityValid(integralActivity)) return false;
        Query allow = Query.query(Criteria.where("userId").is(userId))
                .addCriteria(Criteria.where("activityId").is(integralActivity.getId()))
                .addCriteria(Criteria.where("time").gte(1));
        if (mongoTemplate.exists(allow, IntegralActivityRecord.class)) return false;
        Query query = Query.query(Criteria.where("userId").is(userId))
                .addCriteria(Criteria.where("activityId").is(integralActivity.getId()));
        Update update = new Update().inc("time", 1);
        if (!mongoTemplate.exists(query, IntegralActivityRecord.class)) {
            mongoTemplate.upsert(query, new Update().currentDate("createdAt"), IntegralActivityRecord.class);
        }
        int time = Optional.ofNullable(mongoTemplate.findAndModify(query, update, IntegralActivityRecord.class)).map(IntegralActivityRecord::getTime).orElse(0);
        if (time == 0) {
            mongoTemplate.updateFirst(query, new Update().currentDate("operatedAt"), IntegralActivityRecord.class);
            return true;
        }
        return false;
    }

    /**
     * 检测活动的次数限制,每天限制n次
     * 会修改活动的参加次数
     *
     * @param activity 活动
     * @param userId   用户Id
     * @return 是否允许
     */
    public boolean checkLimitByDay(IntegralActivity activity, long userId) {
        if (!checkActivityValid(activity)) return false;
        if (activity.getLimit() == null) return true;
        long nowEpochDay = LocalDate.now().toEpochDay();
        int time = modifyTime(userId, activity.getId(), nowEpochDay, 1);
        switch (IntegralActivityLimitType.from(activity.getExtra().getOrDefault(IntegralActivityLimitType.limitType.name(), ""))) {
            case day:
                if (time >= activity.getLimit()) {
                    modifyTime(userId, activity.getId(), nowEpochDay, -1);
                    return false;
                }
                return true;
            case first:
                modifyTime(userId, activity.getId(), nowEpochDay, -1);
                return false;
            case noCheck:
                modifyTime(userId, activity.getId(), nowEpochDay, -1);
                return true;
            default:
                modifyTime(userId, activity.getId(), nowEpochDay, -1);
                throw new RuntimeException(new Translate("未支持的次数检测方式[%s]", activity.getExtra().get(IntegralActivityLimitType.limitType.name())).toString());
        }
    }

    /**
     * 修改指定用户知道日期对某个活动成功的次数
     *
     * @param userId      用户Id
     * @param activityId  活动Id
     * @param nowEpochDay 日期
     * @param i           次数
     * @return 以前的次数, 首次为0
     */
    private int modifyTime(long userId, Long activityId, long nowEpochDay, int i) {
        Query query = Query.query(Criteria.where("userId").is(userId))
                .addCriteria(Criteria.where("activityId").is(activityId == null ? "null" : activityId))
                .addCriteria(Criteria.where("nowEpochDay").is(nowEpochDay));
        Update update = new Update().inc("time", i).currentDate("operatedAt");
        if (!mongoTemplate.exists(query, IntegralActivityRecord.class))
            mongoTemplate.upsert(query, new Update().currentDate("createdAt"), IntegralActivityRecord.class);
        return Optional.ofNullable(mongoTemplate.findAndModify(query, update, IntegralActivityRecord.class).getTime()).orElse(0);
    }

    /**
     * 拉去店铺的第一次积分扫描活动
     *
     * @param shopId 店铺Id
     * @param userId 用户Id   目前没用,但是可能用于判断积分什么的
     * @return 首次扫码活动
     */
    public Optional<IntegralActivity> findFirstActivity(long shopId, long userId) {
        List<IntegralActivity> activityList = integralActivityCacheHolder.findByShopId(shopId).orElse(new ArrayList<>());
        Date now = new Date();
        return activityList.stream().filter(activity -> Objects.equals(-1, activity.getLimit()))
                .filter(activity -> IntegralActivityLimitType.from(activity.getExtra().getOrDefault(IntegralActivityLimitType.limitType.name(), "")) == IntegralActivityLimitType.first)
                .filter(this::checkActivityValid)
                .findFirst();
    }

    /**
     * 标记所有有积分扫描的用户在某个活动已经参加过一次
     *
     * @param shopId     店铺Id
     * @param activityId 活动Id
     * @return 影响数量
     */
    public long markThemAsHaveTriggerActivity_M(long shopId, long activityId) {
        List<StoreIntegral> storeIntegralList = storeIntegralReadService.findByShopId(shopId).getResult();
        if (CollectionUtils.isEmpty(storeIntegralList)) return 0L;
        long n = 0;
        Update update = new Update().inc("time", 1);
        for (StoreIntegral storeIntegral : storeIntegralList) {
            boolean haveScan = storeIntegralRecordReadService.countByOriginAndIntegralId(0, storeIntegral.getId()).orElse(0) > 0;
            if (!haveScan) continue;
            try {
                Query query = Query.query(Criteria.where("activityId").is(activityId)).addCriteria(Criteria.where("userId").is(storeIntegral.getUserId()));
                mongoTemplate.upsert(query, update, IntegralActivityRecord.class);
                n++;
            } catch (Exception ex) {
                log.error("{} fail to trigger the marker shopId [{}] activityId[{}] storeIntegral [{}]", LogUtil.getClassMethodName(), shopId, activityId, storeIntegral, ex);
            }
        }
        return n;
    }

    /**
     * 检测是否为第一次访问这个活动
     *
     * @param activity 活动
     * @param userId   用户Id
     * @return 是否第一次
     */
    public boolean isFirstTrigger(IntegralActivity activity, Long userId) {
        if (activity == null || activity.getId() == null)
            throw new RuntimeException(new Translate("用户[%s]访问的积分活动Id必须不为空", userId).toString());
        if (!checkActivityValid(activity)) return false;
        Query allow = Query.query(Criteria.where("userId").is(userId))
                .addCriteria(Criteria.where("activityId").is(activity.getId()))
                .addCriteria(Criteria.where("time").gte(1));
        return !mongoTemplate.exists(allow, IntegralActivityRecord.class);
    }
}
