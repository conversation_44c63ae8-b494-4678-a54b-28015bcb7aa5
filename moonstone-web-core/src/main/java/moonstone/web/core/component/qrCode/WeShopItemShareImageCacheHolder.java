package moonstone.web.core.component.qrCode;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.*;
import moonstone.common.enums.BondedType;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.component.wx.WxAccessTokenCacheHolder;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.validation.constraints.NotNull;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeShopItemShareImageCacheHolder {
    final Function<Long, String> page = weShopItemId -> "pages/design/index";

    @Autowired
    private ShopWxaProjectReadService shopWxaProjectReadService;
    @Autowired
    private ShopWxaReadService shopWxaReadService;

    @Autowired
    private WeShopItemCacher weShopItemCacher;
    @Autowired
    private ItemCacheHolder itemCacheHolder;
    @Autowired
    private WeShopSkuCacheHolder weShopSkuCacheHolder;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private WeShopSkuReadService weShopSkuReadService;
    @Autowired
    private WxAccessTokenCacheHolder wxAccessTokenCacheHolder;
    @Autowired
    private SourceShopQuerySlice sourceShopQuerySlice;
    @Autowired
    private SkuCustomReadService skuCustomReadService;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    LoadingCache<String, LoadingCache<Long, BufferedImage>> imageCacheForWeShopItem = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(appConfig -> Caffeine.newBuilder()
                    .expireAfterWrite(2, TimeUnit.HOURS)
                    .maximumSize(1000)
                    .build(getShareImageFromWeShopItemId(appConfig)));

    LoadingCache<String, LoadingCache<Long, BufferedImage>> imageCacheForWeShop = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(appConfig -> Caffeine.newBuilder()
                    .expireAfterWrite(2, TimeUnit.HOURS)
                    .maximumSize(1000)
                    .build(getShareImageForWeShop(appConfig)));

    public void invalidateForWeShopItemId(Long weShopItemId) {
        imageCacheForWeShop.asMap().forEach((config, cache) -> cache.invalidate(weShopItemId));
        imageCacheForWeShopItem.asMap().forEach((config, cache) -> cache.invalidate(weShopItemId));
        imageCacheForWeShopItem.invalidateAll();
    }

    public BufferedImage createWeShopShareImage(Long weShopId, Long projectId) {
        String appConfig = Objects.isNull(projectId) ? "" : JsonObject.mapFrom(shopWxaReadService.findById(shopWxaProjectReadService.findById(projectId).getResult().getShopWxaId()).getResult()).toString();
        return Objects.requireNonNull(imageCacheForWeShop.get(appConfig)).get(weShopId);
    }

    CacheLoader<Long, BufferedImage> getShareImageForWeShop(String appConfig) {
        return weShopId -> {
            WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseGet(WeShop::new);

            String appId = "wx90e48a315747ccb9";
            String appSecret = "ebb83ced7885897e4fd5ecfc403ed92d";
            if (weShop.getAppId() != null) {
                appId = weShop.getAppId();
                appSecret = weShop.getSecret();
            }
            if (!appConfig.isEmpty()) {
                ShopWxa shopWxa = new JsonObject(appConfig).mapTo(ShopWxa.class);
                appId = shopWxa.getAppId();
                appSecret = shopWxa.getAppSecret();
            }

            String accessToken = wxAccessTokenCacheHolder.getAccessToken(appId, appSecret).take();
            String accessTokenGainUrlApi = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
            Map<String, String> args = ImmutableMap.of("page", "pages/design/index", "scene", "weShopId=" + weShopId, "width", "280");
            HttpRequest request = HttpRequest.post(accessTokenGainUrlApi).contentType("application/json", "utf-8").send(JSON.toJSONBytes(args));
            if (!request.ok()) {
                return null;
            }
            BufferedImage bufferedImage = null;
            BufferedImage finger = null;
            ByteArrayOutputStream buff = new ByteArrayOutputStream();
            try {
                request.receive(buff);
                bufferedImage = ImageIO.read(new ByteArrayInputStream(buff.toByteArray()));
                var fp = getClass().getResourceAsStream("/指纹.png");
                if (fp != null) {
                    finger = ImageIO.read(fp);
                }else {
                    fp = getClass().getResourceAsStream("/fp.png");
                }
                if (fp != null) {
                    finger = ImageIO.read(fp);
                }
            } catch (Exception ex) {
                log.error("{} fail to create shopShare image", LogUtil.getClassMethodName(), ex);
            }
            if (bufferedImage == null) {
                log.error("{} fail to receive the image from weShop[appId => {}] cause[{}]", LogUtil.getClassMethodName(), appId, buff);
            }
            return ShareImageUtil.createShopShareImage(weShop.getName(), "扫描或长按识别二维码，查看线上商城", bufferedImage, finger);
        };
    }

    public BufferedImage getShareImageForWeShopItemId(Long weShopItemId, Long projectId) {
        String appConfig = Objects.isNull(projectId) ? "" : JsonObject.mapFrom(shopWxaReadService.findById(shopWxaProjectReadService.findById(projectId).getResult().getShopWxaId()).getResult()).toString();
        return Objects.requireNonNull(imageCacheForWeShopItem.get(appConfig)).get(weShopItemId);
    }

    CacheLoader<Long, BufferedImage> getShareImageFromWeShopItemId(String appConfig) {
        return (weShopItemId) -> {
            WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
            Item item = itemCacheHolder.findItemById(weShopItem.getItemId());
            log.debug("{} weShopItem[{}], item[{}]", LogUtil.getClassMethodName(), Json.toJson(weShopItem), item == null ? "null=>" + appConfig : Json.toJson(item));
            List<WeShopSku> weShopSkus = weShopSkuReadService.findByWeShopItemId(weShopItemId).orElseGet(ArrayList::new);
            List<Sku> skuList = weShopSkus.isEmpty() ? skuCacheHolder.findSkusByItemId(Objects.requireNonNull(item).getId()) :
                    weShopSkus.stream().map(WeShopSku::getSkuId).map(skuCacheHolder::findSkuById).filter(Objects::nonNull).collect(Collectors.toList());
            for (Sku sku : skuList) {
                Optional<WeShopSku> weShopSkuOpt = weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShopItem.getWeShopId(), sku.getId());
                if (!weShopSkuOpt.isPresent()) {
                    continue;
                }
                log.debug("{} weShopSku[{}]", LogUtil.getClassMethodName(), Json.toJson(weShopSkuOpt.get()));
                long finalPrice = weShopSkuOpt.get().isPriceSet() ? weShopSkuOpt.map(WeShopSku::getPrice).orElseGet(() -> sku.getPrice() + Optional.ofNullable(weShopSkuOpt.get().getDiffPrice()).orElse(0L)) : sku.getPrice().longValue();
                try {
                    BigDecimal price = new BigDecimal(finalPrice).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
                    if (sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).orElse(-1L).equals(sku.getShopId())) {
                        return ShareImageUtil.createItemShareImageForGongXiao(item.getMainImage_(), qrCodeImageQueryFor(appConfig, weShopItemId, page.apply(weShopItemId)), item.getName(),
                                price,
                                BondedType.GENERAL_TRADE.getCode().equals(item.getIsBonded()) ? "中国"
                                        : Optional.ofNullable(skuCustomReadService.findBySkuId(sku.getId())).map(SkuCustom::getCustomOrigin).orElse("海外优品")
                                , "扫描或长按识别二维码");
                    } else {
                        return ShareImageUtil.createItemShareImage(item.getMainImage_(), qrCodeImageQueryFor(appConfig, weShopItemId, page.apply(weShopItemId)), item.getName(), price, "扫描或长按识别二维码");
                    }
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
            throw new JsonResponseException(Translate.of("错误"));
        };
    }

    private ShopWxa getShopWxaFromWeShopItemId(Long weShopItemId) {
        WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopItem.getWeShopId()).orElseGet(WeShop::new);
        if (weShop.getAppId() == null) {
            ShopWxa shopWxa = new ShopWxa();
            shopWxa.setAppId("wx90e48a315747ccb9");
            shopWxa.setAppSecret("ebb83ced7885897e4fd5ecfc403ed92d");
            return shopWxa;
        }
        ShopWxa shopWxa = new ShopWxa();
        shopWxa.setAppId(weShop.getAppId());
        shopWxa.setAppSecret(weShop.getSecret());
        return shopWxa;
    }

    private BufferedImage qrCodeImageQueryFor(@NotNull String appConfig, Long weShopItemId, String page) throws IOException {
        ShopWxa shopWxa = appConfig.isEmpty() ? getShopWxaFromWeShopItemId(weShopItemId) : new JsonObject(appConfig).mapTo(ShopWxa.class);
        String accessToken = wxAccessTokenCacheHolder.getAccessToken(shopWxa.getAppId(), shopWxa.getAppSecret()).take();
        String getWXJumpImageApiUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
        WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
        Map<String, String> args = ImmutableMap.of("page", "pages/item/item", "scene", String.format("weShopId=%s,weItemId=%s", weShopItem.getWeShopId(), weShopItem.getId()), "width", "280");
        HttpRequest request = HttpRequest.post(getWXJumpImageApiUrl).contentType("application/json", "utf-8").send(JSON.toJSONBytes(args));
        if (request.ok()) {
            return ImageIO.read(request.stream());
        }
        throw new JsonResponseException(Translate.of("获取微信分享图片失败"));
    }
}
