package moonstone.web.core.component.profit.dto;

import lombok.Data;
import moonstone.common.model.IsPresent;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;
import java.util.List;

@Data
public class OrderProfitRecord implements IsPresent {
    String _id;
    // 订单名称
    String orderName;
    // 店铺Id
    Long shopId;
    // 订单Id
    @Indexed
    Long orderId;
    // 关联Id -> weShop=>userId 或者 代理人Id
    @Indexed
    Long referenceId;
    // 订单内商品列表
    List<OrderProfitItemRecord> orderProfitRecordList;
    // 商品出售价格
    Long orderSellPrice;
    // 买家名称
    String buyerName;
    // 买家Id
    Long buyerId;
    // 订单OutFrom
    @Indexed
    String outFrom;
    // 订单来源 如某某门店
    String orderFrom;
    // 订单创建时间
    Date orderAt;

    // 受益人Id
    @Indexed
    Long ownerId;
    // 利润来源Id
    Long sourceId;
    // 利润金额
    Long profit;
    // 利润状态 -1退款 1待收益 2已收益
    Integer profitStatus;
    // 待收益Id
    Long foreseeProfitId;
    // 已收益Id
    Long earnedProfitId;
    // 确定收益时间
    Date earnedAt;
    // 利润类型
    Integer profitType;

    // 数据修改时间
    Date updatedAt;
    // 数据创建时间
    Date createdAt;

    @Override
    public boolean isPresent() {
        return (getProfitType() & presentMaskBit.MASK_BIT.getValue()) == presentMaskBit.Present.getValue();
    }
}
