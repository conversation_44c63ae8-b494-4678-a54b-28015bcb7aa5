package moonstone.web.core.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.cache.UserWxCacheHolder;
import moonstone.web.core.component.wx.WxMsgTemplates;
import moonstone.web.core.component.wx.WxSubscribeMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@Component
public class WxSubscribeMsgSender {
    @Autowired
    private WxMsgTemplates wxMsgTemplates;
    @Autowired
    private ShopWxaCacheHolder shopWxaCacheHolder;
    @Autowired
    private UserWxCacheHolder userWxCacheHolder;

    public Either<Boolean> send(WxSubscribeMsg wxSubscribeMsg) {
        String accessToken;
        String appId = wxSubscribeMsg.getAppId();
        String secret;
        ShopWxa shopWxa;
        if (wxSubscribeMsg.getShopId() != null) {
            shopWxa = shopWxaCacheHolder.findReleaseOneForShopId(wxSubscribeMsg.getShopId());
            appId = shopWxa.getAppId();
        } else {
            shopWxa = shopWxaCacheHolder.findByAppId(appId);
        }
        secret = shopWxa.getAppSecret();
        accessToken = wxMsgTemplates.getAccessToken(appId, secret).take();
        String openId = wxSubscribeMsg.getOpenId();
        if (wxSubscribeMsg.getOpenId() == null) {
            openId = userWxCacheHolder.findByUserIdAndAppId(wxSubscribeMsg.getUserId(), appId).getOpenId();
        }
        MsgArg msgArg = new MsgArg();
        msgArg.setTouser(openId);
        msgArg.setAccess_token(accessToken);
        msgArg.setData(wxSubscribeMsg.getData());
        msgArg.setTemplate_id(wxSubscribeMsg.getTemplate_id());
        msgArg.setPage(wxSubscribeMsg.getPage());
        HttpRequest httpRequest = HttpRequest.post(wxMsgTemplates.wxMsgBackendUrl.concat(String.format("/message/subscribe/send?access_token=%s", accessToken)))
                .contentType(HttpRequest.CONTENT_TYPE_JSON)
                .send(JSON.toJSONBytes(msgArg));
        if (httpRequest.ok()) {
            JSONObject resJson = JSONObject.parseObject(httpRequest.body());
            log.debug("{} Wx-Subscribe msg result [{}]", LogUtil.getClassMethodName(), resJson);
            if (resJson.getInteger("errcode") != 0) {
                log.error("{} fail to send msg, res => {}", LogUtil.getClassMethodName(), resJson.toJSONString());
                return Either.error(resJson.getString("errMsg"));
            }
            return Either.ok(true);
        }
        return Either.error("http.code." + httpRequest.code());
    }

    public Set<String> getSupportedAppId() {
        return new HashSet<>(Collections.singletonList("wx742a0012cc701d79"));
    }

    @Data
    private static class MsgArg {
        String access_token;
        String touser;
        String template_id;
        String page;
        Object data;
    }
}
