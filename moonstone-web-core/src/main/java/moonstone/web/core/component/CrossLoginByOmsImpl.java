package moonstone.web.core.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.thirdParty.service.ThirdPartyUserShopWriteService;
import moonstone.user.cache.ThirdPartyUserCache;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.model.User;
import moonstone.user.service.ThirdPartyUserWriteService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.api.CrossLoginService;
import moonstone.web.core.component.api.OmsUserSync;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import moonstone.web.core.config.JwtConfig;
import moonstone.web.core.events.ThirdPartyUserUpdateEvent;
import moonstone.web.core.events.thirdParty.ThirdPartySynchronizeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.spec.SecretKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * cross login from oms, if other cross login is set
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CrossLoginByOmsImpl implements CrossLoginService {
    @Autowired
    UserReadService<User> userReadService;
    @Autowired
    ThirdPartyUserShopWriteService thirdPartyUserShopWriteService;
    @Autowired
    ThirdPartyUserShopReadService thirdPartyUserShopReadService;
    @Autowired
    ThirdPartyUserWriteService thirdPartyUserWriteService;
    @Autowired
    OmsUserSync omsUserSync;
    @Autowired
    OmsUserDetailQueryComponent omsUserDetailQueryComponent;
    @Autowired
    OmsAccessCodeComponent omsAccessCodeComponent;
    @Autowired
    OmsUserIdQueryHelper omsUserIdQueryHelper;
    @Autowired
    ParanaUserWrapper paranaUserWrapper;
    @Autowired
    ShopCacheHolder shopCacheHolder;
    @Autowired
    ThirdPartyUserCache thirdPartyUserCache;
    @Autowired
    ThirdPartyUserShopCache thirdPartyUserShopCache;
    @Autowired
    JwtConfig jwtConfig;

    Executor executor = new ForkJoinPool();
    Gson gson = new Gson();

    /**
     * if out user not exists sync the user shop information
     * sync user and thirdPartyStock async if user exists
     *
     * @param jws the token carry data
     * @return paranaUser
     */
    @Override
    public Either<CommonUser> crossLogin(Jws<Claims> jws) {
        // we all know the the struct
        try {
            JsonObject omsJwtUser = gson.fromJson(gson.toJson(jws.getBody().get(ClaimsIndex.user.name())), JsonObject.class);
            ThirdPartyUser thirdPartyUser = syncUser(omsJwtUser.get("userId").getAsLong() + "", null, omsJwtUser.get("userName").getAsString()).take();
            return Either.ok(paranaUserWrapper.wrap(thirdPartyUser.getUserId(), null));
        } catch (Exception exception) {
            log.error("{} fail to cross login from oms[JWT => {}]",
                    LogUtil.getClassMethodName(), jws.toString(), exception);
            return Either.error(Translate.of("获取用户信息失败"));
        }
    }

    private Either<ThirdPartyUser> createThirdPartyUser(Long userId, Long omsUserId) {
        try {
            ThirdPartyUser thirdPartyUser = new ThirdPartyUser();
            thirdPartyUser.setType(ThirdPartyUserType.OMS.getType());
            thirdPartyUser.setUserId(userId);
            thirdPartyUser.setThirdPartyId(omsUserId.toString());
            thirdPartyUserWriteService.create(thirdPartyUser);
            thirdPartyUserCache.invalidate(omsUserId.toString());
            EventSender.publish(new ThirdPartyUserUpdateEvent(omsUserId.toString()));
            return Either.ok(thirdPartyUser);
        } catch (Exception ex) {
            return Either.error(ex);
        }
    }

    @Override
    public Either<String> crossLogin(Long userId) {
        try {
            Long omsUserId = omsUserIdQueryHelper.queryOmsUserId(userId);
            OmsJwtUser omsJwtUser = new OmsJwtUser();
            omsJwtUser.setUserId(omsUserId);
            Container container = new Container(omsJwtUser);
            return Either.ok(Jwts.builder().signWith(new SecretKeySpec(jwtConfig.getJwtKey().getBytes(), jwtConfig.getDefaultAlg().getJcaName()), jwtConfig.getDefaultAlg())
                    .setIssuer(jwtConfig.getSIGNER())
                    .setIssuedAt(new Date())
                    .setExpiration(Date.from(LocalDateTime.now().plusHours(9).atZone(ZoneId.systemDefault()).toInstant()))
                    .addClaims(JSONObject.parseObject(JSON.toJSONString(container))).compact());
        } catch (Exception exception) {
            log.error("{} fail to cross login for userId[{}]", LogUtil.getClassMethodName(), userId, exception);
            return Either.error(Translate.of("交叉登录失败"));
        }
    }

    /**
     * sync the thirdPartyUserShop
     *
     * @param userName  用户名称
     * @param outUserId 用户中心数据
     * @param userId    userId
     */
    private void syncUserShop(String userName, Long outUserId, Long userId) {
        if (Objects.isNull(shopCacheHolder.findShopByUserId(userId))) {
            omsUserSync.markOuterUserAsShop(outUserId.toString(), userName, new ArrayList<>());
        }
        List<ThirdPartyUserShop> thirdPartyUserShops = queryOuterThirdPartyUserShop(outUserId);
        if (thirdPartyUserShops.isEmpty()) {
            return;
        }
        Long shopId = Objects.requireNonNull(shopCacheHolder.findShopByUserId(userId)).getId();
        ThirdPartyUserShop first = thirdPartyUserShops.get(0);
        first.setShopId(shopId);
        Optional<ThirdPartyUserShop> thirdPartyUserShop = thirdPartyUserShopCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V2.Id(), shopId));
        if (!thirdPartyUserShop.isPresent() || !first.getThirdPartyCode().equals(thirdPartyUserShop.get().getThirdPartyCode())) {
            ThirdPartyUserShop createOrUpdate = thirdPartyUserShop.orElseGet(ThirdPartyUserShop::new);
            createOrUpdate.setShopId(shopId);
            createOrUpdate.setThirdPartyCode(first.getThirdPartyCode());
            createOrUpdate.setThirdPartyId(ThirdPartySystem.Y800_V2.Id());
            thirdPartyUserShopWriteService.bind(createOrUpdate);
            thirdPartyUserShopCache.invalidate(new ThirdSystemAID(ThirdPartySystem.Y800_V2.Id(), shopId));
            thirdPartyUserShopCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V2.Id(), shopId))
                    .ifPresent(syncAccount -> EventSender.send(new ThirdPartySynchronizeEvent(ThirdPartySystem.Y800_V2, syncAccount)));
        }
    }

    @Override
    public Either<ThirdPartyUser> syncUser(String outUserId, String mobile, String name) {
        Long userId;
        Long userCenterId = Long.parseLong(outUserId);
        Optional<ThirdPartyUser> thirdPartyUser = thirdPartyUserCache.findThirdPartyUser(ThirdPartyUserType.OMS.getType(), userCenterId.toString());
        if (!thirdPartyUser.isPresent()) {
            OmsUserDetailQueryComponent.OmsUserDetail omsUserDetail = omsUserDetailQueryComponent.queryUserDetail(userCenterId).take();
            Objects.requireNonNull(omsUserDetail.getUserId());
            List<ThirdPartyUserShop> thirdPartyUserShops = queryOuterThirdPartyUserShop(userCenterId);
            for (ThirdPartyUserShop thirdPartyUserShop : thirdPartyUserShops) {
                List<ThirdPartyUserShop> match = thirdPartyUserShopReadService.findByAccessCode(thirdPartyUserShop.getThirdPartyCode()).getResult();
                if (Objects.isNull(match)) {
                    continue;
                }
                if (match.size() == 1) {
                    Long shopUserId = shopCacheHolder.findShopById(match.get(0).getShopId()).getUserId();
                    return createThirdPartyUser(shopUserId, userCenterId);
                }
            }
            User user = userReadService.findByMobile(omsUserDetail.getMobile()).getResult();
            if (Objects.nonNull(user)) {
                return createThirdPartyUser(user.getId(), userCenterId);
            }
            userId = omsUserSync.syncTheUser(omsUserDetail.getUserId().toString(), omsUserDetail.getMobile(), omsUserDetail.getUserName());
            if (Objects.isNull(shopCacheHolder.findShopByUserId(userId))) {
                omsUserSync.markOuterUserAsShop(omsUserDetail.getUserId().toString(), omsUserDetail.getUserName(), thirdPartyUserShops);
            }
        } else {
            userId = thirdPartyUser.get().getUserId();
        }
        // sync the shop
        try {
            executor.execute(() -> syncUserShop(name, Long.parseLong(outUserId), userId));
        } catch (Exception ignore) {
            // ignore the thread is reject
        }
        return Either.ok(thirdPartyUser
                .orElseGet(() -> thirdPartyUserCache.findThirdPartyUser(ThirdPartyUserType.OMS.getType(), outUserId).orElseThrow(() -> Translate.exceptionOf("数据同步失败"))));
    }

    /**
     * 查询用户的OMS的外部accessCode
     *
     * @param omsUserId oms的用户Id
     * @return 用户信息
     */
    private List<ThirdPartyUserShop> queryOuterThirdPartyUserShop(Long omsUserId) {
        return omsAccessCodeComponent.getAccessCode(omsUserId).orElseGet(ArrayList::new)
                .stream()
                .map(accessCode -> {
                    ThirdPartyUserShop thirdPartyUserShop = new ThirdPartyUserShop();
                    thirdPartyUserShop.setThirdPartyId(ThirdPartySystem.Y800_V2.Id());
                    thirdPartyUserShop.setThirdPartyCode(accessCode);
                    log.debug("{} System Sync Shop-AccessCode from OMS [{}] Code => {}", LogUtil.getClassMethodName(), omsUserId, accessCode);
                    return thirdPartyUserShop;
                }).collect(Collectors.toList());
    }

    @Data
    public static class OmsJwtUser {
        Long userId;
        String userName;
        Boolean supperUser;
        List<Object> roles;
        String system;
        LocalDateTime loginTime;
        LocalDateTime validTime;
        LocalDateTime refreshTime;
        Map<String, Object> extend;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Container {
        OmsJwtUser user;
    }
}
