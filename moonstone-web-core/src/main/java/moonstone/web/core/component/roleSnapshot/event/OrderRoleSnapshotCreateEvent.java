package moonstone.web.core.component.roleSnapshot.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderRoleSnapshotCreateEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = -6490677548461320388L;
    
    /**
     * 单据id
     */
    private Long orderId;

    /**
     * 单据类型
     */
    private OrderRoleSnapshotOrderTypeEnum orderType;
}
