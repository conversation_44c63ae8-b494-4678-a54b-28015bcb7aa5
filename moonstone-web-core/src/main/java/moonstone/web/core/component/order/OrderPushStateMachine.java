package moonstone.web.core.component.order;

import moonstone.order.model.SkuOrder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class OrderPushStateMachine {

    /**
     * 依赖自动采购单机制
     *
     * @param skuOrderList 子订单
     * @return 需要采购单
     */
    public boolean needGather(List<SkuOrder> skuOrderList) {
        for (SkuOrder skuOrder : skuOrderList) {
            if (Objects.nonNull(skuOrder.getGatherOrderId()))
                return true;
        }
        return false;
    }
}
