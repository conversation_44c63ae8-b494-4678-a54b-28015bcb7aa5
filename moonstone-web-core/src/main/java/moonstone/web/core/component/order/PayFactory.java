package moonstone.web.core.component.order;

import com.alibaba.fastjson.JSON;
import com.danding.common.net.Response;
import com.danding.mercury.pay.sdk.MercuryPayClient;
import com.danding.mercury.pay.sdk.trade.create.MercuryPayTradeCreateModel;
import com.danding.mercury.pay.sdk.trade.create.MercuryPayTradeCreateRequest;
import com.danding.mercury.pay.sdk.trade.create.MercuryPayTradeCreateResult;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.model.PaymentParams;
import io.terminus.pay.model.Redirect;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.service.PayChannel;
import io.terminus.pay.util.URLUtil;
import io.terminus.pay.wechatpay.common.model.token.JsapiWxToken;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.*;
import moonstone.order.component.PaymentAccountUtil;
import moonstone.order.dto.fsm.FlowBook;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.PaymentWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxReadService;
import moonstone.weShop.model.WeShop;
import moonstone.web.core.order.api.PaymentParamsMaker;
import moonstone.web.core.order.dto.PayParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * pay factory
 * @apiNote 重构准备
 */
@Component
@Slf4j
public class PayFactory {

    @Value("${mercury.pay.host}")
    String mercuryPayHost;

    @Value("${mercury.pay.appCode}")
    String mercuryPayAppCode;

    @Value("${mercury.pay.merchantCode}")
    String mercuryPayMerchantCode;

    @Value("${pay.notifyUrl}")
    String payNotifyUrl;

    @Value("${pay.returnUrl}")
    String returnUrl;

    @Autowired
    PaymentLogic paymentLogic;

    @Autowired
    IntegralPayFactory integralPayFactory;

    @Autowired
    PaymentWriteService paymentWriteService;

    @Autowired
    ChannelRegistry channelRegistry;

    @Autowired
    PaymentParamsMaker paymentParamsMaker;

    @Autowired
    ShopOrderReadService shopOrderReadService;

    @Autowired
    TokenProvider<JsapiWxToken> jsapiWxTokenTokenProvider;
    @Autowired
    WeShopCacheHolder weShopCacheHolder;

    @Resource
    private UserWxReadService userWxReadService;

    public MercuryPayTradeCreateResult preMercuryPay(PayParams payParams) {
        OrderLevel orderLevel = OrderLevel.fromInt(payParams.getOrderType());
        Payment payment = paymentLogic.prePay(payParams.getChannel(), payParams.getPromotionId(),
                payParams.getOrderIds(), orderLevel, OrderEvent.PAY.toOrderOperation(), 0L);

        PaymentParams params = paymentParamsMaker.makeParams(payment);
        if (!ObjectUtils.isEmpty(payParams.getAppId())) {
            params.setAppId(payParams.getAppId());
        }

        MercuryPayClient client = new MercuryPayClient(mercuryPayHost + "/api",
                mercuryPayAppCode, mercuryPayMerchantCode);
        MercuryPayTradeCreateRequest request = new MercuryPayTradeCreateRequest();
        MercuryPayTradeCreateModel model = new MercuryPayTradeCreateModel();
        model.setOutOrderNo(params.getTradeNo());
        if (payment.getChannel().contains(PayChannelName.alipay.toString())) {
            model.setPayChannel(PayChannelName.alipay.toString());
            if (payment.getChannel().contains(PayChannelName.pc.toString())) {
                model.setPayType(PayChannelName.pc.toString());
            } else if (payment.getChannel().contains(PayChannelName.app.toString())) {
                model.setPayType(PayChannelName.app.toString());
            } else if (payment.getChannel().contains(PayChannelName.wap.toString())) {
                model.setPayType(PayChannelName.wap.toString());
            }
        } else if (payment.getChannel().contains(PayChannelName.wechat.toString())) {
            model.setPayChannel(PayChannelName.wechatpay.toString());
            if (payment.getChannel().contains(PayChannelName.jsapi.toString())) {
                model.setPayType(PayChannelName.jsapi.toString());
            } else if (payment.getChannel().contains(PayChannelName.app.toString())) {
                model.setPayType(PayChannelName.app.toString());
            } else if (payment.getChannel().contains(PayChannelName.qr.toString())) {
                model.setPayType(PayChannelName.Native.toString());
            } else if (payment.getChannel().contains(PayChannelName.wap.toString())) {
                model.setPayType(PayChannelName.wap.toString());
            }
        }
        String notifyUrl = URLUtil
                .backNofityUrl(payNotifyUrl, payment.getChannel(), params.getSellerNo()) + "/new";
        String returnUrl = URLUtil.addDomain(this.returnUrl, params.getDomain());
        request.setNotifyUrl(notifyUrl);
        request.setReturnUrl(returnUrl);
        model.setSubject(params.getSubject());
        model.setOpenId("222");
        model.setPayAppId(params.getAppId());
        model.setTotalAmount(new BigDecimal(params.getFee()).divide(new BigDecimal(100), 2, RoundingMode.DOWN).toString());
        request.setBizModel(model);
        Response<?> response = client.execute(request);
        if (response.isSuccess()) {
            log.error("response={}", JSON.toJSONString(response));
            MercuryPayTradeCreateResult result = (MercuryPayTradeCreateResult) response.getResult();
            if (result.isSuccess()) {
                payment.setPayRequest(result.getResult().getRequestMessage());
                paymentWriteService.update(payment);
                return result;
            } else {
                log.error("error={},desc={}", response.getError(), response.getErrorMessage());
                throw new JsonResponseException(response.getError());
            }
        } else {
            log.error("error={},desc={}", response.getError(), response.getErrorMessage());
            throw new JsonResponseException(response.getError());
        }

    }

    /**
     * 创建支付单
     *
     * @param payParams 支付参数
     * @return 使用支付中间件后获取的支付结果
     */
    public TradeRequest prePay(PayParams payParams) {
        OrderLevel orderLevel = OrderLevel.fromInt(payParams.getOrderType());
        log.info("PayFactory.prePay, orderIds={}, 开始构建支付单", JSON.toJSONString(payParams.getOrderIds()));
        Payment payment = paymentLogic.prePay(payParams.getChannel(), payParams.getPromotionId(),
                payParams.getOrderIds(), orderLevel, OrderEvent.PAY.toOrderOperation(), 0L);
        log.info("PayFactory.prePay, orderIds={}, 完成构建支付单, 支付单id={}", JSON.toJSONString(payParams.getOrderIds()), payment.getId());

        if (Objects.nonNull(payment.getStatus())) {
            if (payment.getStatus() == OrderStatus.PAID.getValue()) {
                return TradeRequest.fail(Translate.of("支付单已经完成支付, 请勿重复支付"));
            }
            if (!FlowBook.ONLINE_PAY_FIRST.getFlow().operationAllowed(payment.getStatus(), OrderEvent.PAY.toOrderOperation())) {
                return TradeRequest.fail(Translate.of("支付单状态已不允许支付"));
            }
        }
        PaymentParams params = paymentParamsMaker.makeParams(payment);
        if (PayChannelName.integral_pay.toString().equals(payParams.getChannel())) {
            return payWithIntegral(payment, params, payParams, getLoginUserId());
        }
        if (!ObjectUtils.isEmpty(payParams.getAppId())) {
            params.setAppId(payParams.getAppId());
        }
        rewindPayInfoForWeshopOrder(payParams, params);
        matchAppIdForOpenId(params);
        log.info("[op:make](payParams) data:{}", JSON.toJSONString(payParams));
        PayChannel paymentChannel = channelRegistry.findChannel(payParams.getChannel());
        try {
            log.info("PayFactory.prePay, orderIds={}, 支付单id={}, 开始调用paymentChannel.paymentRequest", JSON.toJSONString(payParams.getOrderIds()), payment.getId());
            TradeRequest result = paymentChannel.paymentRequest(params);
            log.info("PayFactory.prePay, orderIds={}, 支付单id={}, 完成调用paymentChannel.paymentRequest, result={}",
                    JSON.toJSONString(payParams.getOrderIds()), payment.getId(), JSON.toJSONString(result));

            payment.setPayRequest(result.getResult().getRequestOriginalUrl());
            PaymentAccountUtil.genRecpAccount(payment.getPayRequest()).ifPresent(payment::setRecpAccount);
            paymentWriteService.update(payment);
            return result;
        } catch (Exception ex) {
            log.error("PayFactory.prePay error, orderIds={}, 支付单id={} ", JSON.toJSONString(payParams.getOrderIds()), payment.getId(), ex);
            throw ex;
        } finally {
            BeanProtector.threadLocal.remove();
        }
    }

    private void matchAppIdForOpenId(PaymentParams params) {
        try {
            String openId = UserOpenIdUtil.getOpenId();
            if (ObjectUtils.isEmpty(openId)) {
                return;
            }

            var appType = UserUtil.getCurrentAppType();
            var list = userWxReadService.findByOpenId(openId).getResult();
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            list.stream().filter(entity -> appType == null || appType.getCode().equals(entity.getAppType()))
                    .findFirst()
                    .map(UserWx::getAppId)
                    .ifPresent(params::setAppId);
        } catch (Exception ignored) {

        } finally {
            log.info("Param -> {}", params);
        }
    }

    private TradeRequest payWithIntegral(Payment payment, PaymentParams params, PayParams payParams, Long userId) {
        /*
         * 去积分支付--同步处理
         * booleanResult.getResult() =true支付成功
         * booleanResult.getResult() =false支付失败
         * payment.getChannel() =Integral-pay
         */
        payment.setPayRequest("");
        paymentWriteService.update(payment);
        Either<Boolean> booleanResult = integralPayFactory.onIntegralPayment(payment.getId(), userId, params.getFee(), payment.getOutId(), payParams.getOrderIds());
        return TradeRequest.ok(new Redirect(payment.getChannel(), booleanResult.take(), "", "", ""));
    }

    /**
     * 置换支付信息
     *
     * @param params    支付信息
     * @param orderId   订单Id
     * @param orderType 订单类型
     */
    private void replacePayToken(PaymentParams params, Long orderId, Integer orderType) {
        try {
            JsapiWxToken jsapiWxToken = jsapiWxTokenTokenProvider.findToken(params.getSellerNo());
            // no skuOrder payment here
            if (orderType != OrderLevel.SHOP.getValue()) {
                throw new IllegalStateException(Translate.of("不支持SkuOrder付款"));
            }
            Long weShopId = Long.parseLong(shopOrderReadService.findById(orderId).getResult().getOutShopId());
            WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseThrow(() -> new RuntimeException(Translate.of("异常店铺信息")));
            if (Objects.nonNull(weShop.getMchAppId())) {
                if (Objects.nonNull(params.getAppId())) {
                    return;
                }
                jsapiWxToken.setAppId(weShop.getMchAppId());
                jsapiWxToken.setMchId(weShop.getMchId());
                jsapiWxToken.setSecret(weShop.getMchSecret());
            }
        } catch (Exception ex) {
            log.error("{} fail to inject pay-param [{}] order[{}] type[{}]", LogUtil.getClassMethodName(), JSON.toJSONString(params)
                    , orderId, orderType, ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("WeShopPayInfo", String.format("订单[%s]类型[%s] 支付失败", orderId, orderType), ex, EmailReceiverGroup.DEVELOPER));
            throw new JsonResponseException(Translate.of("获取支付信息失败, 请联系商户设置支付信息"));
        }
    }

    /**
     * 检测是否为weShop渠道
     *
     * @param payParams 支付信息
     * @param params    支付参数
     */
    private void rewindPayInfoForWeshopOrder(PayParams payParams, PaymentParams params) {
        if (OrderLevel.SHOP.getValue() != payParams.getOrderType()) {
            return;
        }
        boolean isWeShopChannel = false;
        boolean isFirst = true;

        for (Long orderId : payParams.getOrderIds()) {
            ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
            if (Objects.equals(shopOrder.getOutFrom(), OrderOutFrom.WE_SHOP.Code())) {
                if (isFirst) {
                    isWeShopChannel = true;
                } else {
                    if (!isWeShopChannel) {
                        throw new JsonResponseException(Translate.of("订单[%s]无法与其他订单一起支付, 请独立支付", orderId));
                    }
                }
            } else {
                if (isWeShopChannel) {
                    throw new JsonResponseException(Translate.of("订单[%s]无法与其他订单一起支付, 请独立支付", orderId));
                }
            }
            if (isFirst) {
                isFirst = false;
            }
        }
        if (isWeShopChannel) {
            replacePayToken(params, payParams.getOrderIds().get(0), payParams.getOrderType());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean closePayment(long paymentId) {
        Optional.ofNullable(paymentWriteService.updateStatus(paymentId, -1).getResult())
                .filter(Predicate.isEqual(Boolean.TRUE))
                .orElseThrow(() -> new RuntimeException(Translate.of("数据更替失败, 请重试")));
        paymentLogic.closePayment(paymentId)
                .logError(error -> log.error("{} can't close payment for now", LogUtil.getClassMethodName()));
        return true;
    }

    private long getLoginUserId() {
        return Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
    }

    enum PayChannelName {
        /**
         * Pay Channel Name Display
         */
        wap, wechat, mock, jsapi, wechatpay, alipay, app, qr, Native, integral_pay, pc;


        @Override
        public String toString() {
            return name().toLowerCase().replace("_", "-");
        }
    }
}
