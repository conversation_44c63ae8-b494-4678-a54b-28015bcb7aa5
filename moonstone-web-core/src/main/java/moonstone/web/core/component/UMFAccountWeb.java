package moonstone.web.core.component;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/api/umf/config")
public class UMFAccountWeb {
    @Autowired
    MongoTemplate mongoTemplate;

    @GetMapping
    public UmfConfig config(){
        return Optional.ofNullable(mongoTemplate.findOne(new Query(Criteria.where("payWay").exists(true)), UmfConfig.class))
                .orElse(new UmfConfig());
    }

    @PostMapping
    public UmfConfig set(@RequestBody UmfConfig umfConfig){
        mongoTemplate.dropCollection(UmfConfig.class);
        mongoTemplate.insert(umfConfig);
        return config();
    }

    @Data
    public static class UmfConfig {
        String payWay;
        String recpAccount;
    }
}
