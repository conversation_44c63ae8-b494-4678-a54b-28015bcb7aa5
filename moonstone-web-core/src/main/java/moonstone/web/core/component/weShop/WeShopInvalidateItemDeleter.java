package moonstone.web.core.component.weShop;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.eventbus.Message;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.web.core.events.weShopItem.WeShopItemDeletedEvent;
import org.springframework.stereotype.Component;

import java.util.List;

@AllArgsConstructor
@Component
@Slf4j
public class WeShopInvalidateItemDeleter extends AbstractVerticle {
    private final WeShopItemReadService weShopItemReadService;
    private final WeShopItemWriteService weShopItemWriteService;


    @Override
    public void start() throws Exception {
        super.start();
        vertx.eventBus().<Long>consumer(WeShopVertxEvent.DELETE_INVALIDATE_ITEM.name())
                .handler(this::deleteInvalidateItem);
    }

    /**
     * delete the invalidate weShopItem
     *
     * @param weShopIdMessage contain the weShopId
     */
    private void deleteInvalidateItem(Message<Long> weShopIdMessage) {
        List<Long> weShopItemIdList = weShopItemReadService.findIdsByWeShopIdAndStatus(weShopIdMessage.body(), -1);
        weShopItemIdList.forEach(weShopItemWriteService::delete);
        weShopItemIdList.stream().map(WeShopItemDeletedEvent::new).forEach(EventSender::publish);
        try {
            Thread.sleep(300);
        } catch (Exception exception) {
            log.error("{} Fail to sleep address[{}] weShopId[{}]", LogUtil.getClassMethodName(), weShopIdMessage.address()
                    , weShopIdMessage.body(), exception);
        }
        weShopIdMessage.reply(true);
    }
}
