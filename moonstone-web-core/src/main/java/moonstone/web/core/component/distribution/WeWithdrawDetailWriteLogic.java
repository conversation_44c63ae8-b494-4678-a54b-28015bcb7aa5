package moonstone.web.core.component.distribution;

import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.enums.TradeStatus;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.model.BusinessPayParams;
import io.terminus.pay.model.TradeResult;
import io.terminus.pay.service.PayChannel;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.EncryptUtil;
import moonstone.common.utils.EventSender;
import moonstone.weShop.dto.WeWithdrawApplicationSubmit;
import moonstone.weShop.enums.WeShopAccountDetailType;
import moonstone.weShop.enums.WeWithdrawDetailStatus;
import moonstone.weShop.enums.WeWithdrawDetailType;
import moonstone.weShop.model.*;
import moonstone.weShop.service.*;
import moonstone.web.core.component.cache.DeveloperEmailAddressCache;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Author:  CaiZhy
 * Date:    2019/1/9
 */
@Slf4j
@Component
public class WeWithdrawDetailWriteLogic {
    @RpcConsumer
    private WeWithdrawDetailReadService weWithdrawDetailReadService;
    @RpcConsumer
    private WeWithdrawDetailWriteService weWithdrawDetailWriteService;
    @RpcConsumer
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @RpcConsumer
    private WeShopShopAccountWriteService weShopShopAccountWriteService;
    @RpcConsumer
    private WeShopAccountDetailReadService weShopAccountDetailReadService;
    @RpcConsumer
    private WeShopAccountDetailWriteService weShopAccountDetailWriteService;
    @RpcConsumer
    private WeShopReadService weShopReadService;
    @RpcConsumer
    private WeShopWalletReadService weShopWalletReadService;
    @Autowired
    private ParanaConfig paranaConfig;
    @Autowired
    private ChannelRegistry channelRegistry;
    @Autowired
    private DeveloperEmailAddressCache developerEmailAddressCache;
    @Autowired
    private EnvironmentConfig environmentConfig;


    private static final DateTimeFormatter DFT_BATCH = DateTimeFormat.forPattern("yyyyMMddHHmmss");

    @Transactional
    public Response<Long> create(WeWithdrawApplicationSubmit weWithdrawApplicationSubmit, Long customTaxRate) {
        try {
            //限制最低提现金额0.03元
            if (weWithdrawApplicationSubmit.getAmount() < 30L) {
                throw new JsonResponseException("weWithdrawDetail.amount.smaller.than.minimum");
            }
            //检验余额充足
            Long amount = weWithdrawApplicationSubmit.getAmount();
            // ad A x (税率+100) / 100 = 真实提现金额
            Long checkAmount = weWithdrawApplicationSubmit.getAmount() * (100 + customTaxRate) / 10;
            checkAmount = checkAmount % 10 > 4 ? checkAmount / 10 + 1 : checkAmount / 10;
            Response<WeShopShopAccount> rWeShopShopAccount =
                    weShopShopAccountReadService.findByWeShopIdAndShopId(weWithdrawApplicationSubmit.getWeShopId(), weWithdrawApplicationSubmit.getShopId());
            if (!rWeShopShopAccount.isSuccess()) {
                log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}",
                        weWithdrawApplicationSubmit.getWeShopId(), weWithdrawApplicationSubmit.getShopId(), rWeShopShopAccount.getError());
                throw new JsonResponseException(rWeShopShopAccount.getError());
            }
            WeShopShopAccount weShopShopAccount = rWeShopShopAccount.getResult();
            if (weShopShopAccount.getBalance() < checkAmount) {
                throw new JsonResponseException("weWithdrawDetail.create.balance.insufficient");
            }
            //检查是否实名认证，并填充
            Response<WeShop> rWeShop = weShopReadService.findById(weWithdrawApplicationSubmit.getWeShopId());
            if (!rWeShop.isSuccess()) {
                log.error("failed to find weShop by id={}, error code: {}", weWithdrawApplicationSubmit.getWeShopId(), rWeShop.getError());
                throw new JsonResponseException(rWeShop.getError());
            }
            WeShop weShop = rWeShop.getResult();
            if (ObjectUtils.isEmpty(weShop.getRealName())) {
                log.warn("weShop(id={}) want to withdraw without real name certification", weShop.getId());
                throw new JsonResponseException("weWithdrawDetail.create.real.name.defect");
            }
            //检验提现密码
            Response<WeShopWallet> rWeShopWallet = weShopWalletReadService.findByWeShopId(weWithdrawApplicationSubmit.getWeShopId());
            if (!rWeShopWallet.isSuccess()) {
                log.error("failed to find weShop wallet by weShopId={}, error code: {}", weWithdrawApplicationSubmit.getWeShopId(), rWeShopWallet.getError());
                throw new JsonResponseException(rWeShopWallet.getError());
            }
            WeShopWallet weShopWallet = rWeShopWallet.getResult();
            if (ObjectUtils.isEmpty(weShopWallet)) {
                log.warn("weShop(id={}) want to withdraw without setting wallet password", weWithdrawApplicationSubmit.getWeShopId());
                throw new JsonResponseException("weWithdrawDetail.create.wallet.password.defect");
            }
            if (!EncryptUtil.match(weWithdrawApplicationSubmit.getPassword(), weShopWallet.getPassword())) {
                log.error("weShop(id={}, userId={})'s wallet password mismatch, withdraw failed",
                        weWithdrawApplicationSubmit.getWeShopId(), weWithdrawApplicationSubmit.getUserId());
                throw new JsonResponseException("weShopWallet.password.mismatch");
            }
            //填充数据
            WeWithdrawDetail weWithdrawDetail = makeWeWithdrawDetail(weWithdrawApplicationSubmit, weShopShopAccount, weShop);
            //创建提现明细
            Response<Long> createResponse = weWithdrawDetailWriteService.create(weWithdrawDetail);
            if (!createResponse.isSuccess()) {
                log.error("failed to create weWithdrawDetail({}), error code: {}", weWithdrawDetail, createResponse.getError());
                throw new JsonResponseException(createResponse.getError());
            }
            Long weWithdrawDetailId = createResponse.getResult();
            //给提现明细设置交易流水号
            WeWithdrawDetail toUpdate = new WeWithdrawDetail();
            toUpdate.setId(weWithdrawDetailId);
            String prefix = DFT_BATCH.print(new DateTime(new Date()));
            String suffix = weWithdrawDetailId.toString();
            Integer trackLength = suffix.length();
            String padding = Strings.repeat("0", 18 - trackLength);
            toUpdate.setTradeNo(prefix + padding + suffix);
            Response<Boolean> updateResponse = weWithdrawDetailWriteService.update(toUpdate);
            if (!updateResponse.isSuccess() || !updateResponse.getResult()) {
                log.error("failed to update weWithdrawDetail tradeNo to {}, error code: {}", toUpdate.getTradeNo(), updateResponse.getError());
                throw new JsonResponseException(updateResponse.getError());
            }
            //扣减对应账户余额
            Response<Boolean> deductBalanceResponse = weShopShopAccountWriteService.addBalance(weShopShopAccount.getId(), -checkAmount);
            if (!deductBalanceResponse.isSuccess() || !deductBalanceResponse.getResult()) {
                log.error("failed to deduct balance for weShopShopAccount(id={}), amount={},checkAmount={}, error code: {}",
                        weShopShopAccount.getId(), amount, checkAmount, deductBalanceResponse.getError());
                throw new JsonResponseException(deductBalanceResponse.getError());
            }
            Long withdrawId;
            {
                //生成对应的账户明细
                WeShopAccountDetail weShopAccountDetail = new WeShopAccountDetail();
                weShopAccountDetail.setType(WeShopAccountDetailType.WITHDRAW.getValue());
                weShopAccountDetail.setAmounts(-amount);
                weShopAccountDetail.setBalance(weShopShopAccount.getBalance() - amount);
                weShopAccountDetail.setWeShopId(weWithdrawDetail.getWeShopId());
                weShopAccountDetail.setShopId(weWithdrawDetail.getShopId());
                weShopAccountDetail.setSourceIds(Lists.newArrayList(weWithdrawDetailId));
                Map<String, String> extra = new HashMap<>();
                extra.put(DistributionConstants.ACCOUNT_DETAIL_WITHDRAW_ID, weWithdrawDetailId.toString());
                weShopAccountDetail.setExtra(extra);
                weShopAccountDetail.setStatus(1);
                Response<Long> accountDetailResponse = weShopAccountDetailWriteService.create(weShopAccountDetail);
                if (!accountDetailResponse.isSuccess()) {
                    log.error("failed to create weShop account detail({}), error code: {}", weShopAccountDetail, accountDetailResponse.getError());
                    throw new JsonResponseException(accountDetailResponse.getError());
                }
                withdrawId = accountDetailResponse.getResult();
            }

            //生成税务提取明细
            {
                WeShopAccountDetail taxDetail = new WeShopAccountDetail();
                taxDetail.setType(WeShopAccountDetailType.TAX.getValue());
                taxDetail.setAmounts(-(checkAmount - amount));
                taxDetail.setBalance(weShopShopAccount.getBalance() - checkAmount);
                taxDetail.setWeShopId(weWithdrawDetail.getWeShopId());
                taxDetail.setShopId(weWithdrawDetail.getShopId());
                taxDetail.setSourceIds(Lists.newArrayList(weWithdrawDetailId, withdrawId));
                Map<String, String> extra = new HashMap<>();
                extra.put(DistributionConstants.ACCOUNT_DETAIL_WITHDRAW_ID, weWithdrawDetailId.toString());
                taxDetail.setExtra(extra);
                taxDetail.setStatus(1);
                Response<Long> accountDetailResponse = weShopAccountDetailWriteService.create(taxDetail);
                if (!accountDetailResponse.isSuccess()) {
                    log.error("failed to create weShop account detail({}), error code: {}", taxDetail, accountDetailResponse.getError());
                    throw new JsonResponseException(accountDetailResponse.getError());
                }
            }
            return createResponse;
        } catch (Exception e) {
            log.error("fail to create weWithdrawApplicationSubmit({}), cause: {}", weWithdrawApplicationSubmit, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    private WeWithdrawDetail makeWeWithdrawDetail(WeWithdrawApplicationSubmit weWithdrawApplicationSubmit, WeShopShopAccount weShopShopAccount, WeShop weShop) {
        WeWithdrawDetail weWithdrawDetail = new WeWithdrawDetail();
        weWithdrawDetail.setUserId(weWithdrawApplicationSubmit.getUserId());
        weWithdrawDetail.setWeShopId(weWithdrawApplicationSubmit.getWeShopId());
        weWithdrawDetail.setShopId(weWithdrawApplicationSubmit.getShopId());
        weWithdrawDetail.setPreApplicationBalance(weShopShopAccount.getBalance());
        weWithdrawDetail.setAmount(weWithdrawApplicationSubmit.getAmount());
        weWithdrawDetail.setCommissionCharge(weWithdrawApplicationSubmit.getCommissionCharge());
        weWithdrawDetail.setStatus(WeWithdrawDetailStatus.PENDING.getValue());
        weWithdrawDetail.setType(weWithdrawApplicationSubmit.getType());
        weWithdrawDetail.setApplicantName(weShop.getRealName());
        weWithdrawDetail.setApplyRemark(weWithdrawApplicationSubmit.getApplyRemark());
        weWithdrawDetail.setOpenId(weWithdrawApplicationSubmit.getOpenId());
        weWithdrawDetail.setClientIp(weWithdrawApplicationSubmit.getClientIp());
        weWithdrawDetail.setExtra(weWithdrawApplicationSubmit.getExtra());
        weWithdrawDetail.setTags(weWithdrawApplicationSubmit.getTags());
        return weWithdrawDetail;
    }

    @Transactional
    public void sellerAgree(Long id, CommonUser currentUser) {
        try {
            Response<WeWithdrawDetail> rWeWithdrawDetail = weWithdrawDetailReadService.findById(id);
            if (!rWeWithdrawDetail.isSuccess()) {
                log.error("failed to find weWithdrawDetail by id={}, error code: {}", id, rWeWithdrawDetail.getError());
                throw new JsonResponseException(rWeWithdrawDetail.getError());
            }
            WeWithdrawDetail weWithdrawDetail = rWeWithdrawDetail.getResult();
            //检查商家权限
            checkSellerAuth(weWithdrawDetail, currentUser);
            //修改状态为提现中
            if (weWithdrawDetail.getStatus() != WeWithdrawDetailStatus.PENDING.getValue()) {
                throw new JsonResponseException("weWithdrawDetail.status.not.pending");
            }
            Response<Boolean> updateStatusResponse = weWithdrawDetailWriteService.updateStatus(id, WeWithdrawDetailStatus.WITHDRAWING.getValue());
            if (!updateStatusResponse.isSuccess() || !updateStatusResponse.getResult()) {
                log.error("failed to update weWithdrawDetail(id={}) status to {}, error code: {}",
                        id, WeWithdrawDetailStatus.WITHDRAWING.getValue(), updateStatusResponse.getError());
                throw new JsonResponseException(updateStatusResponse.getError());
            }
            //真正的提现操作事务
            realWithdraw(weWithdrawDetail);
        } catch (Exception e) {
            log.error("seller(id={}, shopId={}) fail to agree weWithdrawDetail(id={}), cause: {}",
                    currentUser.getId(), currentUser.getShopId(), id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("weWithdrawDetail.agree.fail");
        }
    }

    @Transactional
    public void sellerReject(Long id, String auditingRemark, CommonUser currentUser) {
        try {
            Response<WeWithdrawDetail> rWeWithdrawDetail = weWithdrawDetailReadService.findById(id);
            if (!rWeWithdrawDetail.isSuccess()) {
                log.error("failed to find weWithdrawDetail by id={}, error code: {}", id, rWeWithdrawDetail.getError());
                throw new JsonResponseException(rWeWithdrawDetail.getError());
            }
            WeWithdrawDetail weWithdrawDetail = rWeWithdrawDetail.getResult();
            //检查商家权限
            checkSellerAuth(weWithdrawDetail, currentUser);

            if (weWithdrawDetail.getStatus() != WeWithdrawDetailStatus.PENDING.getValue()) {
                throw new JsonResponseException("weWithdrawDetail.status.not.pending");
            }
            doAfterWithdrawFailed(weWithdrawDetail, WeWithdrawDetailStatus.REFUSE, auditingRemark, null);
        } catch (Exception e) {
            log.error("seller(id={}, shopId={}) fail to reject weWithdrawDetail(id={}), cause: {}",
                    currentUser.getId(), currentUser.getShopId(), id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("weWithdrawDetail.reject.fail");
        }
    }

    private WeShopAccountDetail getTaxBy(WeWithdrawDetail weWithdrawDetail) {
        val rPaging = weShopAccountDetailReadService.paging(WeShopAccountDetailType.TAX.getValue(), weWithdrawDetail.getWeShopId(), weWithdrawDetail.getShopId()
                , 1, null
                , DateUtil.withTimeAtStartOfDay(weWithdrawDetail.getCreatedAt()), DateUtil.withTimeAtEndOfDay(weWithdrawDetail.getCreatedAt())
                , null, null,
                1, Integer.MAX_VALUE);
        if (!rPaging.isSuccess() || CollectionUtils.isEmpty(rPaging.getResult().getData())) {
            log.error("failed to find tax for WeWithdrawDetail(id={}), error code: {}",
                    weWithdrawDetail.getId(), rPaging.getError());
            throw new JsonResponseException(rPaging.getError());
        }
        WeShopAccountDetail rTax = null;
        for (WeShopAccountDetail wd : rPaging.getResult().getData()) {
            if (wd.getExtra().get("withdrawId").equals(weWithdrawDetail.getId() + "")) {
                rTax = wd;
            }
        }
        if (rTax == null) {
            log.error("failed to find tax for WeWithdrawDetail(id={}), error code: {}",
                    weWithdrawDetail.getId(), rPaging.getError());
            throw new JsonResponseException(rPaging.getError());
        }
        return rTax;
    }

    private void doAfterWithdrawFailed(WeWithdrawDetail weWithdrawDetail, WeWithdrawDetailStatus targetStatus, String auditingRemark, String errorMsg) {
        //修改申请状态并填写拒绝原因
        WeWithdrawDetail toUpdate = new WeWithdrawDetail();
        toUpdate.setId(weWithdrawDetail.getId());
        toUpdate.setStatus(targetStatus.getValue());
        toUpdate.setErrorMsg(errorMsg);
        toUpdate.setAuditingRemark(auditingRemark);
        Response<Boolean> updateResponse = weWithdrawDetailWriteService.update(toUpdate);
        if (!updateResponse.isSuccess() || !updateResponse.getResult()) {
            log.error("failed to update weWithdrawDetail({}), error code: {}", toUpdate, updateResponse.getError());
            throw new JsonResponseException(updateResponse.getError());
        }
        WeShopAccountDetail tax = getTaxBy(weWithdrawDetail);
        //退回余额
        Long amount = weWithdrawDetail.getAmount() - tax.getAmounts();
        Response<WeShopShopAccount> rWeShopShopAccount =
                weShopShopAccountReadService.findByWeShopIdAndShopId(weWithdrawDetail.getWeShopId(), weWithdrawDetail.getShopId());
        if (!rWeShopShopAccount.isSuccess()) {
            log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}",
                    weWithdrawDetail.getWeShopId(), weWithdrawDetail.getShopId(), rWeShopShopAccount.getError());
            throw new JsonResponseException(rWeShopShopAccount.getError());
        }
        WeShopShopAccount weShopShopAccount = rWeShopShopAccount.getResult();
        Response<Boolean> returnBalanceResponse = weShopShopAccountWriteService.addBalance(weShopShopAccount.getId(), amount);
        if (!returnBalanceResponse.isSuccess() || !returnBalanceResponse.getResult()) {
            log.error("failed to return balance for weShopShopAccount(id={}), amount={}, error code: {}",
                    weShopShopAccount.getId(), amount, returnBalanceResponse.getError());
            throw new JsonResponseException(returnBalanceResponse.getError());
        }

        Long backId;
        {
            //生成账户退回明细
            WeShopAccountDetail weShopAccountDetail = new WeShopAccountDetail();
            weShopAccountDetail.setType(WeShopAccountDetailType.WITHDRAW_FAILED.getValue());
            weShopAccountDetail.setAmounts(amount);
            weShopAccountDetail.setBalance(weShopShopAccount.getBalance() + amount);
            weShopAccountDetail.setWeShopId(weWithdrawDetail.getWeShopId());
            weShopAccountDetail.setShopId(weWithdrawDetail.getShopId());
            weShopAccountDetail.setSourceIds(Lists.newArrayList(weWithdrawDetail.getId()));
            Map<String, String> extra = new HashMap<>();
            extra.put(DistributionConstants.ACCOUNT_DETAIL_WITHDRAW_ID, weWithdrawDetail.getId().toString());
            weShopAccountDetail.setExtra(extra);
            weShopAccountDetail.setStatus(1);
            Response<Long> accountDetailResponse = weShopAccountDetailWriteService.create(weShopAccountDetail);
            if (!accountDetailResponse.isSuccess()) {
                log.error("failed to create weShop account detail({}), error code: {}", weShopAccountDetail, accountDetailResponse.getError());
                throw new JsonResponseException(accountDetailResponse.getError());
            }
            backId = accountDetailResponse.getResult();
        }
        {
            // 生成税务退回明细
            WeShopAccountDetail weShopAccountDetail = new WeShopAccountDetail();
            weShopAccountDetail.setType(WeShopAccountDetailType.TAX_FAILED.getValue());
            weShopAccountDetail.setAmounts(-tax.getAmounts());
            weShopAccountDetail.setBalance(weShopShopAccount.getBalance() - tax.getAmounts() + amount);
            weShopAccountDetail.setWeShopId(weWithdrawDetail.getWeShopId());
            weShopAccountDetail.setShopId(weWithdrawDetail.getShopId());
            weShopAccountDetail.setSourceIds(Lists.newArrayList(weWithdrawDetail.getId(), backId));
            Map<String, String> extra = new HashMap<>();
            extra.put(DistributionConstants.ACCOUNT_DETAIL_WITHDRAW_ID, weWithdrawDetail.getId().toString());
            weShopAccountDetail.setExtra(extra);
            weShopAccountDetail.setStatus(1);
            Response<Long> accountDetailResponse = weShopAccountDetailWriteService.create(weShopAccountDetail);
            if (!accountDetailResponse.isSuccess()) {
                log.error("failed to create weShop account detail({}), error code: {}", weShopAccountDetail, accountDetailResponse.getError());
                throw new JsonResponseException(accountDetailResponse.getError());
            }
        }
    }

    private void realWithdraw(WeWithdrawDetail weWithdrawDetail) {
        //提现（成功：修改明细状态，账户累计提现金额；不成功：修改明细状态，退还余额，创建账户明细）
        TradeResult result;
        if (environmentConfig.isOnline()) {
            switch (WeWithdrawDetailType.fromInt(weWithdrawDetail.getType())) {
                case WEIXIN: {
                    Response<WeShop> weShopResponse = weShopReadService.findById(weWithdrawDetail.getWeShopId());
                    if (!weShopResponse.isSuccess()) {
                        log.error("failed to find weShop by id={}, error code: {}", weWithdrawDetail.getWeShopId(), weShopResponse.getError());
                        throw new JsonResponseException(weShopResponse.getError());
                    }
                    WeShop weShop = weShopResponse.getResult();
                    String channel = "wechatpay-jsapi";
                    PayChannel payChannel = channelRegistry.findChannel(channel);
                    BusinessPayParams businessPayParams = makeBusinessPayToChangeParams(weWithdrawDetail, channel, weShop.getRealName());
                    result = payChannel.businessPay(businessPayParams);
                    break;
                }
                case BANK:
                default: {
                    log.warn("we can only withdraw to change now!!! how can the weWithdrawDetail(id={}) be created???", weWithdrawDetail.getId());
                    throw new JsonResponseException("weWithdrawDetail.create.type.not.supportive");
                }
            }
        } else {
            //若不是线上环境，则模拟提现
            result = new TradeResult();
            result.setChannel("mockpay");
            result.setType(TradeType.BUSINESS_PAY.value());
            result.setStatus(TradeStatus.SUCCESS.value());
            result.setGatewaySerialNo(weWithdrawDetail.getPaymentNo());
            result.setTradeAt(new Date());
        }
        //结果处理
        if (!result.isFail()) {
            //修改状态为提现成功,并记录提现数据
            WeWithdrawDetail toUpdate = new WeWithdrawDetail();
            toUpdate.setId(weWithdrawDetail.getId());
            toUpdate.setStatus(WeWithdrawDetailStatus.SUCCESS.getValue());
            toUpdate.setPaymentNo(result.getGatewaySerialNo());
            toUpdate.setPaymentAt(result.getTradeAt());
            Response<Boolean> updateResponse = weWithdrawDetailWriteService.update(toUpdate);
            if (!updateResponse.isSuccess() || !updateResponse.getResult()) {
                log.error("failed to update weWithdrawDetail({}), error code: {}", toUpdate, updateResponse.getError());
                throw new JsonResponseException(updateResponse.getError());
            }
            //账户累计提现金额
            Response<WeShopShopAccount> rWeShopShopAccount = weShopShopAccountReadService.findByWeShopIdAndShopId(
                    weWithdrawDetail.getWeShopId(), weWithdrawDetail.getShopId());
            if (!rWeShopShopAccount.isSuccess()) {
                log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}",
                        weWithdrawDetail.getWeShopId(), weWithdrawDetail.getShopId(), rWeShopShopAccount.getError());
                throw new JsonResponseException(rWeShopShopAccount.getError());
            }
            WeShopShopAccount weShopShopAccount = rWeShopShopAccount.getResult();
            Response<Boolean> response = weShopShopAccountWriteService.addWithdraw(weShopShopAccount.getId(), weWithdrawDetail.getAmount());
            if (!response.isSuccess()) {
                log.error("failed to add withdraw(amount={}) to weShopShopAccount(id={}), error code: {}",
                        weWithdrawDetail.getAmount(), weShopShopAccount.getId(), response.getError());
                throw new JsonResponseException(response.getError());
            }
        } else {
            log.error("failed to business pay to change by weWithdrawDetail={}, error code: {}", weWithdrawDetail, result.getExtra());
            doAfterWithdrawFailed(weWithdrawDetail, WeWithdrawDetailStatus.FAIL, null, result.getError());
            //提现失败的邮件通知
            String toes = developerEmailAddressCache.getAllInJson();
            String data = "weWithdrawDetailId=" + weWithdrawDetail.getId();
            EventSender.sendApplicationEvent(new MsgSendRequestEvent(toes, "email.error.notice", ImmutableMap.of("location", "WeWithdrawDetailWriteLogic（提现）",
                    "env", environmentConfig.getEnv(), "errorCode", result.getError(),
                    "data", data, "errorMsg", "TradeResult:\n" + result)));
            log.info("sendSms email={}, errorCode={}", toes, result.getError());
        }
    }

    private BusinessPayParams makeBusinessPayToChangeParams(WeWithdrawDetail weWithdrawDetail, String channel, String realName) {
        BusinessPayParams params = new BusinessPayParams();
        params.setAppId(paranaConfig.getParanaWeSellerAppId());
        params.setChannel(channel);
        params.setPayToType("toChange");
        params.setSellerNo(weWithdrawDetail.getShopId().toString());
        params.setOpenId(weWithdrawDetail.getOpenId());
        params.setTradeNo(weWithdrawDetail.getTradeNo());
        params.setAmount(weWithdrawDetail.getAmount().intValue());
        params.setDesc("微分销店主提现测试");
        params.setCheckName("FORCE_CHECK");//NO_CHECK：不校验真实姓名；FORCE_CHECK：强校验真实姓名
        params.setReUserName(realName);
        params.setSpbillCreateIp(weWithdrawDetail.getClientIp());
        return params;
    }

    private void checkSellerAuth(WeWithdrawDetail weWithdrawDetail, CommonUser currentUser) {
        if (!Objects.equal(currentUser.getShopId(), weWithdrawDetail.getShopId())) {
            log.error("weWithdrawDetail(id={}) not belong to user(id={}, shopId={})", weWithdrawDetail.getId(), currentUser.getId(), currentUser.getShopId());
            throw new JsonResponseException("weWithdrawDetail.seller.not.owner");
        }
    }
}
