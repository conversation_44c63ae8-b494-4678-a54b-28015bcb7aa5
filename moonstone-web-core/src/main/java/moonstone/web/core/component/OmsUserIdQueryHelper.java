package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.shop.model.Shop;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OmsUserIdQueryHelper {
    @Autowired
    private ThirdPartyUserReadService thirdPartyUserReadService;
    @Autowired
    private ShopCacheHolder shopCacheHolder;
    @Autowired
    private ThirdPartyUserShopCache thirdPartyUserShopCache;
    @Autowired
    private OmsAccessCodeComponent omsAccessCodeComponent;

    /**
     * query OmsUserId
     *
     * @param userId inner userId
     * @return OmsUserId
     */
    public Long queryOmsUserId(Long userId) {
        ThirdPartyUser thirdPartyUser = thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), userId).orElse(null);
        if (Objects.nonNull(thirdPartyUser)) {
            return Long.parseLong(thirdPartyUser.getThirdPartyId());
        }
        // query from accessCode
        Shop ownShop = shopCacheHolder.findShopByUserId(userId);
        if (Objects.isNull(ownShop)) {
            return null;
        }
        Optional<String> accessCodeOpt = thirdPartyUserShopCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V2.Id(), userId))
                .map(ThirdPartyUserShop::getThirdPartyCode);
        return accessCodeOpt.map(accessCode -> omsAccessCodeComponent.queryOuterUserIdByAccessCode(accessCode).orElse(null)).orElse(null);
    }
}
