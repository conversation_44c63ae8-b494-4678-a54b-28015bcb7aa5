package moonstone.web.core.component;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.CommonUser;
import moonstone.common.model.SimpleRulerJudgeBean;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.IntermediateInfoWriteService;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 对额外拉新佣金的裁决器(宝宝佣金)
 */
@RestController
@Slf4j
public class ItemExLinkedProfitRulerJudgeImpl implements SimpleRulerJudgeBean<Item> {
    @Autowired
    private MongoTemplate mongoTemplate;
    @RpcConsumer
    private ItemReadService itemReadService;
    @RpcConsumer
    private SkuReadService skuReadService;
    @RpcConsumer
    private IntermediateInfoReadService intermediateInfoReadService;
    @RpcConsumer
    private IntermediateInfoWriteService intermediateInfoWriteService;

    @EqualsAndHashCode(callSuper = true)
    @Data
    static
    class View extends SimpleItemJudgeRulerPrinciple {
        String name;
    }

    @GetMapping("/api/ruler/item/profit/list")
    List<SimpleItemJudgeRulerPrinciple> getList() {
        return mongoTemplate.findAll(SimpleItemJudgeRulerPrinciple.class).stream().map(ruler ->
        {
            View view = new View();
            Optional.ofNullable(itemReadService.findById(ruler.getItemId()).getResult()).map(Item::getName).ifPresent(view::setName);
            BeanUtils.copyProperties(ruler, view);
            return view;
        }).collect(Collectors.toList());
    }

    private Query parseMap(Long shopId, Long itemId, String itemCode) {
        Query query = new Query();
        if (shopId != null) {
            query.addCriteria(Criteria.where("shopId").is(shopId));
        }
        if (itemId != null) {
            query.addCriteria(Criteria.where("itemId").is(itemId));
        }
        if (itemCode != null) {
            query.addCriteria(Criteria.where("itemCode").is(itemCode));
        }
        return query;
    }

    @GetMapping("/api/ruler/item/profit/query")
    List<SimpleItemJudgeRulerPrinciple> getList(Long shopId, Long itemId, String itemCode) {
        Query query = parseMap(shopId, itemId, itemCode);
        return mongoTemplate.find(query, SimpleItemJudgeRulerPrinciple.class).stream().map(ruler ->
        {
            View view = new View();
            Optional.ofNullable(itemReadService.findById(ruler.getItemId()).getResult()).map(Item::getName).ifPresent(view::setName);
            BeanUtils.copyProperties(ruler, view);
            return view;
        }).collect(Collectors.toList());
    }

    /**
     * 查找这个SkuId的对应的新客佣金
     * 先查询允许与是否设定了
     */
    public Optional<Long> getFirstCommissionProfitBySkuId(Long skuId) {
        Item item = itemReadService.findById(skuReadService.findSkuById(skuId).getResult().getItemId()).getResult();
        val skuLevelConfig = getList(null, item.getId(), null);
        if (skuLevelConfig.stream().map(SimpleItemJudgeRulerPrinciple::getCustomProfit).anyMatch(Predicate.isEqual(false))) {
            return Optional.empty();
        }
        if (skuLevelConfig.isEmpty()) {
            val itemLevelConfig = getList(null, null, item.getItemCode());
            if (itemLevelConfig.stream().map(SimpleItemJudgeRulerPrinciple::getCustomProfit).anyMatch(Predicate.isEqual(false))) {
                return Optional.empty();
            }
            if (itemLevelConfig.isEmpty()) {
                val shopLevelConfig = getList(item.getShopId(), null, null);
                if (shopLevelConfig.isEmpty() || shopLevelConfig.stream().noneMatch(SimpleItemJudgeRulerPrinciple::getCustomProfit)) {
                    return Optional.empty();
                }
            }
        }
        return Optional.ofNullable(intermediateInfoReadService.findByThirdAndType(skuId, ThirdIntermediateType.SKU.getValue()).take())
                .orElse(new ArrayList<>())
                .stream()
                .peek(info -> log.debug("{} baby profit set from intermediate [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(info)))
                .filter(info -> info.getFirstCommission() != null && info.getFirstCommission() > 0)
                .findFirst()
                .map(IntermediateInfo::getFirstCommission);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/api/ruler/item/profit/save")
    boolean saveOne(@RequestBody SimpleItemJudgeRulerPrinciple principle, @RequestParam(defaultValue = "false") boolean noAuth) {
        CommonUser operator = UserUtil.getCurrentUser();
        if (!noAuth && (operator == null || operator.getShopId() == null)) {
            throw new JsonResponseException("user.not.login");
        }
        if (StringUtils.hasText(principle.get_id())) {
            SimpleItemJudgeRulerPrinciple old = mongoTemplate.findById(principle.get_id(), SimpleItemJudgeRulerPrinciple.class);
            principle.setCreatedAt(old.createdAt);
        }
        if (principle.getShopId() == null) {
            principle.setShopId(((CommonUser) UserUtil.getCurrentUser()).getShopId());
        }
        if (Objects.equals(false, principle.getAllow())) {
            principle.setCustomProfit(false);
        }
        if (Objects.equals(true, principle.getCustomProfit()) && Objects.equals(principle.getProfit(), 0L)) {
            throw new JsonResponseException(new Translate("自定义新客佣金必须高于0").toString());
        }
        log.debug("{} principle:{}", LogUtil.getClassMethodName(), JSON.toJSONString(principle));
        if (Objects.equals(true, principle.getCustomProfit())) {
            if (principle.getItemId() != null) {
                Optional.ofNullable(skuReadService.findSkusByItemId(principle.getItemId()).getResult())
                        .orElse(new ArrayList<>())
                        .stream().map(Sku::getId)
                        .forEach(synchronizeProfitSet(principle.getProfit()));
            }
            if (StringUtils.hasText(principle.getItemCode())) {
                getSkusByItemCode(principle.getItemCode())
                        .forEach(synchronizeProfitSet(principle.getProfit()));
            }
        }
        mongoTemplate.save(principle);
        return true;
    }

    /**
     * 获取Sku列表 由ItemCode匹配 Item再获取Sku
     *
     * @param itemCode ItemCode
     */
    private Iterable<Long> getSkusByItemCode(String itemCode) {
        return Optional.ofNullable(itemReadService.findByItemCode(itemCode).getResult())
                .orElse(new ArrayList<>())
                .stream().map(Item::getId)
                .map(skuReadService::findSkusByItemId)
                .map(Response::getResult)
                .reduce(new ArrayList<>(), (left, right) -> {
                    left.addAll(right);
                    return right;
                }).stream()
                .map(Sku::getId).collect(Collectors.toList());
    }

    /**
     * 同步佣金设定
     *
     * @param profit 佣金
     */
    private Consumer<Long> synchronizeProfitSet(Long profit) {
        return skuId -> {
            boolean exists = intermediateInfoReadService.exists(skuId, ThirdIntermediateType.SKU.getValue()).orElse(false);
            log.debug("{} profit-info sku[{}] exists[{}]", LogUtil.getClassMethodName(), skuId, exists);
            if (!exists) {
                IntermediateInfo skuSet = new IntermediateInfo();
                skuSet.setThirdId(skuId);
                skuSet.setType(ThirdIntermediateType.SKU.getValue());
                skuSet.setFirstCommission(profit);
                skuSet.fillNullWithZero();
                intermediateInfoWriteService.create(skuSet);
                return;
            }
            for (IntermediateInfo info : intermediateInfoReadService.findByThirdAndType(skuId, ThirdIntermediateType.SKU.getValue()).orElse(new ArrayList<>())) {
                IntermediateInfo update = new IntermediateInfo();
                update.setId(info.getId());
                update.setFirstCommission(profit);
                log.debug("{} update sku[{}] result[{}]", LogUtil.getClassMethodName(), skuId, intermediateInfoWriteService.update(update));
            }
        };
    }

    @PostMapping("/api/ruler/item/profit/add")
    boolean addOne(@RequestBody SimpleItemJudgeRulerPrinciple principle, @RequestParam(defaultValue = "false") Boolean noAuth) {
        CommonUser operator = UserUtil.getCurrentUser();
        if (!noAuth && (operator == null || operator.getShopId() == null)) {
            throw new JsonResponseException("user.not.login");
        }
        if (principle.getShopId() == null) {
            principle.setShopId(((CommonUser) UserUtil.getCurrentUser()).getShopId());
        }
        principle.set_id(null);
        Query query = Query.query(Criteria.where("shopId").is(principle.getShopId())).addCriteria(Criteria.where("itemCode").is(principle.getItemCode())).addCriteria(Criteria.where("itemId").is(principle.getItemId()));
        String id = Optional.ofNullable(mongoTemplate.upsert(query, Update.update("allow", principle.getAllow()).currentDate("createdAt"), SimpleItemJudgeRulerPrinciple.class).getUpsertedId()).map(Object::toString)
                .orElse(mongoTemplate.findOne(query, SimpleItemJudgeRulerPrinciple.class).get_id());
        principle.set_id(id);
        saveOne(principle, noAuth);
        return true;
    }

    @PostMapping("/api/ruler/item/profit/del")
    int delete(String _id, Long shopId, Long itemId, @RequestParam(required = false) String itemCode) {
        Long operatorShopId = ((CommonUser) UserUtil.getCurrentUser()).getShopId();
        Query query = parseMap(shopId, itemId, itemCode);
        if (_id != null) {
            query.addCriteria(Criteria.where("_id").is(_id));
        }
        log.debug("{} query:{}", LogUtil.getClassMethodName(), query);
        itemId = Optional.ofNullable(mongoTemplate.findOne(query, SimpleItemJudgeRulerPrinciple.class)).map(SimpleItemJudgeRulerPrinciple::getItemId).orElse(itemId);
        itemCode = Optional.ofNullable(mongoTemplate.findOne(query, SimpleItemJudgeRulerPrinciple.class)).map(SimpleItemJudgeRulerPrinciple::getItemCode).orElse(itemCode);
        List<Sku> skuList = new ArrayList<>();
        Optional.ofNullable((itemCode))
                .map(itemReadService::findByItemCode)
                .map(Response::getResult)
                .orElse(new ArrayList<>())
                .stream().map(Item::getId)
                .map(skuReadService::findSkusByItemId)
                .map(Response::getResult)
                .filter(Objects::nonNull)
                .forEach(skuList::addAll);
        skuList.addAll(Optional.ofNullable(itemId).map(skuReadService::findSkusByItemId).map(Response::getResult)
                .orElse(new ArrayList<>()));
        skuList.stream().filter(sku -> Objects.equals(sku.getShopId(), operatorShopId)).map(Sku::getId).forEach(synchronizeProfitSet(0L));
        return (int) mongoTemplate.remove(query, SimpleItemJudgeRulerPrinciple.class).getDeletedCount();
    }

    /**
     * 用于商品判断是否允许额外拉新佣金
     */
    @Data
    static class SimpleItemJudgeRulerPrinciple {
        String _id;
        // 商店Id
        Long shopId;
        // 商品Id可为空
        Long itemId;
        // 商品Code
        String itemCode;
        // 是否允许
        Boolean allow = true;
        // 开启自定义佣金
        Boolean customProfit = false;
        // 自定义的佣金(冗余字段)
        Long profit;
        Date createdAt;
    }

    @Override
    public boolean allow(Item aimTarget) {
        if (aimTarget == null) {
            return false;
        }
        SimpleItemJudgeRulerPrinciple specificRuler = mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(aimTarget.getShopId())).addCriteria(Criteria.where("itemId").is(aimTarget.getId())), SimpleItemJudgeRulerPrinciple.class);
        log.debug("{} specificRule : {}", LogUtil.getClassMethodName(), JSON.toJSONString(specificRuler));
        if (specificRuler != null) {
            return specificRuler.allow == (specificRuler.getItemCode() == null || Objects.equals(specificRuler.getItemCode(), aimTarget.getItemCode()));
        }
        List<SimpleItemJudgeRulerPrinciple> simpleItemJudgeRulerPrinciples = mongoTemplate.find(Query.query(Criteria.where("shopId").is(aimTarget.getShopId())).addCriteria(Criteria.where("itemId").is(null)), SimpleItemJudgeRulerPrinciple.class);
        if (simpleItemJudgeRulerPrinciples != null) {
            if (simpleItemJudgeRulerPrinciples.stream().filter(SimpleItemJudgeRulerPrinciple::getAllow).map(SimpleItemJudgeRulerPrinciple::getItemCode).anyMatch(Predicate.isEqual(aimTarget.getItemCode()))) {
                log.debug("{} shopRule : {}", LogUtil.getClassMethodName(), JSON.toJSONString(simpleItemJudgeRulerPrinciples));
                return true;
            }
            if (simpleItemJudgeRulerPrinciples.stream().filter(one -> !one.getAllow()).map(SimpleItemJudgeRulerPrinciple::getItemCode).anyMatch(Predicate.isEqual(aimTarget.getItemCode()))) {
                log.debug("{} shopRule : {}", LogUtil.getClassMethodName(), JSON.toJSONString(simpleItemJudgeRulerPrinciples));
                return false;
            }
        }
        // 采取默认的规则
        log.debug("{} use the default rule", LogUtil.getClassMethodName());
        return allow(aimTarget.getItemCode());
    }

    /**
     * 是否允许这个商品被用于额外拉新佣金(宝宝佣金)
     * 将数据由数据库读取使用
     *
     * @param code 被序列化后的数据
     * @return 裁决结果, 目前仅仅用于某个固定商品代码 85
     */
    @Deprecated
    @Override
    public boolean allow(String code) {
        return !Objects.equals(code, "85");
    }
}
