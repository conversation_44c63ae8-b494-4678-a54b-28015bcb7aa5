/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.component.captcha;

import com.github.cage.Cage;

import javax.servlet.http.HttpSession;

/**
 * 验证码生成器
 *
 * Author  : panxin
 * Date    : 7:18 PM 3/7/16
 * Mail    : <EMAIL>
 */
public abstract class CaptchaGenerator {

    protected static final String CAPTCHA_TOKEN = "captchaToken";

    protected Cage cage;

    /**
     * 生成验证码到HttpSession中
     * @param session HttpSession
     * @return 图片字节码数组
     */
    public byte[] captcha(HttpSession session) {
        // create the text for the image
        String token = cage.getTokenGenerator().next();
        session.setAttribute(CAPTCHA_TOKEN, token);
        return cage.draw(token);
    }

    public abstract String generateCaptchaToken();

    public byte[] captcha(String token) {
        return cage.draw(token);
    }


    /**
     * 获取HttpSession中的验证码字符串
     * @param session HttpSession
     * @return HttpSession中验证码字符串
     */
    public String getGeneratedKey(HttpSession session) {
        String token = (String) session.getAttribute(CAPTCHA_TOKEN);
        //when token retrieved ,it should be removed
        session.removeAttribute(CAPTCHA_TOKEN);
        return token;
    }
}
