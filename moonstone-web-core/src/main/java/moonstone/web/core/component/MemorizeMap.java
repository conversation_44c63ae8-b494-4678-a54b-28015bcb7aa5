package moonstone.web.core.component;

import lombok.Data;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;

@Data
public class MemorizeMap<S, R> {
    HashMap<S, R> memorizeMap = new HashMap<>();

    /**
     * 可记忆化的可计算函数 f(x)->y
     *
     * @param <S> x域
     * @param <R> y域
     */
    public interface MemorizeAbleFunction<S, R> extends BiFunction<S, Function<S, Optional<R>>, Optional<R>> {
    }

    /**
     * 用于初始化目前的记忆池
     *
     * @param s 记忆索引
     * @param r 记忆目标数据
     * @return 自身
     */
    public MemorizeMap<S, R> init(S s, R r) {
        memorizeMap.put(s, r);
        return this;
    }

    // todo: fixme 改成模拟堆栈, 如果递归超过了1000次
    /**
     * 从记忆池中获取计算值   <>Lambda结合子不可避</>
     *
     * @param calculator 原计算函数
     * @return 被包裹的数据
     */
    Function<S, Optional<R>> getFromMemory(MemorizeAbleFunction<S, R> calculator) {
        return source -> {
            if (memorizeMap.containsKey(source)) {
                return Optional.ofNullable(memorizeMap.get(source));
            }
            Optional<R> result = calculator.apply(source, getFromMemory(calculator));
            result.ifPresent(r -> memorizeMap.put(source, r));
            return result;
        };
    }

    /**
     * 自动记忆化并且自动计算
     * {@SEE 计算函数必须计算完备，不然则gc爆炸或者栈爆炸}
     *
     * @param calculator 计算函数
     * @param source     计算获取的原值
     * @return 计算结果
     */
    public Optional<R> cal(MemorizeAbleFunction<S, R> calculator, S source) {
        return calculator.apply(source, getFromMemory(calculator));
    }

    public static void main(String[] args) {
        MemorizeMap<BigInteger, BigInteger> memorizeMap = new MemorizeMap<>();
        memorizeMap.cal((x, f) ->
                Optional.of(x.compareTo(new BigInteger("1")) <= 0 ? new BigInteger("1") : (f.apply(x.subtract(new BigInteger("1")))
                        .orElse(new BigInteger("1")).add(f.apply(x.subtract(new BigInteger("2"))).orElse(new BigInteger("1"))))), new BigInteger("2500"))
                .ifPresent(System.out::println);
    }
}
