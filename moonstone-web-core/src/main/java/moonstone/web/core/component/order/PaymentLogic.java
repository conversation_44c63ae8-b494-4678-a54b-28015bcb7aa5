/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.component.order;

import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.model.PaymentCloseParams;
import io.terminus.pay.model.RefundParams;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.model.TradeResult;
import io.terminus.pay.service.PayChannel;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderPaymentEvent;
import moonstone.event.PaymentPaidEvent;
import moonstone.order.api.FlowPicker;
import moonstone.order.api.TradeBatchNoGenerator;
import moonstone.order.component.PayInfoDigestor;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.promotion.component.PaymentCharger;
import moonstone.promotion.dto.PromotionResult;
import moonstone.shop.model.Shop;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.User;
import moonstone.web.core.events.settle.PaymentSettleEvent;
import moonstone.web.core.refund.api.RefundWrapFromPaymentApi;
import moonstone.web.core.util.ParanaUserMaker;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 通用的支付逻辑, 在收到支付网关回调后, 只更新支付单, 并发出支付事件, 异步更新相关订单状态
 * Date: 2016-05-06
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PaymentLogic {

    private static final Pattern APP_ID_PATTERN = Pattern.compile("appid=([^&]+)");
    private static final Pattern OUT_TRADE_NO_PATTERN = Pattern.compile("out_trade_no=([^&]+)");
    private static final Pattern MCH_ID_PATTERN = Pattern.compile("mch_id=([^&]+)");
    @Resource
    private PaymentReadService paymentReadService;
    @Resource
    private PaymentWriteService paymentWriteService;
    @Autowired
    private RefundReadService refundReadService;
    @Autowired
    private RefundWriteService refundWriteService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private GatherOrderReadService gatherOrderReadService;
    @Autowired
    private FlowPicker flowPicker;
    @Autowired
    private TradeBatchNoGenerator tradeBatchNoGenerator;
    @Autowired
    private PaymentCharger paymentCharger;
    @Autowired
    private ChannelRegistry channelRegistry;
    @Autowired
    private PaymentLogic self;
    @Autowired
    private ShopCacheHolder shopCacheHolder;
    @Autowired
    private UserCacheHolder userCacheHolder;
    @Autowired
    private UserProfileCacheHolder userProfileCacheHolder;

    /**
     * 在跳去支付网关前, 创建支付单,  保存支付渠道等信息, 持久化全局的优惠信息, 可能需要冻结某些优惠(如积分, 优惠券等)
     * 没有锁, 可能导致二次支付, 完全依赖第三方进行对订单锁定
     *
     * @param channel        支付渠道
     * @param promotionId    支付单对应的平台级别的优惠信息, 可能为空
     * @param orderIds       支付单对应的(子)订单列表
     * @param orderLevel     订单级别
     * @param orderOperation 操作
     * @return 支付单id
     */
    public Payment prePay(String channel, Long promotionId,
                          List<Long> orderIds, OrderLevel orderLevel, OrderOperation orderOperation, Long stage) {

        if (CollectionUtils.isEmpty(orderIds)) {
            log.error("no orderIds specified");
            throw new JsonResponseException("orderIds.empty");
        }

        String payInfoMd5 = PayInfoDigestor.digest(orderIds, orderLevel, channel, stage);
        Response<Optional<Payment>> findExistedPayment = paymentReadService.findPaymentByPayInfoMd5(payInfoMd5);
        if (!findExistedPayment.isSuccess()) {
            log.error("fail to find payment by payInfoMd5:{},cause:{}",
                    payInfoMd5, findExistedPayment.getError());
            throw new JsonResponseException(findExistedPayment.getError());
        }
        Optional<Payment> paymentOptional = findExistedPayment.getResult();
        if (paymentOptional.isPresent()) {
            return paymentOptional.get();
        }

        PromotionResult promotionResult = new PromotionResult();

        String payAccountNo = checkPriceAndFindPayAccountNo(orderIds, orderLevel, orderOperation, promotionId, promotionResult);

        Payment payment = new Payment();
        payment.setPayInfoMd5(payInfoMd5);
        payment.setStatus(OrderStatus.NOT_PAID.getValue());
        payment.setChannel(channel);
        payment.setPromotionId(promotionId);
        payment.setFee(promotionResult.getFee());
        payment.setOriginFee(promotionResult.getOriginFee());
        payment.setDiscount(promotionResult.getDiscount());
        payment.setPayAccountNo(payAccountNo);
        //stage为0表示全额支付
        payment.setStage(0);
        // 初始推送状态都为空，支付成功回调后再变更推送状态
        payment.setPushStatus(PaymentPushStatus.NULL.getValue());

        Response<Long> r = paymentWriteService.create(payment, orderIds, orderLevel);
        if (!r.isSuccess()) {
            log.error("failed to create payment for orders(ids={}, type={}), error code:{}",
                    orderIds, orderLevel, r.getError());
            throw new JsonResponseException(r.getError());
        }
        final Long paymentId = r.getResult();

        //为了拿到outId,强行查一把刚创建的payment
        Response<Payment> findPayment = paymentReadService.findById(paymentId);
        if (!findPayment.isSuccess()) {
            log.error("fail to find payment by id:{},cause:{}", paymentId, findPayment.getError());
            throw new JsonResponseException(findPayment.getError());
        }

        EventSender.sendApplicationEvent(new OrderPaymentEvent(paymentId));
        return findPayment.getResult();
    }

    private void annulMd5(Payment payment, String md5) {
        var update = new Payment();
        update.setId(payment.getId());
        update.setPayInfoMd5(md5);
        paymentWriteService.update(update);
    }

    /**
     * 计算价格并且获取收款人帐号信息
     *
     * @param orderIds        订单列表
     * @param orderLevel      订单级别
     * @param orderOperation  订单操作
     * @param promotionId     优惠id
     * @param promotionResult 复制回去的价格
     * @return 收款人帐号
     */
    private String checkPriceAndFindPayAccountNo(List<Long> orderIds, OrderLevel orderLevel, OrderOperation orderOperation, Long promotionId, PromotionResult promotionResult) {
        String payAccountNo = "";
        CommonUser buyer = null;
        switch (orderLevel) {
            case SHOP -> {
                List<ShopOrder> shopOrders = findShopOrdersByIds(orderIds);
                for (ShopOrder shopOrder : shopOrders) {
                    buyer = ParanaUserMaker.from(userCacheHolder.findByUserId(shopOrder.getBuyerId()).orElseThrow(() -> new RuntimeException(Translate.of("订单购买人查找失败"))));
                    Flow flow = flowPicker.pick(shopOrder, OrderLevel.SHOP);
                    if (!flow.operationAllowed(shopOrder.getStatus(), orderOperation)) {
                        log.error("shopOrder(id={})'s status({}) is not payable", shopOrder.getId(), shopOrder.getStatus());
                        throw new JsonResponseException("shop.order.not.payable");
                    }

                    payAccountNo = shopOrder.getShopId().toString();
                }
                BeanUtils.copyProperties(paymentCharger.chargeForShopOrders(shopOrders, buyer, promotionId), promotionResult);
            }
            case SKU -> {
                List<SkuOrder> skuOrders = findSkuOrdersByIds(orderIds);
                for (SkuOrder skuOrder : skuOrders) {
                    buyer = ParanaUserMaker.from(userCacheHolder.findByUserId(skuOrder.getBuyerId()).orElseThrow(() -> new RuntimeException(Translate.of("订单购买人查找失败"))));
                    Flow flow = flowPicker.pick(skuOrder, OrderLevel.SKU);
                    if (!flow.operationAllowed(skuOrder.getStatus(), orderOperation)) {
                        log.error("skuOrder(id={})'s status({}) is not payable", skuOrder.getId(), skuOrder.getStatus());
                        throw new JsonResponseException("sku.order.not.payable");
                    }
                    payAccountNo = skuOrder.getShopId().toString();
                }

                //TODO 子订单支付是不是不允许使用平台优惠?
                BeanUtils.copyProperties(paymentCharger.chargeForSkuOrders(skuOrders, buyer, promotionId), promotionResult);
            }
            case GATHER -> {
                List<GatherOrder> gatherOrderList = gatherOrderReadService.findByIds(orderIds).take();
                for (GatherOrder gatherOrder : gatherOrderList) {
                    buyer = ParanaUserMaker.from(userCacheHolder.findByUserId(gatherOrder.getBuyerId()).orElseThrow(() -> new RuntimeException(Translate.of("订单购买人查找失败"))));
                    Flow flow = flowPicker.pick(gatherOrder, OrderLevel.GATHER);
                    if (!flow.operationAllowed(gatherOrder.getStatus(), orderOperation)) {
                        log.error("{} gatherOrder(id => {}, status => {}) not allow to pay", LogUtil.getClassMethodName(), gatherOrder.getId(), gatherOrder.getStatus());
                        throw new JsonResponseException("gather.order.not.payable");
                    }
                    payAccountNo = gatherOrder.getShopId().toString();
                }
                BeanUtils.copyProperties(paymentCharger.chargeForGatherOrders(gatherOrderList, buyer, promotionId), promotionResult);
            }
            default -> throw new JsonResponseException("order.level.unknown");
        }
        return payAccountNo;
    }

    /**
     * 支付网关回调后, 更新支付单的状态, 以及完善支付流水号
     * <p>
     * 同时还得发出支付成功事件, 便于更新订单状态,以及执行对应的优惠等
     *
     * @param toBeUpdated 待更新的支付单
     * @return 是否更新成功
     */
    public Boolean postPay(Payment toBeUpdated) {
        String outId = toBeUpdated.getOutId();
        String paySerialNo = toBeUpdated.getPaySerialNo();

        Response<Payment> rPayment = paymentReadService.findByOutId(outId);
        if (!rPayment.isSuccess()) {
            log.error("failed to find payment(outId={}), error code:{}", outId, rPayment.getError());
            throw new JsonResponseException(rPayment.getError());
        }
        Payment payment = rPayment.getResult();
        Long paymentId = payment.getId();

        if (java.util.Objects.isNull(payment.getStatus())) {
            log.error("{} fail to judge payment[id=>{}, serial=>{}] status[{}]", LogUtil.getClassMethodName(), paymentId, paySerialNo, payment.getStatus());
            throw new RuntimeException(Translate.of("不可测的支付单状态[%s] -> null", payment.getId()));
        }
        switch (OrderStatus.fromInt(payment.getStatus())) {
            case PAID:
                //如果支付流水号相等, 表示收到支付回调的重复消息, 忽略即可
                if (Objects.equal(paySerialNo, payment.getPaySerialNo())) {
                    log.info("paySerialNo({}) for Payment(id={}) has been processed, skip", paySerialNo, paymentId);
                    return true;
                } else { //如果支付流水号不等, 表示出错了,
                    log.error("oops...., payment(id={}) has already been paid, and its paySerialNo is : {}, " +
                            " but is different from this one:{}", paymentId, payment.getPaySerialNo(), paySerialNo);
                    throw new JsonResponseException("payment.serialNo.mismatch");
                }
            case NOT_PAID:
                log.info("[Payment](onPay) mark payment:{} paid", payment.getId());
                final Date paidAt = MoreObjects.firstNonNull(toBeUpdated.getPaidAt(), new Date());
                Response<Boolean> r = self.payCallbackInNewTransaction(paymentId, paySerialNo, paidAt, OrderStatus.PAID.getValue(), toBeUpdated.getPayResponse());
                if (!r.isSuccess()) {
                    log.error("failed to update paySerialNo to {} for  Payment(id={}), error code:{}",
                            paySerialNo, paymentId, r.getError());
                    throw new JsonResponseException(r.getError());
                }
                //触发订单支付事件
                EventSender.sendApplicationEvent(new OrderPaymentEvent(paymentId));
                //触发生成结算事件
                if (!"Integral-pay".equals(payment.getChannel())) {
                    EventSender.sendApplicationEvent(new PaymentSettleEvent(payment.getChannel(), paymentId, toBeUpdated.getOutId(), toBeUpdated.getPaySerialNo(), toBeUpdated.getPaidAt()));
                }

                List<Long> orderIds = paymentReadService.findOrderIdsByPaymentId(paymentId).getResult().stream().filter(op -> op.getOrderLevel() == OrderLevel.SHOP)
                        .map(OrderRelation::getOrderId).collect(Collectors.toList());
                EventSender.sendApplicationEvent(new PaymentPaidEvent(orderIds, paymentId, OrderStatus.PAID.getValue()));
                return true;
            case BUYER_CANCEL:
                // 订单自动关闭了, 先将支付单状态写入到自动待退款, 然后等待系统自动退款
                Payment update = new Payment();
                update.setId(paymentId);
                update.setStatus(OrderStatus.PAYMENT_CLOSED_WAIT_REFUND.getValue());
                paymentWriteService.update(update);
            case PAYMENT_CLOSED_WAIT_REFUND:
                // 订单已经处于待关闭状态了, 直接返回true
                return true;
            default:
                log.error("{} failed to update status of payment(id={}) due to its abnormal status({}) ", LogUtil.getClassMethodName(),
                        paymentId, payment.getStatus());
                throw new RuntimeException(Translate.of("不可测的支付单状态[%s]->[%s]", payment.getId(), payment.getStatus()));
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Response<Boolean> payCallbackInNewTransaction(Long paymentId, String paySerialNo, Date paidAt, Integer status, String payResponse) {
        return paymentWriteService.payCallback(paymentId, paySerialNo, paidAt, status, payResponse);
    }

    private List<SkuOrder> findSkuOrdersByIds(List<Long> orderIds) {
        List<SkuOrder> skuOrders;
        Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByIds(orderIds);
        if (!rSkuOrders.isSuccess()) {
            log.error("failed to find sku orders by ids({}), error code:{}", orderIds, rSkuOrders.getError());
            throw new JsonResponseException(rSkuOrders.getError());
        }
        skuOrders = rSkuOrders.getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            log.error("no sku order found by ids({})", orderIds);
            throw new JsonResponseException("order.not.found");
        }
        return skuOrders;
    }

    private List<ShopOrder> findShopOrdersByIds(List<Long> orderIds) {
        Response<List<ShopOrder>> rShopOrders = shopOrderReadService.findByIds(orderIds);
        if (!rShopOrders.isSuccess()) {
            log.error("failed to find shop orders by ids({}), error code:{}", orderIds, rShopOrders.getError());
            throw new JsonResponseException(rShopOrders.getError());
        }
        List<ShopOrder> shopOrders = rShopOrders.getResult();
        if (CollectionUtils.isEmpty(shopOrders)) {
            log.error("no shop order found by ids({})", orderIds);
            throw new JsonResponseException("order.not.found");
        }
        return shopOrders;
    }

    public Either<Boolean> closePayment(long paymentId) {
        Payment payment = paymentReadService.findById(paymentId).getResult();
        if (payment == null) {
            log.info("[PaymentLogic](closePayment) failed to query payment By paymentId:{}", paymentId);
            return Either.error(String.format("fail.query.payment for paymentId:%d", paymentId));
        }
        PaymentCloseParams paymentCloseParams = packPaymentCloseParams(payment).orElseThrow(() -> new JsonResponseException(new Translate("创建退款数据失败").toString()));
        PayChannel payChannel = channelRegistry.findChannel(payment.getChannel());
        try {
            TradeResult tradeResult = payChannel.paymentClose(paymentCloseParams);
            log.info("[PaymentLogic](closePayment) close payment result:{}", tradeResult);
            return tradeResult.isFail() ? Either.error(tradeResult.getError()) : Either.ok(true);
        } catch (Exception ex) {
            log.info("[PaymentLogic](closePayment) close payment error for paymentId:{}", payment.getId());
            ex.printStackTrace();
            return Either.error("fail in execute close payment:" + paymentId);
        }
    }

    public Either<Boolean> refundPaymentPaidAfterClosed(Long paymentId) {
        try {
            log.info("自动退款[{}]", paymentId);
            Payment payment = paymentReadService.findById(paymentId).getResult();
            log.info("支付单将自动退款[{}]", payment);
            OrderStatus status = OrderStatus.fromInt(payment.getStatus());
            if (status != OrderStatus.PAYMENT_CLOSED_WAIT_REFUND) {
                return Either.error(Translate.of("支付单状态[%s]已经不适合自动退回"));
            }
            PayChannel channel = channelRegistry.findChannel(payment.getChannel());
            TradeRequest tradeRequest = channel.refundRequest(buildRefundRequest(payment));
            Refund refund = refundReadService.findByTradeNo(payment.getOutId()).getResult()
                    .stream().filter(agreeRefund -> java.util.Objects.equals(agreeRefund.getStatus(), OrderStatus.REFUND_APPLY_AGREED.getValue()))
                    .findAny().orElseThrow(() -> new RuntimeException(Translate.of("关联退款单查找失败")));
            if (tradeRequest.isSuccess() || java.util.Objects.equals("订单已全额退款", tradeRequest.getError())) {
                refundWriteService.updateStatusAndRefundAt(refund.getId(), OrderStatus.REFUND.getValue(), new Date());
                paymentWriteService.updateStatus(paymentId, OrderStatus.PAYMENT_CLOSED_COMPLETE_REFUND.getValue());
                return Either.ok(true);
            }
            return Either.error(tradeRequest.getError());
        } catch (Exception e) {
            log.error("{} fail to refund the payment[{}]", LogUtil.getClassMethodName(), paymentId, e);
            return Either.error(e);
        }
    }

    /**
     * 自动创建退款单和生成退款参数
     *
     * @param payment 支付单
     * @return 退款单
     */
    private RefundParams buildRefundRequest(Payment payment) {
        Optional<Refund> existsRefund = refundReadService.findByTradeNo(payment.getOutId()).getResult()
                .stream().filter(agreed -> java.util.Objects.equals(agreed.getStatus(), OrderStatus.REFUND_APPLY_AGREED.getValue()))
                .findFirst();
        BaseUser paymentBuyer = findPaymentBuyer(payment);
        Shop paymentShop = shopCacheHolder.findShopById(Long.parseLong(payment.getPayAccountNo()));
        Refund refund = existsRefund.orElseGet(() ->
                RefundWrapFromPaymentApi.wrapRefundFromPayment(paymentBuyer
                        , Translate.of("订单关闭后自动退款")
                        , paymentShop.getId()
                        , paymentShop.getName()
                        , payment
                        , OrderStatus.REFUND_APPLY_AGREED.getValue()
                        , "[]"
                        , 1
                        , Refund.RefundType.ON_SALE_REFUND.value()
                ));
        if (existsRefund.isEmpty()) {
            // 使用支付的金额作为退款金额
            refund.setFee(payment.getFee());
            List<Long> orderIds = paymentReadService.findOrderIdsByPaymentId(payment.getId()).getResult().stream().map(OrderRelation::getOrderId).collect(Collectors.toList());
            refundWriteService.create(refund, orderIds, OrderLevel.SHOP);
        }

        // update refund OutId
        String refundOutId = tradeBatchNoGenerator.generateBatchNo(new Date(), refund.getId());
        Refund updateRefundOutId = new Refund();
        updateRefundOutId.setId(refund.getId());
        updateRefundOutId.setOutId(refundOutId);
        refundWriteService.update(updateRefundOutId);

        RefundParams refundParams = new RefundParams();
        refundParams.setChannel(payment.getChannel());
        refundParams.setRefundNo(refundOutId);
        refundParams.setRefundAmount(refund.getFee());
        refundParams.setRefundReason(refund.getBuyerNote());
        refundParams.setSellerNo(refund.getRefundAccountNo());
        //内部交易流水号
        refundParams.setTradeNo(refund.getTradeNo());
        //外部交易流水号
        refundParams.setPaymentCode(payment.getPaySerialNo());
        refundParams.setTotalFee(payment.getFee());
        return refundParams;
    }

    /**
     * 从订单中拉取支付单相关的购买用户信息
     *
     * @param payment 支付单
     * @return 支付用户
     */
    private BaseUser findPaymentBuyer(Payment payment) {
        User tmpUser = new User();
        for (OrderPayment orderPayment : paymentReadService.findOrderIdsByPaymentId(payment.getId()).getResult()) {
            switch (OrderLevel.fromInt(orderPayment.getOrderType())) {
                case SKU: {
                    SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId()).getResult();
                    tmpUser.setId(skuOrder.getBuyerId());
                    tmpUser.setName(skuOrder.getBuyerName());
                    return tmpUser;
                }
                case SHOP: {
                    ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId()).getResult();
                    tmpUser.setId(shopOrder.getBuyerId());
                    tmpUser.setName(shopOrder.getBuyerName());
                    return tmpUser;
                }
                case GATHER: {
                    GatherOrder gatherOrder = gatherOrderReadService.findById(orderPayment.getOrderId()).getResult();
                    tmpUser.setId(gatherOrder.getBuyerId());
                    tmpUser.setName(userProfileCacheHolder.getUserProfileByUserId(gatherOrder.getBuyerId()).getRealName());
                    return tmpUser;
                }
                default:
            }
        }
        log.error("{} fail to find user of payment[id=>{}, paySerial=>{}]", LogUtil.getClassMethodName(), payment.getId(), payment.getPaySerialNo());
        return tmpUser;
    }

    private java.util.Optional<PaymentCloseParams> packPaymentCloseParams(Payment payment) {
        PaymentCloseParams paymentCloseParams = new PaymentCloseParams();
        Matcher m;
        m = APP_ID_PATTERN.matcher(payment.getPayRequest());
        if (!m.find()) {
            return java.util.Optional.empty();
        }
        String appId = m.group(1);
        m = OUT_TRADE_NO_PATTERN.matcher(payment.getPayRequest());

        if (!m.find()) {
            return java.util.Optional.empty();
        }
        String outTradeNo = m.group(1);
        m = MCH_ID_PATTERN.matcher(payment.getPayRequest());

        if (!m.find()) {
            return java.util.Optional.empty();
        }
        String mchId = m.group(1);
        paymentCloseParams.setAppId(appId);
        paymentCloseParams.setChannel(payment.getChannel());
        paymentCloseParams.setTradeNo(outTradeNo);
        paymentCloseParams.setSellerNo(mchId);
        return java.util.Optional.of(paymentCloseParams);
    }
}
