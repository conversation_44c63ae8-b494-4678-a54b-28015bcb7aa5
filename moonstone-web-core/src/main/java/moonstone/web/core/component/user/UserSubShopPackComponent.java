package moonstone.web.core.component.user;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.enums.UserRole;
import moonstone.common.model.CommonUser;
import moonstone.shop.model.Shop;
import moonstone.user.cache.SubSellerCacheHolder;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.SubSeller;
import moonstone.weShop.model.WeShop;
import moonstone.web.core.component.StoreProxyRegisterComponent;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static org.springframework.util.ObjectUtils.isEmpty;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@Component
public class UserSubShopPackComponent {
    private final WeShopCacheHolder weShopCacheHolder;
    private final SubSellerCacheHolder subSellerCacheHolder;
    private final ShopCacheHolder shopCacheHolder;
    private final StoreProxyRegisterComponent storeProxyRegisterComponent;
    private final UserTypeBean userTypeBean;

    /**
     * 装饰子店铺信息
     *
     * @param commonUser 用户
     * @param shopId     店铺Id
     */
    public void wrap(CommonUser commonUser, Long shopId) {
        if (Objects.isNull(commonUser)) {
            return;
        }
        // fulfill the weShopId
        if (Objects.nonNull(shopId)) {
            commonUser.setWeShopId(null);
            weShopCacheHolder.findByUserIdAndShopId(commonUser.getId(), shopId)
                    .map(WeShop::getId).ifPresent(commonUser::setWeShopId);
        }
        // fulfill the shopId
        Long userIdOfShop = null;
        if (userTypeBean.isSeller(commonUser)) {
            userIdOfShop = commonUser.getId();
        } else if (userTypeBean.isSubAccount(commonUser)) {
            userIdOfShop = subSellerCacheHolder.findSubSellerByUserId(commonUser.getId()).map(SubSeller::getMasterUserId)
                    .orElse(null);
        }
        if (userIdOfShop != null) {
            Optional.ofNullable(shopCacheHolder.findShopByUserId(userIdOfShop))
                    .filter(normal -> normal.getStatus() != -1)
                    .map(Shop::getId).ifPresent(commonUser::setShopId);
        }
        // try to inject the proxy shop id
        if (isEmpty(commonUser.getShopId()) || Objects.nonNull(shopId)) {
            // 注入下级代理店铺Id
            if (Objects.nonNull(shopId)) {
                storeProxyRegisterComponent.getStoreProxyByShopIdAndUserId(shopId, commonUser.getId())
                        .map(StoreProxy::getShopId).ifPresent(commonUser::setShopId);
            } else {
                storeProxyRegisterComponent.getProxyShopIdFromUserId(commonUser.getId())
                        .stream().findFirst().ifPresent(commonUser::setShopId);
            }
        }
        if (commonUser.getWeShopId() == null && commonUser.getRoles() != null) {
            commonUser.getRoles().remove(UserRole.WE_DISTRIBUTOR.name());
        }
        if (commonUser.getShopId() == null && commonUser.getRoles() != null) {
            commonUser.getRoles().remove(UserRole.SELLER.name());
        }
    }
}
