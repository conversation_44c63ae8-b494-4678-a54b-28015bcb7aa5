package moonstone.web.core.component.cache;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.SkuOrder;
import moonstone.web.core.constants.RedisConstants;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SkuStockCacheAtRedisManager {
    @Autowired(required = false)
    private JedisPool jedisPool;

    @Resource
    private RedissonClient redissonClient;

    public void rollBackSkuOrder(List<SkuOrder> skuOrders) {
        Map<String, String> stockInRedis = new HashMap<>(skuOrders.size());
        try (Jedis jedis = jedisPool.getResource()) {
            for (SkuOrder skuOrder : skuOrders) {
                Long skuId = skuOrder.getSkuId();
                String skuIndex = RedisConstants.SKU_STOCK_PREFIX + skuId;
                Lock lock = redissonClient.getLock(RedisConstants.SKU_STOCK_LOCK_PREFIX + skuId);
                lock.lock();
                try {
                    jedis.expire(skuIndex, 2 * 60);
                    if (!jedis.exists(skuIndex)) {
                        stockInRedis.put(skuIndex, "None");
                        continue;
                    }
                    stockInRedis.put(skuIndex, jedis.get(skuIndex));
                    jedis.incrBy(skuIndex, skuOrder.getQuantity());
                } finally {
                    lock.unlock();
                }
            }
            log.debug("{} rollback skuStock at redis[{}]", LogUtil.getClassMethodName(), stockInRedis);
        }
    }

    public void removeCache(List<Long> skuIds) {
        Map<String, String> stockInRedis = new HashMap<>(8);
        try (Jedis jedis = jedisPool.getResource()) {
            skuIds = skuIds.stream().distinct().collect(Collectors.toList());
            for (Long skuId : skuIds) {
                String skuIndex = RedisConstants.SKU_STOCK_PREFIX + skuId;
                Lock lock = redissonClient.getLock(RedisConstants.SKU_STOCK_LOCK_PREFIX + skuId);
                lock.lock();
                try {
                    if (!jedis.exists(skuIndex)) {
                        stockInRedis.put(skuIndex, "None");
                        continue;
                    }
                    stockInRedis.put(skuIndex, jedis.get(skuIndex));
                    jedis.del(skuIndex);
                } finally {
                    lock.unlock();
                }
            }
            log.debug("{} delete skuStock at redis[{}]", LogUtil.getClassMethodName(), stockInRedis);
        }
    }
}