package moonstone.web.core.component.wx;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UserUtil;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ShopIdComposerByWxOpenId {
    @Autowired
    private UserWxReadService userWxReadService;
    @Autowired
    private ShopWxaReadService shopWxaReadService;

    /**
     * 如果用户OpenId存在 => 用户使用了小程序, 目前小程序与店铺Id 具有绑定关系, 因此认为其在一个店铺
     *
     * @param user   用户
     * @param openId 微信openId
     */
    public void setCurrentShopId(CommonUser user, String openId) {
        Optional.ofNullable(readShopIdFromOpenId(openId, user.getId()))
                .ifPresent(UserUtil::putCurrentShopId);
    }

    /**
     * 读取目前的店铺Id
     *
     * @param openId 微信openId
     * @param userId 用户Id
     * @return 店铺Id
     */
    private Long readShopIdFromOpenId(String openId, Long userId) {
        if (userId == null || openId == null) {
            return null;
        }
        List<UserWx> userWxList = userWxReadService.findByOpenId(openId).orElseGet(ArrayList::new)
                .stream().filter(userWx -> Objects.equals(userWx.getUserId(), userId)).collect(Collectors.toList());
        if (userWxList.size() > 1) {
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("用户出现重复的OpenId"
                    , String.format("UserId [%s] OpenId [%s] content [%s]", userId, openId, JSON.toJSONString(userWxList))
                    , EmailReceiverGroup.DEVELOPER));
        }
        try {
            return userWxList
                    .stream().map(UserWx::getAppId).distinct()
                    .map(shopWxaReadService::findByAppId)
                    .map(Response::getResult)
                    .map(ShopWxa::getShopId).filter(Objects::nonNull).findFirst().orElse(null);
        }
        catch (Exception ex){
            log.error("{} fail compose shopId from userId [{}] openId [{}]", LogUtil.getClassMethodName(), userId, openId, ex);
            return null;
        }
    }
}
