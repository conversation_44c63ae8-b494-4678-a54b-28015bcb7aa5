package moonstone.web.core.component;

import moonstone.item.api.SyncLockManager;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.locks.Lock;

@Component
public class SyncLockManagerByHazelcast implements SyncLockManager {

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Lock lock(Integer systemId, Long shopId) {
        return redissonClient.getLock(String.format("[ItemSync](%s)_shop[%s]", systemId, shopId));
    }
}
