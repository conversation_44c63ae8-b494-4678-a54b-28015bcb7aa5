package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.SingleProductCommissionEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.IsPresent;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.web.core.component.api.ProfitMakerAPIPrototype;
import moonstone.order.bo.BalanceDetailBO;
import moonstone.order.bo.ProfitResultBO;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.*;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SubStoreProfitMaker implements ProfitMakerAPIPrototype {

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Resource
    private BalanceDetailReadService balanceDetailReadService;

    @Resource
    private IntermediateInfoReadService intermediateInfoReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Override
    public OrderOutFrom suitOrderOutFrom() {
        return OrderOutFrom.SUB_STORE;
    }

    @Override
    public Boolean suitOrder(ShopOrder shopOrder) {
        return shopOrder.getOutShopId() != null &&
                suitOrderOutFrom().Code().equals(shopOrder.getOutFrom());
    }

    @Override
    public List<BalanceDetail> earnForeseeProfit(ShopOrder shopOrder, @Nullable Payment payment) {
        return calculateSubStoreMethodProfit(shopOrder);
    }

    private List<BalanceDetail> calculateSubStoreMethodProfit(ShopOrder shopOrder) {
        // 门店
        SubStore subStore = subStoreReadService.findById(Long.parseLong(shopOrder.getOutShopId())).getResult();
        if (!subStore.isAuthed()) {
            log.warn("SubStoreProfitMaker.calculateSubStoreMethodProfit, shopOrderId={}, subStoreId={}, 门店尚未审核通过",
                    shopOrder.getId(), subStore.getId());
            return Collections.emptyList();
        }

        // 导购的用户id
        Long guiderUserId = Optional.ofNullable(shopOrder.getReferenceId()).orElse(0L);

        // 服务商的用户id
        Long serviceProviderUserId = findServiceProviderUserId(shopOrder.getShopId(), subStore.getUserId());

        return calculateForeseeProfit(shopOrder, guiderUserId, subStore, serviceProviderUserId);
    }

    private Long findServiceProviderUserId(Long shopId, Long subStoreUserId) {
        return userRelationEntityReadService.findBy(subStoreUserId, UserRelationEntity.UserRelationType.SUPER, shopId)
                .getResult()
                .stream()
                .findFirst()
                .map(UserRelationEntity::getRelationId)
                .orElse(0L);
    }

    /**
     * 若门店的基础信息有配置单独的“门店分佣比例”，则门店的最终利润还要与它相乘
     *
     * @param subStoreFee
     * @param subStore
     */
    private Long extraCalculationForSubStore(SubStore subStore, Long subStoreFee) {
        if (subStoreFee == null || subStoreFee <= 0 ||
                subStore == null || subStore.getCommissionRate() == null) {
            return subStoreFee;
        }
        if (!subStore.checkCommissionRate()) {
            throw new RuntimeException(String.format("subStoreId=%s, commissionRate=%s非法的分佣比例",
                    subStore.getId(), subStore.getCommissionRate()));
        }

        return subStoreFee * subStore.getCommissionRate() / 10000;
    }

    @Override
    public List<BalanceDetail> refundProfit(ShopOrder shopOrder, Refund refund) {
        return null;
    }

    @Override
    public List<BalanceDetail> convertForeseeProfitIntoPresentProfit(ShopOrder shopOrder, @Nullable Shipment shipment) {

        var id = shopOrder.getId();
        var shopId = shopOrder.getShopId();
        List<BalanceDetail> balanceDetails = balanceDetailReadService.findUnPresentOrderProfitByOrderId(id, shopId);
        for (BalanceDetail profit : balanceDetails) {
            profit.setId(null);
            profit.setUpdatedAt(null);
            profit.setCreatedAt(null);
            profit.setType(1);
            // convert it into present profit
            profit.setStatus(profit.getStatus() | IsPresent.presentMaskBit.Present.getValue() | IsPersistAble.maskBit.PersistAble.getValue());
        }
        return balanceDetails;

    }

    /**
     * 计算并构造利润对象
     * <br/> 参照于 sub_store_profit_generator.clj 上的 foresee 方法，在它的基础上做的修改
     *
     * @param shopOrder
     * @param guiderUserId
     * @param subStore
     * @param serviceProviderUserId
     * @return
     */
    private List<BalanceDetail> calculateForeseeProfit(ShopOrder shopOrder,
                                                       Long guiderUserId, SubStore subStore, Long serviceProviderUserId) {
        try {
            // 有点迷，导购的钱先算进服务商的口袋里
            guiderUserId = serviceProviderUserId;

            // 商家平台的默认分佣配置
            var defaultRate = intermediateInfoReadService.findWithActivityByThirdAndType(
                    shopOrder.getShopId(), ThirdIntermediateType.SHOP).getResult();
            log.info("SubStoreProfitMaker.calculateForeseeProfit, shopOrderId={}, defaultRate={}", shopOrder.getId(), JSON.toJSONString(defaultRate));

            // shopOrder 对应的子订单
            var skuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
            if (CollectionUtils.isEmpty(skuOrders)) {
                throw new RuntimeException("子订单列表查询为空");
            }

            // 先以 skuOrder 为维度，计算并构建利润对象
            var balanceDetails = generateProfitBySkuOrder(skuOrders, defaultRate,
                    guiderUserId, subStore, serviceProviderUserId);

            // 再聚合成 shopOrder 维度的利润对象
            balanceDetails = groupByUser(balanceDetails);

            // 转模型，并补充其它字段的设置
            return convert(balanceDetails, shopOrder);
        } catch (Exception ex) {
            log.error("SubStoreProfitMaker.calculateForeseeProfit error, shopOrderId={} ", shopOrder.getId(), ex);
            throw ex;
        }
    }

    private List<BalanceDetail> convert(List<BalanceDetailBO> balanceDetails, ShopOrder shopOrder) {
        return balanceDetails.stream().map(bo -> {
            var target = new BalanceDetail();

            target.setUserId(bo.getUserId());
            target.setChangeFee(bo.getChangeFee());
            target.setType(ProfitType.InCome.getValue());
            target.setFee(0L);

            target.setRelatedId(shopOrder.getId());
            target.setSourceId(shopOrder.getShopId());

            target.setStatus(1 | BalanceDetail.maskBit.OrderRelated.getValue() |
                    OrderRelated.orderRelatedMask.ShopOrder.getValue() |
                    IsPersistAble.maskBit.PersistAble.getValue());
            switch (bo.getUserRole()) {
                case SUB_STORE, SERVICE_PROVIDER -> target.setStatus(target.getStatus() | BalanceDetail.SourceMark.Reach.getBitMark());
                case STORE_GUIDER -> target.setStatus(target.getStatus() | BalanceDetail.SourceMark.Linked.getBitMark());
            }

            return target;
        }).collect(Collectors.toList());
    }

    /**
     * 再聚合成 shopOrder 维度的利润对象
     *
     * @param sourceList
     * @return
     */
    private List<BalanceDetailBO> groupByUser(List<BalanceDetailBO> sourceList) {
        return sourceList.stream()
                .collect(Collectors.groupingBy(bo -> bo.getUserId() + "_" + bo.getUserRole().getCode()))
                .values()
                .stream()
                .map(this::sumUp)
                .collect(Collectors.toList());
    }

    /**
     * 金额相加，汇总成一个利润对象
     *
     * @param list
     * @return
     */
    private BalanceDetailBO sumUp(List<BalanceDetailBO> list) {
        var target = new BalanceDetailBO();
        target.setUserId(list.get(0).getUserId());
        target.setUserRole(list.get(0).getUserRole());
        target.setChangeFee(0L);

        for (var current : list) {
            target.setChangeFee(target.getChangeFee() +
                    (current.getChangeFee() == null ? 0L : current.getChangeFee()));
        }

        return target;
    }

    /**
     * 先以 skuOrder 为维度，计算并构建利润对象
     *
     * @param skuOrders
     * @param defaultRate
     * @param guiderUserId
     * @param subStore
     * @param serviceProviderUserId
     * @return
     */
    private List<BalanceDetailBO> generateProfitBySkuOrder(List<SkuOrder> skuOrders, IntermediateInfo defaultRate,
                                                           Long guiderUserId, SubStore subStore, Long serviceProviderUserId) {
        var resultList = new ArrayList<BalanceDetailBO>();

        for (var skuOrder : skuOrders) {
            // 逐个子单处理
            var subList = generateProfit(skuOrder, defaultRate, guiderUserId, subStore, serviceProviderUserId);
            if (!CollectionUtils.isEmpty(subList)) {
                resultList.addAll(subList);
            }
        }

        return resultList;
    }

    /**
     * 单个 skuOrder 生成利润列表
     *
     * @param skuOrder
     * @param defaultRate
     * @param guiderUserId
     * @param subStore
     * @param serviceProviderUserId
     * @return
     */
    private List<BalanceDetailBO> generateProfit(SkuOrder skuOrder, IntermediateInfo defaultRate,
                                                 Long guiderUserId, SubStore subStore, Long serviceProviderUserId) {
        // 获取目标分佣配置
        var targetRate = findTargetRate(skuOrder.getSkuId(), defaultRate);
        log.info("SubStoreProfitMaker.generateProfit, shopOrderId={}, skuOrderId={}, using rate={}",
                skuOrder.getOrderId(), skuOrder.getId(), JSON.toJSONString(targetRate));

        // 用来算佣的订单金额
        Long fee = skuOrder.getFee();

        // 计算各个角色的所得佣金
        var profitResult = calculateProfitResult(fee, targetRate, subStore);

        // 初步构造
        var subStoreDetail = build(subStore.getUserId(), SubStoreUserIdentityEnum.SUB_STORE, profitResult.getSubStoreFee());
        var guiderDetail = build(guiderUserId, SubStoreUserIdentityEnum.STORE_GUIDER, profitResult.getGuiderFee());
        var serviceProviderDetail = build(serviceProviderUserId, SubStoreUserIdentityEnum.SERVICE_PROVIDER,
                profitResult.getServiceProviderFee());

        // 返回
        return Lists.newArrayList(guiderDetail, subStoreDetail, serviceProviderDetail);
    }

    /**
     * 根据金额、分佣配置来计算各个角色的所得佣金
     *
     * @param fee
     * @param targetRate
     * @param subStore
     * @return
     */
    private ProfitResultBO calculateProfitResult(Long fee, IntermediateInfo targetRate, SubStore subStore) {
        // 各角色的分佣比例（当前只考虑”按比例“的分佣模式，不管”固定费用“模式）
        Long subStoreRate = targetRate.getFirstRate();
        Long guiderRate = targetRate.getSecondRate();
        Long originServiceProviderRate = targetRate.getServiceProviderRate() == null ? 0L : targetRate.getServiceProviderRate();
        Long serviceProviderRate = (10000L - subStoreRate) * originServiceProviderRate / 10000;

        // 各角色佣金
        Long subStoreFee = subStoreRate * fee / 10000;
        Long guiderFee = guiderRate * fee / 10000;
        Long serviceProviderFee = serviceProviderRate * fee / 10000;

        // 门店的额外分佣比例配置处理
        subStoreFee = extraCalculationForSubStore(subStore, subStoreFee);

        return new ProfitResultBO(guiderFee, subStoreFee, serviceProviderFee);
    }

    private BalanceDetailBO build(Long userId, SubStoreUserIdentityEnum role, Long changeFee) {
        var balanceDetail = new BalanceDetailBO();

        balanceDetail.setUserId(userId);
        balanceDetail.setUserRole(role);
        balanceDetail.setChangeFee(changeFee);

        return balanceDetail;
    }

    /**
     * 获取目标分佣配置
     *
     * @param skuId
     * @param defaultRate
     * @return
     */
    private IntermediateInfo findTargetRate(Long skuId, IntermediateInfo defaultRate) {
        var skuRate = intermediateInfoReadService.findWithActivityByThirdAndType(
                skuId, ThirdIntermediateType.SKU).getResult();
        if (skuRate == null || SingleProductCommissionEnum.CLOSE.getCode().equals(skuRate.getIsCommission())) {
            return defaultRate;
        }

        return skuRate;
    }
}
