package moonstone.web.core.component.pay.allinpayyst;

import io.terminus.pay.model.Token;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

@EqualsAndHashCode(callSuper = true)
@Data
public class AllInPayYSTToken extends Token {
    @Serial
    private static final long serialVersionUID = -7159641518350918025L;

    /**
     * 通联分配的appId
     */
    private String appId;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 私钥证书文件路径
     */
    private String certPath;

    /**
     * 私钥证书文件对应的密码
     */
    private String certPassword;

    /**
     * 公钥证书文件路径
     */
    private String tlCertPath;

    /**
     * 商家在通联里的小B账户
     */
    private String bizUserId;

    /**
     * 商家在通联里的大B账户
     */
    private String enterpriseUserId = "#yunBizUserId_B2C#";

    /**
     * 托管代付时，减去分佣对象(如：门店)所得后，剩下的订单金小B账户分得的百分比 <br/>
     * 采用万分制，即为 5000 时， 小B账户 分得余下的 50% 订单金额
     */
    private Long bizUserPercent;

    /**
     * 商家在通联上面的账户集编号（商家和其下面的分佣用户共用这个）
     */
    private String accountSetNo;

    /**
     * 集团模式：集团商户收银宝商户号
     */
    private String vspMerchantid;

    /**
     * 集团模式：收银宝子商户号
     */
    private String vspCusid;

    /**
     * 集团模式：上送集团商户号appid
     */
    private String vspAppId;

    /**
     * 要绑定的终端信息
     */
    private String vspTermid;

    /**
     * 该 token 加载的时间点
     */
    private Long tokenLoadTime;

    /**
     * 付汇提现使用的账户
     */
    private String enterpriseWithdrawAccount;
}
