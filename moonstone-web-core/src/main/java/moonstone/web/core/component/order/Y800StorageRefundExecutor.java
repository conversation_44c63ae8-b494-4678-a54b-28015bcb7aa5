package moonstone.web.core.component.order;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentCancel;
import moonstone.common.utils.Translate;
import moonstone.order.api.OutSideRefundRealExecutor;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.web.core.component.api.Y800V3Api;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class Y800StorageRefundExecutor implements OutSideRefundRealExecutor {
    @RemoteAPI
    private Y800V3Api storageRemoteAPI;

    @Autowired
    private ThirdPartyUserShopCache thirdPartyUserShopCache;

    @Autowired
    private Y800OrderIdGenerator y800OrderIdGenerator;

    @Override
    public Either<Boolean> executeRefund(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        log.info("发起Y800订单退款请求 Y800StorageRefundExecutor");
        Y800ShipmentCancel y800ShipmentCancel = new Y800ShipmentCancel();
        try (Y800V3Api api = storageRemoteAPI) {
            ThirdPartyUserShop userShop = thirdPartyUserShopCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V3.Id(), shopOrder.getShopId()))
                    .orElseThrow(() -> Translate.exceptionOf("查找帐号信息失败"));
            Map<String, String> shopExtra = userShop.getExtra();
            api.setAppId(userShop.getThirdPartyCode());
            api.setSecret(userShop.getThirdPartyKey());

            y800ShipmentCancel.setWhCode(shopExtra.get(ShopExtra.WhCode.getCode()));
            y800ShipmentCancel.setAccessCode(userShop.getExtra().get(ShopExtra.Y800StorageAccessCode.getCode()));
            y800ShipmentCancel.setReason("退款");
            y800ShipmentCancel.setThirdNo(y800OrderIdGenerator.getDeclareId(shopOrder.getId()).take());

            log.info("Y800StorageRefundExecutor.executeRefund, api.orderCancel parameter={}", JSON.toJSONString(y800ShipmentCancel));
            var result = api.orderCancelWithResult(y800ShipmentCancel);
            log.info("Y800StorageRefundExecutor.executeRefund, api.orderCancel result={}", JSON.toJSONString(result));
            if (StringUtils.isNotBlank(result.getErrorMsg())) {
                throw new RuntimeException(result.getErrorMsg());
            }
            if (result.getError() != null) {
                throw new RuntimeException(result.getError());
            }

            return Either.ok(true);
        } catch (Exception ex) {
            var success = List.of("ORDER_NOT_EXIST", "ORDER_CANCELED", "ORDER_ALREADY_CANCEL");
            if (success.contains(ex.getMessage())) {
                return Either.ok(true);
            }
            return Either.error(ex);
        }
    }

    @Override
    public ThirdPartySystem getThirdPartySystem() {
        return ThirdPartySystem.Y800_V3;
    }
}
