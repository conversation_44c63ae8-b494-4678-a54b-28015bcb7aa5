package moonstone.web.core.component.profit.app;

import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.SimpleRulerJudgeBean;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.UserWithdrawSum;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawPrinciple;
import moonstone.order.service.UserWithdrawSumManager;
import moonstone.order.service.WithdrawPrincipleManager;
import moonstone.web.core.exceptions.WithdrawOverFlowException;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;

@Component
@Slf4j
public class WithdrawAllowRulerBean implements SimpleRulerJudgeBean<WithDrawProfitApply> {
    @Autowired
    WithdrawPrincipleManager withdrawPrincipleManager;
    @Autowired
    UserWithdrawSumManager userWithdrawSumManager;
    BigDecimal HUNDREDS = BigDecimal.valueOf(100L);

    /**
     * 特化后的判断允许,如果这个提现的Id存在则持久化裁决结果,不然则只裁决
     *
     * @param aimTarget 裁决实体
     * @return 规则判断结果
     */
    @Override
    public boolean allow(WithDrawProfitApply aimTarget) {
        Long shopId = aimTarget.getSourceId();
        Long userId = aimTarget.getUserId();
        try {
            WithdrawPrinciple withdrawPrinciple = withdrawPrincipleManager.findByShopId(shopId).orElse(new WithdrawPrinciple());
            UserWithdrawSum withdrawSum = userWithdrawSumManager.findByUserIdAndShopId(userId, shopId).orElseGet(() -> {
                UserWithdrawSum userWithdrawSum = new UserWithdrawSum();
                userWithdrawSum.setUserId(userId);
                userWithdrawSum.setShopId(shopId);
                userWithdrawSum.setSum(new HashMap<>());
                userWithdrawSum.setTimes(new HashMap<>());
                return userWithdrawSum;
            });
            if (BigDecimal.valueOf(aimTarget.getFee()).divide(HUNDREDS, RoundingMode.DOWN).compareTo(withdrawPrinciple.getMoneyBottomLimit()) < 0) {
                throw new JsonResponseException(Translate.of("提现金额需要高于最低金额[%s]", withdrawPrinciple.getMoneyBottomLimit()));
            }
            if (BigDecimal.valueOf(aimTarget.getFee()).divide(HUNDREDS, RoundingMode.DOWN).compareTo(withdrawPrinciple.getStaticServiceFee()) < 0) {
                throw new JsonResponseException(Translate.of("提现金额需要高于基础服务费[%s]", withdrawPrinciple.getStaticServiceFee()));
            }
            if (withdrawPrinciple.getMoneyMaxLimit() != null && BigDecimal.valueOf(aimTarget.getFee()).divide(HUNDREDS, RoundingMode.DOWN).compareTo(withdrawPrinciple.getMoneyMaxLimit()) > 0) {
                throw new JsonResponseException(Translate.of("提现金额需要低于最高金额[%s]", withdrawPrinciple.getMoneyBottomLimit()));
            }

            for (WithdrawPrinciple.IndexEnum index : WithdrawPrinciple.IndexEnum.values()) {
                BigDecimal moneyMax = withdrawPrinciple.getMoneyLimit().getOrDefault(index.getIndex(), BigDecimal.valueOf(Long.MAX_VALUE));
                Long countMax = withdrawPrinciple.getTimeLimit().getOrDefault(index.getIndex(), Long.MAX_VALUE);
                BigDecimal moneyUsed = withdrawSum.getSum().getOrDefault(index.getIndex(), BigDecimal.valueOf(0L));
                Long timeUsed = withdrawSum.getTimes().getOrDefault(index.getIndex(), 0L);
                switch (index) {
                    case DAY: {
                        if (withdrawSum.getLastWithdrawAt() == null || new DateTime(withdrawSum.getLastWithdrawAt()).getDayOfYear() != new DateTime(new Date()).getDayOfYear()) {
                            timeUsed = 0L;
                            moneyUsed = BigDecimal.ZERO;
                        }
                        break;
                    }
                    case YEAR: {
                        if (withdrawSum.getLastWithdrawAt() == null || new DateTime(withdrawSum.getLastWithdrawAt()).getYear() != new DateTime(new Date()).getYear()) {
                            timeUsed = 0L;
                            moneyUsed = BigDecimal.ZERO;
                        }
                        break;
                    }
                    case MONTH: {
                        if (withdrawSum.getLastWithdrawAt() == null || new DateTime(withdrawSum.getLastWithdrawAt()).getMonthOfYear() != new DateTime(new Date()).getMonthOfYear()) {
                            timeUsed = 0L;
                            moneyUsed = BigDecimal.ZERO;
                        }
                        break;
                    }
                    default:
                        break;
                }
                moneyUsed = moneyUsed.add(BigDecimal.valueOf(aimTarget.getFee()).divide(HUNDREDS, RoundingMode.DOWN));
                timeUsed++;
                if (timeUsed > countMax) {
                    log.debug("{} refused apply:{} for timeUsed:{} moneyUsed:{}", LogUtil.getClassMethodName(), aimTarget, timeUsed, moneyMax);
                    throw new WithdrawOverFlowException(Translate.of("当前可提现次数已超限"));
                }
                if (moneyUsed.compareTo(moneyMax) > 0) {
                    log.debug("{} refused apply:{} for timeUsed:{} moneyUsed:{}", LogUtil.getClassMethodName(), aimTarget, timeUsed, moneyMax);
                    throw new WithdrawOverFlowException(Translate.of("当前提现金额已超出单日或者单月可提现额度"));
                }
                withdrawSum.getSum().put(index.getIndex(), moneyUsed);
                withdrawSum.getTimes().put(index.getIndex(), timeUsed);
            }
            withdrawSum.setLastWithdrawAt(new Date());
            if (aimTarget.getId() != null) {
                userWithdrawSumManager.save(withdrawSum);
            }
            return true;
        } catch (WithdrawOverFlowException withdrawOverFlowException) {
            throw new JsonResponseException(withdrawOverFlowException.getMessage());
        } catch (Exception ex) {
            log.error("{} failed to judge the withdraw:{}", LogUtil.getClassMethodName(), aimTarget, ex);
            return false;
        }
    }
}
