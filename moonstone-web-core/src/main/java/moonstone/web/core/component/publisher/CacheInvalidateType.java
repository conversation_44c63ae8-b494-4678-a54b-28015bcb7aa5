package moonstone.web.core.component.publisher;

import com.google.common.base.Objects;

/**
 * 失效缓存类型
 * Author:cp
 * Created on 8/1/16.
 */
public enum CacheInvalidateType {

    /**
     * 营销活动
     */
    PROMOTION("invalidate-promotion-cache"),

    /**
     * 登录用户
     */
    LOGIN_USER("invalidate-login-user-cache"),

    /**
     * 其他
     */
    OTHER("invalidate-other-cache");

    private final String key;

    CacheInvalidateType(String key) {
        this.key = key;
    }

    public static CacheInvalidateType fromKey(String key) {
        for (CacheInvalidateType cacheInvalidateType : CacheInvalidateType.values()) {
            if (Objects.equal(cacheInvalidateType.key, key)) {
                return cacheInvalidateType;
            }
        }
        return OTHER;
    }

    @Override
    public String toString() {
        return key;
    }
}
