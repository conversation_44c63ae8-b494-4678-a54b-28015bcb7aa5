package moonstone.web.core.component.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxReadService;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UserWxCacheHolder {
    @Autowired
    UserWxReadService userWxReadService;
    @Autowired
    SqlSession sqlSession;


    LoadingCache<UserAndAppId, UserWx> userWxCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build(userAndAppId -> userWxReadService.findByAppIdAndUserId(userAndAppId.getAppId(), userAndAppId.getUserId()).take().orElse(null));
    LoadingCache<UserIdAndShopId, UserWx> userIdAndShopIdUserWxLoadingCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build(userIdAndShopId -> sqlSession.selectOne("UserWx.findByUserIdAndShopId", Map.of("userId", userIdAndShopId.userId()
                    , "shopId", userIdAndShopId.shopId())));

    public UserWx findByUserIdAndAppId(Long userId, String appId) {
        return userWxCache.get(new UserAndAppId(userId, appId));
    }

    public UserWx findByUserIdAndShopId(Long userId, Long shopId) {
        return userIdAndShopIdUserWxLoadingCache.get(new UserIdAndShopId(userId, shopId));
    }

    record UserIdAndShopId(Long userId, Long shopId) {

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class UserAndAppId {
        Long userId;
        String appId;
    }
}