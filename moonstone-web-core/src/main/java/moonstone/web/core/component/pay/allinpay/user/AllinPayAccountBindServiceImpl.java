package moonstone.web.core.component.pay.allinpay.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.OpenClient;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.enums.UserRole;
import moonstone.common.exception.WxRuntimeException;
import moonstone.common.utils.UserUtil;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.web.core.component.pay.allinpay.enums.AllInPayAcctTypeEnum;
import moonstone.web.core.component.pay.allinpay.user.res.*;
import moonstone.showcase.mq.message.SetCompanyInfoBackByH5Message;
import moonstone.web.op.allinpay.lock.RedissonDistributedLock;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.CopyUtil;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreWriteService;
import moonstone.user.entity.UserBindAllinPay;
import moonstone.user.service.UserBindAllinPayService;
import moonstone.user.service.UserWriteService;
import moonstone.web.core.component.pay.allinpay.user.dto.AllinPayCreateAccountDTO;
import moonstone.web.core.component.pay.allinpay.user.dto.AllinPayLoginUserRegisterAccountDTO;
import moonstone.web.core.component.pay.allinpay.user.req.*;
import moonstone.web.core.component.pay.allinpay.util.AllinPayAccountUtil;
import moonstone.web.op.allinpay.AllinPayConfigConstant;
import moonstone.web.op.allinpay.constant.AllinPayConstant;
import moonstone.web.op.allinpay.dto.*;
import moonstone.web.op.allinpay.enums.AllinPayRoleTypeEnum;
import moonstone.web.op.allinpay.enums.PayTagTypeEnum;
import moonstone.web.op.allinpay.service.IAllinpayService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@Service
public class AllinPayAccountBindServiceImpl implements AllinPayAccountBindService {


    @Resource
    private IAllinpayService allinpayService;

    @Resource
    private UserBindAllinPayService userBindAllinPayService;

    @Resource
    private UserWriteService userWriteService;

    @Resource
    private RedissonDistributedLock lock;

    @Resource
    private HttpServletRequest request;

    @Resource
    private SubStoreWriteService subStoreWriteService;
    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private SubStoreWriteCardInfoHandler subStoreWriteCardInfoHandler;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private BalanceDetailManager balanceDetailManager;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> initAllinPayUser(AllinPayLoginUserRegisterAccountDTO dto) {
        CommonUser user = getLoginUser();

        AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);
        dto.setAllinPayRoleTypeByTenantType(allinPayRoleTypeEnum.getType());
        AllInPayAcctTypeEnum acctTypeEnum = getAcctTypeByRequest();
        UserBindAllinPay userBindAllinPay = tlMemberRegister(acctTypeEnum, user, dto);
        return Result.success("ok");
    }

    private AllInPayAcctTypeEnum getAcctTypeByRequest() {
        String appType = httpServletRequest.getHeader("x-app-type");
        if(StringUtils.isEmpty(appType)){
            log.info("appType为空 默认微信登录");
            return AllInPayAcctTypeEnum.weChatMiniProgram;
        }
        if("0".equals(appType)){
            return AllInPayAcctTypeEnum.weChatMiniProgram;
        }else if("1".equals(appType)){
            return AllInPayAcctTypeEnum.aliPayService;
        }
        throw new RuntimeException("非法的appType" + appType);
    }
    private AppTypeEnum getAppTypeEnumByRequest() {
        String appType = httpServletRequest.getHeader("x-app-type");
        if(StringUtils.isEmpty(appType)){
            log.info("appType为空 默认微信登录.");
            return AppTypeEnum.WECHAT;
        }
        AppTypeEnum appTypeEnum = AppTypeEnum.from(Integer.valueOf(appType));
        return appTypeEnum;
    }

    @Async
    @Override
    public UserBindAllinPay tlMemberRegisterAsync(AllInPayAcctTypeEnum acctTypeEnum, CommonUser user, AllinPayLoginUserRegisterAccountDTO registerAccountDTO) {
        AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);
        registerAccountDTO.setAllinPayRoleTypeByTenantType(allinPayRoleTypeEnum.getType());
        return tlMemberRegister(acctTypeEnum, user, registerAccountDTO);
    }

    /**
     * 为当前登陆账户注册会员
     * @param user
     * @param registerAccountDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public UserBindAllinPay tlMemberRegister(AllInPayAcctTypeEnum acctTypeEnum, CommonUser user, AllinPayLoginUserRegisterAccountDTO registerAccountDTO) {
        log.info("【通联】为当前登陆人创建会员(含静默开户) userId:{} dto:{}",user.getId(), JSON.toJSONString(registerAccountDTO));

//        AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);

        // 创建通联支付会员
        AllinPayCreateAccountDTO createAccountDTO = new AllinPayCreateAccountDTO();
        createAccountDTO.setShopId(registerAccountDTO.getShopId());
        createAccountDTO.setAllinPayRoleTypeByTenantType(registerAccountDTO.getAllinPayRoleTypeByTenantType());

        // 此处作为控制支付标识的统一入口
        if(AllinPayRoleTypeEnum.PERSON.getType().equals(registerAccountDTO.getAllinPayRoleTypeByTenantType())){
            if(PayTagTypeEnum.bindAcctTag(user)){
                /**
                 * weChatPublic-微信公众号
                 * weChatMiniProgram -微信小程序
                 * aliPayService -支付宝生活号
                 * unionPayjs -银联JS
                 * 操作类型是“set”必须上送
                 */
//                createAccountDTO.setAcctType("weChatMiniProgram");
                createAccountDTO.setAcctType(acctTypeEnum.getCode());
                /**
                 * openId appId 有传递过来则入库，无则后续需要设置openId或者绑定手机号
                 */
                createAccountDTO.setAcct(registerAccountDTO.getOpenId());
                createAccountDTO.setAppId(registerAccountDTO.getAppId());
                log.info("通联个人会员 绑定支付标识");
            }else{
                log.info("通联个人会员 不绑定支付标识");
            }
        }else{
            log.info("通联企业会员 不绑定支付标识 企业会员不允许下单");
        }

        String lockKey = "Parana:" + user.getId();
        return lock.lock(lockKey, () -> {
            Result<UserBindAllinPay> result = createSyncAccount(user, createAccountDTO);
            if(result.isSuccess()){
                return result.getData();
            }else{
                throw new ServiceException(result.getErrorMsg());
            }
        });
    }

    /**
     * 为指定用户创建会员
     *
     * @param user
     * @param createAccountDTO
     * @return
     */
    private Result<UserBindAllinPay> createSyncAccount(CommonUser user, AllinPayCreateAccountDTO createAccountDTO) {
        Long userId = user.getId();
        UserBindAllinPay dbAccount = userBindAllinPayService.getByShopIdAndUserIdAndAllinUserType(createAccountDTO.getShopId(), user.getId(), createAccountDTO.getAllinPayRoleTypeByTenantType());
        if(dbAccount != null){
            if(AllinPayRoleTypeEnum.COMPANY.getType().equals(createAccountDTO.getAllinPayRoleTypeByTenantType())){
                log.info("企业会员 暂时先不设置支付标识");
                return Result.data(dbAccount);
            }
            if(StringUtils.isNotEmpty(createAccountDTO.getAcct())){
                /**
                 * 这里没有过滤已经设置过的openId，可以加个过滤层
                 * 通商云只需要openId（acct） 且可绑定多个  同一个不能重复绑定
                 * TODO 绑定多个的情况下  数据库只记录最后一个
                 */
                // 客户端获取openId时每次都给该用户设置支付标示
                ApplyBindAcctDTO acctDTO = new ApplyBindAcctDTO();
                acctDTO.setBizUserId(dbAccount.getBizUserId());
                // 设置支付标识
                acctDTO.setOperationType("set");
                acctDTO.setAcctType(createAccountDTO.getAcctType());
                acctDTO.setAcct(createAccountDTO.getAcct());
                // openIdA设置给用户A后，再次设置会会提示"已经设置"。这里不会抛出异常
                Result<String> result = allinpayService.applyBindAcct(createAccountDTO.getShopId(), acctDTO);
                if(result.isSuccess() || result.getErrorMsg().contains("支付账户用户标识已绑定")){
                    UserBindAllinPay updateObject = new UserBindAllinPay();
                    updateObject.setId(dbAccount.getId());
                    updateObject.setAcct(createAccountDTO.getAcct());
                    updateObject.setAppId(createAccountDTO.getAppId());
                    updateObject.setUpdatedAt(new Date());
                    userBindAllinPayService.updateById(updateObject);
                }
            }
            return Result.data(dbAccount);
        }

        String bizUserId = getBizUserId(String.valueOf(userId));

        Result<String> result = allinpayRegister(bizUserId, createAccountDTO);

        if(!result.isSuccess()){
            log.error("注册第三方账户失败 参数：{}, result:{}", JSON.toJSONString(createAccountDTO),JSON.toJSONString(result));
            return Result.fail(result.getErrorMsg());
        }

//        {"userId":"ab9be5c1-328b-42ca-8a55-cd0f52a495b4","bizUserId":"BUYER4208000000001"}
        Date now = new Date();
        UserBindAllinPay bindAllinPay = new UserBindAllinPay();
        bindAllinPay.setBindStep(AllinPayConstant.BindStep.REGISTER);
        bindAllinPay.setStepStatus(Boolean.FALSE);
        bindAllinPay.setBizUserId(bizUserId);
        // 不记录当前登录人的角色
        bindAllinPay.setRoleType("");
        // 以入参为准
        bindAllinPay.setAllinpayRoleType(createAccountDTO.getAllinPayRoleTypeByTenantType());

        bindAllinPay.setShopId(createAccountDTO.getShopId());
        bindAllinPay.setUserId(userId);
        bindAllinPay.setAllinpayUserId(result.getData());
        bindAllinPay.setAcct(createAccountDTO.getAcct());
        bindAllinPay.setAppId(createAccountDTO.getAppId());
        bindAllinPay.setAcctType(createAccountDTO.getAcctType());
        bindAllinPay.setCreatedAt(now);
        bindAllinPay.setUpdatedAt(now);
        boolean res = userBindAllinPayService.save(bindAllinPay);

        // 创建的是启用的通联会员：检测是否要修改门店表冗余的会员类型
        if(bindAllinPay.getIsEnabled()){
            subStoreWriteCardInfoHandler.checkAndUpdateAllinPayRoleTypeIfIsSub(createAccountDTO.getShopId(), userId, bindAllinPay.getAllinpayRoleType());
        }

//        userWriteService.updateBizUserId(user.getId(), bizUserId);

        if(res){
            return Result.data(bindAllinPay);
        }else{
            return Result.fail("保存记录失败");
        }
    }

    private Result<String> allinpayRegister(String bizUserId, AllinPayCreateAccountDTO createAccountDTO) {
        if(AllinPayRoleTypeEnum.PERSON.getType().equals(createAccountDTO.getAllinPayRoleTypeByTenantType())){
//			List<UserBindAllinPay> accountList = UserBindAllinPayService.list(Wrappers.<UserBindAllinPay>lambdaQuery()
//				.eq(UserBindAllinPay::getAcct, createAccountDTO.getAcct())
//				.eq(UserBindAllinPay::getAppId, createAccountDTO.getAppId())
//				.eq(UserBindAllinPay::getIsDeleted, PmsConstant.DB_NOT_DELETED));
//			if(!CollectionUtils.isEmpty(accountList)){
//				return R.fail("该openId已经被使用");
//			}

            CreateCustomerMemberDTO createCustomerMemberDTO = new CreateCustomerMemberDTO();
            createCustomerMemberDTO.setBizUserId(bizUserId);
            createCustomerMemberDTO.setSource(AllinPayConstant.Source.MOBILE);
            // 个人会员
            createCustomerMemberDTO.setMemberType(AllinPayRoleTypeEnum.PERSON.getType());
            createCustomerMemberDTO.setAcct(createAccountDTO.getAcct());
            createCustomerMemberDTO.setAcctType(createAccountDTO.getAcctType());
            return allinpayService.createCustomerMember(createAccountDTO.getShopId(), createCustomerMemberDTO);
        }else {
            CreateMemberDTO createMemberDTO = new CreateMemberDTO();
            createMemberDTO.setBizUserId(bizUserId);
            // 企业会员
            createMemberDTO.setMemberType(AllinPayRoleTypeEnum.COMPANY.getType());
            createMemberDTO.setSource(AllinPayConstant.Source.PC);
            Result<String> r = allinpayService.createMember(createAccountDTO.getShopId(), createMemberDTO);
            if(!r.isSuccess()){
                throw new ServiceException(r.getErrorMsg());
            }
            return r;
        }
    }

    private String getBizUserId(String accountId) {
        // random得到的是一个double型的值
        int number = new Random().nextInt(9000)+1000;

//        String ruleType = StringUtils.leftPad(String.valueOf(roleType),2,"0");
        String _accountId = StringUtils.leftPad(accountId,9,"0");
        String bizUserId = "TSY" + String.valueOf(number) + _accountId;
        return bizUserId;
    }

    /**
     * 发送验证码
     *  pms个人组织｜pms企业组织｜pms租客 未注册通联会员时，自动创建
     *
     * @param sendPhoneDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> sendPhoneCode(AllinPaySendPhoneCodeDTO sendPhoneDTO) {
        CommonUser user = getLoginUser();

        log.info("发送验证码 {} : {}", user.getId(), JSON.toJSONString(sendPhoneDTO));

        UserBindAllinPay account = userBindAllinPayService.getPersonByShopIdAndUserId(sendPhoneDTO.getShopId(), user.getId());
        if(account == null){
            AllinPayLoginUserRegisterAccountDTO registerAccountDTO =  new AllinPayLoginUserRegisterAccountDTO(sendPhoneDTO.getShopId());
            AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);
            registerAccountDTO.setAllinPayRoleTypeByTenantType(allinPayRoleTypeEnum.getType());
            AllInPayAcctTypeEnum acctTypeEnum = getAcctTypeByRequest();
            account = tlMemberRegister(acctTypeEnum, user, registerAccountDTO);
        }
        VerificationCodeDTO verificationCodeDTO = new VerificationCodeDTO();
        verificationCodeDTO.setBizUserId(account.getBizUserId());
        verificationCodeDTO.setPhone(sendPhoneDTO.getPhone());

        Long verificationCodeType = sendPhoneDTO.getVerificationCodeType() == null ? 9 : sendPhoneDTO.getVerificationCodeType();
        verificationCodeDTO.setVerificationCodeType(verificationCodeType);

        Result<String> result = allinpayService.sendVerificationCode(sendPhoneDTO.getShopId(), verificationCodeDTO);
        if(!result.isSuccess()){
            return Result.fail(result.getErrorMsg());
        }
        return Result.data(true);
    }


    /**
     * 绑定手机号
     * @param bindingPhoneDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> bindingPhone(AllinPayBindingPhoneDTO bindingPhoneDTO) {
        CommonUser user = getLoginUser();
        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getPersonByShopIdAndUserId(bindingPhoneDTO.getShopId(), user.getId());
        if(userBindAllinPay == null){
            return Result.fail("未注册通联会员");
        }

        if(StringUtils.isNotBlank(userBindAllinPay.getBindPhone())){
            return Result.fail("当前已绑定手机号 不允许再绑定");
        }

        log.info("绑定手机号 {} : {}", user.getId(), JSON.toJSONString(bindingPhoneDTO));

        // 先进行解绑支付标识（手机号）操作
        unbindingAcct(userBindAllinPay);

        BindPhoneDTO bindPhoneDTO = new BindPhoneDTO();
        bindPhoneDTO.setBizUserId(userBindAllinPay.getBizUserId());
        bindPhoneDTO.setPhone(bindingPhoneDTO.getPhone());
        bindPhoneDTO.setVerificationCode(bindingPhoneDTO.getVerificationCode());

        Result<String> result = allinpayService.bindPhone(bindingPhoneDTO.getShopId(), bindPhoneDTO);
        if(result.isSuccess()){
            AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);

            UserBindAllinPay updateEntity = new UserBindAllinPay();
            updateEntity.setId(userBindAllinPay.getId());
            if(AllinPayRoleTypeEnum.COMPANY.getType().equals(updateEntity.getAllinpayRoleType())){
                // 企业会员
                updateEntity.setBindStep(AllinPayConstant.BindStep.OVER);
                updateEntity.setStepStatus(Boolean.TRUE);
            }else{
                updateEntity.setBindStep(AllinPayConstant.BindStep.BIND_PHONE);
            }
            updateEntity.setBindPhone(bindingPhoneDTO.getPhone());
            updateEntity.setUpdatedAt(new Date());

            // TODO  api与save会有一致性问题（api不允许重复绑定）
            userBindAllinPayService.updateById(updateEntity);
            return Result.data(true);
        }else{
            return Result.fail(result.getErrorMsg());
        }
    }

    private void unbindingAcct(UserBindAllinPay account) {
        if(StringUtils.isEmpty(account.getAcct())){
            log.info("本地支付标识为空 不调用解绑手机号接口");
            return;
        }

        UnBindPhoneDTO unBindPhoneDTO = new UnBindPhoneDTO();
        unBindPhoneDTO.setBizUserId(account.getBizUserId());
        if(StringUtils.isNotEmpty(account.getBindPhone())){

        }else if(StringUtils.isNotEmpty(account.getAcct())){
            // 解绑支付标识
            // ...
            unBindPhoneDTO.setPhone("***********");
            unBindPhoneDTO.setVerificationCode("");

            // 注意不能清理本地acct：解绑手机号并不会导致支付标识的解绑，已绑定的支付标识重调用依旧会报错“支付账户用户标识已绑定”
//            updateEntity.setAcct("");
//            updateEntity.setAppId("");
        }

        Result<String> result = allinpayService.unbindPhone(account.getShopId(), unBindPhoneDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> setCompanyInfo(AllinPaySetCompanyInfoDTO companyInfoDTO) {
        CommonUser user = getLoginUser();
        log.info("设置企业信息 {} : {}", user.getId(), JSON.toJSONString(companyInfoDTO));

        UserBindAllinPay dbEntity = userBindAllinPayService.getCompanyByShopIdAndUserId(companyInfoDTO.getShopId(), user.getId());

        if(dbEntity == null){
            // 企业组织 未创建过记录则新建企业会员
            AllinPayLoginUserRegisterAccountDTO registerAccountDTO = new AllinPayLoginUserRegisterAccountDTO(companyInfoDTO.getShopId());
            registerAccountDTO.setAllinPayRoleTypeByTenantType(AllinPayRoleTypeEnum.COMPANY.getType());
            AllInPayAcctTypeEnum acctTypeEnum = getAcctTypeByRequest();
            dbEntity = tlMemberRegister(acctTypeEnum, user, registerAccountDTO);
        }

        CompanyInfoDTO dto = new CompanyInfoDTO();
        dto.setBizUserId(dbEntity.getBizUserId());
        CompanyBaseInfoDTO infoDTO = new CompanyBaseInfoDTO();
        infoDTO.setAccountNo(companyInfoDTO.getAccountNo());
        infoDTO.setBankName(companyInfoDTO.getBankName());
        infoDTO.setParentBankName(companyInfoDTO.getParentBankName());
        infoDTO.setUniCredit(companyInfoDTO.getCompanyCode());
        infoDTO.setCompanyName(companyInfoDTO.getCompanyName());
        infoDTO.setUnionBank(companyInfoDTO.getUnionBank());

        infoDTO.setLegalName(companyInfoDTO.getLegalName());
        infoDTO.setIdentityType(1);
        infoDTO.setLegalIds(companyInfoDTO.getLegalIds());
        infoDTO.setLegalPhone(companyInfoDTO.getLegalPhone());
        dto.setCompanyBasicInfo(infoDTO);

        UserBindAllinPay updateEntity = new UserBindAllinPay();
        updateEntity.setId(dbEntity.getId());
        updateEntity.setCompanyInfo(JSON.toJSONString(companyInfoDTO));
        updateEntity.setUpdatedAt(new Date());
        updateEntity.setBindStep(AllinPayConstant.BindStep.SET_COMPANY_INFO_WAIT_BACK);
        userBindAllinPayService.updateById(updateEntity);

        /**
         * TODO 可能出现本地事务未提交，第三方请求就发布过来的情况。
         */
        Result<String> result = allinpayService.setCompanyInfo(dbEntity.getShopId(), dto);
        if(!result.isSuccess()){
//            if(result.getErrorMsg().contains("当前企业用户已审核通过，无法设置企业信息")){
//                return Result.fail("当前企业用户已审核通过，无法设置企业信息");
//            }
            // 事务回滚
            throw new WxRuntimeException(result.getErrorMsg());
        }
        return Result.data(true);
    }

    @Override
    public Result<Boolean> updateCompanyInfo(AllinPaySetCompanyInfoDTO companyInfoDTO) {
        CommonUser user = getLoginUser();
        log.info("设置企业信息 {} : {}", user.getId(), JSON.toJSONString(companyInfoDTO));

        UserBindAllinPay dbEntity = userBindAllinPayService.getCompanyByShopIdAndUserId(companyInfoDTO.getShopId(), user.getId());

        CompanyInfoDTO dto = new CompanyInfoDTO();
        dto.setBizUserId(dbEntity.getBizUserId());
        CompanyBaseInfoDTO infoDTO = new CompanyBaseInfoDTO();
        infoDTO.setAccountNo(companyInfoDTO.getAccountNo());
        infoDTO.setBankName(companyInfoDTO.getBankName());
        infoDTO.setParentBankName(companyInfoDTO.getParentBankName());
        infoDTO.setUniCredit(companyInfoDTO.getCompanyCode());
        infoDTO.setCompanyName(companyInfoDTO.getCompanyName());
        infoDTO.setUnionBank(companyInfoDTO.getUnionBank());

        infoDTO.setLegalName(companyInfoDTO.getLegalName());
        infoDTO.setIdentityType(1);
        infoDTO.setLegalIds(companyInfoDTO.getLegalIds());
        infoDTO.setLegalPhone(companyInfoDTO.getLegalPhone());
        dto.setCompanyBasicInfo(infoDTO);

        Result<String> result = allinpayService.updateCompanyInfo(dbEntity.getShopId(), dto);
        if(!result.isSuccess()){
            // 事务回滚
            throw new WxRuntimeException(result.getErrorMsg());
        }
        return Result.data(true);
    }

    @Override
    public Result<CardBinVo> cardBin(AllinPayCardBinQueryDTO cardBinQueryDTO) {
        Result<String> result = allinpayService.getBankCardBin(cardBinQueryDTO.getShopId(), cardBinQueryDTO.getCardNo());
        if(!result.isSuccess()){
            return Result.fail(result.getErrorMsg());
        }

        JSONObject obj = JSON.parseObject(result.getData());
        JSONObject cardBin = obj.getJSONObject("cardBinInfo");
        CardBinVo cardBinVo = new CardBinVo();
        cardBinVo.setCardBin(cardBin.getString("cardBin"));
        cardBinVo.setCardType(cardBin.getLong("cardType"));
        cardBinVo.setBankCode(cardBin.getString("bankCode"));
        cardBinVo.setBankName(cardBin.getString("bankName"));
        cardBinVo.setCardName(cardBin.getString("cardName"));
        cardBinVo.setCardLength(cardBin.getString("cardLenth"));
        cardBinVo.setCardState(cardBin.getLong("cardState"));
        cardBinVo.setCardTypeLabel(cardBin.getString("cardTypeLabel"));
        return Result.data(cardBinVo);
    }

    @Override
    public Result<String> signContract(SignContractReq dto) {
//		UserBindAllinPay accountBindAllinPay = accountBindAllinPayService.getAllinPayAccount(pmsUser, false);
//		if(null == accountBindAllinPay){
//			return Result.fail("还没有通联帐户");
//		}
        CommonUser user = getLoginUser();
        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getPersonByShopIdAndUserId(dto.getShopId(), user.getId());
		if(null == userBindAllinPay){
			return Result.fail("还没有通联帐户");
		}
        log.info("获取签约地址 {} : {}", user.getId(), JSON.toJSONString(dto));

        Long source = AllinPayConstant.Source.PC;
//        if(StringUtils.equals(user.getProductCode(), ProductCodeEnum.SABER.getCode())){
//            source = AllinPayConstant.Source.MOBILE;
//        }
//        if(StringUtils.equals(user.getProductCode(), ProductCodeEnum.SWORD.getCode())){
//            source = StringUtils.equals(user.getDeviceType(), DeviceTypeEnum.WEB.getCode()+"") ?
//                    AllinPayConstant.Source.PC : AllinPayConstant.Source.MOBILE;
//        }

        SignContractDTO signContractDTO = new SignContractDTO();
        signContractDTO.setBizUserId(userBindAllinPay.getBizUserId());
        signContractDTO.setSource(source);
        if(AllinPayRoleTypeEnum.PERSON.getType().equals(userBindAllinPay.getAllinpayRoleType())){
            // 个人会员：名称
            signContractDTO.setSignAcctName(userBindAllinPay.getAuthUserName());
        }else{
            // 企业会员：对公户提现，则上送“企业名称”
            AllinPaySetCompanyInfoDTO companyInfoDTO = JSON.parseObject(userBindAllinPay.getCompanyInfo(), AllinPaySetCompanyInfoDTO.class);
            signContractDTO.setSignAcctName(companyInfoDTO.getCompanyName());
        }
        if(AllinPayConstant.SignContractStatus.SIGNED.equals(userBindAllinPay.getSignContractStatus())){
            log.info("已经签约过了 {}", userBindAllinPay.getBizUserId());
            return Result.data(getSignContractUrl(userBindAllinPay, signContractDTO));
        }

        // 去签约
        UserBindAllinPay updateObject = new UserBindAllinPay();
        updateObject.setId(userBindAllinPay.getId());
        updateObject.setBizUserId(userBindAllinPay.getBizUserId());
        updateObject.setSignContractStatus(AllinPayConstant.SignContractStatus.SIGNING);
//        LambdaQueryWrapper updateWrapper = Wrappers.<AccountBindAllinPay>query().lambda()
//                .eq(AccountBindAllinPay::getBizUserId, dto.getBizUserId());
        userBindAllinPayService.updateById(updateObject);
        AppTypeEnum appTypeEnum = getAppTypeEnumByRequest();
        Result<String> r = allinpayService.signAcctProtocol(dto.getShopId(), signContractDTO, appTypeEnum);
        if(!r.isSuccess()){
            throw new ServiceException(r.getErrorMsg());
        }
        return r;
    }

    @Override
    public Result<String> getMemberInfo(String bizUserId) {
        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getByBizUserId(bizUserId);
        if(userBindAllinPay == null){
            return Result.fail("未注册通联会员");
        }
        GetMemberInfoDTO infoDTO = new GetMemberInfoDTO();
        infoDTO.setBizUserId(bizUserId);
        Result<String> r = allinpayService.getMemberInfo(userBindAllinPay.getShopId(), infoDTO);
        if(!r.isSuccess()){
            throw new ServiceException(r.getErrorMsg());
        }
        return r;
    }

    @Override
    public Result<String> queryBankCard(String bizUserId) {
        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getByBizUserId(bizUserId);
        if(userBindAllinPay == null){
            return Result.fail("未注册通联会员");
        }
        QueryBankCardDTO infoDTO = new QueryBankCardDTO();
        infoDTO.setBizUserId(bizUserId);
        Result<String> r = allinpayService.queryBankCard(userBindAllinPay.getShopId(), infoDTO);
        if(!r.isSuccess()){
            throw new ServiceException(r.getErrorMsg());
        }
        return r;
    }

    @Override
    public Result<String> decryptBankCard(String cardNo, String bizUserId) {
        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getByBizUserId(bizUserId);
        if(userBindAllinPay == null){
            return Result.fail("未注册通联会员");
        }
        QueryBankCardDTO infoDTO = new QueryBankCardDTO();
        infoDTO.setBizUserId(bizUserId);
        OpenClient openClient = allinpayService.getOpenClient(userBindAllinPay.getShopId());
        return Result.success(openClient.decrypt(cardNo));
    }
    @Override
    public Result<Integer> getSignContractStatus(SignContractReq dto) {
        CommonUser user = getLoginUser();
        UserBindAllinPay accountBindAllinPay = userBindAllinPayService.getPersonByShopIdAndUserId(dto.getShopId(), user.getId());
        if(null == accountBindAllinPay){
            return Result.fail("还没有通联帐户");
        }
        log.info("查询签约状态 {} : {}", user.getId(), JSON.toJSONString(dto));

        return Result.data(accountBindAllinPay.getSignContractStatus());
    }

    /**
     *
     * @param authenticationDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> authentication(AllinPayAuthenticationDTO authenticationDTO) {
        CommonUser user = getLoginUser();
        UserBindAllinPay dbEntity = userBindAllinPayService.getPersonByShopIdAndUserId(authenticationDTO.getShopId(), user.getId());

        if(dbEntity == null){
            // 租客、房东、个人组织、企业组织（选择成功个人组织）未创建过记录则新建个人会员
            AllinPayLoginUserRegisterAccountDTO registerAccountDTO = new AllinPayLoginUserRegisterAccountDTO(authenticationDTO.getShopId());
            AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);
            registerAccountDTO.setAllinPayRoleTypeByTenantType(allinPayRoleTypeEnum.getType());
            AllInPayAcctTypeEnum acctTypeEnum = getAcctTypeByRequest();
            dbEntity = tlMemberRegister(acctTypeEnum, user, registerAccountDTO);
        }else{
            if(StringUtils.isNotEmpty(dbEntity.getAuthUserName())){
                if(dbEntity.getAuthUserName().equals(authenticationDTO.getName())){
                    return Result.data(true);
                }else{
                    return Result.fail("该用户已经实名过:" + dbEntity.getAuthUserName());
                }
            }
        }
        log.info("实名认证 {} : {}", user.getId(), JSON.toJSONString(authenticationDTO));

        RealNameDTO realNameDTO = new RealNameDTO();
        realNameDTO.setBizUserId(dbEntity.getBizUserId());
        realNameDTO.setIsAuth("true");
        realNameDTO.setName(authenticationDTO.getName());
        realNameDTO.setIdentityType("1");
        realNameDTO.setIdentityNo(authenticationDTO.getIdentityNo());

        Result<String> result = allinpayService.setRealName(dbEntity.getShopId(), realNameDTO);
        if(!result.isSuccess()){
            return Result.fail(result.getErrorMsg());
        }
        UserBindAllinPay updateEntity = new UserBindAllinPay();
        updateEntity.setId(dbEntity.getId());
        updateEntity.setBindStep(AllinPayConstant.BindStep.AUTH);
        updateEntity.setAuthUserIdNumber(authenticationDTO.getIdentityNo());
        updateEntity.setAuthUserIdType("1");
        updateEntity.setAuthUserName(authenticationDTO.getName());
        updateEntity.setUpdatedAt(new Date());

        userBindAllinPayService.updateById(updateEntity);

        Response<SubStore> subStoreResponse = subStoreReadService.findUserIdAndShopId(user.getId(), authenticationDTO.getShopId());
        if(subStoreResponse.isSuccess() && subStoreResponse.getResult() != null){
            SubStore subStoreEntity = subStoreResponse.getResult();
            // 是门店需要回写银行卡
            subStoreWriteCardInfoHandler.writeName(subStoreEntity, authenticationDTO.getName());
        }else{
            log.error("获取门店失败 {}", subStoreResponse.getError());
        }
        return Result.data(true);
    }



    @Override
    public Result<String> registerCompanyMember(AllinPayRegisterCompanyMemberDTO dto) {
        Long userId = dto.getSubUserId();
        CommonUser user = new CommonUser();
        user.setId(userId);

        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getCompanyByShopIdAndUserId(dto.getShopId(), userId);
        if(userBindAllinPay == null){
            // 未注册通联会员  需要进行注册
            AllinPayLoginUserRegisterAccountDTO registerAccountDTO = new AllinPayLoginUserRegisterAccountDTO(dto.getShopId());
            registerAccountDTO.setAllinPayRoleTypeByTenantType(AllinPayRoleTypeEnum.COMPANY.getType());
            AllInPayAcctTypeEnum acctTypeEnum = getAcctTypeByRequest();
            userBindAllinPay = tlMemberRegister(acctTypeEnum, user, registerAccountDTO);
        }else{
            if(AllinPayConstant.BindStep.COMPANY_INFO_H5.equals(userBindAllinPay.getStepStatus())){
                return Result.fail("企业会员开户H5 流程已走通");
            }
        }
        RegisterCompanyMemberDTO registerCompanyMemberDTO = new RegisterCompanyMemberDTO();
        registerCompanyMemberDTO.setBizUserId(userBindAllinPay.getBizUserId());
        registerCompanyMemberDTO.setCompanyName(dto.getCompanyName());
        // 固定个体工商户
        registerCompanyMemberDTO.setComProperty(2L);
        return allinpayService.registerCompanyMember(dto.getShopId(), registerCompanyMemberDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<SubStoreInfoVO> switchAvailability(SwitchAvailabilityDTO dto) {
        Long subUserId = dto.getSubUserId();
        Long shopId = dto.getShopId();

        Response<SubStore> subStoreResponse = subStoreReadService.findUserIdAndShopId(subUserId, shopId);
        if(!subStoreResponse.isSuccess()){
            return Result.fail(subStoreResponse.getError());
        }
        SubStore subStore = subStoreResponse.getResult();
        if(subStore == null){
            return Result.fail("找不到门店:" + subUserId);
        }
        Long subStoreId = subStore.getId();
        String key = "PARANA:switchAvailability:" + subUserId;
        return lock.lock(key, () ->{
            boolean isProfitClean = balanceDetailManager.isProfitClean(shopId, subUserId);
            if(!isProfitClean){
                log.info("存在待结算余额 {} {}", dto.getShopId(), subUserId);
                return Result.fail("请先处理[待结算或待收益余额], 再尝试切换会员");
            }
            long orderCountt = shopOrderReadService.getPendingPaymentOrderCountLastHour(shopId, subStoreId);
            if(orderCountt > 0){
                log.info("存在待支付订单 {} {}", dto.getShopId(), subUserId);
                return Result.fail("存在[待支付订单], 请稍后尝试");
            }
            long withDrawProfitApplyCount = withDrawProfitApplyReadService.getPendingWithdrawalRequestCount(shopId, subUserId);
            if(withDrawProfitApplyCount > 0){
                log.info("存在待提现收益 {} {}", dto.getShopId(), subUserId);
                return Result.fail("请先处理[提现申请单], 再尝试切换会员");
            }

            UserBindAllinPay person = userBindAllinPayService.getPersonByShopIdAndUserId(dto.getShopId(), dto.getSubUserId());
            UserBindAllinPay company = userBindAllinPayService.getCompanyByShopIdAndUserId(dto.getShopId(), dto.getSubUserId());

            String bizUserId = dto.getBizUserId();

            if(person != null && bizUserId.equals(person.getBizUserId())){
                // 打开个人会员 关闭企业会员
                openAndClose(person, company);
            }else if(company != null && bizUserId.equals(company.getBizUserId())){
                // 打开企业会员  关闭个人会员
                openAndClose(company, person);
            }else{
                return Result.fail("错误的bizUserId " + bizUserId);
            }
            return Result.success("");
        });
    }

    @Override
    public Result<AllinPayAccountDetailVO> allinPayAccountDetail(SubStoreInfoDTO dto) {
        AllinPayAccountDetailVO vo = new AllinPayAccountDetailVO();

        log.info("查询门店的通联账户详情 param {}", JSON.toJSONString(dto));
        UserBindAllinPay person = userBindAllinPayService.getPersonByShopIdAndUserId(dto.getShopId(), dto.getSubUserId());

        AllinPayAccountDetailVO.Person personVO = new AllinPayAccountDetailVO.Person();
        if(person != null){
            personVO = CopyUtil.copy(person, AllinPayAccountDetailVO.Person.class);
            settingAccountBaseInfo(personVO, person);
            if(person.getIsEnabled()){
                vo.setActiveBizUserId(person.getBizUserId());
                vo.setActiveAllinpayRoleType(person.getAllinpayRoleType());
            }
        }
        UserBindAllinPay company = userBindAllinPayService.getCompanyByShopIdAndUserId(dto.getShopId(), dto.getSubUserId());
        AllinPayAccountDetailVO.Company companyVO = new AllinPayAccountDetailVO.Company();
        if(company != null){
            companyVO = CopyUtil.copy(company, AllinPayAccountDetailVO.Company.class);
            settingAccountBaseInfo(companyVO, company);
            settingCompanyCreateStep(companyVO, company);

            if(StringUtils.isNotEmpty(company.getCompanyInfo())){
                CompanyBaseInfoDTO companyInfoStr = JSONObject.parseObject(company.getCompanyInfo(), CompanyBaseInfoDTO.class);
                companyVO.setCompanyName(companyInfoStr.getCompanyName());
            }
            if(company.getIsEnabled()){
                vo.setActiveBizUserId(company.getBizUserId());
                vo.setActiveAllinpayRoleType(company.getAllinpayRoleType());
            }
        }
        vo.setCompany(companyVO);
        vo.setPerson(personVO);
        return Result.data(vo);
    }


    private void settingCompanyCreateStep(AllinPayAccountDetailVO.Company companyVO, UserBindAllinPay company) {
        if(StringUtils.isNotEmpty(company.getBizUserId())){
            companyVO.setStep("1");
        }
        if(StringUtils.isNotEmpty(company.getAuthUserIdNumber())){
            companyVO.setStep("2");
        }
        if(StringUtils.isNotEmpty(company.getAcctProtocolNo())){
            companyVO.setStep("3");
        }
        if(StringUtils.isNotEmpty(company.getCompanyInfoFailReason()) && !"--".equals(company.getCompanyInfoFailReason())){
            companyVO.setStatus(false);
        }else{
            companyVO.setStatus(true);
        }
    }

    private void settingAccountBaseInfo(AllinPayAccountDetailVO.Base baseVO, UserBindAllinPay person) {
        String authUserIdNumber = person.getAuthUserIdNumber();
        if(StringUtils.isNotEmpty(authUserIdNumber)){
            String maskIdCard = AllinPayAccountUtil.maskIdCard(authUserIdNumber);
            baseVO.setAuthUserIdNumber(maskIdCard);
        }
        String phone = person.getBindPhone();
        if(StringUtils.isNotEmpty(phone)){
            String _phone = AllinPayAccountUtil.maskPhoneNumber(phone);
            baseVO.setBindPhone(_phone);
        }
        String bindCardPhone = person.getBindCardPhone();
        if(StringUtils.isNotEmpty(bindCardPhone)){
            String _bindCardPhone = AllinPayAccountUtil.maskPhoneNumber(bindCardPhone);
            baseVO.setBindCardPhone(_bindCardPhone);
        }
        String cardNo = person.getBindCardNo();
        if(StringUtils.isNotEmpty(cardNo)){
            String _cardNo = "**** **** **** " + cardNo.substring(cardNo.length() - 4);
            baseVO.setBindCardNo(_cardNo);
        }
    }


    private void openAndClose(UserBindAllinPay open, UserBindAllinPay close) {
        // 先关闭后开启
        if(close != null){
            UserBindAllinPay closeRecord = new UserBindAllinPay();
            closeRecord.setId(close.getId());
            closeRecord.setIsEnabled(false);
            userBindAllinPayService.updateById(closeRecord);
            log.info("关闭会员成功 {}", close.getId());
        }
        UserBindAllinPay openRecord = new UserBindAllinPay();
        openRecord.setId(open.getId());
        openRecord.setIsEnabled(true);
        userBindAllinPayService.updateById(openRecord);
        log.info("开启会员成功 {}", open.getId());

        String name = open.getAuthUserName();
        String bankName = open.getBindCardBranchName();
        String bankNo = open.getBindCardNo();

        log.info("回写银行卡信息 name {} bankName {} bankNo {}", name, bankName, bankNo);
        Response<SubStore> subStoreResponse = subStoreReadService.findUserIdAndShopId(open.getUserId(), open.getShopId());
        if(subStoreResponse.isSuccess() && subStoreResponse.getResult() != null){
            SubStore subStoreEntity = subStoreResponse.getResult();

            if(StringUtils.isEmpty(open.getBindCardNo())){
                // 清空卡号
                subStoreWriteCardInfoHandler.clearCardInfo(subStoreEntity);
            }else{
                // 修改门店支付信息
                subStoreWriteCardInfoHandler.writeNameAndCardInfo(subStoreEntity, name, bankName, bankNo);
            }
            // 修改通联会员类型
            subStoreWriteCardInfoHandler.checkAndUpdateAllinPayRoleTypeIfIsSub(subStoreEntity, open.getAllinpayRoleType());
        }else{
            log.error("获取门店失败 {}", subStoreResponse.getError());
        }

    }


//    /**
//     * 确认绑卡
//     *
//     * @param cardDTO
//     * @return
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
//    public Result<Boolean> bindingCard(AllinPayBindingCardDTO cardDTO) {
//        User user = getLoginUser();
//        UserBindAllinPay dbEntity = userBindAllinPayService.getByUserId(user.getId());
//
//        // 查询accountId(开户id，非account_bind_allinpay中的accountId)
////        Integer type = PayRoleTypeEnum.getAccountInfoType(dbEntity.getRoleType());
////        Long aId = accountInfoService.getAccountId(type, dbEntity.getAccountId(),true);
//
//        BindCardData bindCardData = JSON.parseObject(dbEntity.getBindCardData(),BindCardData.class);
//        String phone = bindCardData.getPhone();
//        String tranceNum = bindCardData.getTranceNum();
//        if(StringUtils.isEmpty(tranceNum)){
//            return Result.fail("请先执行请求绑卡操作");
//        }
//
////		if(PayRoleTypeEnum.TENANT_PERSONAL.getType().equals(account.getRoleType()) && account.getStepStatus()){
////			// 个人组织 && 第一次绑卡完毕
////			return R.fail("非法请求");
////		}
////
////		// 企业组织 && 第一次绑卡完毕 & 选择成为个人组织
////		if(PayRoleTypeEnum.TENANT_COMPANY.getType().equals(account.getRoleType())
////			&& account.getStepStatus()
////			&& AllinPayRoleTypeEnum.PERSON.getType().equals(account.getAllinPayRoleType())){
////			return R.fail("非法请求2");
////		}
//
////        // 保存卡信息
////        AccountBindCard bindCardDTO = new AccountBindCard();
////        bindCardDTO.setTenantId(SecureUtil.getTenantId());
////        bindCardDTO.setType(type);
////        bindCardDTO.setName(bindCardData.getName());
////        bindCardDTO.setBankName(bindCardData.getCardBinVo().getBankName());
////        bindCardDTO.setBankCode(bindCardData.getCardBinVo().getBankCode());
////        // 卡号
////        bindCardDTO.setCardNo(account.getBindCardNo());
////        bindCardDTO.setPhone(bindCardData.getPhone());
////        bindCardDTO.setAccountId(aId);
////        bindCardDTO.setCardSource(CardSourceEnum.ALLIN_PAY.getKey());
////        accountBindCardService.save(bindCardDTO);
////
////
////        // 个人组织 & 第一次绑卡
////        payChannelBusiness.openPayBrandByAllinPay(account);
////
////        if(RENTER.getType().equals(account.getRoleType())){
////            // 租客开通account_info表记录
////            accountInfoService.addRenter(pmsUser);
////        }
//
//        // 修改绑定流程
//        UserBindAllinPay updateEntity = new UserBindAllinPay();
//        updateEntity.setId(dbEntity.getId());
//        updateEntity.setBindStep(AllinPayConstant.BindStep.OVER);
//        updateEntity.setStepStatus(Boolean.TRUE);
//        updateEntity.setUpdatedAt(new Date());
//        userBindAllinPayService.updateById(updateEntity);
//
//        // 最后调用第三方api，如果异常事物回滚
//        BindBankCardDTO bankCardDTO = new BindBankCardDTO();
//        bankCardDTO.setBizUserId(dbEntity.getBizUserId());
//        bankCardDTO.setPhone(phone);
//        bankCardDTO.setTranceNum(tranceNum);
//        bankCardDTO.setVerificationCode(cardDTO.getVerificationCode());
//        Result<String> result2 = allinpayService.bindBankCard(bankCardDTO);
//        if(!result2.isSuccess()){
//            throw new ServiceException(result2.getErrorMsg());
//        }
//        return Result.data(true);
//    }

    /**
     *
     *
     * @param result
     * @param bizUserId
     * @param bizContent
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<String> setCompanyInfoBack(boolean result, String bizUserId, JSONObject bizContent) {
        UserBindAllinPay account = userBindAllinPayService.getByBizUserId(bizUserId);
        if(account == null){
            log.error("【通商】设置公司信息回调：找不到本地记录： bizUserId:{} result: {}",bizUserId,result);
            return Result.data(AllinPayConfigConstant.SUBCODE_OK);
        }
        if(AllinPayConstant.BindStep.BIND_PHONE.equals(account.getBindStep())){
            log.error("【通商】设置公司信息回调：错误的流程：记录已经绑定手机（页面只有等回调处理完毕后才能绑定手机），操作无效： bizUserId:{} result: {} account:{}",bizUserId,result,JSON.toJSONString(account));
            return Result.data(AllinPayConfigConstant.SUBCODE_OK);
        }
        if(!AllinPayRoleTypeEnum.COMPANY.getType().equals(account.getAllinpayRoleType())){
            log.error("【通商】设置公司信息回调：本地记录通商会员类型非企业会员： bizUserId:{} result: {} account:{}",bizUserId,result,JSON.toJSONString(account));
            return Result.data(AllinPayConfigConstant.SUBCODE_OK);
        }
        if(!result){
            //审核失败 流程回滚到绑定手机号完毕
            UserBindAllinPay updateEntity = new UserBindAllinPay();
            updateEntity.setId(account.getId());
            updateEntity.setBindStep(AllinPayConstant.BindStep.SET_COMPANY_INFO_FAIL);
            updateEntity.setCompanyInfoResult(bizContent.toJSONString());
            updateEntity.setUpdatedAt(new Date());
            userBindAllinPayService.updateById(updateEntity);
            log.warn("【通商】设置公司信息回调：审核失败已经回滚绑流程成为phone bizUserId：{}",bizUserId);
            return Result.data(AllinPayConfigConstant.SUBCODE_OK);
        }

        String companyInfo = account.getCompanyInfo();
        AllinPaySetCompanyInfoDTO companyInfoDto;
        if(StringUtils.isEmpty(companyInfo)){
            companyInfoDto = JSONObject.parseObject(companyInfo,AllinPaySetCompanyInfoDTO.class);
        }else{
            companyInfoDto = new AllinPaySetCompanyInfoDTO();
        }
        companyInfoDto.setCompanyName(bizContent.getString("companyName"));

        // 1. 更新流程状态
        UserBindAllinPay updateEntity = new UserBindAllinPay();
        updateEntity.setId(account.getId());
        updateEntity.setBindCardNo(companyInfoDto.getAccountNo());
        updateEntity.setBindCardBranchName(companyInfoDto.getBankName());
        updateEntity.setCompanyInfo(JSON.toJSONString(companyInfoDto));

        updateEntity.setBindStep(AllinPayConstant.BindStep.COMPANY_INFO);
        updateEntity.setStepStatus(Boolean.FALSE);
        updateEntity.setUpdatedAt(new Date());
        userBindAllinPayService.updateById(updateEntity);

        return Result.data(AllinPayConfigConstant.SUBCODE_OK);
    }


    /**
     * @param status
     * @param bizUserId
     * @param bizContent
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<String> setCompanyInfoBackByH5(boolean status, String bizUserId, JSONObject bizContent) {
        UserBindAllinPay account = userBindAllinPayService.getByBizUserId(bizUserId);
        if(account == null){
            log.error("【通商】设置公司信息回调：找不到本地记录： bizUserId:{} result: {}",bizUserId, status);
            return Result.data(AllinPayConfigConstant.SUBCODE_OK);
        }
        // 查询企业信息
        SetCompanyInfoBackByH5Message message = new SetCompanyInfoBackByH5Message();
        message.setBizContent(bizContent.toJSONString());
        message.setBizUserId(bizUserId);

        SendResult sendResult = rocketMQTemplate.syncSend(SetCompanyInfoBackByH5Message.TOPIC, message);
        log.info("bizUserId {} MQ {} msgId  {}", bizUserId, sendResult.getSendStatus().name(), sendResult.getMsgId());
        return Result.data(AllinPayConfigConstant.SUBCODE_OK);
    }

    @Override
    public Result<Boolean> processAllinpaySignContractMessage(Map<String, String> params) {
        JSONObject bizContent = JSONObject.parseObject(params.get("bizContent"));
        String bizUserId = bizContent.getString("bizUserId");
        String acctProtocolNo = bizContent.getString("acctProtocolNo");
        String status = bizContent.getString("result");
        log.warn("通联电子签约回调:" +JSON.toJSONString(bizContent));

        int signContractStatus = StringUtils.equalsIgnoreCase(status, "OK") ? AllinPayConstant.SignContractStatus.SIGNED :
                AllinPayConstant.SignContractStatus.UNSIGNED;

        UserBindAllinPay dbEntity = userBindAllinPayService.getByBizUserId(bizUserId);
        if(dbEntity == null){
            log.error("通联电子签约回调 bizUserId不存在 {}", bizUserId);
            throw new WxRuntimeException("bizUserId不存在");
        }

        UserBindAllinPay updateObject = new UserBindAllinPay();
        updateObject.setId(dbEntity.getId());
        updateObject.setBizUserId(bizUserId);
        updateObject.setSignContractStatus(signContractStatus);
        updateObject.setAcctProtocolNo(acctProtocolNo);
        boolean result = userBindAllinPayService.updateById(updateObject);
        if(!result){
            log.error("处理通联电子签约回调失败:" + JSON.toJSONString(params));
        }
        return Result.status(result);
    }

    @Override
    public Result<Boolean> bindOpenId(AllinPayBindOpenIdDTO bindOpenIdDTO) {
        CommonUser user = getLoginUser();
        UserBindAllinPay account = userBindAllinPayService.getPersonByShopIdAndUserId(bindOpenIdDTO.getShopId(), user.getId());
        if(account == null){
            return Result.fail("用户未绑定通联账户");
        }

        if(StringUtils.isEmpty(account.getAcct())){
            // 一个用户关于一个appId唯一 所以只能绑定一个openId
            List<UserBindAllinPay> accountList = userBindAllinPayService.getByOpenIdAndAppId(bindOpenIdDTO.getOpenId(), bindOpenIdDTO.getAppId(), AllinPayRoleTypeEnum.PERSON.getType());
            if(!CollectionUtils.isEmpty(accountList)){
                return Result.fail("该openId已经被使用");
            }

            AllInPayAcctTypeEnum acctTypeEnum = getAcctTypeByRequest();

            //绑定账户标识
            ApplyBindAcctDTO acctDTO = new ApplyBindAcctDTO();
            acctDTO.setBizUserId(account.getBizUserId());
            acctDTO.setOperationType("set");
            acctDTO.setAcctType(acctTypeEnum.getCode());
            acctDTO.setAcct(bindOpenIdDTO.getOpenId());

            Result<String> acctStr = allinpayService.applyBindAcct(account.getShopId(), acctDTO);
            if(!acctStr.isSuccess()){
                return Result.fail(acctStr.getErrorMsg());
            }
            // 修改绑定流程
            UserBindAllinPay updateEntity = new UserBindAllinPay();
            updateEntity.setId(account.getId());
            updateEntity.setAcctType(acctDTO.getAcctType());
            updateEntity.setAcct(acctDTO.getAcct());
            updateEntity.setAppId(bindOpenIdDTO.getAppId());
            updateEntity.setUpdatedAt(new Date());
            userBindAllinPayService.updateById(updateEntity);
            return Result.data(true);
        }
        return Result.fail("openId已经设置过");
    }

    @Override
    public Result<Boolean> unbindingPhone(AllinPayUnBindingPhoneDTO unBindingPhoneDTO) {
        log.info("解绑通联账户手机号:{}", JSON.toJSONString(unBindingPhoneDTO));

        CommonUser user = getLoginUser();
        UserBindAllinPay account = userBindAllinPayService.getPersonByShopIdAndUserId(unBindingPhoneDTO.getShopId(), user.getId());
        if(account == null){
            return Result.fail("用户未绑定通联账户");
        }
        if(StringUtils.isEmpty(account.getBindPhone()) && StringUtils.isEmpty(account.getAcct())){
            return Result.fail("用户未绑定手机号与支付标识 不可解绑");
        }


        log.info("解绑手机号 {} : {}", user.getId(), JSON.toJSONString(unBindingPhoneDTO));

        boolean clearPhone = false;
        UnBindPhoneDTO unBindPhoneDTO = new UnBindPhoneDTO();
        unBindPhoneDTO.setBizUserId(account.getBizUserId());
        if(StringUtils.isNotEmpty(account.getBindPhone())){
            // 解绑手机号
            if(StringUtils.isEmpty(unBindingPhoneDTO.getVerificationCode())){
                return Result.fail("解绑手机号验证码不能为空");
            }

            unBindPhoneDTO.setPhone(unBindingPhoneDTO.getPhone());
            unBindPhoneDTO.setVerificationCode(unBindingPhoneDTO.getVerificationCode());
            clearPhone = true;
        }else if(StringUtils.isNotEmpty(account.getAcct())){
            // 解绑支付标识
            // ...
            unBindPhoneDTO.setPhone("***********");
            unBindPhoneDTO.setVerificationCode("");

            // 注意不能清理本地acct：解绑手机号并不会导致支付标识的解绑，已绑定的支付标识重调用依旧会报错“支付账户用户标识已绑定”
//            updateEntity.setAcct("");
//            updateEntity.setAppId("");
        }

        Result<String> result = allinpayService.unbindPhone(account.getShopId(), unBindPhoneDTO);
        if(result.isSuccess()){
            if(clearPhone){
                UserBindAllinPay updateEntity = new UserBindAllinPay();
                updateEntity.setId(account.getId());
                updateEntity.setBindPhone("");
                userBindAllinPayService.updateById(updateEntity);
                log.info("清理本地手机号");
            }
            return Result.success("ok");
        }
        return Result.fail(result.getErrorMsg());
    }

    @Override
    public Result<AccountAllinPayQueryVO> queryBindInfoByLoginUser(QueryBindInfoDTO dto) {
        Long shopId = dto.getShopId();
        CommonUser user = getLoginUser();
        log.info("查询通联账户绑定信息 {} : {}", user.getId(), JSON.toJSONString(dto));
        AccountAllinPayQueryVO allinPayQueryVO = new AccountAllinPayQueryVO();

        Result<SubStore> subStoreResult = getSubStore(shopId, user.getId());
        if(!subStoreResult.isSuccess()){
            log.info("获取门店信息失败 {}", subStoreResult.getErrorMsg());
            return Result.fail(subStoreResult.getErrorMsg());
        }
        SubStore subStore = subStoreResult.getData();

        UserBindAllinPay dbEntity = userBindAllinPayService.getPersonByShopIdAndUserId(dto.getShopId(), user.getId());
        if(dbEntity == null){
            // 未注册
            allinPayQueryVO.setStep(AllinPayConstant.BindStep.WAIT);
            log.info("门店未注册");
            return Result.data(allinPayQueryVO);
        }

        // 设置回显信息
        allinPayQueryVO.setStepStatus(dbEntity.getStepStatus());
        allinPayQueryVO.setAllinPayRoleType(dbEntity.getAllinpayRoleType());
        allinPayQueryVO.setCardNo(dbEntity.getBindCardNo());
        allinPayQueryVO.setBranchName(dbEntity.getBindCardBranchName());
        allinPayQueryVO.setBindCardPhone(dbEntity.getBindPhone());
        if(StringUtils.isEmpty(dbEntity.getBindCardNo())){
//            if(StringUtils.isNotEmpty(subStore.getBankNo())){
//                log.info("绑定流程 未绑定银行卡 是门店  回显门店的银行卡信息 {} {}", subStore.getBankName(), subStore.getBankNo());
//                allinPayQueryVO.setBranchName(subStore.getBankName());
//                allinPayQueryVO.setCardNo(subStore.getBankNo());
//            }
        }
        if(StringUtils.isNotEmpty(dbEntity.getBindCardNo())){
            String cardNo = dbEntity.getBindCardNo();

            String _cardNo = "**** **** **** " + cardNo.substring(cardNo.length() - 4);
            List<CardVO> cardVOList = new ArrayList<>();
            CardVO cardVO = new CardVO();
            cardVO.setCardName(dbEntity.getBindCardBranchName());
            cardVO.setCardNo(_cardNo);
            cardVOList.add(cardVO);
            allinPayQueryVO.setCardList(cardVOList);
        }

        String step = getStep(dbEntity, subStore);
        allinPayQueryVO.setStep(step);

        if(user.getRoles().contains(UserRole.ADMIN)){
            // 企业组织
            if(StringUtils.isNotEmpty(dbEntity.getCompanyInfo())){
                AllinPaySetCompanyInfoDTO companyInfoDTO = JSON.parseObject(dbEntity.getCompanyInfo(), AllinPaySetCompanyInfoDTO.class);
                companyInfoDTO.setBindPhone(dbEntity.getBindPhone());
                allinPayQueryVO.setCompanyInfo(companyInfoDTO);
            }
            // 企业选个人会员 填充回显数据
            if(StringUtils.isNotEmpty(dbEntity.getAuthUserName())){
                allinPayQueryVO.setAuthUserName(dbEntity.getAuthUserName());
                allinPayQueryVO.setAuthUserIdNumber(dbEntity.getAuthUserIdNumber());
                allinPayQueryVO.setAuthUserIdType(dbEntity.getAuthUserIdType());
            }
            if(StringUtils.isNotEmpty(dbEntity.getBindPhone())){
                allinPayQueryVO.setBindPhone(dbEntity.getBindPhone());
            }
            if(AllinPayConstant.BindStep.SET_COMPANY_INFO_FAIL.equals(dbEntity.getBindStep())){
                JSONObject bizContent = JSON.parseObject(dbEntity.getCompanyInfoResult());
                String failReason = bizContent.getString("failReason");
                String remark = bizContent.getString("remark");
                // 审核失败
                allinPayQueryVO.setCompanyFailReason(failReason);
                allinPayQueryVO.setCompanyFailRemark(remark);
            }
        }else{
            allinPayQueryVO.setAuthUserName(dbEntity.getAuthUserName());
            allinPayQueryVO.setAuthUserIdNumber(dbEntity.getAuthUserIdNumber());
            allinPayQueryVO.setAuthUserIdType(dbEntity.getAuthUserIdType());
            allinPayQueryVO.setBindPhone(dbEntity.getBindPhone());
        }

        // 设置各个环节的绑定状态
        AllinPayAccountInfoVO allinPayAccountInfoVO = AllinPayAccountUtil.settingStepStatus(dbEntity);
        allinPayQueryVO.setAccountInfo(allinPayAccountInfoVO);

        // 调试用
        allinPayQueryVO.setAcct(dbEntity.getAcct());
        allinPayQueryVO.setAcctType(dbEntity.getAcctType());

        log.info("响应信息 {}", JSON.toJSONString(allinPayQueryVO));
        return Result.data(allinPayQueryVO);
    }

    @Override
    public Result<SubStoreInfoVO> subStoreInfo(SubStoreInfoDTO dto) {
        Long shopId = dto.getShopId();
        SubStoreInfoVO allinPayQueryVO = new SubStoreInfoVO();

//        CommonUser user = getLoginUser();
//        Response<SubStore> subStoreResult = subStoreReadService.findUserIdAndShopId(user.getId(), shopId);
//        SubStore subStore = subStoreResult.getResult();

        log.info("查询门店信息 param {}", JSON.toJSONString(dto));
        // TODO
        UserBindAllinPay dbEntity = userBindAllinPayService.getPersonByShopIdAndUserId(dto.getShopId(), dto.getSubUserId());
        if(dbEntity == null){
            allinPayQueryVO.setAllinpayRoleType(null);
            allinPayQueryVO.setAuthUserName("");
            allinPayQueryVO.setAuthUserIdNumber("");
            allinPayQueryVO.setAuthUserIdType("");
            allinPayQueryVO.setBindPhone("");
            allinPayQueryVO.setBindCardNo("");
            allinPayQueryVO.setBindCardBranchName("");
            allinPayQueryVO.setBizUserId("");
        }else{
            allinPayQueryVO.setAllinpayRoleType(dbEntity.getAllinpayRoleType());
            allinPayQueryVO.setAuthUserName(dbEntity.getAuthUserName());
            String authUserIdNumber = dbEntity.getAuthUserIdNumber();
            if(StringUtils.isNotEmpty(authUserIdNumber)){
                String maskIdCard = AllinPayAccountUtil.maskIdCard(authUserIdNumber);
                allinPayQueryVO.setAuthUserIdNumber(maskIdCard);
            }
            allinPayQueryVO.setAuthUserIdType(dbEntity.getAuthUserIdType());
            allinPayQueryVO.setBindPhone(dbEntity.getBindPhone());
            String cardNo = dbEntity.getBindCardNo();
            if(StringUtils.isNotEmpty(cardNo)){
                String _cardNo = "**** **** **** " + cardNo.substring(cardNo.length() - 4);
                allinPayQueryVO.setBindCardNo(_cardNo);
            }
            allinPayQueryVO.setBindCardBranchName(dbEntity.getBindCardBranchName());
            allinPayQueryVO.setBizUserId(dbEntity.getBizUserId());
        }
        return Result.data(allinPayQueryVO);
    }

    @Override
    public void checkAllinPayAccountInfo(String outShopId, PaymentChannelEnum channel, Long userId) {
        if(!PaymentChannelEnum.ALLINPAY_YST.equals(channel)){
            log.info("非通商云支付 不校验门店信息");
            return;
        }
        if(StringUtils.isEmpty(outShopId)){
            log.error("outShopId 为空 不校验门店信息");
            return;
        }
        Long outShopIdLong = Long.valueOf(outShopId);
        Response<SubStore> subStoreResponse = subStoreReadService.findById(outShopIdLong);
        if(!subStoreResponse.isSuccess() && subStoreResponse.getResult() != null){
            log.error("找不到门店 {}", outShopId);
            throw new RuntimeException("找不到门店");
        }
        SubStore subStore = subStoreResponse.getResult();

        UserBindAllinPay userBindAllinPay = userBindAllinPayService.getActiveByShopIdAndUserId(subStore.getShopId(), subStore.getUserId());
        if(userBindAllinPay == null){
            log.info("门店用户ID {} 登录用户ID {}", subStore.getUserId(), userId);
            if(userId.equals(subStore.getUserId())){
                log.info("需完善通商云账户信息 未开户");
                // 写死提示 前端要根据这个提示内容选择弹框的功能
                throw new RuntimeException("需完善通商云账户信息");
            }else{
                throw new RuntimeException("请联系门店完善通商云账户信息");
            }
        }
        if(!userBindAllinPay.getStepStatus()
                || AllinPayConstant.SignContractStatus.SIGNED != userBindAllinPay.getSignContractStatus()
                || org.springframework.util.StringUtils.isEmpty(userBindAllinPay.getBindCardNo())){
            log.info("门店用户ID {} 登录用户ID {}", subStore.getUserId(), userId);
            if(AllinPayRoleTypeEnum.PERSON.getType().equals(userBindAllinPay.getAllinpayRoleType())){
                if(userId.equals(subStore.getUserId())){
                    log.info("需完善通商云账户信息 未完善 个人会员");
                    // 写死提示 前端根据该提示 跳出个人会员绑卡流程
                    throw new RuntimeException("需完善通商云账户信息");
                }else{
                    throw new RuntimeException("请联系门店完善通商云账户信息");
                }
            }else{
                if(userId.equals(subStore.getUserId())){
                    log.info("需完善通商云账户信息 未完善 企业会员");
                    throw new RuntimeException("请先添加企业会员的提现银行卡");
                }else{
                    throw new RuntimeException("请联系门店完善通商云账户信息");
                }
            }
        }
    }


    private Result<SubStore> getSubStore(Long shopId, Long  userId) {
        Response<SubStore> subStoreResponse = subStoreReadService.findUserIdAndShopId(userId, shopId);
        if(!subStoreResponse.isSuccess() && subStoreResponse.getResult() != null){
            return Result.fail(subStoreResponse.getError());
        }
        // 目前完善信息流程只给门店用
        SubStore subStore = subStoreResponse.getResult();
        if(subStore == null){
            return Result.fail("无操作权限");
        }
        return Result.data(subStore);
    }

    private String getStep(UserBindAllinPay dbEntity, SubStore subStore) {
//        if(StringUtils.isNotEmpty(dbEntity.getBindPhone()) && StringUtils.isNotEmpty(dbEntity.getAcct())){
//            throw new RuntimeException("门店角色 不允许手机号与acct均不为空");
//        }
//        if(StringUtils.isEmpty(dbEntity.getBindPhone()) && StringUtils.isNotEmpty(dbEntity.getAcct())){
//            log.info("绑定流程 门店角色  未绑定手机号 且支付标识不为空");
//            // 一个额外的步骤 但是不会在库中记录
//            return AllinPayConstant.BindStep.BIND_ACCT;
//        }
        return dbEntity.getBindStep();
    }

    private String getSignContractUrl(UserBindAllinPay userBindAllinPay, SignContractDTO signContractDTO){
        SignContractQueryDTO signContractQueryDTO = CopyUtil.copy(signContractDTO, SignContractQueryDTO.class);
        if(null == signContractQueryDTO){
            throw new ServiceException("签约信息不能为空");
        }
        AppTypeEnum appTypeEnum = getAppTypeEnumByRequest();
        Result<String> r = allinpayService.signContractQuery(userBindAllinPay.getShopId(), signContractQueryDTO, appTypeEnum);
        if(!r.isSuccess()){
            throw new ServiceException(r.getErrorMsg());
        }
        return r.getData();
    }

    /**
     * 申请绑卡
     * @param applyBindBankCardDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> applyBindBankCard(AllinPayApplyBindBankCardDTO applyBindBankCardDTO) {
//        CommonUser user = getLoginUser();
        Long shopId = applyBindBankCardDTO.getShopId();
        Long userId = applyBindBankCardDTO.getUserId();
        UserBindAllinPay dbEntity;
        if(AllinPayRoleTypeEnum.COMPANY.getType().equals(applyBindBankCardDTO.getAllinpayRoleType())){
            dbEntity = userBindAllinPayService.getCompanyByShopIdAndUserId(applyBindBankCardDTO.getShopId(), userId);
        }else{
            dbEntity = userBindAllinPayService.getPersonByShopIdAndUserId(applyBindBankCardDTO.getShopId(), userId);
        }
        if(dbEntity == null){
            return Result.fail("未注册通联账户");
        }
        log.info("申请绑卡 {} : {}", userId, JSON.toJSONString(applyBindBankCardDTO));

        // 门店需要回显之前的银行卡信息
        Response<SubStore> subStore = subStoreReadService.findUserIdAndShopId(userId, shopId);
        if(!subStore.isSuccess()){
            return Result.fail(subStore.getError());
        }
        SubStore subStoreEntity = subStore.getResult();
        if(subStoreEntity == null){
            return Result.fail("仅门店可绑定银行卡");
        }


        // 查询卡bin
        AllinPayCardBinQueryDTO cardBinQueryDTO = new AllinPayCardBinQueryDTO();
        cardBinQueryDTO.setShopId(shopId);
        cardBinQueryDTO.setCardNo(applyBindBankCardDTO.getCardNo());
        Result<CardBinVo> cardBinResult = cardBin(cardBinQueryDTO);
        if(!cardBinResult.isSuccess()){
            return Result.fail("查询卡bin信息出错," + cardBinResult.getErrorMsg());
        }

        // 绑卡方式：必须是8（银行卡四要素验证（全部银行）），这种方式不会有短信验证码到校验
        // https://cloud.allinpay.com/ts-cloud-dev-web/#/apiCenter/index?params=y&key=300
        ApplyBindBankCardDTO bindBankCardDTO = new ApplyBindBankCardDTO();
        bindBankCardDTO.setCardCheck(8);
        bindBankCardDTO.setBizUserId(dbEntity.getBizUserId());
        bindBankCardDTO.setPhone(applyBindBankCardDTO.getPhone());
        bindBankCardDTO.setCardNo(applyBindBankCardDTO.getCardNo());

//        AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.getAllinPayRoleTypeEnum(user);
//        if(AllinPayRoleTypeEnum.COMPANY.equals(allinPayRoleTypeEnum)){
//            // 如果是企业会员，请填写法人姓名（企业组织绑定个人卡需要查询法人信息）
////            TenantVO tenantInfo = uacService.getTenantInfo(SecureUtil.getTenantId());
////            bindBankCardDTO.setIdentityType("1");
////            bindBankCardDTO.setIdentityNo(tenantInfo.getLeaderId());
////            bindBankCardDTO.setName(tenantInfo.getLeaderName());
//            throw new WxRuntimeException("暂不支持企业开户");
//        }else{
//            bindBankCardDTO.setIdentityType(1L);
//            bindBankCardDTO.setIdentityNo(dbEntity.getAuthUserIdNumber());
//            bindBankCardDTO.setName(dbEntity.getAuthUserName());
//        }
        bindBankCardDTO.setIdentityType(1L);
        bindBankCardDTO.setIdentityNo(dbEntity.getAuthUserIdNumber());
        bindBankCardDTO.setName(dbEntity.getAuthUserName());

        // 存储未加密前到数据
        ApplyBindBankCardDTO copyBindBankCardDTO = CopyUtil.copy(bindBankCardDTO, ApplyBindBankCardDTO.class);
        CardBinVo cardBinVo = cardBinResult.getData();
        BindCardData bindCardData = new BindCardData(copyBindBankCardDTO, cardBinVo);

        Result<String> result1 = allinpayService.applyBindBankCard(dbEntity.getShopId(), bindBankCardDTO);
        if(!result1.isSuccess()){
            String message = result1.getErrorMsg();
            // 移除“外部错误：”字样
            message = message.replace("外部错误：", "");
            // 移除“渠道返回：”字样
            message = message.replace("渠道返回：", "");
            throw new WxRuntimeException(message);
        }

        if(subStoreEntity != null){
            if(dbEntity.getIsEnabled()){
                // 【是门店 & 当前账户是启用的】需要回写银行卡
                subStoreWriteCardInfoHandler.writeCardInfo(subStoreEntity,  cardBinVo.getBankName(), applyBindBankCardDTO.getCardNo());
            }else {
                log.info("当前通联会员未启用 不回写到门店表");
            }
        }

//		// 记录tranceNum(绑卡方式6、7返回)
		String tranceNum = result1.getData();
        bindCardData.setTranceNum(tranceNum);


        // 更新通联账户信息
        UserBindAllinPay updateEntity = new UserBindAllinPay();
        updateEntity.setId(dbEntity.getId());
        updateEntity.setBindCardBranchName(applyBindBankCardDTO.getBranchName());
        updateEntity.setBindStep(AllinPayConstant.BindStep.OVER);
        updateEntity.setStepStatus(Boolean.TRUE);
        updateEntity.setUpdatedAt(new Date());
//        updateEntity2.setBindCardTranceNum(tranceNum);
        updateEntity.setBindCardNo(applyBindBankCardDTO.getCardNo());
        updateEntity.setBindCardData(JSON.toJSONString(bindCardData));
        updateEntity.setBindCardPhone(applyBindBankCardDTO.getPhone());
        updateEntity.setUpdatedAt(new Date());
		userBindAllinPayService.updateById(updateEntity);
        return Result.data(true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public Result<Boolean> unBindBankCard(AllinPayUnBindBankCardDTO applyBindBankCardDTO) {
        Long userId = applyBindBankCardDTO.getUserId();
        UserBindAllinPay account;
        if(AllinPayRoleTypeEnum.COMPANY.getType().equals(applyBindBankCardDTO.getAllinpayRoleType())){
            account = userBindAllinPayService.getCompanyByShopIdAndUserId(applyBindBankCardDTO.getShopId(), userId);
        }else{
            account = userBindAllinPayService.getPersonByShopIdAndUserId(applyBindBankCardDTO.getShopId(), userId);
        }
        if(account == null){
            return Result.fail("用户未开通通联账户");
        }
        if(StringUtils.isEmpty(account.getBindCardNo())){
            return Result.fail("当前未绑定银行卡到通联");
        }

        Long shopId = applyBindBankCardDTO.getShopId();

        Result<SubStore> subStoreResult = getSubStore(shopId, userId);
        if(!subStoreResult.isSuccess()){
            return Result.fail(subStoreResult.getErrorMsg());
        }
        SubStore subStore = subStoreResult.getData();

        UnbindBankCardDTO unBindPhoneDTO = new UnbindBankCardDTO();
        unBindPhoneDTO.setBizUserId(account.getBizUserId());
        unBindPhoneDTO.setCardNo(account.getBindCardNo());

        Result<String> result = allinpayService.unbindBankCard(account.getShopId(), unBindPhoneDTO);
        if(result.isSuccess()){
            UserBindAllinPay updateEntity = new UserBindAllinPay();
            updateEntity.setId(account.getId());
            updateEntity.setBindCardNo("");
            updateEntity.setBindCardData("");
            updateEntity.setBindCardBranchName("");
            userBindAllinPayService.updateById(updateEntity);

            if(subStore != null){
                if(account.getIsEnabled()){
                    // 【是门店 & 当前账户是启用的】需要删除银行卡
                    subStoreWriteCardInfoHandler.clearCardInfo(subStore);
                }else {
                    log.info("当前通联会员未启用 不需要删除门店表银行卡");
                }
            }
            return Result.success("ok");
        }
        return Result.fail(result.getErrorMsg());
    }

    private CommonUser getLoginUser() {
        CommonUser operator = UserUtil.getCurrentUser();
//        CommonUser operator = new CommonUser();
//        operator.setId(128l);
//        List<String> list = new ArrayList<>();
//        list.add(UserRole.ADMIN.name());
//        operator.setRoles(list);
        return operator;
    }

//    private UserBindAllinPay getAndCreateAllinPayAccount(CommonUser user) {
//        UserBindAllinPay account = userBindAllinPayService.getByShopIdAndUserId(createAccountDTO.getShopId(), user.getId());
//        if(account == null){
//            account = tlMemberRegister(user, new AllinPayLoginUserRegisterAccountDTO());
//        }
//        return account;
//    }


}
