package moonstone.web.core.component;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.event.*;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShipmentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.weShop.model.WeShop;
import moonstone.web.core.model.OrderOperatorRecord;
import moonstone.web.core.model.OrderStatusChangeAction;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.TextIndexDefinition;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * 订单操作记录
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderOperatorRecordSync {
    private final MongoTemplate mongoTemplate;
    private final ShopOrderReadService shopOrderReadService;
    private final SkuOrderReadService skuOrderReadService;
    private final ShipmentReadService shipmentReadService;
    private final PaymentReadService paymentReadService;
    private final WeShopCacheHolder weShopCacheHolder;

    @Profile({"dev", "test"})
    public class TestRecordDeleteJob {
        @Scheduled(cron = "0 0/1 * * * ?")
        public void job() {
            scanOldRecordAndDelete();
        }
    }

    @Profile("online")
    public class OnlineRecordDeleteJob {
        @Scheduled(cron = "0 0 0/1 * * ?")
        public void job() {
            scanOldRecordAndDelete();
        }
    }

    @PostConstruct
    public void checkIndex() {
        IndexOperations indexOperations = mongoTemplate.indexOps(OrderOperatorRecord.class);
        if (indexOperations.getIndexInfo().stream().noneMatch(index -> index.getIndexFields().stream().anyMatch(indexField -> indexField.getKey().equals("orderId")))) {
            indexOperations.ensureIndex(new TextIndexDefinition.TextIndexDefinitionBuilder().onField("orderId").build());
        }
    }

    public void scanOldRecordAndDelete() {
        LocalDateTime now = LocalDateTime.now();
        Date thereMonthAgo = Date.from(now.minusMonths(3).atZone(ZoneId.systemDefault()).toInstant());
        for (OrderOperatorRecord record : mongoTemplate.findAllAndRemove(Query.query(Criteria.where("createdAt").lte(thereMonthAgo)), OrderOperatorRecord.class)) {
            log.debug("{} Order[{}] timeout delete", LogUtil.getClassMethodName(), record);
        }
    }


    private void insertOnlyOnce(OrderOperatorRecord orderOperatorRecord) {
        OrderOperatorRecord record = mongoTemplate.findOne(Query.query(Criteria.where("orderId").is(orderOperatorRecord.getOrderId())).addCriteria(Criteria.where("actionType").is(orderOperatorRecord.getActionType())).addCriteria(Criteria.where("createdAt").is(orderOperatorRecord.getCreatedAt())), OrderOperatorRecord.class);
        if (!Objects.isNull(record)) {
            if (Objects.equals(orderOperatorRecord.getOrderAction().toString(), orderOperatorRecord.getOrderAction().toString()) && Objects.equals(orderOperatorRecord.getOperatorId(), record.getOperatorId())) {
                return;
            }
        }
        mongoTemplate.insert(orderOperatorRecord);
    }

    @EventListener(OrderCreatedEvent.class)
    public void recordOrderCreate(OrderCreatedEvent orderCreatedEvent) {
        ShopOrder shopOrder = shopOrderReadService.findById(orderCreatedEvent.getOrderId()).getResult();
        OrderOperatorRecord operatorRecord = new OrderOperatorRecord();
        operatorRecord.setOrderId(orderCreatedEvent.getOrderId());
        operatorRecord.setCreatedAt(new Date());
        operatorRecord.setActionType("Order");
        if (Objects.nonNull(shopOrder)) {
            operatorRecord.setCreatedAt(shopOrder.getCreatedAt());
            operatorRecord.setBuyerId(shopOrder.getBuyerId());
            operatorRecord.setOperatorId(shopOrder.getBuyerId());
        }
        operatorRecord.setOrderAction(new OrderStatusChangeAction("购买订单", "购买订单但未支付"));
        insertOnlyOnce(operatorRecord);
    }

    @EventListener(PaymentPaidEvent.class)
    public void recordOrderPaymentPaid(PaymentPaidEvent paymentPaidEvent) {
        for (OrderBase orderBase : paymentReadService.findOrdersByPaymentId(paymentPaidEvent.getPaymentId()).getResult()) {
            if (orderBase instanceof ShopOrder) {
                insertOnlyOnce(new OrderOperatorRecord(orderBase.getId(), orderBase.getBuyerId(), orderBase.getBuyerId(), new OrderStatusChangeAction("支付订单", "订单被支付"), OrderEvent.PAY.name(), new Date()));
            }
            if (orderBase instanceof GatherOrder) {
                List<ShopOrder> shopOrderList = Optional.ofNullable(shopOrderReadService.findByGatherOrderId(orderBase.getId()).take()).orElseThrow(() -> new RuntimeException(Translate.of("查找采购单对应信息失败")));
                for (ShopOrder shopOrder : shopOrderList) {
                    Long operatorId = NumberUtil.parseNumber(shopOrder.getOutShopId(), Long.class).map(weShopCacheHolder::findByWeShopId)
                            .orElse(Optional.empty()).map(WeShop::getUserId).orElse(null);
                    insertOnlyOnce(new OrderOperatorRecord(shopOrder.getId(), shopOrder.getBuyerId(), operatorId, new OrderStatusChangeAction("采购订单", "向供应商采购定单"), "gather", new Date()));
                }
            }
        }
    }

    /**
     * 添加订单操作日志：确认收货
     *
     * @param confirmEvent
     */
    @EventListener(OrderConfirmEvent.class)
    public void recordOrderConfirmEvent(OrderConfirmEvent confirmEvent) {
        switch (OrderLevel.fromInt(confirmEvent.getOrderType())) {
            case SKU: {
                SkuOrder skuOrder = skuOrderReadService.findById(confirmEvent.getOrderId()).getResult();
                OrderCriteria orderCriteria = new OrderCriteria();
                orderCriteria.setOrderId(skuOrder.getOrderId());
                Long count = skuOrderReadService.countShopOrder(orderCriteria).getResult();
                if (count == 1) {
                    insertOnlyOnce(new OrderOperatorRecord(skuOrder.getOrderId(), skuOrder.getBuyerId(), null, new OrderStatusChangeAction("确定收货", "订单确定收货, 将会结算佣金"), OrderEvent.CONFIRM.name(), new Date()));
                } else {
                    insertOnlyOnce(new OrderOperatorRecord(skuOrder.getOrderId(), skuOrder.getBuyerId(), null, new OrderStatusChangeAction("确定收货", Translate.of("子订单[%s]确定收货, 将会结算佣金", skuOrder.getId())), OrderEvent.CONFIRM.name(), new Date()));
                }
                return;
            }
            case SHOP: {
                ShopOrder shopOrder = shopOrderReadService.findById(confirmEvent.getOrderId()).getResult();
                insertOnlyOnce(new OrderOperatorRecord(shopOrder.getId(), shopOrder.getBuyerId(), null, new OrderStatusChangeAction("确定收货", Translate.of("确定收货, 将会结算佣金")), OrderEvent.CONFIRM.name(), new Date()));
                return;
            }
            default: {

            }
        }
    }

    @EventListener(OrderShipmentEvent.class)
    public void recordOrderShipment(OrderShipmentEvent orderShipmentEvent) {
        Shipment shipment = shipmentReadService.findById(orderShipmentEvent.getShipmentId()).getResult();
        for (OrderShipment orderShipment : shipmentReadService.findOrderIdsByShipmentId(shipment.getId()).getResult()) {
            switch (orderShipment.getOrderLevel()) {
                case SKU: {
                    SkuOrder skuOrder = skuOrderReadService.findById(orderShipment.getOrderId()).getResult();
                    OrderCriteria orderCriteria = new OrderCriteria();
                    orderCriteria.setOrderId(skuOrder.getOrderId());
                    Long count = skuOrderReadService.countShopOrder(orderCriteria).getResult();
                    if (count == 1) {
                        insertOnlyOnce(new OrderOperatorRecord(skuOrder.getOrderId(), skuOrder.getBuyerId(), null, new OrderStatusChangeAction("发货", Translate.of("发货, 物流单号[%s]", shipment.getShipmentSerialNo()))
                                , OrderEvent.SHIP.name(), shipment.getCreatedAt()));
                    } else {
                        insertOnlyOnce(new OrderOperatorRecord(skuOrder.getOrderId(), skuOrder.getBuyerId(), null, new OrderStatusChangeAction("发货", Translate.of("子订单[%s]发货, 物流单号[%s]", skuOrder.getId(), shipment.getShipmentSerialNo()))
                                , OrderEvent.SHIP.name(), shipment.getCreatedAt()));
                    }
                    break;
                }
                case SHOP: {
                    ShopOrder shopOrder = shopOrderReadService.findById(orderShipment.getOrderId()).getResult();
                    insertOnlyOnce(new OrderOperatorRecord(shopOrder.getId(), shopOrder.getBuyerId(), null, new OrderStatusChangeAction("发货", Translate.of("发货, 物流单号[%s]", shipment.getShipmentSerialNo()))
                            , OrderEvent.SHIP.name(), shipment.getCreatedAt()));
                    break;
                }
                default: {

                }
            }
        }
    }

    @EventListener(OrderRefundEvent.class)
    public void recordOrderRefundEvent(OrderRefundEvent orderRefundEvent) {
        // todo : 采购单
    }
    // todo : 增加审核订单, 审核采购单

    @EventListener(OrderPushEvent.class)
    public void recordOrderPushEvent(OrderPushEvent orderPushEvent) {
        ShopOrder shopOrder = shopOrderReadService.findById(orderPushEvent.getOrderId()).getResult();
        if (Objects.equals(orderPushEvent.getPushStatus(), SkuOrderPushStatus.FINISHED.value())) {
            insertOnlyOnce(new OrderOperatorRecord(shopOrder.getId(), shopOrder.getBuyerId(), null, new OrderStatusChangeAction("推送", Translate.of("订单推送第三方完成")), "push", new Date()));
        } else {
            insertOnlyOnce(new OrderOperatorRecord(shopOrder.getId(), shopOrder.getBuyerId(), null, new OrderStatusChangeAction("推送", Translate.of("订单推送第三方失败, 状态[%s]", orderPushEvent.getPushStatus())), "push", new Date()));
        }
    }

    @EventListener(PaymentPushFailedNotify.class)
    public void recordPaymentPushFailEvent(PaymentPushFailedNotify paymentPushFailedNotify) {
        for (OrderBase orderBase : paymentReadService.findOrdersByPaymentId(paymentPushFailedNotify.getPaymentId()).getResult()) {
            if (orderBase instanceof ShopOrder) {
                insertOnlyOnce(new OrderOperatorRecord(orderBase.getId(), orderBase.getBuyerId(), null, new OrderStatusChangeAction("支付单推送", Translate.of("支付单推送失败")), "paymentPush", new Date()));
            }
        }
    }

    @EventListener(PaymentPushSuccessNotify.class)
    public void recordPaymentPushSuccessEvent(PaymentPushSuccessNotify paymentPushSuccessNotify) {
        for (OrderBase orderBase : paymentReadService.findOrdersByPaymentId(paymentPushSuccessNotify.getPaymentId()).getResult()) {
            if (orderBase instanceof ShopOrder) {
                insertOnlyOnce(new OrderOperatorRecord(orderBase.getId(), orderBase.getBuyerId(), null, new OrderStatusChangeAction("支付单推送", Translate.of("支付单推送成功")), "paymentPush", new Date()));
            }
        }
    }

    @EventListener(PaymentDeclareSuccessNotify.class)
    public void recordPaymentDeclareEvent(PaymentDeclareSuccessNotify paymentDeclareSuccessNotify) {
        ShopOrder shopOrder = shopOrderReadService.findById(paymentDeclareSuccessNotify.getOrderId()).getResult();
        insertOnlyOnce(new OrderOperatorRecord(shopOrder.getId(), shopOrder.getBuyerId(), null, new OrderStatusChangeAction("支付单申报", Translate.of("支付单申报成功")), "paymentDeclare", paymentDeclareSuccessNotify.getCreatedAt()));
    }

    @EventListener(PaymentDeclareFailNotify.class)
    public void recordPaymentDeclareFailEvent(PaymentDeclareFailNotify paymentDeclareFailNotify) {
        for (OrderBase orderBase : paymentReadService.findOrdersByPaymentId(paymentDeclareFailNotify.getPaymentId()).getResult()) {
            if (orderBase instanceof ShopOrder) {
                insertOnlyOnce(new OrderOperatorRecord(orderBase.getId(), orderBase.getBuyerId(), null, new OrderStatusChangeAction("支付单申报", Translate.of("支付单申报失败")), "paymentDeclare", new Date()));
            }
        }
    }

}
