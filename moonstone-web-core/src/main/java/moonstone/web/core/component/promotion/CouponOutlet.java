package moonstone.web.core.component.promotion;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.PromotionTrack;
import moonstone.promotion.model.UserPromotion;
import moonstone.promotion.service.PromotionTrackReadService;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.promotion.service.UserPromotionWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 优惠券输出(领券、发券)
 */
@Component
@Slf4j
public class CouponOutlet {

    @Autowired
    private PromotionCacher promotionCacher;

    @RpcConsumer
    private PromotionTrackReadService promotionTrackReadService;

    @RpcConsumer
    private UserPromotionReadService userPromotionReadService;

    @RpcConsumer
    private UserPromotionWriteService userPromotionWriteService;

    @Autowired(required = false)
    private JedisPool jedisPool;

    /**
     * 买家领券
     *
     * @param buyerId     买家id
     * @param promotionId 券id
     */
    @Transactional
    public void receive(Long buyerId, Long promotionId) {
        Promotion promotion = findCoupon(promotionId);

        //检查优惠券状态
        checkPromotionStatus(promotion);

        //检查是否已经达到发放数量限制
        checkSendLimit(promotion, 1);

        //检查领取限制
        checkReceiveLimit(buyerId, promotion);

        UserPromotion userPromotion = makeUserPromotion(buyerId, promotion);
        Response<Long> createResp = userPromotionWriteService.create(userPromotion);
        if (!createResp.isSuccess()) {
            log.error("fail to create user promotion:{} when buyer(id={}) receive coupon,cause:{}",
                    userPromotion, buyerId, createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        final String redisTrackName = getPromotionTrackNameAtRedis(promotionId);
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.incr(redisTrackName);
        } catch (Exception ex) {
            log.error("{} failed to increase the promotion(id:{}) data", LogUtil.getClassMethodName(), promotionId, ex);
        }
    }

    /**
     * 获取PromotionTrack在Redis中的名字
     *
     * @param promotionId 优惠价Id
     * @return promotionTrackIndex
     */
    private String getPromotionTrackNameAtRedis(Long promotionId) {
        return String.format("%s_receivedQuantity:%s", PromotionTrack.class.getSimpleName(), promotionId);
    }

    /**
     * 给买家发券(运营或者系统调用)
     *
     * @param buyerIds    买家id列表
     * @param promotionId 券id
     * @param quantity    每个买家发放券的数量
     */
    public void send(List<Long> buyerIds, Long promotionId, int quantity) {
        Promotion promotion = findCoupon(promotionId);
        if (!Objects.equal(promotion.getShopId(), 0L)) {
            log.error("the coupon(promotion id={}) is not belong to platform",
                    promotion.getId());
            throw new JsonResponseException("coupon.not.belong.to.platform");
        }

        //检查优惠券状态
        checkPromotionStatus(promotion);

        //检查是否已经达到发放数量限制
        checkSendLimit(promotion, buyerIds.size() * quantity);

        List<UserPromotion> userPromotions = Lists.newArrayList();
        for (Long buyerId : buyerIds) {
            int remain = quantity;
            while (remain > 0) {
                //目前优惠券实现为不可叠加券
                userPromotions.add(makeUserPromotion(buyerId, promotion));
                remain--;
            }
        }

        Response<List<Long>> createResp = userPromotionWriteService.batchCreate(userPromotions);
        if (!createResp.isSuccess()) {
            log.error("fail to batch create user promotions:{},cause:{}",
                    userPromotions, createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        final String redisTrackName = getPromotionTrackNameAtRedis(promotionId);
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.incrBy(redisTrackName, (long) quantity * buyerIds.size());
        } catch (Exception ex) {
            log.error("{} failed to increase the promotion(id:{}) data", LogUtil.getClassMethodName(), promotionId, ex);
        }
    }

    public boolean hasMeetReceiveLimit(Promotion promotion, int receiveQuantity) {
        if (promotion.getExtra().containsKey("receiveLimit")) {
            int receiveLimit = Integer.parseInt(promotion.getExtra().get("receiveLimit"));
            return receiveQuantity >= receiveLimit;
        }
        return false;
    }

    private UserPromotion makeUserPromotion(Long buyerId, Promotion promotion) {
        UserPromotion userPromotion = new UserPromotion();
        userPromotion.setUserId(buyerId);
        userPromotion.setShopId(promotion.getShopId());
        userPromotion.setPromotionId(promotion.getId());
        userPromotion.setType(promotion.getType());
        userPromotion.setName(promotion.getName());
        userPromotion.setAvailableQuantity(1);
        userPromotion.setFrozenQuantity(0);
        userPromotion.setStatus(promotion.getStatus());
        userPromotion.setStartAt(promotion.getStartAt());
        userPromotion.setEndAt(promotion.getEndAt());
        return userPromotion;
    }

    private Promotion findCoupon(Long promotionId) {
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);
        if (!PromotionType.isCoupon(promotion.getType())) {
            log.error("the promotion(id={}) is not a coupon", promotionId);
            throw new JsonResponseException("coupon.not.found");
        }
        return promotion;
    }

    private void checkPromotionStatus(Promotion promotion) {
        final Long promotionId = promotion.getId();
        switch (PromotionStatus.fromInt(promotion.getStatus())) {
            case INIT -> {
                log.error("coupon(promotion id={}) is not published", promotionId);
                throw new JsonResponseException("coupon.not.published");
            }
            case STOP -> {
                log.error("coupon(promotion id={}) was stopped", promotionId);
                throw new JsonResponseException("coupon.was.stopped");
            }
            default -> {
                Date now = new Date();
                if (now.after(promotion.getEndAt())) {
                    log.error("coupon(promotion id={}) is expire", promotionId);
                    throw new JsonResponseException("coupon.is.expire");
                }
            }
        }
    }

    /**
     * 判断发送数量是否达到上限
     * 从Redis中读取数量缓存
     *
     * @param promotion 优惠卷
     * @param delta     数量
     */
    private void checkSendLimit(Promotion promotion, int delta) {
        Map<String, String> extra = promotion.getExtra();
        if (extra != null && StringUtils.hasText(extra.get("sendLimit"))) {
            var sendLimit = new BigDecimal(extra.get("sendLimit"));
            if (sendLimit.setScale(0, RoundingMode.DOWN).compareTo(BigDecimal.valueOf(-1)) == 0){
                // never track the number for that has no limit
                return;
            }
            final String redisId = getPromotionTrackNameAtRedis(promotion.getId());
            String quantityStr;
            try (Jedis jedis = jedisPool.getResource()) {
                quantityStr = jedis.get(redisId);
            } catch (Exception ex) {
                log.error("{} failed to get jedis promotionId:{}", LogUtil.getClassMethodName(), promotion.getId(), ex);
                quantityStr = null;
            }
            final Integer receivedQuantity = NumberUtil.parseNumber(quantityStr, Integer.TYPE).orElseGet(() ->
            {
                Response<PromotionTrack> findPromotionTrack = promotionTrackReadService.findByPromotionId(promotion.getId());
                if (!findPromotionTrack.isSuccess()) {
                    log.error("fail to find promotion track by promotionId={},cause:{}",
                            promotion.getId(), findPromotionTrack.getError());
                    throw new JsonResponseException(findPromotionTrack.getError());
                }
                PromotionTrack promotionTrack = findPromotionTrack.getResult();
                try (Jedis jedis = jedisPool.getResource()) {
                    jedis.setnx(redisId, "" + promotionTrack.getReceivedQuantity());
                } catch (Exception ex) {
                    log.error("{} failed to set quantity cache promotionId:{}", LogUtil.getClassMethodName(), promotion.getId(), ex);
                }
                return promotionTrack.getReceivedQuantity();
            });
            if (BigDecimal.valueOf( receivedQuantity + delta ).compareTo( sendLimit) > 0) {
                log.error("coupon(promotionId={}) send over", promotion.getId());
                throw new JsonResponseException("coupon.send.over");
            }
        }
    }

    private void checkReceiveLimit(Long buyerId, Promotion promotion) {
        Response<List<UserPromotion>> findResp = userPromotionReadService.findByUserIdAndPromotionId(buyerId, promotion.getId());
        if (!findResp.isSuccess()) {
            log.error("fail to find user promotion by userId={},promotionId={},cause:{}",
                    buyerId, promotion.getId(), findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<UserPromotion> userPromotions = findResp.getResult();

        if (hasMeetReceiveLimit(promotion, userPromotions.size())) {
            log.error("user(id={}) has received coupon(promotionId={}) meet limit",
                    buyerId, promotion.getId());
            throw new JsonResponseException("user.has.received.coupon.already");
        }
    }

}
