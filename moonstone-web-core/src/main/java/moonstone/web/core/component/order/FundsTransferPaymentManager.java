package moonstone.web.core.component.order;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.utils.DateUtil;
import moonstone.order.api.payoperation.fundstransfer.FundsTransferCallbackResult;
import moonstone.order.api.payoperation.fundstransfer.FundsTransferOperation;
import moonstone.order.enu.FundsTransferPaymentRelatedTypeEnum;
import moonstone.order.enu.FundsTransferPaymentStatusEnum;
import moonstone.order.model.FundsTransferPayment;
import moonstone.order.model.Refund;
import moonstone.order.service.FundsTransferPaymentReadService;
import moonstone.order.service.FundsTransferPaymentWriteService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class FundsTransferPaymentManager {

    @Resource
    private FundsTransferPaymentReadService fundsTransferPaymentReadService;

    @Resource
    private FundsTransferPaymentWriteService fundsTransferPaymentWriteService;

    @Resource
    private List<FundsTransferOperation> operationList;


    /**
     * 根据退款单创建资金调拨单
     *
     * @param refund
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public FundsTransferPayment createByRefund(Refund refund) {
        var existed = findOngoing(refund.getId());
        log.info("返回正在进行中的资金调拨单 {}", JSONUtil.toJsonStr(existed));
        if (existed != null) {
            return existed;
        }
        log.info("没有正在进行中的资金调拨单");
        var target = convert(refund);

        create(target);

        return target;
    }

    /**
     * 发起资金调拨
     *
     * @param payment
     */
    public void pay(FundsTransferPayment payment) {
        // 发起
        log.info("发起资金调拨单 {}", JSONUtil.toJsonStr(payment));
        var result = operationList.stream()
                .filter(operation -> operation.supportedChannel(payment.getPayChannel()))
                .findAny()
                .map(operation -> operation.fundsTransferRequest(payment))
                .orElseThrow();

        // 更新
        var updateObject = new FundsTransferPayment();
        updateObject.setId(payment.getId());
        updateObject.setPayRequest(result.getRawRequestJson());
        updateObject.setPayResponse(result.getRawResponseJson());
        updateObject.setStatus(result.isSuccess() ? FundsTransferPaymentStatusEnum.PAYING.getCode() : FundsTransferPaymentStatusEnum.FAILED.getCode());
        updateObject.setTradeNo(result.getTradeNo());
        fundsTransferPaymentWriteService.update(updateObject);
    }

    /**
     * 根据回调结果进行更新
     *
     * @param callback
     */
    public void updateByCallback(FundsTransferCallbackResult callback) {
        var payment = fundsTransferPaymentReadService.findById(callback.getFundsTransferPaymentId()).getResult();

        var updateObject = new FundsTransferPayment();
        updateObject.setId(payment.getId());
        updateObject.setPayCallback(callback.getRawCallbackJson());
        updateObject.setTradeNo(callback.getTradeNo());
        updateObject.setStatus(callback.isPaySuccess() ? FundsTransferPaymentStatusEnum.PAID.getCode() :
                FundsTransferPaymentStatusEnum.FAILED.getCode());

        fundsTransferPaymentWriteService.update(updateObject);
    }

    private void create(FundsTransferPayment target) {
        var createResponse = fundsTransferPaymentWriteService.create(target);
        if (StringUtils.isNotBlank(createResponse.getError())) {
            throw new RuntimeException("FundsTransferPayment插入失败");
        }

        Long id = target.getId();
        if (id == null) {
            throw new RuntimeException("FundsTransferPayment.id 为空");
        }

        var updateObject = new FundsTransferPayment();
        updateObject.setId(id);
        updateObject.setOrderNo(buildOrderNo(id));
        var updateResponse = fundsTransferPaymentWriteService.update(updateObject);
        if (StringUtils.isNotBlank(updateResponse.getError())) {
            throw new RuntimeException("FundsTransferPayment.orderNo 更新失败");
        }

        target.setOrderNo(updateObject.getOrderNo());
    }

    private String buildOrderNo(Long id) {
        return "FTP" + DateUtil.toString(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS_SSS) + id;
    }

    /**
     * 对应单据是否存在进行中的资金调拨
     *
     * @param refundId
     * @return
     */
    private FundsTransferPayment findOngoing(Long refundId) {
        var response = fundsTransferPaymentReadService.findBy(
                refundId, FundsTransferPaymentRelatedTypeEnum.REFUND);
        if (StringUtils.isNotBlank(response.getError())) {
            throw new RuntimeException("FundsTransferPayment查询失败");
        }

        var list = response.getResult();
        log.info("查询到对应的调拨单信息 {}", JSONUtil.toJsonStr(list));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        var statusList = List.of(FundsTransferPaymentStatusEnum.PAYING.getCode(), FundsTransferPaymentStatusEnum.NOT_PAID.getCode());
        return list.stream()
                .filter(entity -> statusList.contains(entity.getStatus()))
                .findAny()
                .orElse(null);
    }

    private FundsTransferPayment convert(Refund refund) {
        var target = new FundsTransferPayment();

        target.setAmount(refund.getFee());
        target.setCreatedBy(0L);
        target.setIsValid(DataValidEnum.VALID.getCode());
        target.setPayChannel(refund.getChannel());

        //target.setPaidAt();
        //target.setPayCallback();
        //target.setPayRequest();
        //target.setPayResponse();
        //target.setTradeNo();

        target.setRelatedOrderId(refund.getId());
        target.setRelatedOrderType(FundsTransferPaymentRelatedTypeEnum.REFUND.getCode());
        target.setShopId(refund.getShopId());
        target.setStatus(FundsTransferPaymentStatusEnum.NOT_PAID.getCode());
        target.setUpdatedBy(0L);

        return target;
    }
}
