package moonstone.web.core.component.cache;

import blue.sea.moonstone.bridge.app.ShareDataHelper;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import io.vertx.core.shareddata.AsyncMap;
import io.vertx.ext.mongo.MongoClient;
import moonstone.web.core.component.Mongo;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SkuTaxCacheAtMongo {

    @Resource
    private Vertx vertx;

    @EventListener(ContextRefreshedEvent.class)
    public void onload() {
        vertx.eventBus().<Buffer>consumer("SkuTaxCache").handler(this::saveCache);
        vertx.eventBus().<Buffer>consumer("remove-tax-cache").handler(this::removeCache);
        vertx.runOnContext(v -> readCache());
    }

    private void removeCache(Message<Buffer> bufferMessage) {
        Mongo.Pool.getMongo(vertx).removeDocument("SkuTaxCache", new JsonObject());
        vertx.sharedData().getAsyncMap("SkuTaxCache")
                .onSuccess(AsyncMap::clear);
    }

    private void readCache() {
        JsonObject config = ShareDataHelper.getMongoConfig(vertx);
        MongoClient client = Mongo.Pool.getMongo(vertx);
        client.find("SkuTaxCache", new JsonObject().put("cachedAt",
                new JsonObject().put("$gte", System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000)))
                .onSuccess(taxCache -> {
                    vertx.sharedData().getAsyncMap("SkuTaxCache")
                            .onSuccess(cacheMap -> {
                                for (JsonObject cache : taxCache) {
                                    String id = (String) cache.remove("id");
                                    cacheMap.putIfAbsent(id, cache);
                                }
                            });
                });
    }

    public void saveCache(Message<Buffer> msg) {
        // save it into the mongo
        MongoClient client = Mongo.Pool.getMongo(vertx);
        client.save("SkuTaxCache", msg.body().toJsonObject());
    }
}
