package moonstone.web.core.component.vertx;

import co.elastic.apm.api.ElasticApm;
import co.elastic.apm.api.Scope;
import co.elastic.apm.api.Transaction;
import com.alibaba.fastjson.JSON;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
public class EventBusMiddleWare implements BeanPostProcessor {

    Promise<Vertx> vertxPromise = Promise.promise();

    public static final Map<String, List<String>> eventHandlerMap = new HashMap<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        for (Method method : bean.getClass().getMethods()) {
            if (method.isAnnotationPresent(VertxEventBusListener.class)) {
                register(bean);
                log.info("EventBusMiddleWare {} {}", method.getDeclaringClass().getName(), method.getName());
                return bean;
            }
        }
        if (bean instanceof Vertx vertx) {
            vertxPromise.complete(vertx);
        }
        return bean;
    }

    /**
     * register other Bean
     *
     * @param bean bean that contain method should observe message from vertx
     */
    public void register(Object bean) {
        vertxPromise.future().onSuccess(vertx -> {
            for (Method method : bean.getClass().getMethods()) {
                if (method.isAnnotationPresent(VertxEventBusListener.class)) {
                    VertxEventBusListener handler = method.getAnnotation(VertxEventBusListener.class);

                    putToMap(bean, method, handler);

                    vertx.eventBus().<Buffer>consumer(handler.value().getName())
                            .handler(m -> vertx.executeBlocking(p -> {
//                                handleMessageWithMethod(method, bean, m);
//                                p.complete();
                                String methodName = bean.getClass().getSimpleName() + "#" + method.getName();
                                Transaction transaction = null;
                                Scope scope = null;
                                try{
                                    transaction = ElasticApm.startTransaction();
                                    transaction.setType("vertx-event");
                                    transaction.setName(methodName);
                                    scope = transaction.activate();

                                    handleMessageWithMethod(method, bean, m);
                                    p.complete();
                                }catch (Exception e){
                                    e.printStackTrace();
                                }finally {
                                    try {
                                        if(transaction != null){
                                            transaction.end();
                                        }
                                        if(scope != null){
                                            if(scope != null && scope instanceof Scope){
                                                scope.close();
                                            }
                                        }
                                    }catch (Exception e){
                                        log.warn("关闭apm事务失败", e);
                                    }
                                }
                            }, r -> {
                            }));
                }
            }
        });
    }

    /**
     * try to deserialize the json into Object, Override this if you hate the FAST_JSON
     *
     * @param json json
     * @return the POJO
     */
    protected Object deserialize(String json, Type type) {
        return JSON.parseObject(json, type);
    }

    /**
     * register the method with the message so that it can react with the message broadcast at vertx
     *
     * @param method  method with Annotation
     * @param bean    the bean will be act
     * @param message message body
     */
    private void handleMessageWithMethod(Method method, Object bean, Message<Buffer> message) {
        boolean open = method.canAccess(bean);
        try {
            method.setAccessible(true);
            if (method.getParameterCount() == 1) {
                Parameter parameter = method.getParameters()[0];
                if (parameter.getType().equals(Message.class)) {
                    method.invoke(bean, message);
                } else if (parameter.getType().equals(message.body().getClass())) {
                    method.invoke(bean, message.body());
                } else if (parameter.getType().equals(String.class)) {
                    method.invoke(bean, message.body().toString());
                } else if (parameter.getType().equals(JsonObject.class)) {
                    method.invoke(bean, message.body().toJsonObject());
                } else if (parameter.getType().equals(JsonArray.class)) {
                    method.invoke(bean, message.body().toJsonArray());
                } else {
                    method.invoke(bean, deserialize(message.body().toString(), parameter.getParameterizedType()));
                }
            } else if (method.getParameterCount() == 0) {
                LoggerFactory.getLogger(bean.getClass()).warn("Bean[{}] Method[{}] receive no parameter at Address[{}]", bean, method.getName(), message.address());
                method.invoke(bean);
            } else {
                LoggerFactory.getLogger(bean.getClass()).error("Bean[{}] Method[{}] receive to many parameter[{}] at Address[{}]", bean, method.getName(), method.getParameterCount(), message.address());
                throw new IllegalStateException(String.format("Bean[%s] Method[%s] Receive too many Parameter[%s] at Address[%s]", bean.getClass().getName(), method.getName(), method.getParameterCount(), message.address()));
            }
        } catch (InvocationTargetException invocationTargetException){
            LoggerFactory.getLogger(bean.getClass()).error("Fail to handle message from [{}] with [{}] at Method[{}:{}]"
                    , message.address(), message.headers(), bean.getClass().getName(), method.getName(), invocationTargetException.getCause());
        }
        catch (Exception exception) {
            LoggerFactory.getLogger(bean.getClass()).error("Fail to handle message from [{}] with [{}] at Method[{}:{}]"
                    , message.address(), message.headers(), bean.getClass().getName(), method.getName(), exception);
        } finally {
            method.setAccessible(open);
        }
    }


    private void putToMap(Object bean, Method method, VertxEventBusListener handler) {
        String eventName = handler.value().getName();
        List<String> list = eventHandlerMap.get(eventName);
        if(CollectionUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        list.add(bean.getClass().getName() + "#" + method.getName());
        eventHandlerMap.put(eventName, list);
    }

}
