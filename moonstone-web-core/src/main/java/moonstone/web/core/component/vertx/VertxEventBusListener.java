package moonstone.web.core.component.vertx;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface VertxEventBusListener {
    /**
     * @return the class type of message at Vertx
     */
    Class<?> value() default Object.class;
}
