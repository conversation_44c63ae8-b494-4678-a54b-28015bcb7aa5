package moonstone.web.core.component.item.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@Data
public class PriceModifyDTO {

    String modify;

    List<SkuPriceModifyDTO> skuPriceModifyList;

    public PriceModifyDTO() {
    }

    public List<SkuPriceModifyDTO> getSkuPriceModifyList() {
        return skuPriceModifyList != null ? skuPriceModifyList : modify == null ? null : Stream.of(modify.split(",")).filter(StringUtils::hasText).map(SkuPriceModifyDTO::new).collect(Collectors.toList());
    }

    @Data
    @AllArgsConstructor
    public static class SkuPriceModifyDTO {
        // 单品Id
        Long skuId;

        // 修改类型
        Integer type;

        // 价格 (由修改类型变化)
        Long price;

        Boolean taxBear = false;

        Long itemId;

        Long weShopItemId;

        public SkuPriceModifyDTO() {
        }

        public SkuPriceModifyDTO(Long skuId, Integer type, Long price, Boolean taxBear, Long itemId) {
            this.skuId = skuId;
            this.type = type;
            this.price = price;
            this.taxBear = taxBear;
            this.itemId = itemId;
        }

        SkuPriceModifyDTO(String skuPriceModifyPart) {
            String[] splitStr = skuPriceModifyPart.split("_");
            if (splitStr.length < 1) return;
            skuId = Long.parseLong(splitStr[0]);
            if (splitStr.length < 2) return;
            type = Integer.parseInt(splitStr[1]);
            if (splitStr.length < 3) return;
            price = Long.parseLong(splitStr[2]);
        }
    }
}
