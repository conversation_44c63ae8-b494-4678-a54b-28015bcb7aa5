package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.UserRole;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.Shop;
import moonstone.user.model.SubSeller;
import moonstone.user.model.User;
import moonstone.user.service.SellerReadService;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.SubManagerJudge;
import moonstone.user.service.UserReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;


@Component
@Slf4j
public class SubManagerJudgeImpl implements SubManagerJudge {
    @Autowired
    private StoreProxyReadService storeProxyReadService;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private SellerReadService sellerReadService;
    @Autowired
    private ShopCacheHolder shopCacheHolder;

    @Override
    public boolean isSubManager(Long shopId, Long userId) {
        if (userId == null) return false;
        Shop shop = shopCacheHolder.findShopById(shopId);
        if (shop.getUserId().equals(userId))
            return true;
        String shopStyle = (Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new).getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()));
        switch (shopStyle) {
            case "ladderDistribution": {

            }
            case "weShop":
            default: {
                if (Objects.equals(UserUtil.getUserId(), userId)) {
                    CommonUser commonUser = UserUtil.getCurrentUser();
                    if (commonUser.getRoles() != null && commonUser.getRoles().contains(UserRole.ADMIN.name()))
                        return true;
                    return Objects.equals(commonUser.getShopId(), shopId);
                }
                User user = userReadService.findById(userId).getResult();
                if (user == null) return false;
                // 管理员则可以浏览所有数据
                if (user.getRoles() != null && user.getRoles().contains(UserRole.ADMIN.name())) return true;
                // 下级用户也可以浏览
                if (Objects.equals(user.getType(), 3)) {
                    SubSeller subSeller = sellerReadService.findSubSellerByUserId(userId).getResult().orElse(null);
                    return subSeller != null && Objects.equals(subSeller.getMasterUserId(), shop.getUserId());
                }
                return false;
            }
        }
    }
}
