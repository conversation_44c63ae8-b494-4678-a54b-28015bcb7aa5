package moonstone.web.core.component.distribution;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.weShop.model.TaxRate;
import moonstone.weShop.service.TaxRateReadService;
import moonstone.weShop.service.TaxRateWriteService;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WithDrawTaxWriteService {
    @RpcConsumer
    TaxRateWriteService taxRateWriteService;
    @RpcConsumer
    TaxRateReadService taxRateReadService;

    public Response<Long> newTaxRate(@Nullable Long shopId, @Nullable Long weShopId, Long taxRate) {
        //todo change it
        TaxRate tax;
        if (shopId == null) {
            try {
                tax = taxRateReadService.getOneShopId(-1L).getResult();
                if (tax == null) {
                    tax = new TaxRate();
                }
            } catch (Exception ex) {
                tax = new TaxRate();
            }
            tax.setShopId(-1L);
            tax.setTaxRate(taxRate);
            return taxRateWriteService.save(tax);
        } else {
            try {
                List<TaxRate> taxRates = taxRateReadService.findByShopId(shopId).getResult();
                tax = taxRates.stream()
                        .filter(tr -> tr.getWeShopId().equals(weShopId))
                        .collect(Collectors.toList())
                        .get(0);
                if (tax == null)
                    tax = new TaxRate();
            } catch (Exception ex) {
                tax = new TaxRate();
            }
            tax.setTaxRate(taxRate);
            tax.setShopId(shopId);
            if (weShopId == null) {
                tax.setWeShopId(-1L);
            } else {
                tax.setWeShopId(weShopId);
            }
            return taxRateWriteService.save(tax);
        }
    }
}
