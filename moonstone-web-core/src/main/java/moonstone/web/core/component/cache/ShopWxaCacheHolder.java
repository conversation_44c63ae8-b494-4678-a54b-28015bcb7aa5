package moonstone.web.core.component.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ShopWxaCacheHolder {
    @Autowired
    private ShopWxaReadService shopWxaReadService;
    @Autowired
    private ShopWxaProjectReadService shopWxaProjectReadService;
    LoadingCache<Long, ShopWxa> shopWxaLoadingCache = Caffeine
            .newBuilder().expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(200)
            .build(shopId -> Optional.ofNullable(shopWxaReadService.findByShopId(shopId).getResult())
                    .orElseGet(ArrayList::new)
                    .stream().filter(releaseStatusShopWxa -> releaseStatusShopWxa.getStatus() >= ShopWxaStatus.NORMAL.getValue())
                    .findFirst().orElse(null)
            );

    LoadingCache<String, ShopWxa> shopWxaByAppId = Caffeine.newBuilder().maximumSize(100).expireAfterAccess(5, TimeUnit.MINUTES)
            .build(appId -> shopWxaReadService.findByAppId(appId).getResult());
    LoadingCache<Long, Long> shopWxaIdByProjectId = Caffeine.newBuilder().maximumSize(100)
            .expireAfterWrite(Duration.ofMinutes(5))
            .build(projectId -> Optional.of(projectId)
                    .map(shopWxaProjectReadService::findById)
                    .map(Response::getResult)
                    .map(ShopWxaProject::getShopWxaId)
                    .orElseThrow(() -> Translate.exceptionOf("查找项目失败"))
            );

    public ShopWxa findShopWxaByProjectId(Long projectId) {
        return shopWxaReadService.findById(Objects.requireNonNull(shopWxaIdByProjectId.get(projectId))).getResult();
    }

    public ShopWxa findReleaseOneForShopId(Long shopId) {
        return shopWxaLoadingCache.get(shopId);
    }

    public ShopWxa findByAppId(String appId) {
        return shopWxaByAppId.get(appId);
    }


}
