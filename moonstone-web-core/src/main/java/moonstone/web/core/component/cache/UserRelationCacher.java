package moonstone.web.core.component.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Optional;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

@Service
@RpcProvider
@Slf4j
public class UserRelationCacher {
    @RpcConsumer
    UserRelationEntityReadService userRelationEntityReadService;
    LoadingCache<String, Optional<UserRelationEntity>> cache;

    @PostConstruct
    private void init() {
        cache = Caffeine
                .newBuilder()
                .expireAfterAccess(10, TimeUnit.MINUTES)
                .build(key -> {
                    Long[] args = Stream.of(key.split(",")).map(Long::valueOf)
                            .toArray(Long[]::new);
                    if (args.length < 3) {
                        log.error("{} invalid args({}) for cache,(key={})", LogUtil.getClassMethodName(), Arrays.toString(args), key);
                        return Optional.empty();
                    }
                    return Optional.ofNullable(userRelationEntityReadService.findByUserIdAndRelationIdAndType(args[0], args[1], UserRelationEntity.UserRelationType.from(args[2].intValue()).orElse(UserRelationEntity.UserRelationType.Member)).getResult());
                });
    }

    public Optional<UserRelationEntity> get(Long userId, Long relationId, UserRelationEntity.UserRelationType type) {
        try {
            return cache.get(userId + "," + relationId + "," + type.getType());
        } catch (Exception ex) {
            log.error("{} ex:{}", LogUtil.getClassMethodName(), ex);
            return Optional.empty();
        }
    }

    public void refresh(Long userId, Long relationId, UserRelationEntity.UserRelationType type) {
        refresh(userId + "," + relationId + "," + type.getType());
    }

    public void refresh(String key) {
        cache.refresh(key);
    }

    public ConcurrentMap<String, Optional<UserRelationEntity>> asMap() {
        return cache.asMap();
    }

    @Nullable
    public UserRelationEntity getIfPresent(Object key) {
        return cache.getIfPresent(key.toString()).orElse(null);
    }

    public void put(Long userId, Long relationId, UserRelationEntity.UserRelationType type, UserRelationEntity entity) {
        cache.put(userId + "," + relationId + "," + type.getType(), Optional.of(entity));
    }

    public void put(String key, UserRelationEntity value) {
        cache.put(key, Optional.of(value));
    }

    public void invalidate(Long userId, Long relationId, UserRelationEntity.UserRelationType type) {
        invalidate(userId + "," + relationId + "," + type.getType());
    }

    public void invalidate(String key) {
        cache.invalidate(key);
    }

    public void invalidateAll() {
        cache.invalidateAll();
    }

    public void cleanUp() {
        cache.cleanUp();
    }
}
