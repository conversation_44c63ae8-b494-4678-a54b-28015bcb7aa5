package moonstone.web.core.component.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.Y800ServiceName;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.model.Y800OpenRequest;
import moonstone.common.model.Y800ResponseModel;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.OutSideRefundRealExecutor;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.model.Y800RefundReponse;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.web.core.component.api.Y800V3Api;
import moonstone.web.core.component.api.bo.y800v3.Y800OrderQuery;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class Y800OrderRefundExecutor implements OutSideRefundRealExecutor {
    @Value("${Y800.open.api.gate}")
    protected String y800YangSupport;
    @Value("${Y800.partnerCode}")
    protected String y800PartnerCode;
    @Value("${Y800.partnerKey}")
    protected String y800PartnerKey;
    @Autowired
    protected Y800OrderIdGenerator orderIdGenerator;
    @Autowired
    protected ThirdPartyUserShopReadService thirdPartyUserShopReadService;
    @Autowired
    protected ThirdPartyUserReadService thirdPartyUserReadService;
    @Autowired
    protected SourceShopQuerySlice sourceShopQuerySlice;
    @Autowired
    protected ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @Getter
    ThirdPartySystem thirdPartySystem = ThirdPartySystem.Y800_V2;
    @RemoteAPI
    Y800V3Api y800V3Api;

    @Override
    public Either<Boolean> executeRefund(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        log.info("发起Y800订单退款请求 Y800OrderRefundExecutor");
        if (skuOrderList == null || shopOrder == null) {
            log.error("{} shopOrder:{} skuOrder:{}", LogUtil.getClassMethodName(), JSON.toJSONString(shopOrder), JSON.toJSONString(skuOrderList));
            return Either.error(new Translate("订单数据非法").toString());
        }
        if (isOrderCanceled(shopOrder, skuOrderList)) {
            return Either.ok(true);
        }
        Optional<Y800OpenRequest> requestOpt = packRefundRequest(shopOrder);
        if (skuOrderList.stream().map(SkuOrder::getPushStatus).noneMatch(Predicate.isEqual(SkuOrderPushStatus.FINISHED.value()))) {
            log.debug("{} shopOrderId:{} haven't been pushed", LogUtil.getClassMethodName("skip-refund-on-Y800"), shopOrder.getId());
            return Either.ok(true);
        }
        if (requestOpt.isEmpty()) {
            return Either.error(new Translate("封装退款信息失败").toString());
        }
        String apiUrl = y800YangSupport;
        try {
            log.debug("{} request:{}", LogUtil.getClassMethodName(), requestOpt.get().toMap());
            HttpRequest request = HttpRequest.post(apiUrl);
            request.form(requestOpt.get().toMap());
            if (request.ok()) {
                Y800ResponseModel<Y800RefundReponse> refundResponse = JSON.parseObject(request.body(), new TypeReference<>() {
                });
                log.debug("{} shopOrderId:{} response:{}", LogUtil.getClassMethodName(), shopOrder.getId(), refundResponse);
                if (Objects.equals(refundResponse.getActionCode(), "ORDER_CANCELED")) {
                    log.debug("{} shopOrderId:{} response:{}", LogUtil.getClassMethodName("ORDER_CANCELED"), shopOrder.getId(), refundResponse);
                    return Either.ok(true);
                }
                if (!refundResponse.isSuccess()) {
                    log.error("{} shopOrderId:{} response:{}", LogUtil.getClassMethodName(), shopOrder.getId(), refundResponse);
                    return Either.error(refundResponse.getErrorMsg());
                }
                log.info("{} shopOrderId:{} data:{}", LogUtil.getClassMethodName(), shopOrder.getId(), refundResponse.getData());
                if (refundResponse.getData() == null && refundResponse.isSuccess()) {
                    log.warn("{} order is disable maybe so canceled,", LogUtil.getClassMethodName());
                    return Either.ok(true);
                }
                if (refundResponse.getData() != null) {
                    if (refundResponse.getData().isSuccess()) {
                        return Either.ok(true);
                    }
                    if (Objects.equals(refundResponse.getData().getResultMsg(), "订单已被取消")) {
                        return Either.ok(true);
                    }
                    if (Objects.equals(refundResponse.getData().getResultCode(), "ORDER_CANCELED")) {
                        return Either.ok(true);
                    }
                }
                log.error("{} shopOrderId:{} reason:{}", LogUtil.getClassMethodName(), shopOrder.getId(), refundResponse.getData().getResultMsg());
                return Either.error(String.format("%s 错误信息：%s", new Translate("%s", refundResponse.getErrorMsg()), refundResponse.getErrorMsg()));
            } else {
                log.error("{} request:{} msg:{} shopOrderId:{}", LogUtil.getClassMethodName(), request, request.message(), shopOrder.getId());
                throw new RuntimeException("service-error:" + request.message());
            }
        } catch (Exception ex) {
            log.error("{} shopOrderId:{}", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
            return Either.error(new Translate("退款请求失败").toString());
        }
    }

    private boolean isOrderCanceled(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        try {
            try (Y800V3Api api = y800V3Api) {
                api.setAppId(y800PartnerCode);
                api.setSecret(y800PartnerKey);
                var code = skuOrderList.get(0).getDepotCode();
                if (code == null){
                    var skuOrder = skuOrderList.get(0);
                    var outerSkuId = skuOrder.getOuterSkuId();
                    var system = 1;
                    code = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(skuOrder.getShopId(), system, outerSkuId)
                            .getResult().stream()
                            .map(ThirdPartySkuStock::getDepotCode)
                            .filter(Objects::nonNull).findFirst().orElse("");
                }
                var info = api.deliveryGetDetail(new Y800OrderQuery(shopOrder.getDeclaredId(),
                        readAccessCode(shopOrder.getShopId()).orElseThrow(() -> new RuntimeException("获取AccessCode失败"))
                        , code));
                log.info("Order[{}] Info -> {}", shopOrder.getDeclaredId(), info);
                return "CANCELED".equals(info.getResult().status());
            }
        } catch (Exception e) {
            log.error("Fail to query order[{}] status", shopOrder.getDeclaredId(), e);
            return false;
        }
    }

    private Optional<Y800OpenRequest> packRefundRequest(ShopOrder shopOrder) {
        try {
            RefundBizData bizData = new RefundBizData();
            // 获取shopOrder的商家的信息
            bizData.setAccessCode(readAccessCode(shopOrder.getShopId()).orElseThrow(() -> new RuntimeException("获取AccessCode失败")));
            if (sourceShopQuerySlice.queryProjectIdByShopIdAndSource(shopOrder.getShopId(), MirrorSource.GongXiao.name()).isSuccess()
                    && Objects.equals(OrderOutFrom.WE_SHOP.Code(), shopOrder.getOutFrom())) {
                bizData.setAccessCode("webB2C" + thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), shopOrder.getReferenceId())
                        .map(ThirdPartyUser::getThirdPartyId).orElse(""));
            }
            // 设置其推送的id
            bizData.setOrderNo(orderIdGenerator.getDeclareId(shopOrder));

            Y800OpenRequest request = new Y800OpenRequest();
            request.setPartnerId(y800PartnerCode);
            request.setBizData(bizData);
            request.setServiceName(Y800ServiceName.OrderCanCel.getServiceName());
            request.sign(y800PartnerKey);
            return Optional.of(request);
        } catch (Exception ex) {
            log.error("{} shopOrderId:{} line", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
            return Optional.empty();
        }
    }

    private Optional<String> readAccessCode(Long shopId) {
        return Optional.ofNullable(thirdPartyUserShopReadService
                .findByThirdPartyIdAndShopId(thirdPartySystem.Id(), shopId).getResult())
                .map(ThirdPartyUserShop::getThirdPartyCode);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    static class RefundBizData extends Y800OpenRequest.BizData {
        /// 推送过去的单号,由于目前申报单号的影响,需要注意不同关区生成不同单号
        String orderNo;

        public String getOutOrderNo() {
            return orderNo;
        }
    }
}
