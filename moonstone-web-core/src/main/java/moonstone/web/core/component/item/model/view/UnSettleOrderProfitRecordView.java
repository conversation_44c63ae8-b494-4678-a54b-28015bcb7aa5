package moonstone.web.core.component.item.model.view;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UnSettleOrderProfitRecordView {

    // 订单来源指 xx门店
    String orderFrom;

    List<UnSettleSkuOrderView> skuOrderList;
    // 买家信息
    String buyerName;
    String buyerAvatar;

    Integer quantity;

    // 出售的价格
    Long orderSellPrice;
    // 该单带来的利润
    Long profit;

    // 订单状态
    String status;
    Date orderAt;

    @Data
    public static class UnSettleSkuOrderView {
        String itemName;
        Integer quantity;
        String image;
        Long originPrice;
        /**
         * 型号
         */
        String specification;
    }
}
