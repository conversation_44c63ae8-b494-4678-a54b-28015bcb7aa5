package moonstone.web.core.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableBiMap;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Handler;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.ErrorEmailSender;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.cache.DeveloperEmailAddressCache;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.events.msg.listener.MsgSendRequestListener;
import moonstone.web.core.model.ErrorEmailRecord;
import moonstone.web.core.model.ErrorMailResendMark;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Component
@Slf4j
public class EmailSenderVerticle extends AbstractVerticle implements ErrorEmailSender {

    final int MAX_TURN = 5;
    final int TIME_SEPARATED = 20;
    @Autowired
    DeveloperEmailAddressCache developerEmailAddressCache;
    @Autowired
    MsgSendRequestListener msgSendRequestListener;
    @Autowired
    EnvironmentConfig environmentConfig;
    @Autowired
    MongoTemplate mongoTemplate;

    @Override
    public void start() {
        vertx.eventBus().consumer(ErrorWarnMsg.class.getName(), sendMsgHandle());
        vertx.eventBus().consumer(ErrorWarnMsg.class.getName() + "#suppress", increaseSuppressHandle());
        vertx.eventBus().consumer(ErrorWarnMsg.class.getName() + "#send", findAndSendHandle());
        vertx.eventBus().consumer("email-send", this::sendEmailByEvent);
    }

    private void sendEmailByEvent(Message<Buffer> tMessage) {
        JsonObject body = tMessage.body().toJsonObject();
        String title = body.getString("title");
        //String group = body.getString("group");
        String data = body.getString("data");
        List<String> senderList = Optional.ofNullable(developerEmailAddressCache.getAll()).orElse(Collections.emptyList());
        Map<String, Serializable> map = new HashMap<>(ImmutableBiMap.of("title", title,
                "env", environmentConfig.getEnv(),
                "location", "Order",
                "errorCode", "没有错误",
                "data", "消息提醒"));
        map.put("errorMsg", data);
        vertx.executeBlocking(p -> {
            for (String email : senderList) {
                MsgSendRequestEvent msgSendRequestEvent = new MsgSendRequestEvent(email,
                        "email.error.notice",
                        map);
                msgSendRequestListener.msgSendRequest(msgSendRequestEvent);
            }
        }, false);
    }

    Handler<Message<Buffer>> findAndSendHandle() {
        return message -> vertx.executeBlocking(promise -> {
            String[] args = message.body().toString().split("-");
            long epochDay = Long.parseLong(args[0]);
            message.reply(findAndSend(args.length > 1 ? args[1] : null, args.length > 2 ? args[2] : null, epochDay));
            promise.complete();
        }, result -> {
            if (result.failed()) {
                message.fail(1, result.cause().getMessage());
            }
        });
    }

    Handler<Message<Buffer>> increaseSuppressHandle() {
        return message -> vertx.executeBlocking(promise -> {
            String[] args = message.body().toString().split("-");
            long epochDay = Long.parseLong(args[0]);
            message.reply(increaseTurn(args.length > 1 ? args[1] : null, args.length > 2 ? args[2] : null, epochDay));
            promise.complete();
        }, result -> {
            if (result.failed()) {
                message.fail(1, result.cause().getMessage());
            }
        });
    }

    Handler<Message<Buffer>> sendMsgHandle() {
        return message -> vertx.executeBlocking(result -> {
            sendEmail(JSON.parseObject(message.body().getBytes(), ErrorWarnMsg.class));
            result.complete();
        }, result -> {
            if (result.failed()) {
                message.fail(1, result.cause().getMessage());
            }
        });
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    public void scanResendMark() {
        Date fiveMinusAge = Date.from(LocalDateTime.now().minusMinutes(TIME_SEPARATED).atZone(ZoneId.systemDefault()).toInstant());
        List<ErrorMailResendMark> markList = mongoTemplate.findAllAndRemove(Query.query(Criteria.where("createdAt").lte(fiveMinusAge)), ErrorMailResendMark.class);
        for (ErrorMailResendMark errorMailResendMark : markList) {
            Query query = Query.query(Criteria.where("uuid").is(errorMailResendMark.getUuid()))
                    .addCriteria(Criteria.where("hash").is(errorMailResendMark.getHash()))
                    .addCriteria(Criteria.where("epochDay").is(errorMailResendMark.getEpochDay()));
            for (ErrorEmailRecord errorEmailRecord : mongoTemplate.find(query, ErrorEmailRecord.class)) {
                try {
                    send(errorEmailRecord);
                } catch (Exception ex) {
                    log.error("{} fail to send record", LogUtil.getClassMethodName(), ex);
                }
            }
        }
    }

    // 应用内注
    @Override
    @EventListener(ErrorWarnMsg.class)
    public void sendEmail(ErrorWarnMsg errorWarnMsg) {
        // 存储信息实体
        ErrorEmailRecord errorEmailRecord = saveRecord(errorWarnMsg);
        // 调查是否已发送
        Optional<ErrorEmailRecord> allowSend = allowSend(errorEmailRecord);
        // 消音则不发送
        allowSend.ifPresent(this::send);
        if (allowSend.isPresent() && !Objects.equals(allowSend.get().getErrorDetail(), errorWarnMsg.getErrorDetail())) {
            log.error("{} hash impact for msg [{}] with record [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(errorWarnMsg), JSON.toJSONString(allowSend.orElse(errorEmailRecord)));
            EventSender.publish(ErrorWarnMsg.groupMsg("HASH-IMPACT", JSON.toJSONString(errorWarnMsg), EmailReceiverGroup.DEVELOPER));
        }
    }

    /**
     * 发送邮件信息
     *
     * @param errorEmailRecord 邮件信息
     */
    void send(ErrorEmailRecord errorEmailRecord) {
        if (!environmentConfig.isOnline()) {
            log.trace("{} suppress the email[{}] send at Test Environment", LogUtil.getClassMethodName(), errorEmailRecord.getContent());
            return;
        }
        String title = errorEmailRecord.getTitle() +
                String.format(" :: 总共[%s]此次{%s}", errorEmailRecord.getOccupySum(), errorEmailRecord.getOccupyBetween());
        MsgSendRequestEvent msgSendRequestEvent = new MsgSendRequestEvent(JSON.toJSONString(errorEmailRecord.getReceiver()),
                "email.error.notice", Map.of("location", "ERROR-Warn-Msg",
                "env", environmentConfig.getEnv(),
                "title", title,
                "errorCode", title,
                "data", String.format("<ul><li>[UUID] => %s</li><li>[HASH] => %s</li></ul>%s", errorEmailRecord.getUuid(), errorEmailRecord.getHash(), errorEmailRecord.getContent()),
                "errorMsg", Optional.ofNullable(errorEmailRecord.getErrorDetail()).orElse("")));
        msgSendRequestListener.msgSendRequest(msgSendRequestEvent);
    }

    // 增加目标错误信息的发送次数
    @Override
    public Integer increaseTurn(String uuid, String hash, long epochDay) {
        Query query = Query.query(Criteria.where("epochDay").is(epochDay));
        if (StringUtils.hasText(uuid)) {
            query.addCriteria(Criteria.where("uuid").is(uuid));
        }
        if (StringUtils.hasText(hash)) {
            query.addCriteria(Criteria.where("hash").is(hash));
        }
        if (!mongoTemplate.exists(query, ErrorEmailRecord.class)) {
            return 0;
        }
        return Optional.ofNullable(mongoTemplate.findAndModify(query, new Update().inc("allowTurn", 1), ErrorEmailRecord.class))
                .map(ErrorEmailRecord::getAllowTurn).orElse(2);
    }

    /**
     * 查询并且发送
     *
     * @param uuid     uuid信息
     * @param hash     hash信息
     * @param epochDay 当日
     * @return 发送的数量
     */
    @Override
    public Long findAndSend(String uuid, String hash, long epochDay) {
        Query query = Query.query(Criteria.where("epochDay").is(epochDay));
        if (StringUtils.hasText(uuid)) {
            query.addCriteria(Criteria.where("uuid").is(uuid));
        }
        if (StringUtils.hasText(hash)) {
            query.addCriteria(Criteria.where("hash").is(hash));
        }
        return mongoTemplate.find(query, ErrorEmailRecord.class)
                .stream().filter(Objects::nonNull).peek(this::send)
                .count();
    }

    /**
     * 是否允许发送
     *
     * @param errorEmailRecord 错误邮件信息
     *                         应该携带uuid hash和epochDay
     * @return Optional.of()=> 允许 empty => 不允许
     */
    public Optional<ErrorEmailRecord> allowSend(ErrorEmailRecord errorEmailRecord) {
        Date before = Date.from(LocalDateTime.now().minusMinutes(5).atZone(ZoneId.systemDefault()).toInstant());
        Query hasSend = Query.query(new Criteria().orOperator(Criteria.where("lastSend").exists(false), Criteria.where("lastSend").lte(before)))
                .addCriteria(Criteria.where("uuid").is(errorEmailRecord.getUuid()))
                .addCriteria(Criteria.where("hash").is(errorEmailRecord.getHash()));
        ErrorEmailRecord shouldSend = mongoTemplate.findAndModify(hasSend, Update.update("occupyBetween", 0).currentDate("lastSend"), ErrorEmailRecord.class);
        if (shouldSend == null) {
            if (errorEmailRecord.getOccupySum() >= Optional.ofNullable(errorEmailRecord.getAllowTurn()).orElse(1) * MAX_TURN) {
                log.warn("{} [uuid => {}, hash => {}] trigger the suppress", LogUtil.getClassMethodName(), errorEmailRecord.getUuid(), errorEmailRecord.getHash());
                return Optional.empty();
            }
            Query resendMark = Query.query(Criteria.where("epochDay").is(errorEmailRecord.getEpochDay()))
                    .addCriteria(Criteria.where("uuid").is(errorEmailRecord.getUuid()))
                    .addCriteria(Criteria.where("hash").is(errorEmailRecord.getHash()));
            mongoTemplate.upsert(resendMark, new Update().currentDate("createdAt"), ErrorMailResendMark.class);
            log.debug("{} mark resend errorMsg [ uuid => {}, hash => {}]", LogUtil.getClassMethodName(), errorEmailRecord.getUuid(), errorEmailRecord.getHash());
            return Optional.empty();
        }
        if (Optional.ofNullable(shouldSend.getOccupySum()).orElse(1) < Optional.ofNullable(shouldSend.getAllowTurn()).orElse(1) * MAX_TURN) {
            return Optional.of(shouldSend);
        }
        log.debug("{} suppress [ uuid => {}, hash => {}], for turn [{}]", LogUtil.getClassMethodName(), shouldSend.getUuid(), shouldSend.getHash(), Optional.ofNullable(shouldSend.getAllowTurn()).orElse(1));
        return Optional.empty();
    }

    /**
     * 将错误邮件存入数据库
     *
     * @param errorWarnMsg 错误信息
     * @return 错误信息
     */
    public ErrorEmailRecord saveRecord(ErrorWarnMsg errorWarnMsg) {
        ErrorEmailRecord record = new ErrorEmailRecord();
        BeanUtils.copyProperties(errorWarnMsg, record);

        List<String> receiverList = new ArrayList<>();
        Optional.ofNullable(errorWarnMsg.getReceiver()).map(Arrays::asList).ifPresent(receiverList::addAll);
        receiverList.addAll(Optional.ofNullable(developerEmailAddressCache.getAll()).orElse(Collections.emptyList()));

        record.uuid();

        // save into mongoDB
        Long epochDay = LocalDate.now().toEpochDay();
        String uuid = record.getUuid();
        Query query = Query.query(Criteria.where("uuid").is(uuid))
                .addCriteria(Criteria.where("hash").is(record.getHash()))
                .addCriteria(Criteria.where("title").is(record.getTitle()))
                .addCriteria(Criteria.where("epochDay").is(epochDay));
        // 不存在则初始化
        if (!mongoTemplate.exists(query, ErrorEmailRecord.class)) {
            mongoTemplate.upsert(query
                    , Update.update("content", record.getContent())
                            .set("errorDetail", record.getErrorDetail())
                            .set("receiver", receiverList)
                            .currentDate("lastCreated")
                    , ErrorEmailRecord.class);
        }
        return mongoTemplate.findAndModify(query, new Update().inc("occupyBetween", 1).inc("occupySum", 1), ErrorEmailRecord.class);
    }
}
