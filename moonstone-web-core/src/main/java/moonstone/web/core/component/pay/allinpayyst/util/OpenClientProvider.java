package moonstone.web.core.component.pay.allinpayyst.util;

import com.allinpay.sdk.bean.OpenConfig;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTToken;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class OpenClientProvider {

    @Value("${pay.allinpay.ystBaseUrl}")
    private String allInPayYSTBaseUrl;

    private final Map<String, AllInPayYSTOpenClient> clientMap = new ConcurrentHashMap<>();

    private final String BEAN_NAME = "enhancedOpenClient-%s";

    public AllInPayYSTOpenClient getOpenClient(String accountNo, AllInPayYSTToken token) {
        var bean = clientMap.get(BEAN_NAME.formatted(accountNo));
        if (match(bean, token)) {
            return bean;
        } else {
            return constructByToken(accountNo, token);
        }
    }

    private AllInPayYSTOpenClient constructByToken(String accountNo, AllInPayYSTToken token) {
        var target = new AllInPayYSTOpenClient(new OpenConfig(allInPayYSTBaseUrl, token.getAppId(), token.getSecretKey(),
                token.getCertPath(), token.getCertPassword(), token.getTlCertPath()), System.currentTimeMillis());

        clientMap.put(BEAN_NAME.formatted(accountNo), target);

        return target;
    }

    /**
     * 判断 bean 的配置是否与 token 相匹配。<br/>
     * 实际上支付配置这玩意配好了就不会轻易修改，因此判断逻辑并没有做得太严谨
     *
     * @param bean
     * @param token
     * @return
     */
    private boolean match(AllInPayYSTOpenClient bean, AllInPayYSTToken token) {
        if (bean == null) {
            return false;
        }

        var beanConfig = bean.getConfig();
        if (beanConfig == null || bean.getTokenLoadTime() == null) {
            return false;
        }

        return bean.getTokenLoadTime() >= token.getTokenLoadTime() && beanConfig.getAppId().equals(token.getAppId()) &&
                beanConfig.getSecretKey().equals(token.getSecretKey()) && beanConfig.getCertPwd().equals(token.getCertPassword());
    }
}
