package moonstone.web.core.component.weShop;

import io.vertx.core.AbstractVerticle;
import moonstone.cache.WeShopSkuCacheHolder;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.web.core.component.qrCode.WeShopItemShareImageCacheHolder;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.weShopItem.WeShopItemUpdatedEvent;
import moonstone.web.core.events.weShopItem.WeShopSkuUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

@Component
public class WeShopSkuCacheInvalidateGuard extends AbstractVerticle {
    @Autowired
    private WeShopSkuCacheHolder weShopSkuCacheHolder;
    @Autowired
    private WeShopSkuReadService weShopSkuReadService;
    @Autowired
    private WeShopItemReadService weShopItemReadService;
    @Autowired
    private WeShopItemShareImageCacheHolder weShopItemShareImageCacheHolder;


    @VertxEventBusListener(WeShopItemUpdatedEvent.class)
    public void invalidateSkuByWeShopItemUpdate(WeShopItemUpdatedEvent weShopItemUpdatedEvent) {
        WeShopItem weShopItem = weShopItemReadService.findById(weShopItemUpdatedEvent.getWeShopItemId()).getResult();
        if (weShopItem == null) {
            return;
        }
        weShopItemShareImageCacheHolder.invalidateForWeShopItemId(weShopItem.getId());
        for (WeShopSku weShopSku : weShopSkuReadService.findByWeShopIdAndItemId(weShopItem.getWeShopId(), weShopItem.getItemId()).orElseGet(ArrayList::new)) {
            weShopSkuCacheHolder.invalidate(weShopSku.getWeShopId(), weShopSku.getSkuId());
            weShopSkuCacheHolder.invalidate(weShopSku.getId());
        }
    }

    @VertxEventBusListener(WeShopSkuUpdateEvent.class)
    public void invalidate(WeShopSkuUpdateEvent weShopSkuUpdateEvent) {
        Optional.ofNullable(weShopSkuUpdateEvent.getWeShopSkuId()).ifPresent(weShopSkuCacheHolder::invalidate);
        if (Objects.nonNull(weShopSkuUpdateEvent.getWeShopSkuId())) {
            weShopSkuReadService.findById(weShopSkuUpdateEvent.getWeShopSkuId()).ifSuccess(weShopSku -> {
                        Long weShopItemId = weShopItemReadService.findByWeShopIdAndItemId(weShopSku.getWeShopId(), weShopSku.getItemId()).getResult().getId();
                weShopSkuCacheHolder.invalidate(weShopSku.getWeShopId(), weShopSku.getSkuId());
                weShopItemShareImageCacheHolder.invalidateForWeShopItemId(weShopItemId);
                    }
            );
        }
        if (Objects.nonNull(weShopSkuUpdateEvent.getWeShopId()) && Objects.nonNull(weShopSkuUpdateEvent.getItemId())) {
            weShopSkuCacheHolder.invalidate(weShopSkuUpdateEvent.getWeShopId(), weShopSkuUpdateEvent.getItemId());
            Optional.ofNullable(weShopItemReadService.findByWeShopIdAndItemId(weShopSkuUpdateEvent.getWeShopId(), weShopSkuUpdateEvent.getItemId()).getResult())
                    .map(WeShopItem::getId).ifPresent(weShopItemShareImageCacheHolder::invalidateForWeShopItemId);
            weShopSkuReadService.findByWeShopIdAndItemId(weShopSkuUpdateEvent.getWeShopId(), weShopSkuUpdateEvent.getItemId()).orElseGet(ArrayList::new)
                    .forEach(weShopSku -> {
                        weShopSkuCacheHolder.invalidate(weShopSku.getWeShopId(), weShopSku.getSkuId());
                        weShopSkuCacheHolder.invalidate(weShopSku.getId());
                    });
            weShopItemReadService.findListByWeShopIdAndItemId(weShopSkuUpdateEvent.getWeShopId(), weShopSkuUpdateEvent.getItemId()).getResult()
                    .stream().map(WeShopItem::getId)
                    .forEach(weShopItemShareImageCacheHolder::invalidateForWeShopItemId);
        }
    }
}
