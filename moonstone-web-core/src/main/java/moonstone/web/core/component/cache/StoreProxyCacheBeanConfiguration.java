package moonstone.web.core.component.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CacheReadBean;
import moonstone.common.model.ShopIdAndUserId;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.shop.StoreProxyUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class StoreProxyCacheBeanConfiguration extends AbstractVerticle {
    @Autowired
    private StoreProxyReadService storeProxyReadService;


    private LoadingCache<Long, StoreProxy> storeProxyLoadingCache;
    private LoadingCache<ShopIdAndUserId, StoreProxy> storeProxyCacheByUserIdAndShopId;

    @Bean
    CacheReadBean<Long, StoreProxy> getStoreProxyCacheById() {
        return this::getStoreProxyCache;
    }

    @Bean
    CacheReadBean<ShopIdAndUserId, StoreProxy> getStoreProxyCacheByUnionId() {
        return this::getStoreProxyCache;
    }

    @VertxEventBusListener(StoreProxyUpdateEvent.class)
    public void invalidate(StoreProxyUpdateEvent storeProxyUpdateEvent) {
        storeProxyLoadingCache.invalidate(storeProxyUpdateEvent.getId());
        vertx.executeBlocking(p -> {
            storeProxyReadService.findByShopIdAndUserId(storeProxyUpdateEvent.getShopId(), storeProxyUpdateEvent.getUserId())
                    .orElseGet(Optional::empty)
                    .ifPresent(storeProxy -> {
                        storeProxyLoadingCache.invalidate(storeProxy.getId());
                        storeProxyCacheByUserIdAndShopId.invalidate(new ShopIdAndUserId(storeProxy.getShopId(), storeProxy.getUserId()));
                    });
            p.complete(true);
        }, r -> {
        });
    }

    @PostConstruct
    public void init() {
        // 初始化缓存
        storeProxyCacheByUserIdAndShopId = Caffeine.newBuilder().expireAfterAccess(30, TimeUnit.MINUTES)
                .maximumSize(5000)
                .build(key -> {
                    StoreProxy storeProxy = storeProxyReadService.findByShopIdAndUserId(key.getShopId(), key.getUserId()).orElse(Optional.empty()).orElse(null);
                    if (storeProxy != null) {
                        storeProxyLoadingCache.put(storeProxy.getId(), storeProxy);
                    }
                    return storeProxy;
                });
        storeProxyLoadingCache = Caffeine.newBuilder().expireAfterAccess(30, TimeUnit.MINUTES)
                .maximumSize(5000)
                .build(key -> {
                    StoreProxy storeProxy = storeProxyReadService.findById(key).orElse(Optional.empty()).orElse(null);
                    if (storeProxy != null) {
                        storeProxyCacheByUserIdAndShopId.put(new ShopIdAndUserId(storeProxy.getUserId(), storeProxy.getShopId()), storeProxy);
                    }
                    return storeProxy;
                });
    }

    /**
     * 获取缓存
     *
     * @param shopIdAndUserId 聚合索引
     * @return 缓存的代理
     */
    public StoreProxy getStoreProxyCache(ShopIdAndUserId shopIdAndUserId) {
        return getStoreProxyCache(shopIdAndUserId.getUserId(), shopIdAndUserId.getShopId());
    }

    /**
     * 获取缓存
     *
     * @param userId 用户Id
     * @param shopId 平台Id
     * @return 缓存的数据
     */
    public StoreProxy getStoreProxyCache(Long userId, Long shopId) {
        return storeProxyCacheByUserIdAndShopId.get(new ShopIdAndUserId(userId, shopId));
    }

    /**
     * 获取缓存
     *
     * @param id 实体Id
     * @return 缓存的数据
     */
    public StoreProxy getStoreProxyCache(Long id) {
        return storeProxyLoadingCache.get(id);
    }
}
