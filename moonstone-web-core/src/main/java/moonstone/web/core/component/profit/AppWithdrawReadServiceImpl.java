package moonstone.web.core.component.profit;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.dto.WithDrawProfitApplyCriteria;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.web.core.component.profit.api.AppWithdrawReadService;
import moonstone.web.core.component.profit.view.AppWithdrawApplyView;
import moonstone.web.core.component.profit.view.AppWithdrawApplyViewImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Slf4j
@Component
public class AppWithdrawReadServiceImpl implements AppWithdrawReadService {
    @Autowired
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Override
    public Response<Paging<AppWithdrawApplyView>> paging(WithDrawProfitApplyCriteria criteria) {
        Response<Paging<WithDrawProfitApply>> withdrawApplyPagingRes =
                withDrawProfitApplyReadService.paging(criteria);
        if (!withdrawApplyPagingRes.isSuccess())
            return Response.fail(withdrawApplyPagingRes.getError());

        Paging<WithDrawProfitApply> paging = withdrawApplyPagingRes.getResult();
        return Response.ok(new Paging<>(paging.getTotal(), paging.getData().stream().map(AppWithdrawApplyViewImpl::from).collect(Collectors.toList())));
    }
}
