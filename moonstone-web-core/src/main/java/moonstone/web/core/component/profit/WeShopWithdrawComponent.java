package moonstone.web.core.component.profit;

import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.order.model.UserWithdrawSum;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.model.WithdrawPrinciple;
import moonstone.order.service.UserWithdrawSumManager;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.order.service.WithdrawAccountWriteService;
import moonstone.order.service.WithdrawPrincipleManager;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxReadService;
import moonstone.web.core.component.profit.app.WithdrawAllowRulerBean;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.model.dto.WithdrawLimitView;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@Slf4j
public class WeShopWithdrawComponent {
    @Autowired
    WithdrawPrincipleManager withdrawPrincipleManager;
    @Autowired
    UserWithdrawSumManager userWithdrawSumManager;
    @Autowired
    WithdrawAccountReadService withdrawAccountReadService;
    @Autowired
    WithdrawAccountWriteService withdrawAccountWriteService;
    @Autowired
    UserWxReadService userWxReadService;
    @Autowired
    ParanaConfig paranaConfig;

    /**
     * 查找一个可用或者新建一个提现帐号
     *
     * @param withdrawAccountId 提现帐号Id
     * @param userId            用户Id
     * @return 提现用帐号
     */
    public WithdrawAccount findWithdrawAccountOrWechatAccount(Long sourceId, Long withdrawAccountId, Long userId) {
        if (withdrawAccountId != null) {
            Optional<WithdrawAccount> accountOpt = withdrawAccountReadService.findById(withdrawAccountId).orElse(Optional.empty());
            if (!accountOpt.isPresent()) {
                throw new JsonResponseException(new Translate("请选择一个可用帐号进行提现").toString());
            }
            if (!Objects.equals(accountOpt.get().getUserId(), UserUtil.getUserId())) {
                throw new JsonResponseException(new Translate("目标提现帐号不属于你").toString());
            }
            return accountOpt.get();
        }
        Either<List<WithdrawAccount>> withdrawAccountResult = withdrawAccountReadService.findByShopIdAndUserId(sourceId, userId);
        if (!withdrawAccountResult.isSuccess()) {
            throw new JsonResponseException(new Translate("缺少提现帐号信息").toString());
        }
        Optional<WithdrawAccount> optAccount = withdrawAccountResult.take().stream().filter(a -> a.getType() == WithDrawProfitApply.WithdrawPaidType.WECHAT.getType()).findFirst();
        if (!optAccount.isPresent()) {
            String appId = paranaConfig.getParanaWxaAppId();
            WithdrawAccount account = new WithdrawAccount();
            account.setUserId(userId);
            account.setShopId(sourceId);
            account.setName(new Translate("微信钱包提现").toString());
            account.setFrom(new Translate("微信钱包").toString());
            account.setType(WithDrawProfitApply.WithdrawPaidType.WECHAT.getType());
            userWxReadService.findByAppIdAndUserId(appId, userId).orElse(Optional.empty())
                    .map(UserWx::getOpenId).ifPresent(account::setAccount);
            withdrawAccountWriteService.create(account);
            return account;
        } else {
            return optAccount.get();
        }
    }

    /**
     * 打包剩余可提现额度和次数
     *
     * @param userId 用户Id
     * @param shopId 店铺Id
     * @see WithdrawAllowRulerBean#allow(WithDrawProfitApply)
     */
    public WithdrawLimitView constructTimeLimitForProfitDTO(Long userId, Long shopId) {
        WithdrawPrinciple withdrawPrinciple = withdrawPrincipleManager.findByShopId(shopId).orElse(null);
        WithdrawLimitView view = new WithdrawLimitView();
        Map<String, String> cashWithdrawLeft = view.getCashWithdrawLeft();
        Map<String, String> timeWithdrawLeft = view.getTimeWithdrawLeft();

        if (withdrawPrinciple != null) {
            UserWithdrawSum withdrawSum = userWithdrawSumManager.findByUserIdAndShopId(userId, shopId).orElseGet(() -> {
                UserWithdrawSum userWithdrawSum = new UserWithdrawSum();
                userWithdrawSum.setUserId(userId);
                userWithdrawSum.setShopId(shopId);
                userWithdrawSum.setSum(new HashMap<>());
                userWithdrawSum.setTimes(new HashMap<>());
                return userWithdrawSum;
            });
            for (WithdrawPrinciple.IndexEnum index : WithdrawPrinciple.IndexEnum.values()) {
                BigDecimal moneyMax = withdrawPrinciple.getMoneyLimit().getOrDefault(index.getIndex(), BigDecimal.valueOf(Long.MAX_VALUE));
                Long countMax = withdrawPrinciple.getTimeLimit().getOrDefault(index.getIndex(), Long.MAX_VALUE);
                BigDecimal moneyUsed = withdrawSum.getSum().getOrDefault(index.getIndex(), BigDecimal.valueOf(0L));
                Long timeUsed = withdrawSum.getTimes().getOrDefault(index.getIndex(), 0L);
                switch (index) {
                    case DAY: {
                        if (withdrawSum.getLastWithdrawAt() == null || new DateTime(withdrawSum.getLastWithdrawAt()).getDayOfYear() != new DateTime(new Date()).getDayOfYear()) {
                            timeUsed = 0L;
                            moneyUsed = BigDecimal.ZERO;
                        }
                    }
                    case YEAR: {
                        if (withdrawSum.getLastWithdrawAt() == null || new DateTime(withdrawSum.getLastWithdrawAt()).getYear() != new DateTime(new Date()).getYear()) {
                            timeUsed = 0L;
                            moneyUsed = BigDecimal.ZERO;
                        }
                    }
                    case MONTH: {
                        if (withdrawSum.getLastWithdrawAt() == null || new DateTime(withdrawSum.getLastWithdrawAt()).getMonthOfYear() != new DateTime(new Date()).getMonthOfYear()) {
                            timeUsed = 0L;
                            moneyUsed = BigDecimal.ZERO;
                        }
                    }
                    default:
                }
                BigDecimal zeroOrBiggerDB = moneyMax.subtract(moneyUsed);
                if (zeroOrBiggerDB.compareTo(BigDecimal.ZERO) < 0)
                    zeroOrBiggerDB = BigDecimal.ZERO;
                long zeroOrBigger = countMax - timeUsed;
                zeroOrBigger = zeroOrBigger > 0 ? zeroOrBigger : 0;
                cashWithdrawLeft.put(index.getIndex(), moneyMax.equals(BigDecimal.valueOf(Long.MAX_VALUE)) ? "无限制" : zeroOrBiggerDB.toString());
                timeWithdrawLeft.put(index.getIndex(), countMax == Long.MAX_VALUE ? "无限制" : zeroOrBigger + "");
            }
        }
        return view;
    }
}
