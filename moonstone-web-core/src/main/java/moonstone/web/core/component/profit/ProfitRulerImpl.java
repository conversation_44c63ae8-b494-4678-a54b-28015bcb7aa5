package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.IsPersistAble;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.MatrixCalculator;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.LinkedProfitRateGainer;
import moonstone.order.dto.InComeDetail;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.component.profit.dto.StoreProxyWithProfitSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RpcProvider
@Service
public class ProfitRulerImpl implements ProfitRuler {
    // 交易依赖区
    @Autowired
    SkuReadService skuReadService;
    @Autowired
    SkuOrderReadService skuOrderReadService;

    // 佣金直接依赖区
    @Autowired
    IntermediateInfoReadService intermediateInfoReadService;
    @Autowired
    LinkedProfitRateGainer linkedProfitRateGainer;
    MatrixCalculator<BigDecimal> profitCalculator = new MatrixCalculator<>(BigDecimal::new);

    // 必定缓冲区
    /**
     * sku缓冲区,目前目的为获取ShopId,因为ShopId不可变,该缓存只允许用于不可变数据获取
     */
    LoadingCache<Long, Sku> skuCacheForShopId;
    /**
     * 店铺佣金缓冲区,设置为100ms用于处理店铺佣金,防止雪崩与滞后
     * <p>
     * #time 100
     */
    LoadingCache<Long, IntermediateInfo> shopIntermediateCache;
    /**
     * 利润规则缓存
     * <p>
     * #time 100
     */
    LoadingCache<Long, Map<String, String>> profitRulerLoadingCache;

    BigDecimal HUNDRER_BD = new BigDecimal("100");
    IntermediateInfo defaultZeroRateSetForShop;

    @PostConstruct
    public void afterConstruct() {
        defaultZeroRateSetForShop = new IntermediateInfo();
        defaultZeroRateSetForShop.setFirstFee(0L);
        defaultZeroRateSetForShop.setFirstRate(0L);
        defaultZeroRateSetForShop.setSecondFee(0L);
        defaultZeroRateSetForShop.setSecondRate(0L);
        skuCacheForShopId = Caffeine.newBuilder().expireAfterWrite(15, TimeUnit.MINUTES).maximumSize(5000L)
                .build(key -> skuReadService.findSkuById(key).getResult());
        shopIntermediateCache = Caffeine.newBuilder().expireAfterWrite(100, TimeUnit.MILLISECONDS).maximumSize(5000L)
                .build(key -> intermediateInfoReadService.findByThirdAndType(key, ThirdIntermediateType.SHOP.value())
                        .map(Collection::stream)
                        .map(Stream::findFirst)
                        .orElse(Optional.empty())
                        .orElse(defaultZeroRateSetForShop));
        profitRulerLoadingCache = Caffeine.newBuilder().expireAfterWrite(100, TimeUnit.MILLISECONDS).maximumSize(5000L)
                .build(key -> new HashMap<>());
    }

    //todo: 启用且载入该模块
    @Override
    public List<Long> getProfitBenefitUserId(ShopOrder shopOrder) {
        return null;
    }

    /**
     * 根据订单生成由代理获取佣金的函数
     * 该函数负责组合收益单
     *
     * @param shopOrder 订单
     * @return 由代理生成佣金
     */
    @Override
    public Function<StoreProxyWithProfitSource, Optional<InComeDetail>> getProfitGenerator(ShopOrder shopOrder) {
        log.debug("{} init profit gainer for ShopOrderId:{}", LogUtil.getClassMethodName(), shopOrder.getId());
        return (proxy) -> {
            InComeDetail inComeDetail = new InComeDetail();
            inComeDetail.setUserId(proxy.getUserId());
            inComeDetail.setSourceId(proxy.getShopId());
            inComeDetail.setType(ProfitType.InCome.getValue());
            inComeDetail.setStatus(1 | OrderRelated.orderRelatedMask.ShopOrder.getValue()
                    | BalanceDetail.maskBit.OrderRelated.getValue()
                    | IsPersistAble.maskBit.PersistAble.getValue()
                    // 设置状态
                    | proxy.getBalanceSource().getBitMark()
            );
            // 设置其佣金来源
            inComeDetail.setFromSource(proxy.getBalanceSource());
            inComeDetail.setRelatedId(shopOrder.getId());
            // 计算且设置佣金
            switch (proxy.getBalanceSource()) {
                case Linked: {
                    boolean withExtraProfit = false;
                    inComeDetail.setChangeFee(linkedProfitRateGainer.getLinkedFeeFromOrder(shopOrder, withExtraProfit));
                    break;
                }
                case Transmit:
                case Reach:
                    calculateProfit(shopOrder, proxy.getLevel(), proxy.getBalanceSource())
                            .ifPresent(inComeDetail::setChangeFee);
            }
            if (inComeDetail.getChangeFee() == null) {
                return Optional.empty();
            }
            log.info("{} inComeDetail:{} for proxy:{}", LogUtil.getClassMethodName("make-income-detail"), JSON.toJSON(inComeDetail), proxy.getUserName());
            return Optional.of(inComeDetail);
        };
    }


    /**
     * 模仿haskell的Id函数 返回自身
     */
    <T> T identity(T t) {
        return t;
    }

    /**
     * 为阶梯分销计算佣金逻辑
     * 首先获取利润比与固定利润,然后获取sku列表,根据每个sku的原价计算利润,然后与skuOrder中的数量相乘再总和即是真实利润
     * 默认利润比为0L
     * todo: 增加计算子,进行多段计算,计算子使用Factory生成,带有所有所需信息,且模板依赖
     *
     * @param shopOrder  主订单
     * @param sourceMark 利润获取来源
     */
    OptionalLong calculateProfit(ShopOrder shopOrder, Integer level, BalanceDetail.SourceMark sourceMark) {
        // 获取利润值
        List<SkuOrder> skuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            return OptionalLong.empty();
        }
        // 获取商品利润设置
        Map<Long, IntermediateInfo> skuIdMapRate = skuOrders.stream().map(SkuOrder::getSkuId)
                .map(this::getRateSupplierBySkuId)
                .map(Supplier::get)
                .collect(Collectors.toMap(IntermediateInfo::getThirdId, this::identity));
        long profit = 0L;
        try {
            Map<Long, Sku> skuCacheMap = new HashMap<>();
            for (SkuOrder skuOrder : skuOrders) {
                IntermediateInfo rateInfo = skuIdMapRate.get(skuOrder.getSkuId());
                rateInfo.fillNullWithZero();
                log.debug("{} skuOrder:{} skuId:{} rateInfo:{}", LogUtil.getClassMethodName("cal-profit-by-rate"), skuOrder, skuOrder.getSkuId(), rateInfo);
                Sku sku = skuCacheMap.getOrDefault(skuOrder.getSkuId(), skuReadService.findSkuById(skuOrder.getSkuId()).getResult());
                skuCacheMap.put(sku.getId(), sku);
                BigDecimal skuOrderProfit;
                Map<String, String> rulers = getProfitRawRuler(shopOrder.getShopId());

                // 如果存在身份佣金,则不走销售佣金 后续改为接口控制
                // todo: 修改切割为计算子
                if (rulers.getOrDefault("identityProfitFirst", "true").equals("true") && !ObjectUtils.isEmpty(rateInfo.getExtraFeeCalMatrix())) {
                    List<BigDecimal> itemPriceList = new ArrayList<>();
                    for (int i = 1; i < level; i++)
                        itemPriceList.add(null);
                    itemPriceList.add(BigDecimal.valueOf(sku.getPrice()).divide(HUNDRER_BD, RoundingMode.DOWN));
                    skuOrderProfit = ObjectUtils.isEmpty(rateInfo.getExtraFeeCalMatrix()) ? BigDecimal.ZERO :
                            profitCalculator.calWithMatrixByLine(rateInfo.getExtraFeeCalMatrix(), itemPriceList.toArray(new BigDecimal[]{}))
                                    .orElse(BigDecimal.ZERO)
                                    .multiply(HUNDRER_BD);
                } else {
                    // 判断是否为上级利润
                    boolean isSupperProxy = sourceMark.equals(BalanceDetail.SourceMark.Transmit);
                    BigDecimal stepProfit = new BigDecimal((isSupperProxy ? rateInfo.getSecondRate() : rateInfo.getFirstRate()) * sku.getPrice());
                    BigDecimal extraProfit = new BigDecimal((isSupperProxy ? rateInfo.getSecondFee() : rateInfo.getFirstFee()));
                    if (rateInfo.getVersion() == rateInfo.getNowVersion()) {
                        // 新版本为万分制  直接/10000即可
                        stepProfit = (stepProfit).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
                    } else {
                        // 老版本为百分制  直接/100即可
                        stepProfit = (stepProfit).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                    }
                    skuOrderProfit = stepProfit.add(extraProfit);
                }
                // 总和利润
                profit += skuOrderProfit
                        .multiply(new BigDecimal(skuOrder.getQuantity()))
                        .setScale(0, RoundingMode.HALF_UP)
                        .longValue();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} cal failed skuIds:{},{}", LogUtil.getClassMethodName(), Arrays.toString(skuOrders.stream().map(SkuOrder::getSkuId).toArray()), ex.getStackTrace()[0].getLineNumber());
            return OptionalLong.empty();
        }
        return OptionalLong.of(profit);
    }

    /**
     * 获取佣金规则
     */
    @Override
    public Map<String, String> getProfitRawRuler(Long shopId) {
        return profitRulerLoadingCache.get(shopId);
    }

    /**
     * 获取佣金设置生产器
     */
    @Override
    public Supplier<IntermediateInfo> getRateSupplierBySkuId(Long skuId) {
        try {
            Long shopId = Objects.requireNonNull(skuCacheForShopId.get(skuId)).getShopId();
            Map<String, String> rulers = getProfitRawRuler(shopId);
            Optional<IntermediateInfo> skuRateSet = intermediateInfoReadService.findByThirdAndType(skuId, ThirdIntermediateType.SKU.value())
                    .map(Collection::stream)
                    .orElse(Stream.empty())
                    .filter(info -> info.getIsCommission().equals(1))
                    .findFirst();
            if (rulers.getOrDefault("dependOnShopRate", "true").equals("true")) {
                return () -> skuRateSet.orElse(shopIntermediateCache.get(shopId));
            } else {
                return () -> skuRateSet.orElse(null);
            }
        } catch (Exception ex) {
            log.error("{} try to get By SkuId:{} ex:", LogUtil.getClassMethodName(), skuId, ex);
            return null;
        }
    }
}
