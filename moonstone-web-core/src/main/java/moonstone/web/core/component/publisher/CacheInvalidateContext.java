package moonstone.web.core.component.publisher;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * Author:cp
 * Created on 09/11/2016.
 */
@NoArgsConstructor
@Data
public class CacheInvalidateContext implements Serializable {

    @Serial
    private static final long serialVersionUID = 3286410406263231094L;

    private String key;

    private String value;

    public CacheInvalidateContext(String key, String value) {
        this.key = key;
        this.value = value;
    }
}
