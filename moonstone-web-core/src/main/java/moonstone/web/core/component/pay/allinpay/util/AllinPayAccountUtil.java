package moonstone.web.core.component.pay.allinpay.util;

import lombok.extern.slf4j.Slf4j;
import moonstone.user.entity.UserBindAllinPay;
import moonstone.web.core.component.pay.allinpay.user.res.AllinPayAccountInfoVO;

@Slf4j
public class AllinPayAccountUtil {


    public static AllinPayAccountInfoVO settingStepStatus(UserBindAllinPay dbEntity) {
        AllinPayAccountInfoVO allinPayQueryVO = new AllinPayAccountInfoVO();
        allinPayQueryVO.setSignContractStatus(dbEntity.getSignContractStatus());
        if(org.apache.commons.lang.StringUtils.isNotEmpty(dbEntity.getBindPhone())){
            allinPayQueryVO.setStepBindPhone(true);
        }else{
            allinPayQueryVO.setStepBindPhone(false);
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(dbEntity.getAuthUserName())){
            allinPayQueryVO.setStepBindAuth(true);
        }else{
            allinPayQueryVO.setStepBindAuth(false);
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(dbEntity.getBindCardNo())){
            allinPayQueryVO.setStepBindFirstCard(true);
        }else{
            allinPayQueryVO.setStepBindFirstCard(false);

        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(dbEntity.getAcct())){
            allinPayQueryVO.setStepBindOpenId(true);
        }else{
            allinPayQueryVO.setStepBindOpenId(false);
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(dbEntity.getCompanyInfo())){
            allinPayQueryVO.setStepBindCompanyInfo(true);
        }else{
            allinPayQueryVO.setStepBindCompanyInfo(false);
        }
        return allinPayQueryVO;
    }



    public static String maskIdCard(String idCard) {
        // 检查身份证号的长度
        if (idCard == null || idCard.length() != 18) {
            // 身份证号不符合预期的长度
            return "Invalid Id Card";
        }

        // 将身份证号的中间部分替换为 *
        String maskedIdCard = idCard.substring(0, 4) + "********" + idCard.substring(14);

        return maskedIdCard;
    }

    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() != 11) {
            log.error("手机号码不符合预期的长度 {}", phoneNumber);
            return phoneNumber;
        }

        // 取前3位和后4位
        String prefix = phoneNumber.substring(0, 3);
        String suffix = phoneNumber.substring(7);

        // 用 * 号替换中间4位
        String maskedNumber = prefix + "****" + suffix;

        return maskedNumber;
    }

    public static void main(String[] args) {
        // 示例身份证号
        String originalIdCard = "18805141108";

        // 脱敏处理
        String maskedIdCard = maskPhoneNumber(originalIdCard);

        // 输出结果
        System.out.println("Original ID Card: " + originalIdCard);
        System.out.println("Masked ID Card:   " + maskedIdCard);
    }
}
