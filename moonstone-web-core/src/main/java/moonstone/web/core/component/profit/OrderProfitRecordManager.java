package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.ShopIdAndUserId;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserProfileReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.service.WeShopReadService;
import moonstone.web.core.component.profit.dto.OrderProfitItemRecord;
import moonstone.web.core.component.profit.dto.OrderProfitRecord;
import moonstone.web.core.component.profit.dto.OrderProfitViewCriteria;
import moonstone.web.core.util.MongoUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderProfitRecordManager {
    @Autowired
    BalanceDetailReadService balanceDetailReadService;
    @Autowired
    SkuOrderReadService skuOrderReadService;
    @Autowired
    WeShopReadService weShopReadService;
    @Autowired
    StoreProxyReadService storeProxyReadService;
    @Autowired
    SubStoreReadService subStoreReadService;
    @Autowired
    UserProfileReadService userProfileReadService;

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    SkuCacheHolder skuCacheHolder;


    /**
     * 分页缓存开始
     */
    @Value("${mongodb.page.cache:20}")
    private int PAGE_CACHE;
    /**
     * 用于分页时的Id index查询, 不超时 无失活
     */
    private final LoadingCache<ShopIdAndUserId, Cache<String, String>> pageByIndexCacheForUser = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build(shopIdAndUserId -> Caffeine.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).build());


    /**
     * 为订单创建利润记录
     * 利润必须在此函数前调用
     *
     * @param shopOrder 订单
     * @return 利润记录
     */
    public List<OrderProfitRecord> createProfitRecord(ShopOrder shopOrder) {
        return findOrderProfit(shopOrder, true).stream().filter(this::notExists)
                .peek(this::save).collect(Collectors.toList());
    }

    private boolean notExists(OrderProfitRecord orderProfitRecord) {
        Query query = Query.query(Criteria.where("orderId").is(orderProfitRecord.getOrderId()))
                .addCriteria(Criteria.where("ownerId").is(orderProfitRecord.getOwnerId()))
                .addCriteria(Criteria.where("profitType").is(orderProfitRecord.getProfitType()));
        log.debug("{} check orderProfit exists[{}]", LogUtil.getClassMethodName(), query);
        if (mongoTemplate.exists(query, OrderProfitRecord.class)) {
            OrderProfitRecord mayMatch = mongoTemplate.findOne(query, OrderProfitRecord.class);
            query.addCriteria(Criteria.where("profit").is(orderProfitRecord.getProfit()));
            OrderProfitRecord matched = mongoTemplate.findOne(query, OrderProfitRecord.class);
            if (matched != null) {
                log.warn("{} find exists [{}] match [{}]", LogUtil.getClassMethodName(), JSON.toJSON(matched), JSON.toJSONString(orderProfitRecord));
                return false;
            } else {
                log.warn("{} find one[{}] match but profit-fee is not matched", LogUtil.getClassMethodName(), JSON.toJSONString(mayMatch));
            }
            return true;
        }
        return true;
    }

    private OrderProfitRecord generateBaseRecord(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        OrderProfitRecord record = new OrderProfitRecord();
        record.setOrderName(packOrderName(skuOrderList));
        record.setOrderId(shopOrder.getId());
        record.setOrderAt(shopOrder.getCreatedAt());
        record.setOrderProfitRecordList(drainItemListFromSkuOrderList(skuOrderList));
        record.setOrderSellPrice(shopOrder.getFee());
        record.setOutFrom(shopOrder.getOutFrom());
        record.setBuyerId(shopOrder.getBuyerId());
        record.setBuyerName(userProfileReadService.findProfileByUserId(shopOrder.getBuyerId()).getResult().getRealName());
        record.setShopId(shopOrder.getShopId());
        return record;
    }

    /**
     * @param skuOrderList 单品订单列表
     * @return 抽取出只需要的那部分数据
     */
    private List<OrderProfitItemRecord> drainItemListFromSkuOrderList(List<SkuOrder> skuOrderList) {
        ArrayList<OrderProfitItemRecord> recordList = new ArrayList<>();
        for (SkuOrder skuOrder : skuOrderList) {
            OrderProfitItemRecord record = new OrderProfitItemRecord();
            record.setImageUrl(skuOrder.getSkuImage_());
            record.setName(skuOrder.getItemName());
            record.setQuantity(skuOrder.getQuantity());
            // 获取的是原价 但是不包括优惠
            record.setPrice(new BigDecimal(skuOrder.getOriginFee()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
            recordList.add(record);
        }
        return recordList;
    }

    /**
     * 打包单品订单列表
     *
     * @param skuOrderList 单品订单列表
     * @return 名称
     */
    private String packOrderName(List<SkuOrder> skuOrderList) {
        StringBuilder builder = new StringBuilder();
        for (SkuOrder skuOrder : skuOrderList) {
            builder.append(skuCacheHolder.findSkuById(skuOrder.getSkuId()).getName());
            builder.append("x");
            builder.append(skuOrder.getQuantity());
            builder.append(",");
        }
        builder.deleteCharAt(builder.length() - 1);
        return builder.toString();
    }

    /**
     * 查找订单的利润
     *
     * @param shopOrder 订单
     * @return 订单利润
     */
    public List<OrderProfitRecord> findOrderProfit(ShopOrder shopOrder) {
        return findOrderProfit(shopOrder, false);
    }

    /**
     * 查找订单利润
     *
     * @param shopOrder 订单
     * @param rebuild   是否无视已有信息从原订单重构
     * @return 订单利润
     */
    protected List<OrderProfitRecord> findOrderProfit(ShopOrder shopOrder, boolean rebuild) {
        if (!rebuild) {
            Query query = Query.query(Criteria.where("orderId").is(shopOrder.getId())).addCriteria(Criteria.where("shopId").is(shopOrder.getShopId()));
            if (mongoTemplate.exists(query, OrderProfitRecord.class)) {
                return mongoTemplate.find(query, OrderProfitRecord.class);
            }
        }

        // 拉取订单列表
        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
        // 创建基础记录
        OrderProfitRecord record = generateBaseRecord(shopOrder, skuOrderList);

        // 设置订单来源
        setOrderFrom(shopOrder, record);


        Map<String, OrderProfitRecord> orderProfitRecordMap = new HashMap<>();

        // 获取利润信息
        for (BalanceDetail balanceDetail : findIncomeDetail(shopOrder)) {
            OrderProfitRecord profitRecord = Optional.ofNullable(orderProfitRecordMap.get(getIncomeUUID(balanceDetail)))
                    .orElseGet(() -> {
                        OrderProfitRecord instance = new OrderProfitRecord();
                        BeanUtils.copyProperties(record, instance);
                        return instance;
                    });
            orderProfitRecordMap.putIfAbsent(getIncomeUUID(balanceDetail), profitRecord);


            profitRecord.setProfit(balanceDetail.getChangeFee());
            profitRecord.setProfitType(balanceDetail.getStatus() & BalanceDetail.SourceMark.BIT_MASK.getBitMark());
            profitRecord.setProfitStatus(profitRecord.isPresent() ? 2 : 1);
            profitRecord.setOwnerId(balanceDetail.getUserId());
            if (balanceDetail.isPresent()) {
                profitRecord.setEarnedProfitId(balanceDetail.getId());
                profitRecord.setEarnedAt(balanceDetail.getCreatedAt());
            } else {
                profitRecord.setForeseeProfitId(balanceDetail.getId());
            }
        }

        return new ArrayList<>(orderProfitRecordMap.values());
    }

    private List<BalanceDetail> findIncomeDetail(ShopOrder shopOrder) {
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setRelatedId(shopOrder.getId());
        OrderOutFrom orderOutFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
        criteria.setSourceId(shopOrder.getShopId());
        criteria.setStatusBitMarks(Arrays.asList(IsPersistAble.maskBit.PersistAble.getValue(), BalanceDetail.orderRelatedMask.ShopOrder.getValue(), BalanceDetail.maskBit.OrderRelated.getValue()));
        criteria.setType(1);
        criteria.setPageSize(Integer.MAX_VALUE);
        return balanceDetailReadService.paging(criteria).getResult().getData();
    }

    /**
     * 获取可以唯一制定利润类型的编号
     *
     * @param balanceDetail 利润
     * @return 编号
     */
    private String getIncomeUUID(BalanceDetail balanceDetail) {
        return String.format("(%s)[%s]%s_%sO%s", balanceDetail.getSourceId(), balanceDetail.getRelatedId(), balanceDetail.getUserId()
                , balanceDetail.getType(), balanceDetail.getStatus() & BalanceDetail.SourceMark.BIT_MASK.getBitMark());
    }

    /**
     * 获取订单来源
     * 列如weShop的名字或者代理员的名字 门店目前先不接入?
     *
     * @param shopOrder 订单信息
     */
    private void setOrderFrom(ShopOrder shopOrder, OrderProfitRecord record) {
        OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
        switch (outFrom) {
            case LEVEL_Distribution:
                Optional<StoreProxy> storeProxy = storeProxyReadService.findByShopIdAndUserId(shopOrder.getShopId(), shopOrder.getReferenceId()).orElse(Optional.empty());
                record.setOrderFrom(storeProxy.map(StoreProxy::getProxyShopName)
                        .orElse(String.format("二级代理[%s]", shopOrder.getReferenceId())));
                storeProxy.map(StoreProxy::getUserId).ifPresent(record::setReferenceId);
                storeProxy.map(StoreProxy::getShopId).ifPresent(record::setSourceId);
                return;
            case WE_SHOP:
                Either<WeShop> weShopResult = NumberUtil.parseNumber(shopOrder.getOutShopId(), Long.TYPE)
                        .map(weShopReadService::findById).map(Response::getResult);
                record.setOrderFrom(weShopResult.map(WeShop::getName)
                        .orElse(String.format("微店[%s]", shopOrder.getOutShopId())));
                weShopResult.map(WeShop::getUserId).ifSuccess(record::setReferenceId);
                record.setSourceId(shopOrder.getShopId());     //  默认是平台的下级
                return;
            case SUB_STORE:
                Optional<SubStore> subStore = Optional.ofNullable(subStoreReadService.findUserIdAndShopId(Long.parseLong(shopOrder.getOutShopId()), shopOrder.getShopId()).getResult());
                record.setOrderFrom(subStore.map(SubStore::getName)
                        .orElse(String.format("门店[%s]", shopOrder.getReferenceId())));
                subStore.map(SubStore::getUserId).ifPresent(record::setReferenceId);
                subStore.map(SubStore::getShopId).ifPresent(record::setSourceId);
                return;
        }
        if (outFrom != OrderOutFrom.UNKNOW) {
            record.setOutFrom(outFrom.Code());
        }
        log.debug("{} unknown order-from of order[{}] outFrom[{}] refererId[{}] outShopId[{}]"
                , LogUtil.getClassMethodName()
                , shopOrder.getId()
                , shopOrder.getOutFrom()
                , shopOrder.getReferenceId()
                , shopOrder.getOutShopId());
    }

    public List<OrderProfitRecord> refundOrder(Long orderId) {
        Query query = Query.query(Criteria.where("orderId").is(orderId));
        List<OrderProfitRecord> profitRecordList = mongoTemplate.find(query, OrderProfitRecord.class);
        mongoTemplate.updateMulti(query, Update.update("profitStatus", -1), OrderProfitRecord.class);
        return profitRecordList;
    }

    /**
     * 记录确定收益
     *
     * @param orderId           订单Id
     * @param balanceDetailList 确定时使用的利润,必须是present的 而且不能有多余数据不然则会出错
     * @return 收益记录列表
     */
    public List<OrderProfitRecord> ensureOrderProfit(Long orderId, List<BalanceDetail> balanceDetailList) {
        List<OrderProfitRecord> updated = new ArrayList<>();
        for (BalanceDetail detail : balanceDetailList) {
            Query query = Query.query(Criteria.where("orderId").is(orderId))
                    .addCriteria(Criteria.where("ownerId").is(detail.getUserId()))
                    .addCriteria(Criteria.where("profitType").is(detail.getStatus() & BalanceDetail.SourceMark.BIT_MASK.getBitMark()))
                    .addCriteria(Criteria.where("profit").is(detail.getChangeFee()));
            Update update = Update.update("earnedAt", detail.getCreatedAt())
                    .set("earnedProfitId", detail.getId())
                    .set("profitStatus", 2)
                    .currentDate("updatedAt");
            OrderProfitRecord find = mongoTemplate.findAndModify(query, update, OrderProfitRecord.class);
            if (find == null) {
                log.error("{} profit[{}] fail to update record for order[{}]", LogUtil.getClassMethodName(), detail.getId(), orderId);
                String msg = new Translate("来源店铺[%s] 订单[%s] 相关利润[%s] 更新记录失败,金额[%s] 状态[%s]", detail.getSourceId(), orderId, detail.getId(), detail.getChangeFee(), detail.getStatus()).toString();
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("ProfitRecord", msg, EmailReceiverGroup.DEVELOPER));
                continue;
            }
            updated.add(find);
            find.setEarnedAt(detail.getCreatedAt());
            find.setEarnedProfitId(detail.getId());
        }
        return updated;
    }

    /**
     * 保存数据
     * 不保证多线程
     *
     * @param orderProfitRecord 订单利润
     * @return 第一次插入获得的Id
     */
    private String save(OrderProfitRecord orderProfitRecord) {
        Update update = MongoUtil.generateUpdate(orderProfitRecord
                , new HashSet<>(Arrays.asList("_id", "orderId", "ownerId", "profitType", "updatedAt", "createdAt"))
                , true)
                .currentDate("createdAt");
        Query query = Query.query(Criteria.where("orderId").is(orderProfitRecord.getOrderId()))
                .addCriteria(Criteria.where("ownerId").is(orderProfitRecord.getOwnerId()))
                .addCriteria(Criteria.where("profitType").is(orderProfitRecord.getProfitType()));
        return Optional.ofNullable(mongoTemplate.upsert(query, update, OrderProfitRecord.class).getUpsertedId()).map(Object::toString).orElse(null);
    }

    /**
     * 查找用户的利润记录
     *
     * @param criteria 搜索条件
     * @return 列表
     */
    public Paging<OrderProfitRecord> findProfit(OrderProfitViewCriteria criteria) {
        final int page = criteria.getPageNo();
        final int pageSize = criteria.getPageSize();
        final long userId = Objects.requireNonNull(criteria.getUserId());
        final long sourceId = Objects.requireNonNull(criteria.getShopId());
        boolean settled = criteria.getSettled();
        log.debug("{} All => {}", LogUtil.getClassMethodName(), mongoTemplate.findAll(OrderProfitRecord.class));
        OrderOutFrom outFrom = criteria.getOutFrom();
        Supplier<Query> origin = () -> Query.query(Criteria.where("ownerId").is(userId))
                .addCriteria(Criteria.where("sourceId").is(sourceId))
                .addCriteria(Criteria.where("outFrom").is(outFrom.Code()))
                .addCriteria(Criteria.where("profitStatus").is(settled ? 2 : 1));
        Query query = origin.get().with(Sort.by(Sort.Direction.DESC, "createdAt")).limit(pageSize).skip(pageSize * (page - 1));
        // 缓存内的索引
        // only query 5
        // close the cache function
        /*
         Function<Integer, String> cacheIndexTaker = queryPage -> String.format(String.format("profitStatus[%s]-OF[%s]-page[%%s]", settled, outFrom), queryPage);
         if (page >= PAGE_CACHE) {
         Cache<String, String> cache = Objects.requireNonNull(pageByIndexCacheForUser.get(new ShopIdAndUserId(sourceId, userId)));
         String afterId = cache.getIfPresent(cacheIndexTaker.apply(page));
         int depth = 0;
         while (afterId == null && depth <= 5) {
         depth++;
         afterId = cache.getIfPresent(cacheIndexTaker.apply(page + depth));
         if (afterId == null) {
         afterId = cache.getIfPresent(cacheIndexTaker.apply(page - depth));
         }
         }
         if (afterId != null) {
         query.addCriteria(Criteria.where("_id").gt(new ObjectId(afterId))).skip(depth * pageSize);
         } else {
         query.skip(page * pageSize - pageSize);
         }
         }
         **/
        // query and pack
        final List<OrderProfitRecord> recordList = mongoTemplate.find(query, OrderProfitRecord.class);
        // mongodb cache template
        // cache invalidate and set
        /* close the cache
         if (!recordList.isEmpty()) {
         // decrease the code tab
         //noinspection LoopStatementThatDoesntLoop
         while (page == PAGE_CACHE - 1) {
         Cache<String, String> cache = Objects.requireNonNull(pageByIndexCacheForUser.get(new ShopIdAndUserId(sourceId, userId)));
         String afterId = cache.getIfPresent(cacheIndexTaker.apply(PAGE_CACHE));
         if (afterId == null) {
         break;
         }
         Query match = origin.get();
         OrderProfitRecord ifMatch = mongoTemplate.findOne(match.addCriteria(Criteria.where("_id")), OrderProfitRecord.class);
         if (ifMatch.get_id().equals(recordList.get(recordList.size() - 1).get_id())) {
         break;
         }
         cache.invalidateAll();
         break;
         }
         if (page >= PAGE_CACHE) {
         Objects.requireNonNull(pageByIndexCacheForUser.get(new ShopIdAndUserId(sourceId, userId))).put(cacheIndexTaker.apply(page), recordList.get(0).get_id());
         }
         }
         **/
        // Lazy work
        final Paging<OrderProfitRecord> block = Paging.empty();
        return new Paging<OrderProfitRecord>(null, recordList) {
            @Override
            public Long getTotal() {
                if (block.getTotal() == null) {
                    Long total = mongoTemplate.count(origin.get(), OrderProfitRecord.class);
                    block.setTotal(total);
                }
                return block.getTotal();
            }
        };
    }
}
