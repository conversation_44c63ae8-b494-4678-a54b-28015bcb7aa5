package moonstone.web.core.component;

import moonstone.common.api.remote.RemoteAPI;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import scala.Option;
import scala.Tuple2;

import java.lang.reflect.Method;

import static moonstone.web.core.component.api.Y800V3Api.Y800V3RpcCore;

@Configuration
public class Y800V3ApiProvider {
    public static void main(String[] args) {

    }

    @Bean
    public Y800V3RpcCore rpc(@Value("${y800.gateApi:http://outtest.order.yang800.cn/open/apiv3}") String gateApiUrl) {
        return new Y800V3RpcCore() {
            @Override
            public Object decodeAfterInvoke(Method method, Object result) {
                return Y800V3RpcCore.super.decodeAfterInvoke(method, result);
            }

            @Override
            public void setSecret(String secret) {
                Y800V3RpcCore.super.setSecret(secret);
            }

            @Override
            public void setAppId(String appId) {
                Y800V3RpcCore.super.setAppId(appId);
            }

            @Override
            public String apiUrl() {
                return gateApiUrl;
            }

            @Override
            public void close() {
                Y800V3RpcCore.super.close();
            }

            @Override
            public Object proceed(Method method, Object[] args) {
                return Y800V3RpcCore.super.proceed(method, args);
            }

            @Override
            public Tuple2<Method, Object[]> beforeInvoke(Method method, Object[] args) {
                return Y800V3RpcCore.super.beforeInvoke(method, args);
            }

            @Override
            public Object[] prepareArg(Object[] args) {
                return Y800V3RpcCore.super.prepareArg(args);
            }

            @Override
            public scala.collection.immutable.Map<String, Method> getMethodMap() {
                return Y800V3RpcCore.super.getMethodMap();
            }

            @Override
            public Option<RemoteAPI> getConfig() {
                return Y800V3RpcCore.super.getConfig();
            }
        };
    }
}
