package moonstone.web.core.component.pay.allinpayyst.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.order.model.AgentPayOrder;
import moonstone.order.model.AgentPayOrderDetail;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AgentPayOrderBO {

    private List<Long> shopOrderIds;

    private List<AgentPayOrder> agentPayOrders;

    /**
     * key = agentPayOrderId
     */
    private Map<Long, List<AgentPayOrderDetail>> agentPayOrderDetailMap;
}
