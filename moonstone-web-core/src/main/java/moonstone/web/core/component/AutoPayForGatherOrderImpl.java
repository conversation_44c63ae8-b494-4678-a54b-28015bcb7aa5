package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.GatherOrder;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.service.GatherOrderReadService;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.web.core.component.api.AutoPayForGatherOrder;
import moonstone.web.core.component.order.PaymentLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

@Component
@Slf4j
public class AutoPayForGatherOrderImpl implements AutoPayForGatherOrder {

    @Autowired
    private GatherOrderReadService gatherOrderReadService;
    @Autowired
    private ThirdPartyUserReadService thirdPartyUserReadService;
    @Autowired
    private PaymentLogic paymentLogic;

    @Override
    public Boolean payForGatherOrder(Long gatherOrderId) {
        Payment payment = paymentLogic.prePay("oms", null, Collections.singletonList(gatherOrderId),
                OrderLevel.GATHER, OrderEvent.PAY.toOrderOperation(), 0L);
        Long fee = payment.getFee();
        GatherOrder gatherOrder = gatherOrderReadService.findById(gatherOrderId).take();

        Optional<ThirdPartyUser> thirdPartyUserOpt = thirdPartyUserReadService.findByTypeAndThirdPartyId(ThirdPartyUserType.OMS.getType(), gatherOrder.getBuyerId().toString()).getResult();
        if (!thirdPartyUserOpt.isPresent()) {
            log.error("{} weShopUser[{}] not found", LogUtil.getClassMethodName(), gatherOrder.getBuyerId());
            return false;
        }
        ThirdPartyUser thirdPartyUser = thirdPartyUserOpt.get();
        String outSideUserId = thirdPartyUser.getThirdPartyId();

        return pay(outSideUserId, fee);
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void autoPay() {
        for (Long gatherOrderId : gatherOrderReadService.findStatusGroupByBuyerId(OrderStatus.NOT_PAID.getValue()).take()) {
            payForGatherOrder(gatherOrderId);
        }
    }
}
