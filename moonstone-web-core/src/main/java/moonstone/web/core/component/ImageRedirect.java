package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;


/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/image")
@Slf4j
public class ImageRedirect {

    @RequestMapping("/redirect")
    public void redirect(HttpServletResponse response, String url) throws IOException {
        response.addHeader("Cache-Control", "no-cache");
        response.addHeader("Cache-Control", "no-store");
        response.addHeader("Cache-Control", "must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", -1);
        response.setContentType("image/png");
        BufferedImage bufferedImage = ImageIO.read(new URL(url));
        ImageIO.write(bufferedImage, "png", response.getOutputStream());
    }

}