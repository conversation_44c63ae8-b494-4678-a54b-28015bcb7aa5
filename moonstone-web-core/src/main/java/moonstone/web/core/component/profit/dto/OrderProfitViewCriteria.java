package moonstone.web.core.component.profit.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.common.enums.OrderOutFrom;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderProfitViewCriteria {
    Long userId;
    Long shopId;
    Boolean settled;
    OrderOutFrom outFrom;
    Integer pageNo;
    Integer pageSize;

    public Boolean getSettled() {
        return !Objects.isNull(settled) && settled;
    }

    public Integer getPageNo() {
        return Objects.isNull(pageNo) ? 1 : pageNo;
    }

    public Integer getPageSize() {
        return Objects.isNull(pageSize) ? 50 : pageSize;
    }

    public void setOutFrom(String outFrom){
        this.outFrom = OrderOutFrom.fromCode(outFrom);
    }
}
