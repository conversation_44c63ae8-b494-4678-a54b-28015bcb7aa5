package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.IRecordInsert;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;
import moonstone.web.core.model.Record;
import moonstone.web.core.model.enu.RecordDateLimitType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Nullable;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 使用redis进行设置访问缓存,使用mongoTemplate记录每月的访问次数,都是不重要的数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecordManager {
    final String QUEUE_NAME = String.format("[queue](%s)", RecordManager.class.getSimpleName());
    final Function<String, String> getQueueDupKey = key -> String.format("queue@[Dup](%s)-%s", RecordManager.class.getSimpleName(), key);
    @Autowired
    private JedisPool jedisPool;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Value("${record.key_set.name:RecordKeySet}")
    private String recordKeySetNameInRedis;

    @PreDestroy
    @Scheduled(cron = "* 0/10 * * * ?")
    public void syncRedisAndMongodb() {
        saveIntoMongodb();
    }

    private String getSimpleName(Class<?> clazz) {
        Class<?> node = clazz;
        while (node.getSimpleName().isEmpty()) {
            node = node.getSuperclass();
        }
        return node.getSimpleName();
    }

    /**
     * save record from redis into mongodb
     */
    private void saveIntoMongodb() {
        Lock lock = redissonClient.getLock(RecordManager.class.getSimpleName() + "#lock");
        try {
            if (!lock.tryLock(1, TimeUnit.SECONDS)) {
                throw new RuntimeException("fail to gain the lock");
            }
        } catch (Exception e) {
            log.error("{} fail to gain the lock for redis save", LogUtil.getClassMethodName(), e);
            return;
        }
        try {
            saveIntoMongodb(false, false);
        } finally {
            lock.unlock();
        }
    }

    Pattern pattern = Pattern.compile("\\[([^]]*)]([^(]*)\\((.*)\\)");

    /**
     * scan all the record and save it into mongodb
     *
     * @param removeAfterUpdate need remove the record in redis
     * @param scanAll           skip queue to scan all the data
     */
    private void saveIntoMongodb(boolean removeAfterUpdate, boolean scanAll) {
        try (Jedis jedis = jedisPool.getResource()) {
            // if scan all, save all the record and delete the set and queue
            // if not scan all, just pop queue, delete the duplicate check lock
            Iterator<String> finder = scanAll ? jedis.smembers(recordKeySetNameInRedis).iterator()
                    : new Iterator<String>() {
                @Override
                public boolean hasNext() {
                    return jedis.llen(QUEUE_NAME) > 0;
                }

                @Override
                public String next() {
                    return jedis.lpop(QUEUE_NAME);
                }
            };
            // insert action
            finder.forEachRemaining(key -> {
                // key :: [className]prefix(userId)
                log.debug("{} try record the [{}]", LogUtil.getClassMethodName(), key);
                Matcher matcher = pattern.matcher(key);
                if (!matcher.find()) {
                    log.error("{} can't match the key[{}]", LogUtil.getClassMethodName(), key);
                    return;
                }
                // parse value
                String className = matcher.group(1);
                String innerKey = matcher.group(2);
                String userIdStr = matcher.group(3);
                Long userId = null;
                if (userIdStr != null && !"null".equals(userIdStr)) {
                    userId = NumberUtil.parseNumber(userIdStr, Long.TYPE).orElse(null);
                }
                Long num = NumberUtil.parseNumber(Objects.toString(jedis.get(key)), Long.TYPE).orElse(null);
                if (removeAfterUpdate) {
                    log.info("{} remove data[{}] from redis by [{}]", LogUtil.getClassMethodName(), num, key);
                    jedis.del(key);
                }
                if (Objects.equals(innerKey, "null")) {
                    innerKey = null;
                }

                // allow queue push
                String queueDulCheck = getQueueDupKey.apply(key);
                jedis.del(queueDulCheck);
                // update record
                log.debug("{} try to persist the record[{} => {}]", LogUtil.getClassMethodName(), key, num);
                updateRecord(className, innerKey, userId, num, 1);
            });
            // delete the set and queue for cache of record key
            if (scanAll) {
                jedis.del(recordKeySetNameInRedis);
                jedis.del(QUEUE_NAME);
            }
        }
    }

    @Scheduled(cron = "0 0 0 * * ?")
    public void batchUpdateRecord() {
        String action = UUID.randomUUID().toString();
        // 昨天
        long epochDay = LocalDate.now().toEpochDay() - 1;
        // check if batch up action
        log.info("{} [action => {}] try to batch update record for [day => {}]", LogUtil.getClassMethodName(), action, epochDay);
        try (Jedis jedis = jedisPool.getResource()) {
            String index = String.format("[RecordUpdateFor-%s]", epochDay);
            if (jedis.exists(index)) {
                log.info("{} batch up for [day => {}] has react by [action => {}], current [action => {}] ignore", LogUtil.getClassMethodName(), epochDay, jedis.get(index), action);
                return;
            }
            jedis.setnx(index, action);
            if (!jedis.get(index).equals(action)) {
                log.info("{} batch up for [day => {}] has react by [action => {}], current [action => {}] ignore", LogUtil.getClassMethodName(), epochDay, jedis.get(index), action);
                return;
            }
            // expire after a day, allow it die a second quicker
            jedis.expire(index, 24 * 60 * 60 - 1);
        }
        saveIntoMongodb(true, true);
        // use it as distribution lock
        log.info("{} [action => {}], actually batch up [day => {}]", LogUtil.getClassMethodName(), action, epochDay);
        for (Record record : mongoTemplate.find(Query.query(Criteria.where("epochDay").is(epochDay)).addCriteria(Criteria.where("type").is(1)), Record.class)) {
            updateRecord(record.getClassName(), record.getKey(), record.getUserId(), record.getNum(), 2);
            updateRecord(record.getClassName(), record.getKey(), record.getUserId(), record.getNum(), 3);
            updateRecord(record.getClassName(), record.getKey(), record.getUserId(), record.getNum(), 4);
            updateRecord(record.getClassName(), record.getKey(), record.getUserId(), record.getNum(), -1);
        }
    }

    /**
     * 对对应类型获取epochDay
     *
     * @param type 类型
     * @return 流失的天
     */
    private Long getEpochDay(Integer type) {
        long epochDay = LocalDate.now().toEpochDay();
        switch (type) {
            case -1:
                //  Long term
                return -1L;
            case 1:
                return epochDay;
            case 2:
                return epochDay - LocalDate.now().getDayOfWeek().getValue() + 1;

            case 3:
                return epochDay - LocalDate.now().getDayOfMonth() + 1;

            case 4:
                return epochDay - LocalDate.now().getDayOfYear() + 1;
            default:
                log.error("{} unknown type[{}]", LogUtil.getClassMethodName(), type);
                throw new IllegalArgumentException(new Translate("未知的[%s]类型", type).toString());
        }
    }

    /**
     * 更新记录
     * {@see Record#type} 小心type
     *
     * @param className 类名
     * @param innerKey  内部key
     * @param userId    用户Id
     * @param num       数量  如果type为1 则赋值
     * @param type      类型  1: day 2:week 3:month 4:year    影响num的赋值
     */
    private void updateRecord(String className, String innerKey, Long userId, Long num, Integer type) {
        if (num == null) {
            return;
        }
        // @arg epochDay  时间  day的日期,week的第一天,month的第一天,year的第一天
        long epochDay = getEpochDay(type);

        Query query = Query.query(Criteria.where("className").is(className))
                .addCriteria(Criteria.where("epochDay").is(epochDay))
                .addCriteria(Criteria.where("type").is(type));

        if (innerKey != null) {
            query.addCriteria(Criteria.where("key").is(innerKey));
        }
        if (userId != null) {
            query.addCriteria(Criteria.where("userId").is(userId));
        }

        // 如果type不为 1 则为增加
        Update update = type == 1 ? Update.update("num", num) : new Update().inc("num", num);

        if (!mongoTemplate.exists(query, Record.class)) {
            mongoTemplate.upsert(query, new Update().currentDate("createdAt"), Record.class);
        }
        if (mongoTemplate.updateFirst(query, update, Record.class).getModifiedCount() != 1) {
            log.error("{} query => {} update => {}, one => {}", LogUtil.getClassMethodName(),
                    query, update, mongoTemplate.find(query, Record.class));
            log.error("{} fail to update[{}] by query[{}]", LogUtil.getClassMethodName(), update, query);
        }
        // delete empty num
        query.addCriteria(Criteria.where("num").is(null));
        if (mongoTemplate.exists(query, Record.class)) {
            mongoTemplate.remove(query, Record.class);
        }
    }

    /**
     * 查找记录
     *
     * @param userId              用户Id
     * @param innerKey            数值
     * @param targetClass         类名
     * @param recordDateLimitType 类型
     * @return 记录
     */
    public List<Record> findRecord(@Nullable Long userId, @Nullable String innerKey, Class<?> targetClass, RecordDateLimitType recordDateLimitType) {
        if (recordDateLimitType == RecordDateLimitType.day) {
            return findRecord(userId, innerKey, targetClass, LocalDate.now().toEpochDay());
        }
        String className = getSimpleName(targetClass);
        Query query = Query.query(Criteria.where("className").is(className))
                .addCriteria(Criteria.where("type").is(recordDateLimitType.getType()))
                .addCriteria(Criteria.where("epochDay").is(getEpochDay(recordDateLimitType.getType())));

        if (userId != null) {
            query.addCriteria(Criteria.where("userId").is(userId));
        }
        if (innerKey != null) {
            query.addCriteria(Criteria.where("key").is(innerKey));
        }

        return mongoTemplate.find(query, Record.class);
    }

    public List<Record> findRecord(@Nullable Long userId, IRecordInsert iRecordInsert, RecordDateLimitType recordDateLimitType) {
        return findRecord(userId, iRecordInsert.key(), iRecordInsert.getClass(), recordDateLimitType);
    }

    public List<Record> findRecord(@Nullable Long userId, IRecordInsert iRecordInsert, Long epochDay) {
        return findRecord(userId, iRecordInsert.key(), iRecordInsert.getClass(), epochDay);
    }

    /**
     * 查找记录
     *
     * @param userId      用户Id
     * @param innerKey    数值
     * @param targetClass 类名
     * @param epochDay    过去的天数
     * @return 记录
     */
    public List<Record> findRecord(@Nullable Long userId, @Nullable String innerKey, Class<?> targetClass, Long epochDay) {
        String className = getSimpleName(targetClass);
        if (LocalDate.now().toEpochDay() == epochDay) {
            Record record = new Record();
            record.setEpochDay(LocalDate.now().toEpochDay());
            record.setKey(innerKey);
            record.setUserId(userId);
            record.setClassName(className);
            record.setType(RecordDateLimitType.day.getType());
            Optional<String> data;
            try (Jedis jedis = jedisPool.getResource()) {
                data = Optional.ofNullable(jedis.get(String.format("[%s]%s(%s)", className, innerKey, userId)));
            }
            data.map(Long::parseLong).ifPresent(record::setNum);
            return Collections.singletonList(record);
        }

        Query query = Query.query(Criteria.where("className").is(className))
                .addCriteria(Criteria.where("type").is(RecordDateLimitType.day.getType()))
                .addCriteria(Criteria.where("epochDay").is(epochDay));
        if (userId != null) {
            query.addCriteria(Criteria.where("userId").is(userId));
        }
        if (innerKey != null) {
            query.addCriteria(Criteria.where("key").is(innerKey));
        }

        return mongoTemplate.find(query, Record.class);
    }

    /**
     * 添加记录
     *
     * @param userId        用户Id
     * @param iRecordInsert 记录实体, 使用各种Builder吧
     * @return 今日是否有记录
     */
    public boolean increaseRecord(@Nullable Long userId, IRecordInsert iRecordInsert) {
        return increaseRecord(userId, iRecordInsert.key(), iRecordInsert.num(), iRecordInsert.getClass());
    }

    /**
     * 增加某个记录,比如访问 赞 分享次数 等不重要数据
     *
     * @param userId      用户Id
     * @param innerKey    数值  用于标识哪个商品什么的
     * @param num         修改的数值
     * @param targetClass 类名  用于表明类型
     * @return 之前是否已有记录
     */
    private boolean increaseRecord(@Nullable Long userId, @Nullable String innerKey, long num, Class<?> targetClass) {
        boolean haveAccess;

        String className = getSimpleName(targetClass);
        String redisIndex = String.format("[%s]%s(%s)", className, innerKey, userId);

        try (Jedis jedis = jedisPool.getResource()) {
            haveAccess = jedis.exists(redisIndex);
            jedis.setnx(redisIndex, "0");
            jedis.incrBy(redisIndex, num);
            long second = LocalDate.now().plusDays(1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() -
                    LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
            jedis.expire(redisIndex, (int) second + 1);
            jedis.sadd(recordKeySetNameInRedis, redisIndex);
            //  check the duplicate
            String queueDulCheck = getQueueDupKey.apply(redisIndex);
            if (!jedis.exists(queueDulCheck)) {
                jedis.set(queueDulCheck, "1");
                //  10 分钟内只允许推一次
                jedis.pexpire(queueDulCheck, 60 * 1000 * 10L);
                jedis.rpush(QUEUE_NAME, redisIndex);
            }
        }
        return haveAccess;
    }
}
