package moonstone.web.core.component.pay.bhecard.app;

import io.terminus.pay.component.MultiTokenProvider;
import io.terminus.pay.constants.Tokens;
import moonstone.web.core.component.pay.bhecard.domain.EasyPayToken;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Service
public class EasyPayTokenProvider extends MultiTokenProvider<EasyPayToken> {
    public static EasyPayToken DEFAULT_TOKEN = new EasyPayToken();

    @PostConstruct
    public void registerDefaultToken() {
        register(Tokens.DEFAULT_ACCOUNT, DEFAULT_TOKEN);
    }
}
