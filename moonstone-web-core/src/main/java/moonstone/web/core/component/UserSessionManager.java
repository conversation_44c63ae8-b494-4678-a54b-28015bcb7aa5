package moonstone.web.core.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.web.core.AppConstants;
import moonstone.web.core.events.shop.ShopUpdateEvent;
import moonstone.web.core.session.SessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpSession;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * # 用户Session管理器
 * <p>
 * - 登录用户记录
 * - 登录用户踢出
 *
 * @apiNote 是受陷于目前项目遗留错误所制作的用户管理器
 */
@Slf4j
@Component
public class UserSessionManager {

    private LoadingCache<Long, JSONObject> userIdAndShopIdCache;
    private static final int cacheSizeLimit = 500;

    @Value("${session.redis-prefix:afsession}")
    private String sessionPrefix;

    @Autowired
    private JedisPool jedisPool;
    @Autowired
    private ShopReadService shopReadService;
    @Autowired
    private SessionManager sessionManager;


    @PostConstruct
    void afterInit() {
        userIdAndShopIdCache = Caffeine.newBuilder().maximumSize(500).expireAfterAccess(15, TimeUnit.MINUTES)
                .build(this::load);
    }

    public JSONObject load(@SuppressWarnings("NullableProblems") Long userId) {
        try (Jedis jedis = jedisPool.getResource()) {
            //noinspection OptionalGetWithoutIsPresent 故意抛出
            return toUserEntity(jedis.get(jedis.get(AppConstants.SESSION_USER_REG_PREFIX + userId))).get();
        } catch (Exception ex) {
            log.error("{} load User(id:{}) From Jedis failed:", LogUtil.getClassMethodName(), userId, ex);
            throw ex;
        }
    }

    /**
     * 剔除用户登录凭据
     *
     * @param userId 用户Id
     * @return 是否有用户被剔除
     */
    public Boolean kickTheUserByUserId(Long userId) {
        if (userId == null) return false;
        EventSender.publish(new UserUpdateEvent(userId));
        Optional.ofNullable(shopReadService.findByUserId(userId).getResult())
                .map(Shop::getId)
                .ifPresent(shopId -> EventSender.publish(new ShopUpdateEvent(shopId)));
        userIdAndShopIdCache.invalidate(userId);
        try (Jedis jedis = jedisPool.getResource()) {
            String sessionId = getUserSessionIdRaw(jedis, userId);
            jedis.del(AppConstants.SESSION_USER_REG_PREFIX + userId);
            if (ObjectUtils.isEmpty(sessionId)) {
                return false;
            }
            sessionManager.getSession(sessionId).removeAttribute(AppConstants.SESSION_USER_ID);
            return true;
        } catch (Exception ex) {
            log.error("{} userId:{} ex:", LogUtil.getClassMethodName(), userId, ex);
            return false;
        }
    }

    /**
     * 转换为实体
     *
     * @param entityRaw 原数据
     * @return 实体
     */
    private Optional<JSONObject> toUserEntity(String entityRaw) {
        try {
            return Optional.of(JSON.parseObject(entityRaw, JSONObject.class));
        } catch (Exception ex) {
            log.error("{} entityRaw:{} ex:", LogUtil.getClassMethodName(), entityRaw, ex);
            return Optional.empty();
        }
    }

    /**
     * 登录,实际上由C端控制
     *
     * @param session 目前登录用户的Session
     * @return 是否有用户数据<成功登录>
     */
    public Optional<Boolean> login(HttpSession session) {
        if (session == null)
            return Optional.empty();
        try (Jedis jedis = jedisPool.getResource()) {
            Optional<String> userIdOpt = Optional.ofNullable(session.getAttribute(AppConstants.SESSION_USER_ID))
                    .map(Objects::toString)
                    .map(AppConstants.SESSION_USER_REG_PREFIX::concat);
            userIdOpt.ifPresent(sessionIdIndex -> jedis.set(sessionIdIndex, session.getId()));
            return userIdOpt.map(obj -> true);
        } catch (Exception ex) {
            log.error("{} login by sessionId:{} ex:", LogUtil.getClassMethodName(), session.getId(), ex);
            return Optional.empty();
        }
    }

    /**
     * 最后一层抽象
     *
     * @param jedis  Jedis
     * @param userId 用户Id
     * @return 抽你一巴掌
     */
    private String getUserSessionIdRaw(Jedis jedis, Long userId) {
        return jedis.get(AppConstants.SESSION_USER_REG_PREFIX + userId);
    }

    /**
     * 查找用户Session
     *
     * @param userId 用户Id
     * @return 是否存在用户Session
     */
    public Optional<String> getUserSessionId(Long userId) {
        try (Jedis jedis = jedisPool.getResource()) {
            return Optional.ofNullable(getUserSessionIdRaw(jedis, userId));
        } catch (Exception ex) {
            log.error("{} userId:{} ex:", LogUtil.getClassMethodName(), userId, ex);
            return Optional.empty();
        }
    }
}
