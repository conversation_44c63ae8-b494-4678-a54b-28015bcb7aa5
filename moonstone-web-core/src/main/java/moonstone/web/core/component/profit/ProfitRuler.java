package moonstone.web.core.component.profit;

import moonstone.item.model.IntermediateInfo;
import moonstone.order.dto.InComeDetail;
import moonstone.order.model.ShopOrder;
import moonstone.web.core.component.profit.dto.StoreProxyWithProfitSource;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 佣金规则
 */
public interface ProfitRuler {
    /**
     * 拉取佣金受益人
     *
     * @param shopOrder
     * @return
     */
    List<Long> getProfitBenefitUserId(ShopOrder shopOrder);

    /**
     * 生成对应的佣金计算器
     *
     * @param shopOrder
     * @return
     */
    Function<StoreProxyWithProfitSource, Optional<InComeDetail>> getProfitGenerator(ShopOrder shopOrder);

    /**
     * 获取对应的佣金规则
     */
    Map<String, String> getProfitRawRuler(Long shopId);

    /**
     * 获取佣金设置
     *
     * @param skuId
     * @return
     */
    Supplier<IntermediateInfo> getRateSupplierBySkuId(Long skuId);
}
