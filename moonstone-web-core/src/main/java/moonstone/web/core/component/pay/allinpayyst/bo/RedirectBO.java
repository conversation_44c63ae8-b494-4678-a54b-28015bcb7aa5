package moonstone.web.core.component.pay.allinpayyst.bo;

import io.terminus.pay.model.Redirect;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Setter
@Getter
public class RedirectBO extends Redirect {

    @Serial
    private static final long serialVersionUID = -8775071384315659777L;

    private Boolean invokeH5;

    private Boolean invokeMiniProgram;

    public RedirectBO(String channel, Boolean redirectNow, String redirectUrl, String requestOriginalUrl, Boolean invokeH5,
                      Boolean invokeMiniProgram) {
        super(channel, redirectNow, redirectUrl, requestOriginalUrl);
        this.invokeH5 = invokeH5;
        this.invokeMiniProgram = invokeMiniProgram;
    }

    public RedirectBO(String channel, Boolean redirectNow, String redirectUrl, Object redirectInfo, String requestOriginalUrl,
                      Boolean invokeH5, Boolean invokeMiniProgram) {
        super(channel, redirectNow, redirectUrl, redirectInfo, requestOriginalUrl);
        this.invokeH5 = invokeH5;
        this.invokeMiniProgram = invokeMiniProgram;
    }
}
