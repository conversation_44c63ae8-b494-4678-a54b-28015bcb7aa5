package moonstone.web.core.component.user;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityWriteService;
import moonstone.web.core.component.cache.UserRelationCacher;
import moonstone.web.core.model.UserRegisterForShopEvent;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;


@Slf4j
@Component
public class ShopMemberRegisterListener {
    @Resource
    private UserRelationEntityWriteService userRelationEntityWriteService;
    @Autowired
    private UserRelationCacher userRelationCacher;

    @Resource
    private RedissonClient redissonClient;

    @EventListener(UserRegisterForShopEvent.class)
    private void registerMember(UserRegisterForShopEvent event) throws InterruptedException {
        log.info("registerMember {}", JSON.toJSONString(event));
        Long nowUserId = event.getUserId();
        Long shopId = event.getShopId();
        //如果用户已经注册则直接取消注册
        if (userRelationCacher.get(nowUserId, shopId, UserRelationEntity.UserRelationType.Member).isPresent()) {
            return;
        }
        //如果用户不存在则注册一个
        Lock lock = redissonClient.getLock(String.format("ShopMember[%s=>%s]", shopId, nowUserId));
        if (!lock.tryLock(3, TimeUnit.SECONDS)) {
            return;
        }
        try {
            // 进入临界点后, 二重检测 防止回忆重放
            userRelationCacher.invalidate(nowUserId, shopId, UserRelationEntity.UserRelationType.Member);
            //二重检测, 防止回忆重放
            if (userRelationCacher.get(nowUserId, shopId, UserRelationEntity.UserRelationType.Member).isPresent()) {
                return;
            }
            UserRelationEntity shopMember = new UserRelationEntity();
            shopMember.setUserId(nowUserId);
            shopMember.setRelationId(shopId);
            shopMember.setType(UserRelationEntity.UserRelationType.Member.getType());
            Response<Long> rCreate = userRelationEntityWriteService.create(shopMember);
            if (!rCreate.isSuccess()) {
                log.error("{} nowUserId:{} relationEntity:{}", LogUtil.getClassMethodName("create-relation"), nowUserId, shopMember);
            }
        } finally {
            lock.unlock();
            userRelationCacher.invalidate(nowUserId, shopId, UserRelationEntity.UserRelationType.Member);
        }
    }
}