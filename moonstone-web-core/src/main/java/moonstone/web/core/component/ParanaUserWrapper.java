package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.CommonUser;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.web.core.util.ParanaUserMaker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

/**
 * 包装用户信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ParanaUserWrapper {
    @Autowired
    private ShopCacheHolder shopCacheHolder;
    @Autowired
    private UserCacheHolder userCacheHolder;
    @Autowired
    private StoreProxyRegisterComponent storeProxyRegisterComponent;
    @Autowired
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @Autowired
    private UserTypeBean userTypeBean;

    /**
     * 封装打包用户信息
     * 将用户携带的店铺信息也带上, shopId storeProxyId
     *
     * @param userId 用户Id
     * @return 返回用户信息数据
     */
    public CommonUser wrap(Long userId, Long shopId) {
        if (userId == null) {
            return null;
        }
        User user = userCacheHolder.findByUserId(userId).orElse(null);
        if (Objects.isNull(user)) {
            return null;
        }
        CommonUser commonUser = ParanaUserMaker.from(user);
        if (userTypeBean.isSeller(user)) {
            commonUser.setShopId(shopCacheHolder.findShopByUserId(user.getId()).getId());
        }
        if (userTypeBean.isWeDistributor(user)) {
            Optional.ofNullable(weShopShopAccountReadService.findByUserId(user.getId()).getResult())
                    .orElseGet(ArrayList::new).stream()
                    .filter(account -> Objects.equals(account.getShopId(), Optional.ofNullable(shopId).orElse(0L)))
                    .findFirst().map(WeShopShopAccount::getWeShopId)
                    .ifPresent(commonUser::setWeShopId);
        }
        if (Objects.isNull(commonUser.getShopId()) && Objects.nonNull(shopId)) {
            storeProxyRegisterComponent.getStoreProxyByShopIdAndUserId(shopId, userId)
                    .map(StoreProxy::getShopId)
                    .ifPresent(commonUser::setShopId);
        }
        return commonUser;
    }
}
