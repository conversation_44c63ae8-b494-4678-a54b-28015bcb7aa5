package moonstone.web.core.component.distribution;

import com.google.common.base.Function;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.weShop.dto.WeWithdrawDetailForSeller;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeWithdrawDetail;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeWithdrawDetailReadService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  <PERSON>ai<PERSON>hy
 * Date:    2019/1/10
 */
@Slf4j
@Component
public class WeWithdrawDetailReadLogic {
    @RpcConsumer
    private WeWithdrawDetailReadService weWithdrawDetailReadService;
    @RpcConsumer
    private WeShopReadService weShopReadService;
    @RpcConsumer
    private UserReadService<User> userReadService;

    public Response<Paging<WeWithdrawDetailForSeller>> pagingForSeller(Long shopId,
                                                                       String applicantName,
                                                                       String mobile,
                                                                       Integer status,
                                                                       String statuses,
                                                                       Integer pageNo,
                                                                       Integer pageSize) {
        try {
            //处理手机号模糊查找
            List<Long> userIds = null;
            if (!ObjectUtils.isEmpty(mobile)) {
                Response<List<User>> rUserList = userReadService.findLikeMobile(mobile);
                if (!rUserList.isSuccess()) {
                    log.error("failed to find users mobile like {}, error code: {}", mobile, rUserList.getError());
                    throw new JsonResponseException(rUserList.getError());
                }
                List<User> userList = rUserList.getResult();
                if (!CollectionUtils.isEmpty(userList)) {
                    userIds = Lists.transform(userList, new Function<User, Long>() {
                        @Override
                        public Long apply(User user) {
                            return user.getId();
                        }
                    });
                }
            }
            //实际分页查找
            Response<Paging<WeWithdrawDetail>> rWeWithdrawDetailPaging = weWithdrawDetailReadService.paging(null, userIds, null, shopId,
                    null, applicantName, status, statuses, null, null, null, null, pageNo, pageSize);
            if (!rWeWithdrawDetailPaging.isSuccess()) {
                log.error("failed to paging weWithdraw details by userIds={}, shopId={}, applicantName={}, " +
                        "status={}, statuses={}, pageNo={}, pageSize={}, error code: {}",
                        userIds, shopId, applicantName, status, statuses, pageNo, pageSize, rWeWithdrawDetailPaging.getError());
                throw new JsonResponseException(rWeWithdrawDetailPaging.getError());
            }
            Paging<WeWithdrawDetail> weWithdrawDetailPaging = rWeWithdrawDetailPaging.getResult();
            //组装数据
            List<WeWithdrawDetailForSeller> data = new ArrayList<>();
            for (WeWithdrawDetail weWithdrawDetail : weWithdrawDetailPaging.getData()) {
                WeWithdrawDetailForSeller weWithdrawDetailForSeller = new WeWithdrawDetailForSeller();
                weWithdrawDetailForSeller.setId(weWithdrawDetail.getId());
                Response<WeShop> rWeShop = weShopReadService.findById(weWithdrawDetail.getWeShopId());
                if (!rWeShop.isSuccess()) {
                    log.error("failed to find weShop by id={}, error code: {}", weWithdrawDetail.getWeShopId(), rWeShop.getError());
                    throw new JsonResponseException(rWeShop.getError());
                }
                WeShop weShop = rWeShop.getResult();
                weWithdrawDetailForSeller.setWeShopName(weShop.getName());
                Response<User> rUser = userReadService.findById(weWithdrawDetail.getUserId());
                if (!rUser.isSuccess()) {
                    log.error("failed to find user by id={}, error code: {}", weWithdrawDetail.getUserId(), rUser.getError());
                    throw new JsonResponseException(rUser.getError());
                }
                User user = rUser.getResult();
                weWithdrawDetailForSeller.setMobile(user.getMobile());
                weWithdrawDetailForSeller.setAppliedAt(weWithdrawDetail.getCreatedAt());
                weWithdrawDetailForSeller.setPreApplicationBalance(weWithdrawDetail.getPreApplicationBalance());
                weWithdrawDetailForSeller.setAmount(weWithdrawDetail.getAmount());
                weWithdrawDetailForSeller.setStatus(weWithdrawDetail.getStatus());
                weWithdrawDetailForSeller.setApplicantName(weWithdrawDetail.getApplicantName());
                weWithdrawDetailForSeller.setApplyRemark(weWithdrawDetail.getApplyRemark());
                weWithdrawDetailForSeller.setAuditingRemark(weWithdrawDetail.getAuditingRemark());
                weWithdrawDetailForSeller.setUpdatedAt(weWithdrawDetail.getUpdatedAt());
                data.add(weWithdrawDetailForSeller);
            }

            return Response.ok(new Paging<>(weWithdrawDetailPaging.getTotal(), data));
        } catch (Exception e) {
            log.error("fail to paging weWithdrawDetail for seller(shopId={}) by applicantName={}, mobile={}, " +
                    "status={}, statuses={}, pageNo={}, pageSize={}, cause: {}",
                    shopId, applicantName, mobile, status, statuses, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }
}
