package moonstone.web.core.component.distribution;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.OrderOutFrom;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.weShop.enums.WeShopProfitAvailableFlag;
import moonstone.weShop.enums.WeShopProfitConfirmFlag;
import moonstone.weShop.enums.WeShopProfitType;
import moonstone.weShop.model.WeShopProfit;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by CaiZhy on 2018/12/18.
 */
@Slf4j
@Component
public class WeShopProfitLogic {
    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;

    @RpcConsumer
    private SkuReadService skuReadService;

    @RpcConsumer
    private WeShopShopAccountReadService weShopShopAccountReadService;

    @RpcConsumer
    private WeShopShopAccountWriteService weShopShopAccountWriteService;

    @RpcConsumer
    private WeShopProfitReadService weShopProfitReadService;

    @RpcConsumer
    private WeShopProfitWriteService weShopProfitWriteService;

    @RpcConsumer
    private WeShopProfitSettleService weShopProfitSettleService;
    @RpcConsumer
    private WeShopItemReadService  weShopItemReadService;
    @Transactional
    public Response<Boolean> createBySkuOrders(List<SkuOrder> skuOrderList){
        try {
            Long weShopId = Long.valueOf(skuOrderList.get(0).getOutShopId());
            Long shopId = skuOrderList.get(0).getShopId();
            Response<WeShopShopAccount> rWeShopShopAccount = weShopShopAccountReadService.findByWeShopIdAndShopId(weShopId, shopId);
            if (!rWeShopShopAccount.isSuccess()) {
                log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}", weShopId, shopId, rWeShopShopAccount.getError());
                throw new JsonResponseException(rWeShopShopAccount.getError());
            }
            Long weShopShopAccountId = rWeShopShopAccount.getResult().getId();
            Long totalProfit = 0L;
            for (SkuOrder skuOrder : skuOrderList) {
                Response<List<WeShopProfit>> rExist = weShopProfitReadService.findBySkuOrderId(skuOrder.getId());
                if (!rExist.isSuccess()){
                    log.error("failed to find weShop profit bu sku order id={}, error code: {}", skuOrder.getId(), rExist.getError());
                    throw new JsonResponseException(rExist.getError());
                }
                if (!rExist.getResult().isEmpty()){
                    log.debug("sku order(id={}) has created weShop profits({})", skuOrder.getId(), rExist.getResult());
                    continue;   //如果已经有对应收益了，则不创建
                }
                Response<ShopOrder> rShopOrder = shopOrderReadService.findById(skuOrder.getOrderId());
                if (!rShopOrder.isSuccess()) {
                    log.error("failed to find shop order by id={}, error code: {}", skuOrder.getOrderId(), rShopOrder.getError());
                    throw new JsonResponseException(rShopOrder.getError());
                }
                ShopOrder shopOrder = rShopOrder.getResult();
                if (Objects.equal(OrderOutFrom.WE_SHOP.Code(), shopOrder.getOutFrom())) {
                    WeShopProfit weShopProfit = new WeShopProfit();
                    weShopProfit.setType(WeShopProfitType.ORDER_PROFIT.getValue());
                    Response<Sku> rSku = skuReadService.findSkuById(skuOrder.getSkuId());
                    if (!rSku.isSuccess()) {
                        log.error("failed to find sku by id={}, error code: {}", skuOrder.getSkuId(), rSku.getError());
                        throw new JsonResponseException(rSku.getError());
                    }
                    Sku sku = rSku.getResult();
                    //modify by liuchao 20190401

                    /* Long profit = sku.getExtraPrice().get(DistributionConstants.SKU_PROFIT).longValue();*/
                    long profit;
                    try {
                        profit = skuOrder.getOriginFee() - sku.getExtraPrice().get(DistributionConstants.SUPPLY_PRICE);
                    }
                    catch (Exception ex)
                    {
                        log.error("[profit](parse) get distribution price failed");
                        throw ex;
                    }

                    weShopProfit.setProfit(profit * skuOrder.getQuantity());
                    weShopProfit.setWeShopId(Long.valueOf(shopOrder.getOutShopId()));
                    weShopProfit.setShopId(shopOrder.getShopId());
                    List<Long> skuIds = new ArrayList<>();
                    skuIds.add(skuOrder.getSkuId());
                    weShopProfit.setOrderSkuIds(skuIds);
                    weShopProfit.setSkuOrderId(skuOrder.getId());
                    weShopProfit.setShopOrderId(skuOrder.getOrderId());
                    weShopProfit.setConfirmFlag(WeShopProfitConfirmFlag.UNCONFIRMED.getValue());
                    weShopProfit.setAvailableFlag(WeShopProfitAvailableFlag.UNAVAILABLE.getValue());
                    weShopProfit.setStatus(1);
                    Response<Long> response = weShopProfitWriteService.create(weShopProfit);
                    if (!response.isSuccess()) {
                        log.error("failed to create weShop profit({}), error code: {}", weShopProfit, response.getError());
                        throw new JsonResponseException(response.getError());
                    }
                    totalProfit += weShopProfit.getProfit();
                }
            }
            Response<Boolean> response = weShopShopAccountWriteService.addTotalProfitAmount(weShopShopAccountId, totalProfit);
            if (!response.isSuccess() || !response.getResult()) {
                log.error("failed to add total profit amount by id={}, profit={}, error code: {}", weShopShopAccountId, totalProfit, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to create weShop profit by skuOrders({}), cause: {}", skuOrderList, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopProfit.create.fail");
        }
    }

    @Transactional
    public Response<Boolean> confirmBySkuOrders(List<SkuOrder> skuOrderList){
        try {
            for (SkuOrder skuOrder : skuOrderList) {
                Response<List<WeShopProfit>> rExists = weShopProfitReadService.findBySkuOrderId(skuOrder.getId());
                if (!rExists.isSuccess()){
                    log.error("failed to find weShop profit bu sku order id={}, error code: {}", skuOrder.getId(), rExists.getError());
                    throw new JsonResponseException(rExists.getError());
                }
                List<WeShopProfit> exists = rExists.getResult();
                if (exists.isEmpty()){
                    log.debug("can not find weShop profits by sku order(id={})", skuOrder.getId());
                    continue;   //如果找不到对应收益了，则不确认
                }
                if (exists.size() > 1){ //正常情况，再确认收货前，同一个子订单对应的未删除收益最多一条
                    log.error("exist more than one weShop profit({}) by the same sku order(id={}) before confirm", exists, skuOrder.getId());
                    throw new JsonResponseException("weShopProfit.exist.illegal");
                }
                WeShopProfit toBeConfirmed = exists.get(0);
                Response<Boolean> response = weShopProfitWriteService.confirm(toBeConfirmed.getId(), 0L);
                if (!response.isSuccess() || !response.getResult()){
                    log.error("failed to confirm weShop profit by id={}, delayTime={}, error code: {}", toBeConfirmed.getId(), 0L, response.getError());
                    throw new JsonResponseException(response.getError());
                }
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to confirm weShop profit by skuOrders({}), cause: {}", skuOrderList, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopProfit.confirm.fail");
        }
    }

    @Transactional
    public Response<Boolean> handleRefundBySkuOrders(List<SkuOrder> skuOrderList){
        try {
            Long oneWeShopProfitId = null;
            for (SkuOrder skuOrder : skuOrderList) {
                Response<List<WeShopProfit>> rExists = weShopProfitReadService.findBySkuOrderId(skuOrder.getId());
                if (!rExists.isSuccess()){
                    log.error("failed to find weShop profit bu sku order id={}, error code: {}", skuOrder.getId(), rExists.getError());
                    throw new JsonResponseException(rExists.getError());
                }
                List<WeShopProfit> exists = rExists.getResult();
                if (exists.isEmpty()){
                    log.debug("can not find weShop profits by sku order(id={})", skuOrder.getId());
                    continue;
                }
                if (exists.size() > 1){ //正常情况，在退货款前，同一个子订单对应的未删除收益最多一条
                    log.error("exist more than one weShop profit({}) by the same sku order(id={}) before handle refund", exists, skuOrder.getId());
                    throw new JsonResponseException("weShopProfit.exist.illegal");
                }
                WeShopProfit exist = exists.get(0);
                //更新已存在的子订单收益的结算时间
                WeShopProfit toUpdate = new WeShopProfit();
                toUpdate.setId(exist.getId());
                toUpdate.setSettleAt(new Date());
                Response<Boolean> updateResponse = weShopProfitWriteService.update(toUpdate);
                if (!updateResponse.isSuccess()){
                    log.error("failed to update weShop profit({}), error code: {}", toUpdate, updateResponse.getError());
                    throw new JsonResponseException(updateResponse.getError());
                }

                Response<ShopOrder> rShopOrder = shopOrderReadService.findById(skuOrder.getOrderId());
                if (!rShopOrder.isSuccess()) {
                    log.error("failed to find shop order by id={}, error code: {}", skuOrder.getOrderId(), rShopOrder.getError());
                    throw new JsonResponseException(rShopOrder.getError());
                }
                ShopOrder shopOrder = rShopOrder.getResult();
                //不论是否结算，生成退货款的扣减收益，并结算该子订单已有的所有未结算订单
                WeShopProfit refundProfit = new WeShopProfit();
                refundProfit.setType(WeShopProfitType.REFUND_CHANGE.getValue());
                Response<Sku> rSku = skuReadService.findSkuById(skuOrder.getSkuId());
                if (!rSku.isSuccess()) {
                    log.error("failed to find sku by id={}, error code: {}", skuOrder.getSkuId(), rSku.getError());
                    throw new JsonResponseException(rSku.getError());
                }
                Sku sku = rSku.getResult();
                //modify by liuchao 20190401

               /* Long profit = sku.getExtraPrice().get(DistributionConstants.SKU_PROFIT).longValue();*/

                Long profit=skuOrder.getOriginFee()- Long.valueOf(skuOrder.getExtra().get(DistributionConstants.SUPPLY_PRICE));

                refundProfit.setProfit(-profit * skuOrder.getQuantity());
                refundProfit.setWeShopId(Long.valueOf(shopOrder.getOutShopId()));
                refundProfit.setShopId(shopOrder.getShopId());
                List<Long> skuIds = new ArrayList<>();
                skuIds.add(skuOrder.getSkuId());
                refundProfit.setOrderSkuIds(skuIds);
                refundProfit.setSkuOrderId(skuOrder.getId());
                refundProfit.setShopOrderId(skuOrder.getOrderId());
                refundProfit.setConfirmFlag(WeShopProfitConfirmFlag.CONFIRMED.getValue());
                Long delayTime = 0L;    //TODO 是否延迟结算时间
                Date confirmAt = new Date();
                Long settleTime = confirmAt.getTime() + delayTime;
                Date settleAt = new Date(settleTime);
                refundProfit.setConfirmAt(confirmAt);
                refundProfit.setSettleAt(settleAt);
                refundProfit.setAvailableFlag(WeShopProfitAvailableFlag.UNAVAILABLE.getValue());
                refundProfit.setStatus(1);
                Response<Long> response = weShopProfitWriteService.create(refundProfit);
                if (!response.isSuccess()) {
                    log.error("failed to create weShop profit({}), error code: {}", refundProfit, response.getError());
                    throw new JsonResponseException(response.getError());
                }
                oneWeShopProfitId = exist.getId();
            }
            //结算该子订单已有的所有未结算订单
            if (oneWeShopProfitId != null) {
                Response<Boolean> settleResponse = weShopProfitSettleService.settleById(oneWeShopProfitId);
                if (!settleResponse.isSuccess()) {
                    log.error("failed to settle weShop profit by id={}, error code: {}", oneWeShopProfitId, settleResponse.getError());
                    throw new JsonResponseException(settleResponse.getError());
                }
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to handle refund weShop profit by skuOrders({}), cause: {}", skuOrderList, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopProfit.handle.refund.fail");
        }
    }

}
