package moonstone.web.core.component;

import blue.sea.moonstone.bridge.app.ShareDataHelper;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.mongo.MongoClient;

import java.util.concurrent.ConcurrentHashMap;

public enum Mongo {
    Pool;

    ConcurrentHashMap<String, MongoClient> mongo = new ConcurrentHashMap<>();

    public MongoClient getMongo(Vertx vertx) {
        return getMongo(ShareDataHelper.getMongoConfig(vertx), vertx);
    }

    public MongoClient getMongo(JsonObject conf, Vertx vertx) {
        if (mongo.containsKey(conf.toString())) {
            return mongo.get(conf.toString());
        }
        MongoClient mongoClient = MongoClient.createShared(vertx, conf);
        MongoClient exists = mongo.putIfAbsent(conf.toString(), mongoClient);
        if (exists != null && exists != mongoClient) {
            mongoClient.close();
        } else {
            exists = mongoClient;
        }
        return exists;
    }
}
