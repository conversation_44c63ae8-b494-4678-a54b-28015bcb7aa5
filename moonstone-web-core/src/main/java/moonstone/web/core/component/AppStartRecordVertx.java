package moonstone.web.core.component;

import io.vertx.core.Vertx;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;

@Component
public class AppStartRecordVertx{

    @Resource
    private AppStartUUID appStartUUID;

    @Resource
    private Vertx vertx;

    @EventListener(ContextRefreshedEvent.class)
    public void refresh(){
        try {
            String address = InetAddress.getLocalHost().getHostAddress();
            vertx.runOnContext(v -> {
                vertx.sharedData().getAsyncMap("UUID")
                        .onSuccess(map -> map.put(address, appStartUUID.password()));
            });
        }catch (Exception ignored){

        }
    }
}
