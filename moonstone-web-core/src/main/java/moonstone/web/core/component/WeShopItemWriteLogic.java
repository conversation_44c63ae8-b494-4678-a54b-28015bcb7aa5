package moonstone.web.core.component;

import com.google.common.base.Throwables;
import com.hazelcast.core.HazelcastInstance;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.cache.WeShopItemCacher;
import moonstone.cache.WeShopSkuCacheHolder;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.weShop.domain.WeShopSkuPriceSlice;
import moonstone.weShop.enums.WeShopItemIndexDisplayStatus;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.weShop.service.WeShopSkuWriteService;
import moonstone.web.core.component.item.model.WeShopItemCreateDTO;
import moonstone.web.core.component.item.model.enu.SkuPriceModifyType;
import moonstone.web.core.component.profit.WeShopSkuTaxCalculatorBase;
import moonstone.web.core.events.weShopItem.WeShopItemDeletedEvent;
import moonstone.web.core.events.weShopItem.WeShopItemUpdatedEvent;
import moonstone.web.core.mirror.app.GongXiaoAppItemInvocation;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.model.dto.record.ShareTimeToday;
import moonstone.web.core.model.dto.tax.ProfitAndTax;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Date:    2019/1/2
 */
@Component
@Slf4j
public class WeShopItemWriteLogic {
    @Resource
    private WeShopItemReadService weShopItemReadService;
    @Autowired
    private WeShopItemWriteService weShopItemWriteService;
    @Autowired
    private WeShopSkuReadService weShopSkuReadService;
    @Autowired
    private WeShopSkuWriteService weShopSkuWriteService;
    @Autowired
    private WeShopSkuTaxCalculatorBase weShopSkuTaxCalculatorBase;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private RecordManager recordManager;
    @Autowired
    private JedisPool jedisPool;
    @Autowired
    private ItemCacheHolder itemCacheHolder;
    @Autowired
    private WeShopSkuCacheHolder weShopSkuCacheHolder;
    @Autowired
    private WeShopItemCacher weShopItemCacher;
    @Autowired
    private SkuCustomReadService skuCustomReadService;
    @Autowired
    private GongXiaoAppItemInvocation gongXiaoAppItemInvocation;
    @Autowired
    private SourceShopQuerySlice sourceShopQuerySlice;

    @Resource
    private RedissonClient redissonClient;

    @Transactional(rollbackFor = RuntimeException.class)
    public Either<List<Long>> createWeShopItemOrUpdate(Long weShopId, List<WeShopItemCreateDTO> weShopItemCreateDTOList, boolean onlyModify) {
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseThrow(() -> Translate.exceptionOf("店铺未找到"));
        Set<Long> weShopItemIdSet = new HashSet<>();
        for (WeShopItemCreateDTO weShopItemCreateDTO : weShopItemCreateDTOList) {
            // 清单统计
            if (weShopItemReadService.countByWeShopIdAndStatus(weShopId, 0).getResult() >= 100) {
                return Either.error(Translate.of("清单总数已满，建议您先去上架或清理商品"));
            }
            // 商品判断
            Item item = weShopItemCreateDTO.getItem();
            if (item.getStatus() <= 0) {
                return Either.error(Translate.of("商品[%s](%s)已下架", item.getName(), item.getId()));
            }
            // use the real lock
            Lock lock = redissonClient.getLock(WeShopItem.class.getSimpleName() + weShopId + "#" + item.getId());
            try {
                if (!lock.tryLock(15, TimeUnit.SECONDS)) {
                    throw new RuntimeException("FAIL TO GAIN LOCK");
                }
            } catch (Exception e) {
                log.error("{} lock timeout [{} [{}]", LogUtil.getClassMethodName(), weShopId, item.getId(), e);
                return Either.error(Translate.exceptionOf("系统超时, 请稍后重试"));
            }
            try {
                boolean priceSet = false;
                for (Sku sku : weShopItemCreateDTO.getPriceModifyMapBySku().keySet()) {
                    // 初始化映射商品状态
                    Long weShopItemId = weShopItemWriteService.initStatusForCreateWeShopItem(weShopId, item.getShopId(), item.getId(), sku.getId())
                            .elseThrow();
                    WeShopItem updateSpecification = new WeShopItem();
                    updateSpecification.setId(weShopItemId);
                    updateSpecification.setSpecification(sku.getSpecification());
                    weShopItemWriteService.update(updateSpecification);
                    // 统计发送事件
                    weShopItemIdSet.add(weShopItemId);
                    // 判断价格是否已经设置
                    // 获取参数信息
                    Optional<ThirdPartySystem> thirdPartySystem = Optional.ofNullable(sku.getTags()).map(tag -> tag.getOrDefault(SkuTagIndex.pushSystem.name(), "").split(",")[0])
                            .filter(StringUtils::hasText)
                            .map(Integer::parseInt).map(ThirdPartySystem::fromInt);
                    WeShopItemCreateDTO.PriceModify priceModify = weShopItemCreateDTO.getPriceModifyMapBySku().get(sku);
                    WeShopSkuPriceSlice weShopSkuPriceSlice = new WeShopSkuPriceSlice(weShopSkuReadService, weShopSkuWriteService, weShopItemId,
                            weShopId, sku, item, priceModify.getTaxBear());
                    // price is modified, change the tax and profit
                    if (Objects.nonNull(priceModify.getType()) && Objects.nonNull(priceModify.getPrice())) {
                        if (!priceSet && Objects.nonNull(priceModify.getPrice()) && priceModify.getPrice() > 0) {
                            priceSet = true;
                        }
                        if (Objects.nonNull(priceModify.getPrice()) && priceModify.getPrice() > 0) {
                            // 预先计算佣金然后置入
                            decorateWithPriceProfitAndTax(thirdPartySystem.orElse(null), priceModify, sku, weShopSkuPriceSlice);
                        } else {
                            decorateDefaultTax(thirdPartySystem.orElse(null), priceModify, sku, weShopSkuPriceSlice);
                        }
                    }
                    // extra action, invalidate the cache and increase the record
                    Consumer<Long> choose = priceSet && !onlyModify ?
                            weShopSkuId -> gongXiaoAppItemInvocation.createGongXiaoShopItem(weShopId, sku, weShopSkuId) : none -> {
                    };
                    weShopSkuPriceSlice.persist(onSell -> recordManager.increaseRecord(null, ShareTimeToday.build(weShopId, OrderOutFrom.WE_SHOP, item.getId())))
                            .ifSuccess(choose)
                            .ifSuccess(weShopSkuCacheHolder::invalidate).take();
                }
                if (priceSet && !onlyModify) {
                    // 需要上架商品 则需要判断 店铺是否通过审核
                    if (weShop.getStatus() != 1) {
                        throw Translate.exceptionOf("店铺未审核通过, 无法上架商品");
                    }
                    weShopItemIdSet.forEach(weShopItemId -> {
                        weShopItemWriteService.updateStatus(weShopItemId, 1);
                        weShopSkuWriteService.updateStatusByWeShopItemId(weShopItemId, 1);
                    });
                }
                // hack
                if (sourceShopQuerySlice.queryProjectIdByShopIdAndSource(item.getShopId(), MirrorSource.GongXiao.name()).isSuccess()) {
                    for (WeShopItem weShopItem : Optional.ofNullable(weShopItemReadService.findListByWeShopIdAndItemId(weShopId, item.getId()).getResult()).orElseGet(ArrayList::new)) {
                        if (weShopSkuReadService.findByWeShopItemId(weShopItem.getId()).getResult().isEmpty()) {
                            weShopItemWriteService.delete(weShopItem.getId());
                            EventSender.publish(new WeShopItemDeletedEvent(weShopItem.getId()));
                        }
                    }
                }
            } finally {
                lock.unlock();
            }
        }
        try {
            weShopItemIdSet.stream().peek(weShopItemCacher::invalidateWeShopItemById).map(WeShopItemUpdatedEvent::new).forEach(EventSender::publish);
        } catch (Exception e) {
            log.error("{} fail to update the cache", LogUtil.getClassMethodName(), e);
        }
        return Either.ok(new ArrayList<>(weShopItemIdSet));
    }

    private void decorateDefaultTax(ThirdPartySystem thirdPartySystem, WeShopItemCreateDTO.PriceModify priceModify, Sku sku, WeShopSkuPriceSlice weShopSkuPriceSlice) {
        long sellPrice = sku.getPrice().longValue();
        boolean bearTax = Optional.ofNullable(skuCustomReadService.findBySkuId(sku.getId()))
                .map(SkuCustom::getCustomTaxHolder).filter(Predicate.isEqual(2)).isPresent();
        ProfitAndTax profitAndTax = weShopSkuTaxCalculatorBase.profitCalculate(sku.getId(),
                sku.getShopId(),
                thirdPartySystem,
                sku.getOuterSkuId(),
                sellPrice,
                sku.getPrice().longValue(),
                null,
                priceModify.getTaxBear(),
                bearTax
        );
        if (profitAndTax.getProfit() < 0L) {
            throw new RuntimeException(Translate.of("利润不能低于0"));
        }
        weShopSkuPriceSlice.setProfit(profitAndTax.getProfit());
        weShopSkuPriceSlice.setTax(profitAndTax.getTax());
    }

    /**
     * 置入预先计算的佣金和利润 以供搜索引擎使用
     *
     * @param thirdPartySystem    第三方系统
     * @param priceModify         金额
     * @param sku                 单品
     * @param weShopSkuPriceSlice 改价业务切面模型
     */
    private void decorateWithPriceProfitAndTax(ThirdPartySystem thirdPartySystem, WeShopItemCreateDTO.PriceModify priceModify, Sku sku, WeShopSkuPriceSlice weShopSkuPriceSlice) {
        // be ware of Java -128..127 mem problem
        boolean modifyPriceBySet = SkuPriceModifyType.SET.getValue() == priceModify.getType();
        long sellPrice = modifyPriceBySet ?
                weShopSkuPriceSlice.setPrice(priceModify.getPrice()).take() :
                weShopSkuPriceSlice.increasePrice(priceModify.getPrice()).take();
        // not bonded, set raw profit
        if (!BondedType.fromInt(itemCacheHolder.findItemById(sku.getItemId()).getIsBonded()).isBonded()) {
            weShopSkuPriceSlice.setProfit(sellPrice - sku.getPrice());
            weShopSkuPriceSlice.setTax(0L);
            return;
        }
        // set preview profit and tax
        boolean sellerBearTax = Optional.ofNullable(priceModify.getTaxBear()).orElse(false);
        ProfitAndTax profitAndTax = weShopSkuTaxCalculatorBase.profitCalculate(sku.getId(),
                sku.getShopId(),
                thirdPartySystem,
                sku.getOuterSkuId(),
                sellPrice,
                sku.getPrice().longValue(),
                null,
                sellerBearTax,
                Optional.ofNullable(skuCustomReadService.findBySkuId(sku.getId()))
                        .map(SkuCustom::getCustomTaxHolder).filter(Predicate.isEqual(2)).isPresent()
        );
        if (profitAndTax.getProfit() < 0L) {
            throw new RuntimeException(Translate.of("利润不能低于0"));
        }
        weShopSkuPriceSlice.setProfit(profitAndTax.getProfit());
        weShopSkuPriceSlice.setTax(profitAndTax.getTax());
    }

    private void removeTheLock(Long weShopId, Long id) {
        try (Jedis jedis = jedisPool.getResource()) {
            String key = generateKey(weShopId, id);
            log.debug("{} delete lock for item[WeShopId=>{}, itemId=>{}] with key({}, {})",
                    LogUtil.getClassMethodName(), weShopId, id, key, jedis.del(key));
        }
    }

    private boolean existsWeShopItemWithLock(Long weShopId, Long id) {
        try (Jedis jedis = jedisPool.getResource()) {
            String key = generateKey(weShopId, id);
            if (jedis.exists(key)) {
                log.debug("{} item[WeShopId=>{}, itemId=>{}] modify at multi-thread", LogUtil.getClassMethodName(), weShopId, id);
                jedis.expire(key, 5);
                return true;
            }
            String self = UUID.randomUUID().toString();
            jedis.setnx(key, self);
            jedis.expire(key, 5);
            if (!Objects.equals(jedis.get(key), self)) {
                return true;
            }
        }
        return false;
    }

    private String generateKey(Long weShopId, Long id) {
        return String.format("weShopItemLock[%s]-[%s]", weShopId, id);
    }

    @Transactional(rollbackFor = Exception.class)
    public Response<Boolean> sort(List<Long> ids, Long weShopId) {
        try {
            Response<WeShopItem> firstWeShopItemResponse = weShopItemReadService.findById(ids.get(0));
            if (!firstWeShopItemResponse.isSuccess()) {
                log.error("failed to find weShop item by id={}, error code: {}", ids.get(0), firstWeShopItemResponse.getError());
                throw new JsonResponseException(firstWeShopItemResponse.getError());
            }
            Map<String, Object> criteria = new HashMap<>(8);
            criteria.put("weShopId", weShopId);
            criteria.put("indexDisplay", WeShopItemIndexDisplayStatus.Show.getValue());
            Response<List<WeShopItem>> rWeShopItems = weShopItemReadService.list(criteria);
            if (!rWeShopItems.isSuccess()) {
                log.error("failed to list index display weShop items by weShopId={}, error code: {}",
                        weShopId, rWeShopItems.getError());
                throw new JsonResponseException(rWeShopItems.getError());
            }
            List<Long> existWeShopItemIds = rWeShopItems.getResult().stream().map(WeShopItem::getId).collect(Collectors.toList());
            if (existWeShopItemIds.size() != ids.size()) {
                throw new JsonResponseException("weShopItem.sort.num.illegal");
            }
            if (!existWeShopItemIds.containsAll(ids)) {
                throw new JsonResponseException("weShopItem.sort.illegal");
            }
            List<WeShopItem> toUpdates = new ArrayList<>();
            for (Long id : ids) {
                WeShopItem toUpdate = new WeShopItem();
                toUpdate.setId(id);
                toUpdate.setSortIndex(toUpdates.size() + 1);
                toUpdates.add(toUpdate);
            }
            Response<Boolean> response = weShopItemWriteService.batchUpdate(toUpdates);
            if (!response.isSuccess()) {
                log.error("failed to batch update weShopItems({}), error code: {}", toUpdates, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response;
        } catch (Exception e) {
            log.error("failed to sort ids={}, cause: {}", ids, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 连带更新WeShopItem的状态
     *
     * @param weShopId     微店Id 为null则取微店ItemShopId
     * @param weShopItemId 微店商品id
     * @param status       状态
     * @return 更新状态
     */
    @Transactional(rollbackFor = Exception.class)
    public Either<Boolean> updateStatus(Long weShopId, Long weShopItemId, int status) {
        WeShopItem weShopItem = weShopItemReadService.findById(weShopItemId).getResult();
        if (weShopId == null) {
            weShopId = weShopItem.getWeShopId();
        }
        if (!weShopItemWriteService.updateStatus(weShopItemId, status).isSuccess()) {
            throw new RuntimeException(String.format("fail to update WeShopItem[%s] status[%s]", weShopItemId, status));
        }
        if (weShopSkuReadService.findByWeShopItemId(weShopItemId).map(List::isEmpty).orElse(true)) {
            weShopSkuWriteService.updateStatusByWeShopIdAndItemId(weShopId, weShopItem.getItemId(), status).take();
        } else {
            weShopSkuWriteService.updateStatusByWeShopItemId(weShopItemId, status).take();
        }
        EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId));
        return Either.ok(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public Either<Boolean> delete(Long weShopId, Long weShopItemId) {
        WeShopItem weShopItem = weShopItemReadService.findById(weShopItemId).getResult();
        if (weShopId == null) {
            weShopId = weShopItem.getWeShopId();
        }
        if (!weShopItemWriteService.delete(weShopItemId).isSuccess()) {
            throw new RuntimeException(new Translate("删除商品[%s]失败", weShopItem.getId()).toString());
        }
        weShopSkuWriteService.deleteWeShopSkuByWeShopItemId(weShopId, weShopItem.getId()).take();
        EventSender.publish(new WeShopItemDeletedEvent(weShopItemId));
        return Either.ok(true);
    }
}
