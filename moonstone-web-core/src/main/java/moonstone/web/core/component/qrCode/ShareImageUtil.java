package moonstone.web.core.component.qrCode;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.util.FontUtil;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;

@Slf4j
/**
 * 商品分享图片生成工具类
 */
@Component
public class ShareImageUtil {


    public final static String IMAGE_FORMAT_JPG = "png"; // 分享图格式
    private final static int IMAGE_WIDTH = 750; // 分享图宽度
    private final static int IMAGE_HEIGHT = 1080; // 分享图高度

    /**
     * 图片size设置
     *
     * @param width
     * @param height
     * @param image
     * @return
     */
    public static BufferedImage resizeImage(int width, int height, BufferedImage image) {
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
        Graphics2D graphics2D = (Graphics2D) bufferedImage.getGraphics();
        graphics2D.drawImage(image.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);
        graphics2D.dispose();
        return bufferedImage;
    }

    private static BufferedImage generateBackgroundImage(int width, int height, int border) {
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
        Graphics2D graphics2D = (Graphics2D) bufferedImage.getGraphics();
        graphics2D.fillRect(0, 0, width, height);
        graphics2D.setColor(Color.white);
        graphics2D.fillRect(0, 0, width, height);
        //画边框
        if (border > 0) {
            graphics2D.setColor(Color.gray);
            graphics2D.drawRect(0, 0, width - border, height - border);
        }
        graphics2D.dispose();
        return bufferedImage;
    }

    /**
     * 生成白色背景图
     *
     * @param width  宽度
     * @param height 高度
     * @return 图片
     */
    private static BufferedImage generateBackgroundImage(int width, int height) {
        return generateBackgroundImage(width, height, 1);
    }

    /**
     * 字符总宽度
     *
     * @param g
     * @param str
     * @return
     */
    private static int getStringWidth(Graphics g, String str) {
        char[] strArr = str.toCharArray();
        int strWidth = g.getFontMetrics().charsWidth(strArr, 0, str.length());
        return strWidth;
    }

    /**
     * 每行的字符数
     *
     * @param strNum   字符串个数
     * @param rowWidth 行宽
     * @param strWidth 字符总宽度
     * @return 每行的字符数 = 行宽 / (总宽度 /字符数)
     */
    private static int getRowStrNum(int strNum, int rowWidth, int strWidth) {
        int rowStrNum = 0;
        rowStrNum = rowWidth * strNum / strWidth;
        return rowStrNum;
    }

    /**
     * 行数
     *
     * @param strWidth 字符总宽度
     * @param rowWidth 行宽
     * @return
     */
    private static int getRows(int strWidth, int rowWidth) {
        int rows = 0;
        if (strWidth % rowWidth > 0) {
            rows = strWidth / rowWidth + 1;
        } else {
            rows = strWidth / rowWidth;
        }
        return rows;
    }

    private static int getStringHeight(Graphics g) {
        int height = g.getFontMetrics().getHeight();
        return height;
    }

    /**
     * 分行写入字符
     *
     * @param g
     * @param strContent
     * @param rowWidth
     * @param x
     * @param y
     * @param font
     * @param preRows    显示的行数
     */
    private static void drawStringWithFontStyleLine(Graphics g, String strContent, int rowWidth, int x, int y, Font font, int preRows) {
        g.setFont(font);
        //获取字符串 字符的总宽度
        int strWidth = getStringWidth(g, strContent);
        //获取字符高度
        int strHeight = getStringHeight(g);
        preRows = preRows - 1;
        // 总宽度大于行宽度，需要分行
        if (strWidth > rowWidth) {
            int rowStrNum = getRowStrNum(strContent.length(), rowWidth, strWidth);
            int rows = getRows(strWidth, rowWidth);
            if (rows > preRows) {
                rows = preRows;
            }
            for (int i = 0; i <= rows; i++) {
                if (i > 0) {
                    //第一行不需要增加字符高度，以后的每一行在换行的时候都需要增加字符高度
                    y = y + strHeight;
                }
                //获取各行的String
                String temp = "";
//                if (i == rows - 1) {
                //最后一行
//                temp = strContent.substring(i * rowStrNum, strContent.length());
                if (i == rows) {
                    if (strContent.length() > (i + 1) * rowStrNum) {
                        temp = strContent.substring(i * rowStrNum, (i + 1) * rowStrNum - 3) + "...";
                    } else {
                        temp = strContent.substring(i * rowStrNum);
                    }
                } else {
                    temp = strContent.substring(i * rowStrNum, (i + 1) * rowStrNum);
                }
                g.drawString(temp, x, y);
            }
        } else {
            //直接绘制
            g.drawString(strContent, x, y);
        }

    }

    /**
     * 在横向一行中，居中写入文字
     *
     * @param g
     * @param strContent
     * @param font
     * @param y
     */
    private static void drawStringCenter(Graphics g, String strContent, Font font, int y) {
        g.setFont(font);
        int strWidth = getStringWidth(g, strContent);
        int x = (IMAGE_WIDTH - strWidth) / 2; // 默认文字长度小于图片宽度
        g.drawString(strContent, x, y);
    }

    private final static int ITEM_IMAGE_WIDTH = 622; // 商品图片宽度
    private final static int ITEM_IMAGE_HEIGHT = 622; // 商品图片高度
    private final static int ITEM_IMAGE_MARGIN_X = 64; // 商品图片空白边距
    private final static int ITEM_IMAGE_MARGIN_Y = 64; // 商品图片空白边距

    private final static int ITEM_WORD_ROW_WIDTH = 622; // 商品名称文字行宽
    private final static int ITEM_WORD_INDEX_X = 64; // 商品名称文字坐标x
    private final static int ITEM_WORD_ROW_HEIGHT = 96; // 商品名称文字行宽
    private final static int ITEM_WORD_INDEX_Y = 766; // 商品名称文字坐标Y

    private final static int ITEM_QR_CODE_INDEX_X = 72; // 二维码坐标x
    private final static int ITEM_QR_CODE_INDEX_Y = 846; // 二维码坐标Y

    private final static int ITEM_PRICE_INDEX_Y = 916; // 价格坐标Y
    private final static int ITEM_PRICE_INDEX_X = 276; //价格坐标X


    private final static int ITEM_NOTE_INDEX_Y = 994; //备注坐标Y
    private final static int ITEM_NOTE_INDEX_X = 276; //备注坐标x

    /**
     * 创建店铺分享图片
     *
     * @param name        店铺名 将显示在图片头部
     * @param desc        动作描述将显示在分享二维码下面
     * @param shareQrCode 分享二维码在中间, 占主体
     * @param fingerImage 装饰性 手指图片
     * @return 分享出的图片
     */
    public static BufferedImage createShopShareImage(String name, String desc, BufferedImage shareQrCode, BufferedImage fingerImage) {
        // 空白背景图生成
        BufferedImage image = generateBackgroundImage(IMAGE_WIDTH, IMAGE_HEIGHT);
        Graphics2D graphics = image.createGraphics();
        //消除文字锯齿
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        //消除画图锯齿
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 商品名写入
        graphics.setColor(new Color(51, 51, 51));

        try {
            Font messageFont = FontUtil.getSiYuanFont();
            messageFont = messageFont.deriveFont(Font.PLAIN, 32);
            drawStringCenter(graphics, name, messageFont, 84);
            drawStringCenter(graphics, desc, messageFont, 756);
        } catch (Exception ex) {
            log.error("{} fail to draw text-item-name [{}]", LogUtil.getClassMethodName(), name, ex);
        }
        int drawMidWidth = IMAGE_WIDTH - shareQrCode.getWidth() / 2;
        int drawMidHeight = 440 - shareQrCode.getHeight() / 2;
        //graphics.drawImage(shareQrCode, drawMidWidth, drawMidHeight, null);
        graphics.drawImage(shareQrCode, 127, 192, 496, 496, null);
        graphics.drawImage(fingerImage, 303, 856, 144, 144, null);

        graphics.dispose();
        return image;
    }

    /**
     * 商品分享图生成
     *
     * @param itemImageUrl 商品图片地址
     * @param qrCodeRef    二维码引用地址
     * @param itemName     商品名称
     * @param price        商品价格
     * @param note         备注
     * @return
     */
    public static BufferedImage createItemShareImage(String itemImageUrl, BufferedImage qrCodeRef, String itemName, BigDecimal price, String note) {
        BufferedImage image = null;
        try {
            // 空白背景图生成
            image = generateBackgroundImage(IMAGE_WIDTH, IMAGE_HEIGHT);
            // 商品图画入
            URL imageURL = new URL(itemImageUrl);
            BufferedImage itemImage = resizeImage(ITEM_IMAGE_WIDTH, ITEM_IMAGE_HEIGHT, ImageIO.read(imageURL));
            Graphics2D g = image.createGraphics();

            //消除文字锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            //消除画图锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            g.drawImage(itemImage, ITEM_IMAGE_MARGIN_X, ITEM_IMAGE_MARGIN_Y, itemImage.getWidth(), itemImage.getHeight(), null);
            // 商品名写入
            g.setColor(new Color(51, 51, 51));

            Font messageFont = FontUtil.getSiYuanFont();
            messageFont = messageFont.deriveFont(Font.PLAIN, 32);

            drawStringWithFontStyleLine(g, itemName, ITEM_WORD_ROW_WIDTH, ITEM_WORD_INDEX_X, ITEM_WORD_INDEX_Y, messageFont, 2);

            // 二维码生成并画入
            BufferedImage qrCodeImage = resizeImage(180, 180, qrCodeRef);
            g.drawImage(qrCodeImage, ITEM_QR_CODE_INDEX_X, ITEM_QR_CODE_INDEX_Y, qrCodeImage.getWidth(), qrCodeImage.getHeight(), null);

            // 金额画入
            Font priceFont = FontUtil.getSiYuanFont();
            priceFont = priceFont.deriveFont(Font.BOLD, 48);
            g.setColor(new Color(245, 18, 20));
            g.setFont(priceFont);
            String priceStr = new DecimalFormat("￥#0.00").format(price);
            g.drawString(priceStr, ITEM_PRICE_INDEX_X, ITEM_PRICE_INDEX_Y);

            // 备注信息画入
            Font noteFont = FontUtil.getSiYuanFont();
            noteFont = noteFont.deriveFont(Font.PLAIN, 24);
            g.setColor(new Color(153, 153, 153));
            g.setFont(noteFont);
            g.drawString(note, ITEM_NOTE_INDEX_X, ITEM_NOTE_INDEX_Y);
            g.dispose();

        } catch (Exception e) {
            log.error("fail to create itemShareImage, cause: {}", Throwables.getStackTraceAsString(e));
        }
        return image;

    }

    /**
     * 商品分享图生成
     *
     * @param itemImageUrl 商品图片地址
     * @param qrCodeRef    二维码引用地址
     * @param itemName     商品名称
     * @param price        商品价格
     * @param note         备注
     * @return
     */
    public static BufferedImage createItemShareImageForGongXiao(String itemImageUrl, BufferedImage qrCodeRef, String itemName, BigDecimal price, String origin, String note) {
        BufferedImage image = null;
        try {
            // 空白背景图生成
            image = generateBackgroundImage(IMAGE_WIDTH, IMAGE_HEIGHT, 0);
            // 商品图画入
            URL imageURL = new URL(itemImageUrl);
            BufferedImage itemImage = resizeImage(ITEM_IMAGE_WIDTH, ITEM_IMAGE_HEIGHT, ImageIO.read(imageURL));
            Graphics2D g = image.createGraphics();

            //消除文字锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            //消除画图锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            g.drawImage(itemImage, ITEM_IMAGE_MARGIN_X, ITEM_IMAGE_MARGIN_Y - 40, itemImage.getWidth(), itemImage.getHeight(), null);
            // 商品名写入
            g.setColor(new Color(51, 51, 51));

            Font messageFont = FontUtil.getSiYuanFont();
            messageFont = messageFont.deriveFont(Font.PLAIN, 32);

            drawStringWithFontStyleLine(g, itemName, ITEM_WORD_ROW_WIDTH, ITEM_WORD_INDEX_X, ITEM_WORD_INDEX_Y - 55, messageFont, 1);

            // 二维码生成并画入
            BufferedImage qrCodeImage = resizeImage(180, 180, qrCodeRef);
            g.drawImage(qrCodeImage, ITEM_QR_CODE_INDEX_X, ITEM_QR_CODE_INDEX_Y, qrCodeImage.getWidth(), qrCodeImage.getHeight(), null);

            // 金额画入
            Font priceFont = FontUtil.getSiYuanFont();
            priceFont = priceFont.deriveFont(Font.BOLD, 48);
            String priceStr = new DecimalFormat("#0.00").format(price);
            String pricePrefix = "￥";
            g.setColor(new Color(245, 18, 20));
            Font s = priceFont.deriveFont(Font.BOLD, 24);
            g.setFont(s);
            g.drawString(pricePrefix, 64, 786 + 30);
            g.setFont(priceFont);
            g.drawString(priceStr, 64 + 24, 786 + 30);
            s = s.deriveFont(Font.PLAIN, 24);
            g.setFont(s);
            g.setColor(new Color(153, 153, 153));
            g.drawString(origin + "  |  " + (!origin.equals("中国") ? "跨境保税" : "普通贸易"), 64, 768 - 15);

            // 备注信息画入
            Font noteFont = FontUtil.getSiYuanFont();
            noteFont = noteFont.deriveFont(Font.PLAIN, 24);
            g.setColor(new Color(153, 153, 153));
            g.setFont(noteFont);
            g.drawString(note, ITEM_NOTE_INDEX_X, ITEM_NOTE_INDEX_Y - 60);
            g.drawString("查看商品详情", ITEM_NOTE_INDEX_X, ITEM_NOTE_INDEX_Y - 24);
            g.dispose();

        } catch (Exception e) {
            log.error("fail to create itemShareImage, cause: {}", Throwables.getStackTraceAsString(e));
        }
        return image;

    }

//    private final static int SHOP_NAME_INDEX_Y = 102; // 微店名称坐标Y
//    private final static int SHOP_QR_CODE_WIDTH = 496; // 二维码宽度
//    private final static int SHOP_QR_CODE_HEIGHT = 496; // 二维码高度
//    private final static int SHOP_IMAGE_MARGIN = 127; // 微店分享图边距
//    private final static int SHOP_QR_CODE_INDEX_Y = 192; // 二维码坐标Y
//    private final static int SHOP_NOTE_INDEX_Y = 756; // 备注坐标Y
//    private final static int SHOP_FOOT_IMAGE_WITH = 144; // 页脚图片宽度
//    private final static int SHOP_FOOT_IMAGE_HEIGHT = 144; // 页脚图片高度
//    private final static int SHOP_FOOT_INDEX_X = 303; // 页脚图片坐标X
//    private final static int SHOP_FOOT_INDEX_Y = 850; // 页脚图片坐标Y

//    /**
//     * 店铺分享图片生成
//     * @param shopLogoUrl 店铺Logo地址
//     * @param qrCodeRef 二维码引用地址
//     * @param footImageUrl 页脚图片地址
//     * @param shopName 店铺名称
//     * @param note 备注
//     * @return
//     */
//    public static BufferedImage createShopShareImage(String shopLogoUrl, String qrCodeRef, String footImageUrl, String shopName, String note){
//        BufferedImage image = null;
//        try {
//            // 空白背景图生成
//            image = generateBackgroundImage(IMAGE_WIDTH, IMAGE_HEIGHT);
//            Graphics2D g = image.createGraphics();
//
//            // 店铺名画入
//            g.setColor(new Color(51, 51, 51));
//            Font shopNameFont = new Font("微软雅黑", Font.BOLD,40);
//            drawStringCenter(g, shopName, shopNameFont, SHOP_NAME_INDEX_Y);
//
//            // 二维码生成并画入
//            QrCodeOptions qrCodeConfig = new QrCodeOptions();
//            qrCodeConfig.setMsg(qrCodeRef);
//            qrCodeConfig.setLogo(shopLogoUrl);
//            qrCodeConfig.setLogoStyle(QrCodeOptions.LogoStyle.ROUND);
//            qrCodeConfig.setW(SHOP_QR_CODE_WIDTH);
//            qrCodeConfig.setH(SHOP_QR_CODE_HEIGHT);
//            qrCodeConfig.setPicType(IMAGE_FORMAT_JPG);
//            qrCodeConfig.setMatrixToImageConfig(new MatrixToImageConfig());
//            Map<EncodeHintType, Object> hints = new HashMap<>();
//            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
//            hints.put(EncodeHintType.MARGIN, 1);
//            qrCodeConfig.setHints(hints);
//            BitMatrix bitMatrix = QrCodeUtil.encode(qrCodeConfig);
//            BufferedImage qrCodeImage = QrCodeUtil.toBufferedImage(qrCodeConfig, bitMatrix);
//            g.drawImage(qrCodeImage, SHOP_IMAGE_MARGIN, SHOP_QR_CODE_INDEX_Y, qrCodeImage.getWidth(), qrCodeImage.getHeight(), null);
//
//            // 备注信息画入
//            g.setColor(new Color(51, 51, 51));
//            Font noteFont = new Font("微软雅黑", Font.PLAIN,24);
//            drawStringCenter(g, note, noteFont, SHOP_NOTE_INDEX_Y);
//
//            // 页脚图画入
//            URL imageURL = new URL(footImageUrl);
//            BufferedImage footImage = resizeImage(SHOP_FOOT_IMAGE_WITH, SHOP_FOOT_IMAGE_HEIGHT, ImageIO.read(imageURL));
//            g.drawImage(footImage, SHOP_FOOT_INDEX_X, SHOP_FOOT_INDEX_Y, footImage.getWidth(), footImage.getHeight(), null);
//
//            g.dispose();
//
//        } catch (Exception e){
//            log.error("fail to create shopShareImage, cause: {}", Throwables.getStackTraceAsString(e));
//        }
//        return image;
//
//    }
//

}
