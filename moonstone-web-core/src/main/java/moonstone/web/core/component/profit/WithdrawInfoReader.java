package moonstone.web.core.component.profit;

import lombok.AllArgsConstructor;
import moonstone.common.model.WithExtraMap;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.InComeDetail;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.web.core.component.profit.view.WithdrawAndProfitView;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Component
@AllArgsConstructor
public class WithdrawInfoReader {
    BalanceDetailReadService balanceDetailReadService;
    BalanceDetailManager balanceDetailManager;
    WithdrawAccountReadService withdrawAccountReadService;
    WeShopWithdrawComponent weShopWithdrawComponent;

    /**
     * 打包拉取出利润提现统计数据
     *
     * @param userId 用户Id
     * @param shopId 店铺Id
     * @return 提现统计数据
     */
    public WithdrawAndProfitView getWithdrawAndProfitViewFromUserIdAndShopId(Long userId, Long shopId) {
        WithdrawAndProfitView view = new WithdrawAndProfitView();
        BigDecimal HUNDRED = new BigDecimal("100");
        List<InComeDetail> cashList = balanceDetailManager.getCash(userId, shopId).getResult();
        // 设置可体现金额
        cashList.stream().filter(BalanceDetail::isPresent).findFirst()
                .map(BalanceDetail::getFee).map(Objects::toString).map(BigDecimal::new)
                .map(db -> db.divide(HUNDRED, 2, RoundingMode.DOWN)).ifPresent(view::setMoney);
        cashList.stream().filter(db -> !db.isPresent()).findFirst()
                .map(BalanceDetail::getFee).map(Objects::toString).map(BigDecimal::new)
                .map(db -> db.divide(HUNDRED, 2, RoundingMode.DOWN)).ifPresent(view::setForeseeProfit);
        // 获取全部确定的利润(包括已提现)
        balanceDetailManager.getEarned(userId, shopId)
                .map(BalanceDetail::getFee).map(Objects::toString).map(BigDecimal::new)
                .map(db -> db.divide(HUNDRED, 2, RoundingMode.DOWN)).ifSuccess(view::setCollectProfit);
        // 获取提现密码
        String password = cashList.stream().filter(BalanceDetail::isPresent).findFirst()
                .map(WithExtraMap::getExtra).orElseGet(HashMap::new)
                .getOrDefault("pw", "");
        view.setPasswordSet(StringUtils.hasText(password));
        // 设置已体现金额 => 所有已确认金额 - 可提现剩余金额
        if (view.getCollectProfit() != null && view.getMoney() != null) {
            view.setWithdrawnProfit(view.getCollectProfit().subtract(view.getMoney()));
        }
        // 置入提现规则下限定的数据
        view.setWithdrawLimitView(weShopWithdrawComponent.constructTimeLimitForProfitDTO(userId, shopId));
        List<WithdrawAccount> withdrawAccounts = withdrawAccountReadService.findByShopIdAndUserId(shopId, userId).orElseGet(ArrayList::new);
        withdrawAccounts.stream().filter(WithdrawAccount::isDefault).findFirst().ifPresent(view::setWithdrawAccount);
        view.setWithdrawAccountList(withdrawAccounts);
        return view;
    }
}
