package moonstone.web.core.component.pay.allinpayyst.util;

import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.OpenConfig;

public class AllInPayYSTOpenClient extends OpenClient {

    private final OpenConfig config;
    private final Long tokenLoadTime;

    public AllInPayYSTOpenClient(OpenConfig config, Long tokenLoadTime) {
        super(config);

        this.config = config;
        this.tokenLoadTime = tokenLoadTime;
    }

    public OpenConfig getConfig() {
        return config;
    }

    public Long getTokenLoadTime() {
        return tokenLoadTime;
    }
}
