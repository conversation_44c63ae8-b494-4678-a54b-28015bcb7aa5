package moonstone.web.core.component.pay.xinbada.domain;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import moonstone.common.utils.UUID;

import java.security.MessageDigest;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface HeadBuilder {

    /**
     * 自动生成请求头, 在pure的情况下使用
     * @param token 密钥
     * @param userUUID  用户UUID
     * @param pojoBean  请求体
     * @return Head
     */
    static Map<String, String> buildHead(String token, String userUUID, Object pojoBean) {
        return buildHead(UUID.randomUUID().toString().replace("-", ""), token, userUUID, pojoBean);
    }

    /**
     * 构造请求头
     *
     * @param token    token
     * @param userUUID 用户ID
     * @param pojoBean 请求bean
     * @return 头
     */
    static Map<String, String> buildHead(String uuid, String token, String userUUID, Object pojoBean) {
        if (Objects.nonNull(pojoBean)) {
            try {
                MessageDigest digest = MessageDigest.getInstance("SHA-512");
                String signature = Base64.getEncoder().encodeToString(digest.digest(JSON.toJSONBytes(pojoBean)));
                return ImmutableMap.of(Index.token.name(), token
                        , Index.signature.name(), signature
                        , Index.sequence_no.name(), uuid
                        , Index.xinbada_user_uuid.name(), userUUID
                );
            } catch (Exception ignore) {
            }
        }
        return ImmutableMap.of(Index.token.name(), token
                , Index.sequence_no.name(), uuid
                , Index.xinbada_user_uuid.name(), userUUID
        );
    }

    enum Index {
        /**
         * index
         */
        token, sequence_no, xinbada_user_uuid, signature
    }
}
