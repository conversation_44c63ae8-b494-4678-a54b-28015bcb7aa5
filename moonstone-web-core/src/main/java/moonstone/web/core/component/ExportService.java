package moonstone.web.core.component;

import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.ParanaDefaultThreadFactory;
import moonstone.web.core.exports.common.Exporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Component
public class ExportService {
	@Autowired
	Exporter exporter;

	@Autowired
	Vertx vertx;

	ExecutorService executorService = Executors.newSingleThreadExecutor(new ParanaDefaultThreadFactory("exportService"));

	@PostConstruct
	private void load() {
		vertx.runOnContext(v -> {
			vertx.eventBus().localConsumer("export#xlsx")
					.handler(this::export);
		});
	}

	private void export(Message<Object> objectMessage) {
		JsonObject jsonObject = (JsonObject) objectMessage.body();
		try {
			String fileName = File.createTempFile("export", jsonObject.getString("file") + ".xls").getAbsolutePath();
			String name = jsonObject.getString("name");
			JsonArray data = jsonObject.getJsonArray("data");

			log.debug("Export Data Count -> {}", data.size());
			executorService.submit(() -> {
				Buffer buffer = exporter.exportWithTimeOut(name, data.getList(), 985 * 30);
				vertx.fileSystem().writeFile(fileName, buffer);
				objectMessage.reply(fileName);
			});
		} catch (Exception e) {
			log.error("Fail to export the file", e);
		}
	}
}
