package moonstone.web.core.component;

import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/code/reload")
public class HotReloadApp {
    @Autowired
    Vertx vertx;

    @PostMapping
    public Object reload(@RequestParam("file") MultipartFile code, String sign) throws Exception {
        return vertx.eventBus().request("code/reload", new JsonObject()
                .put("code", new String(code.getBytes()))
                .put("sign", sign)
        ).toCompletionStage().toCompletableFuture().join().body();
    }
}
