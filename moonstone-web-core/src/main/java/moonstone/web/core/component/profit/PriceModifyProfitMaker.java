package moonstone.web.core.component.profit;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.cache.WeShopSkuCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.IsPresent;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OutComeDetail;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.*;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.web.core.component.api.ProfitMakerAPIPrototype;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PriceModifyProfitMaker implements ProfitMakerAPIPrototype {
    @Autowired
    private WeShopSkuCacheHolder weShopSkuCacheHolder;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private WeShopSkuTaxCalculatorBase weShopSkuTaxCalculatorBase;
    @Autowired
    private SkuCustomReadService skuCustomReadService;
    @Autowired
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @Autowired
    private BalanceDetailReadService balanceDetailReadService;
    @Autowired
    private ItemCacheHolder itemCacheHolder;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;

    private final Predicate<String> allIsNum = Pattern.compile("^\\d+$").asPredicate();

    /**
     * 读取订单的来源,目前以ReferenceId为默认来源,如果不存在 则用outShopId当做来源
     *
     * @param shopOrder 订单
     * @return weShopId
     */
    public Long readWeShopIdFromOrder(ShopOrder shopOrder) {
        Supplier<Long> readFromOutShopId = () -> {
            if (Objects.isNull(shopOrder.getOutShopId())) {
                return null;
            }
            if (allIsNum.test(shopOrder.getOutShopId())) {
                return Long.parseLong(shopOrder.getOutShopId());
            }
            String[] splitStr = shopOrder.getOutShopId().split(":");
            if (!splitStr[0].trim().startsWith(WeShop.class.getSimpleName())) {
                throw new IllegalArgumentException(new Translate("订单[%s]来源店铺[%s]不属于[%s]", shopOrder.getId(), shopOrder.getOutShopId(), WeShop.class.getSimpleName()).toString());
            }
            return Long.parseLong(splitStr[1].trim());
        };
        // 目前就直接将outShopId拆解为两部分吧,weShop:id
        return Optional.ofNullable(readFromOutShopId.get())
                .orElseGet(() -> weShopShopAccountReadService.findByUserId(shopOrder.getReferenceId()).getResult()
                        .stream().filter(account -> Objects.equals(account.getShopId(), shopOrder.getShopId()))
                        .findFirst()
                        .map(WeShopShopAccount::getWeShopId)
                        .orElseThrow(NullPointerException::new)
                );
    }

    @Override
    public OrderOutFrom suitOrderOutFrom() {
        return OrderOutFrom.WE_SHOP;
    }

    @Override
    public Boolean suitOrder(ShopOrder shopOrder) {
        return Optional.ofNullable(readWeShopIdFromOrder(shopOrder))
                .flatMap(weShopCacheHolder::findByWeShopId)
                .isPresent();
    }

    /**
     * 拆出税费差额度
     *
     * @param skuOrder         单品订单
     * @param thirdPartySystem 第三方系统
     * @param weShopSku        分销单品
     * @param orderDiffPrice   订单价格差额
     * @return 需要承担的税额
     */
    Long splitTheTaxShouldBear(SkuOrder skuOrder, ThirdPartySystem thirdPartySystem, WeShopSku weShopSku, Long orderDiffPrice) {
        if (skuOrder.getTax() != null && skuOrder.getTax() > 0) {
            log.warn("{} skuOrder[id => {}, orderId => {}] skuId[{}] set tax[{}], so bear it all", LogUtil.getClassMethodName(), skuOrder.getId(), skuOrder.getOrderId(), skuOrder.getSkuId(), skuOrder.getTax());
            return skuOrder.getTax();
        }
        // 如果拆税出错 则报警
        return null;
    }

    @Override
    public List<BalanceDetail> earnForeseeProfit(ShopOrder shopOrder, Payment payment) {
        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
        Long weShopId = readWeShopIdFromOrder(shopOrder);
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseThrow(() -> new RuntimeException(new Translate("分销店铺[%s]计算订单[%s]利润失败,因为店铺寻找失败", weShopId, shopOrder.getId()).toString()));
        Map<Long, Long> profitBySkuOrder = new HashMap<>(8);
        for (SkuOrder skuOrder : skuOrderList) {
            Long skuId = skuOrder.getSkuId();
            // 下单代表有货
            WeShopSku weShopSku = weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShopId, skuId).orElseThrow(() -> new RuntimeException(new Translate("商品[%s]查找失败", skuId).toString()));

            Long diffPrice = Optional.ofNullable(weShopSku.getDiffPrice()).orElseGet(() -> weShopSku.getPrice() - skuCacheHolder.findSkuById(skuId).getPrice());

            long orderDiffPrice = skuOrder.getQuantity() * diffPrice;

            Optional<ThirdPartySystem> thirdPartySystem = Optional.ofNullable(skuOrder.getTags()).map(tag -> tag.get(SkuTagIndex.pushSystem.name())).map(idStr -> idStr.split(",")[0])
                    .map(Integer::parseInt).map(ThirdPartySystem::fromInt);
            // 利润将被扣减税金
            boolean sellerBearTax = Optional.ofNullable(weShopSku.getTaxSellerBear()).orElse(false);
            try {
                if (BondedType.fromInt(itemCacheHolder.findItemById(weShopSku.getItemId()).getIsBonded()).isBonded()) {
                    Long enjoyProfit = weShopSkuTaxCalculatorBase.profitCalculate(skuOrder.getSkuId(),
                            shopOrder.getShopId(),
                            thirdPartySystem.orElse(null),
                            skuOrder.getOuterSkuId(),
                            skuOrder.getOriginFee() / skuOrder.getQuantity(),
                            (skuOrder.getOriginFee() - orderDiffPrice) / skuOrder.getQuantity(),
                            Optional.ofNullable(skuOrder.getTax()).orElse(0L) / skuOrder.getQuantity(),
                            sellerBearTax,
                            Optional.ofNullable(skuCustomReadService.findBySkuId(skuId))
                                    .map(SkuCustom::getCustomTaxHolder).filter(Predicate.isEqual(2)).isPresent()
                    ).getProfit() * skuOrder.getQuantity();
                    profitBySkuOrder.put(skuOrder.getId(), enjoyProfit);
                } else {
                    profitBySkuOrder.put(skuOrder.getId(), orderDiffPrice);
                }
            } catch (Exception ex) {
                log.error("{} fail to calculate profit for order[skuOrderId => {}, orderId => {}, shopId => {}, weShopId => {}] buyerId = [{}]"
                        , LogUtil.getClassMethodName()
                        , skuOrder.getId()
                        , skuOrder.getOrderId()
                        , skuOrder.getShopId()
                        , weShopId
                        , ex);
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("改价拆税利润计算"
                        , new Translate("店铺[%s] 订单[%s] 子订单[%s] 外部SkuCode[%s] 买家[%s] 变动价[%s] 微店[%s]", skuOrder.getShopId(), skuOrder.getOrderId(), skuOrder.getId(), skuOrder.getOuterSkuId(), skuOrder.getBuyerId(), weShopSku.getWeShopId()).toString()
                        , ex, EmailReceiverGroup.DEVELOPER));
                throw new RuntimeException(ex);
            }
        }
        log.debug("{} profit enjoy by weShop[{}] from order[{}] contain[{}]", LogUtil.getClassMethodName(), weShop.getId(), shopOrder.getId(), profitBySkuOrder);
        // 打包利润
        return new ArrayList<>(makeProfitFromOrderAndWeShop(shopOrder, weShop, profitBySkuOrder));
    }

    /**
     * 封装出待收益佣金
     *
     * @param shopOrder        订单
     * @param weShop           微店
     * @param profitBySkuOrder 佣金map
     * @return 佣金
     */
    private Collection<? extends BalanceDetail> makeProfitFromOrderAndWeShop(ShopOrder shopOrder, WeShop weShop, Map<Long, Long> profitBySkuOrder) {
        Long profitSum = profitBySkuOrder.values().stream().reduce(Long::sum).orElse(0L);

        BalanceDetail foreseeProfit = new InComeDetail();
        foreseeProfit.setSourceId(shopOrder.getShopId());
        foreseeProfit.setUserId(weShop.getUserId());
        foreseeProfit.setRelatedId(shopOrder.getId());
        foreseeProfit.setChangeFee(profitSum);
        foreseeProfit.setType(ProfitType.InCome.getValue());
        // 目前全部认为是直接获取的佣金级别
        foreseeProfit.setStatus(IsPersistAble.maskBit.PersistAble.getValue()
                | BalanceDetail.maskBit.OrderRelated.getValue()
                | BalanceDetail.SourceMark.Reach.getBitMark()
                | BalanceDetail.orderRelatedMask.ShopOrder.getValue()
        );
        return Collections.singletonList(foreseeProfit);
    }


    @Override
    public List<BalanceDetail> refundProfit(ShopOrder shopOrder, Refund refund) {
        // 寻找已经存在的利润将其退回
        Long weShopId = weShopCacheHolder.findByWeShopId(readWeShopIdFromOrder(shopOrder)).map(WeShop::getUserId)
                .orElseThrow(() -> new RuntimeException(new Translate("订单[%s]来源[%s]查找微店失败", shopOrder.getId(), Optional.ofNullable(shopOrder.getReferenceId()).map(Objects::toString).orElse(shopOrder.getOutShopId())).toString()));
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseThrow(() -> new RuntimeException(new Translate("订单[%s] 微店[%s] 不存在", shopOrder.getId(), weShopId).toString()));
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(shopOrder.getShopId());
        criteria.setRelatedId(shopOrder.getId());
        criteria.setUserId(weShop.getUserId());
        criteria.setStatusBitMarks(Arrays.asList(
                IsPersistAble.maskBit.PersistAble.getValue(),
                BalanceDetail.maskBit.OrderRelated.getValue(),
                BalanceDetail.SourceMark.Reach.getBitMark(),
                BalanceDetail.orderRelatedMask.ShopOrder.getValue()
        ));
        criteria.setNotStatusBitMarks(Collections.singletonList(IsPresent.presentMaskBit.Present.getValue()));

        List<BalanceDetail> refundList = new ArrayList<>();
        for (BalanceDetail balanceDetail : Optional.ofNullable(balanceDetailReadService.paging(criteria)).map(Response::getResult).map(Paging::getData).orElseGet(ArrayList::new)) {
            BalanceDetail refundProfit = new OutComeDetail();
            BeanUtils.copyProperties(balanceDetail, refundProfit);
            refundProfit.setType(ProfitType.OutCome.getValue());
            refundProfit.setStatus(balanceDetail.getStatus() & ~BalanceDetail.maskBit.OrderRelated.getValue()
                    & ~BalanceDetail.orderRelatedMask.ShopOrder.getValue()
                    | BalanceDetail.maskBit.RefundRelated.getValue());
            refundProfit.getExtra().put("orderId", Optional.ofNullable(refundProfit.getRelatedId()).orElse(shopOrder.getId()).toString());
            refundProfit.setRelatedId(refund.getId());
            refundList.add(refundProfit);
        }
        return refundList;
    }

    @Override
    public List<BalanceDetail> convertForeseeProfitIntoPresentProfit(ShopOrder shopOrder, Shipment shipment) {
        // 寻找已经存在的利润转换为Present利润
        if (shopOrder.getStatus() <= 1) {
            log.warn("{} order[{}] at refund??", LogUtil.getClassMethodName(), shopOrder.getId());
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("退款状态结算利润??", new Translate("订单[%s] 状态[%s] 触发结算可收益利润", shopOrder.getId(), shopOrder.getStatus()).toString(), EmailReceiverGroup.DEVELOPER));
        }
        // 寻找已经存在的利润将其退回
        Long weShopId = weShopCacheHolder.findByWeShopId(readWeShopIdFromOrder(shopOrder)).map(WeShop::getId)
                .orElseThrow(() -> new RuntimeException(new Translate("订单[%s]来源[%s]查找微店失败", shopOrder.getId(), Optional.ofNullable(shopOrder.getReferenceId()).map(Objects::toString).orElse(shopOrder.getOutShopId())).toString()));
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseThrow(() -> new RuntimeException(new Translate("订单[%s] 微店[%s] 不存在", shopOrder.getId(), weShopId).toString()));
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(shopOrder.getShopId());
        criteria.setRelatedId(shopOrder.getId());
        criteria.setUserId(weShop.getUserId());
        criteria.setStatusBitMarks(Arrays.asList(
                IsPersistAble.maskBit.PersistAble.getValue(),
                BalanceDetail.maskBit.OrderRelated.getValue(),
                BalanceDetail.SourceMark.Reach.getBitMark(),
                BalanceDetail.orderRelatedMask.ShopOrder.getValue()
        ));
        criteria.setNotStatusBitMarks(Collections.singletonList(IsPresent.presentMaskBit.Present.getValue()));

        List<BalanceDetail> presentProfitList = new ArrayList<>();
        for (BalanceDetail balanceDetail : Optional.ofNullable(balanceDetailReadService.paging(criteria)).map(Response::getResult).map(Paging::getData).orElseGet(ArrayList::new)) {
            BalanceDetail presentProfit = new InComeDetail();
            BeanUtils.copyProperties(balanceDetail, presentProfit);
            presentProfit.setStatus(balanceDetail.getStatus() | IsPresent.presentMaskBit.Present.getValue());
            presentProfitList.add(presentProfit);
        }
        return presentProfitList;
    }
}
