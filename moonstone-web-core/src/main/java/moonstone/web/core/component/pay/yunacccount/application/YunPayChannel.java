package moonstone.web.core.component.pay.yunacccount.application;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableBiMap;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.RequestParams;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.model.*;
import io.terminus.pay.service.PayChannel;
import io.terminus.pay.util.URLUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ServiceRuntimeException;
import moonstone.common.model.Either;
import moonstone.common.utils.*;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.order.service.WithDrawProfitApplyWriteService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxReadService;
import moonstone.web.core.component.api.WithdrawPayService;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.events.profit.WithdrawFailEvent;
import moonstone.web.core.events.profit.WithdrawSuccessEvent;
import moonstone.web.core.model.YunAccount;
import moonstone.web.core.model.YunRequestModel;
import moonstone.web.core.shop.application.ShopYunAccountManager;
import moonstone.web.core.util.OutSystemIdProviderImpl;
import moonstone.web.core.util.YunAccountEncryptUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.WillClose;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 云账户对接支付,主要是完成商户打款功能,其中包括打款订单取消
 * 该接口仅仅用于对外支付单,而非系统内部订单与支付单
 * 用于云账户对外打款,用于提现(打款业务),并非收款业务
 *
 * <AUTHOR>
 * @startuml use plantuml to active the uml graphic
 * BalanceDetailManager -> PayChannel : withdrawWithPay -> businessPay,\r bring the Withdraw pay fee into PayChannel
 * note left: carry the receiver info
 * PayChannel -> YunPaySystem: invoke payment api, \r create payment
 * YunPaySystem -> PayChannel : return the payment id
 * PayChannel -> BalanceDetailManager : payment create success
 * YunPaySystem --> PayChannel : PaySuccess, make the \r withdraw apply paid success
 * @enduml
 * @see WithdrawPayService 注意这个支付接入与其他的都不同
 */
@Slf4j
@Service
public class YunPayChannel implements PayChannel {
    /**
     * 用于注册自己
     */
    @Autowired
    private ChannelRegistry channelRegistry;

    @Autowired
    private UserWxReadService userWxReadService;
    @Autowired
    private WithdrawAccountReadService withdrawAccountReadService;
    @Autowired
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;
    @Autowired
    private WithDrawProfitApplyWriteService withDrawProfitApplyWriteService;
    @Autowired
    private BalanceDetailManager balanceDetailManager;
    @Autowired
    private ShopYunAccountManager shopYunAccountManager;
    @Autowired
    private OutSystemIdProvider outSystemIdProvider;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 回调允许的Ip地址
     */
    Set<String> allowedIpAddress = Stream.of("**************", "************", "************").collect(Collectors.toSet());
    /**
     * 获取回调地址
     */
    @Value("${pay.notifyUrl}")
    String paranaPaidNotify;
    /**
     * 默认云账户域名
     */
    @Value("${parana.payChannel.YunAccount.gateway:https://api-jiesuan.yunzhanghu.com}")
    String gateway;
    /**
     * 超时设置
     */
    @Value("${parana.payChannel.YunAccount.timeout:30}")
    Long timeout;
    /**
     * 调用接口地址
     */
    Map<String, String> paywayMap = ImmutableBiMap.of(
            "bank", "/api/payment/v1/order-realtime"
            , "alipay", "/api/payment/v1/order-alipay"
            , "wechat", "/api/payment/v1/order-wxpay"
    );
    /**
     * 取消支付订单
     */
    String cancelUrl = "/api/payment/v1/fail";
    /**
     * 查询用户是否已经接近限额
     */
    String riskQueryUrl = "/api/payment/v1/risk-check/amount";
    Map<String, String> codeMap = new HashMap<>();
    Map<String, String> apiCodeMap = new HashMap<>();
    ExecutorService executorService = Executors.newCachedThreadPool(new ParanaDefaultThreadFactory("yunPayChannel"));


    @PostConstruct
    void initData() {
        // 自动注入一下数据
        try {
            apiCodeMap = Either.ok(getClass().getResourceAsStream("/YunAccountAPICodeMap.json"))
                    .flatMap(this::readStream)
                    .map(ByteArrayOutputStream::toString)
                    .map(str -> JSON.parseObject(str, new TypeReference<Map<String, String>>() {
                    }))
                    .orElse(new HashMap<>());

            codeMap = Either.ok(getClass().getResourceAsStream("/YunAccountCodeMap.json"))
                    .flatMap(this::readStream)
                    .map(ByteArrayOutputStream::toString)
                    .map(str -> JSON.parseObject(str, new TypeReference<Map<String, String>>() {
                    }))
                    .orElse(new HashMap<>(3));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} init error,cause:{}", LogUtil.getClassMethodName(), ex.getMessage());
        } finally {
            channelRegistry.register(PayChannelsConstants.YunAccount.ALIPAY, this);
            channelRegistry.register(PayChannelsConstants.YunAccount.WECHAT, this);
            channelRegistry.register(PayChannelsConstants.YunAccount.BANK, this);
            channelRegistry.register(PayChannelsConstants.YUNACCOUNT, this);
        }
    }

    @WillClose
    @PreDestroy
    protected void close() throws Exception {
        Thread.sleep(5000L);
        if (!executorService.isShutdown()) {
            log.info("{} closing payChannel Executor Pool", LogUtil.getClassMethodName());
            executorService.shutdown();
            executorService.shutdownNow();
        }
    }

    /**
     * 将有限的inputStream读取为byteArrayOutPutStream 以便于处理
     * (3秒钟timeOut)
     *
     * @param stream inputStream必须要为有限流,若是无限流则会陷入死循环
     * @return 含有结果的数据
     */
    private Either<ByteArrayOutputStream> readStream(InputStream stream) {
        Future<Either<ByteArrayOutputStream>> timeOutWatcher = executorService.submit(() -> {
            try {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] rom = new byte[1024];
                while (true) {
                    int size = stream.read(rom);
                    if (size <= 0) {
                        break;
                    }
                    byteArrayOutputStream.write(rom, 0, size);
                }
                return Either.ok(byteArrayOutputStream);
            } catch (Exception ex) {
                log.error("{} failed to read stream,cause:{}", LogUtil.getClassMethodName(), ex.getMessage());
                return Either.error(ex);
            }
        });
        try {
            return timeOutWatcher.get(10, TimeUnit.SECONDS);
        } catch (TimeoutException ex) {
            ex.getStackTrace();
            return Either.error(ex, new Translate("流读取超时,请检查流的来源").toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @AllArgsConstructor
    @Getter
    enum YunOrderStatus {
        /**
         * 云支付
         */
        //已删除
        DELETE(-1)
        //已受理
        , ACCEPT(0)
        //已打款,
        , PAID(1)
        //打款失败
        , FAIL(2)
        // 暂停处理
        , PAUSE(4)
        // 打款中
        , PROCCESS(5)
        // 正在等待打款
        , WAIT(8)
        // 已经退款(打款失败)
        , REFUND(9)
        // 已经取消
        , CACNCEL(15);
        int status;
    }

    @Data
    static class YunAccountAPIRecord {
        /**
         * 纳税主体Id 进行分辨商户
         */
        String dealerId;
        /**
         * 提现向外部提供的交易号
         *
         * @see OutSystemIdProviderImpl#getId(long id, Date createdAt, OutSystemIdProviderImpl.type withdraw)
         */
        String withdrawTradeNo;
        /**
         * 提现号
         *
         * @see WithDrawProfitApply#getId()
         */
        Long withdrawApplyId;
        /**
         * 云账户API交互的模型
         *
         * @see YunAccountPaymentOrderModel
         */
        YunAccountPaymentOrderModel yunAccountPaymentOrderModel;
        /**
         * 请求原型
         *
         * @see YunRequestModel#encode(String, String)  需要将数据加密
         */
        YunRequestModel requestModel;
        YunAccountResponse<PayResponse> payResponse;
        /**
         * 回调原始报文
         */
        YunRequestModel callBack;
        /**
         * 回调获取解析的数据
         */
        JSONObject callBackDecode;
    }

    @Data
    static class YunAccountPaymentOrderModel {
        /**
         * 订单Id
         *
         * @see OutSystemIdProviderImpl#getId(long 提现订单号, Date 提现createdAt, OutSystemIdProviderImpl.type 提现类型)  由它生成
         */
        @JSONField(name = "order_id")
        @JsonProperty("order_id")
        String orderId;
        /**
         * 商户Id
         *
         * @see YunAccount#getDealerId()  由这里获得
         */
        @JSONField(name = "dealer_id")
        @JsonProperty("dealer_id")
        String dealerId;
        /**
         * 纳税主体
         *
         * @see YunAccount#getBrokerId()  由这里获取
         */
        @JSONField(name = "broker_id")
        @JsonProperty("broker_id")
        String brokerId;
        @JSONField(name = "real_name")
        @JsonProperty("real_name")
        String realName;// 姓名必填
        @JSONField(name = "id_card")
        @JsonProperty("id_card")
        String idCard;// 身份证必填
        @JSONField(name = "card_no")
        @JsonProperty("card_no")
        String cardNo;// 收款人支付宝帐号
        String openid;// 微信提现必填(商户appId下的)
        /**
         * 单位为元,可用
         *
         * @see BigDecimal#toString()  使用他的进行获取字符串,同时保留两位小数
         */
        String pay;
        String notes;// 选填备注
        @JSONField(name = "pay_remark")
        @JsonProperty("pay_remark")
        String payRemark;// 打款备注,最大20个字符,不运行特殊字符
        @JSONField(name = "check_name")
        @JsonProperty("check_name")
        String checkName;// 支付宝检测姓名 (Check|NoCheck)
        @JSONField(name = "notify_url")
        @JsonProperty("notify_url")
        String notifyUrl;// 回调地址 必填
        @JSONField(name = "wx_app_id")
        @JsonProperty("wx_app_id")
        String wxAppId;// 微信打款商户微信AppId(选填,仅仅在微信时需要填写)

        /**
         * fuck the damn stupid php
         */
        public void setPay(BigDecimal pay) {
            this.pay = pay.toString();
        }
    }

    @Data
    static class PayResponse {
        BigDecimal pay;//支付的金额
        String ref;//综合服务平台流水
        @JSONField(name = "order_id")
        @JsonProperty("order_id")
        String orderId;//订单号
    }

    @Data
    static class RiskCheckResult {
        @JSONField(name = "is_over_whole_user_month_quota")
        @JsonProperty("is_over_whole_user_month_quota")
        String isOverWholeUserMonthQuota;// 是否已经超过这个月的限额 true
        @JSONField(name = "is_over_whole_user_year_quota")
        @JsonProperty("is_over_whole_user_year_quota")
        String isOverWholeUserYearQuota;// 是否已经超过今年的限额 true
    }

    @Data
    static class YunAccountResponse<T> {
        /**
         * @see this#apiCodeMap 查看这个的对应的信息
         */
        String code;
        T data;// 反馈的数据
        String message;
        @JSONField(name = "request_id")
        @JsonProperty("request_id")
        String requestId; // 请求的id
    }

    /**
     * 商户支付,将调取下面的订单请求发送
     * 将数据封包进行调用下订单
     *
     * @param params 提现参数
     * @return 支付结果
     */
    @Override
    public TradeResult businessPay(BusinessPayParams params) {
        if (params == null || params.getChannel() == null || !params.getChannel().startsWith(PayChannelsConstants.YUNACCOUNT)) {
            return TradeResult.fail(TradeType.BUSINESS_PAY, PayChannelsConstants.YUNACCOUNT, new Translate("不支持的支付渠道").toString());
        }
        PaymentParams requestParam = new PaymentParams();
        BeanUtils.copyProperties(params, requestParam);
        TradeResult result = new TradeResult();
        // pack data into request
        Optional<UserWx> optionalUserWx = Optional.ofNullable(userWxReadService.findByOpenIdAndAppId(params.getOpenId(), params.getAppId()).getResult());
        optionalUserWx
                .map(UserWx::getUserId)
                .map(Objects::toString)
                .ifPresent(requestParam::setBuyerNo);
        requestParam.setContent(params.getDesc());
        requestParam.setSystemNo(params.getTradeNo());
        requestParam.setSubject(new Translate("第三方云账户提现").toString());
        requestParam.setFee(params.getAmount().longValue());
        // request it
        TradeRequest request = paymentRequest(requestParam);
        if (!request.isSuccess()) {
            result = TradeResult.fail(TradeType.BUSINESS_PAY, params.getChannel(), request.getError());
            result.setExtra(request.getError());
            result.setPaySuccess(false);
            return result;
        }
        result.setChannel(request.getResult().getChannel());
        result.setType(TradeType.BUSINESS_PAY.value());
        result.setExtra(request.getResult().getRequestOriginalUrl());
        result.setMerchantSerialNo(params.getTradeNo());
        return result;
    }

    /**
     * 关闭提现支付单
     *
     * @param params 关闭订单用的参数
     * @return 关闭结果
     */
    @Override
    public TradeResult paymentClose(PaymentCloseParams params) {
        throw new RuntimeException(new Translate("目前不允许取消").toString());
    }

    /**
     * 提现支付单请求,用于对外请求,实际上是对外支付,并非收款
     *
     * @param withdrawPayment 提现用支付单,并非系统内部支付单
     * @return 支付单申请结果
     */
    @Override
    public TradeRequest paymentRequest(PaymentParams withdrawPayment) {
        if (withdrawPayment.getFee() <= 0) {
            return TradeRequest.fail(new Translate("请输入真实有意义的提现金额").toString());
        }

        if (!withdrawPayment.getChannel().startsWith(PayChannelsConstants.YUNACCOUNT)) {
            return TradeRequest.fail(new Translate("%s 并非提现支持的渠道", withdrawPayment.getChannel()).toString());
        }
        OutSystemIdProviderImpl.OutSystemIdContainer container = outSystemIdProvider.decode(withdrawPayment.getTradeNo());
        if (!container.getType().equals(OutSystemIdProviderImpl.type.WithDrawTradeNo)) {
            return TradeRequest.fail(new Translate("%s 非正常提现单号", withdrawPayment.getTradeNo()).toString());
        }
        Long withdrawApplyId = container.getId();
        WithDrawProfitApply withDrawProfitApply = withDrawProfitApplyReadService.findById(withdrawApplyId).getResult();
        if (withDrawProfitApply == null) {
            return TradeRequest.fail(new Translate("%d 无法找到对应的提现单", withdrawApplyId).toString());
        }

        String rawResponse = "not connected";
        String rawRequestJson = "not inited";
        try {
            YunAccount yunAccount = shopYunAccountManager.getOneByShopId(withDrawProfitApply.getSourceId()).elseThrow(() -> new ServiceRuntimeException(new Translate("该提现所需商户(%d)帐号尚未注册", withDrawProfitApply.getSourceId()).toString()));
            log.debug("{} withdraw by YunAccount for apply(id:{}) account:{}", LogUtil.getClassMethodName(), withdrawApplyId, yunAccount);
            WithdrawAccount withdrawAccount = withdrawAccountReadService.findById(withDrawProfitApply.getWithdrawAccountId()).orElse(Optional.empty())
                    .orElseThrow(() -> new RuntimeException(new Translate("无法找到对应的提现帐号:%s", withDrawProfitApply.getWithdrawAccountId()).toString()));
            YunAccountPaymentOrderModel yunAccountPaymentOrderModel = new YunAccountPaymentOrderModel();
            yunAccountPaymentOrderModel.setOrderId(withdrawPayment.getTradeNo());
            yunAccountPaymentOrderModel.setBrokerId(yunAccount.getBrokerId());
            yunAccountPaymentOrderModel.setDealerId(yunAccount.getDealerId());
            yunAccountPaymentOrderModel.setNotes(withdrawPayment.getContent());
            yunAccountPaymentOrderModel.setPay(new BigDecimal(withdrawPayment.getFee()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            yunAccountPaymentOrderModel.setRealName(withdrawAccount.getName());
            if (!ObjectUtils.isEmpty(withDrawProfitApply.getExtra().get(WithdrawAccount.I_REAL_NAME))) {
                yunAccountPaymentOrderModel.setRealName(withDrawProfitApply.getExtra().get(WithdrawAccount.I_REAL_NAME));
            }
            yunAccountPaymentOrderModel.setIdCard(withdrawAccount.getExtra().get(WithdrawAccount.I_ID_NO));
            if (!ObjectUtils.isEmpty(withDrawProfitApply.getExtra().get(WithdrawAccount.I_ID_NO))) {
                yunAccountPaymentOrderModel.setIdCard(withDrawProfitApply.getExtra().get(WithdrawAccount.I_ID_NO));
            }
            if (yunAccountPaymentOrderModel.getRealName() == null || yunAccountPaymentOrderModel.getRealName().trim().isEmpty() || yunAccountPaymentOrderModel.getIdCard() == null || yunAccountPaymentOrderModel.getIdCard().trim().isEmpty()) {
                throw new RuntimeException("提现帐号必须提供开户身份证与对应姓名");
            }
            WithDrawProfitApply.WithdrawPaidType paidType = withDrawProfitApply.getPaidType();
            switch (paidType) {
                case ALIPAY:
                case SelfBank:
                case BANK: {
                    yunAccountPaymentOrderModel.setCardNo(withdrawAccount.getAccount());
                    break;
                }
                case WECHAT: {
                    yunAccountPaymentOrderModel.setOpenid(withdrawPayment.getOpenId());
                    yunAccountPaymentOrderModel.setWxAppId(withdrawPayment.getAppId());
                    break;
                }
                case OTHER:
                    throw new ServiceRuntimeException(new Translate("目前不支持该类型提现").toString());
                default:
            }
            String notifyUrl = withdrawPayment.getDomain() == null ? URLUtil.addDomain(this.paranaPaidNotify, withdrawPayment.getDomain()) : this.paranaPaidNotify;
            notifyUrl = notifyUrl + "/" + withdrawPayment.getChannel() + "/account/" + yunAccountPaymentOrderModel.getOrderId();
            yunAccountPaymentOrderModel.setNotifyUrl(notifyUrl);
            // 开始准备HTTP调用
            String requestUrl = gateway + paywayMap.get(withdrawPayment.getChannel().substring(PayChannelsConstants.YUNACCOUNT.length() + 1));
            // record it for debug
            rawRequestJson = JSON.toJSONString(yunAccountPaymentOrderModel);
            YunRequestModel requestModel = prepareRequest(yunAccount, yunAccountPaymentOrderModel);
            int retry = 0;
            while (retry < 3) {
                /*
                 * 每次申请的Id都必须不一致(使用不同的withdrawId和时间组合一下应该就是不一致的啦)
                 */
                String requestId = outSystemIdProvider.getId(withdrawApplyId, OutSystemIdProviderImpl.type.OutSideRequest);
                log.debug("{} request:{} requestId:{}", LogUtil.getClassMethodName("api-order-request"), rawRequestJson, requestId);
                HttpRequest request = HttpRequest.post(requestUrl)
                        .header("dealer-id", yunAccount.getDealerId())
                        .header("request-id", requestId)
                        .form(JSON.parseObject(JSON.toJSONString(requestModel)))
                        .connectTimeout(timeout.intValue())
                        .readTimeout(timeout.intValue());
                if (!request.ok()) {
                    retry++;
                } else {
                    YunAccountAPIRecord record = new YunAccountAPIRecord();
                    record.setDealerId(yunAccountPaymentOrderModel.getDealerId());
                    record.setWithdrawApplyId(withdrawApplyId);
                    record.setWithdrawTradeNo(yunAccountPaymentOrderModel.getOrderId());
                    record.setYunAccountPaymentOrderModel(yunAccountPaymentOrderModel);
                    record.setRequestModel(requestModel);
                    mongoTemplate.save(record);
                    // record it for debug
                    rawResponse = request.body();
                    YunAccountResponse<PayResponse> response = JSON.parseObject(rawResponse, new TypeReference<YunAccountResponse<PayResponse>>() {
                    });
                    if ("成功".equals(apiCodeMap.get(response.getCode()))) {
                        // 成功
                        if (!Objects.equals(response.getData().getOrderId(), yunAccountPaymentOrderModel.getOrderId())) {
                            log.error("{} request-order-id:{} response-order-id:{} applyId:{} accountId:{}", LogUtil.getClassMethodName("FUCK_SERIOUS_NOT_EQUAL"), yunAccountPaymentOrderModel.getOrderId(), response.getData().getOrderId(), withdrawApplyId, withdrawAccount.getId());
                            throw new ServiceRuntimeException(new Translate("不匹配的订单号(%s),请联系客服", response.getData().getOrderId()).toString());
                        }
                        if (!Objects.equals(response.getRequestId(), requestId)) {
                            log.error("{} request-order-id:{} response-order-id:{} applyId:{} accountId:{}", LogUtil.getClassMethodName("FUCK_SERIOUS_NOT_EQUAL"), yunAccountPaymentOrderModel.getOrderId(), response.getData().getOrderId(), withdrawApplyId, withdrawAccount.getId());
                            throw new ServiceRuntimeException(new Translate("不匹配的请求号(%s),请联系客服", response.getRequestId()).toString());
                        }
                        mongoTemplate.updateFirst(Query.query(Criteria.where("withdrawTradeNo").is(yunAccountPaymentOrderModel.getOrderId()))
                                , Update.update("payResponse", response)
                                , YunAccountAPIRecord.class);
                        return TradeRequest.ok(new Redirect(withdrawPayment.getChannel(), false, null, response.getData(), rawRequestJson));
                    } else {
                        log.error("{} failed to apply a payment,raw:{}\npost:{}\nresponse:{},\ncode:{} desc:{}\nmessage:{}", LogUtil.getClassMethodName(), rawRequestJson, requestModel, rawResponse, response.getCode(), apiCodeMap.get(response.getCode()), response.getMessage());
                        throw new RuntimeException(new Translate("提现支付单创建失败,%s", apiCodeMap.get(response.getMessage())).toString());
                    }
                }
            }
            throw new RuntimeException(new Translate("连接失败,服务器未响应或者互联网链接有问题").toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} rawRequestJson:{} rawResponseJson:{} withdrawApplyId:{} error:{}", LogUtil.getClassMethodName("FAIL-WITHDRAW"), rawRequestJson, rawResponse, withdrawApplyId, ex.getMessage());
            return TradeRequest.fail(new Translate("提现失败,原因:%s", ex.getMessage()).toString());
        }
    }

    /**
     * 将数据处理置入,包装成调用的结构
     *
     * @param yunAccount 云账户帐号
     * @param data       数据内容
     * @return 返回已经完成的数据
     */
    private YunRequestModel prepareRequest(YunAccount yunAccount, Object data) {
        String rawRequestJson = JSON.toJSONString(data);
        String encodeRequest = YunAccountEncryptUtil.tripleDesEncrypt(rawRequestJson, yunAccount.getKey_3Des())
                .map(ByteArrayOutputStream::toByteArray)
                .map(Base64.getEncoder()::encodeToString)
                .elseThrow(() -> new RuntimeException(new Translate("加密失败,请检查密钥").toString()));
        return new YunRequestModel(encodeRequest, yunAccount.getAppKey());
    }

    /**
     * 成功提现时调用的接口
     *
     * @param request 原始请求头
     * @return 真实到款提现结果
     */
    @Override
    public TradeResult paymentCallback(HttpServletRequest request) {
        try {
            String dealerId = request.getHeader("dealer-id");
            YunAccount yunAccount = shopYunAccountManager.getOneByDealerId(dealerId).elseThrow(() -> new RuntimeException(new Translate("无法查找到支付商户:%s", dealerId).toString()));
            String rawResponseJson = JSON.toJSONString(ServletUtil.getParamMap(request));
            YunRequestModel requestModel = JSON.parseObject(rawResponseJson, YunRequestModel.class);
            if (requestModel == null) {
                log.error("{} can't parse model,raw json:{}", LogUtil.getClassMethodName(), rawResponseJson);
                throw new RuntimeException(new Translate("读取回执数据失败").toString());
            }
            String signedShouldBe = YunRequestModel.encode(requestModel.getMess(), requestModel.getTimestamp(), requestModel.getData(), yunAccount.getAppKey());
            if (!requestModel.getSign().equals(signedShouldBe)) {
                log.error("{} signed failed,should be:{} but it is:{},A-TradeNO:{}", LogUtil.getClassMethodName("VERIFY-WITHDRAW-CALLBACK"), signedShouldBe, requestModel.getSign(), request.getAttribute(RequestParams.ACCOUNT));
            }

            byte[] encryptBytes = Base64.getDecoder().decode(requestModel.getData());
            JSONObject paidResultJsonObject = YunAccountEncryptUtil.tripleDesDecrypt(encryptBytes, yunAccount.getKey_3Des()).map(ByteArrayOutputStream::toString).map(JSON::parseObject).elseThrow(() -> new RuntimeException(new Translate("解码失败?").toString()));
            log.info("{} notify By YunAccount,notify-data:{}", LogUtil.getClassMethodName(), paidResultJsonObject.toJSONString());
            JSONObject data = paidResultJsonObject.getJSONObject("data");
            String withdrawOutSystemId = data.getString("order_id");
            OutSystemIdProviderImpl.OutSystemIdContainer idContainer = outSystemIdProvider.decode(withdrawOutSystemId);
            log.debug("{} decode outSystemId:{}", LogUtil.getClassMethodName(), idContainer.toString());
            Long withdrawApplyId = idContainer.getId();
            String paidStatus = data.getString("status");
            WithdrawFailEvent withdrawFailEvent = new WithdrawFailEvent();
            WithdrawSuccessEvent withdrawSuccessEvent = new WithdrawSuccessEvent();
            WithDrawProfitApply apply = withDrawProfitApplyReadService.findById(withdrawApplyId).getResult();
            if (apply.isPaid() || apply.isError() || apply.isReject() || apply.isClosed() || !apply.isWaiting()) {
                log.debug("{} apply-now:{} need to stop the response", LogUtil.getClassMethodName(), JSON.toJSONString(apply));
                TradeResult result = new TradeResult();
                result.setType(TradeType.BUSINESS_PAY.value());
                result.setCallbackResponse("SUCCESS".toLowerCase());
                result.setGatewaySerialNo(data.getString("ref"));
                result.setMerchantSerialNo(data.getString("order_id"));
                result.setChannel(Optional.ofNullable(request.getAttribute("channel")).map(Object::toString).orElse(PayChannelsConstants.YUNACCOUNT));
                // 并非真实系统内支付单 目前先隔离
                result.setPaySuccess(false);
                return result;
            }
            WithdrawAccount account = withdrawAccountReadService.findById(apply.getWithdrawAccountId()).orElse(Optional.empty()).orElseThrow(() -> Translate.exceptionOf("查询提现账户失败"));
            WithDrawProfitApply.WithdrawPaidType paidType = WithDrawProfitApply.WithdrawPaidType.from(account.getType()).orElse(WithDrawProfitApply.WithdrawPaidType.OTHER);
            switch (paidStatus) {
                case "1": {
                    //打款成功
                    if (!apply.bePaid(paidType, data.getString("broker_bank_bill"))) {
                        log.error("{} applyId:{} broker_bank_bill:{}", LogUtil.getClassMethodName("设置提现支付信息失败"), apply.getId(), data.get("broker_bank_bill"));
                    }
                    apply.getExtra().put("tax", data.getString("tax"));
                    apply.getExtra().put("sysFee", data.getString("sys_fee"));
                    apply.getExtra().put("userFee", data.getString("user_fee"));
                    withdrawSuccessEvent.setTime(apply.getCreatedAt());
                    withdrawSuccessEvent.setPaidAt(data.getDate("ﬁnished_time"));
                    withdrawSuccessEvent.setApplyId(withdrawApplyId);
                    withdrawSuccessEvent.setProfit(new BigDecimal(data.getString("pay")));
                    withdrawSuccessEvent.setShopId(apply.getSourceId());
                    withdrawSuccessEvent.setUserId(apply.getUserId());
                    withdrawSuccessEvent.setAccount(account.getAccount());
                    withdrawSuccessEvent.setChannel(new Translate("云账户[%s]", paidType.getName()).toString());
                    EventSender.sendApplicationEvent(withdrawSuccessEvent);
                    break;
                }
                case "4": {
                    // pause paid
                    log.info("{} withdraw:{} is paused ref:{}", LogUtil.getClassMethodName(), withdrawApplyId, data.getString("ref"));
                    break;
                }
                case "2":
                    // paid failed
                case "9":
                    // paid failed and refund
                case "15":
                    // cancel paid
                default: {
                    apply.setStatus(apply.getStatus() | WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit());
                    apply.getExtra().put("error", data.getString("status_detail_message"));
                    withdrawFailEvent.setReason(data.getString("status_detail_message"));
                    withdrawFailEvent.setProfit(new BigDecimal(data.getString("pay")));
                    withdrawFailEvent.setAccount(account.getAccount());
                    withdrawFailEvent.setTime(apply.getCreatedAt());
                    withdrawFailEvent.setUserId(apply.getUserId());
                    withdrawFailEvent.setShopId(apply.getSourceId());
                    withdrawFailEvent.setApplyId(withdrawApplyId);
                    apply.initAuth();
                    balanceDetailManager.rejectWithDraw(0L, apply, withdrawFailEvent.getReason());
                }
            }
            if (apply.isPaid()) {
                if (!withDrawProfitApplyWriteService.update(apply).isSuccess()) {
                    return TradeResult.fail(TradeType.BUSINESS_PAY, PayChannelsConstants.YUNACCOUNT, new Translate("数据库更新失败").toString());
                }
            }
            mongoTemplate.updateFirst(Query.query(Criteria.where("withdrawTradeNo").is(withdrawOutSystemId))
                    , Update.update("callBack", requestModel).set("callBackDecode", paidResultJsonObject)
                    , YunAccountAPIRecord.class);
            TradeResult result = new TradeResult();
            result.setType(TradeType.BUSINESS_PAY.value());
            result.setCallbackResponse("SUCCESS".toLowerCase());
            result.setGatewaySerialNo(data.getString("ref"));
            result.setMerchantSerialNo(data.getString("order_id"));
            result.setChannel(Optional.ofNullable(request.getAttribute("channel")).map(Object::toString).orElse(PayChannelsConstants.YUNACCOUNT));
            // 并非真实系统内支付单 目前先隔离
            result.setPaySuccess(false);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} failed to deal with callback,message:{} throw:", LogUtil.getClassMethodName(), ex.getMessage(), ex);
            return TradeResult.fail(TradeType.BUSINESS_PAY, PayChannelsConstants.YUNACCOUNT, ex.getMessage());
        }
    }


    /**
     * 退款 目前不支持
     *
     * @param refund 不支持哦
     * @return 操作结果
     */
    @Deprecated
    @Override
    public TradeRequest refundRequest(RefundParams refund) {
        throw new ServiceRuntimeException(new Translate("目前不支持退款功能").toString());
    }

    /**
     * 目前不支持退款
     *
     * @param request 不支持哦
     * @return 不支持哦
     */
    @Deprecated
    @Override
    public TradeResult refundCallback(HttpServletRequest request) {
        throw new ServiceRuntimeException(new Translate("目前不支持退款功能").toString());
    }
}
