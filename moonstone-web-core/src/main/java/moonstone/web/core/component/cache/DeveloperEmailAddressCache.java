package moonstone.web.core.component.cache;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.enums.EmailBusinessTypeEnum;
import moonstone.user.model.Email;
import moonstone.user.service.EmailReadService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DeveloperEmailAddressCache {

    @Resource
    private EmailReadService emailReadService;

    private static List<Email> DEVELOPER_EMAILS = null;

    /**
     * 获取所有开发人员的邮箱地址，json格式
     *
     * @return
     */
    public String getAllInJson() {
        if (CollectionUtils.isEmpty(DEVELOPER_EMAILS)) {
            DEVELOPER_EMAILS = emailReadService.findEmailsByShopId(0L,
                    EmailBusinessTypeEnum.SYSTEM_ERROR_REMIND).getResult();
        }
        if (CollectionUtils.isEmpty(DEVELOPER_EMAILS)) {
            return null;
        }

        return JSON.toJSONString(DEVELOPER_EMAILS.stream().map(Email::getEmail).collect(Collectors.toList()));
    }

    /**
     * 获取所有开发人员的邮箱地址
     *
     * @return
     */
    public List<String> getAll() {
        if (CollectionUtils.isEmpty(DEVELOPER_EMAILS)) {
            DEVELOPER_EMAILS = emailReadService.findEmailsByShopId(0L,
                    EmailBusinessTypeEnum.SYSTEM_ERROR_REMIND).getResult();
        }
        if (CollectionUtils.isEmpty(DEVELOPER_EMAILS)) {
            return null;
        }

        return DEVELOPER_EMAILS.stream().map(Email::getEmail).collect(Collectors.toList());
    }

    @Scheduled(cron = "0 0 0 * * ?")
    public void refresh() {
        DEVELOPER_EMAILS = emailReadService.findEmailsByShopId(0L,
                EmailBusinessTypeEnum.SYSTEM_ERROR_REMIND).getResult();
    }
}
