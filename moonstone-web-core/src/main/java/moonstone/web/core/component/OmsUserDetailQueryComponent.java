package moonstone.web.core.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OmsUserDetailQueryComponent {
    @Autowired
    private OmsApiGate omsApiGate;

    /**
     * 查询OMS用户详情
     *
     * @param userId 用户Id
     * @return Oms用户详情
     */
    public Either<OmsUserDetail> queryUserDetail(Long userId) {
        HttpRequest queryUserDetail = HttpRequest.get(omsApiGate.getApiGate() + OmsApiGate.Api.userDetail.getUrl() + "?userId=" + userId);
        if (queryUserDetail.ok()) {
            JSONObject result = JSONObject.parseObject(queryUserDetail.body());
            if (result.getBoolean(OmsApiGate.ResultIndex.success.name())) {
                JSONObject user = result.getJSONObject(OmsApiGate.ResultIndex.result.name());
                return Either.ok(JSON.parseObject(JSON.toJSONString(user), OmsUserDetail.class));
            }
            return Either.error(Translate.of("错误的请求 [%s]", result.getString(OmsApiGate.ResultIndex.errorMessage.name())));

        }
        return Either.error(Translate.of("Http request error for user[%s]", userId));
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class OmsUserDetail {
        Long userId;
        String userName;
        String mobile;
    }
}
