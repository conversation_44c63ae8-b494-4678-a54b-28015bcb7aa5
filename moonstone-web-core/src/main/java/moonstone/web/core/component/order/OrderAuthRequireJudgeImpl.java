package moonstone.web.core.component.order;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.cache.ThirdPartyCacheHolder;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.LogUtil;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Sku;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderAuthRequireJudge;
import moonstone.order.service.OrderReadService;
import moonstone.shop.model.Shop;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.model.SkuOrderAuthRequireJudgeRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class OrderAuthRequireJudgeImpl implements OrderAuthRequireJudge {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired(required = false)
    private JedisPool jedisPool;

    @Autowired
    private ShopCacheHolder shopCacheHolder;

    @Autowired
    private SkuCacheHolder skuCacheHolder;

    @Autowired
    private ItemCacheHolder itemCacheHolder;

    @Autowired
    private OrderReadService orderReadService;

    @Autowired
    private ThirdPartyCacheHolder thirdPartyCacheHolder;

    @Autowired
    private UserReadService<User> userReadService;

    LoadingCache<String, Predicate<SkuOrder>> requireAuthPredicateCache = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build(new CacheLoader<String, Predicate<SkuOrder>>() {
                @Override
                public Predicate<SkuOrder> load(@Nullable String key) {
                    // 如果出现错误 则全部抛出需要审核 避免更严重的错误
                    Optional<SkuOrderAuthRequireJudgeRule> requireJudgeRule = Optional.ofNullable(JSON.parseObject(key, SkuOrderAuthRequireJudgeRule.class));
                    /*
                    检查条件由三个 索引导入
                        shopId  shopId-itemId   shopId-itemId-skuId
                     当导入的条件中不带有额外检查条件 只包含shopId itemId skuId 三种类型时,直接返回requireAuth 进行判断
                     当包含额外条件时,如果符合额外条件 则返回requireAuth 不然返回 requireAuth的相反值
                     */
                    return skuOrder -> {
                        try {
                            if (!requireJudgeRule.isPresent()) return false;

                            log.debug("{} judge skuOrder[shopId=>{},buyerId=>{},skuId=>{}] with rule [{}]", LogUtil.getClassMethodName(), skuOrder.getShopId(), skuOrder.getBuyerId(), skuOrder.getSkuId(), JSON.toJSONString(requireJudgeRule));
                            SkuOrderAuthRequireJudgeRule rule = requireJudgeRule.get();
                            boolean requireAuth = rule.isRequireAuth();

                            Set<Long> userIdSet = rule.getUserIdSet();
                            Set<Long> userIdExcludeSet = rule.getUserIdExcludeSet();
                            Long buyLimit = rule.getUserBoughtAbove();
                            String itemCode = rule.getItemCode();
                            Long priceBelowLimit = rule.getPriceBelow();
                            String mobileLike = rule.getMobileLike();
                            Integer thirdPartySkuStockBelowLimit = rule.getThirdPartySkuStockBelow();

                            if (Stream.of(userIdSet, userIdExcludeSet, buyLimit, itemCode, priceBelowLimit, mobileLike, thirdPartySkuStockBelowLimit).anyMatch(Objects::nonNull)) {
                                if (userIdSet != null && userIdSet.contains(skuOrder.getBuyerId())) return requireAuth;
                                if (userIdExcludeSet != null && !userIdExcludeSet.contains(skuOrder.getBuyerId()))
                                    return !requireAuth;
                                if (buyLimit != null) {
                                    OrderCriteria orderCriteria = new OrderCriteria();
                                    orderCriteria.setBuyerId(skuOrder.getBuyerId());
                                    orderCriteria.setShopId(skuOrder.getShopId());
                                    orderCriteria.setStatus(Arrays.asList(1, 2, 3));
                                    boolean triggerTheLimit = orderReadService.querySkuOrderCount(orderCriteria).orElse(new ArrayList<>())
                                            .stream().filter(count -> Objects.equals(count.getSkuId(), skuOrder.getSkuId()))
                                            .findFirst().filter(count -> count.getNum() >= buyLimit).isPresent();
                                    if (triggerTheLimit)
                                        return requireAuth;
                                }
                                if (thirdPartySkuStockBelowLimit != null && Objects.equals(skuOrder.getIsThirdPartyItem(), 1) && skuOrder.getOuterSkuId() != null) {
                                    ThirdPartySystem thirdPartySystem = ThirdPartySystem.fromInt(Integer.parseInt(skuOrder.getTags().get(SkuTagIndex.pushSystem.name()).split(",")[0]));
                                    if (thirdPartyCacheHolder.getStock(skuOrder.getShopId(), thirdPartySystem, skuOrder.getOuterSkuId()).stream().map(ThirdPartySkuStock::getAuthenticStock).reduce(Integer::sum).orElse(0) <= thirdPartySkuStockBelowLimit)
                                        return requireAuth;
                                }
                                if (itemCode != null && Objects.equals(itemCode, itemCacheHolder.findItemById(skuOrder.getItemId()).getItemCode()))
                                    return requireAuth;
                                if (priceBelowLimit != null && priceBelowLimit < skuOrder.getFee())
                                    return requireAuth;
                                if (mobileLike != null && Optional.ofNullable(userReadService.findById(skuOrder.getBuyerId()).getResult()).map(User::getMobile).orElse("").matches(mobileLike))
                                    return requireAuth;
                                return !requireAuth;
                            }
                            return requireAuth;
                        } catch (Exception ex) {
                            log.error("{} fail to judge skuOrder[shopId=>{},buyerId=>{},skuId=>{}] so return require auth", LogUtil.getClassMethodName(), skuOrder.getShopId(), skuOrder.getBuyerId(), skuOrder.getSkuId(), ex);
                            return true;
                        }
                    };
                }
            });


    /**
     * 是否允许跳过审核
     *
     * @param skuOrder 子订单
     */
    @Override
    public boolean allow(SkuOrder skuOrder) {
        Shop shop = shopCacheHolder.findShopById(skuOrder.getShopId());
        Map<String, String> shopExtra = shop.getExtra();
        Sku sku = skuCacheHolder.findSkuById(skuOrder.getSkuId());
        boolean shopNeedAuth = shopExtra != null && Objects.equals("true", shopExtra.get(ParanaConstants.SHOP_OPEN_ORDER_AUTH));
        boolean skuNeedAuth = sku.getExtra() != null && sku.getExtra().getOrDefault(SkuExtraIndex.NeedAuth.getCode(), "false").equals("true");

        return !(shopNeedAuth || skuNeedAuth || specialRuleJudge(skuOrder).test(skuOrder));
    }

    private Predicate<SkuOrder> specialRuleJudge(SkuOrder skuOrder) {
        List<Predicate<SkuOrder>> predicateList =
                getStrFromCacheOrDB(skuOrder).stream().map(requireAuthPredicateCache::get).collect(Collectors.toList());
        return predicateList.stream().reduce(any -> false, Predicate::or);
    }

    private @NotNull
    List<String> getStrFromCacheOrDB(SkuOrder skuOrder) {
        try (Jedis jedis = jedisPool.getResource()) {
            Function<Long, Function<Long, Function<Long, List<String>>>> readCache = shopId -> itemId -> skuId -> {
                String index = String.format("[AuthRule]-%s-%s-%s", shopId, itemId, skuId);
                String value = jedis.get(index);
                jedis.expire(index, 5);
                if (value == null) {
                    List<String> ruleStrList = readRuleStrFromDB(shopId, itemId, skuId);
                    StringBuilder builder = new StringBuilder();
                    ruleStrList.forEach(str -> {
                        builder.append(Base64.getEncoder().encodeToString(str.getBytes()));
                        builder.append("\n");
                    });
                    jedis.setnx(index, builder.toString());
                    jedis.expire(index, 60 * 15);
                    return ruleStrList;
                }
                return Arrays.stream(value.split("\n")).map(String::getBytes).map(Base64.getDecoder()::decode).map(String::new).collect(Collectors.toList());
            };
            List<String> ruleStrList = (readCache.apply(skuOrder.getShopId()).apply(null).apply(null));
            ruleStrList.addAll(readCache.apply(skuOrder.getShopId()).apply(skuOrder.getItemId()).apply(null));
            ruleStrList.addAll(readCache.apply(skuOrder.getShopId()).apply(skuOrder.getItemId()).apply(skuOrder.getSkuId()));
            return ruleStrList.stream().distinct().collect(Collectors.toList());
        } catch (Exception ex) {
            log.error("{} fail to query the cache for order[shopId=>{},buyerId=>{},skuId=>{}]", LogUtil.getClassMethodName(), skuOrder.getShopId(), skuOrder.getBuyerId(), skuOrder.getSkuId(), ex);
            List<String> ruleStrList = readRuleStrFromDB(skuOrder.getShopId(), null, null);
            ruleStrList.addAll(readRuleStrFromDB(skuOrder.getShopId(), skuOrder.getItemId(), null));
            ruleStrList.addAll(readRuleStrFromDB(skuOrder.getShopId(), skuOrder.getItemId(), skuOrder.getSkuId()));
            return ruleStrList;
        }
    }

    private List<String> readRuleStrFromDB(Long shopId, Long itemId, Long skuId) {
        List<String> ruleStrList = new ArrayList<>();
        Query query = Query.query(Criteria.where("shopId").is(shopId));
        mongoTemplate.find(query.addCriteria(Criteria.where("itemId").is(null)).addCriteria(Criteria.where("skuId").is(null)), SkuOrderAuthRequireJudgeRule.class).stream().map(JSON::toJSONString).forEach(ruleStrList::add);
        query = Query.query(Criteria.where("shopId").is(shopId));
        query.addCriteria(Criteria.where("itemId").is(itemId)).addCriteria(Criteria.where("skuId").is(null));
        mongoTemplate.find(query, SkuOrderAuthRequireJudgeRule.class).stream().map(JSON::toJSONString).forEach(ruleStrList::add);
        query = Query.query(Criteria.where("shopId").is(shopId));
        query.addCriteria(Criteria.where("itemId").is(itemId));
        query.addCriteria(Criteria.where("skuId").is(skuId));
        mongoTemplate.find(query, SkuOrderAuthRequireJudgeRule.class).stream().map(JSON::toJSONString).forEach(ruleStrList::add);
        return ruleStrList.stream().distinct().collect(Collectors.toList());
    }
}
