package moonstone.web.core.component.user;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.utils.UserUtil;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxReadService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Slf4j
@Component
public class UserAppInfoComponent {

    @Resource
    private UserWxReadService userWxReadService;

    @Resource
    private ShopWxaReadService shopWxaReadService;

    public String findAppId(String shopId, String openId, Long userId) {
        var appType = UserUtil.getCurrentAppType();

        return userWxReadService.findByOpenId(openId)
                .getResult()
                .stream()
                .filter(wx -> wx.getUserId().equals(userId))
                .filter(wx -> appType == null || appType.getCode().equals(wx.getAppType()))
                .findFirst()
                .map(UserWx::getAppId)
                .orElse(findReleaseOneForShopId(Long.parseLong(shopId), appType));
    }

    private String findReleaseOneForShopId(Long shopId, AppTypeEnum appType) {
        var list = shopWxaReadService.findByShopId(shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.stream()
                .filter(entity -> entity.getStatus() >= ShopWxaStatus.NORMAL.getValue())
                .filter(entity -> appType == null || appType.getCode().equals(entity.getAppType()))
                .findFirst()
                .map(ShopWxa::getAppId)
                .orElse(null);
    }
}
