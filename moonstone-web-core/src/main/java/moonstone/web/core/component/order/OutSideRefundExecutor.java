package moonstone.web.core.component.order;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.exception.ApiException;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.OutSideRefundRealExecutor;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class OutSideRefundExecutor{
    private final Map<ThirdPartySystem, OutSideRefundRealExecutor> executorMap = new ConcurrentHashMap<>();

    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired(required = false)
    private List<OutSideRefundRealExecutor> refundRealExecutors;

    @PostConstruct
    public void init() {
        if (refundRealExecutors != null) {
            refundRealExecutors.forEach(this::register);
        }
    }


    /**
     * 向这个外部订单退款处理者注册自己
     *
     * @param executor 自己啦
     */
    public void register(OutSideRefundRealExecutor executor) {
        executorMap.put(executor.getThirdPartySystem(), executor);
    }

    /**
     * 执行外部退款,仅仅进行外部IO操作
     *
     * @param shopOrder 被退款的单子
     */
    public Either<Boolean> executeRefund_OM(ShopOrder shopOrder) {
        // 1. 首先获取skuOrder列表信息
        Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId());
        if (!rSkuOrders.isSuccess()) {
            log.error("{} databaseReadError shopOrderId:{}", LogUtil.getClassMethodName(), shopOrder.getId());
            return Either.error(new Translate("试图退款失败,请稍后再试").toString());
        }
        List<SkuOrder> skuOrders = rSkuOrders.getResult();

        List<Either<Boolean>> result;
        try {
            result = getThirdPartySystem(rSkuOrders.getResult()).stream()
                    .map(executorMap::get)
                    .map(executor -> executor.executeRefund(shopOrder, skuOrders))
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            log.error("{} refund shopOrderId:{} thirdPartySystem failed", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
            throw new ApiException(ex.getMessage());
        }
        // 如果没有第三方则是退款成功啦
        if (result.isEmpty()) {
            return Either.ok(true);
        }
        if (result.stream().allMatch(Either::isSuccess)) {
            return Either.ok(true);
        } else if (result.stream().map(Either::isSuccess).allMatch(Predicate.isEqual(false))) {
            return Either.error(result.get(0).getErrorMsg());
        }
        log.error("{} shopOrderId:{} half-refund-failed", LogUtil.getClassMethodName(), shopOrder.getId());
        return Either.error(new Translate("一部分商品退款成功,请联系客服进行详细操作").toString());
    }

    /**
     * 获取第三方信息以进行第三方退款
     * 依赖如下
     * {@link moonstone.order.component.DefaultRichOrderMaker skuOrder制作流程}
     * {@link moonstone.item.model.Sku 的tags {"pushSystem":"ThirdPartySystemId"}}
     * {@link ThirdPartySystem id}
     * {@link moonstone.order.api.AbstractPersistedOrderMaker make的流程}
     *
     * @param skuOrders 单品订单数据
     * @return 订单的三方系统集合
     */
    private List<ThirdPartySystem> getThirdPartySystem(List<SkuOrder> skuOrders) {
        // 故意不检测数据合法性以使得错误抛出 [依赖业务本身流程]
        return skuOrders
                .stream()
                // 筛选第三方商品
                .filter(skuOrder -> Objects.equals(1, skuOrder.getIsThirdPartyItem()))
                // 仅仅对已经推送完毕的进行推送
                .filter(skuOrder -> Objects.equals(skuOrder.getPushStatus(), SkuOrderPushStatus.FINISHED.value()))
                // 获取 tags tags::{"pushSystem":"thirdPartySystem.id"}
                .map(SkuOrder::getTags)
                .map(tagsMap -> tagsMap.get("pushSystem"))
                .map(str -> Arrays.stream(str.split(",")).filter(systemStr -> !ThirdPartySystem.GongXiao.Id().toString().equals(systemStr)).findFirst().orElse("1"))
                // 转换 如果失败则表示其出现错误了
                .map(Integer::valueOf)
                .map(ThirdPartySystem::fromInt)
                .collect(Collectors.toList());
    }

    /* 创建保存SkuOrder推送信息的函数 */
    private final Function<SkuOrder, SkuOrder> persistSkuOrderPushStatusGenerate = skuOrder -> {
        SkuOrder update = new SkuOrder();
        update.setId(skuOrder.getId());
        update.setPushStatus(update.getPushStatus());
        return update;
    };

    /* 创建停止SkuOrder推送信息的函数 */
    private final Function<SkuOrder, SkuOrder> stopSkuOrderPushStatusGenerate = skuOrder -> {
        SkuOrder update = new SkuOrder();
        update.setId(skuOrder.getId());
        update.setPushStatus(SkuOrderPushStatus.NOT_NEED.value());
        return update;
    };

    /// 测试stream 内部抛出异常
    public static void main(String[] args) {
        Function<Integer, Integer> div = i -> 5 / i;
        try {
            Stream.of(1, 2, 3, 4, 0, 9, 8, 6).map(div).forEach(System.out::println);
        } catch (Exception ex) {
            log.error("{} catch u", LogUtil.getClassMethodName());
        }
    }
}
