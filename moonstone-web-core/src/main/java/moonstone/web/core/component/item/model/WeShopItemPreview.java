package moonstone.web.core.component.item.model;

import lombok.Data;

import java.util.List;

@Data
public class WeShopItemPreview {

    // 商品id
    Long itemId;

    // 商品名称
    String itemName;

    // 商品图片显示
    String url;

    // 来源国际
    String nation;

    // 国家图片url
    String nationUrl;

    // 商品类型 [保税,完税]
    String type;

    // 商品是否保税
    Integer isBonded;

    // 最低价
    Long lowPrice;

    // 最高价
    Long highPrice;

    // 规格,[罐,箱]
    String unit;

    //  建议零售价
    Long suggestPrice;

    // 原市场价格
    Long originPrice;

    // 税金承担方
    Integer customTaxHolder;

    // 详细改价内容
    List<WeShopSkuPreview> weShopSkuPreviewList;
}
