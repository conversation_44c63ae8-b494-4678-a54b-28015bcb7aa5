package moonstone.web.core.component.profit.api;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.order.dto.WithDrawProfitApplyCriteria;
import moonstone.web.core.component.profit.view.AppWithdrawApplyView;

public interface AppWithdrawReadService {
    /**
     * 分页查询体现记录
     *
     * @param criteria 聚合条件
     * @return 结果
     */
    Response<Paging<AppWithdrawApplyView>> paging(WithDrawProfitApplyCriteria criteria);
}
