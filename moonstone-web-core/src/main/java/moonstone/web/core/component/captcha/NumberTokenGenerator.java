package moonstone.web.core.component.captcha;

import com.github.cage.IGenerator;

import java.util.Random;

public class NumberTokenGenerator implements IGenerator<String> {

    private final Integer length;
    private final Random rnd;

    public NumberTokenGenerator(int length){
        this.length = length;
        this.rnd = new Random();
    }

    public NumberTokenGenerator(int length, Random rnd) {
        this.length = length;
        this.rnd = rnd;
    }

    @Override
    public String next() {
        char[] token = new char[length];
        for (int i = 0; i < length; i++) {
            token[i] = Character.forDigit(rnd.nextInt(10), 10);
        }
        return new String(token);
    }
}
