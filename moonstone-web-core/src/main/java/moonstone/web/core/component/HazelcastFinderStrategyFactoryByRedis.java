package moonstone.web.core.component;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.ImmutableMap;
import com.hazelcast.cluster.Address;
import com.hazelcast.config.properties.PropertyDefinition;
import com.hazelcast.logging.ILogger;
import com.hazelcast.spi.discovery.DiscoveryNode;
import com.hazelcast.spi.discovery.DiscoveryStrategy;
import com.hazelcast.spi.discovery.DiscoveryStrategyFactory;
import com.hazelcast.spi.partitiongroup.MemberGroup;
import com.hazelcast.spi.partitiongroup.PartitionGroupStrategy;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.events.HazelcastHeartBeatByRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class HazelcastFinderStrategyFactoryByRedis implements DiscoveryStrategyFactory {
    Cache<String, Integer> lruSet = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();
    @Autowired
    private JedisPool jedisPool;

    @Override
    public Class<? extends DiscoveryStrategy> getDiscoveryStrategyType() {
        return DiscoveryByRedisStrategy.class;
    }

    @EventListener(HazelcastHeartBeatByRedis.class)
    public void findOne(HazelcastHeartBeatByRedis heartBeat) {
        lruSet.put(heartBeat.getAddress(), heartBeat.getPort());
    }

    private List<DiscoveryNode> buildNode() {
        try {
            Thread.sleep(2000);
        } catch (Exception ignored) {

        }
        List<DiscoveryNode> discoveryNodeList = new ArrayList<>();
        lruSet.asMap().forEach((host, port) -> {
            try {
                Address address = new Address(host, port);
                discoveryNodeList.add(new DiscoveryNode() {
                    @Override
                    public Address getPrivateAddress() {
                        return address;
                    }

                    @Override
                    public Address getPublicAddress() {
                        return getPrivateAddress();
                    }

                    @Override
                    public Map<String, Object> getProperties() {
                        return ImmutableMap.of("strategy", "redis");
                    }
                });

            } catch (Exception ex) {
                log.error("{} fail to discovery nodes", LogUtil.getClassMethodName(), ex);
            }
        });
        return discoveryNodeList;
    }

    @Override
    public DiscoveryStrategy newDiscoveryStrategy(DiscoveryNode discoveryNode, ILogger logger, Map<String, Comparable> properties) {
        return new DiscoveryByRedisStrategy(jedisPool, discoveryNode, logger, properties, this::buildNode);
    }

    @Override
    public Collection<PropertyDefinition> getConfigurationProperties() {
        return null;
    }

    /**
     * custom it as you need
     */
    @SuppressWarnings("rawtypes")
    static class DiscoveryByRedisStrategy implements DiscoveryStrategy {
        JedisPool jedisPool;
        DiscoveryNode discoveryNode;
        ILogger iLogger;
        Map<String, Comparable> properties;
        Supplier<Iterable<DiscoveryNode>> discoverNodes;

        public DiscoveryByRedisStrategy(JedisPool jedisPool, DiscoveryNode discoveryNode, ILogger iLogger, Map<String, Comparable> properties, Supplier<Iterable<DiscoveryNode>> discoverNodes) {
            this.jedisPool = jedisPool;
            this.discoveryNode = discoveryNode;
            this.iLogger = iLogger;
            this.properties = properties;
            this.discoverNodes = discoverNodes;
        }

        @Override
        public void start() {

        }

        @Override
        public Iterable<DiscoveryNode> discoverNodes() {
            return discoverNodes.get();
        }

        @Override
        public void destroy() {

        }

        @Override
        public PartitionGroupStrategy getPartitionGroupStrategy() {
            return () -> (Iterable<MemberGroup>) Collections::emptyIterator;
        }

        @Override
        public Map<String, String> discoverLocalMetadata() {
            return new HashMap<>();
        }
    }

}
