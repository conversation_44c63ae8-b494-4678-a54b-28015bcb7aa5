package moonstone.web.core.component;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.model.Either;
import moonstone.common.model.EntityBase;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.SubProxySum;
import moonstone.order.service.SubProxyDataWriteService;
import moonstone.shop.model.Shop;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.StoreProxyWriteService;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.util.StoreProxyAuthProxy;
import moonstone.web.core.util.StoreProxyJXAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class StoreProxyRegisterComponent {
    @Autowired
    private StoreProxyWriteService storeProxyWriteService;
    @Autowired
    private StoreProxyReadService storeProxyReadService;
    @Autowired
    private SubProxyDataWriteService subProxyDataWriteService;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private UserProfileReadService userProfileReadService;

    protected final LoadingCache<Long, Optional<StoreProxy>> proxyCachedById = Caffeine.newBuilder().expireAfterWrite(3L, TimeUnit.MINUTES)
            .build(key -> storeProxyReadService.findById(key).take());
    // 代理的店铺Id缓存
    /**
     * Proxy ShopId Cache for UserId
     */
    protected final LoadingCache<Long, List<Long>> proxyShopIdCacheByUserId = Caffeine.newBuilder().expireAfterWrite(3L, TimeUnit.MINUTES)
            .build(userId -> storeProxyReadService.findByUserIdAndLevel(userId, null).orElseGet(ArrayList::new).stream().map(StoreProxy::getShopId).collect(Collectors.toList()));
    // 用于拒绝用户连续注册
    /**
     * reject the user reduplicate reg (old version, maybe rewrite at hazelcast latter)
     */
    private final Cache<UserIdAndShopId, Boolean> lastHaveTry = Caffeine.newBuilder().expireAfterWrite(1L, TimeUnit.SECONDS).build();
    // 用户缓存
    /**
     * StoreProxy cache
     */
    private final LoadingCache<UserIdAndShopId, Long> storeProxyCache = Caffeine.newBuilder().expireAfterAccess(3L, TimeUnit.MINUTES)
            .build(key -> {
                        Optional<StoreProxy> storeProxyOpt = storeProxyReadService.findByShopIdAndUserId(key.getShopId(), key.getUserId()).orElse(Optional.empty());
                return storeProxyOpt.map(EntityBase::getId).orElse(null);
                    }
            );

    public List<Long> getProxyShopIdFromUserId(Long userId) {
        return proxyShopIdCacheByUserId.get(userId);
    }

    public LoadingCache<Long, Optional<StoreProxy>> getProxyCachedById() {
        return proxyCachedById;
    }

    public Optional<StoreProxy> getStoreProxyByShopIdAndUserId(long shopId, long userId) {
        //noinspection ConstantConditions
        return Optional.ofNullable(storeProxyCache.get(new UserIdAndShopId(userId, shopId))).flatMap(proxyCachedById::get);
    }

    private void addStoreProxyData(Long storeProxyId) {
        if (storeProxyId == null) {
            log.error("{}", LogUtil.getClassMethodName("null-store-ProxyId"));
            return;
        }
        storeProxyReadService.findById(storeProxyId)
                .logErrorStr(errorStr -> log.error("{} for storeProxyId:{} cause:{}", LogUtil.getClassMethodName("add-statics-data"), storeProxyId, errorStr))
                .ifSuccess(o -> o.ifPresent(this::addStoreProxyData));
    }

    /**
     * 添加代理数据
     */
    private void addStoreProxyData(StoreProxy storeProxy) {
        SubProxySum subProxySum = new SubProxySum();
        subProxySum.setShopId(storeProxy.getShopId());
        subProxySum.setUserId(storeProxy.getUserId());
        subProxySum.setSupperProxyId(storeProxy.getSupperId());
        subProxySum.setTradeFeeSum(0L);
        subProxySum.setOrderNum(0L);
        subProxySum.setIncomeSum(0L);
        subProxySum.setFanNum(0L);
        val rCreate = subProxyDataWriteService.create(subProxySum);
        if (!rCreate.isSuccess()) {
            log.error("{} error init proxy-data:{}", LogUtil.getClassMethodName("init-proxy-statics-data"), storeProxy);
        }
    }

    /**
     * 判断最近是不是刚刚点击过,用于拒绝重复点击注册
     *
     * @param shopId 店铺Id
     * @param userId 用户Id
     * @return 是否允许注册
     */
    private boolean canWeRegNow(Long shopId, Long userId) {
        synchronized (lastHaveTry) {
            lastHaveTry.cleanUp();
            boolean hasTry = lastHaveTry.getIfPresent(new UserIdAndShopId(userId, shopId)) == null;
            if (hasTry) {
                lastHaveTry.put(new UserIdAndShopId(userId, shopId), false);
                System.out.println("OK");
            }
            return hasTry;
        }
    }

    /**
     * 注册经销商
     *
     * @param shop        店铺
     * @param applier     申请人
     * @param storeProxys 详情
     * @return 成功与Id
     */
    public Either<Long> registerStoreProxyLevelOne(Shop shop, User applier, Map<String, String> storeProxys) {
        if (!canWeRegNow(shop.getId(), applier.getId())) {
            return Either.error(new Translate("点击太过频繁请稍后再试").toString());
        }
        Optional<StoreProxy> storeProxyOpt = getStoreProxyByShopIdAndUserId(shop.getId(), applier.getId());
        try {
            if (storeProxyOpt.isPresent()) {
                if (!storeProxyOpt.get().getStatus().equals(5)) {
                    log.error("{} shopId:{} applierId:{}", LogUtil.getClassMethodName("add-exist-store-proxy"), shop.getId(), applier.getId());
                    return Either.error(new Translate("注册代理员失败").toString());
                }
                // 经销商 默认认为通过经销商审核
                StoreProxyJXAuth jxAuth = StoreProxyJXAuth.convertStoreProxy(storeProxyOpt.get());
                jxAuth.initAuth();
                jxAuth.auth();
                storeProxyOpt.get().setStatus(jxAuth.getStatus());
                storeProxyOpt.get().setCity(storeProxys.get("city"));
                storeProxyOpt.get().setCityId(storeProxys.get("cityId"));
                storeProxyOpt.get().setProvince(storeProxys.get("province"));
                storeProxyOpt.get().setProvinceId(storeProxys.get("provinceId"));
                storeProxyOpt.get().setCounty(storeProxys.get("county"));
                storeProxyOpt.get().setCountyId(storeProxys.get("countyId"));
                storeProxyOpt.get().setProxyShopName(storeProxys.get("proxyShopName"));
                storeProxyOpt.get().setTel(storeProxys.get("tel"));
                storeProxyOpt.get().setAddress(storeProxys.get("address"));
                storeProxyOpt.get().setProxyMobile(storeProxys.get("proxyMobile"));
                storeProxyOpt.get().setBossName(storeProxys.get("bossName"));
                storeProxyOpt.get().setBossMobile(storeProxys.get("bossMobile"));
                storeProxyOpt.get().getExtra().put("imageList", storeProxys.get("imageList"));
                storeProxyOpt.get().getExtra().put("type", storeProxys.get("type"));
                NumberUtil.parseNumber(storeProxys.get("eastLongtitude"), Long.TYPE).ifSuccess(storeProxyOpt.get()::setEastLongtitude);
                NumberUtil.parseNumber(storeProxys.get("northLatitude"), Long.TYPE).ifSuccess(storeProxyOpt.get()::setNorthLatitude);
                storeProxyWriteService.updateWithStatus(storeProxyOpt.get()).ifSuccess(r -> sendSms(r ? storeProxyOpt.get().getId() : null));
                return Either.ok(storeProxyOpt.get().getId());
            } else {
                StoreProxy storeProxy = new StoreProxy();
                storeProxy.setUserName(applier.getName());
                storeProxy.setUserId(applier.getId());
                storeProxy.setShopId(shop.getId());
                storeProxy.setSupperId(null);
                StoreProxyAuthProxy authProxy = StoreProxyAuthProxy.build(storeProxy);
                authProxy.initAuth();
                storeProxy.setLevel(1);
                storeProxy.setFlag(1);
                storeProxy.setCity(storeProxys.get("city"));
                storeProxy.setCityId(storeProxys.get("cityId"));
                storeProxy.setProvince(storeProxys.get("province"));
                storeProxy.setProvinceId(storeProxys.get("provinceId"));
                storeProxy.setCounty(storeProxys.get("county"));
                storeProxy.setCountyId(storeProxys.get("countyId"));
                storeProxy.setProxyShopName(storeProxys.get("proxyShopName"));
                storeProxy.setTel(storeProxys.get("tel"));
                storeProxy.setAddress(storeProxys.get("address"));
                storeProxy.setProxyMobile(storeProxys.get("proxyMobile"));
                storeProxy.setBossName(storeProxys.get("bossName"));
                storeProxy.setBossMobile(storeProxys.get("bossMobile"));
                storeProxy.getExtra().put("imageList", storeProxys.get("imageList"));
                storeProxy.getExtra().put("type", storeProxys.get("type"));
                NumberUtil.parseNumber(storeProxys.get("eastLongtitude"), Long.TYPE).ifSuccess(storeProxy::setEastLongtitude);
                NumberUtil.parseNumber(storeProxys.get("northLatitude"), Long.TYPE).ifSuccess(storeProxy::setNorthLatitude);
                return storeProxyWriteService.create(storeProxy).ifSuccess(this::addStoreProxyData).ifSuccess(this::sendSms);
            }
        } catch (Exception exception) {
            log.error("{} fail to reg storeProxy for [UserId=>{}, shopId=>{}]", LogUtil.getClassMethodName(), applier.getId(), shop.getId(), exception);
            return Either.error(Translate.of("申请失败"));
        } finally {
            storeProxyCache.invalidate(new UserIdAndShopId(applier.getId(), shop.getId()));
            proxyShopIdCacheByUserId.invalidate(applier.getId());
        }
    }

    /**
     * 注册下级代理
     *
     * @param shop        店铺
     * @param invitor     邀请人
     * @param beInvited   被邀请人
     * @param storeProxys 详细信息
     * @return 代理Id
     */
    public Either<Long> registerStoreProxyLevelTwo(Shop shop, User invitor, User beInvited, Map<String, String> storeProxys) {
        val rBeenInvited = storeProxyReadService.findByShopIdAndUserId(shop.getId(), beInvited.getId());
        log.info("[invite-proxy1] {}", rBeenInvited);

        if (!rBeenInvited.isSuccess()) {
            log.error("{} shopId:{} beInvitedUserId:{}", LogUtil.getClassMethodName("fail-judge-been-proxy"), shop.getId(), beInvited.getId());
            return Either.error(new Translate("查找该用户相关信息失败").toString());
        }
        if (rBeenInvited.take().isPresent()) {
            log.info("{} shopId:{} beInvitedUserId:{} proxyId:{}", LogUtil.getClassMethodName("invited-already-proxy"), shop.getId(), beInvited.getId(), rBeenInvited.take().get().getId());
            StoreProxy existsOne = rBeenInvited.take().get();
            // magical number 不要动 是bug但是被当作了feature
            if (!(existsOne.getStatus().equals(9) || existsOne.getStatus().equals(5))) {
                log.error("{} shopId:{} applierId:{}", LogUtil.getClassMethodName("add-exist-store-proxy"), shop.getId(), beInvited.getId());
                return Either.error(new Translate("注册代理员失败").toString());
            }
            // 重新设置其审核状态
            StoreProxyJXAuth storeProxyJXAuth = StoreProxyJXAuth.convertStoreProxy(existsOne);
            storeProxyJXAuth.initAuth();
            if (ShopFunctionSlice.build(shop).isLessAuth()) {
                storeProxyJXAuth.auth();
            }
            existsOne.setStatus(storeProxyJXAuth.getStatus());
            existsOne.setCity(storeProxys.get("city"));
            existsOne.setCityId(storeProxys.get("cityId"));
            existsOne.setProvince(storeProxys.get("province"));
            existsOne.setProvinceId(storeProxys.get("provinceId"));
            existsOne.setCounty(storeProxys.get("county"));
            existsOne.setCountyId(storeProxys.get("countyId"));
            existsOne.setProxyShopName(storeProxys.get("proxyShopName"));
            existsOne.setTel(storeProxys.get("tel"));
            existsOne.setAddress(storeProxys.get("address"));
            existsOne.setProxyMobile(storeProxys.get("proxyMobile"));
            existsOne.setBossName(storeProxys.get("bossName"));
            existsOne.setBossMobile(storeProxys.get("bossMobile"));
            existsOne.getExtra().put("type", storeProxys.get("type"));
            existsOne.getExtra().put("imageList", storeProxys.get("imageList"));
            NumberUtil.parseNumber(storeProxys.get("eastLongtitude"), Long.TYPE).ifSuccess(existsOne::setEastLongtitude);
            NumberUtil.parseNumber(storeProxys.get("northLatitude"), Long.TYPE).ifSuccess(existsOne::setNorthLatitude);
            storeProxyWriteService.updateWithStatus(existsOne).ifSuccess(r -> sendSms(r ? existsOne.getId() : null));
            storeProxyCache.invalidate(new UserIdAndShopId(beInvited.getId(), shop.getId()));
            proxyShopIdCacheByUserId.invalidate(beInvited.getId());
            return Either.ok(existsOne.getId());
        }
        if (invitor == null) {
            throw new JsonResponseException(new Translate("缺少邀请人信息").toString());
        }
        val rIsProxy = storeProxyReadService.findByShopIdAndUserId(shop.getId(), invitor.getId());
        if (!rIsProxy.isSuccess()) {
            log.error("{} shopId:{} invitorUserId:{}", LogUtil.getClassMethodName("fail-find-proxy"), shop.getId(), invitor.getId());
            return Either.error(new Translate("查找用户相关信息失败").toString());
        }
        if (!rIsProxy.take().isPresent()) {
            log.error("{} shopId:{} invitorUserId:{} is not a proxy of shop", LogUtil.getClassMethodName("fail-invite-proxy"), shop.getId(), invitor.getId());
            return Either.error(new Translate("你不是该店的代理员").toString());
        }
        StoreProxy invitorEntity = rIsProxy.take().get();
        log.info("[invite-proxy2] {}", rBeenInvited);
        StoreProxy beInvitedEntity = new StoreProxy();
        beInvitedEntity.setUserId(beInvited.getId());
        beInvitedEntity.setUserName(beInvited.getName());
        beInvitedEntity.setShopId(shop.getId());
        beInvitedEntity.setLevel(invitorEntity.getLevel() + 1);
        beInvitedEntity.setSupperId(invitorEntity.getUserId());
        // 代理需要经销商审核 所以+4 奇怪的magic number 误动
        StoreProxyJXAuth storeProxyJXAuth = StoreProxyJXAuth.convertStoreProxy(beInvitedEntity);
        storeProxyJXAuth.initAuth();
        if (ShopFunctionSlice.build(shop).isLessAuth()) {
            storeProxyJXAuth.auth();
        }
        // 拷贝回审核状态
        beInvitedEntity.setStatus(storeProxyJXAuth.getStatus());
        beInvitedEntity.setFlag(1);
        beInvitedEntity.setCity(storeProxys.get("city"));
        beInvitedEntity.setCityId(storeProxys.get("cityId"));
        beInvitedEntity.setProvince(storeProxys.get("province"));
        beInvitedEntity.setProvinceId(storeProxys.get("provinceId"));
        beInvitedEntity.setCounty(storeProxys.get("county"));
        beInvitedEntity.setCountyId(storeProxys.get("countyId"));
        beInvitedEntity.setProxyShopName(storeProxys.get("proxyShopName"));
        beInvitedEntity.setTel(storeProxys.get("tel"));
        beInvitedEntity.setAddress(storeProxys.get("address"));
        beInvitedEntity.setProxyMobile(storeProxys.get("proxyMobile"));
        beInvitedEntity.setBossName(storeProxys.get("bossName"));
        beInvitedEntity.setBossMobile(storeProxys.get("bossMobile"));
        beInvitedEntity.getExtra().put("type", storeProxys.get("type"));
        beInvitedEntity.getExtra().put("imageList", storeProxys.get("imageList"));
        NumberUtil.parseNumber(storeProxys.get("eastLongtitude"), Long.TYPE).ifSuccess(beInvitedEntity::setEastLongtitude);
        NumberUtil.parseNumber(storeProxys.get("northLatitude"), Long.TYPE).ifSuccess(beInvitedEntity::setNorthLatitude);
        try {
            return storeProxyWriteService.create(beInvitedEntity).ifSuccess(this::addStoreProxyData).ifSuccess(this::sendSms);
        } finally {
            storeProxyCache.invalidate(new UserIdAndShopId(beInvited.getId(), shop.getId()));
            proxyShopIdCacheByUserId.invalidate(beInvited.getId());
        }
    }

    /**
     * 发送代理注册变更短信
     *
     * @param proxyId 代理Id
     */
    private void sendSms(Long proxyId) {
        if (proxyId == null) {
            return;
        }
        StoreProxy proxy = storeProxyReadService.findById(proxyId).orElse(Optional.empty()).orElse(null);
        if (proxy == null || proxy.getLevel() == 1) {
            log.debug("{} proxy:{} not need send Sms", LogUtil.getClassMethodName(), proxy);
            return;
        }
        StoreProxy supper = storeProxyReadService.findByShopIdAndUserId(proxy.getShopId(), proxy.getSupperId()).orElse(Optional.empty()).orElse(null);
        if (supper == null) {
            log.error("{} supper:(shopId:{},userId:{}) is null", LogUtil.getClassMethodName(), proxy.getShopId(), proxy.getSupperId());
            return;
        }
        String smsTemplate = "sms." + proxy.getShopId() + ".user.auth.apply";
        String userName = Optional.ofNullable(userProfileReadService.findProfileByUserId(supper.getUserId()).getResult()).map(UserProfile::getRealName).orElse(supper.getUserName());
        String mobile = Optional.ofNullable(userReadService.findById(supper.getUserId()).getResult()).map(User::getMobile).orElse("");
        EventSender.sendApplicationEvent(new MsgSendRequestEvent(mobile, smsTemplate, ImmutableMap.of("p_name", userName, "c_name", proxy.getProxyShopName())));
    }

    @AllArgsConstructor
    @Data
    private static class UserIdAndShopId {
        Long userId;
        Long shopId;
    }
}
