package moonstone.web.core.component.profit.view;

import lombok.Data;
import moonstone.common.utils.Translate;
import moonstone.order.model.WithDrawProfitApply;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@see AppWithdrawApplyView}
 */
@Data
public class AppWithdrawApplyViewImpl implements AppWithdrawApplyView {
    String account;
    Boolean complete;
    Boolean error;
    String status;
    String reason;
    BigDecimal money;
    Date createdAt;
    Date paidAt;
    Integer type;
    String userType;

    /**
     * 为App包装提现view层
     *
     * @param apply 提现数据
     * @return 提现View层
     */
    public static AppWithdrawApplyView from(WithDrawProfitApply apply) {
        AppWithdrawApplyViewImpl applyView = new AppWithdrawApplyViewImpl();
        applyView.setMoney(new BigDecimal(apply.getFee()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN));
        applyView.setCreatedAt(apply.getCreatedAt());
        applyView.setComplete(apply.isPaid());
        applyView.setError(apply.isError());
        applyView.setReason(apply.getExtra().get(WithDrawProfitApply.REASON));
        applyView.setStatus(new Translate("提现中").toString());
        applyView.setAccount(apply.getWithdrawAccount());
        applyView.setType(apply.getPaidType().getType());
        if (apply.isPaid()) {
            applyView.setStatus(new Translate("提现成功").toString());
        }
        if (apply.isReject() || apply.isError() || apply.isClosed()) {
            applyView.setStatus(new Translate("提现失败").toString());
        }
        return applyView;
    }

    @Override
    public Boolean isComplete() {
        return complete;
    }

    @Override
    public Boolean isError() {
        return error;
    }
}
