package moonstone.web.core.component;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.B3Util;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.Json;
import moonstone.order.service.PayerInfoService;
import moonstone.user.dto.UpdatePayerInfoEvent;
import moonstone.user.model.PayerInfo;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.nio.charset.StandardCharsets;
import java.security.Key;

@Slf4j
@Component
public class EncryptHelpListener extends AbstractVerticle {
    @Override
    public JsonObject config() {
        return new JsonObject().put("worker", true);
    }
    Key key ;

    @Override
    public void start() throws Exception {
        getVertx().eventBus().<JsonObject>localConsumer("encrypt#decode")
                .handler(this::decodeIt);
        getVertx().eventBus().consumer("hash#b3")
                .handler(this::hashB3);
        key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
    }

  /**
   * @apiNote UpdatePayerInfoTest#convert() see the test
   */
  @EventListener(UpdatePayerInfoEvent.class)
    public void updatePayerInfo(UpdatePayerInfoEvent event) {
        System.out.println("UPDATING EVENT -> " + event.toString());
        payerInfoService.save(JsonObject.mapFrom(event).mapTo(PayerInfo.class));
    }
    @Autowired
    PayerInfoService payerInfoService;

    private void hashB3(Message<Object> bufferMessage) {
        Object msg = bufferMessage.body();
        JsonObject req;
        if (msg instanceof Buffer buffer) {
            req = buffer.toJsonObject();
        } else if (msg instanceof String str) {
            req = new JsonObject(str);
        } else if (msg instanceof JsonObject js) {
            req = js;
        } else {
            bufferMessage.reply("NO JSON-OBJECT");
            return;
        }
        int turn = req.getInteger("turn", 1);
        byte[] salt = req.getString("salt", "moonstone").getBytes(StandardCharsets.UTF_8);
        byte[] rawData = req.getString("data").getBytes(StandardCharsets.UTF_8);
        vertx.executeBlocking(p -> {
            try {
                byte[] data = rawData;
                for (int i = 0; i < turn - 1; i++) {
                    data = B3Util.b3sum(data);
                    byte[] rom = new byte[data.length + salt.length];
                    System.arraycopy(data, 0, rom, 0, data.length);
                    System.arraycopy(salt, 0, rom, data.length, salt.length);
                    data = rom;
                }
                p.complete(Buffer.buffer(B3Util.b3sumStr(data)));
            } catch (Exception e) {
                log.error("FAIL TO B3", e);
                p.fail(e.getMessage());
            }
        }, false, p -> bufferMessage.reply(p.result()));
    }

    private void decodeIt(Message<JsonObject> req) {
        JsonObject body = req.body();
        try {
            if (!ObjectUtils.isEmpty(body) && body.containsKey("password")) {
                PayerInfo info = Json.parseObject(body.toString(), PayerInfo.class);
                if (info == null) {
                    req.reply(new JsonObject());
                    return;
                }
                vertx.<JsonObject>executeBlocking(p -> p.complete(JsonObject.mapFrom(PayerInfo.Helper.decode(key,
                        info))), r -> {
                    if (r.failed()) {
                        log.error("Fail to decode {}", info, r.cause());
                        req.fail(-1, "解码失败");
                        return;
                    }
                    try {
                        r.result().put("hash", info.getHash());
                    }finally {
                        req.reply(r.result());
                    }
                });
            } else {
                req.reply(body);
            }
        }catch (Exception e){
            log.error("FAIL TO DECODE IT", e);
            req.fail(-1, "购买人信息解密失败");
        }
    }
}
