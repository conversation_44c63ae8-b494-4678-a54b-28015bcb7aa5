package moonstone.web.core.component.user;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.*;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.model.ShopVipInformation;
import moonstone.order.service.HonestFanDataReadService;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.criteria.UserExtraInformationCriteria;
import moonstone.user.model.UserExtraInformation;
import moonstone.user.service.UserExtraInformationReadService;
import moonstone.user.service.UserExtraInformationWriteService;
import moonstone.web.core.component.order.BirthProfitRecorder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RpcProvider
@Component
public class BabyBirthManager {
    @RpcConsumer
    private UserExtraInformationReadService userExtraInformationReadService;
    @RpcConsumer
    private UserExtraInformationWriteService userExtraInformationWriteService;
    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;
    @RpcConsumer
    private OrderWriteService orderWriteService;
    @RpcConsumer
    private IntermediateInfoReadService intermediateInfoReadService;
    @RpcConsumer
    private BalanceDetailManager balanceDetailManager;
    @Autowired
    private EntityInformationCenter entityInformationCenter;
    @Autowired
    private HonestFanDataReadService honestFanDataReadService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private BirthProfitRecorder birthProfitRecorder;

    /**
     * 根据特定逻辑继续审核和奖励佣金
     *
     * @param userExtraInformationId 被审核的信息
     * @param user                   审核人
     * @return
     */
    public Either<Boolean> authTheBirth(Long userExtraInformationId, CommonUser user) {
        Long operatorId = user.getId();
        Either<UserExtraInformation> rApply = userExtraInformationReadService.findById(userExtraInformationId);
        if (!rApply.isSuccess()) {
            log.error("{} error:{}", LogUtil.getClassMethodName(), rApply.getErrorMsg());
            return Either.error(rApply.getErrorMsg());
        }
        UserExtraInformation entity = rApply.take();
        if (!authOperator(user, entity.getShopId())) {
            return Either.error(new Translate("权限不足").toString());
        }
        // judge the num of apply
        UserExtraInformationCriteria criteria = new UserExtraInformationCriteria();
        criteria.setUserId(entity.getUserId());
        criteria.setShopId(entity.getShopId());
        criteria.setStatusMasks(Stream.of(AuthAbleByStatus.AuthStatus.AUTHED).map(AuthAble.AuthStatus::getValue).collect(Collectors.toList()));
        criteria.setNotStatusMasks(Stream.of(AuthAbleByStatus.AuthStatus.REJECT).map(AuthAble.AuthStatus::getValue).collect(Collectors.toList()));
        Either<Long> rAuthCount = userExtraInformationReadService.count(criteria);
        if (rAuthCount.orElse(Long.MAX_VALUE) > 3) {
            log.debug("{} count-result:{}", LogUtil.getClassMethodName("auth-birth-information"), rAuthCount.isSuccess() ? rAuthCount.take() : rAuthCount.getErrorMsg());
            return Either.error(new Translate("该用户已审核数量已经达到上限").toString());
        }
        // auth
        if (!entity.auth()) {
            log.debug("{} id:{} operatorId:{}", LogUtil.getClassMethodName("auth-birth-information"), userExtraInformationId, operatorId);
            return Either.error(new Translate("审核失败,请重试").toString());
        }
        entity.setOperatorId(operatorId);
        // persist auth
        Either<Boolean> rUpdate = userExtraInformationWriteService.update(entity);
        if (!(rUpdate.isSuccess() && rUpdate.take())) {
            log.debug("{} id:{} rUpdate:{}", LogUtil.getClassMethodName("update"), userExtraInformationId, rUpdate.getErrorMsg());
            return Either.error(new Translate("更新审核失败").toString());
        }
        if (!ObjectUtils.isEmpty(entity.getExtra().get(entity.getSTORE_PROXY_INDEX()))) {
            Long storeProxyId = Long.valueOf(entity.getExtra().get(entity.getSTORE_PROXY_INDEX()));
            birthProfitRecorder.increaseFakeProfit(storeProxyId, entity.getUserId(), entity.getShopId());
        } else {
            log.error("{} not storeProxy information for this auth(babyInfo):{}", LogUtil.getClassMethodName(), entity.getId());
        }
        return Either.ok(true);
    }

    /**
     * 拒绝审核
     *
     * @param userExtraInformationId 被审核的信息
     * @param user                   操作人
     * @return
     */
    public Either<Boolean> refuse(Long userExtraInformationId, CommonUser user, String reason) {
        UserExtraInformation entity = userExtraInformationReadService.findById(userExtraInformationId).take();
        if (!authOperator(user, entity.getShopId())) {
            return Either.error(new Translate("权限不足").toString());
        }
        if (!entity.reject()) {
            log.warn("{} applyId:{} userId:{}", LogUtil.getClassMethodName(), user, user.getId());
            return Either.error(new Translate("拒绝失败,请重试").toString());
        }
        entity.getExtra().put(entity.getREASON_INDEX(), reason);
        entity.setOperatorId(user.getId());
        return userExtraInformationWriteService.update(entity);
    }

    /**
     * @param user       操作人
     * @param criteria   筛选条件
     * @param authStatus -1为拒绝 0 为待审核 1为审核通过
     */
    public Either<Paging<UserExtraInformation>> paging(CommonUser user, UserExtraInformationCriteria criteria, Integer authStatus) {
        if (criteria == null) criteria = new UserExtraInformationCriteria();
        criteria.setShopId(user.getShopId());
        criteria.setType(UserExtraInformation.UserExtraInformationType.BirthInformation.getType());
        if (authStatus != null) {
            criteria.setStatusMasks(criteria.getStatusMasks() == null ? new ArrayList<>() : criteria.getStatusMasks());
            switch (authStatus) {
                case -1: {
                    criteria.getStatusMasks().add(AuthAbleByStatus.AuthStatus.REJECT.getValue());
                    break;
                }
                case 0: {
                    criteria.getStatusMasks().add(AuthAbleByStatus.AuthStatus.MASK_CODE.getValue());
                    break;
                }
                case 1: {
                    criteria.getStatusMasks().add(AuthAbleByStatus.AuthStatus.AUTHED.getValue());
                    break;
                }
            }
        }
        return userExtraInformationReadService.paging(criteria);
    }

    /**
     * 判断是否存在这个数据
     *
     * @param shopId  平台Id
     * @param birthId 出生证明id
     * @return
     */
    public Either<Boolean> exists(Long shopId, String birthId) {
        UserExtraInformationCriteria criteria = new UserExtraInformationCriteria();
        criteria.setShopId(shopId);
        criteria.setHashData(birthId);
        criteria.setType(UserExtraInformation.UserExtraInformationType.BirthInformation.getType());
        criteria.setNotStatusMasks(Arrays.asList(AuthAble.AuthStatus.REJECT.getValue()));
        return userExtraInformationReadService.count(criteria).map(count -> count > 0);
    }

    /**
     * 插入一条出生记录
     *
     * @param userId  用户Id
     * @param shopId  平台Id
     * @param birthId 出生证明id
     * @param imgUrl  出生证明的图片地址
     * @return
     */
    @Deprecated
    public Either<Long> insert(Long userId, Long shopId, String birthId, String imgUrl, Long shopVipInformationId) {
        UserExtraInformation birthInformation = new UserExtraInformation();
        birthInformation.setUserId(userId);
        birthInformation.setShopId(shopId);
        birthInformation.setHashData(birthId);
        birthInformation.setImgUrl(imgUrl);
        birthInformation.setType(UserExtraInformation.UserExtraInformationType.BirthInformation.getType());
        birthInformation.setRelatedEntity(entityInformationCenter.build(ShopVipInformation.class, shopVipInformationId));
        birthInformation.setStatus(1);
        birthInformation.initAuth();
        if (exists(shopId, birthId).orElse(true)) {
            return Either.error(new Translate("该编号已存在").toString());
        }
        return userExtraInformationWriteService.create(birthInformation);
    }

    /**
     * 插入一条出生记录
     *
     * @param userId  用户Id
     * @param shopId  平台Id
     * @param birthId 出生证明id
     * @param imgUrl  出生证明的图片地址
     * @return
     */
    public Either<Long> insert(Long userId, Long shopId, String birthId, String imgUrl, String shopVipInformationId, int seq, @NotNull Long refererId) {
        UserExtraInformation birthInformation = new UserExtraInformation();
        birthInformation.setUserId(userId);
        birthInformation.setShopId(shopId);
        birthInformation.setHashData(birthId);
        birthInformation.setImgUrl(imgUrl);
        birthInformation.setType(UserExtraInformation.UserExtraInformationType.BirthInformation.getType());
        birthInformation.getExtra().put("mdb_id", shopVipInformationId);
        birthInformation.getExtra().put("seq", seq + "");
        birthInformation.getExtra().put(birthInformation.getSTORE_PROXY_INDEX(), refererId.toString());
        birthInformation.setStatus(1);
        birthInformation.initAuth();
        if (exists(shopId, birthId).orElse(true)) {
            return Either.error(new Translate("该编号已存在").toString());
        }
        return userExtraInformationWriteService.create(birthInformation);
    }

    private boolean authOperator(CommonUser operator, Long shopId) {
        return Objects.equals(operator.getShopId(), shopId);
    }

    /**
     * 修改数据,将原有数据改为不可用,将新数据插入
     *
     * @param userId      用户Id
     * @param shopId      平台Id
     * @param refererId   新修改的代理店的用户Id
     * @param oldBirthId  老的出生编号
     * @param birthId     出生编号
     * @param birthImgUrl 出生图片地址
     * @return
     */
    public Either<Long> update(Long userId, Long shopId, Long refererId, String oldBirthId, String birthId, String birthImgUrl) {
        // 利用业务逻辑来做这个事情
        Either<List<UserExtraInformation>> rFindEntity = userExtraInformationReadService.findByHashDataAndShopId(oldBirthId, shopId, UserExtraInformation.UserExtraInformationType.BirthInformation.getType());
        if (!rFindEntity.isSuccess()) {
            return Either.error(rFindEntity.getErrorMsg());
        }
        if (rFindEntity.take().isEmpty()) {
            log.error("{} birdthId:{} shopId:{}", LogUtil.getClassMethodName(), birthId, shopId);
            return Either.error(new Translate("数据异常").toString());
        }
        UserExtraInformation information = rFindEntity.take().get(0);
        if (!Objects.equals(information.getUserId(), userId)) {
            log.error("{} userId:{} of entity:{}", LogUtil.getClassMethodName("not-belong"), userId, information);
            return Either.error(new Translate("数据异常").toString());
        }
        UserExtraInformation change = new UserExtraInformation();
        BeanUtils.copyProperties(information, change);
        if (!information.getHashData().equals(birthId) || !Objects.equals(information.getImgUrl(), birthImgUrl)) {
            if (information.isAuthed()) {
                return Either.error(new Translate("已审核成功的数据不可以修改").toString());
            }
            information.setStatus(-1);
            change.getExtra().remove(change.getREASON_INDEX());
            change.initAuth();
            change.setHashData(birthId);
            change.setImgUrl(birthImgUrl);
            change.getExtra().put("old", information.getId().toString());
            change.getExtra().put(change.getSTORE_PROXY_INDEX(), refererId.toString());
        } else {
            // 没有修改
            return Either.ok(information.getId());
        }
        if (!userExtraInformationWriteService.update(information).isSuccess()) {
            return Either.error(new Translate("更新数据失败").toString());
        }
        Either<Long> rCreate = userExtraInformationWriteService.create(change);
        if (rCreate.isSuccess()) {
            return Either.ok(change.getId());
        } else {
            return Either.error(rCreate.getErrorMsg());
        }
    }
}
