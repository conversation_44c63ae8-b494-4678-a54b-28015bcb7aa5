package moonstone.web.core.component.cache;


import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import moonstone.cache.ItemCacheHolder;
import moonstone.integral.model.IntegralActivity;
import moonstone.integral.service.IntegralActivityReadService;
import moonstone.integral.vo.IntegralActivityView;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class IntegralActivityCacheHolder {
    @Autowired
    private IntegralActivityReadService integralActivityReadService;
    @Autowired
    private ItemCacheHolder itemCacheHolder;
    @Autowired
    private ItemReadService itemReadService;
    LoadingCache<Long, Optional<List<IntegralActivity>>> activityCacheByShopId = Caffeine.newBuilder()
            .expireAfterAccess(15, TimeUnit.MINUTES)
            .maximumSize(10000)
            .build(key -> integralActivityReadService.findByShopId(key).map(Optional::of)
                    .orElse(Optional.empty())
            );
    LoadingCache<Long, Optional<IntegralActivity>> activityCacheById = Caffeine.newBuilder()
            .expireAfterAccess(15, TimeUnit.MINUTES)
            .maximumSize(10000)
            .build(key -> integralActivityReadService.findById(key).map(Optional::of)
                    .orElse(Optional.empty()));
    // 使用非lambda来保障对activityCacheById的引用 以避免未init
    LoadingCache<Long, Optional<IntegralActivityView>> integralActivityViewCache = Caffeine.newBuilder()
            .expireAfterAccess(15, TimeUnit.MINUTES)
            .maximumSize(10000)
            .build(new CacheLoader<Long, Optional<IntegralActivityView>>() {
                @Override
                public Optional<IntegralActivityView> load(@NotNull Long key) {
                    Optional<IntegralActivity> activity = activityCacheById.get(key);
                    String[] itemCodes = Objects.requireNonNull(activity).map(IntegralActivity::getItemCode).map(str -> str.split(",")).orElse(new String[0]);
                    List<Long> itemIdList = new ArrayList<>();
                    for (String itemCode : itemCodes) {
                        activity.map(IntegralActivity::getShopId).map(shopId -> itemReadService.findByShopIdAndCode(shopId, itemCode).getResult())
                                .orElseGet(ArrayList::new).stream().map(Item::getId).forEach(itemIdList::add);
                    }
                    Optional<IntegralActivityView> activityView = activity.map(IntegralActivityView::from);

                    activityView.ifPresent(view -> view.setItemIds(itemIdList.toArray(new Long[0])));
                    return activityView;
                }
            });

    public void invalidate(Long id, Long shopId) {
        Optional.ofNullable(id).ifPresent(_id -> {
            Objects.requireNonNull(activityCacheById.get(_id)).map(IntegralActivity::getShopId).ifPresent(activityCacheByShopId::invalidate);
            activityCacheById.invalidate(_id);
        });
        Optional.ofNullable(shopId).ifPresent(activityCacheByShopId::invalidate);
        integralActivityViewCache.invalidateAll();
    }

    public Optional<IntegralActivity> findById(Long id) {
        return activityCacheById.get(id);
    }

    public Optional<IntegralActivityView> findViewById(Long id) {
        return integralActivityViewCache.get(id);
    }

    public Optional<IntegralActivity> findItemCode(Long shopId, String itemCode) {
        if (itemCode == null || shopId == null) return Optional.empty();
        Optional<IntegralActivity> integralActivity = Objects.requireNonNull(activityCacheByShopId.get(shopId)).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull).filter(activity -> activity.getItemCodeSet() != null && activity.getItemCodeSet().contains(itemCode)).findFirst();
        integralActivity.ifPresent(activity -> activityCacheById.put(activity.getId(), Optional.of(activity)));
        return integralActivity;
    }

    public Optional<IntegralActivity> findByItemId(Long itemId) {
        if (itemId == null) return Optional.empty();
        Item item = itemCacheHolder.findItemById(itemId);
        return findItemCode(item.getShopId(), item.getItemCode());
    }

    public Optional<List<IntegralActivity>> findByShopId(Long shopId) {
        Optional<List<IntegralActivity>> integralActivityList = activityCacheByShopId.get(shopId);
        Objects.requireNonNull(integralActivityList).ifPresent(activityList -> activityList.forEach(activity -> activityCacheById.put(activity.getId(), Optional.of(activity))));
        return integralActivityList;
    }

}
