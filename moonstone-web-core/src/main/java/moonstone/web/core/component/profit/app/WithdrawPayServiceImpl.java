package moonstone.web.core.component.profit.app;

import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.Channels;
import io.terminus.pay.enums.TradeStatus;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.model.BusinessPayParams;
import io.terminus.pay.model.TradeResult;
import io.terminus.pay.service.PayChannel;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.OutSystemIdProvider;
import moonstone.common.utils.Translate;
import moonstone.order.enu.WithdrawBy;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawPrinciple;
import moonstone.order.model.domain.AbstractWithdrawApplyDomain;
import moonstone.order.service.WithDrawProfitApplyWriteService;
import moonstone.order.service.WithdrawPrincipleManager;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.UserWx;
import moonstone.web.core.component.api.WithdrawPayService;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.component.pay.xinbada.application.XinBaDaPayChannel;
import moonstone.web.core.constants.EnvironmentConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class WithdrawPayServiceImpl implements WithdrawPayService {
    final static String DEFAULT_CHANNEL = "wechatpay-jsapi";
    /**
     * 是否将sourceId当作shopId使用
     */
    final boolean sourceId_is_shopId = true;
    @Autowired
    ChannelRegistry channelRegistry;
    @Autowired
    EnvironmentConfig environmentConfig;
    @Autowired
    OutSystemIdProvider outSystemIdProvider;
    @Autowired
    ShopWxaProjectReadService shopWxaProjectReadService;
    @Autowired
    ShopWxaReadService shopWxaReadService;
    @Autowired
    ShopPayInfoReadService shopPayInfoReadService;
    @Autowired
    WithdrawPrincipleManager withdrawPrincipleManager;
    @Autowired
    WithDrawProfitApplyWriteService withDrawProfitApplyWriteService;
    @Autowired
    ShopCacheHolder shopCacheHolder;

    @Override
    public Response<TradeResult> pay(UserWx apply, WithDrawProfitApply withDrawProfitApply) {
        TradeResult result;

        WithdrawPrinciple withdrawPrinciple = withdrawPrincipleManager.findByShopId(withDrawProfitApply.getSourceId()).orElseGet(WithdrawPrinciple::new);

        String channel = determineChannel(withDrawProfitApply);
        // 如果提现方式不为但丁 则需要变换渠道

        long serviceFee = Optional.ofNullable(withDrawProfitApply.getServiceFee()).orElseGet(() -> BigDecimal.valueOf(withDrawProfitApply.getFee()).divide(new BigDecimal("100"), RoundingMode.DOWN)
                .multiply(withdrawPrinciple.getRateServiceFee()).add(withdrawPrinciple.getStaticServiceFee())
                .multiply(new BigDecimal("100")).longValue());
        try {
            boolean testEnvironmentAllow = withDrawProfitApply.getSourceId() == 1L
                    || withDrawProfitApply.getSourceId() == 82L
                    || withDrawProfitApply.getSourceId() == 133L;
            testEnvironmentAllow &= !channel.equals(DEFAULT_CHANNEL);
            testEnvironmentAllow &= !channel.equals(Channels.Alipay.WAP);
            if (environmentConfig.isOnline() || testEnvironmentAllow) {
                log.debug("{} paidType:{} channel:{}", LogUtil.getClassMethodName("find-channel"), withDrawProfitApply.getExtra().get(WithdrawBy.INDEX.getName()), channel);
                PayChannel payChannel = channelRegistry.findChannel(channel);
                BusinessPayParams businessPayParams = new BusinessPayParams();
                businessPayParams.setAppId(getAppIdBySourceId(withDrawProfitApply.getSourceId()));
                businessPayParams.setChannel(channel);
                businessPayParams.setPayToType("toChange");
                // use the apply.sourceId is ok too
                if (sourceId_is_shopId && withDrawProfitApply.getSourceId() != 0L) {
                    businessPayParams.setSellerNo(withDrawProfitApply.getSourceId().toString());
                } else {
                    businessPayParams.setSellerNo(withDrawProfitApply.getExtra().get("sellerNo"));
                }
                if (businessPayParams.getSellerNo().equals("0")) {
                    businessPayParams.setSellerNo(withDrawProfitApply.getExtra().get("sellerNo"));
                }
                businessPayParams.setOpenId(apply.getOpenId());
                businessPayParams.setTradeNo(outSystemIdProvider.getId(withDrawProfitApply.getId(), withDrawProfitApply.getCreatedAt(), OutSystemIdProvider.type.WithDrawTradeNo));
                long amount = withDrawProfitApply.getFee() - serviceFee;
                businessPayParams.setAmount((int) amount);
                businessPayParams.setDesc("[" + withDrawProfitApply.getId() + "] " + new Translate("利润提现:")
                        + new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) + new Translate("元"));
                businessPayParams.setCheckName("NO_CHECK");
                businessPayParams.setReUserName(apply.getNickName());
                /// 老弟吃我ｎａｔ啦！
                businessPayParams.setSpbillCreateIp("127.0.0.1");
                log.debug("{} apply:{} payParams:{}", LogUtil.getClassMethodName(), apply, businessPayParams);
                result = payChannel.businessPay(businessPayParams);
                if (payChannel instanceof XinBaDaPayChannel) {
                    AbstractWithdrawApplyDomain.build(withDrawProfitApply).requireQuery(true);
                }
            } else {            //若不是线上环境，则模拟提现
                result = new TradeResult();
                result.setChannel("mockpay");
                result.setType(TradeType.BUSINESS_PAY.value());
                result.setStatus(TradeStatus.SUCCESS.value());
                result.setGatewaySerialNo(withDrawProfitApply.getPaySerialNo());
                result.setTradeAt(new Date());
            }
            if (result.isFail()) {
                log.error("[ThirdPartyPayService](pay) failed to withDraw! result:{}", JSON.toJSONString(result));
                return Response.fail(result.getError());
            }
            if (Objects.isNull(withDrawProfitApply.getServiceFee())) {
                Either<Boolean> fullFillResult = withDrawProfitApplyWriteService.fulfillServiceFee(withDrawProfitApply, serviceFee);
                if (!fullFillResult.isSuccess()) {
                    log.error("{} fail to update apply[{}] serviceFee[{}]", LogUtil.getClassMethodName(), withDrawProfitApply.getId(), serviceFee, fullFillResult.getError());
                    EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("提现服务费获取失败", String.format("提现[%s] 设置服务费[%s]", withDrawProfitApply.getId(), serviceFee), fullFillResult.getError(), EmailReceiverGroup.DEVELOPER));
                }
            }
            return Response.ok(result);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} method:{} line:{}", LogUtil.getClassMethodName(), ex.getStackTrace()[0].getMethodName(), ex.getStackTrace()[0].getLineNumber(), ex);
            return Response.fail(ex.getMessage());
        }

    }

    /**
     * judge the payChannel of this withdraw
     *
     * @param withDrawProfitApply withdraw apply
     * @return payChannel
     */
    String determineChannel(WithDrawProfitApply withDrawProfitApply) {
        if (withDrawProfitApply.getExtra().getOrDefault(WithdrawBy.INDEX.getName(), WithdrawBy.DanDing.getName()).equals(WithdrawBy.YunAccount.getName())) {
            WithDrawProfitApply.WithdrawPaidType paidType = withDrawProfitApply.getPaidType();
            switch (paidType) {
                case WECHAT: {
                    return PayChannelsConstants.YunAccount.WECHAT;

                }
                case ALIPAY: {
                    return PayChannelsConstants.YunAccount.ALIPAY;

                }
                case BANK:
                case SelfBank: {
                    return PayChannelsConstants.YunAccount.BANK;
                }
                default:
                case OTHER:
                    throw new RuntimeException(new Translate("目前不支持其他提现方式").toString());
            }
        }
        if (ShopFunctionSlice.build(shopCacheHolder.findShopById(withDrawProfitApply.getSourceId())).isWithdrawRequireCertification()) {
            return PayChannelsConstants.XIN_BA_DA_PAY;
        }
        return DEFAULT_CHANNEL;
    }

    /**
     * 获取微信支付appId
     *
     * @param sourceId 目前是shopId 但是未来是sourceId
     * @return 可用的支付信息
     */
    String getAppIdBySourceId(long sourceId) {
        long shopId = convertSourceIdIntoShopId(sourceId);
        ShopPayInfo shopPayInfo = shopPayInfoReadService.findByShopIdAndPayChannel(shopId, "wechatpay").getResult();
        if (shopPayInfo == null) {
            log.error("{}", LogUtil.getClassMethodName("FuckUP-NULL"));
            throw new RuntimeException("空AppId检查一下");
        }
        return shopPayInfo.getAccountNo();
    }

    /**
     * 获取sourceId对应的小程序的对应的shopId
     *
     * @param sourceId 小程序（未来将是projectId） 目前是使用shopId
     * @return shopId
     */
    private Long convertSourceIdIntoShopId(long sourceId) {
        if (sourceId == 0) {
            // 某个商家是第一个测试的商家 所以绑死了这个商家
            return 28L;
        }
        // 如果sourceId是shopId的设计还在使用
        if (sourceId_is_shopId) {
            return sourceId;
        }
        ShopWxaProject shopWxaProject = shopWxaProjectReadService.findById(sourceId).getResult();
        if (shopWxaProject == null) {
            log.error("{} sourceId:{}", LogUtil.getClassMethodName(), sourceId);
            throw new JsonResponseException(new Translate("获取帐号信息失败").toString());
        }
        ShopWxa shopWxa = shopWxaReadService.findById(shopWxaProject.getShopWxaId()).getResult();
        if (shopWxa == null) {
            log.error("{} shopWxaProject:{}", LogUtil.getClassMethodName(), shopWxaProject);
            throw new JsonResponseException(new Translate("获取帐号信息失败").toString());
        }
        return shopWxa.getShopId();
    }

}
