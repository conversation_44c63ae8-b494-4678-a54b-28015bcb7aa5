package moonstone.web.core.component.listener;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.showcase.mq.config.anno.MQEventConsumerMethod;
import moonstone.web.core.component.cache.SkuStockCacheAtRedisManager;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.item.ItemUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("UnstableApiUsage")
@Component
@Slf4j
public class RedisItemStockCacheClearer extends AbstractVerticle {

    @Autowired
    private SkuStockCacheAtRedisManager skuStockCacheAtRedisManager;
    @RpcConsumer
    private SkuReadService skuReadService;

    @VertxEventBusListener(ItemUpdateEvent.class)
    @MQEventConsumerMethod(ItemUpdateEvent.class)
    public void itemUpdated(ItemUpdateEvent itemUpdateEvent) {
        log.info("ItemUpdateEvent LOG : RedisItemStockCacheClearer {}", itemUpdateEvent.getItemId());
        updateStockCache(itemUpdateEvent.getItemId());
    }

    /**
     * 清楚商品在redis中的缓存迫使其从数据库中刷新（可能会导致超卖 但是由于前者的数据更新 可能性大幅度下降)
     *
     * @param itemId 商品Id
     */
    private void updateStockCache(Long itemId) {
        Response<List<Sku>> rSkus = skuReadService.findSkusByItemId(itemId);
        if (!rSkus.isSuccess()) {
            log.error("{} failed to update sku stock in redis may cause item(id:{}) can't be ordered", LogUtil.getClassMethodName(), itemId);
            return;
        }
        skuStockCacheAtRedisManager.removeCache(rSkus.getResult().stream().map(Sku::getId).collect(Collectors.toList()));
    }
}
