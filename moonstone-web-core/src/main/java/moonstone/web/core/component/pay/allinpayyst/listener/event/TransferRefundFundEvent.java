package moonstone.web.core.component.pay.allinpayyst.listener.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 退款时资金不足时，触发的资金调拨事件
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TransferRefundFundEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = -4539317188299682443L;

    private String refundOutId;
}
