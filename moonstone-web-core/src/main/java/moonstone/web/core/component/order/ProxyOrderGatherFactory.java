package moonstone.web.core.component.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.api.OrderGatherFactory;
import moonstone.order.model.GatherOrder;
import moonstone.order.model.OrderGather;
import moonstone.order.model.ShopOrder;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

@Component
public class ProxyOrderGatherFactory implements OrderGatherFactory {
    @Override
    public Optional<ShopOrder> gather(OrderGather orderGather, ShopOrder shopOrder) {
        if (!(orderGather instanceof ProxyOrderGather))
            return Optional.of(shopOrder);
        ProxyOrderGather proxyOrderGather = (ProxyOrderGather) orderGather;
        if (proxyOrderGather.getProxyId() == null) {
            proxyOrderGather.setProxyId(shopOrder.getReferenceId());
        }
        if (!Objects.equals(proxyOrderGather.getProxyId(), shopOrder.getReferenceId()))
            return Optional.of(shopOrder);
        orderGather.getGatheredOrderList().add(shopOrder);
        return Optional.empty();
    }

    @Override
    public OrderGather getTarget() {
        return new ProxyOrderGather(this);
    }

    @Override
    public OrderOutFrom outFrom() {
        return OrderOutFrom.EXCEL_IMPORT;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class ProxyOrderGather extends OrderGather {
        Long proxyId;

        ProxyOrderGather(OrderGatherFactory orderGatherFactory) {
            this.factory = orderGatherFactory;
        }

        @Override
        protected void decorate(GatherOrder gatherOrder) {
            gatherOrder.setOutShopId(proxyId == null ? null : proxyId.toString());
        }
    }
}
