package moonstone.web.core.component.pay.allinpayyst.listener;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.enu.FundsTransferPaymentRelatedTypeEnum;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.FundsTransferPayment;
import moonstone.order.model.Refund;
import moonstone.order.service.RefundReadService;
import moonstone.web.core.component.order.FundsTransferPaymentManager;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTChannel;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTTokenProvider;
import moonstone.web.core.component.pay.allinpayyst.listener.event.TransferRefundFundEvent;
import moonstone.web.core.util.LockKeyUtils;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class TransferRefundFundListener {

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private AllInPayYSTTokenProvider allInPayYSTTokenProvider;

    @Resource
    private AllInPayYSTChannel allInPayYSTChannel;

    @Resource
    private FundsTransferPaymentManager fundsTransferPaymentManager;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 退款时资金不足时，触发的资金调拨事件
     * <br/>不管了，丢就丢吧
     *
     * @param event
     */
    @EventListener(TransferRefundFundEvent.class)
    public void transferRefundFund(TransferRefundFundEvent event) {
        log.debug("TransferRefundFundListener.transferRefundFund receive event={}", JSON.toJSONString(event));
        int count = 0;
        Refund refund = null;
        while (count < 5){
            refund = refundReadService.findByOutId(event.getRefundOutId()).getResult();
            if (ObjUtil.isNotEmpty(refund)) {
                break;
            }
            ThreadUtil.safeSleep(1000);
            count++;
        }
        if (refund == null) {
            log.error("通过退款outId查询退款为空 outId={}", event.getRefundOutId());
            return;
        }
        log.info("查询到退款信息 {}", JSONUtil.toJsonStr(refund));
        if (PaymentChannelEnum.ALLINPAY_YST.getCode().equals(refund.getChannel())) {
            Refund finalRefund = refund;
            CompletableFuture.runAsync(() -> transferByRefund(finalRefund));
        }
    }

    private void transferByRefund(Refund refund) {
        log.info("退款资金调拨 退款信息 {} ", JSONUtil.toJsonStr(refund));
        // 创建资金调拨单
        var payment = create(refund);

        // 发起资金调拨单
        pay(payment);
    }

    private void pay(FundsTransferPayment payment) {
        if (payment == null || payment.getId() == null) {
            return;
        }

        var lock = redissonClient.getLock(LockKeyUtils.fundsTransferPaymentPay(payment.getId()));
        if (!lock.tryLock()) {
            throw new RuntimeException("获取锁失败");
        }

        try {
            fundsTransferPaymentManager.pay(payment);
        } catch (Exception ex) {
            log.error("TransferRefundFundListener.pay error, id={}", payment.getId(), ex);
        } finally {
            lock.unlock();
        }
    }

    private FundsTransferPayment create(Refund refund) {
        var lock = redissonClient.getLock(LockKeyUtils.fundsTransferPaymentCreate(
                refund.getId(), FundsTransferPaymentRelatedTypeEnum.REFUND.getCode()));
        if (!lock.tryLock()) {
            throw new RuntimeException("获取锁失败");
        }

        FundsTransferPayment payment = null;
        try {
            payment = fundsTransferPaymentManager.createByRefund(refund);
        } catch (Exception ex) {
            log.error("TransferRefundFundListener.create error, refundId={}", refund.getId(), ex);
        } finally {
            lock.unlock();
        }

        return payment;
    }
}
