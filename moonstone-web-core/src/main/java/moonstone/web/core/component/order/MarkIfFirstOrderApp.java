package moonstone.web.core.component.order;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.DateUtil;
import moonstone.event.OrderRefundEvent;
import moonstone.event.PaymentPaidEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.FirstOrderMark;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRefund;
import moonstone.order.service.*;
import moonstone.user.model.PayerInfo;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class MarkIfFirstOrderApp {
  @Resource
  SkuOrderReadService skuOrderReadService;
  @Resource
  ShopOrderReadService orderReadService;
  @Resource
  PaymentReadService paymentReadService;
  @Resource
  RefundReadService refundReadService;
  @Autowired
  PayerInfoService payerInfoService;
  @Autowired
  FirstOrderMarkService firstOrderMarkService;

  @Resource
  private RedissonClient redissonClient;

  public void scanAndMarkFirstOrder(LocalDate from, Long shopId) {
    // find the attr-info
    List<Map<String, Object>> orderMarkList = firstOrderMarkService.findPossibleFirstOrderMark(from, shopId);
    persist(orderMarkList);
  }

  @Scheduled(cron = "0 0/1 * * * ?")
  public void refreshFirstOrderMarkOneMonth() {
    List<Map<String, Object>> orderMarkList = firstOrderMarkService.findPossibleFirstOrderMark(LocalDate.now().minusDays(1), 274L);
    persist(orderMarkList);
  }

  private void persist(List<Map<String, Object>> orderMarkList) {
    var lock = redissonClient.getLock("FIRST-ORDER-LOCK");
    if (!lock.tryLock()) {
      return;
    }
    try {
      persistWithoutLock(orderMarkList);
    } finally {
      lock.unlock();
    }
  }

  private void persistWithoutLock(List<Map<String, Object>> orderMarkList) {
    try {
      Map<Long, List<Map<String, Object>>> markMap = new HashMap<>();
      for (Map<String, Object> mark : orderMarkList) {
        markMap.computeIfAbsent(Long.parseLong(mark.get("order_id").toString()), v -> new ArrayList<>())
          .add(mark);
      }
      List<FirstOrderMark> marks = new ArrayList<>();
      for (Map.Entry<Long, List<Map<String, Object>>> e : markMap.entrySet()) {
        var builder = new StringBuilder();
        Long shopId = null;
        Long userId = null;
        Long orderId = e.getKey();
        Long orderAt = null;
        List<Long> categoryIdList = new ArrayList<>();
        for (Map<String, Object> m : e.getValue()) {
          var raw = m.get("shop_category_id");
          var shopCategoryId = raw == null ? null : Long.parseLong(raw.toString());
          if (categoryIdList.contains(shopCategoryId))
            continue;
          if (raw == null) {
            System.out.println("ERROR DATA -> " + m);
          }
          categoryIdList.add(shopCategoryId);
          shopId = Long.parseLong(m.get("shop_id").toString());
          userId = Long.parseLong(m.get("buyer_id").toString());
          Object createdAt = m.get("created_at");
          if (createdAt instanceof Date date) {
            orderAt = date.toInstant().toEpochMilli();
          }
          if (createdAt instanceof String str) {
            orderAt = LocalDateTime.parse(str, DateTimeFormatter.ofPattern(DateUtil.YMDHMS_FORMAT))
              .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
          }
          if (createdAt instanceof LocalDateTime localDateTime) {
            orderAt = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
          }
        }
        categoryIdList.sort(Long::compareTo);
        for (Long categoryId : categoryIdList.stream().filter(i -> i != null && i > 0).toList()) {
          builder.append(categoryId).append("-");
        }
        // find the cert-info and then mark it
        builder.append(hashByOrderCert(orderId));
        marks.add(new FirstOrderMark(shopId, userId, orderId, builder.toString(), orderAt));
      }

      for (FirstOrderMark mark : marks) {
        firstOrderMarkService.save(mark);
      }
    } catch (Exception e) {
      log.error("FAIL TO CREATE FirstOrderMark", e);
    }
  }

  private String hashByOrderCert(Long orderId) {
    PayerInfo record = payerInfoService.findByOrderId(orderId);
    return record.getHash();
  }

  @EventListener(OrderRefundEvent.class)
  public void removeMark(OrderRefundEvent refundEvent) {
    // bad code here
    if (!refundEvent.getOrderOperation().equals(OrderEvent.REFUND_APPLY_AGREE.toOrderOperation())
      && !refundEvent.getOrderOperation().equals(OrderEvent.REFUND.toOrderOperation())) {
      return;
    }
    log.debug("REFUND IT");
    Long minId = null;
    for (OrderRefund orderRefund : refundReadService.findOrderIdsByRefundId(refundEvent.refundId()).getResult()) {
      Long orderId = orderRefund.getOrderId();
      if (orderRefund.getOrderLevel() == OrderLevel.SKU) {
        orderId = skuOrderReadService.findById(orderRefund.getOrderId()).getResult().getOrderId();
      }
      if (minId == null) {
        minId = orderId;
      } else {
        minId = Math.min(minId, orderId);
      }
      firstOrderMarkService.removeByOrderId(orderId);
    }
    if (minId == null) return;
    var order = orderReadService.findById(minId).getResult();
    List<Map<String, Object>> marks = firstOrderMarkService.findPossibleFirstOrderMark(
      LocalDate.ofInstant(order.getCreatedAt().toInstant(), ZoneId.systemDefault()),
      order.getShopId());
    persist(marks);
  }

  @EventListener(PaymentPaidEvent.class)
  public void orderPaid(PaymentPaidEvent event) {
    dispatchEvent(event);
  }

  // hope this dispatch can be handle before the app die
  private void dispatchEvent(PaymentPaidEvent event) {
    var orders = paymentReadService.findOrderIdsByPaymentId(event.getPaymentId()).getResult();

    for (var op : orders) {
      var orderId = op.getOrderId();
      if (op.getOrderLevel() == OrderLevel.SKU) {
        orderId = skuOrderReadService.findById(orderId).getResult().getOrderId();
      }
      var order = orderReadService.findById(orderId).getResult();
      var date = LocalDate.ofInstant(order.getCreatedAt().toInstant(), ZoneId.systemDefault());
      List<Map<String, Object>> marks = firstOrderMarkService.findPossibleFirstOrderMark(date, order.getShopId());
      persist(marks);
    }
  }
}
