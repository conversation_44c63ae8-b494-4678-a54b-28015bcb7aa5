package moonstone.web.core.component;

import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.api.OrderNeedGatherJudge;
import moonstone.order.model.SkuOrder;
import moonstone.shop.model.Shop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class OrderNeedGatherJudgeImpl implements OrderNeedGatherJudge {
    @Autowired
    private ShopCacheHolder shopCacheHolder;

    @Override
    public boolean needGather(SkuOrder skuOrder) {
        if (Objects.isNull(skuOrder) || Objects.isNull(skuOrder.getOutFrom())) return false;
        OrderOutFrom outFrom = OrderOutFrom.fromCode(skuOrder.getOutFrom());
        if (outFrom != OrderOutFrom.WE_SHOP)
            return false;
        Shop shop = shopCacheHolder.findShopById(skuOrder.getShopId());
        Map<String, String> shopExtra = Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new);
        return !shopExtra.getOrDefault(ShopExtra.NoGatherNeed.getCode(), "true").equals("true");
    }
}
