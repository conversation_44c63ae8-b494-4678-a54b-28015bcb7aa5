package moonstone.web.core.component.profit;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.IgnoreAble;
import moonstone.common.model.IsPresent;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OutComeDetail;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.BalanceDetailWriteService;
import moonstone.shop.slice.ShopFunctionSlice;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class RealProfitChangeSlice {
    private final ShopCacheHolder shopCacheHolder;
    private final BalanceDetailReadService balanceDetailReadService;
    private final BalanceDetailWriteService balanceDetailWriteService;
    private final RedissonClient redissonClient;

    /**
     * 初始化已赚取数据
     *
     * @param userId 用户id
     * @param shopId 店铺Id
     */
    private void initEarnData(Long userId, Long shopId) {
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(shopId);
        criteria.setUserId(userId);
        criteria.setType(ProfitType.Earned.getValue());
        try {
            Optional<BalanceDetail> optEarned = balanceDetailReadService.paging(criteria.toMap()).getResult().getData().stream().findFirst();
            if (optEarned.isPresent()) {
                return;
            }
            BalanceDetail earned = new BalanceDetail();
            earned.setSourceId(shopId);
            earned.setUserId(userId);
            earned.setType(ProfitType.Earned.getValue());
            earned.setFee(0L);
            earned.setChangeFee(0L);
            earned.setStatus(IsPresent.presentMaskBit.Present.getValue());
            balanceDetailWriteService.create(earned);
        } catch (Exception ex) {
            log.error("{} get earned failed:{}", LogUtil.getClassMethodName(), ex.getMessage());
        }
    }

    /**
     * 真实扣减 不对外提供
     *
     * @param change 利润
     * @return 修改后钱包剩余
     * @see moonstone.order.api.BalanceDetailManager#changeRealProfit(BalanceDetail)  使用带有锁机制的
     */
    @Transactional(rollbackFor = RuntimeException.class)
    Long changeRealProfitWithoutLock(BalanceDetail change) {
        if (change.getSourceId() == null) {
            // deprecate, don't use this feature
            change.setSourceId(0L);
        }
        // block the balance change if the shop is closed
        final long sourceId = change.getSourceId();
        if (sourceId > 0 && ShopFunctionSlice.build(shopCacheHolder.findShopById(sourceId)).isProfitClosed()) {
            boolean allow = change.isPresent() ||
                    (change.getType() == 2);
            if (!allow) {
                log.debug("{} shop is closed, so no profit is allowed", LogUtil.getClassMethodName());
                throw new RuntimeException(Translate.of("店铺功能停止"));
            }
        }
        /// 获取用户id
        final long userId = change.getUserId();
        /// 初始化余额缓存值
        InComeDetail cachePresent = null;
        InComeDetail cacheUnPresent = null;
        BalanceDetail decreaseOne = null;
        log.info("{}(change) work on Action:{} for UserId:{}", LogUtil.getClassMethodName(), JSON.toJSONString(change), userId);
        /// 操作链
        List<InnerUpdateNode> innerUpdateCaches = new ArrayList<>();
        ///　检查是否未关联外键
        Consumer<BalanceDetail> checkNullRelatedId = (dto) -> {
            if (dto.getChangeFee() < 0) {
                log.error("{} action deny for profit which below zero,BalanceDetail:{}", LogUtil.getClassMethodName("deny-income"), dto);
                throw new RuntimeException("deny.wrong.changeFee");
            }
            if (dto.getRelatedId() == null) {
                log.error("{} (change) action deny because null relatedId,action:{}", LogUtil.getClassMethodName(), JSON.toJSONString(dto));
                throw new RuntimeException("deny.null.relatedId");
            }
        };
        /// 获取余额缓存值，最好以后添加一个账本检查
        List<InComeDetail> cash = getCash(userId, sourceId);
        /// 获取余额　但是不存在
        if (CollectionUtils.isEmpty(cash)) {
            cash = initCash(userId, sourceId);
        }

        ///  从数据库中获取余额
        for (BalanceDetail subDetail : cash) {
            InComeDetail inComeDetail = new InComeDetail(subDetail);
            if (inComeDetail.isPresent()) {
                cachePresent = inComeDetail;
            } else {
                cacheUnPresent = inComeDetail;
            }
        }

        /// 拒绝空账本
        if (cachePresent == null || cacheUnPresent == null) {
            log.error("{} (change) read cache failed by userId:{},data:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(cash));
            throw new RuntimeException("find.balanceDetail.fail");
        }
        if (change.getType().equals(ProfitType.InCome.getValue())) {
            /// - 收入类型
            /// 是收入类型
            InComeDetail inComeDetail = new InComeDetail(change);
            /// 是由于订单收入获取的
            if (inComeDetail.isOrderRelated()) {
                ///     a. 订单关联的
                /// 判断是否下单得到的，下单得到的是预先收入
                if (inComeDetail.isPresent()) {
                    ////        * 是真实收入
                    /// 如果订单关联的真实收入添加则是表示其确定收货了，从预收入内转移款项进入真实收入
                    /// 仅仅ShopOrder产生真实余额操作
                    OutComeDetail decreaseDetail = new OutComeDetail(change);
                    decreaseDetail.setType(ProfitType.OutCome.getValue());
                    ///　逆转其是否为为确认形式
                    decreaseDetail.setStatus(decreaseDetail.getStatus() ^ InComeDetail.presentMaskBit.Present.getValue() | IgnoreAble.IgnoreAbleDefaultMaskbit.IgnoreAble.getBit());
                    ///　检查是否未关联外键
                    checkNullRelatedId.accept(decreaseDetail);
                    /// 仅仅在是真实持久化也就是判断其不是日志时使用
                    if (inComeDetail.isPersistAble()) {
                        /// 增加可用余额
                        innerUpdateCaches.add(new InnerUpdateNode(change.getChangeFee(), true));
                        /// 设置该状态余额
                        change.setFee(cachePresent.getFee() + change.getChangeFee());
                        /// 减少预收入
                        innerUpdateCaches.add(new InnerUpdateNode(-change.getChangeFee(), false));
                        /// 设置该状态余额
                        decreaseDetail.setFee(cacheUnPresent.getFee() - decreaseDetail.getChangeFee());
                        /// 判断其是否为扣除至0以下
                        if (decreaseDetail.getFee() < 0) {
                            log.error("{} (change) unPresent below zero,so revoke it,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
                            throw new RuntimeException("reject.change.0");
                        }
                        //获取earned实体
                        initEarnData(change.getUserId(), change.getSourceId());
                        innerUpdateCaches.add(new InnerUpdateNode(change.getChangeFee(), true, ProfitType.Earned.getValue()));
                    }
                    ///　检查是否未关联外键
                    checkNullRelatedId.accept(change);
                    Response<Boolean> rPersistAdd = balanceDetailWriteService.create(change);
                    Response<Boolean> rPersistDec = balanceDetailWriteService.create(decreaseDetail);
                    decreaseOne = decreaseDetail;
                    /// 判断持久化是否失败
                    if (!(rPersistDec.isSuccess() && rPersistAdd.isSuccess())) {
                        log.error("{} (change) persist BalanceDetail failed,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
                        throw new RuntimeException("change.profit.fail");
                    }
                } else {
                    ///         * 不是真实收入
                    /// 仅仅在是真实持久化也就是判断其不是日志时使用
                    if (change.isPersistAble()) {
                        /// 增加预收入
                        innerUpdateCaches.add(new InnerUpdateNode(change.getChangeFee(), false));
                        /// 设置该状态余额
                        cacheUnPresent.setFee(change.getChangeFee() + cacheUnPresent.getFee());
                    }
                    ///　检查是否未关联外键
                    checkNullRelatedId.accept(change);
                    var rPersistAdd = balanceDetailWriteService.create(change);
                    if (!rPersistAdd.isSuccess()) {
                        log.error("{} (change) persist BalanceDetail failed,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
                        throw new RuntimeException("change.profit.fail");
                    }
                }
            }
            /// 是由提现失败获取的
            else if (inComeDetail.isWithDrawRelated() || inComeDetail.isIntegralRelated()) {
                ///     b. 是体现关联的 也就是提现失败退款
                innerUpdateCaches.add(new InnerUpdateNode(change.getChangeFee(), change.isPresent()));
                change.setFee(cachePresent.getFee() + change.getChangeFee());
                ///　检查是否未关联外键
                checkNullRelatedId.accept(change);
                var rPersist = balanceDetailWriteService.create(change);
                if (!rPersist.isSuccess() || !rPersist.getResult()) {
                    log.error("{} (change) persist BalanceDetail failed,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
                    throw new RuntimeException("change.profit.fail");
                }
                log.debug("{} withdraw-or-integral change:{} caches:{}", LogUtil.getClassMethodName(), JSON.toJSONString(inComeDetail), JSON.toJSONString(innerUpdateCaches));
            }
            /// bug  下次应该提前总和成switch 以避免这种多级if
            else {
                ///     c. 错误类型 不知道与什么关联 也不知道其关系
                log.error("{} (change) unexpected income type {}", LogUtil.getClassMethodName(), inComeDetail.getType());
                throw new RuntimeException("change.profit.fail");
            }
        } else if (change.getType().equals(ProfitType.OutCome.getValue())) {
            /// - 支出类型
            /// 判断是否为对真实额度操作
            if (change.isPersistAble()) {
                if (change.isPresent()) {
                    /// 扣除真实可用的余额
                    innerUpdateCaches.add(new InnerUpdateNode(-change.getChangeFee(), true));
                    change.setFee(cachePresent.getFee() - change.getChangeFee());
                } else {
                    /// 扣除预估收入
                    innerUpdateCaches.add(new InnerUpdateNode(-change.getChangeFee(), false));
                    change.setFee(cacheUnPresent.getFee() - change.getChangeFee());
                }
            }
            /// 检查是否为null相关
            checkNullRelatedId.accept(change);
            /// 判断是否为0
            if (change.getFee() < 0) {
                log.error("{} (change) unPresent below zero,so revoke it,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
                throw new RuntimeException("reject.change.0");
            }
            var rPersist = balanceDetailWriteService.create(change);
            if (!rPersist.isSuccess()) {
                log.error("{} (change) persist BalanceDetail failed,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
                throw new RuntimeException("change.profit.fail");
            }
        } else {
            /// - 未知类型 错误类型 或者是后来添加的类型
            log.error("{} (change) failed to change profit by {}", LogUtil.getClassMethodName(), change);
        }
        /// 由链操作修改余额缓存值
        log.debug("before update, innerUpdateCaches={}", JSON.toJSONString(innerUpdateCaches));
        boolean failed = !innerUpdateCaches.stream().map(dto -> balanceDetailWriteService.updateCacheBySourceId(userId, dto.fee, dto.present, sourceId, dto.type))
                .allMatch(Response::isSuccess);
        /// 链操作失败了 回滚一切数据库操作
        if (failed && !innerUpdateCaches.isEmpty()) {
            log.error("{} (change) persist cache failed,revoke all action,userId:{},action:{}", LogUtil.getClassMethodName(), userId, JSON.toJSONString(change));
            throw new RuntimeException("change.profit.fail");
        }
        // find the fee that after the change
        List<InComeDetail> cashAfterChange = getCash(userId, sourceId);
        /// update the changed fee
        Stream.of(change, decreaseOne)
                .filter(Objects::nonNull)
                .forEach(record -> {
                    long fee = cashAfterChange.stream().filter(detail -> detail.isPresent() == record.isPresent())
                            .map(BalanceDetail::getFee).findFirst().orElse(-1L);
                    BalanceDetail updateFee = new BalanceDetail();
                    updateFee.setFee(fee);
                    updateFee.setId(record.getId());
                    updateFee.setStatus(null);
                    balanceDetailWriteService.update(updateFee);
                });
        return cashAfterChange.stream().filter(detail -> detail.isPresent() == change.isPresent())
                .map(BalanceDetail::getFee).findFirst().orElse(-1L);
    }


    public List<InComeDetail> getCash(Long userId, Long sourceId) {
        List<BalanceDetail> cash = queryCash(userId, sourceId);
        if (cash == null) {
            throw Translate.exceptionOf("用户[%s]平台[%s]获取余额失败", userId, sourceId);
        }
        if (cash.isEmpty()) {
            return initCash(userId, sourceId);
        }
        return cash.stream().map(InComeDetail::new).collect(Collectors.toList());
    }

    List<BalanceDetail> queryCash(Long userId, Long sourceId) {
        BalanceDetailCriteria balanceDetailCriteria = new BalanceDetailCriteria();
        /// 缓存　因为实际上的数值应该由全体明细计算
        balanceDetailCriteria.setType(ProfitType.CacheInCome.getValue());
        balanceDetailCriteria.setUserId(userId);
        /// 因为实际上数值只有两个
        balanceDetailCriteria.setPageSize(2);
        /// -1表示删除的数值
        balanceDetailCriteria.setNotStatus(-1);

        balanceDetailCriteria.setSourceId(sourceId);

        return balanceDetailReadService.paging(balanceDetailCriteria.toMap()).getResult().getData();
    }

    List<InComeDetail> initCash(long userId, long sourceId) {
        Lock lock = redissonClient.getLock("InitCash[" + userId + "-" + sourceId + "]");
        lock.lock();
        try {
            List<BalanceDetail> exists = queryCash(userId, sourceId);
            if (!exists.isEmpty()) {
                return exists.stream().map(InComeDetail::new).collect(Collectors.toList());
            }
            /// 设置真实可用余额
            InComeDetail cachePresent = new InComeDetail();
            cachePresent.setStatus(InComeDetail.presentMaskBit.Present.getValue());
            cachePresent.setUserId(userId);
            cachePresent.setType(ProfitType.CacheInCome.getValue());
            cachePresent.setSourceId(sourceId);
            /// 设置预估余额
            InComeDetail cacheUnPresent = new InComeDetail();
            cacheUnPresent.setUserId(userId);
            cacheUnPresent.setStatus(1);
            cacheUnPresent.setType(ProfitType.CacheInCome.getValue());
            cacheUnPresent.setSourceId(sourceId);
            /// 疑惑以下创建是否会死锁?理应不会 因为在同一个事务,但是其他搜索的情况 不确定
            var rPersistP = balanceDetailWriteService.create(cachePresent);
            var rPersistUp = balanceDetailWriteService.create(cacheUnPresent);
            if (!(rPersistP.isSuccess() && rPersistUp.isSuccess())) {
                log.error("[BalanceDetailManager](change) init balanceDetail failed userId:{}", userId);
                throw new RuntimeException("init error");
            }
            return Arrays.asList(cachePresent, cacheUnPresent);
        } finally {
            lock.unlock();
        }
    }

    /**
     * <AUTHOR>
     */
    @Data
    private static class InnerUpdateNode {
        private final long fee;
        private final boolean present;
        private final int type;

        private InnerUpdateNode(Long fee, boolean present) {
            this.fee = fee == null ? 0 : fee;
            this.present = present;
            this.type = ProfitType.CacheInCome.getValue();
        }

        private InnerUpdateNode(Long fee, boolean present, int type) {
            this.fee = fee == null ? 0 : fee;
            this.present = present;
            this.type = type;
        }
    }
}
