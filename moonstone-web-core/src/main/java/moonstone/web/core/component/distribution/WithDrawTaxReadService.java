package moonstone.web.core.component.distribution;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.weShop.model.TaxRate;
import moonstone.weShop.service.TaxRateReadService;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WithDrawTaxReadService {
    @RpcConsumer
    TaxRateReadService taxRateReadService;
    public TaxRate getTaxBy(@Nullable Long shopId, @Nullable Long weShopId)
    {
        try {
            if (shopId == null) {
                return taxRateReadService.getOneShopId(-1L).getResult();
            }
            Response<List<TaxRate>> rTax = taxRateReadService.findByShopId(shopId);
            if (rTax.isSuccess()) {
                List<TaxRate> taxRates = rTax.getResult().stream().filter(tr -> tr.getWeShopId().equals(weShopId)).collect(Collectors.toList());
                if (!taxRates.isEmpty()) {
                    return taxRates.get(0);
                }
                taxRates = rTax.getResult().stream().filter(tr -> tr.getWeShopId().equals(-1L)).collect(Collectors.toList());
                if (!taxRates.isEmpty()) {
                    return taxRates.get(0);
                }
                return taxRateReadService.getOneShopId(-1L).getResult();
            }
            return taxRateReadService.getOneShopId(-1L).getResult();
        }
        catch (Exception ex)
        {
            log.error("failed to getTaxBy shopId:{} weShopId:{}",shopId,weShopId);
            ex.printStackTrace();
            throw new RuntimeException("fail.find.taxRate");
        }
    }
}
