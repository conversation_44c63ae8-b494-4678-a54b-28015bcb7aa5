package moonstone.web.core.component;

import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TypeAliasPackageParser {
    private static final Pattern regex = Pattern.compile("\\((.*?)\\)");
    private static final Splitter or = Splitter.on('|').omitEmptyStrings().trimResults();
    private static final Splitter comma = Splitter.on(CharMatcher.is(',').or(CharMatcher.is(';').or(CharMatcher.is('\t').or(CharMatcher.is('\n'))))).omitEmptyStrings().trimResults();

    public TypeAliasPackageParser() {
    }

    public static String flatPackageNames(String inputs) {
        return Joiner.on(',').join(resolveTypeAliasPackageNames(inputs));
    }

    static Set<String> resolveTypeAliasPackageNames(String inputs) {
        Set<String> result = new HashSet<>();

        for (String input : comma.split(inputs)) {
            result.addAll(resolvePackageNames(input));
        }

        return result;
    }

    static Set<String> resolvePackageNames(String input) {
        Set<String> packageNames = new HashSet<>();
        Matcher matcher = regex.matcher(input);
        ArrayList<MatchPosition> matchPositions = new ArrayList<>();

        while (matcher.find()) {
            matchPositions.add(new TypeAliasPackageParser.MatchPosition(matcher.start(), matcher.end(), matcher.group(1)));
        }

        if (matchPositions.isEmpty()) {
            packageNames.add(input);
        } else {
            resolveRegexPackageNames(input, matchPositions, packageNames);
        }

        return packageNames;
    }

    private static void resolveRegexPackageNames(String input, List<MatchPosition> matchPositions, Set<String> packageNames) {
        Multimap<Integer, String> current = HashMultimap.create();
        int currentIndex = 0;
        current.put(currentIndex, "");

        for (TypeAliasPackageParser.MatchPosition matchPosition : matchPositions) {
            int start = matchPosition.start();
            int end = matchPosition.end();
            String betweenCurrentAndStart = input.substring(currentIndex, start);
            String text = matchPosition.text();

            for (String part : or.split(text)) {

                for (String c : current.get(currentIndex)) {
                    current.put(end, c + betweenCurrentAndStart + part);
                }
            }
            currentIndex = end;
        }

        int length = input.length();
        int endIndex = currentIndex;
        current.get(currentIndex).forEach(packageName -> {
            String betweenLastMatchEndAndInputEnd = input.substring(endIndex, length);
            current.put(length, packageName + betweenLastMatchEndAndInputEnd);
        });
        packageNames.addAll(current.get(length));

    }

    public static class MatchPosition implements Serializable {
        private static final long serialVersionUID = -8954011461860739607L;
        private final int start;
        private final int end;
        private final String text;

        public MatchPosition(int start, int end, String text) {
            this.start = start;
            this.end = end;
            this.text = text;
        }

        public int start() {
            return this.start;
        }

        public int end() {
            return this.end;
        }

        public String text() {
            return this.text;
        }
    }
}