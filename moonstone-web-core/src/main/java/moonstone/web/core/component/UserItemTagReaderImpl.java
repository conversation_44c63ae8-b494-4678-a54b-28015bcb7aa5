package moonstone.web.core.component;

import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.item.api.UserItemTagReader;
import moonstone.item.model.Tag;
import moonstone.item.service.ItemTagManager;
import moonstone.user.service.SubManagerJudge;
import moonstone.web.core.events.user.UserTagChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;

@Component
@Slf4j
public class UserItemTagReaderImpl implements UserItemTagReader {
    @Autowired
    private ItemTagManager itemTagManager;
    @Autowired
    private JedisPool jedisPool;
    @Autowired
    private SubManagerJudge subManagerJudge;
    @Autowired
    private ShopCacheHolder shopCacheHolder;

    private final BiFunction<Long, Long, String> cacheKeyByUserIdAndShopId = (userId, shopId) -> String.format("[UserTag](%s)(%s)", shopId, userId);

    @EventListener(UserTagChangeEvent.class)
    public void invalidateUserTagCache(UserTagChangeEvent event) {
        try (Jedis jedis = jedisPool.getResource()) {
            String index = cacheKeyByUserIdAndShopId.apply(event.getUserId(), event.getShopId());
            jedis.del(index);
            jedis.set(index, null);
        }
    }

    @Override
    public String findByShopIdAndUserId(Long shopId, BaseUser currentUser) {
        String patchTags;
        try (Jedis jedis = jedisPool.getResource()) {
            String index = cacheKeyByUserIdAndShopId.apply(currentUser.getId(), shopId);
            String cache = jedis.get(index);
            if (cache == null) {
                cache = read(shopId, currentUser.getId());
                jedis.set(index, cache);
                jedis.expire(index, 60 * 5);
            }
            patchTags = cache;
        }
        return patchTags;
    }

    /**
     * 从数据库里拉取userTag的信息
     *
     * @param shopId 店铺Id
     * @param userId 用户Id
     * @return ItemTag总和
     */
    private String read(Long shopId, Long userId) {
        List<String> userOwnedTags = new ArrayList<>(itemTagManager.findTagIdsByUserId(userId, shopId).orElseGet(ArrayList::new));
        if (shopId == null)
            return StringUtils.arrayToDelimitedString(userOwnedTags.toArray(), ";");
        switch (Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra()).orElseGet(HashMap::new).getOrDefault(ShopExtra.ItemTagType.getCode(), "1")) {
            case "0":
                if (subManagerJudge.isSubManager(shopId, userId))
                    itemTagManager.findTagIdsByShopId(shopId).orElseGet(ArrayList::new)
                            .stream().map(Tag::get_id).forEach(userOwnedTags::add);
            case "1":
            default:
                return StringUtils.arrayToDelimitedString(userOwnedTags.toArray(), ";");
        }
    }
}
