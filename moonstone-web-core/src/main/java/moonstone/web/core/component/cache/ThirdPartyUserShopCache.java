package moonstone.web.core.component.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.FinderBean;
import moonstone.common.utils.LogUtil;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.thirdParty.ThirdPartyUserShopUnbindEvent;
import moonstone.web.core.events.thirdParty.ThirdPartyUserShopUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ThirdPartyUserShopCache extends AbstractVerticle implements FinderBean<ThirdSystemAID, Optional<ThirdPartyUserShop>> {
    // dep
    @Autowired
    private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

    // cache
    private LoadingCache<ThirdSystemAID, Optional<ThirdPartyUserShop>> thirdPartyUserShopLoadingCache;


    @PostConstruct
    public void afterInit() {
        thirdPartyUserShopLoadingCache = Caffeine.newBuilder().maximumSize(500).expireAfterAccess(8, TimeUnit.HOURS)
                .build(this::load);
    }

    /**
     * 清楚缓存
     * 当绑定被取消的时候,删除缓存
     *
     * @param event 事件
     */
    @VertxEventBusListener(ThirdPartyUserShopUnbindEvent.class)
    public void removeCache(ThirdPartyUserShopUnbindEvent event) {
        thirdPartyUserShopLoadingCache.invalidate(new ThirdSystemAID(event.getThirdPartySystemId(), event.getThirdPartyUserShopId()));
    }

    @VertxEventBusListener(ThirdPartyUserShopUpdateEvent.class)
    public void removeCache(ThirdPartyUserShopUpdateEvent event) {
        thirdPartyUserShopLoadingCache.invalidate(new ThirdSystemAID(event.getThirdPartySystemId(), event.getShopId()));
    }

    public Optional<ThirdPartyUserShop> findBy(Integer systemId, Long shopId){
        return findBy(new ThirdSystemAID(systemId, shopId));
    }

    @Override
    public Optional<ThirdPartyUserShop> findBy(ThirdSystemAID index) {
        return thirdPartyUserShopLoadingCache.get(index);
    }

    /**
     * 并非为外部准备的方法,为内部调用真实数据库加载数据使用
     * 外部请 使用Get
     *
     * @param aid 复合Id
     * @return 返回加载结果
     */
    private Optional<ThirdPartyUserShop> load(ThirdSystemAID aid) {
        try {
            return Optional.ofNullable(thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(aid.getSystemId(), aid.getShopId()).getResult());
        } catch (Exception ex) {
            log.error("{} fail to load ThirdPartySystemUserShop for [{}]", LogUtil.getClassMethodName(), aid.toString(), ex);
            return Optional.empty();
        }
    }
    public void invalidate(ThirdSystemAID key) {
        thirdPartyUserShopLoadingCache.invalidate(key);
    }
}
