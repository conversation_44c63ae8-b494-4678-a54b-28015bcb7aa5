package moonstone.web.core.component.siteMessage;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import moonstone.cache.ShopCacheHolder;
import moonstone.shop.model.Shop;
import moonstone.user.model.SubSeller;
import moonstone.user.model.SubSellerRole;
import moonstone.user.service.SellerReadService;
import moonstone.user.service.SellerRoleReadService;
import moonstone.web.core.msg.enu.SiteMessageLevel;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站内信功能辅助组件
 */
@Component
public class SiteMessageComponent {

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Resource
    private SellerRoleReadService sellerRoleReadService;

    @Resource
    private SellerReadService sellerReadService;

    //各个角色名称
    public static final String ROLE_NAME_FINANCIAL = "财务";
    public static final String ROLE_NAME_CUSTOMER_SERVICE = "客服";
    public static final String ROLE_NAME_SALES_OPERATION = "销售运营";
    public static final String ROLE_NAME_MANAGER = "管理员";

    //各个角色名称对应的站内信消息类别
    private static final Map<String, List<SiteMessageLevel>> ROLE_MESSAGE_LEVEL_MAP = ImmutableMap.of(
            ROLE_NAME_FINANCIAL, Lists.newArrayList(SiteMessageLevel.WITHDRAW),
            ROLE_NAME_CUSTOMER_SERVICE, Lists.newArrayList(SiteMessageLevel.REFUND),
            ROLE_NAME_SALES_OPERATION, Lists.newArrayList(SiteMessageLevel.WITHDRAW, SiteMessageLevel.REFUND, SiteMessageLevel.SHOP_AUTH),
            ROLE_NAME_MANAGER,Lists.newArrayList(SiteMessageLevel.WITHDRAW, SiteMessageLevel.REFUND, SiteMessageLevel.SHOP_AUTH)
    );

    /**
     * 查询具有指定角色名称的角色的用户id
     *
     * @param shopId
     * @param roleName
     * @return
     */
    public List<Long> findUserIdsByRoleName(Long shopId, String roleName) {
        return findUserIdsByRoleNames(shopId, Lists.newArrayList(roleName));
    }

    /**
     * 查询具有指定角色名称的角色的用户id
     *
     * @param shopId
     * @param roleNameList
     * @return
     */
    public List<Long> findUserIdsByRoleNames(Long shopId, List<String> roleNameList) {
        if (shopId == null || CollectionUtils.isEmpty(roleNameList)) {
            return Collections.emptyList();
        }

        Shop shop = shopCacheHolder.findShopById(shopId);
        if (shop == null) {
            return Collections.emptyList();
        }

        //查出对应的角色id
        var roleList = sellerRoleReadService.findByMasterUserIdAndNames(shop.getUserId(), roleNameList).getResult();
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptyList();
        }
        List<Long> roleIds = roleList.stream().map(SubSellerRole::getId).collect(Collectors.toList());

        //查询对应的用户id
        var sellerList = sellerReadService.findByMasterUserIdAndRoleIds(shop.getUserId(), roleIds).getResult();
        if (CollectionUtils.isEmpty(sellerList)) {
            return Collections.emptyList();
        }

        return sellerList.stream().map(SubSeller::getUserId).collect(Collectors.toList());
    }

    /**
     * 查询当前用户可以查看站内信消息的种类
     *
     * @param shopId
     * @param userId
     * @return
     */
    public Set<SiteMessageLevel> findMessageLevel(Long shopId, Long userId) {
        if (shopId == null || userId == null) {
            return Collections.emptySet();
        }

        Shop shop = shopCacheHolder.findShopById(shopId);
        if (shop == null) {
            return Collections.emptySet();
        }

        //查出对应的角色id
        var roleNames = Lists.newArrayList(ROLE_NAME_FINANCIAL, ROLE_NAME_CUSTOMER_SERVICE, ROLE_NAME_SALES_OPERATION,ROLE_NAME_MANAGER);
        var roleList = sellerRoleReadService.findByMasterUserIdAndNames(shop.getUserId(), roleNames).getResult();
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptySet();
        }
        List<Long> roleIds = roleList.stream().map(SubSellerRole::getId).collect(Collectors.toList());

        //查询对应的用户id
        var sellerList = sellerReadService.findByMasterUserIdAndRoleIds(shop.getUserId(), roleIds).getResult();
        if (CollectionUtils.isEmpty(sellerList)) {
            return Collections.emptySet();
        }

        return convert(userId, sellerList, roleList);
    }

    private Set<SiteMessageLevel> convert(Long userId, List<SubSeller> sellerList, List<SubSellerRole> roleList) {
        Set<SiteMessageLevel> resultSet = new HashSet<>();

        var roleMap = roleList.stream().collect(Collectors.toMap(
                SubSellerRole::getId, SubSellerRole::getName, (k1, k2) -> k1));

        sellerList.stream()
                .filter(seller -> seller.getUserId().equals(userId))
                .forEach(seller -> resultSet.addAll(ROLE_MESSAGE_LEVEL_MAP.get(roleMap.get(seller.getRoleId()))));

        return resultSet;
    }
}
