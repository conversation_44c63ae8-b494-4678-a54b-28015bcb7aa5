package moonstone.web.core.component.pay.allinpayyst.util;

public interface MethodName {

    // 托管代收申请
    String agentCollectApply = "allinpay.yunst.orderService.agentCollectApply";

    // 托管代付申请
    String signalAgentPay = "allinpay.yunst.orderService.signalAgentPay";

    // 退款申请(未完成代付时)
    String refund = "allinpay.yunst.orderService.refund";

    // 退款申请(已完成代付时)
    String orderSplitRefund = "allinpay.yunst.orderService.orderSplitRefund";

    // 会员收银宝渠道商户信息及终端信息绑定接口, 电子账户资金调拨收银宝的前题
    String vspTermidService = "allinpay.yunst.memberService.vspTermidService";

    // 电子账户资金调拨收银宝接口
    String transferTeaRefundFund = "allinpay.yunst.orderService.transferTeaRefundFund";

    // 单据状态查询
    String getOrderStatus = "allinpay.yunst.orderService.getOrderStatus";

    // 提现申请
    String withdrawApply = "allinpay.yunst.orderService.withdrawApply";

    // 查询余额
    String queryBalance = "allinpay.yunst.orderService.queryBalance";
}
