package moonstone.web.core.component.user;

import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.UserRole;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.ShopWriteService;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.ThirdPartyUserShopWriteService;
import moonstone.user.cache.ThirdPartyUserCache;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.model.User;
import moonstone.user.service.ThirdPartyUserWriteService;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserWriteService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopWriteService;
import moonstone.web.core.component.api.OmsUserSync;
import moonstone.web.core.events.shop.ShopUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OmsUserSyncImpl implements OmsUserSync {
    @Autowired
    private ThirdPartyUserWriteService thirdPartyUserWriteService;

    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private UserWriteService<User> userWriteService;

    @Autowired
    private ShopReadService shopReadService;
    @Autowired
    private ShopWriteService shopWriteService;

    @Autowired
    private WeShopReadService weShopReadService;
    @Autowired
    private WeShopWriteService weShopWriteService;

    @Autowired
    private ShopCacheHolder shopCacheHolder;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private ThirdPartyUserCache thirdPartyUserCache;

    @Autowired
    private ThirdPartyUserShopWriteService thirdPartyUserShopWriteService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long syncTheUser(String outerUserId, String mobile, String name) {
        Optional<ThirdPartyUser> outerUserRes = thirdPartyUserCache.findThirdPartyUser(ThirdPartyUserType.OMS.getType(), outerUserId);
        if (!outerUserRes.isPresent()) {
            return createUserForOuterUserId(outerUserId, mobile, name);
        }
        ThirdPartyUser thirdPartyUser = outerUserRes.get();
        User user = userReadService.findById(thirdPartyUser.getUserId()).getResult();
        if (Objects.isNull(user)) {
            throw new RuntimeException(Translate.of("系统内部对用户[userId => %s, outerUserId => %s]信息丢失", thirdPartyUser.getUserId(), thirdPartyUser.getThirdPartyId()));
        }
        User update = new User();
        update.setId(user.getId());
        update.setMobile(mobile);
        update.setName(name);
        if (!userWriteService.update(update).getResult()) {
            log.error("{} {}", LogUtil.getClassMethodName(), Translate.of("更新用户[userId => %s, outerUserId => %s]信息失败", thirdPartyUser.getUserId(), thirdPartyUser.getThirdPartyId()));
        }
        return thirdPartyUser.getUserId();
    }


    /**
     * 为外部用户创建内部用户信息
     *
     * @param outerUserId 外部用户Id
     * @param mobile      手机号
     * @param name        用户名称
     * @return 内部用户Id
     */
    private Long createUserForOuterUserId(String outerUserId, String mobile, String name) {
        User user = new User();
        String originMobile = mobile;
        while (Objects.nonNull(userReadService.findByMobile(mobile).getResult())) {
            log.error("{} {}", LogUtil.getClassMethodName(), Translate.of("手机号[%s]已经存在, 请使用不同的手机号", mobile));
            mobile = (System.currentTimeMillis() % 100) + "@" + originMobile;
        }
        user.setMobile(mobile);
        user.setName(name);
        user.setType(2);
        user.setStatus(1);
        user.setRoles(Collections.singletonList(UserRole.BUYER.name()));

        // save user
        if (Objects.isNull(userWriteService.create(user).getResult())) {
            throw new RuntimeException(Translate.of("创建用户[outerUserId => %s]失败", outerUserId));
        }
        ThirdPartyUser thirdPartyUser = new ThirdPartyUser();
        thirdPartyUser.setUserId(user.getId());
        thirdPartyUser.setThirdPartyId(outerUserId);
        thirdPartyUser.setType(ThirdPartyUserType.OMS.getType());
        thirdPartyUserCache.invalidate(outerUserId);
        // save relation
        if (Objects.isNull(thirdPartyUserWriteService.create(thirdPartyUser))) {
            throw new RuntimeException(Translate.of("创建用户[outerUserId => %s]映射关系失败", outerUserId));
        }
        return user.getId();
    }

    @Override
    public Either<Long> markOuterUserAsShop(String outerUserId, String shopName, List<ThirdPartyUserShop> thirdPartyUserShopList) {
        Optional<ThirdPartyUser> thirdPartyUserOpt = thirdPartyUserCache.findThirdPartyUser(ThirdPartyUserType.OMS.getType(), outerUserId);
        if (!thirdPartyUserOpt.isPresent()) {
            return Either.error(Translate.of("第三方用户不存在"));
        }
        User user = userReadService.findById(thirdPartyUserOpt.get().getUserId()).getResult();
        if (Objects.isNull(user)) {
            return Either.error(Translate.of("第三方用户[outerUserId => %s]信息缺失", outerUserId));
        }
        if (Objects.isNull(shopCacheHolder.findShopByUserId(user.getId()))) {
            return createShopForUser(user, shopName, thirdPartyUserShopList);
        }
        // update old shop
        Shop shop = shopCacheHolder.findShopByUserId(user.getId());
        Shop update = new Shop();
        update.setId(shop.getId());
        if (!shopWriteService.update(update).isSuccess()) {
            throw new RuntimeException(Translate.of("店铺[%s]信息更新失败", shop.getId()));
        }
        for (ThirdPartyUserShop thirdPartyUserShop : thirdPartyUserShopList) {
            thirdPartyUserShop.setShopId(shop.getId());
            if (!thirdPartyUserShopWriteService.bind(thirdPartyUserShop).isSuccess()) {
                throw new RuntimeException(Translate.of("更新店铺额外信息失败"));
            }
        }
        return Either.ok(shop.getId());
    }

    /**
     * 创建店铺
     *
     * @param user                   用户
     * @param shopName               店铺Id
     * @param thirdPartyUserShopList 货源信息(第三方信息)
     * @return 结果
     */
    private Either<Long> createShopForUser(User user, String shopName, List<ThirdPartyUserShop> thirdPartyUserShopList) {
        Shop shop = new Shop();
        shop.setExtra(ImmutableMap.of(ShopExtra.homeDecoration.getCode(), Boolean.TRUE.toString()));
        shop.setUserId(user.getId());
        shop.setUserName(user.getName());
        shop.setName(shopName);
        shop.setIsSupply(1);
        shop.setType(1);
        shop.setStatus(0);
        if (!shopWriteService.create(shop).isSuccess()) {
            return Either.error(Translate.of("创建店铺信息失败"));
        }
        for (ThirdPartyUserShop thirdPartyUserShop : thirdPartyUserShopList) {
            thirdPartyUserShop.setShopId(shop.getId());
            if (!thirdPartyUserShopWriteService.bind(thirdPartyUserShop).isSuccess()) {
                throw new RuntimeException(Translate.of("绑定店铺货源信息失败"));
            }
        }

        if (!userWriteService.addRole(user.getId(), UserRole.SELLER).isSuccess()) {
            throw new RuntimeException(Translate.of("更新用户[%s]身份失败", user.getId()));
        }
        shopCacheHolder.invalidate(shop.getId());
        EventSender.publish(new ShopUpdateEvent(shop.getId()));
        EventSender.publish(new UserUpdateEvent(user.getId()));
        return Either.ok(shop.getId());
    }

    @Override
    public Either<Long> markOuterUserAsWeShop(String outerUserId, String weShopName) {
        Optional<ThirdPartyUser> thirdPartyUserOpt = thirdPartyUserCache.findThirdPartyUser(ThirdPartyUserType.OMS.getType(), outerUserId);
        if (!thirdPartyUserOpt.isPresent()) {
            return Either.error(Translate.of("第三方用户[%s]不存在", outerUserId));
        }
        Optional<WeShop> weShopOpt = weShopCacheHolder.findByUserId(thirdPartyUserOpt.get().getUserId());
        if (!weShopOpt.isPresent()) {
            WeShop weShop = new WeShop();
            weShop.setUserId(thirdPartyUserOpt.get().getUserId());
            weShop.setStatus(1);
            weShop.setName(weShopName);
            weShopWriteService.create(weShop);
            return Either.ok(weShop.getId());
        }
        WeShop update = new WeShop();
        update.setId(weShopOpt.get().getId());
        update.setName(weShopName);
        weShopWriteService.update(update);
        return Either.ok(update.getId());
    }
}