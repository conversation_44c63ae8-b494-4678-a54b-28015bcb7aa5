package moonstone.web.core.order.dto;

import lombok.*;
import moonstone.order.dto.OrderGroup;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderGroupViewObject extends OrderGroup implements Serializable {

    static final long serialVersionUID = -4451589719091198566L;

    String waitingSellerAuthDesc;
    /**
     * 门店名 --一级
     */
    String subStoreName;
    Long subStoreProfit;
    Long subStoreProfitFrom;
    /**
     * 是否已经变为可用余额 为了防止bug目前两者都开启功能
     */
    boolean subStoreRealProfit;
    /**
     * 导购员名  ---  二级
     */
    String guiderName;
    Long guiderProfit;
    boolean guiderRealProfit;

    String serviceProviderName;
    Long serviceProviderProfit;
    Long serviceProviderProfitFrom;
    boolean serviceProviderRealProfit;

    /**
     * 额外佣金(拉新佣金)持有人名称
     */
    String linkedProfitOwner;
    /**
     * 额外佣金(拉新佣金)
     */
    Long linkedProfit;
    /**
     * 额外的额外佣金
     */
    Long exLinkedProfit;
    String buyerAvatarUrl;
    /**
     * 申报单号
     */
    String declaredNo;
    /**
     * 订单查询增加一小时内显示退款申请按钮
     * <p>
     * true显示 false 不显示
     */
    Boolean refund = false;
    /**
     * 阶梯分销信息
     */
    DistributionProfitVO distributionProfit;
    /**
     * 佣金利润 显示
     */
    List<OrderProfitView> orderProfitViewList = new ArrayList<>();

    /**
     * 支付单号
     */
    private String paymentOutId;

    /**
     * 代付状态
     *
     * @see AgentPayStatus
     */
    private Integer agentPayStatusCode;
    private String agentPayStatusDescription;

    /**
     * 订单推送异常描述
     */
    private String orderPushErrorMessage;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class OrderProfitView {
        Long fromId;
        String profitOwnerName;
        Long profitOwnerUserId;
        BigDecimal profit;
        String profitName;
    }

    @AllArgsConstructor
    @Getter
    public enum AgentPayStatus {
        UNKNOWN(-1, "未知"),
        NO_NEED(0, "无须代付"),
        NOT_PAID(1, "未支付"),
        PAYING(2, "支付中"),
        PAID(3, "已支付"),
        FAILED(4, "支付失败"),
        ;
        private final Integer code;
        private final String description;
    }
}
