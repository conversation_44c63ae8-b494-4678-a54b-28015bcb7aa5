package moonstone.web.core.order.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class SubStoreRefundOrderVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1048098880802119066L;

    /**
     * 退款单id
     */
    private Long refundId;

    /**
     * 退款单状态code
     */
    private Integer refundStatus;

    /**
     * 退款单状态描述
     */
    private String refundStatusName;

    /**
     * 订单的申报单号
     */
    private String declaredId;

    /**
     * 订单的下单时间（毫秒数）
     */
    private Long orderCreateAt;

    /**
     * 订单实付金额
     */
    private Long fee;

    /**
     * 门店名称
     */
    private String subStoreName;

    /**
     * 订单推送异常描述
     */
    private String orderPushErrorMessage;

    /**
     * 订单的商品列表
     */
    private List<SubStoreRefundOrderItemVO> itemList;

    @Data
    public static class SubStoreRefundOrderItemVO implements Serializable {

        @Serial
        private static final long serialVersionUID = 4437564950988303845L;

        /**
         * 商品名称
         */
        private String itemName;

        /**
         * 商品图片url
         */
        private String itemImageUrl;

        /**
         * 商品数量
         */
        private Integer itemNum;
    }

}
