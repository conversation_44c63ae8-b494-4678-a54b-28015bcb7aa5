package moonstone.web.core.order.service.impl;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.event.ForceNewVersionByShopIdNotifyEvent;
import moonstone.event.MethodSwitchNotify;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.service.*;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.stock.impl.service.DepotCustomManager;
import moonstone.thirdParty.service.ThirdPartyJobService;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.user.service.PayerInfoReadService;
import moonstone.web.core.component.order.OrderTaxSplitManager;
import moonstone.web.core.config.ApplicationConfig;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.customs.SkipDeclareDepotCodeRegisterEvent;
import moonstone.web.core.order.OrderReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
@Data
public class PushSystemDep {
    @Autowired
    ShopPayInfoReadService shopPayInfoReadService;
    @Value("${Y800.open.api.gate}")
    String y800YangSupport;
    @Value("${Y800.partnerCode}")
    String y800PartnerCode;
    @Value("${Y800.partnerKey}")
    String y800PartnerKey;
    @Value("${Y800.sourcePlatform}")
    String y800SourcePlatform;
    @Value("${Y800.sourcePlatformV3}")
    String y800SourcePlatformV3;
    @Value("${Y800.merchantCode}")
    String y800MerchantCode;
    @Value("${mercury.pay.host}")
    String mercuryPayHost;
    @Value("${mercury.pay.appCode}")
    String mercuryPayAppCode;
    @Value("${mercury.pay.merchantCode}")
    String mercuryPayMerchantCode;
    @Value("${mercury.pay.customs.notify}")
    String mercuryPayCustomsNotify;
    @Value("${parana.mall.url}")
    String webUrl;
    @Autowired
    OrderReadLogic orderReadLogic;
    @Autowired
    SkuOrderReadService skuOrderReadService;
    @Autowired
    SkuOrderWriteService skuOrderWriteService;
    @Autowired
    ShopOrderReadService shopOrderReadService;
    @Autowired
    OrderWriteService orderWriteService;
    @Autowired
    ReceiverInfoReadService receiverInfoReadService;
    @Autowired
    PaymentReadService paymentReadService;
    @Autowired
    PaymentWriteService paymentWriteService;
    @Autowired
    ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @Autowired
    ApplicationConfig applicationConfig;
    @Autowired
    EnvironmentConfig environmentConfig;
    @Resource
    ThirdPartyUserShopReadService thirdPartyUserShopReadService;
    @Autowired
    DepotCustomManager depotCustomManager;
    @Autowired
    PushOrderJudge pushOrderJudge;
    @Autowired
    Y800OrderIdGenerator y800OrderIdGenerator;
    @Autowired
    ShopCacheHolder shopCacheHolder;
    @Autowired
    Y800PayMchManager y800PayMchManager;
    @RpcConsumer
    OrderTaxSplitManager orderTaxSplitManager;
    @RpcConsumer
    ThirdPartyJobService thirdPartyJobService;
    // 设置方法版本
    Map<String, String> methodVersion = new HashMap<>();
    Set<Long> forceNewVersionSet = new HashSet<>();

    HashSet<String> skipDeclareDepotCustomCodeHashSet = new HashSet<>();

    @Autowired
    PayerInfoReadService payerInfoReadService;


    @EventListener(SkipDeclareDepotCodeRegisterEvent.class)
    public void registerSkipDeclareDepotCode(SkipDeclareDepotCodeRegisterEvent skipDeclareDepotCodeRegisterEvent) {
        if (skipDeclareDepotCodeRegisterEvent.isRegister()) {
            skipDeclareDepotCustomCodeHashSet.add(skipDeclareDepotCodeRegisterEvent.getDepotCode());
        } else {
            skipDeclareDepotCustomCodeHashSet.remove(skipDeclareDepotCodeRegisterEvent.getDepotCode());
        }
    }

    /**
     * 版本切换
     *
     * @param methodSwitchNotify 版本切换通知
     */
    @EventListener(MethodSwitchNotify.class)
    public void changeMethodVersion(MethodSwitchNotify methodSwitchNotify) {
        String className = OrderDeclareServiceImpl.class.getSimpleName();
        if (methodSwitchNotify.className().equals(className)) {
            methodVersion.put(methodSwitchNotify.methodName(), methodSwitchNotify.methodVersion());
        }
    }

    /**
     * 强制设置某个店使用新版本数据
     *
     * @param forceNewVersionByShopIdNotifyEvent 强制设置的通知
     */
    @EventListener(ForceNewVersionByShopIdNotifyEvent.class)
    public void ForceNewVersionSetBeNotified(ForceNewVersionByShopIdNotifyEvent forceNewVersionByShopIdNotifyEvent) {
        if (forceNewVersionByShopIdNotifyEvent.force())
            forceNewVersionSet.add(forceNewVersionByShopIdNotifyEvent.shopId());
        else
            forceNewVersionSet.remove(forceNewVersionByShopIdNotifyEvent.shopId());
    }
}