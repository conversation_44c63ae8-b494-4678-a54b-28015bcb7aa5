package moonstone.web.core.order.convert;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentOrder;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentOrderSku;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.ShareUtils;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.dto.PushOrderItem;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.SkuOrderShippingWarehouseTypeEnum;
import moonstone.order.enu.Y800V3PayCodeEnum;
import moonstone.order.model.*;
import moonstone.order.service.OutIdShopOrderReadService;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.User;
import moonstone.user.service.PayerInfoReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.order.OrderTaxSplitManager;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.order.service.impl.PushSystemDep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.Key;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * api-v3 发货单创建-模型转换
 */
@Slf4j
@Component
public class Y800V3DeliveryCreateConvertor {

    @Autowired
    private Y800OrderIdGenerator y800OrderIdGenerator;

    @Resource
    private ReceiverInfoReadService receiverInfoReadService;

    @Resource
    private OutIdShopOrderReadService outIdShopOrderReadService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    @Resource
    private UserReadService userReadService;;

    @Resource
    private OrderTaxSplitManager orderTaxSplitManager;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private PushSystemDep pushSystemDep;

    //一般贸易的订单
    private static final String BOND_TYPE_GENERAL = "general";
    //保税订单
    private static final String BOND_TYPE_BOND = "bond";
    //保税和完税商品的订单
    private static final String BOND_TYPE_MIX = "mix";

    public Y800ShipmentOrder convert(String whCode, String accessCode, ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        Y800ShipmentOrder target = new Y800ShipmentOrder();

        //"WEBSC"
        target.setSourcePlatform(pushSystemDep.getY800SourcePlatformV3());
        target.setAccessCode(accessCode);
        target.setWhCode(whCode);
        target.setThirdNo(y800OrderIdGenerator.getDeclareId(shopOrder));

        if (Objects.equals(OrderOutFrom.EXCEL_IMPORT.Code(), shopOrder.getOutFrom())) {
            outIdShopOrderReadService.findByShopOrderIdWithLimit(shopOrder.getId(), 1).orElseGet(ArrayList::new)
                    .stream().findFirst().map(OutIdShopOrder::getOutId).ifPresent(target::setSourceOrderNo);
        }

        //贸易类型
        target.setBondType(getBondType(skuOrderList));

        //收件人信息
        appendReceiverInfo(target, shopOrder);

        //支付相关的信息（跨境和混合模式订单必填）
        if (!target.getBondType().equals(BOND_TYPE_GENERAL)) {
            appendPaymentInfo(target, shopOrder, skuOrderList);
        }

        //商品信息
        target.setSkuList(convert(shopOrder, skuOrderList, target.getBondType()));

        //快递公司代码
        Optional.ofNullable(shopOrder.getExtra())
                .map(extra -> extra.get(ShopOrderExtra.shipCorp.name()))
                .ifPresent(target::setLogisticsCode);

        return target;
    }

    private void appendReceiverInfo(Y800ShipmentOrder target, ShopOrder shopOrder) {
        ReceiverInfo receiverInfo = receiverInfoReadService.findByOrderId(shopOrder.getId(), OrderLevel.SHOP).getResult().get(0);
        if (receiverInfo == null) {
            log.warn("Y800V3DeliveryCreateConvertor.appendPaymentInfo 收件人信息查询结果为空，shopOrderId={}", shopOrder.getId());
            return;
        }

        target.setReceiverMobile(receiverInfo.getMobile());
        target.setReceiverName(receiverInfo.getReceiveUserName());
        target.setReceiverProvince(receiverInfo.getProvince());
        target.setReceiverCity(receiverInfo.getCity());
        target.setReceiverDistrict(receiverInfo.getRegion());
        target.setReceiverDetailAddress(receiverInfo.getDetail());
    }

    /**
     * 填充支付相关的信息
     *
     * @param target
     * @param shopOrder
     * @param skuOrderList
     */
    private void appendPaymentInfo(Y800ShipmentOrder target, ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        //支付单的信息
        var payment = findPayment(shopOrder, skuOrderList);
        if (payment == null) {
            log.warn("Y800V3DeliveryCreateConvertor.appendPaymentInfo 支付单信息查询结果为空，shopOrderId={}", shopOrder.getId());
            return;
        }

        target.setPayCode(getPayCode(payment));
        target.setPayNo(payment.getPaySerialNo());
        target.setPayAmount(new BigDecimal(shopOrder.getFee())
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .toPlainString());

        //支付人的信息
        var payerInfos = payerInfoReadService.findByOrderIds(Lists.newArrayList(shopOrder.getId())).getResult();
        if (CollectionUtils.isEmpty(payerInfos)) {
            log.warn("Y800V3DeliveryCreateConvertor.appendPaymentInfo 支付人信息查询结果为空，shopOrderId={}", shopOrder.getId());
            return;
        }
        Key key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
        PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(key, payerInfos.get(0).info());

        User buyerUser = (User) userReadService.findById(shopOrder.getBuyerId()).getResult();

        target.setPayerName(info.getName());
        target.setPayerNo(info.getNo());
        if (buyerUser != null) {
            target.setPayerMobile(buyerUser.getMobile());
        }
    }

    /**
     * 由payment的channel映射到 api-v3 的支付平台编码
     *
     * @param payment
     * @return
     */
    private String getPayCode(Payment payment) {
        if (payment == null) {
            return null;
        }

        if (!environmentConfig.isOnline() && PaymentChannelEnum.MOCK_PAY.getCode().equals(payment.getChannel())) {
            log.info("paymentId={}, 非线上环境的模拟支付，统一传联动={}", payment.getId(), Y800V3PayCodeEnum.LIAN_DONG.getCode());
            return Y800V3PayCodeEnum.LIAN_DONG.getCode();
        }

        Y800V3PayCodeEnum y800V3PayCode = Y800V3PayCodeEnum.from(PaymentChannelEnum.from(payment.getChannel()));
        if (y800V3PayCode == null) {
            return null;
        }

        return y800V3PayCode.getCode();
    }

    /**
     * 查询对应的支付单
     *
     * @param shopOrder
     * @param skuOrderList
     * @return
     */
    public Payment findPayment(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        var payments = paymentReadService.findByOrderIdAndOrderLevel(shopOrder.getId(), OrderLevel.SHOP).getResult();
        payments = CollectionUtils.isEmpty(payments) ? payments :
                payments.stream().filter(payment -> OrderStatus.PAID.getValue() == payment.getStatus()).toList();
        if (!CollectionUtils.isEmpty(payments)) {
            return payments.get(0);
        }

        payments = paymentReadService.findByOrderIdAndOrderLevel(skuOrderList.get(0).getId(), OrderLevel.SKU).getResult();
        payments = CollectionUtils.isEmpty(payments) ? payments :
                payments.stream().filter(payment -> OrderStatus.PAID.getValue() == payment.getStatus()).toList();
        if (CollectionUtils.isEmpty(payments)) {
            return null;
        }

        return payments.get(0);
    }

    /**
     * bond-商品均为保税订单,<br/>
     * general-商品均为一般贸易的订单, <br/>
     * mix-商品包含保税和完税商品的订单,<br/>
     * 但当前系统是会把 保税商品和完税商品进行拆单的，即一个shopOrder要么是保税，要么是完税，不存在mix
     *
     * @param skuOrderList
     * @return
     */
    private String getBondType(List<SkuOrder> skuOrderList) {
        if (CollectionUtils.isEmpty(skuOrderList)) {
            return BOND_TYPE_GENERAL;
        }

        return skuOrderList.stream().allMatch(skuOrder -> BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) ?
                BOND_TYPE_BOND : BOND_TYPE_GENERAL;
    }

    /**
     * 老旧的订单打包 仅仅保证可用
     *
     * @param skuOrderList 单品订单列表
     */
    private Y800ShipmentOrderSku[] convert(ShopOrder shopOrder, List<SkuOrder> skuOrderList, String bondType) {
        List<Y800ShipmentOrderSku> orderSkuList = new ArrayList<>(skuOrderList.size());

        long realShopOriginFee = skuOrderList.stream().mapToLong(SkuOrder::getOriginFee).sum();
        List<BigDecimal> shareDiscount = getShareDiscount(shopOrder, skuOrderList);

        for (int i = 0; i < skuOrderList.size(); i++) {
            SkuOrder skuOrder = skuOrderList.get(i);

            Y800ShipmentOrderSku target = new Y800ShipmentOrderSku();
            target.setSkuNo(skuOrder.getOuterSkuId());
            target.setNum(skuOrder.getQuantity() + "");

            //跨境和混合模式订单必填
            if (!BOND_TYPE_GENERAL.equals(bondType)) {
                PushOrderItem pushOrderItem;
                if (Objects.equals(skuOrder.getShippingWarehouseType(), SkuOrderShippingWarehouseTypeEnum.JD_CLOUD_TRANSACTION.getValue())) {
                    // 京东云的不拆税金
                    pushOrderItem = buildJdCloudPushOrderItem(skuOrder);
                } else {
                    pushOrderItem = orderTaxSplitManager.makePushOrderItem_M(skuOrder, shareDiscount.get(i),
                        shopOrder.getShipFee().longValue(), realShopOriginFee).take();
                }
                appendAmountInfo(target, pushOrderItem, skuOrder);
            }

            orderSkuList.add(target);
        }

        return orderSkuList.toArray(new Y800ShipmentOrderSku[]{});
    }

    private PushOrderItem buildJdCloudPushOrderItem(SkuOrder skuOrder) {
        PushOrderItem pushOrderItem = new PushOrderItem();
        // 1. 获取本地Sku单品的映射数量
        int unitQuantity = 1;
        if (!ObjectUtils.isEmpty(skuOrder.getExtra().get("unitQuantity"))) {
            unitQuantity = Integer.parseInt(skuOrder.getExtra().get("unitQuantity"));
        }
        pushOrderItem.setPrice(BigDecimal.valueOf(skuOrder.getOriginFee())
                .divide(BigDecimal.valueOf((long) skuOrder.getQuantity() * unitQuantity), 3, RoundingMode.DOWN)
                .divide(BigDecimal.valueOf(100), 3, RoundingMode.DOWN));
        pushOrderItem.setNum(skuOrder.getQuantity() * unitQuantity);
        return pushOrderItem;
    }

    private void appendAmountInfo(Y800ShipmentOrderSku target, PushOrderItem pushOrderItem, SkuOrder skuOrder) {
        if (pushOrderItem == null) {
            log.warn("Y800V3DeliveryCreateConvertor 计算 orderTaxSplitManager.makePushOrderItem_M 结果为空, shopOrderId={}, skuOrderId={}",
                    skuOrder.getOrderId(), skuOrder.getId());
            return;
        }

        target.setGoodsAmount(pushOrderItem.getPrice() == null ? "0" :
                pushOrderItem.getPrice().multiply(new BigDecimal(pushOrderItem.getNum())).toPlainString());

        target.setTax(pushOrderItem.getTax() == null ? "0" : pushOrderItem.getTax().toPlainString());
        target.setDiscount(pushOrderItem.getDiscount() == null ? "0" : pushOrderItem.getDiscount().toPlainString());
        target.setShipFee(pushOrderItem.getShipFee() == null ? "0" : pushOrderItem.getShipFee().toPlainString());
        target.setNum(pushOrderItem.getNum() + "");
    }

    private List<BigDecimal> getShareDiscount(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        Integer shopDiscount = shopOrder.getDiscount() == null ? 0 : shopOrder.getDiscount();
        Integer shipDiscount = shopOrder.getOriginShipFee() - shopOrder.getShipFee();
        BigDecimal needShareDiscount = BigDecimal.valueOf(shopDiscount + shipDiscount).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);

        List<BigDecimal> originPrice = new ArrayList<>();
        for (SkuOrder skuOrder : skuOrderList) {
            originPrice.add(BigDecimal.valueOf(skuOrder.getOriginFee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
        }

        return ShareUtils.share(needShareDiscount, originPrice, 1);
    }
}
