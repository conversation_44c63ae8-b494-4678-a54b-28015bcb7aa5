package moonstone.web.core.order.convert;

import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.RefundStatusView;
import moonstone.order.enu.RefundExtraIndexEnum;
import moonstone.order.enu.RefundReasonType;
import moonstone.order.enu.Y800V3DeliveryConfirmCallbackTypeEnum;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.result.RefundItemDO;
import moonstone.web.core.order.dto.RefundDetailVO;
import moonstone.web.core.order.dto.RefundItemVO;
import moonstone.web.core.order.dto.RefundPageVO;
import moonstone.web.core.order.dto.SubStoreRefundOrderVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退款单相关的模型转换
 */
public class RefundConvertor {

    public static RefundDetailVO convert(Refund refund, List<ShopOrder> shopOrderList, List<RefundItemDO> refundItemDOS) {
        if (refund == null) {
            return null;
        }

        RefundDetailVO target = new RefundDetailVO();

        String applyString = refund.getExtra().get("apply");
        if (StringUtils.isNotBlank(applyString)) {
            target.setApplyTime(DateUtil.toString(new Date(Long.parseLong(applyString.split("@")[1]))));
        }
        if (!CollectionUtils.isEmpty(shopOrderList)) {
            target.setDeclareId(shopOrderList.stream().map(ShopOrder::getDeclaredId).collect(Collectors.joining(",")));
        }
        target.setFee(new BigDecimal(refund.getFee()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        target.setItemList(convert(refundItemDOS));

        target.setReason(RefundReasonType.from(refund.getReasonType()).getDesc());
        target.setRefundId(refund.getId().toString());
        target.setRejectInfo(convertRejectInfo(refund));
        target.setStatusString(convertStatusString(refund.getStatus()));
        target.setBuyerNote(refund.getBuyerNote());

        return target;
    }

    private static String convertStatusString(Integer status) {
        if (OrderStatus.getRefundStatusByRefundView(RefundStatusView.IN_PROGRESS).contains(status)) {
            return RefundStatusView.IN_PROGRESS.getDescription();
        } else if (OrderStatus.getRefundStatusByRefundView(RefundStatusView.SUCCESS).contains(status)) {
            return RefundStatusView.SUCCESS.getDescription();
        } else if (OrderStatus.getRefundStatusByRefundView(RefundStatusView.FAILURE).contains(status)) {
            return RefundStatusView.FAILURE.getDescription();
        } else {
            return "Unknown";
        }
    }

    private static RefundDetailVO.RefundRejectVO convertRejectInfo(Refund refund) {
        String rejectString = refund.getExtra().get("reject");
        if (StringUtils.isBlank(rejectString)) {
            return null;
        }

        var target = new RefundDetailVO.RefundRejectVO();
        target.setRejectTime(DateUtil.toString(new Date(Long.parseLong(rejectString.split("@")[1]))));
        target.setSellerNote(refund.getSellerNote());

        return target;
    }

    public static List<RefundPageVO> convert(List<Refund> refundList, Map<Long, List<RefundItemDO>> refundItemMap) {
        if (CollectionUtils.isEmpty(refundList)) {
            return Collections.emptyList();
        }

        return refundList.stream().map(source -> {
            RefundPageVO target = new RefundPageVO();

            target.setRefundId(source.getId().toString());
            target.setFee(new BigDecimal(source.getFee()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            target.setItemList(convert(refundItemMap.get(source.getId())));
            target.setTotalItemQuantity(target.getItemList().stream().mapToLong(RefundItemVO::getQuantity).sum());

            Optional.of(source.getExtra()).ifPresent(extra -> target.setOrderPushErrorMessage(
                    Y800V3DeliveryConfirmCallbackTypeEnum.parseErrorMessage(extra.get(RefundExtraIndexEnum.orderPushErrorType.name()))));

            return target;
        }).collect(Collectors.toList());
    }

    public static List<RefundItemVO> convert(List<RefundItemDO> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        return sourceList.stream().map(source -> {
            RefundItemVO target = new RefundItemVO();

            target.setItemName(source.getItemName());
            target.setQuantity(source.getQuantity());
            target.setSkuImage(ImageUrlHandler.complete(source.getSkuImage()));
            target.setOriginUnitFee(new BigDecimal(source.getOriginFee()).divide(
                    new BigDecimal(source.getQuantity() * 100), 2, RoundingMode.HALF_UP));

            return target;
        }).collect(Collectors.toList());
    }

    public static SubStoreRefundOrderVO convert(Refund refund, ShopOrder shopOrder, List<RefundItemDO> itemList,
                                                Map<Long, String> shopOrderSubStoreNameMap,
                                                Map<Long, List<OrderRoleSnapshot>> orderRoleSnapshotMap) {
        SubStoreRefundOrderVO target = new SubStoreRefundOrderVO();
        if (refund == null) {
            return target;
        }

        if (shopOrder != null) {
            target.setDeclaredId(shopOrder.getDeclaredId());
            target.setFee(shopOrder.getFee());
            target.setOrderCreateAt(shopOrder.getCreatedAt().getTime());

            if (shopOrderSubStoreNameMap != null) {
                target.setSubStoreName(shopOrderSubStoreNameMap.get(shopOrder.getId()));
            } else if (orderRoleSnapshotMap != null && !CollectionUtils.isEmpty(orderRoleSnapshotMap.get(shopOrder.getId()))) {
                //快照兜底
                orderRoleSnapshotMap.get(shopOrder.getId())
                        .stream()
                        .filter(snapshot -> SubStoreUserIdentityEnum.SUB_STORE.getCode().equals(snapshot.getUserRole()))
                        .findAny()
                        .ifPresent(snapshot -> target.setSubStoreName(snapshot.getName()));
            }
        }

        if (!CollectionUtils.isEmpty(itemList)) {
            target.setItemList(itemList.stream().map(sourceItem -> {
                SubStoreRefundOrderVO.SubStoreRefundOrderItemVO targetItem = new SubStoreRefundOrderVO.SubStoreRefundOrderItemVO();
                targetItem.setItemImageUrl(ImageUrlHandler.complete(sourceItem.getSkuImage()));
                targetItem.setItemName(sourceItem.getItemName());
                targetItem.setItemNum(sourceItem.getQuantity().intValue());

                return targetItem;
            }).collect(Collectors.toList()));
        }

        target.setRefundId(refund.getId());
        target.setRefundStatus(refund.getStatus());
        target.setRefundStatusName(OrderStatus.fromInt(refund.getStatus()).intoString());

        Optional.of(refund.getExtra()).ifPresent(extra -> target.setOrderPushErrorMessage(
                Y800V3DeliveryConfirmCallbackTypeEnum.parseErrorMessage(extra.get(RefundExtraIndexEnum.orderPushErrorType.name()))));

        return target;
    }
}
