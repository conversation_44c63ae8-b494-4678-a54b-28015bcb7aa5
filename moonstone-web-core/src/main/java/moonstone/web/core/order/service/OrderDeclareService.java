package moonstone.web.core.order.service;

import moonstone.order.model.Payment;

import java.util.Optional;

public interface OrderDeclareService {

    /**
     * 支付信息推送至洋800
     */
    void payInfoPushV1(Payment payment);

    /**
     * 海关支付单推送至mercury-pay工程
     */
    void paymentDeclare(Payment payment);

    /**
     * 检测订单是否需要延迟
     *
     * @param payment 订单
     * @return 是否成功
     */
    Boolean needDelay(Payment payment);

    /**
     * 检测支付单推送状态是否同步
     * 不同步则返回一个更新的payment
     *
     * @param payment 被检测的payment
     * @return 需要更新的Payment
     */
    Optional<Payment> syncPaymentPushStatus(Payment payment);
}
