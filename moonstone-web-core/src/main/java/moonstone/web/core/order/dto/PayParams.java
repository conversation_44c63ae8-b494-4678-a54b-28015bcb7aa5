package moonstone.web.core.order.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Author:cp
 * Created on 9/12/16.
 */
@Data
public class PayParams implements Serializable {

    private static final long serialVersionUID = -4079603486664032735L;

    /**
     * 支付渠道
     */
    @NotNull
    private String channel;
    /**
     * 平台级的营销活动, 可空
     */
    private Long promotionId;
    /**
     * (子)订单id列表
     */
    @NotNull
    private List<Long> orderIds;
    /**
     * 订单级别
     */
    private Integer orderType = 1;
    /**
     * 使用的appId
     */
    private String appId;

    /**
     * @see moonstone.shop.enums.ShopPayInfoUsageChannelEnum
     */
    private Integer usageChannel;
}