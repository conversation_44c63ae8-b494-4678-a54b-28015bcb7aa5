package moonstone.web.core.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Author:  CaiZhy
 * Date:    2019/3/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JsapiWechatpayParams extends PayParams implements Serializable {
    private static final long serialVersionUID = -8384664459652667822L;

    /**
     * 微信公众号支付时传的授权code
     */
    @NotNull
    private String wxCode;
}
