package moonstone.web.core.order.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RefundPageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8036111197624396830L;

    /**
     * 退款单主键 (退款单号)
     */
    private String refundId;

    /**
     * 退款金额
     */
    private BigDecimal fee;

    /**
     * 商品总数量
     */
    private Long totalItemQuantity;

    /**
     * 订单推送异常描述
     */
    private String orderPushErrorMessage;

    /**
     * 退款商品列表
     */
    private List<RefundItemVO> itemList;
}
