package moonstone.web.core.order.service;

import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.web.core.component.api.Y800V3Api;

import java.util.List;
import java.util.Optional;

public interface Y800V3CustomsClearancePushService {

    /**
     * 是否为 api-v3 接口的订单
     *
     * @param skuOrders
     * @return
     */
    boolean isY800V3Order(List<SkuOrder> skuOrders);

    /**
     * 是否为 api-v3 接口的订单
     *
     * @param shopOrder
     * @return
     */
    boolean isY800V3Order(ShopOrder shopOrder);

    /**
     * api-v3 接口的订单进行清关信息推送
     *
     * @param payment
     * @param shopOrderList
     * @param skuOrderList
     */
    @Deprecated
    void pushV3CustomsClearanceInfo(Payment payment, List<ShopOrder> shopOrderList, List<SkuOrder> skuOrderList);

    /**
     * 单一主订单推送清关信息，并更新主订单上的相关标记
     *
     * @param payment
     * @param shopOrder
     * @param apiV3
     */
    void pushSingleOrder(Payment payment, ShopOrder shopOrder, Y800V3Api apiV3);
}
