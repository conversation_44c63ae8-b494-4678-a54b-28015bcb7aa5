package moonstone.web.core.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.order.dto.SkuOrderForExportView;

/**
 * 真实订单export的实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderExportView extends SkuOrderForExportView {
    //购买人Id
    private Long buyerId = null;
    //店铺名
    private String shopName = null;
    //订单号 skuOrderId
    private Long orderId = null;
    //订单状态
    private String status = null;
    private Boolean firstOrder = null;
    //支付流水号
    private String paySerialNo = null;
    //下单时间
    private String createAt = null;
    //支付时间
    private String paidAt = null;
    private String confirmAt = null;
    //买家
    private String buyerName = null;
    //买家微信昵称
    private String wxBuyerName = null;
    //收件人
    private String receiveUserName = null;
    //手机号
    private String mobile = null;
    //收货地址
    private String address = null;
    //接下来的信息
    //抵扣金额
    //总价
    private String fee = null;
    private String outFromName;
    private String declaredNo;

    private String subStoreName;
    private String subStoreProfit;
    private String subStoreWithdrawStatus;
    private String guiderName;
    private String guiderProfit;
    // 额外利润拥有者
    private String linkedProfitOwner;
    // 额外利润
    private Long linkedProfit;
    // 额外的额外的利润
    private Long exLinkedProfit;
    private String payerName;
    private String payerNo;

    private String categoryName;

    /**
     * 支付单号
     */
    private String paymentOutId;
}
