package moonstone.web.core.order.dto;

import lombok.Data;

/**
 * Created by Administrator on 2018/3/14.
 */
@Data
public class FinanceContrastDto {

    private String id;
    /**
     * 应用类型
     */
    private int appType;
    /**
     * 业务类型
     */
    private int bussinessType;
    /**
     * 编码(根据业务类型不同，存放不同编码)
     */
    private String sn;
    /**
     * 关联单号（根据业务类型不同，存放不同需要记录的编码）
     */
    private String relevanceSn;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 支付方式
     */
    private int payWay;
    /**
     * 收入
     */
    private long income;
    /**
     * 支出
     */
    private long expenses;
    /**
     * 第三方单号
     */
    private String outSn;
    /**
     * 备注
     */
    private String remark;
    /**
     * 外部系统数据创建时间
     */
    private long outCreateTime;
    /**
     * 外部系统数据更新时间
     */
    private long outUpdateTime;
    /**
     * 终端类型(包括：1-pc  2-app  3-h5)
     */
    private int terminalType;
}
