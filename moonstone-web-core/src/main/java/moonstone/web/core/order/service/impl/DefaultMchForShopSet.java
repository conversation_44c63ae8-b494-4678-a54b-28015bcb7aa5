package moonstone.web.core.order.service.impl;

interface DefaultMchForShopSet {
    /**
     * 获取默认的mch code
     *
     * @param shopId 店铺Id
     * @return 默认配置的mch
     */
    static String defaultMchForShop(Long shopId) {
        String mchCode = null;
        switch (shopId.intValue()) {
            case 28:
                mchCode = "M2019061811164883471";
                break;
            case 29:
                mchCode = "M2019062419552707157";
                break;
            case 30:
                mchCode = "M2019071018161924277";
                break;
            case 31:
                mchCode = "M2019062618072780930";
                break;
            case 32:
                mchCode = "M2019062710382940767";
                break;
            case 33:
                mchCode = "M2019071215584334760";
                break;
            case 35:
                mchCode = "M2019121009353582553";
                break;
        }
        return mchCode;
    }
}
