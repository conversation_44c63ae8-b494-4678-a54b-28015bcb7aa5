package moonstone.web.core.order.web;

import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.PushOneOrderResult;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.core.events.thirdParty.ThirdPartyPushOrderErrorEvent;
import moonstone.web.core.order.service.OrderPushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/order/pushSystem")
public class PushSystems {
    @Autowired
    private OrderPushService orderPushService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private UserTypeBean userTypeBean;

    @RequestMapping(value = "/{id}/repush", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean repush(@PathVariable("id") Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByShopOrderId(id);
        if (!rSkuOrders.isSuccess()) {
            log.error("failed to find sku orders by shop order id={}, error code:{}", id, rSkuOrders.getError());
            throw new JsonResponseException(rSkuOrders.getError());
        }
        List<SkuOrder> skuOrders = rSkuOrders.getResult();
        //判断用户是否有重推权限
        for (SkuOrder skuOrder : skuOrders) {
            if (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser) && !Objects.equal(skuOrder.getShopId(), commonUser.getShopId())) {
                log.error("user(id={}) is not allowed to repush sku order(id={})", commonUser.getId(), id);
                throw new JsonResponseException("sku.order.repush.not.allowed");
            }

            //判断订单是否允许重推
            if (skuOrder.getStatus() != OrderStatus.PAID.getValue()) {
                log.error("sku order(id={}) status is not PAID", id);
                throw new JsonResponseException("sku.order.not.paid");
            }
            /*if (skuOrder.getPushStatus() == SkuOrderPushStatus.FINISHED.value()){
                log.error("sku order(id={}) has been pushed successfully, it do not need repush.");
                throw new JsonResponseException("sku.order.push.has.success");
            }*/
        }

        Either<Boolean> r = orderPushService.updatePushStatusByOrderId(id, SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.ERROR.value());
        if (!r.isSuccess()) {
            log.error("failed to rePush skuOrder by shopOrderId={}, error code:{}",
                    id, r.getError());
            throw new JsonResponseException(r.getError());
        }
        return r.getResult();
    }

    @RequestMapping(value = "/{id}/push", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public PushOneOrderResult push(@PathVariable("id") Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        SkuOrder skuOrder = skuOrderReadService.findById(id).getResult();
        //判断用户是否有推单权限
        if (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser) && !Objects.equal(skuOrder.getShopId(), commonUser.getShopId())) {
            log.error("user(id={}) is not allowed to push sku order(id={})", commonUser.getId(), id);
            throw new JsonResponseException("sku.order.push.not.allowed");
        }

        //判断订单是否允许推单
        if (skuOrder.getStatus() != OrderStatus.PAID.getValue()) {
            log.error("sku order(id={}) status is not PAID", id);
            throw new JsonResponseException("sku.order.not.paid");
        }
        switch (SkuOrderPushStatus.fromInt(skuOrder.getPushStatus())) {
            case WAITING_SELLER_AUTH: {
                log.error("sku order(id={}) need seller auth before push.", skuOrder.getId());
                throw new JsonResponseException("sku.order.need.auth.before.push");
            }
            case ERROR: {
                log.error("sku order(id={}) has been pushed fail, need repush first.", skuOrder.getId());
                throw new JsonResponseException("sku.order.push.has.fail");
            }
               /* case FINISHED: {
                    log.error("sku order(id={}) has been pushed successfully, it do not need push.");
                    throw new JsonResponseException("sku.order.push.has.success");
                }*/
        }

        Boolean result = orderPushService.pushOrder(skuOrder.getOrderId());
        if (!result) {
            log.error("failed to push order(id={})", id);

            List<Long> errorOrderIds = new ArrayList<>();
            errorOrderIds.add(id);
            EventSender.send(new ThirdPartyPushOrderErrorEvent(errorOrderIds, this.getClass().getName(), this.getClass().getSimpleName() + ".push.fail"));

            Response<List<SkuOrder>> rAfterPushSkuOrders = skuOrderReadService.findByShopOrderId(id);
            if (!rAfterPushSkuOrders.isSuccess()) {
                log.error("failed to find sku orders by order id={}, error code: {}", id, rAfterPushSkuOrders.getError());
                throw new JsonResponseException("sku.order.push.error.msg.find.fail");
            }
            String pushErrorMsg = null;
            if (skuOrder.getPushStatus() == SkuOrderPushStatus.ERROR.value() && skuOrder.getPushErrorMsg() != null) {
                pushErrorMsg = skuOrder.getPushErrorMsg();
            }
            return PushOneOrderResult.fail(pushErrorMsg);
        }
        return PushOneOrderResult.ok();
    }
}
