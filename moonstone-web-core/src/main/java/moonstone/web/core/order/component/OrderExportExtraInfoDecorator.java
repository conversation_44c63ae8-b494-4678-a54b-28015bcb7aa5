package moonstone.web.core.order.component;

import io.vertx.core.Future;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.order.dto.OrderGroup;
import moonstone.order.model.ProfitWithdrawRecord;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.web.core.order.dto.OrderExportView;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import org.apache.ibatis.session.SqlSession;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiConsumer;

@Slf4j
@Component
public record OrderExportExtraInfoDecorator(ShopReadService shopReadService, Vertx vertx, SqlSession sqlSession) {

    /**
     * 修饰导出的外部推送订单号
     *
     * @param outOrderIdMap 订单号Map
     * @return 修饰器
     */
    public BiConsumer<Row, OrderExportView> decorateOrderId(Map<Long, String> outOrderIdMap) {
        return (row, view) -> {
            try {
                Cell cell = row.createCell(row.getLastCellNum());
                if (view == null) {
                    cell.setCellValue("外部订单号");
                } else {
                    if (ObjectUtils.isEmpty(outOrderIdMap.get(view.getOrderId()))) {
                        return;
                    }
                    cell.setCellValue(outOrderIdMap.get(view.getOrderId()));
                }
            } catch (Exception e) {
                log.error("{} fail to fill OrderOutId [OrderId => {}] at export", LogUtil.getClassMethodName(), view == null ? "null" : view.getOrderId(), e);
            }
        };
    }

    /**
     * 添加额外导入订单数据
     *
     * @param sheetAt       sheet
     * @param outOrderIdMap 外部Id映射
     */
    public void addExportOrderId(Sheet sheetAt, Map<Long, String> outOrderIdMap) {
        boolean create = false;
        String orderId = null;
        try {
            for (int i = 1; i <= sheetAt.getLastRowNum() && i < sheetAt.getLastRowNum(); i++) {
                Row row = sheetAt.getRow(i);
                orderId = Optional.ofNullable(row.getCell(0).getStringCellValue()).filter(StringUtils::hasText).orElse(orderId);
                String outOrderId = NumberUtil.parseNumber(orderId, Long.TYPE).map(outOrderIdMap::get).orElse(null);
                if (ObjectUtils.isEmpty(outOrderId)) {
                    continue;
                }
                if (!create) {
                    create = true;
                }
                int lastCellNum = row.getLastCellNum();
                if (row.getCell(lastCellNum) == null) {
                    row.createCell(lastCellNum);
                }
                row.getCell(lastCellNum).setCellValue(outOrderId);
            }
            if (create) {
                Row row = sheetAt.getRow(0);
                int lastCellNum = row.getLastCellNum();
                if (row.getCell(lastCellNum) == null) {
                    row.createCell(lastCellNum);
                }
                row.getCell(lastCellNum).setCellValue(new Translate("外部单号").toString());
            }
        } catch (Exception ex) {
            log.error("{} sheetSize:{} ex", LogUtil.getClassMethodName(), sheetAt.getLastRowNum(), ex);
        }
    }

    /**
     * 修改导出表名
     *
     * @param shopId 平台id
     */
    public BiConsumer<Row, OrderExportView> decorateCellTitleName(long shopId) {
        Map<String, String> shopExtra = Optional.ofNullable(shopReadService.findById(shopId).getResult()).map(Shop::getExtra).orElse(new HashMap<>(8));
        if ("ladderDistribution".equals(shopExtra.getOrDefault("salesPattern", ""))) {
            return (column, data) -> {
                if (data != null) {
                    return;
                }
                int nullCellCount = 0;
                for (int i = column.getFirstCellNum(); nullCellCount < 5 && i <= column.getLastCellNum(); i++) {
                    Cell cell = column.getCell(i);
                    if (cell == null) {
                        nullCellCount++;
                        continue;
                    }
                    if (cell.getStringCellValue() != null && cell.getStringCellValue().contains("门店")) {
                        cell.setCellValue(cell.getStringCellValue().replaceAll("门店", "经销商"));
                    }
                    if (cell.getStringCellValue() != null && cell.getStringCellValue().contains("导购")) {
                        cell.setCellValue(cell.getStringCellValue().replaceAll("导购", "门店"));
                    }
                }
            };
        }
        return ((row, orderExportView) -> {
        });
    }

    /**
     * 修改导出表名
     *
     * @param sheetAt 表格
     * @param shopId  平台id
     */
    public void changeTheColumnName(Sheet sheetAt, long shopId) {
        Map<String, String> shopExtra = Optional.ofNullable(shopReadService.findById(shopId).getResult()).map(Shop::getExtra).orElse(new HashMap<>(8));
        int nullCellCount = 0;
        if ("ladderDistribution".equals(shopExtra.getOrDefault("salesPattern", ""))) {
            Row column = sheetAt.getRow(sheetAt.getFirstRowNum());
            if (column == null) {
                return;
            }
            for (int i = column.getFirstCellNum(); nullCellCount < 5 && i <= column.getLastCellNum(); i++) {
                Cell cell = column.getCell(i);
                if (cell == null) {
                    nullCellCount++;
                    continue;
                }
                if (cell.getStringCellValue() != null && cell.getStringCellValue().contains("门店")) {
                    cell.setCellValue(cell.getStringCellValue().replaceAll("门店", "经销商"));
                }
                if (cell.getStringCellValue() != null && cell.getStringCellValue().contains("导购")) {
                    cell.setCellValue(cell.getStringCellValue().replaceAll("导购", "门店"));
                }
            }
        }
    }

    public BiConsumer<Row, OrderExportView> decorateExProfit(Long shopId) {
        Shop shop = shopReadService.findById(shopId).getResult();
        if (!ShopFunctionSlice.build(shop).isFanSystemEnable()) {
            return (row, view) -> {
            };
        }
        return (row, view) -> {
            short lastCell = row.getLastCellNum();
            int extraIndex = lastCell + 2;
            if (view == null) {
                row.createCell(lastCell).setCellValue(new Translate("忠诚佣金").toString());
                row.createCell((int) lastCell + 1).setCellValue(new Translate("忠诚佣金持有人").toString());
                row.createCell(extraIndex).setCellValue(new Translate("新客佣金").toString());
                return;
            }
            if (view.getLinkedProfit() != null) {
                row.createCell(lastCell).setCellValue(new BigDecimal(view.getLinkedProfit()).divide(new BigDecimal("100"), RoundingMode.DOWN).toString());
                row.createCell((int) lastCell + 1).setCellValue(view.getLinkedProfitOwner());
            }
            if (view.getExLinkedProfit() != null) {
                row.createCell(extraIndex).setCellValue(new BigDecimal(view.getExLinkedProfit()).divide(new BigDecimal("100"), RoundingMode.DOWN).toString());
            }
        };
    }

    /**
     * 为特定平台添加特定的输出数据
     *
     * @param sheet            表
     * @param orderExportViews 数据
     * @param shopId           平台Id
     */
    public void addExProfitColumn(Sheet sheet, List<OrderExportView> orderExportViews, Long shopId) {
        Row column = sheet.getRow(0);
        if (orderExportViews.isEmpty()) {
            return;
        }
        Shop shop = shopReadService.findById(shopId).getResult();
        if (ShopFunctionSlice.build(shop).isFanSystemEnable()) {
            short lastCell = column.getLastCellNum();
            int extraIndex = lastCell + 2;
            column.createCell(lastCell).setCellValue(new Translate("忠诚佣金").toString());
            column.createCell((int) lastCell + 1).setCellValue(new Translate("忠诚佣金持有人").toString());
            column.createCell(extraIndex).setCellValue(new Translate("新客佣金").toString());
            for (int i = 0; i < orderExportViews.size(); i++) {
                if (i < sheet.getLastRowNum()) {
                    Row row = sheet.getRow(i + 1);
                    if (row != null) {
                        OrderExportView orderExportView = orderExportViews.get(i);
                        if (orderExportView.getLinkedProfit() != null) {
                            row.createCell(lastCell).setCellValue(new BigDecimal(orderExportViews.get(i).getLinkedProfit()).divide(new BigDecimal("100"), RoundingMode.DOWN).toString());
                            row.createCell((int) lastCell + 1).setCellValue(orderExportViews.get(i).getLinkedProfitOwner());
                        }
                        if (orderExportView.getExLinkedProfit() != null) {
                            row.createCell(extraIndex).setCellValue(new BigDecimal(orderExportViews.get(i).getExLinkedProfit()).divide(new BigDecimal("100"), RoundingMode.DOWN).toString());
                        }
                    }
                }
            }
        }
    }

    public BiConsumer<Row, OrderExportView> decorateServiceProviderProfit(Long shopId, List<? extends OrderGroup> orderGroup) {
        Shop shop = shopReadService.findById(shopId).getResult();
        int[] index = new int[4];
        index[0] = -1;
        Map<Long, OrderGroup> orderMap = new HashMap<>(orderGroup.size());
        for (OrderGroup group : orderGroup) {
            orderMap.put(group.getShopOrder().getId(), group);
        }
        return ((row, orderExportView) -> {
            // find the order id n
            if (row.getRowNum() == 0) {
                for (int i = row.getFirstCellNum(); i <= row.getLastCellNum(); i++) {
                    if (row.getCell(i).getStringCellValue().contains("订单号")) {
                        index[0] = i;
                        break;
                    }
                }
                // add column
                Cell c = row.createCell(row.getLastCellNum());
                c.setCellValue("服务商名称");
                index[1] = c.getColumnIndex();
                c = row.createCell(row.getLastCellNum());
                c.setCellValue("服务商佣金");
                index[2] = c.getColumnIndex();
                c = row.createCell(row.getLastCellNum());
                c.setCellValue("服务商提现状态");
                index[3] = c.getColumnIndex();
                return;
            }
            // find the order
            Long id = Long.parseLong(row.getCell(index[0]).getStringCellValue());
            OrderGroupViewObject viewObject = (OrderGroupViewObject) orderMap.get(id);
            if (Objects.nonNull(viewObject.getServiceProviderName())) {
                row.createCell(index[1]).setCellValue(viewObject.getServiceProviderName());
            }
            if (Objects.nonNull(viewObject.getServiceProviderProfit())) {
                row.createCell(index[2]).setCellValue(new BigDecimal(viewObject.getServiceProviderProfit()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN).toString());
            }
            try {
                row.createCell(index[3]).setCellValue(queryServiceProviderProfitStatus(((OrderGroupViewObject) orderGroup).getServiceProviderProfitFrom()));
            } catch (Exception ignore) {

            }
        });
    }

    String convertName(JsonObject jsonObject) {
        if (jsonObject == null) {
            return "未提现";
        }
        return switch (jsonObject.getInteger("status")) {
            case 1 -> "审核中";
            case 2 -> "已提现";
            default -> "未知状态";
        };
    }

    private String queryServiceProviderProfitStatus(Long profitId) {
        if (profitId == null) {
            return "";
        }
        return vertx.<ProfitWithdrawRecord>executeBlocking(p->p.complete(sqlSession.selectOne("ProfitWithdrawRecord.findByProfitId",
                        Map.of("profitId", profitId))))
                .map(JsonObject::mapFrom)
                .map(this::convertName)
                .recover(e -> Future.succeededFuture("未提现"))
                .toCompletionStage().toCompletableFuture().join();
    }
}
