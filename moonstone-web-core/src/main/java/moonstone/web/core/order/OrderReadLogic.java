package moonstone.web.core.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.google.common.collect.*;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.SysOperLog;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.FlowPicker;
import moonstone.order.api.OrderAndOperation;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.fsm.*;
import moonstone.order.dto.view.SkuOrderView;
import moonstone.order.enu.OrderFlagEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.ShopOrderIdentityErrorEnum;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.ShopReadService;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.*;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopProfit;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.web.core.component.order.view.ShopOrderVO;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.user.service.StoreProxySubUserManager;
import moonstone.web.core.util.PayInfoVOPacker;
import org.apache.ibatis.session.SqlSession;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * order query logical
 */
@Component
@Slf4j
public record OrderReadLogic(UserReadService<User> userReadService,
							 StoreProxyReadService storeProxyReadService,
							 WeShopReadService weShopReadService,
							 WeShopShopAccountReadService weShopShopAccountReadService,
							 OrderReadService orderReadService,
							 ShopOrderReadService shopOrderReadService,
							 SkuOrderReadService skuOrderReadService,
							 OrderWriteService orderWriteService,
							 ShopReadService shopReadService,
							 SkuReadService skuReadService,
							 SkuCacheHolder skuCacheHolder,
							 PaymentReadService paymentReadService,
							 StoreProxySubUserManager storeProxySubUserManager,
							 UserProfileCacheHolder userProfileCacheHolder,
							 StoreProxyManager storeProxyManager,
							 ShopCacheHolder shopCacheHolder,
							 SubStoreCache subStoreCache,
							 GuiderCache guiderCache,
							 FlowPicker flowPicker,
							 ObjectMapper objectMapper,
							 MongoTemplate mongoTemplate,
							 ServiceProviderCache serviceProviderCache,
							 FirstOrderMarkService firstOrderMarkService,
							 SqlSession sqlSession,
							 RefundReadService refundReadService,
							 SysOperLogWriteService sysOperLogWriteService,
							 UserProfileReadService userProfileReadService,
							 FirstOrderMarkReadService firstOrderMarkReadService
) implements moonstone.order.api.OrderReadLogic {

	@Deprecated
	@Override
	public Response<Paging<OrderGroup>> convertProfitToOrderGroup(Paging<WeShopProfit> weShopProfitPaging) {
		try {
			List<WeShopProfit> weShopProfits = weShopProfitPaging.getData();
			List<Long> shopOrderIds = Lists.transform(weShopProfits, WeShopProfit::getShopOrderId);
			Response<List<ShopOrder>> rShopOrders = shopOrderReadService.findByIds(shopOrderIds);
			if (!rShopOrders.isSuccess()) {
				log.error("failed to find shop orders by ids({}), error code: {}", shopOrderIds, rShopOrders.getError());
				throw new JsonResponseException(rShopOrders.getError());
			}
			Paging<ShopOrder> shopOrderPaging = new Paging<>(weShopProfitPaging.getTotal(), rShopOrders.getResult());
			Response<Paging<OrderGroup>> ordersR = orderReadService.buildOrderGroup(shopOrderPaging);
			if (!ordersR.isSuccess()) {
				log.error("failed to build order group by shop order paging({}), error code: {}", weShopProfitPaging, ordersR.getError());
				throw new JsonResponseException(ordersR.getError());
			}
			Paging<OrderGroup> orderGroupPaging = ordersR.getResult();

			for (OrderGroup orderGroup : orderGroupPaging.getData()) {
				try {
					Flow flow = flowPicker.pick(orderGroup.getShopOrder(), OrderLevel.SHOP);
					List<Long> skuIds = orderGroup.getSkuOrderAndOperations().stream().map(input -> ((SkuOrder) input.order()).getSkuId()).collect(Collectors.toList());

					Response<List<Sku>> skuRes = skuReadService.findSkusByIds(skuIds);
					if (!skuRes.isSuccess()) {
						log.error("fail to find sku  by ids {} for order paging error:{}",
								skuIds, skuRes.getError());
						continue;
					}

					ImmutableMap<Long, Sku> skuIdAndSkuMap = Maps.uniqueIndex(skuRes.getResult(), sku -> Objects.isNull(sku) ? 0L : sku.getId());

					List<OrderAndOperation> skuOperations = new ArrayList<>(orderGroup.getSkuOrderAndOperations().size());
					for (var skuOrderAndOperation : orderGroup.getSkuOrderAndOperations()) {
						SkuOrder skuOrder = (SkuOrder) skuOrderAndOperation.order();
						var sku = skuIdAndSkuMap.get(skuOrder.getSkuId());
						var operations = flow.availableOperations(skuOrder.getStatus());
						if (Optional.ofNullable(orderGroup.getShopOrder().getExtra()).orElse(Collections.emptyMap())
								.getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
								.equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
							operations = skuOrderAndOperation.operation().stream()
									.filter(op -> !op.getText().equals("pay")).collect(Collectors.toSet());
						}
						skuOperations.add(new OrderGroup.SkuOrderAndOperation(skuOrder, operations, sku,
								OrderFlagEnum.getFrontEndFlagCodes(skuOrder.getFlag())));
					}
					orderGroup.setSkuOrderAndOperations(skuOperations);
					//确定店铺订单可以执行的操作
					//如果是根据状态筛选,那归组出来的子订单可能不能构成一个总单,这个时候就要以数据库真实数据为准
					//如果不根据状态筛选, 由于订单列表查询的时候只会返回有限数量的子订单,所以也要重新找一把
					orderGroup.setShopOrderOperations(
							pickCommonSkuOperation(orderGroup.getSkuOrderAndOperations().stream().map(op -> (SkuOrder) op.order()).collect(Collectors.toList())
									, flow));
					if (Optional.ofNullable(orderGroup.getShopOrder().getExtra()).orElse(Collections.emptyMap())
							.getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
							.equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
						orderGroup.setShopOrderOperations(orderGroup.getShopOrderOperations().stream()
								.filter(op -> !op.getText().equals("pay")).collect(Collectors.toSet()));
					}
				} catch (Exception e) {
					log.error("fail to find order operations by orderGroup({}), skip it. cause: ",
							JSON.toJSONString(orderGroup), e);
				}
			}

			return Response.ok(orderGroupPaging);
		} catch (Exception e) {
			log.error("failed to convert profit to group order by weShop profit paging({}), cause: {}",
					weShopProfitPaging, Throwables.getStackTraceAsString(e));
			throw new JsonResponseException(e.getMessage());
		}
	}

	/**
	 * 根据搜索条件进行微调
	 * 如以店铺与手机号查询
	 * 如阶梯分销的特定查询
	 * 如权限管理的数据隔离
	 *
	 * @param criteria 查询条件
	 * @return 是否进行真实查询
	 */
	private Optional<OrderCriteria> prefixSearchForCriteria(OrderCriteria criteria) {
		// shopId 小于0 则不进行进一步筛选处理, 因为数据一定不存在
		if (criteria == null || Optional.ofNullable(criteria.getShopId()).filter(shopId -> shopId < 0).isPresent()) {
			return Optional.empty();
		}
		criteria.setOutFrom(ObjectUtils.isEmpty(criteria.getOutFrom()) ? null : criteria.getOutFrom());

		// 读取用户手机号
		Optional.ofNullable(criteria.getMobile()).map(mobile -> userReadService.findBy(mobile, LoginType.MOBILE).getResult())
				.map(User::getId).ifPresent(criteria::setBuyerId);
		Optional.ofNullable(criteria.getShopName()).map(shopReadService::findByName).map(Response::getResult)
				.map(Shop::getId).ifPresent(criteria::setShopId);

		switch (OrderOutFrom.fromCode(criteria.getOutFrom())) {
			case LEVEL_Distribution -> decorateCriteriaWithSubShopIdByStoreProxy(criteria);
			case WE_SHOP -> decorateCriteriaWithSubShopIdByWeShop(criteria);
			default -> {
			}
		}

		// 根据店铺Id 与 子店铺名称 处理 查询下级店铺
		decorateByShopIdAndSubShopName(criteria.getShopId(), criteria.getSubShopName(), criteria);
		// 筛选了不存在的值
		if (criteria.getOutShopIds() != null && criteria.getOutShopIds().isEmpty()) {
			if (StrUtil.isNotBlank(criteria.getOutShopId())) {
				criteria.getOutShopIds().add(criteria.getOutShopId());
				criteria.setOutShopId(null);
				return Optional.of(criteria);
			}
		}
		log.debug("{} after prefix search criteria transform into [{}]", LogUtil.getClassMethodName(), criteria.toMap());
		return Optional.of(criteria);
	}

	/**
	 * 根据下级店铺名称设置查询的订单来源
	 *
	 * @param shopId      店铺Id
	 * @param subShopName 店铺名称
	 * @param criteria    查询条件
	 */
	private void decorateByShopIdAndSubShopName(Long shopId, String subShopName, OrderCriteria criteria) {
		if (ObjectUtils.isEmpty(subShopName) || Objects.isNull(shopId)) {
			return;
		}
		String salePattern = Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra())
				.map(extra -> extra.get(ShopExtra.SalesPattern.getCode()))
				.orElse(SalePattern.Common.getCode());
		// query the shop salePattern
		Map<String, Consumer<String>> orderQueryCriteriaBySalePattern =
				ImmutableMap.of(SalePattern.WeShop.getCode(), weShopName -> {
							criteria.setOutShopIds(weShopReadService.findLikeName(weShopName).getResult()
									.stream().map(WeShop::getId)
									.filter(id -> weShopShopAccountReadService.findByWeShopIdAndShopId(id, shopId).getResult() != null).map(Object::toString)
									.collect(Collectors.toList()));
							if (criteria.getOutShopIds().size() == 1) {
								criteria.setOutShopId(criteria.getOutShopIds().get(0));
								criteria.setOutShopIds(null);
							}
						},
						SalePattern.StoreProxy.getCode(), storeProxyName -> storeProxyReadService.findUserIdByProxyName(subShopName, shopId)
								.ifSuccess(criteria::setRefererId));
		orderQueryCriteriaBySalePattern.getOrDefault(salePattern, doNone -> {
				})
				.accept(subShopName);
	}

	/**
	 * 微店铺的数据查询
	 *
	 * @param criteria 查询条件
	 */
	private void decorateCriteriaWithSubShopIdByWeShop(OrderCriteria criteria) {
		Predicate<WeShop> owned = weShop -> Objects.isNull(criteria.getShopId()) || Optional.ofNullable(weShopShopAccountReadService.findByWeShopId(weShop.getId()).getResult()).stream().flatMap(Collection::stream)
				.anyMatch(account -> Objects.equals(criteria.getShopId(), account.getShopId()));

		if (ObjectUtils.isEmpty(criteria.getThirdNameOne()) && Objects.isNull(criteria.getWeShopId())) {
			Optional.ofNullable(weShopReadService.findLikeName(criteria.getThirdNameOne()).getResult()).stream().flatMap(Collection::stream)
					.filter(owned)
					.findFirst().map(WeShop::getId).ifPresent(criteria::setWeShopId);
		}
		if (ObjectUtils.isEmpty(criteria.getThirdNameTwo()) && Objects.isNull(criteria.getWeShopId())) {
			Optional.ofNullable(weShopReadService.findLikeName(criteria.getThirdNameTwo()).getResult()).stream().flatMap(Collection::stream)
					.filter(owned)
					.findFirst().map(WeShop::getId).ifPresent(criteria::setWeShopId);
		}
	}

	/**
	 * 阶梯分销模式下, 对代理名称搜索进行实现, 同时完成代理之间的数据隔离
	 *
	 * @param criteria 查询条件
	 */
	private void decorateCriteriaWithSubShopIdByStoreProxy(OrderCriteria criteria) {
		// 如果自己是查询条件下的代理, 则进行查询自己
		if (Objects.nonNull(criteria.getShopId()) && Objects.nonNull(criteria.getOperatorId())) {
			if (storeProxySubUserManager.isAuthProxy(criteria.getOperatorId(), criteria.getShopId())) {
				//找出所有用户的id
				List<Long> subProxyReferenceIdList = storeProxySubUserManager.queryReferenceIdBelongUserAtShop(criteria.getOperatorId(), criteria.getShopId());
				// 如果没有下级代理 设置平台Id为null, 加速sql搜索 返回空
				if (subProxyReferenceIdList.isEmpty()) {
					criteria.setShopId(-999L);
					return;
				}
				if (Objects.nonNull(criteria.getRefererId()) && !subProxyReferenceIdList.contains(criteria.getRefererId())) {
					criteria.setShopId(-999L);
					return;
				}
				criteria.setRefererIds(subProxyReferenceIdList);
				// 增加一个或条件 跳过 原有代理人限制, 允许其查看自己购买的订单, 问题来源于 卖家端和买家端调用了相同的接口
				if (criteria.getBuyerId() == null || criteria.getBuyerId().equals(criteria.getOperatorId()) && criteria.getShopId() != 33L) {
					criteria.setOrBuyerId(criteria.getOperatorId());
				}
			}
		}
		// 匹配的代理商推荐Id和其旗下的代理Id
		Set<Long> supperProxyMatchedUserIdSet = new HashSet<>();
		if (!ObjectUtils.isEmpty(criteria.getThirdNameOne())) {
			List<Long> supperProxyUserIdList = storeProxyReadService.findByProxyShopName(criteria.getThirdNameOne()).map(List::stream)
					.orElseGet(Stream::empty)
					.peek(matchProxy -> supperProxyMatchedUserIdSet.add(matchProxy.getUserId()))
					.filter(supperProxy -> Objects.equals(supperProxy.getLevel(), 1))
					.map(StoreProxy::getUserId).toList();
			for (Long userId : supperProxyUserIdList) {
				storeProxyReadService.findBySupperIdAndShopId(userId, criteria.getShopId() == null ? 0 : criteria.getShopId())
						.map(List::stream).orElseGet(Stream::empty).map(StoreProxy::getUserId).forEach(supperProxyMatchedUserIdSet::add);
			}
		}
		// 匹配的下级代理Id
		List<Long> lowerProxyMatchedUserIdSet = ObjectUtils.isEmpty(criteria.getThirdNameTwo()) ? new ArrayList<>() :
				storeProxyReadService.findByProxyShopName(criteria.getThirdNameTwo()).map(List::stream)
						.orElseGet(Stream::empty).filter(lowProxy -> lowProxy.getLevel() > 1).map(StoreProxy::getUserId).collect(Collectors.toList());
		if (!supperProxyMatchedUserIdSet.isEmpty() || !lowerProxyMatchedUserIdSet.isEmpty()) {
			if (Objects.isNull(criteria.getRefererIds())) {
				criteria.setRefererIds(new ArrayList<>(supperProxyMatchedUserIdSet.size() > lowerProxyMatchedUserIdSet.size() ? supperProxyMatchedUserIdSet : lowerProxyMatchedUserIdSet));
			}
			if (StringUtils.hasLength(criteria.getThirdNameOne())) {
				criteria.getRefererIds().retainAll(supperProxyMatchedUserIdSet);
			}
			if (StringUtils.hasLength(criteria.getThirdNameTwo())) {
				criteria.getRefererIds().retainAll(lowerProxyMatchedUserIdSet);
			}
		} else if (StringUtils.hasLength(criteria.getThirdNameOne()) || StringUtils.hasLength(criteria.getThirdNameTwo())) {
			// 输入了查询条件, 但是查询结果为空, 则不需要进行正常的订单查询
			criteria.setShopId(-999L);
		}
		if (Objects.nonNull(criteria.getRefererIds()) && criteria.getRefererIds().isEmpty()) {
			// 拥有查询目标, 但是查询目标为空, 则不需要进行正常的订单查询
			criteria.setShopId(-999L);
		}
	}

	@Override
	public Response<Paging<OrderGroup>> pagingOrder(OrderCriteria criteria) {
		log.debug("Order Paging Param -> {}", criteria.toMap());
		// 由参数处理查询条件, 包括数据隔离
		if (prefixSearchForCriteria(criteria).isEmpty()) {
			return Response.ok(Paging.empty(OrderGroup.class));
		}
		// wash the order date
		if (criteria.getStartAt() != null) {
			criteria.setStartAt(DateUtil.withTimeAtStartOfDay(criteria.getStartAt()));
		}
		if (criteria.getEndAt() != null) {
			criteria.setEndAt(DateUtil.withTimeAtEndOfDay(criteria.getEndAt()));
		}
		Paging<OrderGroup> orderGroupPaging = queryOrder(criteria);

		for (OrderGroup orderGroup : orderGroupPaging.getData()) {
			try {
				// update the ShopOrder's Shop Name
				ShopOrder order = orderGroup.getShopOrder();
				if (order.getFeeId() == null) {
					orderWriteService.writeOrderInfo(order.getId());
				}
				if (OrderOutFrom.SUB_STORE.Code().equals(order.getOutFrom())
						&& order.getOutShopId() != null) {
					coverTheShopNameBySubStoreName(order, orderGroup.getSkuOrderAndOperations().stream().map(OrderAndOperation::order).map(it -> (SkuOrder) it).collect(Collectors.toList()));
				}
				Flow flow = flowPicker.pick(orderGroup.getShopOrder(), OrderLevel.SHOP);
				List<Long> skuIds = Optional.ofNullable(orderGroup.getSkuOrderAndOperations()).stream().flatMap(Collection::stream).map(input -> ((SkuOrder) input.order()).getSkuId()).toList();
				var skus = new ArrayList<Sku>(skuIds.size());
				for (Long skuId : skuIds) {
					skus.add(skuCacheHolder.findSkuById(skuId));
				}

				ImmutableMap<Long, Sku> skuIdAndSkuMap = Maps.uniqueIndex(skus, sku -> sku == null ? 0L : sku.getId());
				if (!CollectionUtils.isEmpty(orderGroup.getSkuOrderAndOperations())) {
					List<OrderAndOperation> operations = new ArrayList<>(orderGroup.getSkuOrderAndOperations().size());
					for (OrderAndOperation skuOrderAndOperation : orderGroup.getSkuOrderAndOperations()) {
						SkuOrder skuOrder = (SkuOrder) skuOrderAndOperation.order();
						var sku = skuIdAndSkuMap.get(skuOrder.getSkuId());
						var op = flow.availableOperations(skuOrder.getStatus());
						if (Optional.ofNullable(orderGroup.getShopOrder().getExtra()).orElse(Collections.emptyMap())
								.getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
								.equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
							op = skuOrderAndOperation.operation().stream()
									.filter(iop -> !iop.getText().equals("pay")).collect(Collectors.toSet());
						}
						operations.add(new OrderGroup.SkuOrderAndOperation(skuOrder, op, sku,
								OrderFlagEnum.getFrontEndFlagCodes(skuOrder.getFlag())));
					}
					orderGroup.setSkuOrderAndOperations(operations);
				}
				//确定店铺订单可以执行的操作
				//如果是根据状态筛选,那归组出来的子订单可能不能构成一个总单,这个时候就要以数据库真实数据为准
				//如果不根据状态筛选, 由于订单列表查询的时候只会返回有限数量的子订单,所以也要重新找一把
				orderGroup.setShopOrderOperations(
						pickCommonSkuOperation(orderGroup.getSkuOrderAndOperations().stream().map(op -> (SkuOrder) op.order()).collect(Collectors.toList())
								, flow));
				if (Optional.ofNullable(orderGroup.getShopOrder().getExtra()).orElse(Collections.emptyMap())
						.getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
						.equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
					orderGroup.setShopOrderOperations(orderGroup.getShopOrderOperations().stream()
							.filter(op -> !op.getText().equals("pay")).collect(Collectors.toSet()));
				}

			} catch (Exception e) {
				log.error("fail to find order operations by orderGroup({}), skip it. cause: ",
						JSON.toJSONString(orderGroup), e);
			}
		}
		return Response.ok(orderGroupPaging);
	}

	private void coverTheShopNameBySubStoreName(ShopOrder order, List<SkuOrder> skuOrders) {
		StringBuilder nameBuilder = new StringBuilder();

		Optional<SubStore> subStore = StringUtils.hasText(order.getOutShopId()) ?
				subStoreCache.findById(Long.parseLong(order.getOutShopId())) : Optional.empty();
		if (subStore.isPresent()) {
			//服务商
			var serviceProvider = serviceProviderCache.findBySubStoreUserIdAndShopId(
					subStore.get().getUserId(), order.getShopId());
			if (serviceProvider != null) {
				nameBuilder.append(serviceProvider.getName()).append(" - ");
			}

			//门店名称
			nameBuilder.append(subStore.get().getName());
		}

		if (order.getReferenceId() != null) {
			nameBuilder.append(" - ");
			guiderCache.findByShopIdAndUserId(order.getShopId(), order.getReferenceId())
					.map(SubStoreTStoreGuider::getStoreGuiderNickname)
					.ifPresent(nameBuilder::append);
		}

		for (SkuOrder skuOrder : skuOrders) {
			skuOrder.setShopName(nameBuilder.toString());
		}
		order.setShopName(nameBuilder.toString());
	}

	@Override
	public Paging<OrderGroup> queryOrder(OrderCriteria orderCriteria) {
		var origin = orderCriteria;
		long cost = System.currentTimeMillis();
		try {
			// just query it, there is no need to diff the sub order now
			var param = new OrderCriteria();
			BeanUtils.copyProperties(orderCriteria, param);
			orderCriteria = param;
			if (StrUtil.isNotBlank(orderCriteria.getStatusStr()) && orderCriteria.getStatusStr().equals("-30")) {
				List<Integer> refundStatus = OrderStatus.getRefundStatus().stream().map(OrderStatus::getValue).toList();
				orderCriteria.setStatus(refundStatus);
			}
			var orderList = shopOrderReadService.pagingShopOrder(orderCriteria);

			// key = shopOrderId
			var skuOrderMap = findSkuOrderMap(orderList.getData());

			// key = userId
			var userProfileMap = findUserProfileMap(orderList.getData());

			// 标记为首单的订单id
			var firstOrderMarkSet = findFirstOrderMarkSet(orderList.getData());

			var orderGroups = new ArrayList<OrderGroup>(orderList.getData().size());
			for (var order : orderList.getData()) {
				var orderGroup = new OrderGroup();
				orderGroup.setShopOrder(order);

				var skuOrders = skuOrderMap.get(order.getId());
				var skuOrderAndOperations = new ArrayList<OrderGroup.SkuOrderAndOperation>(skuOrders.size());
				boolean identifyFail = false;
				for (var skuOrder : skuOrders) {
					var op = flowPicker.pick(skuOrder, OrderLevel.SKU).availableOperations(skuOrder.getStatus());
					var orderOperation = new OrderGroup.SkuOrderAndOperation(skuOrder, op, skuCacheHolder.findSkuById(skuOrder.getSkuId()),
							OrderFlagEnum.getFrontEndFlagCodes(skuOrder.getFlag()));
					skuOrderAndOperations.add(orderOperation);
					if (skuOrder.getPushStatus() == 5) identifyFail = true;
				}
				if (identifyFail) {
					Map<String, String> extra = new HashMap<>();
					if (order.getExtra() != null) extra.putAll(order.getExtra());
					extra.put(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.TRUE.getCode());
					order.setExtra(extra);
				}
				orderGroup.setSkuOrderAndOperations(skuOrderAndOperations);
				// read the profit-view, put it into the order group
				orderGroup.setShopTax(skuOrders.stream().map(SkuOrder::getTax).filter(Objects::nonNull).reduce(0L, Long::sum));
				boolean shouldWaitAuth = order.getStatus() != OrderStatus.NOT_PAID.getValue() && skuOrders.stream().map(SkuOrder::getPushStatus)
						.anyMatch(pushStatus -> pushStatus == SkuOrderPushStatus.WAITING_SELLER_AUTH.value());

				// find is the order is first order, but it kind of suck actually
				if (firstOrderMarkSet.contains(order.getId())) {
					orderGroup.setTag("首单");
				}
				orderGroup.setWaitingSellerAuth(shouldWaitAuth);

				var profile = userProfileMap.get(order.getBuyerId());
				orderGroup.setBuyerAvatar(Optional.ofNullable(profile).map(UserProfile::getAvatar).orElse(null));
				orderGroups.add(orderGroup);
			}
			return new Paging<>(orderList.getTotal(), orderGroups);
		} finally {
			cost = System.currentTimeMillis() - cost;
			if (cost > 300) {
				log.warn("Slow Query of Order : {} by {}", cost, origin.toMap());
			}
		}
	}

	private Set<Long> findFirstOrderMarkSet(List<ShopOrder> shopOrders) {
		if (CollectionUtils.isEmpty(shopOrders)) {
			return Collections.emptySet();
		}

		return firstOrderMarkReadService.findSetByOrderIds(shopOrders.stream().map(ShopOrder::getId).toList());
	}

	private Map<Long, UserProfile> findUserProfileMap(List<ShopOrder> shopOrders) {
		if (CollectionUtils.isEmpty(shopOrders)) {
			return Collections.emptyMap();
		}

		return userProfileReadService.findMapByUserIds(shopOrders.stream().map(ShopOrder::getBuyerId).toList());
	}

	/**
	 * @param shopOrders
	 * @return key = shopOrderId
	 */
	private Map<Long, List<SkuOrder>> findSkuOrderMap(List<ShopOrder> shopOrders) {
		if (CollectionUtils.isEmpty(shopOrders)) {
			return Collections.emptyMap();
		}

		return skuOrderReadService.getSkuOrderMap(shopOrders.stream().map(ShopOrder::getId).toList());
	}

	@Override
	public Response<Long> countShopOrder(OrderCriteria orderCriteria) {
		return orderReadService.countShopOrder(orderCriteria);
	}

	@Override
	public Response<Long> countWeShopOrder(OrderCriteria orderCriteria) {
		Response<List<Long>> rShopOrderIds = shopOrderReadService.listIdsBy(orderCriteria);
		if (!rShopOrderIds.isSuccess()) {
			log.error("failed to list shop order ids by orderCriteria({}), error code: {}", orderCriteria, rShopOrderIds.getError());
			throw new JsonResponseException(rShopOrderIds.getError());
		}
		List<Long> shopOrderIds = rShopOrderIds.getResult();
		if (!CollectionUtils.isEmpty(shopOrderIds)) {
			orderCriteria.setOrderIds(shopOrderIds);
		}
		return skuOrderReadService.countShopOrder(orderCriteria);
	}

	/**
	 * 订单详情
	 */
	@Override
	public Response<OrderDetail> orderDetail(Long shopOrderId) {
		Response<OrderDetail> detailR = orderReadService.findOrderDetailById(shopOrderId);
		if (!detailR.isSuccess()) {
			// 这里直接返回交给herd处理
			return detailR;
		}
		OrderDetail orderDetail = detailR.getResult();
		try {
			Flow flow = flowPicker.pick(orderDetail.getShopOrder(), OrderLevel.SHOP);
			orderDetail.setShopOrderOperations(pickCommonSkuOperation(orderDetail.getSkuOrders(), flow));
			if (Optional.ofNullable(orderDetail.getShopOrder().getExtra()).orElse(Collections.emptyMap())
					.getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
					.equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
				orderDetail.setShopOrderOperations(orderDetail.getShopOrderOperations().stream()
						.filter(op -> !op.getText().equals("pay")).collect(Collectors.toSet()));
			}
		} catch (Exception e) {
			log.error("fail to get shopOrder(id={}) detail's order operation, cause:{}, ignore",
					shopOrderId, Throwables.getStackTraceAsString(e));
		}
		infoShowRefundDetail(orderDetail);
		User user = userReadService.findById(orderDetail.getShopOrder().getBuyerId()).getResult();
		PayInfoVOPacker.packPayInfoForOrderDetailWithShopOrder(orderDetail, mongoTemplate.findOne(Query.query(Criteria.where("orderId").is(orderDetail.getShopOrder().getId())), PayerInfo.class), user);
		decorateSkuSpecification(orderDetail);
		if (OrderOutFrom.SUB_STORE.Code().equals(orderDetail.getShopOrder().getOutFrom())) {
			coverTheShopNameBySubStoreName(orderDetail.getShopOrder(), orderDetail.getSkuOrders());
		}
		if (orderDetail.getShopOrder().getReferenceId() != null && Objects.equals(orderDetail.getShopOrder().getOutFrom(), OrderOutFrom.LEVEL_Distribution.Code())) {
			ShopOrderVO shopOrderView = new ShopOrderVO(orderDetail.getShopOrder());
			storeProxyManager.getStoreProxyByShopIdAndUserId(shopOrderView.getShopId(), shopOrderView.getReferenceId())
					.map(StoreProxy::getProxyShopName).ifPresent(shopOrderView::setSubShopName);
			orderDetail.setShopOrder(shopOrderView);
		}

		Optional.of(orderDetail.getShopOrder().getExtra()).ifPresent(extra -> orderDetail.setOrderPushErrorMessage(
				extra.get(ShopOrderExtra.orderPushErrorMessage.name())));
		return Response.ok(orderDetail);
	}

	@Override
	public Response<OrderDetail> showReceiverInfo(Long shopOrderId) {
		Response<OrderDetail> detailResponse = this.orderDetail(shopOrderId);
		if (detailResponse.isSuccess()) {
			OrderDetail result = detailResponse.getResult();
			List<OrderReceiverInfo> orderReceiverInfos = result.getOrderReceiverInfos();
			// 展示明文
			orderReceiverInfos.forEach(item -> {
				ReceiverInfo receiverInfo = item.getReceiverInfo();
				receiverInfo.decrypt();
				receiverInfo.setDetail(receiverInfo.getDetail());
				receiverInfo.setPhone(receiverInfo.getPhone());
				receiverInfo.setMobile(receiverInfo.getMobile());
				receiverInfo.setPaperNo(receiverInfo.getPaperNo());
				receiverInfo.setReceiveUserName(receiverInfo.getReceiveUserName());
			});

			SysOperLog logInfo = new SysOperLog();
			logInfo.setName("订单解密查看收件人信息:" + shopOrderId);
			logInfo.setShopId(result.getShopOrder().getShopId());
			logInfo.setTitle("订单管理->订单管理");
			logInfo.setUserId(UserUtil.getUserId());
			logInfo.setBusinessType(OpBusinessType.OTHER.getCode());
			logInfo.setStatus(1);
			logInfo.setCreateAt(System.currentTimeMillis());
			sysOperLogWriteService.insertSysOperLog(logInfo);
		}


		return detailResponse;
	}


	@Override
	public Response<OrderDetail> showPayerInfo(Long shopOrderId) {
		Response<OrderDetail> detailResponse = this.orderDetail(shopOrderId);
		if (detailResponse.isSuccess()) {
			OrderDetail orderDetail = detailResponse.getResult();
			User user = userReadService.findById(orderDetail.getShopOrder().getBuyerId()).getResult();
			PayInfoVOPacker.packPayInfoForOrderDetailWithShopOrder(orderDetail, mongoTemplate.findOne(Query.query(Criteria.where("orderId").is(orderDetail.getShopOrder().getId())), PayerInfo.class), user);

			SysOperLog logInfo = new SysOperLog();
			logInfo.setName("订单解密查看支付人信息:" + shopOrderId);
			logInfo.setShopId(orderDetail.getShopOrder().getShopId());
			logInfo.setTitle("订单管理->订单管理");
			logInfo.setUserId(UserUtil.getUserId());
			logInfo.setBusinessType(OpBusinessType.OTHER.getCode());
			logInfo.setStatus(1);
			logInfo.setCreateAt(System.currentTimeMillis());
			sysOperLogWriteService.insertSysOperLog(logInfo);
		}
		return detailResponse;
	}


	/**
	 * 装饰订单详情信息
	 *
	 * @param orderDetail 订单详情
	 */
	private void decorateSkuSpecification(OrderDetail orderDetail) {
		List<SkuOrder> skuOrderViewList = new ArrayList<>();
		for (SkuOrder skuOrder : orderDetail.getSkuOrders()) {
			SkuOrderView view = new SkuOrderView();
			BeanUtils.copyProperties(skuOrder, view);
			Optional.ofNullable(skuCacheHolder.findSkuById(skuOrder.getSkuId()))
					.map(Sku::getSpecification)
					.ifPresent(view::setSpecification);
			skuOrderViewList.add(view);
		}
		orderDetail.setSkuOrders(skuOrderViewList);
	}

	private void infoShowRefundDetail(OrderDetail orderDetail) {
		if (orderDetail.getShopOrder().getStatus() == 0) {
			return;
		}
		var shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(orderDetail.getShopOrder().getShopId()).getExtra()).orElseGet(HashMap::new);
		if (orderDetail.getShopOrder().getShopId() == 29L && shopExtra.get(ShopExtra.AllowRefund.getCode()) == null) {
			shopExtra.put(ShopExtra.AllowRefund.getCode(), "60");
		}
		var allowRefundSet = shopExtra.getOrDefault(ShopExtra.AllowRefund.getCode(), "false").trim();
		if (Boolean.TRUE.toString().toLowerCase().equals(allowRefundSet)) {
			orderDetail.setRefund(true);
			return;
		}
		var now = new DateTime(Date.from(Instant.now()));
		java.util.function.Function<Payment, Boolean> allowRefund = Boolean.FALSE.toString().toLowerCase().equals(allowRefundSet) || allowRefundSet.isEmpty() ?
				payment -> false : payment -> payment != null && payment.getPaidAt() != null && !payment.getPaidAt().before(now.minusMinutes(Integer.parseInt(allowRefundSet)).toDate());
		Response<OrderPayment> listShopResponse = paymentReadService.findOrderPaymentByOrderIdAndOrderLevel(orderDetail.getShopOrder().getId(), OrderLevel.SHOP);
		if (!listShopResponse.isSuccess()) {
			log.error("{} ShowRefund orderId:{}", LogUtil.getClassMethodName(), orderDetail.getShopOrder().getId());
			orderDetail.setRefund(false);
			return;
		}
		if (listShopResponse.getResult() != null) {
			Response<Payment> payment = paymentReadService.findById(listShopResponse.getResult().getPaymentId());
			orderDetail.setRefund(payment.isSuccess() && allowRefund.apply(payment.getResult()));
		} else {
			Response<OrderPayment> listSkuPResponse = paymentReadService.findOrderPaymentByOrderIdAndOrderLevel(orderDetail.getSkuOrders().get(0).getId(), OrderLevel.SKU);
			if (!listSkuPResponse.isSuccess()) {
				log.error("{} ShowRefund orderId:{}", LogUtil.getClassMethodName(), orderDetail.getShopOrder().getId());
				orderDetail.setRefund(false);
				return;
			}
			if (listSkuPResponse.getResult() != null) {
				Response<Payment> payment = paymentReadService.findById(listShopResponse.getResult().getPaymentId());
				orderDetail.setRefund(payment.isSuccess() && allowRefund.apply(payment.getResult()));
			}
		}
	}

	/**
	 * 从sku订单总提取共有的操作作为店铺订单操作
	 *
	 * @param skuOrders sku订单列表
	 * @return 店铺订单操作列表
	 */
	private Set<OrderOperation> pickCommonSkuOperation(Collection<SkuOrder> skuOrders, Flow flow) {
		//查询店铺操作,所有子订单共有的操作才能在订单级别操作
		ArrayListMultimap<OrderOperation, Long> groupSkuOrderIdByOperation = ArrayListMultimap.create();
		for (SkuOrder skuOrder : skuOrders) {
			Set<OrderOperation> orderOperations = flow.availableOperations(skuOrder.getStatus());
			for (OrderOperation orderOperation : orderOperations) {
				groupSkuOrderIdByOperation.put(orderOperation, skuOrder.getId());
			}
		}
		Set<OrderOperation> shopOperation = Sets.newHashSet();
		boolean refundApplyMatch = false;
		boolean returnApplyMatch = false;
		for (OrderOperation operation : groupSkuOrderIdByOperation.keySet()) {
			if (com.google.common.base.Objects.equal(groupSkuOrderIdByOperation.get(operation).size(), skuOrders.size())) {
				shopOperation.add(operation);
			}
			if (operation.getText().equals(OrderEvent.REFUND_APPLY.getText())) {
				refundApplyMatch = true;
			}
			if (operation.getText().equals(OrderEvent.RETURN_APPLY.getText())) {
				returnApplyMatch = true;
			}
		}
		if (refundApplyMatch && returnApplyMatch) {
			shopOperation.removeIf(operation -> operation.getText().equals(OrderEvent.REFUND_APPLY.getText()));
		}
		return shopOperation;
	}

	@Override
	public OrderBase findOrder(Long orderId, OrderLevel orderLevel) {
		switch (orderLevel) {
			case SHOP -> {
				Response<ShopOrder> shopOrderResp = shopOrderReadService.findById(orderId);
				if (!shopOrderResp.isSuccess()) {
					log.error("fail to find shop order by id:{},cause:{}", orderId, shopOrderResp.getError());
					throw new JsonResponseException(shopOrderResp.getError());
				}
				return shopOrderResp.getResult();
			}
			case SKU -> {
				Response<SkuOrder> skuOrderResp = skuOrderReadService.findById(orderId);
				if (!skuOrderResp.isSuccess()) {
					log.error("fail to find sku order by sku order id:{},cause:{}", orderId, skuOrderResp.getError());
					throw new JsonResponseException(skuOrderResp.getError());
				}
				return skuOrderResp.getResult();
			}
			default -> throw new IllegalArgumentException("unknown.order.type");
		}
	}

	@Override
	public List<Long> findStrangeOrderBefore(String date) {
		return shopOrderReadService.findStrangeOrderBefore(date).getResult();
	}
}
