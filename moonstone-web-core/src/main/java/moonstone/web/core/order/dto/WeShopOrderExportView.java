package moonstone.web.core.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.order.dto.SkuOrderForExportView;

@EqualsAndHashCode(callSuper = true)
@Data
public class WeShopOrderExportView extends SkuOrderForExportView {
    Long id;
    //sku的id
    Long skuId;
    //商品名称
    String itemName;
    /**
     * 货码
     */
    String itemCode;
    //商品价格
    String originFee;
    //数量
    Integer quantity;
    //税费
    String tax;
    //运费
    String shipFee;
    //优惠金额
    String discount;
    // 快递公司
    String shipmentName;
    // 快递单号
    String shipmentId;
    String shipmentTime = "";
    //推送状态
    String pushStatus;
    //推送后的洋800订单ID
    String y800PushOrderId;
    //外部推送的系统名
    String outPushSystemName;
    //推送后的外部订单ID
    String outPushOrderId;

    //购买人Id
    Long buyerId = null;
    //店铺名
    String shopName = null;
    //订单号 skuOrderId
    Long orderId = null;
    //订单状态
    String status = null;
    //支付流水号
    String paySerialNo = null;
    //下单时间
    String createAt = null;
    //支付时间
    String paidAt = null;
    String confirmAt = null;
    //买家
    String buyerName = null;
    //买家微信昵称
    String wxBuyerName = null;
    //收件人
    String receiveUserName = null;
    //手机号
    String mobile = null;
    //收货地址
    String address = null;
    //接下来的信息
    //抵扣金额
    //总价
    String fee = null;
    String outFromName;
    String declaredNo;

    String payerName;
    String payerNo;
    /**
     * 微店名称
     */
    String weShopName;
    /**
     * 微店利润
     */
    String weShopProfit;
}