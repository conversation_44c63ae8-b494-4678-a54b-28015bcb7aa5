package moonstone.web.core.order.component;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemSnapShotCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.ParanaDefaultThreadFactory;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.ItemSnapshot;
import moonstone.order.api.OrderExportReader;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.service.SubStoreReadService;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileReadService;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.exports.common.SheetMergeUtil;
import moonstone.web.core.order.RefundReadLogic;
import moonstone.web.core.order.app.SubStoreWithdrawRecordViewApp;
import moonstone.web.core.order.dto.OrderExportView;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import moonstone.web.core.order.dto.WeShopOrderExportView;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Deprecated
@Component
public class OrderExportReaderImpl implements OrderExportReader {
    static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(),
            new ParanaDefaultThreadFactory("orderExportReader"));

    private static final Logger LOG = LoggerFactory.getLogger(OrderExportReaderImpl.class);
    @Autowired
    UserProfileReadService userProfileReadService;
    @Autowired
    ReceiverInfoReadService receiverInfoReadService;
    @Autowired
    PaymentReadService paymentReadService;
    @Autowired
    ShopOrderReadService shopOrderReadService;
    @Autowired
    OrderReadService orderReadService;
    @Autowired
    ShipmentReadService shipmentReadService;
    @Autowired
    SubStoreReadService subStoreReadService;
    @Autowired
    RefundReadLogic refundReadLogic;
    @Autowired
    Exporter exporter;
    @Autowired
    Y800OrderIdGenerator y800OrderIdGenerator;
    @Autowired
    OrderExportExtraInfoDecorator orderExportExtraInfoDecorator;
    @Autowired
    ShopCacheHolder shopCacheHolder;
    @Autowired
    ItemSnapShotCacheHolder itemSnapShotCacheHolder;
    @Autowired
    SubStoreWithdrawRecordViewApp subStoreWithdrawRecordViewApp;
    LoadingCache<Long, Optional<String>> userNickNameCache = Caffeine.newBuilder()
            .expireAfterWrite(3, TimeUnit.SECONDS)
            .build(key -> {
                try {
                    return Optional.ofNullable(userProfileReadService.findProfileByUserId(key).getResult()).map(UserProfile::getRealName);
                } catch (Exception e) {
                    LOG.error("{} fail to take real name for userId[{}]", LogUtil.getClassMethodName(), key);
                    return Optional.empty();
                }
            });

    @Override
    public void exportOrderExcel(OrderCriteria criteria, OutputStream outputStream) {
        int pageSize = 500;
        int page = 1;
        criteria.setSize(pageSize);
        Workbook workbook = new SXSSFWorkbook(1000);
        int line = 0;
        while (page > 0) {
            criteria.setPageNo(page);
            Paging<OrderGroup> orders = orderReadService.findBy(criteria).getResult();
            List<OrderGroup> dataList = orders.getData();
            if (dataList.isEmpty()) {
                break;
            }
            List<OrderExportView> viewList = convertSkuOrderExportViewIntoWorkBook(convertOrderGroupIntoShopOrderExportViewObject(dataList));
            exportIt(line, viewList, workbook);
            line += viewList.size();
            page += 1;
        }
        try {
            workbook.write(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("数据导出失败");
        }
    }

    <T> void exportIt(int prefix, List<T> view, Workbook workbook) {
        exporter.exportWithBook(workbook, view, OrderExportView.class.getSimpleName(), prefix);
    }

    /**
     * 获取读取时间
     *
     * @return 读取时间
     */
    private Supplier<Long> getCost() {
        final long start = System.currentTimeMillis();
        return () -> System.currentTimeMillis() - start;
    }

    @Override
    public void exportExcelSortByShipmentTime(List<? extends OrderGroup> orderGroup, OutputStream outputStream) {
        try {
            Supplier<Long> cast = getCost();
            List<ShopOrderForExportView> orderExportList = convertOrderGroupIntoShopOrderExportViewObject(orderGroup);
            LOG.debug("{} fulfill order group[{}] info cost [{}]", LogUtil.getClassMethodName("packVO"), orderGroup.size(), cast.get());
            cast = getCost();
            long taskCount = orderExportList.parallelStream().peek(orderVo -> {
                if (orderVo.getStatus() == 1 || orderVo.getStatus() == 0) {
                    return;
                }
                //获取确认收货时间
                List<Shipment> shipmentList = Optional.ofNullable(shipmentReadService.findByOrderIdAndOrderLevel(orderVo.getOrderId(), OrderLevel.SHOP).getResult())
                        .filter(list -> !list.isEmpty())
                        .orElseGet(() -> findBySkuOrderList(orderVo.getSkuOrderForExportViewList()));
                if (shipmentList.isEmpty()) {
                    orderVo.setConfirmAt(null);
                } else {
                    // 采取强制设置收货时间
                    shipmentList.stream().map(Shipment::getConfirmAt).filter(Objects::nonNull).findFirst().ifPresent(orderVo::setConfirmAt);
                    Optional<Shipment> shipmentOpt = shipmentList.stream().filter(ship -> ship.getShipmentSerialNo() != null).findFirst();
                    for (SkuOrderForExportView subDatum : orderVo.getSkuOrderForExportViewList()) {
                        shipmentOpt.map(Shipment::getShipmentSerialNo).ifPresent(subDatum::setShipmentId);
                        shipmentOpt.map(Shipment::getShipmentCorpName).ifPresent(subDatum::setShipmentName);
                        shipmentOpt.map(Shipment::getCreatedAt).map(DateTime::new).map(time -> time.toString("yyyy-MM-dd HH:mm:ss")).ifPresent(subDatum::setShipmentTime);
                    }
                }
            }).map(ShopOrderForExportView::getStatus).count();
            LOG.debug("{} decorate shipment date [count -> {}] cost [{}]", LogUtil.getClassMethodName("findShipment"), taskCount, cast.get());
            //FN then IO
            List<OrderExportView> orderExportViewList = convertSkuOrderExportViewIntoWorkBook(orderExportList);

            Workbook workbook;
            Map<Long, String> outOrderId = orderExportList.stream().collect(Collectors.toMap(ShopOrderForExportView::getOrderId, orderVo -> orderVo.getExtra() == null ? "" : orderVo.getExtra().getOrDefault("outOrderId", "")));
            BiConsumer<Row, OrderExportView> outOrderIdDecorator = orderExportExtraInfoDecorator.decorateOrderId(outOrderId);
            Long shopId = orderExportList.isEmpty() ? null : orderExportList.stream().map(ShopOrderForExportView::getShopId).filter(Objects::nonNull).findFirst()
                    .orElse(null);
            BiConsumer<Row, OrderExportView> exProfitDecorator = shopId == null ? (row, view) -> {
            } : orderExportExtraInfoDecorator.decorateExProfit(shopId);
            BiConsumer<Row, OrderExportView> cellNameDecorator = shopId == null ? (row, view) -> {
            } : orderExportExtraInfoDecorator.decorateCellTitleName(shopId);
            BiConsumer<Row, OrderExportView> serviceProfitDecorator = shopId == null ? ((row, orderExportView) -> {
            })
                    : orderExportExtraInfoDecorator.decorateServiceProviderProfit(shopId, orderGroup);
            // 判断店铺类型 然后进行对订单导出处理
            Map<String, String> shopExtra = shopId == null ? new HashMap<>() : shopCacheHolder.findShopById(shopId).getExtra();
            String saleMode = Optional.ofNullable(shopExtra)
                    .map(extra -> extra.getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()))
                    .orElse(ShopExtra.commonShop.getCode());
            if (SalePattern.WeShop.getCode().equals(saleMode)) {
                List<WeShopOrderExportView> weShopOrderExportViews = new ArrayList<>(orderExportViewList.size());
                for (OrderExportView orderExportView : orderExportViewList) {
                    WeShopOrderExportView weShopOrderExportView = new WeShopOrderExportView();
                    BeanUtils.copyProperties(orderExportView, weShopOrderExportView);
                    weShopOrderExportView.setWeShopProfit(orderExportView.getSubStoreProfit());
                    weShopOrderExportView.setWeShopName(orderExportView.getSubStoreName());
                    weShopOrderExportViews.add(weShopOrderExportView);
                }
                workbook = exporter.export(weShopOrderExportViews, WeShopOrderExportView.class);
            } else {
                workbook = exporter.exportWithDecorate(orderExportViewList, OrderExportView.class, (row, view) -> {
                    outOrderIdDecorator.accept(row, view);
                    exProfitDecorator.accept(row, view);
                    cellNameDecorator.accept(row, view);
                    serviceProfitDecorator.accept(row, view);
                });
            }
            LOG.debug("{} export cast [{}]", LogUtil.getClassMethodName(), cast.get());

            SheetMergeUtil.mergeWorkbook(workbook).write(outputStream);
        } catch (Exception ex) {
            LOG.error("{} fail to export order", LogUtil.getClassMethodName("order-export"), ex);
            try {
                outputStream.write(JSON.toJSONString(Response.fail("query.failed")).getBytes());
            } catch (Exception e) {
                LOG.error("fail to get OutputStream");
            }
        }
    }

    private List<Shipment> findBySkuOrderList(List<SkuOrderForExportView> subData) {
        List<Shipment> shipmentList = new ArrayList<>(1);
        for (SkuOrderForExportView subDatum : subData) {
            Optional.ofNullable(shipmentReadService.findByOrderIdAndOrderLevel(subDatum.getId(), OrderLevel.SKU).getResult())
                    .ifPresent(shipmentList::addAll);
            if (!shipmentList.isEmpty()) {
                return shipmentList;
            }
        }
        return shipmentList;
    }

    @Override
    public void refundExportExcel(RefundCriteria refundCriteria, OutputStream outputStream) {
        try {
            //IO work
            refundCriteria.setSize(Integer.MAX_VALUE);
            var rRefund = refundReadLogic.refundPaging(refundCriteria);
            if (!rRefund.isSuccess()) {
                throw new Exception("shopOrder.query.failed");
            }
            List<RefundList> refundDetail = rRefund.getResult().getData();
            //FN IO
            var rRefundVo = packRefundVo(refundDetail);
            if (!rRefundVo.isSuccess()) {
                throw new Exception(rRefundVo.getError());
            }
            List<RefundExportVo> refundExportVo = rRefundVo.getResult();
            exportRefund(refundExportVo).write(outputStream);
        } catch (Exception ex) {
            LOG.error("exportExcel" + ex.getMessage());
            try {
                outputStream.write(JSON.toJSONString(Response.fail("query.failed")).getBytes());
            } catch (Exception e) {
                LOG.error("fail to get OutPutStream");
            }
        }
    }

    /**
     * 需要数据库IO进行交互获取支付信息,所以monad包裹
     */
    public Response<ArrayList<RefundExportVo>> packRefundVo(List<RefundList> refundLists) {
        ArrayList<RefundExportVo> refundExportVos = new ArrayList<>();
        for (RefundList refundList : refundLists) {
            //退款单中必然不是空的
            if (CollectionUtils.isEmpty(refundList.getSkuOrders())) {
                continue;
            }
            var rShopOrder = shopOrderReadService.findById(refundList.getSkuOrders().get(0).getOrderId());
            if (!rShopOrder.isSuccess()) {
                RefundExportVo errorExportRefund = new RefundExportVo();
                errorExportRefund.setOrderId(refundList.getSkuOrders().get(0).getOrderId());
                errorExportRefund.setCoStatus("查找退款单失败, 主订单信息丢失");
                refundExportVos.add(errorExportRefund);
                continue;
            }

            Response<List<ReceiverInfo>> rReceiverInfo;
            try {
                rReceiverInfo = receiverInfoReadService.findByOrderId(rShopOrder.getResult().getId(), OrderLevel.SHOP);
            } catch (Exception ex) {
                ArrayList<ReceiverInfo> empty = new ArrayList<>();
                empty.add(null);
                rReceiverInfo = Response.ok(empty);
            }
            if (!rReceiverInfo.isSuccess()) {
                ArrayList<ReceiverInfo> empty = new ArrayList<>();
                empty.add(null);
                rReceiverInfo = Response.ok(empty);
                LOG.error("query.failed.receiverInfo(orderId:{})", rShopOrder.getResult().getId());
            }
            ReceiverInfo receiverInfo = rReceiverInfo.getResult().get(0);
            var rPayment = paymentReadService.findByOrderIdAndOrderLevel(refundList.getSkuOrders().get(0).getOrderId(), OrderLevel.SHOP);
            if (!rPayment.isSuccess()) {
                LOG.error("query.failed.payment(orderId:{})", refundList.getSkuOrders().get(0).getOrderId());
                continue;
            }
            String paidAt = "";
            if (!CollectionUtils.isEmpty(rPayment.getResult())) {
                if (rPayment.getResult().get(0).getPaidAt() != null) {
                    paidAt = new DateTime(rPayment.getResult().get(0).getPaidAt()).toString("yyyy-MM-dd HH:mm:ss");
                }
            }

            for (SkuOrder skuOrder : refundList.getSkuOrders()) {
                RefundExportVo refundExportVo = new RefundExportVo();
                refundExportVo.setPaidAt(paidAt);
                String wxBuyerName = Optional.ofNullable(userProfileReadService.findProfileByUserId(skuOrder.getBuyerId()).getResult())
                        .map(UserProfile::getRealName)
                        .orElse(skuOrder.getBuyerName());
                refundExportVo.setWxBuyerName(wxBuyerName);
                refundExportVos.add(refundExportVo.from(receiverInfo)
                        .from(rShopOrder.getResult())
                        .from(skuOrder)
                        .from(refundList.getRefund())
                        .from(refundList.getRefundExpressInfo())
                );
            }
        }
        return Response.ok(refundExportVos);
    }


    /**
     * 使用原数据直接进行打包
     *
     * @param orderGroups 订单与其外包装
     * @return 打包完毕的数据
     */
    public List<ShopOrderForExportView> convertOrderGroupIntoShopOrderExportViewObject(List<? extends OrderGroup> orderGroups) {
        Queue<ShopOrderForExportView> finishedQueue = new LinkedBlockingQueue<>();
        long taskStartedAt = System.currentTimeMillis();
        Consumer<OrderGroup> packer = orderGroup -> {
            if (System.currentTimeMillis() - taskStartedAt > 28000) {
                // system timeout
                throw Translate.exceptionOf("系统超时");
            }
            ShopOrder order = orderGroup.getShopOrder();
            //IO Work dirty but work
            List<SkuOrder> skuOrder = orderGroup.getSkuOrderAndOperations().stream().map(it -> ((OrderGroup.SkuOrderAndOperation) it).getSkuOrder()).collect(Collectors.toList());

            Response<List<ReceiverInfo>> rReceiverInfo;
            try {
                rReceiverInfo = receiverInfoReadService.findByOrderId(order.getId(), OrderLevel.SHOP);
            } catch (Exception ex) {
                rReceiverInfo = Response.ok(new ArrayList<>());
            }
            if (!rReceiverInfo.isSuccess()) {
                LOG.error("{} query.failed.receiverInfo(orderId:{})", LogUtil.getClassMethodName(), order.getId());
                rReceiverInfo = Response.ok(new ArrayList<>());
            }
            Response<List<Payment>> rPayment = paymentReadService.findByOrderIdAndOrderLevel(order.getId(), OrderLevel.SHOP);
            if (!rPayment.isSuccess()) {
                LOG.error("{} query.failed.payment(orderId:{})", LogUtil.getClassMethodName(), order.getId());
                rPayment = Response.ok(new ArrayList<>());
            }
            if (rPayment.getResult().size() > 0) {
                rPayment.setResult(rPayment.getResult().stream().sorted(Comparator.<Payment, Integer>comparing(a -> Optional.ofNullable(a).map(Payment::getStatus).orElse(-1)).reversed()).collect(Collectors.toList()));
            }
            ReceiverInfo receiverInfo = rReceiverInfo.getResult().size() > 0 ? rReceiverInfo.getResult().get(0) : null;
            Payment paymentInfo = rPayment.getResult().stream().max(Comparator.comparing(Payment::getStatus)).orElse(null);
            ShopOrderForExportView shopOrderForExportView = packShopOrderViewObject(orderGroup, order, skuOrder,
                    receiverInfo, paymentInfo);
            //设置成功报关的支付流水号
            Optional.ofNullable(paymentInfo).map(Payment::getPaySerialNo)
                    .ifPresent(shopOrderForExportView::setPaySerialNo);
            if (shopOrderForExportView.getDeclaredNo() == null) {
                try {
                    shopOrderForExportView.setDeclaredNo(y800OrderIdGenerator.getDeclareId(order));
                } catch (Exception ex) {
                    shopOrderForExportView.setDeclaredNo(new Translate("申报单号查找失败").toString());
                }
            }
            if (OrderOutFrom.SUB_STORE.Code().equals(order.getOutFrom()) && !ObjectUtils.isEmpty(order.getOutShopId())) {
                try {
                    shopOrderForExportView.setOutFromName(subStoreReadService.findById(Long.valueOf(order.getOutShopId())).getResult().getName());
                } catch (Exception ex) {
                    shopOrderForExportView.setOutFromName("门店信息获取失败");
                    LOG.error("{} orderId:{} outShopId:{} outFrom:{} refererId:{}", LogUtil.getClassMethodName(), order.getId(), order.getOutShopId(), order.getOutFrom(), order.getReferenceId());
                }
            }
            // 添加一个数据修正
            if (Objects.equals(order.getStatus(), OrderStatus.CONFIRMED.getValue())) {
                shopOrderForExportView.setConfirmAt(order.getUpdatedAt());
            }
            finishedQueue.add(shopOrderForExportView);
            log.debug("Order Export -> {}", finishedQueue.size());
        };

        // task dispatch here, write at chaos
        AtomicInteger index = new AtomicInteger(0);
        List<Future<Boolean>> jobDoneList = new LinkedList<>();
        Supplier<Future<Boolean>> jobProducer = () -> EXECUTOR.submit(() -> {
            try {
                if (System.currentTimeMillis() - taskStartedAt > 29000) {
                    LOG.warn("TIME OUT");
                    return;
                }
                int nowIndex = index.getAndIncrement();
                while (nowIndex < orderGroups.size()) {
                    packer.accept(orderGroups.get(nowIndex));
                    nowIndex = index.getAndIncrement();
                }
            } catch (Exception e) {
                LOG.error("ERROR EXPORT, SIZE -> {}", orderGroups.size(), e);
            }
        }, true);
        int cpuCount = Runtime.getRuntime().availableProcessors();
        try {
            for (int i = 0; i < cpuCount; i++) {
                jobDoneList.add(jobProducer.get());
            }
            for (Future<Boolean> done : jobDoneList) {
                done.get(1000, TimeUnit.MILLISECONDS);
            }
            List<ShopOrderForExportView> viewList = new ArrayList<>(finishedQueue);
            viewList.sort(Comparator.comparing(ShopOrderForExportView::getOrderId));
            return viewList;
        } catch (Exception ex) {
            LOG.error("{} fail to judge the job status, By User {}", LogUtil.getClassMethodName(), UserUtil.getCurrentUser(), ex);
        }
        try {
            // give it extra 30 SEC
            for (Future<Boolean> done : jobDoneList) {
                done.get(30, TimeUnit.SECONDS);
            }
        } catch (Exception ex) {
            LOG.error("{} can't finished the job, all Size -> {}", LogUtil.getClassMethodName(), orderGroups.size(), ex);
            if (!finishedQueue.isEmpty()) {
                ShopOrderForExportView error = new ShopOrderForExportView();
                BeanUtils.copyProperties(finishedQueue.element(), error);
                error.setDeclaredNo("订单导出超时, 请调整查询条件");
                error.setOutFromName("订单导出超时, 请调整查询条件");
                error.setAddress("订单导出超时, 请调整查询条件");
                error.setShopName("订单导出超时, 请调整查询条件");
                error.setMobile("订单导出超时, 请调整查询条件");
                error.setPayerName("订单导出超时, 请调整查询条件");
                finishedQueue.add(error);
            }
        }
        List<ShopOrderForExportView> viewList = new ArrayList<>(finishedQueue);
        viewList.sort(Comparator.comparing(ShopOrderForExportView::getOrderId));
        return viewList;
    }

    /**
     * 将shopOrder打包为shopOrderVo 注意shopOrderVo只是中间层，真实输出实体为OrderListVo
     *
     * @param orderGroup   数据外包装
     * @param shopOrder    数据来源
     * @param skuOrders    子订单数据来源
     * @param receiverInfo 收获人数据
     * @param payment      订单支付单数据
     * @return {@link ShopOrderForExportView} 打包完成的数据
     */
    ShopOrderForExportView packShopOrderViewObject(OrderGroup orderGroup, ShopOrder shopOrder, List<SkuOrder> skuOrders, ReceiverInfo receiverInfo, Payment payment) {
        //FP stack save
        ShopOrderForExportView shopOrderForExportView = new ShopOrderForExportView();

        // orderGroup 有两种实现,所以使用BeanCopy直接复制所需数据即可
        BeanUtils.copyProperties(orderGroup, shopOrderForExportView);
        BeanUtils.copyProperties(shopOrder, shopOrderForExportView);
        if (receiverInfo != null) {
            shopOrderForExportView.setReceiveUserName(receiverInfo.getReceiveUserName());
            shopOrderForExportView.setAddress(receiverInfo.getProvince() + receiverInfo.getCity() + receiverInfo.getRegion() + receiverInfo.getDetail());
            shopOrderForExportView.setMobile(receiverInfo.getMobile());
        }
        if (payment != null) {
            shopOrderForExportView.setPaidAt(payment.getPaidAt());
        }
        shopOrderForExportView.setSkuOrderForExportViewList(skuOrders.parallelStream().map(
                skuOrder -> {
                    SkuOrderForExportView view = SkuOrderForExportView.from(skuOrder);
                    try {
                        ItemSnapshot itemSnapshot = itemSnapShotCacheHolder.findBySnapShotId(skuOrder.getItemSnapshotId());
                        if (itemSnapshot == null) {
                            return view;
                        }
                        view.setBarCode(itemSnapshot.getBarCode());
                    } catch (Exception e) {
                        LOG.error("Fail to find the Item Snapshot Order -> {}, SkuOrderId -> {}, ItemId -> {}, ItemSnapShotId -> {}", skuOrder.getOrderId(), skuOrder.getId(), skuOrder.getItemSnapshotId(), skuOrder.getItemId(), e);
                    }
                    return view;
                }).collect(Collectors.toList()));
        shopOrderForExportView.setOrderId(shopOrder.getId());
        shopOrderForExportView.setCreateAt(shopOrder.getCreatedAt());
        shopOrderForExportView.setWxBuyerName(Objects.requireNonNull(userNickNameCache.get(shopOrder.getBuyerId())).orElse(shopOrder.getBuyerName()));
        // profit-fill 仅仅在没有获取到exLinkedProfit的情况下进行获取
        if (shopOrderForExportView.getExLinkedProfit() == null && shopOrder.getExtra() != null && !ObjectUtils.isEmpty(shopOrder.getExtra().get("babyProfit"))) {
            try {
                Long babyProfit = Long.valueOf(shopOrder.getExtra().get("babyProfit"));
                shopOrderForExportView.setExLinkedProfit(babyProfit);
            } catch (Exception ignore) {
                // ignore
            }
        }
        // weShop profit hack, fulfill profit from extra profit container
        if (orderGroup instanceof OrderGroupViewObject) {
            boolean hasProfitView = !CollectionUtils.isEmpty(((OrderGroupViewObject) orderGroup).getOrderProfitViewList());
            if (shopOrderForExportView.getSubStoreProfit() == null && hasProfitView) {
                OrderGroupViewObject.OrderProfitView view = ((OrderGroupViewObject) orderGroup).getOrderProfitViewList().get(0);
                if (view.getProfit() != null) {
                    shopOrderForExportView.setSubStoreProfit(view.getProfit().multiply(BigDecimal.valueOf(100)).longValue());
                    shopOrderForExportView.setSubStoreProfitFrom(((OrderGroupViewObject) orderGroup).getSubStoreProfitFrom());
                    shopOrderForExportView.setSubStoreName(view.getProfitOwnerName());
                }
            }
        }
        String payName = Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).getOrDefault(ShopOrderExtra.payerName.name(), "");
        String payNo = Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).getOrDefault(ShopOrderExtra.payerNo.name(), "");

        shopOrderForExportView.setPayerName(payName);
        shopOrderForExportView.setPayerNo(payNo);

        return shopOrderForExportView;
    }

    public Workbook exportRefund(List<RefundExportVo> dataList) {
        var workman = exporter.export(dataList, RefundExportVo.class);
        return SheetMergeUtil.mergeWorkbook(workman);
    }

    /**
     * 导出订单列表,将数据打包进入Excel
     *
     * @param dataList 订单列表
     * @return 包含订单信息的Excel
     * @apiNote 警告该api将依赖订单列表中第一个的订单的ShopId判断模式
     */
    public List<OrderExportView> convertSkuOrderExportViewIntoWorkBook(List<ShopOrderForExportView> dataList) {
        // beauty the data and format
        ArrayList<List<OrderExportView>> orderVoForLists = dataList.parallelStream().map(order ->
                // 剥离ShopOrder层
                order.getSkuOrderForExportViewList().parallelStream().map(skuOrderForExportView -> {
                    // 提取SkuOrder层 与ShopOrder层融合
                    OrderExportView orderExportView = new OrderExportView();
                    BeanUtils.copyProperties(order, orderExportView);
                    BeanUtils.copyProperties(skuOrderForExportView, orderExportView);
                    orderExportView.setFee(order.getFee() == null ? "" : "" + order.getFee() / 100.0);
                    orderExportView.setGuiderProfit(order.getGuiderProfit() == null ? "" : "" + order.getGuiderProfit() / 100.0);
                    orderExportView.setSubStoreProfit(order.getSubStoreProfit() == null ? "" : "" + order.getSubStoreProfit() / 100.0);
                    orderExportView.setSubStoreWithdrawStatus(subStoreWithdrawRecordViewApp.viewTheProfitWithdrawStatus(order.getSubStoreProfitFrom()));
                    orderExportView.setPaidAt(
                            new DateTime(order.getPaidAt()).toString("yyyy-MM-dd HH:mm:ss"));
                    orderExportView.setCreateAt(
                            new DateTime(order.getCreateAt()).toString("yyyy-MM-dd HH:mm:ss"));
                    orderExportView.setConfirmAt(order.getConfirmAt() == null ? "" :
                            new DateTime(order.getConfirmAt()).toString("yyyy-MM-dd HH:mm:ss"));
                    orderExportView.setStatus(OrderStatus.fromInt(order.getStatus()).intoString());
                    return orderExportView;
                }).sorted(Comparator.comparing(SkuOrderForExportView::getId)).collect(Collectors.toList())
        ).collect(Collectors.toCollection(ArrayList::new));
        ArrayList<OrderExportView> orderExportViewList = new ArrayList<>();
        orderVoForLists.forEach(orderExportViewList::addAll);
        orderExportViewList.sort((o1, o2) -> (Long.compare(o2.getOrderId(), o1.getOrderId())));
        return orderExportViewList;
    }
}