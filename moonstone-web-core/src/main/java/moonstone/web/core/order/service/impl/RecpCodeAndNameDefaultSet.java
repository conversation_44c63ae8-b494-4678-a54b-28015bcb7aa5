package moonstone.web.core.order.service.impl;

import moonstone.order.dto.PayInfoPushCondition;

interface RecpCodeAndNameDefaultSet {
    /**
     * 初始化企业信用数据
     *
     * @param payInfoPushCondition 推送体
     * @param shopId               店铺Id
     */
    static void initRecpCodeAndNameFromShopId(PayInfoPushCondition payInfoPushCondition, Long shopId) {
        switch (shopId.intValue()) {
            case 28: {
                payInfoPushCondition.setRecpCode("91310117550044892H");
                payInfoPushCondition.setRecpName("上海敬腾贸易有限公司");
                break;
            }
            case 29: {
                payInfoPushCondition.setRecpCode("91110108563612852D");
                payInfoPushCondition.setRecpName("国科戎安生物科技（北京）有限公司");
                break;
            }
            case 30: {
                payInfoPushCondition.setRecpCode("91330105793672332L");
                payInfoPushCondition.setRecpName("杭州高歌坦图商贸有限公司");
                break;
            }
            case 31: {
                payInfoPushCondition.setRecpCode("913101160530297398");
                payInfoPushCondition.setRecpName("上海班克母婴用品有限公司");
                break;
            }
            case 32: {
                payInfoPushCondition.setRecpCode("91320594MA1T51E20T");
                payInfoPushCondition.setRecpName("苏州多来多易进出口贸易有限公司");
                break;
            }
            case 33: {
                payInfoPushCondition.setRecpCode("91310116MA1JAT2X2P");
                payInfoPushCondition.setRecpName("上海訾昶电子商务有限公司");
                break;
            }
            default: {
                payInfoPushCondition.setRecpCode("91330104MA27XBBU25");
                payInfoPushCondition.setRecpName("杭州但丁云科技有限公司");
            }
        }
    }
}
