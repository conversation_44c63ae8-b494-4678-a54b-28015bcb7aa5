package moonstone.web.core.order.dto.ccs;

import com.alibaba.fastjson.JSON;
import com.danding.mercury.pay.sdk.MercuryPayModel;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public abstract class CCSRequest {
	/**
	 * 方法名称
	 */
	private String method;

	/**
	 * 版本号
	 */
	private String version = "1.0";

	/**
	 * 请求参数
	 */
	private String bizContent;

	public void setBizModel(MercuryPayModel bizModel) {
		try {
			this.bizContent = Base64.getEncoder().encodeToString(JSON.toJSONString(bizModel).getBytes(StandardCharsets.UTF_8));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public abstract String getMethod();

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getBizContent() {
		return bizContent;
	}

	public void setBizContent(String bizContent) {
		try {
			this.bizContent = Base64.getEncoder().encodeToString(bizContent.getBytes(StandardCharsets.UTF_8));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
