package moonstone.web.core.order;

import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.event.OrderCancelEvent;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Mail: <EMAIL>
 * Data: 16/7/19
 * Author: yangzefeng
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderWriteLogic {

    @Autowired
    SkuOrderReadService skuOrderReadService;
    @Autowired
    ShopOrderReadService shopOrderReadService;
    @Autowired
    private FlowPicker flowPicker;
    @Autowired
    private OrderWriteService orderWriteService;
    @Autowired
    private GatherOrderWriteService gatherOrderWriteService;

    @Autowired
    private PaymentReadService paymentReadService;

    @Autowired
    private PaymentWriteService paymentWriteService;


    /**
     * mark order has refund that can be used as refund reject
     *
     * @param orderBase SkuOrder or ShopOrder
     * @return update success
     * @see OrderWriteService#markHasRefund(OrderBase) delegate method
     */
    public Either<Boolean> markHasRefund(OrderBase orderBase) {
        return orderWriteService.markHasRefund(orderBase);
    }

    public boolean updateOrderStatusByOrderEvent(OrderBase orderBase, OrderLevel orderLevel, OrderEvent orderEvent) {
        Flow flow = flowPicker.pick(orderBase, orderLevel);
        Integer targetStatus = flow.target(orderBase.getStatus(), orderEvent.toOrderOperation());

        if (Objects.equals(orderEvent.getValue(), OrderEvent.BUYER_CANCEL.getValue())
                || Objects.equals(orderEvent.getValue(), OrderEvent.SELLER_CANCEL.getValue())) {
            EventSender.sendApplicationEvent(new OrderCancelEvent(orderBase.getId(), orderLevel.getValue(), orderEvent));
        }

        switch (orderLevel) {
            case SHOP:
                Response<Boolean> updateShopOrderResp = orderWriteService.shopOrderStatusChanged(orderBase.getId(), orderBase.getStatus(), targetStatus);
                if (!updateShopOrderResp.isSuccess()) {
                    log.error("{} fail to update shop order(id={}) from current status:{} to target:{},cause:{}", LogUtil.getClassMethodName(),
                            orderBase.getId(), orderBase.getStatus(), targetStatus, updateShopOrderResp.getError());
                    throw new JsonResponseException(updateShopOrderResp.getError());
                }
                return updateShopOrderResp.getResult();
            case SKU:
                Response<Boolean> updateSkuOrderResp = orderWriteService.skuOrderStatusChanged(orderBase.getId(), orderBase.getStatus(), targetStatus);
                if (!updateSkuOrderResp.isSuccess()) {
                    log.error("{} fail to update sku shop order(id={}) from current status:{} to target:{},cause:{}", LogUtil.getClassMethodName(),
                            orderBase.getId(), orderBase.getStatus(), targetStatus, updateSkuOrderResp.getError());
                    throw new JsonResponseException(updateSkuOrderResp.getError());
                }
                return updateSkuOrderResp.getResult();
            case GATHER:
                return gatherOrderWriteService.updateStatus(orderBase.getId(), orderBase.getStatus(), targetStatus).take();
            default:
                throw new IllegalArgumentException("unknown.order.type");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean sellerAuthOrder(Long shopOrderId, List<SkuOrder> skuOrderList, String sellerNote) {
        Response<Boolean> authResponse = orderWriteService.updateShopOrderAuth(shopOrderId, sellerNote);
        if (!authResponse.isSuccess()) {
            log.error("failed to update shopOrder(id={}) auth by sellerNote={}, error code:{}",
                    shopOrderId, sellerNote, authResponse.getError());
            throw new JsonResponseException(authResponse.getError());
        }
        List<Payment> payments = Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP).getResult()).orElseGet(ArrayList::new)
                .stream().filter(payment -> payment.getStatus() > 0)
                .collect(Collectors.toList());

        List<Long> boundSkuOrders = new LinkedList<>();
        for (SkuOrder skuOrder : skuOrderList) {
            if (1 == skuOrder.getIsBonded()) {
                boundSkuOrders.add(skuOrder.getId());
                if (payments.isEmpty()) {
                    payments = Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(skuOrder.getId(), OrderLevel.SKU).getResult()).orElseGet(ArrayList::new)
                            .stream().filter(payment -> payment.getStatus() > 0)
                            .collect(Collectors.toList());

                }
            }
        }
        Response<Boolean> updatePushStatusResponse = orderWriteService.updateSkuOrderPushStatusByOrderId(
                shopOrderId, SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAITING_SELLER_AUTH.value());
        if (!updatePushStatusResponse.isSuccess()) {
            log.error("failed to update skuOrders push status by shopOrderId={}, currentPushStatus={}, targetPushStatus={}, error code: {}",
                    shopOrderId, SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAITING_SELLER_AUTH.value(), updatePushStatusResponse.getError());
            throw new JsonResponseException(updatePushStatusResponse.getError());
        }

        payments.stream().filter(payment -> Objects.equals(payment.getPushStatus(), PaymentPushStatus.NEED_AUTH.getValue()))
                .map(Payment::getId)
                .forEach(paymentId -> {
                    Payment update = new Payment();
                    update.setId(paymentId);
                    if (!boundSkuOrders.isEmpty()) {
                        update.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                    } else {
                        update.setPushStatus(PaymentPushStatus.NO_NEED_PUSH.getValue());
                    }

                    log.info("OrderWriteLogic.sellerAuthOrder, 审核订单-更新支付单推送状态, shopOrderId={}, updatePayment={}",
                            shopOrderId, JSON.toJSONString(update));
                    paymentWriteService.update(update);
                });

        return Boolean.TRUE;
    }

    public void turnItAsStrangeOrder(Long orderId) {
        ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
        if (shopOrder.getStatus() >= 500) {
            throw new RuntimeException("ALREADY MARKED");
        }
        orderWriteService.updateOrderStatus(orderId, OrderLevel.SHOP, shopOrder.getStatus() + 500);
        for (SkuOrder skuOrder : skuOrderReadService.findByShopOrderId(orderId).getResult()) {
            if (skuOrder.getStatus() >= 500) {
                throw new RuntimeException("ALREADY MARKED");
            }
            orderWriteService.updateOrderStatus(skuOrder.getId(), OrderLevel.SKU, skuOrder.getStatus() + 500);
        }
    }
}
