package moonstone.web.core.order;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Arguments;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.order.api.FlowPicker;
import moonstone.order.api.RefundExpressService;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.RefundDetail;
import moonstone.order.dto.RefundList;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.LoginType;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.order.convert.RefundConvertor;
import moonstone.web.core.order.dto.RefundDetailVO;
import moonstone.web.core.order.dto.RefundPageVO;
import moonstone.web.core.order.dto.RefundVO;
import moonstone.web.core.order.dto.SubStoreRefundOrderVO;
import moonstone.web.core.refund.application.RefundDetailExpressDecorator;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.core.user.service.StoreProxySubUserManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Mail: <EMAIL>
 * Data: 16/7/13
 * Author: yangzefeng
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class RefundReadLogic {

    private final SkuOrderReadService skuOrderReadService;

    private final ShopOrderReadService shopOrderReadService;

    private final RefundReadService refundReadService;

    private final UserReadService<User> userReadService;

    private final UserProfileReadService userProfileReadService;

    private final StoreProxySubUserManager storeProxySubUserManager;

    private final RefundExpressService refundExpressService;

    private final RefundDetailExpressDecorator refundDetailExpressDecorator;

    private final ObjectMapper objectMapper;

    private final FlowPicker flowPicker;

    private final UserTypeBean userTypeBean;

    private final SubStoreCache subStoreCache;

    private final ServiceProviderCache serviceProviderCache;

    private final OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    private final GuiderCache guiderCache;

    private final RefundWriteService refundWriteService;

    private final PaymentReadService paymentReadService;

    public Response<Paging<RefundList>> refundPaging(Map<String, String> refundCriteria) {
        return refundPaging(objectMapper.convertValue(refundCriteria, RefundCriteria.class));
    }

    public Result<DataPage<RefundList>> refundPagingNew(Map<String, String> refundCriteria) {
        return refundPagingNew(objectMapper.convertValue(refundCriteria, RefundCriteria.class));
    }

    public Response<Paging<RefundList>> refundPaging(RefundCriteria criteria) {

        if (criteria != null && !ObjectUtils.isEmpty(criteria.getMobile())) {
            Response<User> userR = userReadService.findBy(criteria.getMobile(), LoginType.MOBILE);
            if (!userR.isSuccess()) {
                log.error("fail to find user by mobile {}, error code:{}",
                        criteria.getMobile(), userR.getError());
                return Response.ok(Paging.empty(RefundList.class));
            } else {
                User user = userR.getResult();
                criteria.setBuyerId(user.getId());
            }
        }
        CommonUser operator = UserUtil.getCurrentUser();
        //数据隔离
        if (criteria != null && operator != null && !ObjectUtils.isEmpty(operator.getShopId())) {
            criteria.setShopId(operator.getShopId());
            if (storeProxySubUserManager.isAuthProxy(operator.getId(), operator.getShopId())) {
                //找出所有用户的id
                List<Long> longs = storeProxySubUserManager.findUserIdFromStoreProxyAtShop(operator.getId(), operator.getShopId());
                if (longs.isEmpty()) {
                    return Response.ok(Paging.empty(RefundList.class));
                }
                if (!ObjectUtils.isEmpty(criteria.getBuyerId()) && !longs.contains(criteria.getBuyerId())) {
                    return Response.ok(Paging.empty(RefundList.class));
                }
                if (ObjectUtils.isEmpty(criteria.getBuyerId())) {
                    criteria.setBuyerIds(longs);
                }
            }
        }

        //处理申报单号
        limitRefundIdByDeclareId(criteria);
        // 处理支付单号
        disposePaymentOutId(criteria);

        Response<Paging<RefundList>> refundsR = refundReadService.findBy(criteria);
        if (!refundsR.isSuccess()) {
            return refundsR;
        }
        Paging<RefundList> refunds = refundsR.getResult();

        for (RefundList refundList : refunds.getData()) {
            try {
                Flow flow = flowPicker.pick(refundList.getSkuOrders().get(0), OrderLevel.SKU);
                RefundVO refund = new RefundVO(refundList.getRefund());
                refundList.setRefund(refund);
                refund.setWxUserName(Optional.ofNullable(userProfileReadService.findProfileByUserId(refund.getBuyerId()).getResult()).map(UserProfile::getRealName).orElse(refund.getBuyerName()));
                // 退款流程走到 同意退款退货 即 按照同意退款来走
                Integer status = refund.getStatus();
                if (status == OrderStatus.RETURN_APPLY_AGREED.getValue()) {
                    status = OrderStatus.REFUND_APPLY_AGREED.getValue();
                }
                Set<OrderOperation> operations = flow.availableOperations(status);
                refundList.setOperations(operations);
                refundExpressService.queryRefundExpress(refund.getId())
                        .ifSuccess(refundList::setRefundExpressInfo);
                //根据订单填充申报单号、门店名、服务商、导购名
                appendInfoByShopOrderId(refundList, refundList.getSkuOrders().get(0).getOrderId());
            } catch (Exception e) {
                log.error("fail to get refund({})'s operations, just don't return it's operations. cause: ",
                        JSON.toJSONString(refundList.getRefund()), e);
            }
        }
        return Response.ok(refunds);
    }

    /**
     * 根据支付单号查询退款单
     *
     * @param criteria 请求参数
     */
    private void disposePaymentOutId(RefundCriteria criteria) {
        if (criteria == null || StringUtils.isBlank(criteria.getPaymentOutId())) {
            return;
        }
        Response<Payment> response = paymentReadService.findByOutId(criteria.getPaymentOutId());
        if (!response.isSuccess()) {
            throw new ApiException("未找到对应的支付单号");
        }
        Payment payment = response.getResult();
        Response<List<OrderPayment>> orderPaymentList = paymentReadService.findOrderIdsByPaymentId(payment.getId());
        List<Long> shopOrderIds = orderPaymentList.getResult().stream().map(OrderRelation::getOrderId).toList();
        //查询订单与退款单关系
        List<Refund> refunds = refundReadService.findByShopOrderIds(shopOrderIds).getResult();
        if (!CollectionUtils.isEmpty(refunds)) {
            criteria.setIdList(refunds.stream().map(Refund::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 根据对应的订单填充申报单号、门店名、服务商名、导购名
     *
     * @param refundList
     * @param orderId
     */
    private void appendInfoByShopOrderId(RefundList refundList, Long orderId) {
        //申报单号
        var shopOrder = shopOrderReadService.findById(orderId).getResult();
        if (shopOrder == null) {
            return;
        }
        refundList.setDeclareId(shopOrder.getDeclaredId());

        //门店名
        Optional<SubStore> subStore = StringUtils.isBlank(shopOrder.getOutShopId()) ? Optional.empty() :
                subStoreCache.findById(Long.parseLong(shopOrder.getOutShopId()));
        if (subStore.isEmpty()) {
            return;
        }
        refundList.setSubStoreName(subStore.get().getName());

        //服务商名
        var serviceProvider = serviceProviderCache.findBySubStoreUserIdAndShopId(
                subStore.get().getUserId(), shopOrder.getShopId());
        if (serviceProvider != null) {
            refundList.setServiceProviderName(serviceProvider.getName());
        }

        //导购名
        if (shopOrder.getReferenceId() != null) {
            var subStoreTStoreGuider = guiderCache.findByShopIdAndUserId(shopOrder.getShopId(), shopOrder.getReferenceId());
            if (subStoreTStoreGuider.isEmpty()) {
                log.info("subStoreTStoreGuider find failed by {}", shopOrder.getRefererId());
            } else {
                refundList.setSubStoreTStoreGuiderName(subStoreTStoreGuider.map(SubStoreTStoreGuider::getStoreGuiderNickname).orElse(null));
            }
        }
    }

    /**
     * 处理查询条件申报单号，将其转化为对应的退货单id
     *
     * @param criteria
     */
    private void limitRefundIdByDeclareId(RefundCriteria criteria) {
        if (criteria == null || StringUtils.isBlank(criteria.getDeclaredId())) {
            return;
        }
        criteria.setIdList(Lists.newArrayList(-1L));

        //查询订单
        var orderCriteria = new OrderCriteria();
        orderCriteria.setDeclaredId(criteria.getDeclaredId());
        var shopOrderIds = shopOrderReadService.listIdsBy(orderCriteria).getResult();
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return;
        }

        //查询订单与退款单关系
        List<Refund> refunds = refundReadService.findByShopOrderIds(shopOrderIds).getResult();
        if (!CollectionUtils.isEmpty(refunds)) {
            criteria.setIdList(refunds.stream().map(Refund::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 在原有的基础上修改了接口的返回格式
     *
     * <AUTHOR>
     */
    public Result<DataPage<RefundList>> refundPagingNew(RefundCriteria criteria) {

        if (criteria != null && !ObjectUtils.isEmpty(criteria.getMobile())) {
            Response<User> userR = userReadService.findBy(criteria.getMobile(), LoginType.MOBILE);
            if (!userR.isSuccess()) {
                log.error("fail to find user by mobile {}, error code:{}",
                        criteria.getMobile(), userR.getError());
                return Result.data(DataPage.build(0, 0, 0, 0, Paging.empty(RefundList.class).getData()));
            } else {
                User user = userR.getResult();
                criteria.setBuyerId(user.getId());
            }
        }
        CommonUser operator = UserUtil.getCurrentUser();
        //数据隔离
        if (criteria != null && operator != null && !ObjectUtils.isEmpty(operator.getShopId())) {
            if (storeProxySubUserManager.isAuthProxy(operator.getId(), operator.getShopId())) {
                //找出所有用户的id
                List<Long> longs = storeProxySubUserManager.findUserIdFromStoreProxyAtShop(operator.getId(), operator.getShopId());
                if (longs.isEmpty()) {
                    return Result.data(DataPage.build(0, 0, 0, 0, Paging.empty(RefundList.class).getData()));
                }
                if (!ObjectUtils.isEmpty(criteria.getBuyerId()) && !longs.contains(criteria.getBuyerId())) {
                    return Result.data(DataPage.build(0, 0, 0, 0, Paging.empty(RefundList.class).getData()));
                }
                if (ObjectUtils.isEmpty(criteria.getBuyerId())) {
                    criteria.setBuyerIds(longs);
                }
            }
        }

        //处理申报单号
        limitRefundIdByDeclareId(criteria);

        Response<Paging<RefundList>> refundsR = refundReadService.findBy(criteria);
        if (!refundsR.isSuccess()) {
            return Result.data(DataPage.build(0, 0, 0, 0, refundsR.getResult().getData()));
        }
        Paging<RefundList> refunds = refundsR.getResult();
        //获取分页信息
        long pageSize;
        long currentPage;
        //获取当页条数
        if (java.util.Objects.nonNull(criteria) && java.util.Objects.nonNull(criteria.getSize())) {
            pageSize = criteria.getSize();
        } else {
            pageSize = 20L;
        }
        //获取当前页数
        if (java.util.Objects.nonNull(criteria.getPageNo())) {
            currentPage = criteria.getPageNo();
        } else {
            currentPage = 1L;
        }
        //构建分页信息类
        TotalCalculation totalCalculation = new TotalCalculation(pageSize, currentPage, refunds.getTotal(), 0L);
        totalCalculation.setTotalPage(totalCalculation.getPageSize(), totalCalculation.getTotalCount());

        for (RefundList refundList : refunds.getData()) {
            try {
                Flow flow = flowPicker.pick(refundList.getSkuOrders().get(0), OrderLevel.SKU);
                RefundVO refund = new RefundVO(refundList.getRefund());
                refundList.setRefund(refund);
                refund.setWxUserName(Optional.ofNullable(userProfileReadService.findProfileByUserId(refund.getBuyerId()).getResult()).map(UserProfile::getRealName).orElse(refund.getBuyerName()));
                Set<OrderOperation> operations = flow.availableOperations(refund.getStatus());
                refundList.setOperations(operations);
                refundExpressService.queryRefundExpress(refund.getId())
                        .ifSuccess(refundList::setRefundExpressInfo);
                //根据订单填充申报单号、门店名、服务商、导购名
                appendInfoByShopOrderId(refundList, refundList.getSkuOrders().get(0).getOrderId());
            } catch (Exception e) {
                log.error("fail to get refund({})'s operations, just don't return it's operations. cause: ",
                        JSON.toJSONString(refundList.getRefund()), e);
            }
        }
        return Result.data(DataPage.build(totalCalculation, refunds.getData()));
    }

    public Response<RefundDetail> findDetailForBuyer(Long afterSaleId, CommonUser commonUser) {
        Refund refund = getRefund(afterSaleId);
        if (!Objects.equals(commonUser.getId(), refund.getBuyerId())) {
            log.error("refund buyer id:{} not equal current operation user(id:{})", refund.getBuyerId(), commonUser.getId());
            return Response.fail("refund.not.belong.to.owner.buyer");
        }
        Response<RefundDetail> rRefundDetail = refundReadService.findDetailById(afterSaleId);
        if (rRefundDetail.isSuccess()) {
            refundDetailExpressDecorator.decorateReturnExpressTrack(rRefundDetail.getResult());
        }
        return rRefundDetail;
    }

    public Response<RefundDetail> findDetailForSeller(Long afterSaleId, CommonUser commonUser) {
        Refund refund = getRefund(afterSaleId);
        if (!Objects.equals(commonUser.getShopId(), refund.getShopId())) {
            log.error("refund shop id:{} not equal current operation seller(id:{})", refund.getShopId(), commonUser.getShopId());
            return Response.fail("refund.not.belong.to.owner.seller");
        }
        Response<RefundDetail> rRefundDetail = refundReadService.findDetailById(afterSaleId);
        if (rRefundDetail.isSuccess()) {
            refundDetailExpressDecorator.decorateReturnExpressTrack(rRefundDetail.getResult());
        }
        return rRefundDetail;
    }

    public Response<RefundDetail> findDetailForAdmin(Long afterSaleId, CommonUser commonUser) {
        Refund refund = getRefund(afterSaleId);
        if (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser)) {
            log.error("refund shop id:{} not equal current operation admin or operator(userId:{})", refund.getShopId(), commonUser.getShopId());
            return Response.fail("refund.not.for.user.not.admin.or.operator");
        }
        Response<RefundDetail> rRefundDetail = refundReadService.findDetailById(afterSaleId);
        if (rRefundDetail.isSuccess()) {
            refundDetailExpressDecorator.decorateReturnExpressTrack(rRefundDetail.getResult());
        }
        return rRefundDetail;
    }

    public Response<RefundDetail> findDetailForDistributor(Long afterSaleId, CommonUser commonUser) {
        Refund refund = getRefund(afterSaleId);
        List<OrderRefund> orderRefundList = Optional.ofNullable(refundReadService.findOrderIdsByRefundId(refund.getId())
                .getResult()).orElseThrow(() -> Translate.exceptionOf("退款单查找订单失败"));
        Long orderId = orderRefundList
                .stream().filter(level -> OrderLevel.fromInt(level.getOrderType()) == OrderLevel.SHOP)
                .findFirst().map(OrderRelation::getOrderId)
                .orElseGet(() -> orderRefundList.stream().filter(level -> OrderLevel.fromInt(level.getOrderType()) == OrderLevel.SKU)
                        .map(OrderRelation::getOrderId).map(skuOrderReadService::findById).map(Response::getResult)
                        .map(SkuOrder::getOrderId).findFirst().orElseThrow(() -> Translate.exceptionOf("退款单对应订单查找失败")));
        ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
        boolean notAllow = (commonUser.getWeShopId() == null || !Objects.equals(shopOrder.getOutShopId(), commonUser.getWeShopId().toString()))
                && !Objects.equals(shopOrder.getBuyerId(), commonUser.getId());
        if (notAllow) {
            log.error("refund(id={}) not belong to distributor(id={})", afterSaleId, commonUser.getId());
            return Response.fail("refund.not.belong.to.owner.distributor");
        }
        Response<RefundDetail> rRefundDetail = refundReadService.findDetailById(afterSaleId);
        if (rRefundDetail.isSuccess()) {
            refundDetailExpressDecorator.decorateReturnExpressTrack(rRefundDetail.getResult());
        }
        return rRefundDetail;
    }

    public Response<List<RefundDetail>> findDetailByOrderForDistributor(Long orderId, Integer orderType, CommonUser
            commonUser) {
        try {
            switch (OrderLevel.fromInt(orderType)) {
                case SHOP: {
                    Response<ShopOrder> rShopOrder = shopOrderReadService.findById(orderId);
                    if (!rShopOrder.isSuccess()) {
                        log.error("failed to find shop order by id={}, error code: {}", orderId, rShopOrder.getError());
                        throw new JsonResponseException(rShopOrder.getError());
                    }
                    ShopOrder shopOrder = rShopOrder.getResult();
                    boolean notAllow = (commonUser.getWeShopId() == null || !Objects.equals(shopOrder.getOutShopId(), commonUser.getWeShopId().toString()))
                            && !Objects.equals(shopOrder.getBuyerId(), commonUser.getId());
                    if (notAllow) {
                        log.error("shop order(id={}) not belong to distributor(id={})", orderId, commonUser.getId());
                        throw new JsonResponseException("shop.order.not.belong.to.weDistributor");
                    }
                    break;
                }
                case SKU: {
                    Response<SkuOrder> rSkuOrder = skuOrderReadService.findById(orderId);
                    if (!rSkuOrder.isSuccess()) {
                        log.error("failed to find sku order by id={}, error code: {}", orderId, rSkuOrder.getError());
                        throw new JsonResponseException(rSkuOrder.getError());
                    }
                    SkuOrder skuOrder = rSkuOrder.getResult();
                    boolean notAllow = (commonUser.getWeShopId() == null || !Objects.equals(skuOrder.getOutShopId(), commonUser.getWeShopId().toString()))
                            && !Objects.equals(skuOrder.getBuyerId(), commonUser.getId());
                    if (notAllow) {
                        log.error("sku order(id={}) not belong to distributor(id={})", orderId, commonUser.getId());
                        throw new JsonResponseException("sku.order.not.belong.to.weDistributor");
                    }
                    break;
                }
                default: {
                    throw new JsonResponseException("unknown.order.level");
                }
            }
            Response<List<Refund>> rRefundsList = refundReadService.findAllByOrderIdAndOrderLevel(orderId, OrderLevel.fromInt(orderType));
            if (!rRefundsList.isSuccess()) {
                log.error("failed to find all refunds by orderId={}, orderType={}, error code: {}", orderId, orderType, rRefundsList.getError());
                throw new JsonResponseException(rRefundsList.getError());
            }
            List<RefundDetail> refundDetailList = new ArrayList<>();
            for (Refund refund : rRefundsList.getResult()) {
                if (refund.getStatus() <= -3) {
                    Response<RefundDetail> rRefundDetail = refundReadService.findDetailById(refund.getId());
                    if (!rRefundDetail.isSuccess()) {
                        log.error("failed to find refund detail by id={}, error code: {}", refund.getId(), rRefundDetail.getError());
                        throw new JsonResponseException(rRefundDetail.getError());
                    }
                    refundDetailExpressDecorator.decorateReturnExpressTrack(rRefundDetail.getResult());
                    refundDetailList.add(rRefundDetail.getResult());
                }
            }
            return Response.ok(refundDetailList);
        } catch (Exception e) {
            log.error("{} failed to find detail for distributor by orderId={}, orderType={}, cause: {}", LogUtil.getClassMethodName(), orderId, orderType, e);
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 根据id查询售后单
     *
     * @param refundId 售后单id
     * @return 售后单
     */
    public Refund getRefund(Long refundId) {
        //获取售后单
        Response<Refund> afterSaleRes = refundReadService.findById(refundId);
        if (!afterSaleRes.isSuccess()) {
            log.error("fail to find refund by id {}, error code:{}",
                    refundId, afterSaleRes.getError());
            throw new JsonResponseException(afterSaleRes.getError());
        }
        if (Arguments.isNull(afterSaleRes.getResult())) {
            log.error("not find refund by id:{}", refundId);
            throw new JsonResponseException("refund.not.exist");
        }

        return afterSaleRes.getResult();
    }

    /**
     * 退款单查询
     *
     * @param refundCriteria
     * @return
     */
    public Paging<RefundPageVO> find(RefundCriteria refundCriteria) {
        //查询退款单
        Integer pageNum = refundCriteria.getPageNo() == null ? 1 : refundCriteria.getPageNo();
        Integer pageSize = refundCriteria.getSize() == null ? 20 : refundCriteria.getSize();
        Paging<Refund> refundPage = refundReadService.findRefundBy(pageNum, pageSize, refundCriteria).getResult();
        if (CollectionUtils.isEmpty(refundPage.getData())) {
            return Paging.empty();
        }

        //查询退款单关联的商品
        var refundItemMap = refundReadService.findRefundItems(
                refundPage.getData().stream().map(Refund::getId).collect(Collectors.toList())).getResult();

        //模型转换
        return new Paging<>(refundPage.getTotal(), RefundConvertor.convert(refundPage.getData(), refundItemMap));
    }

    /**
     * 退款单信息的分页查询
     *
     * @param criteria
     * @return
     */
    public Paging<SubStoreRefundOrderVO> findWithOrderInfo(RefundCriteria criteria) {
        //查询退款单
        Integer pageNum = criteria.getPageNo() == null ? 1 : criteria.getPageNo();
        Integer pageSize = criteria.getSize() == null ? 20 : criteria.getSize();
        Paging<Refund> refundPage = refundReadService.findRefundBy(pageNum, pageSize, criteria).getResult();
        if (CollectionUtils.isEmpty(refundPage.getData())) {
            return new Paging<>(refundPage.getTotal(), Collections.emptyList());
        }

        var refundIds = refundPage.getData().stream().map(Refund::getId).collect(Collectors.toList());

        //退款单关联的主订单 key=refund_id
        var shopOrderMap = refundReadService.findShopOrder(refundIds).getResult();

        //查询退款单关联的商品 key=refund_id
        var refundItemMap = refundReadService.findRefundItems(refundIds).getResult();

        //门店名称(key=shop_order_id, value=门店名称)
        var subStoreNameMap = findSubStoreNameMap(shopOrderMap.values());

        //订单的角色快照信息 key = shop_order_id
        var shopOrderRoleSnapshotMap = findShopOrderRoleMap(shopOrderMap.values());

        //构造
        return new Paging<>(refundPage.getTotal(), refundPage.getData().stream()
                .map(refund -> RefundConvertor.convert(refund, shopOrderMap.get(refund.getId()), refundItemMap.get(refund.getId()),
                        subStoreNameMap, shopOrderRoleSnapshotMap))
                .collect(Collectors.toList()));
    }

    /**
     * 订单的角色快照信息
     *
     * @param shopOrders
     * @return key = shop_order_id
     */
    private Map<Long, List<OrderRoleSnapshot>> findShopOrderRoleMap(Collection<ShopOrder> shopOrders) {
        if (CollectionUtils.isEmpty(shopOrders)) {
            return Collections.emptyMap();
        }

        return orderRoleSnapshotReadService.findMapByShopOrderIds(shopOrders.stream().map(ShopOrder::getId).toList(),
                OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);
    }

    /**
     * 查询订单对应的门店名称
     *
     * @param shopOrders
     * @return
     */
    private Map<Long, String> findSubStoreNameMap(Collection<ShopOrder> shopOrders) {
        if (CollectionUtils.isEmpty(shopOrders)) {
            return Collections.emptyMap();
        }

        Map<Long, String> result = new HashMap<>();
        shopOrders.forEach(shopOrder -> {
            if (!OrderOutFrom.SUB_STORE.Code().equals(shopOrder.getOutFrom()) ||
                    StringUtils.isBlank(shopOrder.getOutShopId())) {
                return;
            }

            var subStore = subStoreCache.findById(Long.parseLong(shopOrder.getOutShopId())).orElse(null);
            if (subStore != null) {
                result.put(shopOrder.getId(), subStore.getName());
            }
        });

        return result;
    }

    /**
     * 根据退款单id查询
     *
     * @param refundId
     * @return
     */
    public RefundDetailVO findById(Long refundId) {
        var refund = refundReadService.findById(refundId).getResult();
        if (refund == null) {
            return null;
        }

        //主订单
        var shopOrderList = findShopOrder(refundId);

        //商品
        var refundItemMap = refundReadService.findRefundItems(List.of(refundId)).getResult();

        return RefundConvertor.convert(refund, shopOrderList, refundItemMap.get(refundId));
    }

    /**
     * 依次判断当前用户是否为 服务商、门店店长、导购 和 消费者，来构造退款单的查询参数
     * <br/>不考虑退款单关联到子订单的情况
     */
    public RefundCriteria getCriteriaByCurrentUser(Long userId, Long shopId) {
        RefundCriteria criteria = new RefundCriteria();

        //当前用户是服务商
        ServiceProvider serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, shopId);
        if (serviceProvider != null) {
            //获取服务商下面的所有门店店长的用户id
            var subStoreUserIds = serviceProviderCache.findBelongSubStoreUserId(shopId, userId);
            if (subStoreUserIds.isEmpty() || CollectionUtils.isEmpty(subStoreUserIds.get())) {
                return null;
            }

            //对应的门店id
            var subStoreIds = subStoreUserIds.get().stream()
                    .map(subStoreUserId -> subStoreCache.findByShopIdAndUserId(shopId, subStoreUserId))
                    .filter(Optional::isPresent)
                    .map(e -> e.get().getId())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subStoreIds)) {
                return null;
            }

            criteria.setSubStoreIdList(subStoreIds);
            return criteria;
        }

        //当前用户是门店
        var subStore = subStoreCache.findByShopIdAndUserId(shopId, userId);
        if (subStore.isPresent()) {
            criteria.setSubStoreIdList(Lists.newArrayList(subStore.get().getId()));
            return criteria;
        }

        //当前用户是导购
        var guider = guiderCache.findByShopIdAndUserId(shopId, userId);
        if (guider.isPresent()) {
            criteria.setGuiderUserId(userId);
            return criteria;
        }

        //当前用户是消费者
        criteria.setBuyerId(userId);
        return criteria;
    }

    /**
     * 查询退款单关联的主订单
     *
     * @param refundId
     * @return
     */
    private List<ShopOrder> findShopOrder(Long refundId) {
        var orderRefunds = refundReadService.findOrderIdsByRefundId(refundId).getResult();
        if (CollectionUtils.isEmpty(orderRefunds)) {
            return Collections.emptyList();
        }

        List<Long> shopOrderIds = new ArrayList<>();

        //直接关联到主订单的
        orderRefunds.stream()
                .filter(entity -> OrderLevel.SHOP.getValue() == entity.getOrderType())
                .map(OrderRefund::getOrderId)
                .forEach(shopOrderIds::add);

        //关联到子订单的
        var skuOrders = skuOrderReadService.findByIds(orderRefunds.stream()
                .filter(entity -> OrderLevel.SKU.getValue() == entity.getOrderType())
                .map(OrderRefund::getOrderId).collect(Collectors.toList())).getResult();
        if (!CollectionUtils.isEmpty(skuOrders)) {
            skuOrders.stream().map(SkuOrder::getOrderId).forEach(shopOrderIds::add);
        }

        return shopOrderReadService.findByIds(shopOrderIds).getResult();
    }

}
