package moonstone.web.core.order.service;

import moonstone.common.model.Either;

import java.util.List;

/**
 * 订单推送系统, 用于将订单推送至外部系统
 * todo 重构使用SkuDomain 以解偶订单推送逻辑和订单与真实商品映射关系
 */
public interface OrderPushService {
    /**
     * 推送订单
     *
     * @param shopOrderId 主订单号
     * @return 加入队列成功
     */
    Boolean pushOrder(Long shopOrderId);

    /**
     * 启动自动推送队列
     */
    void startPush();

    /**
     * 扫描自动推送
     *
     * @return 推送了的订单列表
     */
    List<Long> scanAndPush();

    /**
     * 修改推送状态
     *
     * @param shopOrderId       订单号
     * @param targetPushStatus  期望将订单推送状态修改为
     * @param currentPushStatus 目前的订单推送状态
     * @return 成功
     */
    Either<Boolean> updatePushStatusByOrderId(Long shopOrderId, Integer targetPushStatus, Integer currentPushStatus);

    void pushV3OrderPayer(Long shopOrderId);
}
