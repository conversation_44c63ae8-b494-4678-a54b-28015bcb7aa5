package moonstone.web.core.order.component;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.Gson;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.event.OrderRefundEvent;
import moonstone.order.dto.PaymentCriteria;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.RefundList;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.events.settle.PaymentSettleEvent;
import moonstone.web.core.order.api.FinanceContrastPushService;
import moonstone.web.core.order.dto.FinanceContrastDto;
import moonstone.web.core.order.dto.OutRequestVO;
import moonstone.web.core.order.dto.ResponseVerifyData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RestController
public class FinanceContrastPushServiceImpl implements FinanceContrastPushService {
    final private Gson gson = new Gson();
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    FunctionSwitch functionSwitch;
    //todo 更换url
    @Value("${nio.post.url}")
    private String postUrl;
    @Value("${Y800.finance.project.url}")
    private String apiUrl;
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private RefundReadService refundReadService;
    private final ArrayList<FinanceContrastDto> financeContrastDtoArrayList = new ArrayList<>();
    @Value("${wechat.applet.mchId:**********}")
    private String mchId;
    @Value("${alipay.account:<EMAIL>}")
    private String alipayAccount;
    @Value("${wechat.applet.appId:wx86fe5657944bcaa2}")
    private String wechatAppId;
    @Value("${alipay.pid:****************}")
    private String alipayPid;

    /**
     * 仅用于退款
     * 从退款信息中提取普通支付单打包出推单信息,默认退款
     *
     * @param refund
     * @return
     */
    private List<FinanceContrastDto> packFinanceContrastInfoFromRefund(Refund refund) {
        List<OrderPayment> orderPaymentList = paymentReadService.findOrderIdsByPaymentId(refund.getPaymentId()).getResult();
        ArrayList<FinanceContrastDto> financeContrastDtos = new ArrayList<>(orderPaymentList.size());
        for (OrderPayment orderPayment : orderPaymentList) {
            FinanceContrastDto financeContrastDto = new FinanceContrastDto();
            financeContrastDto.setAppType(3);//com.danding.yang.core.enums.APPType 1:Y800,2:微商城
            financeContrastDto.setIncome(0);//默认退款因此数据为0
            financeContrastDto.setSn(refund.getOutId());
            financeContrastDto.setRelevanceSn(orderPayment.getOrderId().toString());
            financeContrastDto.setOutSn(refund.getPaySerialNo());
            financeContrastDto.setOutUpdateTime(refund.getUpdatedAt().getTime());
            financeContrastDto.setOutCreateTime(refund.getCreatedAt().getTime());
            financeContrastDto.setExpenses(refund.getFee() == null ? 0 : refund.getFee());//默认退款
            financeContrastDto.setBussinessType(4);//1:下单,4:退款
            financeContrastDto.setTerminalType(3);
            switch (orderPayment.getOrderType())//1:店铺订单 2:子订单
            {
                case 1: {
                    ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId())
                            .getResult();
                    financeContrastDto.setTerminalType(shopOrder.getChannel());
                    financeContrastDto.setUserName(shopOrder.getBuyerName());
                    break;
                }
                case 2: {
                    SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId())
                            .getResult();
                    financeContrastDto.setTerminalType(skuOrder.getChannel());
                    financeContrastDto.setUserName(skuOrder.getBuyerName());
                    break;
                }
                default: {
                    financeContrastDto.setUserName("Unknow => SystemDefault");
                }

            }
            if (refund.getChannel().contains("alipay")) {
                financeContrastDto.setPayWay(5);//5:alipay
            } else if (refund.getChannel().contains("wechatpay")) {
                financeContrastDto.setPayWay(6);
            } else {
                financeContrastDto.setPayWay(0);
            }
            Payment payment = paymentReadService.findById(orderPayment.getPaymentId()).getResult();
            if (belongUsAccount(payment.getRecpAccount())) {
                financeContrastDto.setRemark("但丁收款-退款");
            } else {
                financeContrastDto.setRemark("商家自主收款-退款");
            }
            financeContrastDtos.add(financeContrastDto);
        }
        return financeContrastDtos;
    }

    private boolean belongUsAccount(String recpAccount) {
        return new HashSet<>(Arrays.asList(alipayAccount, mchId, wechatAppId, alipayPid)).contains(recpAccount);
    }

    /**
     * 仅用于下单，退款提现需魔改
     * 从普通支付单打包出推单信息,默认下单
     * 退款等需要重新手动打包
     *
     * @param payment
     * @return
     */
    private List<FinanceContrastDto> packFinanceContrastInfoFromPayment(Payment payment) {
        List<OrderPayment> orderPaymentList = paymentReadService.findOrderIdsByPaymentId(payment.getId()).getResult();
        ArrayList<FinanceContrastDto> financeContrastDtos = new ArrayList<>(orderPaymentList.size());
        for (OrderPayment orderPayment : orderPaymentList) {
            FinanceContrastDto financeContrastDto = new FinanceContrastDto();
            financeContrastDto.setAppType(3);//com.danding.yang.core.enums.APPType 1:Y800,2:微商城
            financeContrastDto.setIncome(payment.getFee());
            financeContrastDto.setSn(payment.getOutId());
            financeContrastDto.setRelevanceSn(orderPayment.getOrderId().toString());
            financeContrastDto.setOutSn(payment.getPaySerialNo());
            financeContrastDto.setOutUpdateTime(payment.getUpdatedAt().getTime());
            financeContrastDto.setOutCreateTime(payment.getCreatedAt().getTime());
            financeContrastDto.setExpenses(0);//默认下单
            financeContrastDto.setBussinessType(1);//1:下单
            financeContrastDto.setTerminalType(3);
            switch (orderPayment.getOrderType())//1:店铺订单 2:子订单
            {
                case 1: {
                    ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId())
                            .getResult();
                    financeContrastDto.setTerminalType(shopOrder.getChannel());
                    financeContrastDto.setUserName(shopOrder.getBuyerName());
                    break;
                }
                case 2: {
                    SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId())
                            .getResult();
                    financeContrastDto.setTerminalType(skuOrder.getChannel());
                    financeContrastDto.setUserName(skuOrder.getBuyerName());
                    break;
                }
                default: {
                    financeContrastDto.setUserName("Unknow => SystemDefault");
                }

            }
            if (payment.getChannel().contains("alipay")) {
                financeContrastDto.setPayWay(5);//5:alipay
            } else if (payment.getChannel().contains("wechatpay")) {
                financeContrastDto.setPayWay(6);
            } else {
                financeContrastDto.setPayWay(0);
            }
            if (belongUsAccount(payment.getRecpAccount())) {
                financeContrastDto.setRemark("但丁收款");
            } else {
                financeContrastDto.setRemark("商家自主收款");
            }
            financeContrastDtos.add(financeContrastDto);
        }
        return financeContrastDtos;
    }

    @Override
    @EventListener(OrderRefundEvent.class)
    public void onOrderRefundEvent(OrderRefundEvent event) {
        if (!event.getOrderOperation().equals(OrderEvent.REFUND_SUCCESS.toOrderOperation()))//仅仅真实退款才会推送报表
        {
            return;
        }
        long refundId = event.getRefundId();
        try {
            Refund refund = refundReadService.findById(refundId).getResult();
            List<FinanceContrastDto> financeContrastDtos = packFinanceContrastInfoFromRefund(refund);
            pushFinanceContrast(financeContrastDtos);
        } catch (Exception ex) {
            log.error("push refund fail(refundId:{},cause:{})", refundId, ex.getMessage());
        }
    }

    @Override
    @EventListener(PaymentSettleEvent.class)
    public void onOrderPaymentEvent(PaymentSettleEvent event) {
        long paymentId = event.getPaymentId();
        try {
            Payment payment = paymentReadService.findById(paymentId).getResult();
            List<FinanceContrastDto> financeContrastDtos = packFinanceContrastInfoFromPayment(payment);
            pushFinanceContrast(financeContrastDtos);
        } catch (Exception ex) {
            log.error("push payment fail(paymentId:{},cause:{})", paymentId, ex.getMessage());
        }
    }

    /**
     * 推送支付单进入财务对账
     *
     * @param financeContrastDtos DTO列表
     * @return 是否推送成功, 不需要处理
     */
    public Response<FinanceContrastDto> pushFinanceContrast(List<FinanceContrastDto> financeContrastDtos, Long sec) {
        if (CollectionUtils.isEmpty(financeContrastDtos)) {
            return Response.fail("empty list");
        }
        try {
            OutRequestVO outRequestVO = new OutRequestVO();
            outRequestVO.setAppId("weDistribution");
            StringBuilder sb = new StringBuilder();
            financeContrastDtos.forEach(financeContrastDto -> sb.append("nt:" + financeContrastDto.getAppType() + "s:" + financeContrastDto.getSn()));
            outRequestVO.setRequestId(sb.toString());
            outRequestVO.setUrl(apiUrl + "/finance/contrast/save");
            HashMap<String, String> param = new HashMap<>();
            param.put("financeInfoJsonStr", JSON.toJSONString(financeContrastDtos));
            outRequestVO.setParams(JSON.toJSONString(param));
            outRequestVO.setRequestType("POST");
            ResponseVerifyData responseVerifyData = new ResponseVerifyData();
            responseVerifyData.setFieldName("success");
            responseVerifyData.setFieldValue("true");
            ArrayList<ResponseVerifyData> arrayList = new ArrayList<>();
            arrayList.add(responseVerifyData);
            outRequestVO.setResponseVerify(JSON.toJSONString(arrayList));
            outRequestVO.setScheduleType(0);
            outRequestVO.setScheduleInterval(sec);
            outRequestVO.setMaxCount(10);
            HttpRequest request = HttpRequest.post(postUrl + "/add").form(objectMapper.convertValue(outRequestVO, Map.class));
            try {
                if (request.ok()) {
                    String data = request.body();
                    val r = gson.fromJson(data, Response.class);
                    if (r.isSuccess()) {

                        return Response.ok();
                    }
                    log.error("[nio] post error,cause:{} data:{}", r.getError(), data);
                }
                log.error("[nio] post error,cause:{} data:{}", "network error", JSON.toJSONString(outRequestVO));
                return Response.fail("nio.add.error");
            } catch (Exception ex) {
                log.error("[nio] post error,cause:{} data:{}", ex.getMessage(), JSON.toJSONString(outRequestVO));
                ex.printStackTrace();
                return Response.fail("nio.add.error");
            }

            //HttpRequest request = HttpRequest.post(apiUrl + "/finance/contrast/save").form(ImmutableMap.of("financeInfoJsonStr", JSON.toJSONString(financeContrastDtos)));
        } catch (Exception ex) {
            log.error("push FinanceContrast error,cause:{}", ex);
            return Response.fail("nio.add.error");
        }
    }

    @Override
    public Response<FinanceContrastDto> pushFinanceContrast(List<FinanceContrastDto> financeContrastDtos) {
        return pushFinanceContrast(financeContrastDtos, 60 * 1000L);
    }

    @GetMapping("/api/finance/push/system/manualPush")
    public Response<Long> manualPush(@RequestParam(name = "start", defaultValue = "0") int start, @RequestParam(name = "end", defaultValue = "-1") int end, @RequestParam(name = "size", defaultValue = "2000") int size, @RequestParam(name = "time", defaultValue = "1000") Long time) {
        if (!functionSwitch.getFinancePush()) {
            throw new JsonResponseException("application.not.provide.function");
        }
        long count = 0;
        if (end == -1) {
            end = Integer.MAX_VALUE;
        }
        PaymentCriteria criteria = new PaymentCriteria();
        Function<List<FinanceContrastDto>, Response<FinanceContrastDto>> pushIt = f -> pushFinanceContrast(f, time);
        criteria.setPageSize(size);
        for (int i = start; i <= end; i++) {
            criteria.setPageNo(i);
            val rPayment = paymentReadService.pagingPayments(criteria);
            if (!rPayment.isSuccess()) {
                log.error("fail.push.payment page:{}", i);
                break;
            }
            List<Payment> payments = rPayment.getResult().getData();
            //push
            // TODO: 19-2-12 push
            try {
                count += payments.stream()
                        .filter(payment -> payment.getStatus().equals(OrderStatus.PAID.getValue()))
                        .map(this::packFinanceContrastInfoFromPayment)
                        .map(pushIt)
                        .filter(Response::isSuccess)
                        .collect(Collectors.toList()).size();
            } catch (Exception ex) {
                log.error("push refund finance contrast failed,cause:{}", ex.getMessage());
                ex.printStackTrace();
                return Response.fail("push.finance.failed");
            }
            if (payments.size() < size) {
                break;
            }
            //skip save and check
            //payments.forEach(payment->mongoTemplate.save(payment.getId(),"pushedPayment"));
        }
        RefundCriteria criteria1 = new RefundCriteria();
        criteria1.setSize(size);

        for (int i = start; i <= end; i++) {
            val rRefund = refundReadService.findBy(criteria1);
            if (!rRefund.isSuccess()) {
                log.error("fail.push.refund page:{}", i);
                break;
            }
            List<RefundList> refunds = rRefund.getResult().getData();
            //push
            // TODO: 19-2-12 push
            try {
                count += refunds.stream()
                        .map(RefundList::getRefund)
                        .filter(refund -> refund.getStatus().equals(OrderStatus.REFUND.getValue()))
                        .map(this::packFinanceContrastInfoFromRefund)
                        .map(pushIt)
                        .filter(Response::isSuccess)
                        .collect(Collectors.toList()).size();
            } catch (Exception ex) {
                log.error("push refund finance contrast failed,cause:{}", ex.getMessage());
                ex.printStackTrace();
                return Response.fail("push.finance.failed");
            }
            if (refunds.size() < size) {
                break;
            }
            //skip save and check
            //refunds.forEach(refundList->mongoTemplate.save(refundList.getRefund().getId(),"pushedRefund"));
        }
        return Response.ok(count);
    }

    @Deprecated
    public Response<FinanceContrastDto> pushPayment(Payment payment) {
        ArrayList<FinanceContrastDto> financeContrastDtos;
        synchronized (financeContrastDtoArrayList) {
            if (payment != null) {
                financeContrastDtoArrayList.addAll(packFinanceContrastInfoFromPayment(payment));
            }
            if (CollectionUtils.isEmpty(financeContrastDtoArrayList)) {
                return Response.fail("empty list");
            }
            financeContrastDtos = new ArrayList<>(financeContrastDtoArrayList);
            financeContrastDtoArrayList.clear();
        }
        return pushFinanceContrast(financeContrastDtos);
    }
}
