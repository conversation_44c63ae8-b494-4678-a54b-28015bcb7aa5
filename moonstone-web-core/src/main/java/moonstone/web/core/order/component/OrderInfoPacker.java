package moonstone.web.core.order.component;


import moonstone.event.OrderCreatedEvent;
import moonstone.event.OrderEvent;
import moonstone.order.service.OrderWriteService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class OrderInfoPacker {

    @Resource
    private OrderWriteService orderWriteService;

    @EventListener(OrderCreatedEvent.class)
    public void packOrderInfo(OrderEvent event) {
        orderWriteService.writeOrderInfo(event.orderId());
    }
}
