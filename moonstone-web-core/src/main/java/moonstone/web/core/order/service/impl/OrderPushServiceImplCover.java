package moonstone.web.core.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.Lists;
import com.hazelcast.core.HazelcastInstanceNotActiveException;
import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.OmsV3Rpc;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.exception.EitherMsgException;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentOrder;
import moonstone.common.utils.UUID;
import moonstone.common.utils.*;
import moonstone.event.OrderPushEvent;
import moonstone.item.emu.SkuTagIndex;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.PushOrder;
import moonstone.order.dto.PushOrderItem;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.PaymentWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.Shop;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.service.PayerInfoReadService;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.web.core.component.api.Y800V3Api;
import moonstone.web.core.component.api.bo.y800v3.OrderPayerChangeRequest;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.order.convert.Y800V3DeliveryCreateConvertor;
import moonstone.web.core.order.service.OrderPushService;
import org.joda.time.DateTime;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.slf4j.helpers.LoggerConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.Key;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Primary
@Component
public class OrderPushServiceImplCover extends TaskWithLock implements OrderPushService {

    @Autowired
    private PushSystemDep pushSystemDep;

    @RemoteAPI
    private Y800V3Api y800StorageRemoteAPI;

    @Autowired
    private SourceShopQuerySlice sourceShopQuerySlice;

    @Resource
    private ThirdPartyUserReadService thirdPartyUserReadService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private Y800V3DeliveryCreateConvertor y800V3DeliveryCreateConvertor;

    @Resource
    private PaymentWriteService paymentWriteService;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    private BlockingQueue<Long> shopOrderWaitQueue = null;
    private final Set<Long> unique = new ConcurrentSkipListSet<>();
    private final Payment noNeedPaidPayment = new Payment();

    @EventListener(ContextRefreshedEvent.class)
    public void initBlockQueue() {
        shopOrderWaitQueue = redissonClient.getBlockingQueue(String.format("[%s]-Queue", OrderPushService.class.getSimpleName()));
    }

    @Override
    public void startPush() {
        if (shopOrderWaitQueue == null) {
            initBlockQueue();
        }
        log.info("{} order-push-system start at [{}]", LogUtil.getClassMethodName(), new DateTime());
        Thread thread = new Thread(() -> {
            Optional<Long> orderIdOpt = Optional.empty();
            while (true) {
                try {
                    log.debug("{} push queue still contain [{}]", LogUtil.getClassMethodName(), shopOrderWaitQueue.size());
                    orderIdOpt = Optional.of(shopOrderWaitQueue.take());
                    orderIdOpt.ifPresent(unique::remove);
                    orderIdOpt.ifPresent(this::orderPushFlow);
                } catch (HazelcastInstanceNotActiveException hazelcastClose) {
                    log.error("{} hazelcast closed", LogUtil.getClassMethodName(), hazelcastClose);
                    break;
                } catch (Exception ex) {
                    log.error("{} fail to take order to push[{}]", LogUtil.getClassMethodName(), orderIdOpt, ex);
                }
            }
        });
        thread.setName("OrderPushService");
        thread.start();
    }

    /**
     * 大贸推送功能
     * 向Y800推送
     * 目前使用的是老外部订单系统
     *
     * @param shopOrder            主订单
     * @param waitPushSkuOrderList 子订单
     * @param payment              支付单
     * @param thirdPartyUserShop   推送用的帐号
     */
    private void pushNoBorderOrder(ShopOrder shopOrder, List<SkuOrder> waitPushSkuOrderList, @Nullable Payment payment, ThirdPartyUserShop thirdPartyUserShop) {
        try (OmsV3Rpc storageRemoteAPI = y800StorageRemoteAPI) {
            log.info("推送Y800系统 ApiV3 订单号 {}", shopOrder.getId());
            log.info("推送前 支付单信息 {}", JSONUtil.toJsonStr(payment));
            storageRemoteAPI.setAppId(thirdPartyUserShop.getThirdPartyCode());
            storageRemoteAPI.setSecret(thirdPartyUserShop.getThirdPartyKey());

            //构造入参
            Map<String, String> thirdPartyExtra = Optional.ofNullable(thirdPartyUserShop.getExtra()).orElseGet(HashMap::new);
            Y800ShipmentOrder y800ShipmentOrder = y800V3DeliveryCreateConvertor.convert(thirdPartyExtra.get(ShopExtra.WhCode.getCode()),
                    thirdPartyExtra.get(ShopExtra.Y800StorageAccessCode.getCode()), shopOrder, waitPushSkuOrderList);

            //发起调用
            log.info("OrderPushServiceImplCover.pushNoBorderOrder, api-v3发货单创建, shopOrderId={}, 推送参数={}",
                    shopOrder.getId(), JSON.toJSONString(y800ShipmentOrder));
            String shipmentCode = storageRemoteAPI.deliveryCreate(y800ShipmentOrder)
                    .elseMap(e -> e instanceof EitherMsgException ? "null" : Either.<String>error(e).take());

            //更新子订单的推送标志
            waitPushSkuOrderList.forEach(skuOrder -> markSkuOrderFinishPush(skuOrder, ThirdPartySystem.Y800_V3));

            //更新主订单
            Map<String, String> extra = Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new);
            extra.put("YSC", shipmentCode);
            pushSystemDep.orderWriteService.updateOrderExtra(shopOrder.getId(), OrderLevel.SHOP, extra);

            // 更新支付单的推送状态
            updatePaymentPushSuccess(shopOrder, waitPushSkuOrderList);
        } catch (Exception ex) {
            log.error("推送Y800系统 ApiV3 订单号 {} 错误信息 {}", shopOrder.getId(), ex.getMessage(), ex);
            throw new RuntimeException(ex);
        }
    }

    private void updatePaymentPushSuccess(ShopOrder shopOrder, List<SkuOrder> skuOrders) {
        var payment = y800V3DeliveryCreateConvertor.findPayment(shopOrder, skuOrders);
        if (payment == null) {
            return;
        }

        var updateObject = new Payment();
        updateObject.setId(payment.getId());
        updateObject.setPushStatus(PaymentPushStatus.PUSH_SUCCESS.getValue());
        paymentWriteService.update(updateObject);
    }

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    OutSystemIdProvider outSystemIdProvider;

    private String revokeSeq(String declareOrderNo) {
        OutSystemIdProvider.OutSystemIdContainer container = outSystemIdProvider.decode(declareOrderNo);
        if (container.getSeq() == 0) {
            return declareOrderNo;
        }
        int idLen = (container.getId() + "").length();
        int startAt = declareOrderNo.length() - 3 - idLen;
        return declareOrderNo.substring(0, startAt) + "0" + declareOrderNo.substring(startAt + 1);
    }

    /**
     * 向洋800推送订单信息
     *
     * @param shopOrder            主订单 决定 单号 申报人 支付人 收货人
     * @param waitPushSkuOrderList 决定订单细节 税费 单品 数量 价格
     * @param payment              支付单 关联收款方式 关联先前推送的支付单信息
     */
    private void pushOrderIntoY800(ShopOrder shopOrder, List<SkuOrder> waitPushSkuOrderList, Payment payment, ThirdPartyUserShop thirdPartyUserShop) {
        List<SkuOrder> skuOrders = waitPushSkuOrderList.stream().filter(skuOrder -> hasNotPush(skuOrder, ThirdPartySystem.Y800_V2)).toList();
        try {
            final Long shopId = shopOrder.getShopId();
            ReceiverInfo receiverInfo = pushSystemDep.receiverInfoReadService.findByOrderId(shopOrder.getId(), OrderLevel.SHOP).getResult().get(0);
            log.info("decrypt receiverInfo:{}", receiverInfo);
            PushOrder pushOrder = new PushOrder();

            log.info("v2订单推送-参数组装-begin, shopOrder={}", JSON.toJSONString(shopOrder));
            pushOrder.setOrderNo(pushSystemDep.y800OrderIdGenerator.getDeclareId(shopOrder));
            if (payment.getChannel().equals("umf")) {
                pushOrder.setOrderNo(revokeSeq(pushOrder.getOrderNo()));
                log.info("v2订单推送-参数组装-begin, shopOrderId={}, revokeSeq后的declareId={}", shopOrder.getId(), pushOrder.getOrderNo());
            }
            String receiverName = Optional.ofNullable(receiverInfo.getReceiveUserName()).orElse("默认收件人");
            pushOrder.setName(receiverName.substring(0, Math.min(receiverName.length(), 19)));
            pushOrder.setSourcePlatform(pushSystemDep.y800SourcePlatform);
            pushOrder.setMobile(receiverInfo.getMobile());
            pushOrder.setIdCard(receiverInfo.getPaperNo());
            pushOrder.setProvince(receiverInfo.getProvince());
            pushOrder.setCity(receiverInfo.getCity());
            pushOrder.setDistrict(receiverInfo.getRegion());
            pushOrder.setStreet(receiverInfo.getDetail());
            pushOrder.setRemark(Optional.ofNullable(shopOrder.getSellerNote()).orElse("") + " 保税仓发货");

            PayerInfo payerInfo = mongoTemplate.findOne(Query.query(Criteria.where("orderId").is(shopOrder.getId())), PayerInfo.class);
            if (payerInfo == null) {
                // compatible
                pushOrder.setPayerName(Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).get("payerName"));
                pushOrder.setPayerNumber(Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).get("payerNo"));
            } else {
                Key key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
                PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(key, payerInfo);
                pushOrder.setPayerName(info.getName());
                pushOrder.setPayerNumber(info.getNo());
            }

            if (payment.getChannel().contains("alipay") || payment.getChannel().contains("mockpay")) {
                pushOrder.setPayChannel("alipay");
            } else if (payment.getChannel().contains("wechat")) {
                pushOrder.setPayChannel("wechatpay");
            } else if (PaymentChannelEnum.ALLINPAY.getCode().equals(payment.getChannel()) ||
                    PaymentChannelEnum.ALLINPAY_YST.getCode().equals(payment.getChannel())) {
                pushOrder.setPayChannel("TONGLIAN");
            } else {
                pushOrder.setPayChannel(payment.getChannel());
            }

            if (!ObjectUtils.isEmpty(Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).get("subBankNo"))) {
                pushOrder.setTradeNo(shopOrder.getExtra().get("subBankNo"));
            } else {
                pushOrder.setTradeNo(payment.getPaySerialNo());
            }
            pushOrder.setPayAmount(BigDecimal.valueOf(shopOrder.getFee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
            pushOrder.setAccessCode(thirdPartyUserShop.getThirdPartyCode());
            // 为供销设置特殊值
            if (OrderOutFrom.WE_SHOP.Code().equals(shopOrder.getOutFrom())) {
                if (Objects.equals(sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).orElse(-1L), shopOrder.getShopId())) {
                    thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), shopOrder.getReferenceId())
                            .map(ThirdPartyUser::getThirdPartyId).ifSuccess(userId -> pushOrder.setAccessCode("webB2C" + userId)).take();
                }
            }
            pushOrder.setMerchantCode(pushSystemDep.y800MerchantCode);
            pushSystemDep.y800PayMchManager.findPay800MchByShopId(shopId).map(Y800PayMch::getY800Mch)
                    .map(Optional::ofNullable)
                    .orElse(Optional.empty())
                    .filter(StringUtils::hasText)
                    .ifPresent(pushOrder::setMerchantCode);

            List<PushOrderItem> itemList = new ArrayList<>();
            Integer shopDiscount = shopOrder.getDiscount() == null ? 0 : shopOrder.getDiscount();
            Integer shipDiscount = shopOrder.getOriginShipFee() - shopOrder.getShipFee();
            BigDecimal needShareDiscount = BigDecimal.valueOf(shopDiscount + shipDiscount).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
            List<BigDecimal> originPrice = new ArrayList<>();
            for (SkuOrder skuOrder : skuOrders) {
                originPrice.add(BigDecimal.valueOf(skuOrder.getOriginFee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
            }
            List<BigDecimal> shareDiscount = ShareUtils.share(needShareDiscount, originPrice, 1);
            //为运费计算做的准备
            long realShopOriginFee = 0;
            for (SkuOrder so : pushSystemDep.skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult()) {
                realShopOriginFee += so.getOriginFee();
            }

            for (int i = 0; i < skuOrders.size(); i++) {
                SkuOrder skuOrder = skuOrders.get(i);
                PushOrderItem pushOrderItem = pushSystemDep.orderTaxSplitManager.makePushOrderItem_M(skuOrder, shareDiscount.get(i), shopOrder.getShipFee().longValue(), realShopOriginFee).take();
                itemList.add(pushOrderItem);
            }
            pushOrder.setItemList(itemList);

            String pushOrderJsonStr = JSON.toJSONString(pushOrder);
            log.debug("OrderPUshServiceImplCover.pushOrderIntoY800, shopOrderId={}, push begin, pushOrderJsonStr={}", shopOrder.getId(), pushOrderJsonStr);
            String bizData = new String(Base64.getEncoder().encode(pushOrderJsonStr.getBytes()));
            String partnerId = pushSystemDep.y800PartnerCode;
            String token = pushSystemDep.y800PartnerKey;
            String v = "2.0";
            String serviceName = "order.out.set";
            String signStr = "bizData=" + bizData +
                    "partnerId=" + partnerId +
                    "serviceName=" + serviceName +
                    "v=" + v
                    + token;
            @SuppressWarnings("deprecation") String sign = MD5Util.MD5(signStr);
            if (sign == null) {
                throw new RuntimeException(new Translate("为订单[%s]签名失败", shopOrder.getId()).toString());
            }
            System.out.println(sign.toLowerCase());

            Map<String, Object> params = new HashMap<>();
            params.put("sign", sign);
            params.put("v", v);
            params.put("partnerId", partnerId);
            params.put("serviceName", serviceName);
            params.put("bizData", bizData);
            log.debug("OrderPUshServiceImplCover.pushOrderIntoY800, shopOrderId={}, push params={}", shopOrder.getId(), params);

            HttpRequest request = HttpRequest.post(pushSystemDep.y800YangSupport);
            request.form(params);
            if (request.ok()) {
                Map<String, Object> map = JSON.parseObject(request.body());
                log.info("OrderPUshServiceImplCover.pushOrderIntoY800, shopOrderId={}, push order result, request.body()={}", shopOrder.getId(), JSON.toJSONString(map));
                if (!"success".equals(map.getOrDefault("code", "fail").toString()) && !(map.getOrDefault("errorMsg", "").toString().contains("已存在"))) {
                    throw new RuntimeException(new Translate("订单[%s]推送失败,子订单列表[%s..] 大小 %s 原因 %s", shopOrder.getId(), skuOrders.get(0).getId(), skuOrders.size(), map.get("errorMsg").toString()).toString());
                }

                for (SkuOrder skuOrder : skuOrders) {
                    markSkuOrderFinishPush(skuOrder, ThirdPartySystem.Y800_V2);
                }
            } else {
                throw new JsonResponseException(500, "push.order.http.request.fail");
            }
        } catch (Exception ex) {
            log.error("OrderPUshServiceImplCover.pushOrderIntoY800, [" + pushSystemDep.y800YangSupport + "/api/]Request push skuOrder failed, shopOrderId={}",
                    shopOrder.getId(), ex);

            for (SkuOrder skuOrder : skuOrders) {
                pushSystemDep.skuOrderWriteService.updatePushResult(skuOrder.getId(), SkuOrderPushStatus.ERROR.value(), ex.getMessage(), null);
            }
            throw ex;
        }
    }

    /**
     * 获取订单的已推送列表
     *
     * @param skuOrderExtra 子订单额外信息
     * @return 已推送平台列表
     */
    private List<String> getPushedSystemIdStrList(Map<String, String> skuOrderExtra) {
        return new ArrayList<>(Arrays.asList(Optional.ofNullable(skuOrderExtra).orElse(new HashMap<>()).getOrDefault(SkuTagIndex.pushSystem.name(), "").split(":")));
    }

    /**
     * 标记订单第三方推送完毕
     *
     * @param skuOrder         订单
     * @param thirdPartySystem 推送系统
     */
    private void markSkuOrderFinishPush(SkuOrder skuOrder, ThirdPartySystem thirdPartySystem) {
        ThreadLocal<AtomicInteger> retry = ThreadLocal.withInitial(() -> new AtomicInteger(0));
        if (thirdPartySystem == null || thirdPartySystem == ThirdPartySystem.Unknown) {
            throw new RuntimeException(new Translate("订单[%s] 未知平台[Unknown]不允许标记推送完成", skuOrder.getId()).toString());
        }
        Map<String, String> skuOrderExtra = Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new);
        List<String> pushedSystemIdStrList = getPushedSystemIdStrList(skuOrderExtra);
        pushedSystemIdStrList.add(thirdPartySystem.Id().toString());
        StringBuilder listConcat = new StringBuilder();
        pushedSystemIdStrList.forEach(system -> listConcat.append(system).append(":"));
        skuOrderExtra.put(SkuTagIndex.pushSystem.name(), listConcat.toString());
        pushSystemDep.orderWriteService.updateOrderExtra(skuOrder.getId(), OrderLevel.SKU, skuOrderExtra);
        skuOrder.setExtra(skuOrderExtra);
        if (new Random().nextInt(15) == 13) {
            boolean updateSuccess = Optional.ofNullable(pushSystemDep.skuOrderReadService.findById(skuOrder.getId()).getResult())
                    .map(OrderBase::getExtra)
                    .map(extra -> extra.get(SkuTagIndex.pushSystem.name()))
                    .filter(listConcat.toString()::equals)
                    .isPresent();
            if (!updateSuccess) {
                if (retry.get().incrementAndGet() > 15) {
                    retry.remove();
                    throw new RuntimeException(new Translate("更新子订单[%s]第三方平台[%s]推送状态失败", skuOrder.getId(), thirdPartySystem.name()).toString());
                }
                markSkuOrderFinishPush(skuOrder, thirdPartySystem);
            }
        }
        retry.remove();
    }

    /**
     * 查询订单是否没有推送过该平台
     * 不检测是否该推这个平台
     *
     * @param skuOrder         订单
     * @param thirdPartySystem 推送系统
     * @return 没有推送过
     */
    private boolean hasNotPush(SkuOrder skuOrder, ThirdPartySystem thirdPartySystem) {
        if (thirdPartySystem == ThirdPartySystem.Unknown) {
            log.warn("{} skuOrder[{}] try to push at unknown system", LogUtil.getClassMethodName(), skuOrder.getId());
            return false;
        }
        return !getPushedSystemIdStrList(skuOrder.getExtra()).contains(thirdPartySystem.Id().toString());
    }

    /**
     * 根据订单绑定的推送系统进行推送,如果子订单重推送,则将无视推送阶段进行全程推送
     *
     * @param shopOrderId 待推送的订单Id
     * @return 推送目标平台是否无报错
     */
    @Override
    public Boolean pushOrder(Long shopOrderId) {
        if (!unique.contains(shopOrderId)) {
            unique.add(shopOrderId);
            shopOrderWaitQueue.offer(shopOrderId);
        } else {
            log.debug("{} queue [size => {}, contain => {}]", LogUtil.getClassMethodName(), shopOrderWaitQueue.size(), shopOrderWaitQueue.toArray());
            log.warn("{} skip the order because queue[size => {}, contain => {}] has already contain the order [{}]", LogUtil.getClassMethodName()
                    , unique.size(), Json.toJson(unique), shopOrderId);
            if (unique.size() != shopOrderWaitQueue.size()) {
                log.warn("{} unique[Size => {}] shopOrderWaitQueue[Size => {}]", LogUtil.getClassMethodName(),
                        unique.size(), shopOrderWaitQueue.size());
                unique.clear();
            }
        }
        return true;
    }

    /*
     * 订单信息流转   从hazelcast中获取锁,以避免并发情况下重复订单
     *
     * @param shopOrderId 订单Id
     */
    void orderPushFlow(Long shopOrderId) {
        if (redissonClient == null) {
            log.warn("{} lock system is down for order [{}] because hazelcast is null", LogUtil.getClassMethodName(), shopOrderId);
            orderPushFlowWithoutLock(shopOrderId);
            return;
        }
        String lockName = String.format("[OrderPushLock](%s)", shopOrderId);
        Lock lock = redissonClient.getLock(lockName);

        processWithLock(lock, lockName, shopOrderId, shopOrderId, this::orderPushFlowWithoutLock);
    }

    void orderPushFlowWithoutLock(Long shopOrderId) {
        Future<Boolean> doneBeforeTimeOut = CompletableFuture.supplyAsync(() -> {
            try {
                MDC.put(LoggerConstant.MDC_KEY_TRACE_ID, UUID.generate().toString());

                orderPushFlowAction(shopOrderId);
                return true;
            } finally {
                MDC.clear();
            }
        });
        try {
            doneBeforeTimeOut.get(120, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("{} fail to push order[Id => {}] because timeout", LogUtil.getClassMethodName(), shopOrderId, e);
            throw new RuntimeException(e.getMessage(), e.getCause());
        }
    }

    /*
     * 订单信息流转
     *
     * @param shopOrderId 订单Id
     */
    void orderPushFlowAction(Long shopOrderId) {
        final SkuOrderReadService skuOrderReadService = pushSystemDep.skuOrderReadService;
        final PaymentReadService paymentReadService = pushSystemDep.paymentReadService;
        log.info("开始推送订单 {}", shopOrderId);

        ShopOrder shopOrder = pushSystemDep.shopOrderReadService.findById(shopOrderId).getResult();
        Shop shop = pushSystemDep.shopCacheHolder.findShopById(shopOrder.getShopId());
        List<SkuOrder> allSkuOrderListByShopOrder = skuOrderReadService.findByOrderIdWithStatus(shopOrderId, OrderStatus.PAID.getValue()).getResult();
        if (allSkuOrderListByShopOrder.isEmpty()) {
            log.warn("{} empty skuOrder ? no support for this kind of order[{}]", LogUtil.getClassMethodName(), shopOrderId);
            return;
        }

        // 决定推送平台,推送顺序将由skuOrder中的pushSystem决定,默认情况下将按照子订单Id顺序和聚合的推送系统类别进行推单
        Map<ThirdPartySystem, List<SkuOrder>> skuOrderListMapByPushSystem = groupSkuOrderByPushSystem(allSkuOrderListByShopOrder);

        Payment payment = noNeedPaidPayment;
        if (!Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).getOrDefault(ShopOrderExtra.skipPayment.name(), "")
                .equals(shopOrder.getId().toString())) {
            // fixme: 目前只支持主订单关联支付单 因此目前只使用这个方式搜索
            Supplier<Optional<Payment>> paymentSupplier = () -> Optional
                    .ofNullable(paymentReadService.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP).getResult())
                    .orElseThrow(() -> new RuntimeException(new Translate("订单[%s]对应支付单查询查询失败").toString()))
                    .stream()
                    .filter(relatedPayment -> Objects.equals(relatedPayment.getStatus(), OrderStatus.PAID.getValue()))
                    .filter(relatedPayment -> Objects.equals(relatedPayment.getPushStatus(), PaymentPushStatus.PUSH_SUCCESS.getValue()) ||
                            Objects.equals(relatedPayment.getPushStatus(), PaymentPushStatus.NO_NEED_PUSH.getValue()))
                    .findFirst();
            Optional<Payment> paymentOpt = paymentSupplier.get();
            if (paymentOpt.isEmpty()) {
                log.warn("{} order [{}] payment is not complete push flow yet", LogUtil.getClassMethodName(), shopOrderId);
                boolean update = pushSystemDep.orderWriteService.updateSkuOrderPushStatusByOrderId(shopOrderId, SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS.value(), 99).isSuccess();
                if (paymentSupplier.get().isPresent()) {
                    update = pushSystemDep.orderWriteService.updateSkuOrderPushStatusByOrderId(shopOrderId, SkuOrderPushStatus.WAITING.value(), 99).isSuccess();
                }
                if (!update) {
                    log.error("{} fail to update SkuOrder by orderId[{}] | pushStatus = {}"
                            , LogUtil.getClassMethodName(), shopOrderId, SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS.value());
                }
                return;
            }
            payment = paymentOpt.get();

            /*
             * 检测支付单关联订单信息
             */
            if (!checkPaymentRelatedOrderStatus(payment)) {
                log.warn("{} order[{}] still need wait payment[{}] push", LogUtil.getClassMethodName(), shopOrder.getId(), payment.getId());
                return;
            }
        } else {
            log.warn("{} order[{}] no need payment", LogUtil.getClassMethodName(), shopOrderId);
        }

        //  确定推送平台  推送帐号由缓存读取
        for (ThirdPartySystem system : skuOrderListMapByPushSystem.keySet()) {
            //  fixme:  订单状态一致才允许推送,可能会有特殊情况 日后添加特殊规则  可能导致各种问题
            Optional<List<SkuOrder>> waitToPushListOpt = filterNotNeedPushOrder(skuOrderListMapByPushSystem.get(system), system);
            if (waitToPushListOpt.isEmpty()) {
                log.debug("{} order[{}} not need push at system[{}]", LogUtil.getClassMethodName(), shopOrderId, system.name());
                continue;
            }
            List<SkuOrder> waitToPushList = waitToPushListOpt.get();

            Supplier<ThirdPartyUserShop> thirdPartyUserShopSupplier = () ->
                    Objects.requireNonNull(pushSystemDep.thirdPartyUserShopReadService.findByShopId(shop.getId()).getResult())
                            .stream().filter(account -> Objects.equals(system.Id(), account.getThirdPartyId())).findFirst()
                            .orElseThrow(() -> new RuntimeException(new Translate("查找对应推送系统帐号失败").toString()));

            final ThirdPartyUserShop thirdPartyUserShop =
                    OrderOutFrom.WE_SHOP.Code().equals(shopOrder.getOutFrom()) && sourceShopQuerySlice.queryProjectIdByShopIdAndSource(shopOrder.getShopId(), MirrorSource.GongXiao.name()).isSuccess() ?
                            thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), shopOrder.getReferenceId()).map(ThirdPartyUser::getThirdPartyId)
                                    .map(accessCode -> {
                                        ThirdPartyUserShop weShopAccount = new ThirdPartyUserShop();
                                        weShopAccount.setThirdPartyCode(accessCode);
                                        return weShopAccount;
                                    })
                                    .orElseGet(thirdPartyUserShopSupplier) : thirdPartyUserShopSupplier.get();
            log.debug("{} push order[{}] by system[{}] ant Account[{}]", LogUtil.getClassMethodName(), shopOrderId, system, thirdPartyUserShop.toString());
            try {
                switch (system) {
                    case Y800_V2 -> pushOrderIntoY800(shopOrder, waitToPushList, payment, thirdPartyUserShop);
                    case Y800_V3 -> pushNoBorderOrder(shopOrder, waitToPushList, payment, thirdPartyUserShop);
                    case GongXiao ->
                        // 该推送由YANG800 代替完成
                            waitToPushList.forEach(skuOrder -> markSkuOrderFinishPush(skuOrder, ThirdPartySystem.GongXiao));
                    case Unknown -> log.error("{} unknown push System for order[{}] skuList[{},..] size:{}"
                            , LogUtil.getClassMethodName(), shopOrderId, waitToPushList.get(0).getId(), waitToPushList.size());
                }
            } catch (Exception ex) {
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("订单推送失败", new Translate("订单[%s] 推送失败", shopOrderId).toString(), ex, EmailReceiverGroup.DEVELOPER));
                EventSender.sendApplicationEvent(new OrderPushEvent(shopOrderId, null, SkuOrderPushStatus.ERROR.value()));
                log.error("{} order [{}] push error", LogUtil.getClassMethodName(), shopOrderId, ex);
            }
        }
        // 如果订单全部推送完成则将订单标记为推送完成
        // 预备使用函数
        Predicate<SkuOrder> isPushed = skuOrder -> {
            List<ThirdPartySystem> pushedSystemList = getPushedSystemIdStrList(skuOrder.getExtra()).stream()
                    .filter(StringUtils::hasText)
                    .map(Integer::parseInt).distinct().map(ThirdPartySystem::fromInt).toList();
            List<ThirdPartySystem> shouldPushSystemList = Optional.ofNullable(skuOrder.getTags()
                            .get(SkuTagIndex.pushSystem.name())).map(str -> str.split(","))
                    .map(Arrays::asList)
                    .orElse(new ArrayList<>()).stream()
                    .distinct()
                    .filter(StringUtils::hasText)
                    .map(Integer::parseInt)
                    .map(ThirdPartySystem::fromInt).toList();
            return pushedSystemList.containsAll(shouldPushSystemList);
        };
        Predicate<List<SkuOrder>> isAllPushed = list ->
                list.stream().allMatch(isPushed);
        // 将其判断标记
        for (ThirdPartySystem thirdPartySystem : skuOrderListMapByPushSystem.keySet()) {
            List<SkuOrder> skuOrderList = skuOrderListMapByPushSystem.get(thirdPartySystem);
            if (!isAllPushed.test(skuOrderList)) {
                log.warn("{} skuOrderList[{},...] is not all pushed,thirdPartySystem[{}:{}]"
                        , LogUtil.getClassMethodName(), skuOrderList.get(0).getId(), thirdPartySystem.name(), thirdPartySystem.Id());
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("订单推送不完全完成", Translate.of("订单[orderId => %s] 子订单不完全推送完成, 请检查为何子订单推送失败", shopOrderId), EmailReceiverGroup.DEVELOPER));
                EventSender.sendApplicationEvent(new OrderPushEvent(shopOrderId, null, SkuOrderPushStatus.WAITING.value()));
                continue;
            }
            EventSender.sendApplicationEvent(new OrderPushEvent(shopOrderId, null, SkuOrderPushStatus.FINISHED.value()));
            // 如果出错了,则无法处理(目标平台无事务回归),此等数据库错误难以处理,只能多次重试,但目前未碰到可重试的错误因此对其信赖
            skuOrderList.forEach(skuOrder -> pushSystemDep.skuOrderWriteService.updatePushResult(skuOrder.getId(), SkuOrderPushStatus.FINISHED.value(), "SUCCESS", null));
        }
    }

    /**
     * 检测支付单对应订单是否都已经进入到 [待推送|已推送|不需要推送] 状态
     *
     * @param payment 支付单
     * @return 进入可推送界限了
     */
    private boolean checkPaymentRelatedOrderStatus(Payment payment) {
        final HashSet<Integer> allowPushSet = new HashSet<>(Arrays.asList(SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.FINISHED.value()
                , SkuOrderPushStatus.NOT_NEED.value(), SkuOrderPushStatus.RETRY_PUSH.value(), SkuOrderPushStatus.ERROR.value()));
        List<SkuOrder> skuOrderList = new ArrayList<>();
        Optional.ofNullable(pushSystemDep.paymentReadService.findOrdersByPaymentId(payment.getId()).getResult())
                .orElseGet(ArrayList::new)
                .forEach(order -> {
                    if (order instanceof SkuOrder) {
                        skuOrderList.add((SkuOrder) order);
                    }
                    if (order instanceof ShopOrder) {
                        skuOrderList.addAll(pushSystemDep.skuOrderReadService.findByShopOrderId(order.getId()).getResult());
                    }
                });
        return skuOrderList.stream().map(SkuOrder::getPushStatus).allMatch(allowPushSet::contains);
    }


    /**
     * 由第三方平台信息聚合子订单
     *
     * @param skuOrders 待推送的子订单
     * @return 聚合完成的订单
     */
    private Map<ThirdPartySystem, List<SkuOrder>> groupSkuOrderByPushSystem(List<SkuOrder> skuOrders) {
        Map<ThirdPartySystem, List<SkuOrder>> result = new LinkedHashMap<>();
        Long shopOrderId = skuOrders.get(0).getOrderId();
        for (SkuOrder skuOrder : skuOrders) {
            // 不属于三方商品的则退出聚合循环
            if (!Objects.equals(skuOrder.getIsThirdPartyItem(), 1)) {
                continue;
            }
            List<ThirdPartySystem> thirdPartySystemList = Optional.ofNullable(skuOrder.getTags().get(SkuTagIndex.pushSystem.name()))
                    .map(pushSystemStr -> pushSystemStr.split(","))
                    .map(Arrays::asList)
                    .orElseThrow(() -> new RuntimeException(new Translate("订单[%s] 子定单[%s]第三方系统无法确认 [%s]",
                            shopOrderId, skuOrder.getId(), skuOrder.getTagsJson()).toString()))
                    .stream()
                    .map(Integer::parseInt)
                    .map(ThirdPartySystem::fromInt).toList();

            for (ThirdPartySystem system : thirdPartySystemList) {
                result.putIfAbsent(system, new ArrayList<>());
                result.get(system).add(skuOrder);
            }
        }
        return result;
    }

    /**
     * 检测推送子订单是否推送状态一致
     * 如果不一致则抛出错误
     *
     * @param skuOrders 待推送子订单
     */
    private Optional<List<SkuOrder>> filterNotNeedPushOrder(List<SkuOrder> skuOrders, ThirdPartySystem thirdPartySystem) {
        boolean pushStatusSame = skuOrders.stream().map(SkuOrder::getPushStatus).allMatch(skuOrders.get(0).getPushStatus()::equals);
        boolean anyNull = skuOrders.stream().map(SkuOrder::getPushStatus).anyMatch(Objects::isNull);
        if (!pushStatusSame || anyNull) {
            log.error("{} order[{}] has not all same pushStatus,in order to prevent lost goods so we stop push it",
                    LogUtil.getClassMethodName(), skuOrders.get(0).getOrderId());
            return Optional.empty();
        }

        List<SkuOrder> waitToPushList = new ArrayList<>(skuOrders.size());
        for (SkuOrder skuOrder : skuOrders) {
            try {
                switch (SkuOrderPushStatus.fromInt(skuOrder.getPushStatus())) {
                    case WAITING, ERROR:
                        if (hasNotPush(skuOrder, thirdPartySystem)) {
                            waitToPushList.add(skuOrder);
                        }
                        break;
                    case RETRY_PUSH:
                        log.debug("{} retry push the skuOrder so remove the pushSystem list of skuOrder[{}]",
                                LogUtil.getClassMethodName(), skuOrder.getId());
                        skuOrder.getExtra().remove(SkuTagIndex.pushSystem.name());
                        waitToPushList.add(skuOrder);
                        break;
                    default:
                }
            } catch (Exception ex) {
                log.error("{} fail to judge skuOrder[{}] push status[{}]", LogUtil.getClassMethodName(), skuOrder.getId(),
                        skuOrder.getPushErrorMsg(), ex);
            }
        }

        return waitToPushList.isEmpty() ? Optional.empty() : Optional.of(waitToPushList);
    }

    @Override
    public List<Long> scanAndPush() {
        List<Long> errorOrderIds = new ArrayList<>();
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setPageNo(1);
        orderCriteria.setSize(100);
        orderCriteria.setPushStatus(SkuOrderPushStatus.WAITING.value());
        orderCriteria.setStatus(Collections.singletonList(OrderStatus.PAID.getValue()));
        for (SkuOrder skuOrder : pushSystemDep.skuOrderReadService.findBy(orderCriteria.getPageNo(), orderCriteria.getSize(), orderCriteria).getResult().getData()) {
            //取一条已支付待推的子订单
            if (skuOrder != null) {
                log.info("{} push.skuOrder[{}] of shop[id -> {},name -> {}]", LogUtil.getClassMethodName(), skuOrder.getId(), skuOrder.getShopId(), skuOrder.getShopName());
                if (!pushOrder(skuOrder.getOrderId())) {
                    errorOrderIds.add(skuOrder.getOrderId());
                }
            } else {
                log.info("{} can not find skuOrder waiting for push", LogUtil.getClassMethodName());
                break;
            }
        }
        // 自动重推-1的订单
        orderCriteria.setPushStatus(-1);
        for (SkuOrder skuOrder : pushSystemDep.skuOrderReadService.findBy(orderCriteria.getPageNo(), orderCriteria.getSize(), orderCriteria).getResult().getData()) {
            //取一条已支付待推的子订单
            if (skuOrder != null) {
                log.info("{} push.skuOrder[{}] of shop[id -> {},name -> {}]", LogUtil.getClassMethodName(), skuOrder.getId(), skuOrder.getShopId(), skuOrder.getShopName());
                if (!pushOrder(skuOrder.getOrderId())) {
                    errorOrderIds.add(skuOrder.getOrderId());
                }
            } else {
                log.info("{} can not find skuOrder waiting for push", LogUtil.getClassMethodName());
                break;
            }
        }
        return errorOrderIds;
    }


    @Override
    public Either<Boolean> updatePushStatusByOrderId(Long shopOrderId, Integer targetPushStatus, Integer
            currentPushStatus) {
        try {
            pushSystemDep.orderWriteService.updateSkuOrderPushStatusByOrderId(shopOrderId, targetPushStatus, currentPushStatus);
            return Either.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update pushStatus of skuOrders by shopOrderId={}, targetPushStatus={}, currentPushStatus={}, cause:",
                    shopOrderId, targetPushStatus, currentPushStatus, e);
            return Either.error(Translate.exceptionOf("修改订单推送状态失败"));
        }
    }

    @Override
    public void pushV3OrderPayer(Long shopOrderId) {
        var shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
        if (shopOrder == null) {
            log.error("OrderPushServiceImplCover.pushV3OrderPayer error, shopOrderId={}, 订单查询结果为空", shopOrderId);
            return;
        }

        var config = thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(
                ThirdPartySystem.Y800_V3.Id(), shopOrder.getShopId()).getResult();
        if (config == null) {
            log.error("OrderPushServiceImplCover.pushV3OrderPayer error, shopOrderId={}, v3配置查询为空", shopOrderId);
            return;
        }

        String accessCode = config.getExtra().get(ShopExtra.Y800StorageAccessCode.getCode());
        if (org.apache.commons.lang3.StringUtils.isBlank(accessCode)) {
            log.error("OrderPushServiceImplCover.pushV3OrderPayer error, shopOrderId={}, accessCode为空", shopOrderId);
            return;
        }

        try (var api = y800StorageRemoteAPI) {
            api.setAppId(config.getThirdPartyCode());
            api.setSecret(config.getThirdPartyKey());

            var request = new OrderPayerChangeRequest();
            request.setAccessCode(accessCode);
            request.setThirdNo(shopOrder.getDeclaredId());

            var payerInfos = payerInfoReadService.findByOrderIds(Lists.newArrayList(shopOrder.getId())).getResult();
            if (CollectionUtils.isEmpty(payerInfos)) {
                log.error("OrderPushServiceImplCover.pushV3OrderPayer error, 支付人信息查询结果为空，shopOrderId={}", shopOrderId);
                return;
            }
            Key key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
            PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(key, payerInfos.get(0).info());
            request.setNewPayerName(info.getName());
            request.setNewPayerNumber(info.getNo());

            log.debug("OrderPushServiceImplCover.pushV3OrderPayer, shopOrderId={}, api.orderPayerChange request={}", shopOrderId, JSON.toJSONString(request));
            api.orderPayerChange(request);
        } catch (Exception ex) {
            log.error("OrderPushServiceImplCover.pushV3OrderPayer error, shopOrderId={}", shopOrderId, ex);
        }
    }
}