package moonstone.web.core.order.component;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Joiners;
import io.terminus.pay.model.PaymentParams;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserOpenIdUtil;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.Payment;
import moonstone.order.service.PaymentReadService;
import moonstone.web.core.order.api.PaymentParamsMaker;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * DATE: 16/9/9 上午10:32 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
public class DefaultPaymentParamsMaker implements PaymentParamsMaker {

    @Resource
    private PaymentReadService paymentReadService;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    @Override
    public PaymentParams makeParams(Payment payment) {
        PaymentParams params = new PaymentParams();
        params.setChannel(payment.getChannel());
        String orderIds = getSubject(payment.getId());
        params.setSubject(orderIds);
        params.setContent(orderIds);
        params.setExpiredAt(getExpiredTime(payment.getId()).orElseThrow(() -> new JsonResponseException(new Translate("获取订单超时时间错误").toString())));
        params.setFee(payment.getFee());
        params.setTradeNo(payment.getOutId()); //交易流水号
        params.setSystemNo(payment.getId().toString());
        params.setSellerNo(payment.getPayAccountNo());
        params.setOpenId(UserOpenIdUtil.getOpenId());
        return params;
    }

    protected Optional<Date> getExpiredTime(long paymentId) {
        val rOrderList = paymentReadService.findOrdersByPaymentId(paymentId);
        if (!rOrderList.isSuccess()) {
            log.error("{} fail to query order for paymentId:{}", LogUtil.getClassMethodName("getOrderList"), paymentId);
            return Optional.empty();
        }
        List<OrderBase> orderBases = new ArrayList<>(rOrderList.getResult());
        if (CollectionUtils.isEmpty(orderBases)) {
            log.error("{} empty orderList for paymentId:{}", LogUtil.getClassMethodName("getOrder"), paymentId);
            return Optional.empty();
        }

        var createdAt = orderBases.get(0).getCreatedAt();
        if (createdAt == null) {
            log.error("DefaultPaymentParamsMaker.getExpiredTime error, paymentId={} 对应的订单创建时间为空", paymentId);
            return Optional.empty();
        }

        return Optional.of(new DateTime(createdAt.getTime()).plusMinutes(expireMinutes).toDate());
    }

    protected String getSubject(Long paymentId) {
        Response<List<OrderPayment>> orderPayments = paymentReadService.findOrderIdsByPaymentId(paymentId);
        if (!orderPayments.isSuccess()) {
            log.error("{} findOrderPayments fail, paymentId={} cause={}", LogUtil.getClassMethodName(), paymentId, orderPayments.getError());
            throw new JsonResponseException("payment.params.make.fail");
        }
        List<Long> orderIdList = orderPayments.getResult().stream().map(OrderPayment::getOrderId).collect(Collectors.toList());
        return Joiners.COMMA.join(orderIdList);
    }
}
