package moonstone.web.core.order.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.EventSender;
import moonstone.event.PaymentDeclaredDelayNotify;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.ShopOrderIdentityErrorEnum;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.PaymentWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.component.api.Y800V3Api;
import moonstone.web.core.component.api.bo.y800v3.Y800CustomsClearanceRequest;
import moonstone.web.core.order.convert.Y800V3CustomsClearanceConvertor;
import moonstone.web.core.order.service.Y800V3CustomsClearancePushService;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class Y800V3CustomsClearancePushServiceImpl implements Y800V3CustomsClearancePushService {

    @Resource
    private OrderWriteService orderWriteService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private PaymentWriteService paymentWriteService;

    @Autowired
    private Y800V3CustomsClearanceConvertor y800V3CustomsClearanceConvertor;

    @Autowired
    private Y800V3CustomsClearancePushService self;

    @RemoteAPI
    private Y800V3Api y800V3Api;

    @Override
    public boolean isY800V3Order(List<SkuOrder> skuOrders) {
        if (CollectionUtils.isEmpty(skuOrders)) {
            return false;
        }

        return skuOrders.stream().anyMatch(skuOrder -> skuOrder.containPushSystem(ThirdPartySystem.Y800_V3.Id()));
    }

    @Override
    public boolean isY800V3Order(ShopOrder shopOrder) {
        if (shopOrder == null || shopOrder.getId() == null) {
            return false;
        }

        return isY800V3Order(skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult());
    }

    @Override
    public void pushV3CustomsClearanceInfo(Payment payment, List<ShopOrder> shopOrderList, List<SkuOrder> skuOrderList) {
        if (payment == null || CollectionUtils.isEmpty(shopOrderList) || CollectionUtils.isEmpty(skuOrderList)) {
            return;
        }

        //清关信息推送的前提条件为SkuOrder#pushStatus = 2 [已完成推送]；若不满足，则将清关信息推送进行延迟
        if (!skuOrderList.stream().allMatch(skuOrder -> SkuOrderPushStatus.FINISHED.value() == skuOrder.getPushStatus())) {
            EventSender.sendApplicationEvent(new PaymentDeclaredDelayNotify(payment.getId()));
            return;
        }

        //推送清关信息
        var success = push(payment, shopOrderList);

        //推送成功时，将Payment#pushStatus改完 4 [已完成]
        updatePaymentPushStatus(payment, success);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pushSingleOrder(Payment payment, ShopOrder shopOrder, Y800V3Api apiV3) {
        //更新订单的推送标志
        if (!markPushState(shopOrder)) {
            throw new RuntimeException(String.format("Y800V3CustomsClearancePushServiceImpl.markPushState fail, shopOrderId=%s",
                    shopOrder.getId()));
        }

        //推送
        if (!pushOrderCustomsClearance(payment, shopOrder, apiV3)) {
            throw new RuntimeException(String.format("Y800V3CustomsClearancePushServiceImpl.pushOrderCustomsClearance fail, shopOrderId=%s",
                    shopOrder.getId()));
        }
    }

    /**
     * 逐单推送
     *
     * @param payment
     * @param shopOrderList
     * @return
     */
    private boolean push(Payment payment, List<ShopOrder> shopOrderList) {
        boolean success = true;

        try (Y800V3Api apiV3 = y800V3Api) {
            for (ShopOrder shopOrder : shopOrderList) {
                self.pushSingleOrder(payment, shopOrder, apiV3);
            }
        } catch (Exception ex) {
            log.error("OrderDeclareServiceImpl.pushV3CustomsClearanceInfo error ", ex);
            success = false;
        }

        return success;
    }

    /**
     * 更新主订单的清关信息推送标记
     *
     * @param shopOrder
     * @return
     */
    private boolean markPushState(ShopOrder shopOrder) {
        if (shopOrder.getExtra() == null) {
            shopOrder.setExtra(new HashedMap<>());
        }
        shopOrder.getExtra().put(ShopOrderExtra.customsClearanceInfoPushState.name(), Boolean.TRUE.toString());
        shopOrder.getExtra().put(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode());

        var result = orderWriteService.updateOrderExtra(
                shopOrder.getId(), OrderLevel.SHOP, shopOrder.getExtra()).getResult();
        if (!Boolean.TRUE.equals(result)) {
            log.error("Y800V3CustomsClearancePushServiceImpl.markPushState 更新主订单的清关信息推送标记失败，shopOrderId={}", shopOrder.getId());
            return false;
        }

        return true;
    }

    /**
     * 清关信息推送
     *
     * @param payment
     * @param shopOrder
     * @param apiV3
     * @return
     */
    private boolean pushOrderCustomsClearance(Payment payment, ShopOrder shopOrder, Y800V3Api apiV3) {
        //构造入参
        Y800CustomsClearanceRequest request = y800V3CustomsClearanceConvertor.convert(payment, shopOrder);

        //调用
        var result = apiV3.deliveryQGInfo(request);

        if (result == null || !result.isSuccess()) {
            log.error("Y800V3CustomsClearancePushServiceImpl.pushOrderCustomsClearance 清关信息推送失败，paymentId={}, shopOrderId={}, request={}, result={}",
                    payment.getId(), shopOrder.getId(), JSON.toJSONString(request), JSON.toJSONString(result));
            return false;
        }

        return true;
    }

    private void updatePaymentPushStatus(Payment payment, boolean pushSuccess) {
        Payment updatePayment = new Payment();
        updatePayment.setId(payment.getId());
        updatePayment.setPushStatus(pushSuccess ? PaymentPushStatus.PUSH_SUCCESS.getValue() :
                PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY.getValue());
        paymentWriteService.update(updatePayment);

        //不成功时，稍后重试
        if (!pushSuccess) {
            EventSender.sendApplicationEvent(new PaymentDeclaredDelayNotify(payment.getId()));
        }
    }
}
