package moonstone.web.core.order;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.snapshot.TradeSnapshotService;
import moonstone.web.core.snapshot.dto.OrderItemSnapshotDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Mail: <EMAIL>
 * Data: 16/6/29
 * Author: yangzefeng
 */
@Controller
@Slf4j
public class CommonTradeSnapshots {

    private final TradeSnapshotService tradeSnapshotService;

    @Autowired
    public CommonTradeSnapshots(TradeSnapshotService tradeSnapshotService) {
        this.tradeSnapshotService = tradeSnapshotService;
    }

    @RequestMapping(value = "/api/order/{skuOrderId}/snapshot", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderItemSnapshotDetail> skuOrderSnapshot(@PathVariable("skuOrderId") Long id) {
        return tradeSnapshotService.findOrderItemSnapshot(id);
    }
}
