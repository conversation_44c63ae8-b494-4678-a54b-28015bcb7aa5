package moonstone.web.core.order.convert;

import com.google.common.collect.Lists;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.EncryptHelper;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.user.model.PayerInfo;
import moonstone.user.service.PayerInfoReadService;
import moonstone.web.core.component.api.bo.y800v3.Y800CustomsClearanceRequest;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.security.Key;

/**
 * api-v3 清关信息推送-模型转换
 */
@Component
public class Y800V3CustomsClearanceConvertor {

    @Autowired
    private ThirdPartyUserShopCache thirdPartyUserShopCache;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    private static final String PUSH_CUSTOMS_CLEARANCE_OPERATION_CREATE = "1";
    private static final String PUSH_CUSTOMS_CLEARANCE_OPERATION_UPDATE = "2";

    public Y800CustomsClearanceRequest convert(Payment payment, ShopOrder shopOrder) {
        if (payment == null || shopOrder == null) {
            return null;
        }

        Y800CustomsClearanceRequest request = new Y800CustomsClearanceRequest();

        request.setThirdNo(shopOrder.getDeclaredId());
        request.setDeclareSn(shopOrder.getDeclaredId());

        //第三方平台信息
        appendThirdPartyUserShopInfo(request, shopOrder.getShopId());

        //支付人信息
        appendPayInfo(request, shopOrder.getId());

        //操作：新增 or 更新
        appendOperation(request, payment, shopOrder);

        return request;
    }

    private void appendOperation(Y800CustomsClearanceRequest request, Payment payment, ShopOrder shopOrder) {
        //当前主订单是否成功推送过清关信息
        boolean isShopOrderOnceSuccess = false;
        if (!CollectionUtils.isEmpty(shopOrder.getExtra()) &&
                StringUtils.isNotBlank(shopOrder.getExtra().get(ShopOrderExtra.customsClearanceInfoPushState.name()))) {
            isShopOrderOnceSuccess = Boolean.getBoolean(shopOrder.getExtra().get(ShopOrderExtra.customsClearanceInfoPushState.name()));
        }

        if (PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY.getValue() == payment.getPushStatus() && isShopOrderOnceSuccess) {
            request.setOperation(PUSH_CUSTOMS_CLEARANCE_OPERATION_UPDATE);
        } else {
            request.setOperation(PUSH_CUSTOMS_CLEARANCE_OPERATION_CREATE);
        }
    }

    private void appendThirdPartyUserShopInfo(Y800CustomsClearanceRequest request, Long shopId) {
        var thirdPartyUserShop = thirdPartyUserShopCache.findBy(
                ThirdPartySystem.Y800_V3.Id(), shopId);
        if (thirdPartyUserShop.isEmpty()) {
            return;
        }

        request.setAccessCode(thirdPartyUserShop.get().getExtra().get(ShopExtra.Y800StorageAccessCode.getCode()));
        request.setWhCode(thirdPartyUserShop.get().getExtra().get(ShopExtra.WhCode.getCode()));
    }

    private void appendPayInfo(Y800CustomsClearanceRequest request, Long shopOrderId) {
        var payerInfo = payerInfoReadService.findByOrderIds(Lists.newArrayList(shopOrderId)).getResult();
        if (CollectionUtils.isEmpty(payerInfo)) {
            return;
        }

        Key key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
        PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(key, payerInfo.get(0).info());

        request.setPayerName(info.getName());
        request.setPayerNo(info.getNo());
    }
}
