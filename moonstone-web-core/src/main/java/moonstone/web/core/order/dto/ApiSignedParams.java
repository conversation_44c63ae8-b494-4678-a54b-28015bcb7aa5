package moonstone.web.core.order.dto;

import lombok.Data;
import moonstone.common.utils.MD5Util;

@Data
public class ApiSignedParams {
    String apiid;
    String apisecret;
    String version;
    String timestamp;
    String sign;
    Object data;
    public ApiSignedParams sign()
    {
        StringBuffer sb = new StringBuffer();
        sb.append("apiid=" + apiid);
        sb.append("&apisecret=" + apisecret);
        sb.append("&timestamp=" + timestamp);
        sign = MD5Util.MD5(sb.toString()).toUpperCase();
        return this;
    }
}
