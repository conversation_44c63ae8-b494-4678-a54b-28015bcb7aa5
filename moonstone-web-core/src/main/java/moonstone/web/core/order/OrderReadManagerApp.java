package moonstone.web.core.order;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EmptyUtils;
import moonstone.item.service.SkuReadService;
import moonstone.order.dto.SkuOrderNewVO;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderReadService;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.order.vo.OrderCriteriaAPPVO;
import moonstone.order.vo.OrderGroupAPPVO;
import moonstone.order.vo.ShopOrderAPPVO;
import moonstone.shop.service.ShopReadService;
import moonstone.user.model.User;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.user.service.StoreProxySubUserManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class OrderReadManagerApp {


    @RpcConsumer
    private OrderReadService orderReadService;

    @Autowired
    private ObjectMapper objectMapper;

    @RpcConsumer
    private UserReadService<User> userReadService;

    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private ShopReadService shopReadService;

    @RpcConsumer
    private SkuReadService skuReadService;

    @RpcConsumer
    PaymentReadService paymentReadService;

    @RpcConsumer
    StoreProxySubUserManager storeProxySubUserManager;

    @RpcConsumer
    StoreProxyReadService storeProxyReadService;

    @Autowired
    private StoreProxyManager storeProxyManager;


    public OrderGroupAPPVO pagingOrder(OrderCriteriaAPPVO criteria) {
        OrderGroupAPPVO orderGroupNewVO = new OrderGroupAPPVO();
        Response<Paging<ShopOrder>> pagingShopOrderS = shopOrderReadService.findByShopOrder(criteria.getPageNo(), criteria.getSize(), criteria);
        if (!pagingShopOrderS.isSuccess() || EmptyUtils.isEmpty(pagingShopOrderS.getResult())
                || EmptyUtils.isEmpty(pagingShopOrderS.getResult().getData())) {
            return null;
        }
        List<ShopOrderAPPVO> shopOrderNewVOS = new ArrayList<>();
        for (ShopOrder entity : pagingShopOrderS.getResult().getData()) {
            ShopOrderAPPVO shopOrderNewVO = new ShopOrderAPPVO();
            BeanUtils.copyProperties(entity, shopOrderNewVO);
            Response<List<SkuOrder>> skuSList = skuOrderReadService.findByShopOrderId(entity.getId());
            if (!skuSList.isSuccess() || EmptyUtils.isEmpty(skuSList.getResult())) {
                log.error("failed to find SkuOrder by orderIds({}), error code: {}", entity.getId(), skuSList.getError());
                throw new JsonResponseException(skuSList.getError());
            }
            List<SkuOrderNewVO> skuOrders = packSkuOrders(skuSList.getResult());
            shopOrderNewVO.setSkuOrders(skuOrders);
            shopOrderNewVO.setSumGoodFee(entity.getOriginFee().intValue());
            shopOrderNewVO.setSumPrice(entity.getFee().intValue());
            shopOrderNewVO.setSumQuantity(skuOrders.stream().mapToInt(SkuOrderNewVO::getQuantity).sum());
            shopOrderNewVO.setSumShipFee(entity.getShipFee());
            shopOrderNewVO.setSumTax(skuOrders.stream().mapToInt(ens -> {
                return ens.getTax().intValue();
            }).sum());
            shopOrderNewVOS.add(shopOrderNewVO);
        }
        orderGroupNewVO.setTotal(pagingShopOrderS.getResult().getTotal());
        orderGroupNewVO.setShopOrders(shopOrderNewVOS);
        return orderGroupNewVO;
    }

    /**
     * 组装skuOrders
     *
     * @param skuOrders
     * @return
     */
    private List<SkuOrderNewVO> packSkuOrders(List<SkuOrder> skuOrders) {
        List<SkuOrderNewVO> skuOrderNewVOS = new ArrayList<>();
        for (SkuOrder entity : skuOrders) {
            SkuOrderNewVO skuOrderNewVO = new SkuOrderNewVO();
            BeanUtils.copyProperties(entity, skuOrderNewVO);
            skuOrderNewVOS.add(skuOrderNewVO);
        }
        return skuOrderNewVOS;
    }

}
