package moonstone.web.core.order;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.item.dto.ImageInfo;
import moonstone.order.model.OrderComment;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderCommentReadService;
import moonstone.order.service.OrderCommentWriteService;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Mail: <EMAIL>
 * Data: 16/7/7
 * Author: yangzefeng
 */
@Component
@Slf4j
public class CommentLogic {

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private OrderCommentWriteService orderCommentWriteService;

    @RpcConsumer
    private OrderCommentReadService orderCommentReadService;


    /**
     * 创建评论
     *
     * @param skuOrderId sku订单id
     * @param context    评价内容
     * @return 创建的评价id
     */
    public Long createComment(Long skuOrderId, String context, List<ImageInfo> images,
                              Integer quality, Integer describe,
                              Integer service, Integer express, String extra) {
        Response<SkuOrder> skuOrderR = skuOrderReadService.findById(skuOrderId);
        if (!skuOrderR.isSuccess()) {
            throw new JsonResponseException(skuOrderR.getError());
        }
        SkuOrder skuOrder = skuOrderR.getResult();

        CommonUser user = UserUtil.getCurrentUser();
        //权限校验
        if (!Objects.equals(skuOrder.getBuyerId(), user.getId())
                &&!Objects.equals(skuOrder.getShopId(), user.getShopId())) {
            throw new JsonResponseException("comment.not.owner");
        }

        //先找一把之前的评价
        Response<List<OrderComment>> orderCommentsR =
                orderCommentReadService.findByItemIdAndSkuOrderId(skuOrder.getItemId(), skuOrderId);
        if (!orderCommentsR.isSuccess()) {
            log.error("fail to find orderComment by itemId={},skuOrderId={}, error code:{}",
                    skuOrder.getItemId(), skuOrderId, orderCommentsR.getError());
            throw new JsonResponseException(orderCommentsR.getError());
        }
        List<OrderComment> orderComments = orderCommentsR.getResult();
        //流是有序的,所以可以取最后一个
        List<OrderComment> notDeleteComments = orderComments.stream().filter(
                o-> !Objects.equals(o.getStatus(), OrderComment.Status.DELETE.getValue())).collect(Collectors.toList()
        );
        Long parentId;
        if (CollectionUtils.isEmpty(notDeleteComments)) {
            parentId = -1L;
        } else {
            parentId = notDeleteComments.get(notDeleteComments.size()-1).getId();
        }

        OrderComment orderComment = makeOrderComment(parentId, skuOrder, context, images,
                quality, describe, service, express, extra);
        Response<Long> commentR = orderCommentWriteService.create(orderComment);
        if (!commentR.isSuccess()) {
            throw new JsonResponseException(commentR.getError());
        }

        return orderComment.getId();
    }

    public List<Long> batchCreateComments(List<OrderComment> orderComments) {
        List<Long> skuOrderIds = extractSkuOrderIds(orderComments);
        Response<List<SkuOrder>> findSkuOrders = skuOrderReadService.findByIds(skuOrderIds);
        if (!findSkuOrders.isSuccess()) {
            log.error("fail to find sku orders by skuOrderIds:{},cause:{}",
                    skuOrderIds, findSkuOrders.getError());
            throw new JsonResponseException(findSkuOrders.getError());
        }
        List<SkuOrder> skuOrders = findSkuOrders.getResult();

        List<OrderComment> richOrderComments = makeRichOrderComments(orderComments, skuOrders);
        Response<List<Long>> createResp = orderCommentWriteService.batchCreate(richOrderComments);
        if (!createResp.isSuccess()) {
            log.error("fail to batch create orderComments:{},cause:{}",
                    richOrderComments, createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        return createResp.getResult();
    }

    private List<Long> extractSkuOrderIds(List<OrderComment> orderComments) {
        List<Long> skuOrderIds = Lists.newArrayListWithCapacity(orderComments.size());
        for (OrderComment orderComment : orderComments) {
            skuOrderIds.add(orderComment.getSkuOrderId());
        }
        return skuOrderIds;
    }

    private List<OrderComment> makeRichOrderComments(List<OrderComment> orderComments, List<SkuOrder> skuOrders) {
        Map<Long, SkuOrder> skuOrderByIdIndex = Maps.uniqueIndex(skuOrders, new Function<SkuOrder, Long>() {
            @Override
            public Long apply(SkuOrder skuOrder) {
                return skuOrder.getId();
            }
        });

        final Long buyerId = UserUtil.getUserId();

        List<OrderComment> richComments = Lists.newArrayListWithCapacity(orderComments.size());
        for (OrderComment orderComment : orderComments) {
            SkuOrder skuOrder = skuOrderByIdIndex.get(orderComment.getSkuOrderId());
            if (skuOrder == null) {
                log.error("sku order(id={}) not found when make rich comment",
                        orderComment.getSkuOrderId());
                throw new JsonResponseException("sku.order.not.found");
            }

            if (!com.google.common.base.Objects.equal(buyerId, skuOrder.getBuyerId())) {
                log.error("the skuOrder(id={}) not belong to buyer(id={})",
                        skuOrder.getId(), buyerId);
                throw new JsonResponseException("sku.order.not.belong.to.buyer");
            }

            OrderComment richOrderComment = makeOrderComment(-1L, skuOrder, orderComment.getContext(),
                    orderComment.getImages(), orderComment.getQuality(), orderComment.getDescribe(),
                    orderComment.getService(), orderComment.getExpress(), orderComment.getExtraJson());

            richComments.add(richOrderComment);
        }
        return richComments;
    }

    public OrderComment makeOrderComment(Long parentId, SkuOrder skuOrder, String context, List<ImageInfo> images,
                                         Integer quality, Integer describe,
                                         Integer service, Integer express, String extra) {
        OrderComment orderComment = new OrderComment();

        //如果是评价,parentId是-1, 如果是追评, parentId为上级评价id
        orderComment.setParentId(parentId);
        orderComment.setSkuOrderId(skuOrder.getId());
        orderComment.setUserId(skuOrder.getBuyerId());
        orderComment.setUserName(skuOrder.getBuyerName());
        orderComment.setItemId(skuOrder.getItemId());
        orderComment.setItemName(skuOrder.getItemName());
        orderComment.setSkuAttrs(skuOrder.getSkuAttrs());
        orderComment.setShopId(skuOrder.getShopId());
        orderComment.setShopName(skuOrder.getShopName());
        orderComment.setContext(context);
        orderComment.setImages(images);
        //这里1代表正常状态,-1表示逻辑删除状态
        orderComment.setStatus(OrderComment.Status.OK.getValue());

        //确定评价所属用户类型
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (Objects.equals(orderComment.getUserId(), commonUser.getId())) {
            orderComment.setBelongUserType(OrderComment.UserType.BUYER.getValue());
        } else if (Objects.equals(orderComment.getShopId(), commonUser.getShopId())) {
            orderComment.setBelongUserType(OrderComment.UserType.SELLER.getValue());
        } else {
            throw new JsonResponseException("comment.not.owner");
        }

        orderComment.setHasDisplay(Boolean.FALSE);
        orderComment.setQuality(quality);
        orderComment.setDescribe(describe);
        orderComment.setService(service);
        orderComment.setExpress(express);
        orderComment.setExtraJson(extra);
        return orderComment;
    }

    /**
     * 更新评价状态,一般是用来逻辑删除评价
     *
     * @param commentId 评价id
     * @param status    更新状态
     */
    public void updateStatus(Long commentId, Integer status) {
        orderCommentWriteService.updateStatus(commentId, status);
    }

    public void buyerUpdateStatus(Long commentId, Integer status) {
        Response<OrderComment> commentR = orderCommentReadService.findById(commentId);
        if (!commentR.isSuccess()) {
            throw new JsonResponseException(commentR.getError());
        }
        OrderComment orderComment = commentR.getResult();

        if (!Objects.equals(orderComment.getUserId(), UserUtil.getUserId())) {
            throw new JsonResponseException("order.comment.not.owner");
        }

        updateStatus(commentId, status);
    }

    public void sellerUpdateStatus(Long commentId, Integer status) {
        Response<OrderComment> commentR = orderCommentReadService.findById(commentId);
        if (!commentR.isSuccess()) {
            throw new JsonResponseException(commentR.getError());
        }
        OrderComment orderComment = commentR.getResult();

        CommonUser user = UserUtil.getCurrentUser();
        if (!Objects.equals(orderComment.getShopId(), user.getShopId())) {
            throw new JsonResponseException("order.comment.not.owner");
        }

        updateStatus(commentId, status);
    }

    public void adminUpdateStatus(Long commentId, Integer status) {
        updateStatus(commentId, status);
    }


    private void checkCommentAuth(OrderComment orderComment) {
        CommonUser user = UserUtil.getCurrentUser();
        if (!Objects.equals(user.getShopId(), orderComment.getShopId())
                && !Objects.equals(user.getId(), orderComment.getUserId())) {
            throw new JsonResponseException("comment.not.owner");
        }
    }

    /**
     * 查询某个评价下面的所有回复
     *
     * @param parentId   评价id
     * @param skuOrderId 子订单id
     * @return 所有回复
     */
    public List<OrderComment> getReplies(Long parentId, Long skuOrderId) {
        if (parentId == null && skuOrderId == null) {
            log.error("get comment replies fail, parentId and skuOrderId can both be null");
            throw new InvalidException("get.comment.reply.param.error");
        }

        if (parentId != null) {
            return findByPriorComment(parentId, Boolean.TRUE);
        } else {
            return findBySkuOrderId(skuOrderId, Boolean.TRUE);
        }
    }

    /**
     * 运营后台,商品详情页查看评价回复,不做权限校验,可以根据上级评论查所有回复,也可以根据子订单id查所有的回复
     *
     * @param parentId   上级评论id
     * @param skuOrderId 子订单id
     * @return 评论列表
     */
    public List<OrderComment> noAuthGetReplies(Long parentId, Long skuOrderId) {
        if (parentId == null && skuOrderId == null) {
            log.error("get comment replies fail, parentId and skuOrderId can both be null");
            throw new InvalidException("get.comment.reply.param.error");
        }

        if (parentId != null) {
            return findByPriorComment(parentId, Boolean.FALSE);
        } else {
            return findBySkuOrderId(skuOrderId, Boolean.FALSE);
        }
    }

    private List<OrderComment> findByPriorComment(Long parentId, Boolean needCheck) {
        Response<OrderComment> orderCommentR = orderCommentReadService.findById(parentId);
        if (!orderCommentR.isSuccess()) {
            throw new JsonResponseException(orderCommentR.getError());
        }
        OrderComment parentComment = orderCommentR.getResult();
        //权限校验
        if (needCheck) {
            checkCommentAuth(parentComment);
        }

        Response<List<OrderComment>> orderCommentsR =
                orderCommentReadService.findByItemIdAndSkuOrderId(parentComment.getItemId(), parentComment.getSkuOrderId());
        if (!orderCommentsR.isSuccess()) {
            throw new JsonResponseException(orderCommentsR.getError());
        }
        return orderCommentsR.getResult();
    }

    private List<OrderComment> findBySkuOrderId(Long skuOrderId, Boolean needCheck) {
        Response<SkuOrder> skuOrderR = skuOrderReadService.findById(skuOrderId);
        if (!skuOrderR.isSuccess()) {
            throw new JsonResponseException(skuOrderR.getError());
        }
        SkuOrder skuOrder = skuOrderR.getResult();
        //权限校验
        if (needCheck) {
            checkSkuOrderAuth(skuOrder);
        }

        Response<List<OrderComment>> orderCommentsR =
                orderCommentReadService.findByItemIdAndSkuOrderId(skuOrder.getItemId(), skuOrderId);
        if (!orderCommentsR.isSuccess()) {
            throw new JsonResponseException(orderCommentsR.getError());
        }
        return orderCommentsR.getResult();
    }

    private void checkSkuOrderAuth(SkuOrder skuOrder) {
        CommonUser user = UserUtil.getCurrentUser();
        if (!Objects.equals(user.getShopId(), skuOrder.getShopId())
                && !Objects.equals(user.getId(), skuOrder.getBuyerId())) {
            throw new JsonResponseException("comment.not.owner");
        }
    }
}
