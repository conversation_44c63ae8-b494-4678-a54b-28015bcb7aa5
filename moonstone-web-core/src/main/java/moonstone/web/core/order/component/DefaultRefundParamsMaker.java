package moonstone.web.core.order.component;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.pay.model.RefundParams;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.Payment;
import moonstone.order.model.Refund;
import moonstone.order.service.PaymentReadService;
import moonstone.web.core.order.api.RefundParamsMaker;

/**
 * DATE: 16/9/9 上午10:40 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
public class DefaultRefundParamsMaker implements RefundParamsMaker {

    @RpcConsumer
    private PaymentReadService paymentReadService;

    @Override
    public RefundParams makeParams(Refund refund) {

        RefundParams refundParams = new RefundParams();

        refundParams.setChannel(refund.getChannel());
        refundParams.setRefundNo(refund.getOutId());
        refundParams.setRefundAmount(refund.getFee());
        refundParams.setRefundReason(refund.getBuyerNote());
        refundParams.setSellerNo(refund.getRefundAccountNo());

        //获取支付单信息
        Response<Payment> paymentR = paymentReadService.findById(refund.getPaymentId());
        if (!paymentR.isSuccess()) {
            log.error("fail to find payment by id {}, error code:{}",
                    refund.getPaymentId(), paymentR.getError());
            throw new JsonResponseException(paymentR.getError());
        }
        Payment payment = paymentR.getResult();

        refundParams.setTradeNo(payment.getOutId()); //内部交易流水号
        refundParams.setPaymentCode(payment.getPaySerialNo()); //外部交易流水号
        refundParams.setTotalFee(payment.getFee());
        return refundParams;
    }
}
