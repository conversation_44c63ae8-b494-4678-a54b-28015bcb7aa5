package moonstone.web.core.shop.application;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.model.YunAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 对额外的云账户进行处理,因为目前只有一个这种第三方账户管理所以目前就这么处理吧
 */
@Slf4j
@Repository
public class ShopYunAccountManager {
    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 查找一个可用账户,如果有其他需求则后面添加
     *
     * @param shopId
     * @return
     */
    public Either<YunAccount> getOneByShopId(Long shopId) {
        try {
            return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("status").gte(0)), YunAccount.class));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} failed to get one account by shopId:{}", LogUtil.getClassMethodName(), shopId);
            return Either.error(ex);
        }
    }

    /**
     * 保存帐号
     *
     * @param yunAccount
     * @return
     */
    public Either<Boolean> saveYunAccount(YunAccount yunAccount) {
        try {
            mongoTemplate.save(yunAccount);
            return Either.ok(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} failed to save yunAccount:{} error:{}", LogUtil.getClassMethodName(), yunAccount, ex.getMessage());
            return Either.error(ex);
        }
    }

    /**
     * 查询
     *
     * @param query 查询条件
     * @return
     */
    public Either<List<YunAccount>> queryBy(Query query) {
        try {
            return Either.ok(mongoTemplate.find(query, YunAccount.class));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} failed to query:{}", LogUtil.getClassMethodName(), query);
            return Either.error(ex);
        }
    }

    /**
     * 获取一个帐号
     *
     * @param dealerId 纳税主体Id
     * @return
     */
    public Either<YunAccount> getOneByDealerId(String dealerId) {
        try {
            return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("dealerId").is(dealerId)).addCriteria(Criteria.where("status").gte(0)), YunAccount.class));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} failed to query by dealerId:{}", LogUtil.getClassMethodName(), dealerId);
            return Either.error(ex);
        }
    }
}