package moonstone.web.core;


import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@ServerEndpoint("/agreement")
@Component
@Slf4j
public class WsAgreementEndPoint {
    @Autowired
    Vertx vertx;

    @AllArgsConstructor
    @Getter
    enum Share {
        vertx(null, new ConcurrentHashMap<>(), new ConcurrentHashMap<>());
        Vertx instance;
        Map<String, String> entry;
        Map<String, List<Session>> sessionById;
    }

    public WsAgreementEndPoint() {
        if (Share.vertx.getInstance() != null) {
            this.vertx = Share.vertx.getInstance();
        }
    }

    @PostConstruct
    public void start() throws Exception {
        Share.vertx.instance = vertx;
        vertx.eventBus().consumer(":websocket:agree", buildFuture("agree"));
        vertx.eventBus().consumer(":websocket:reject", buildFuture("reject"));
    }

    private Handler<Message<Buffer>> buildFuture(String status) {
        return msg -> {
            String sid = msg.body().toString();
            if (sessionBySharedIdMap().containsKey(sid)) {
                sendStatus(sessionBySharedIdMap().get(sid), status);
            }
        };
    }

    private void sendStatus(List<Session> sessions, String msg) {
        for (Session session : sessions) {
            session.getAsyncRemote().sendText(new JsonObject().put("type", msg).toString());
        }
    }


    @OnOpen
    public void open(Session session) {

    }

    @OnClose
    public void close(Session session) {
        Map<String, List<Session>> sessionBySharedIdMap = sessionBySharedIdMap();
        Map<String, String> entry = sessionEntry();
        if (entry.isEmpty() || !entry.containsKey(session.getId())) {
            return;
        }
        sessionBySharedIdMap.get(entry.get(session.getId()))
                .remove(session);
        if (sessionBySharedIdMap.get(entry.get(session.getId())).isEmpty()) {
            sessionBySharedIdMap.remove(entry.get(session.getId()));
        }
        entry.remove(session.getId());
    }

    private Map<String, String> sessionEntry() {
        return Share.vertx.entry;
    }

    private Map<String, List<Session>> sessionBySharedIdMap() {
        return Share.vertx.sessionById;
    }

    @OnMessage
    public void message(Session session, String msg) {
        JsonObject param = new JsonObject(msg);
        if (!param.containsKey("act")) {
            session.getAsyncRemote().sendText(new JsonObject().put("success", false).put("error", "无法理解的操作: null").toString());
            return;
        }
        switch (param.getString("act")) {
            case "con": {
                String sid = param.getString("sid");
                sessionEntry().put(session.getId(), sid);
                sessionBySharedIdMap().computeIfAbsent(sid, key -> new LinkedList<>());
                sessionBySharedIdMap().get(sid).add(session);
                break;
            }
            case "agree":
            case "reject":
                String sid = sessionEntry().get(session.getId());
                if (sid == null) {
                    session.getAsyncRemote().sendText(new JsonObject().put("success", false).put("error", "sid未知").toString());
                    return;
                }
                if (sessionBySharedIdMap().containsKey(sid)) {
                    sendStatus(sessionBySharedIdMap().get(sid), param.getString("act"));
                } else {
                    vertx.eventBus().publish(":websocket:" + param.getString("act"), sid);
                }
                break;
            default:
                session.getAsyncRemote().sendText(new JsonObject().put("success", false).put("error", "无法理解的操作: " + param.getString("act")).toString());
                return;
        }
        session.getAsyncRemote().sendText(new JsonObject().put("success", true).toString());
    }
}
