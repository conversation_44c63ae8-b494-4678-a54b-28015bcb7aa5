package moonstone.web.core.events.item.listener;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.json.JsonArray;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.storage.service.StorageService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.api.RefreshItemStockByTimePeriod;
import moonstone.web.core.events.item.ReturnStockEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReturnStockListener extends AbstractVerticle {
    @Autowired
    SkuCacheHolder skuCacheHolder;
    @Autowired
    private RefundReadService refundReadService;
    @Autowired
    private RefundWriteService refundWriteService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private StorageService storageService;

    @Transactional(rollbackFor = RuntimeException.class)
    @EventListener
    @VertxEventBusListener(ReturnStockEvent.class)
    public void returnStock(ReturnStockEvent returnStockEvent) {
        try {
            Set<Long> itemSet = new HashSet<>();
            //根据退款单id列表查询绑定关系
            Response<List<OrderRefund>> rOrderRefunds = refundReadService.findOrderIdsByRefundId(returnStockEvent.getRefundId());
            if (!rOrderRefunds.isSuccess()) {
                log.error("fail to find orderRefunds by refundId={}, cause:{}", returnStockEvent.getRefundId(), rOrderRefunds.getError());
                throw new JsonResponseException("fail to find orderRefunds");
            }

            for (OrderRefund orderRefund : rOrderRefunds.getResult()) {
                if (orderRefund.getStatus() == OrderStatus.REFUND.getValue()) {
                    log.debug("{} order[{} at [{}] refundId=>{}] return stock done", LogUtil.getClassMethodName(), orderRefund.getOrderId(), orderRefund.getUpdatedAt(), orderRefund.getRefundId());
                    continue;
                }
                switch (orderRefund.getOrderLevel()) {
                    case SHOP:
                        Response<List<SkuOrder>> rSkuOrderList = skuOrderReadService.findByShopOrderId(orderRefund.getOrderId());
                        if (!rSkuOrderList.isSuccess()) {
                            log.error("fail to find skuOrderList by shopOrderId={}, cause:{}", orderRefund.getOrderId(), rSkuOrderList.getError());
                            throw new JsonResponseException("fail to find skuOrderList");
                        }

                        for (SkuOrder skuOrder : rSkuOrderList.getResult()) {
                            log.debug("{} return stock [{}] for orderId[orderId => {}, id => {}]", LogUtil.getClassMethodName(),
                                    skuOrder.getQuantity(), skuOrder.getOrderId(), skuOrder.getId());
                            Response<Boolean> deltaR = storageService.increaseBy(skuOrder.getSkuId(), null, null, skuOrder.getQuantity());
                            if (!deltaR.isSuccess()) {
                                log.error("fail to increase sku(id={})'s storage, delta={}, error code:{}",
                                        skuOrder.getSkuId(), skuOrder.getQuantity(), deltaR.getError());
                            }
                            itemSet.add(skuCacheHolder.findSkuById(skuOrder.getSkuId()).getItemId());
                        }
                        break;
                    case SKU:
                        Response<SkuOrder> rSkuOrder = skuOrderReadService.findById(orderRefund.getOrderId());
                        if (!rSkuOrder.isSuccess()) {
                            log.error("fail to find skuOrder by id={}, cause:{}", orderRefund.getOrderId(), rSkuOrder.getError());
                            throw new JsonResponseException("fail to find skuOrder");
                        }
                        SkuOrder skuOrder = rSkuOrder.getResult();

                        log.debug("{} return stock [{}] for orderId[orderId => {}, id => {}]", LogUtil.getClassMethodName(),
                                skuOrder.getQuantity(), skuOrder.getOrderId(), skuOrder.getId());
                        Response<Boolean> deltaR = storageService.increaseBy(skuOrder.getSkuId(), null, null, skuOrder.getQuantity());
                        if (!deltaR.isSuccess()) {
                            log.error("fail to increase sku(id={})'s storage, delta={}, error code:{}",
                                    skuOrder.getSkuId(), skuOrder.getQuantity(), deltaR.getError());
                        }
                        itemSet.add(skuCacheHolder.findSkuById(skuOrder.getSkuId()).getItemId());
                        break;
                    default:
                }
                JsonArray itemArray = new JsonArray();
                itemSet.forEach(itemArray::add);
                vertx.eventBus().request(RefreshItemStockByTimePeriod.ADDRESS, itemArray);
                refundWriteService.updateOrderRefundStatus(orderRefund.getRefundId(), OrderStatus.REFUND.getValue()).take();
            }
        } catch (Exception e) {
            log.error("fail to return stock by refundId={}, cause:{}", returnStockEvent.getRefundId(), e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
