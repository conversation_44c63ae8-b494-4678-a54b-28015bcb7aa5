package moonstone.web.core.events.profit.listener;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.component.siteMessage.SiteMessageComponent;
import moonstone.web.core.events.profit.WithdrawApplyAuthEvent;
import moonstone.web.core.events.profit.WithdrawApplyEvent;
import moonstone.web.core.events.profit.WithdrawSuccessEvent;
import moonstone.web.core.msg.enu.SiteMessageLevel;
import moonstone.web.core.msg.enu.SiteMessageTemplate;
import moonstone.web.core.msg.event.SiteMsgNotify;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class WithdrawSiteMessageNotify {

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Autowired
    private SubStoreCache subStoreCache;

    @Autowired
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private SiteMessageComponent siteMessageComponent;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private GuiderCache guiderCache;

    /**
     * 发起提现申请
     *
     * @param withdrawApplyEvent
     */
    @EventListener(WithdrawApplyEvent.class)
    public void notifyOnApply(WithdrawApplyEvent withdrawApplyEvent) {
        //提现申请记录
        var withDrawProfitApply = withDrawProfitApplyReadService.findById(
                withdrawApplyEvent.withDrawProfitApplyId()).getResult();
        if (withDrawProfitApply == null) {
            log.error("数据不存在，withDrawProfitApplyId={}", withdrawApplyEvent.withDrawProfitApplyId());
            return;
        }

        //待通知的用户id集合
        var receiverUserIdSet = findReceiverList(withDrawProfitApply);

        //服务商或门店的名称
        String applicantName = findApplicantName(withDrawProfitApply);

        //提现金额
        BigDecimal withdrawAmount = new BigDecimal(withDrawProfitApply.getFee()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        for (Long receiverUserId : receiverUserIdSet) {
            EventSender.sendApplicationEvent(new SiteMsgNotify(withDrawProfitApply.getSourceId(),
                    receiverUserId,
                    SiteMessageTemplate.WITHDRAW_APPLY_INITIATE.getName(),
                    null,
                    Map.of("name", applicantName,
                            "profit", withdrawAmount.toPlainString()),
                    Collections.emptyList(),
                    new Date(),
                    SiteMessageLevel.WITHDRAW.getCode()));
        }
    }

    private Set<Long> findReceiverList(WithDrawProfitApply withDrawProfitApply) {
        Set<Long> receiverUserIdSet = new HashSet<>();

        var shop = shopReadService.findById(withDrawProfitApply.getSourceId()).getResult();

        //超管
        receiverUserIdSet.add(shop.getUserId());

        //管理员
        receiverUserIdSet.addAll(siteMessageComponent.findUserIdsByRoleName(shop.getId(),
                SiteMessageComponent.ROLE_NAME_MANAGER));

        //拥有“财务”、“销售运营”角色的的用户id列表
        receiverUserIdSet.addAll(siteMessageComponent.findUserIdsByRoleNames(withDrawProfitApply.getSourceId(),
                Lists.newArrayList(SiteMessageComponent.ROLE_NAME_FINANCIAL, SiteMessageComponent.ROLE_NAME_SALES_OPERATION)));

        return receiverUserIdSet;
    }

    /**
     * 提现申请审核通过
     *
     * @param withdrawSuccessEvent
     */
    @EventListener(WithdrawSuccessEvent.class)
    public void notifyOnSuccess(WithdrawSuccessEvent withdrawSuccessEvent) {
        var event = withdrawSuccessEvent.toEvent();
        EventSender.sendApplicationEvent(new SiteMsgNotify(event.shopId(),
                event.userId(),
                SiteMessageTemplate.WITHDRAW_APPLY_SUCCESS.getName(),
                null,
                Map.of("date", DateTimeFormatter.ofPattern("yyyy年MM月dd日HH:mm:ss")
                                .format(LocalDateTime.ofInstant(event.time().toInstant(), ZoneId.systemDefault())),
                        "profit", event.profit().setScale(2, RoundingMode.HALF_UP).toString()),
                Collections.emptyList(),
                new Date(), 0));
    }

    /**
     * 提现申请审核不通过
     *
     * @param event
     */
    @EventListener(WithdrawApplyAuthEvent.class)
    public void notifyOnReject(WithdrawApplyAuthEvent event) {
        if (event.auth()) {
            return;
        }

        var withDrawProfitApply = withDrawProfitApplyReadService.findById(
                event.id()).getResult();
        if (withDrawProfitApply == null) {
            log.error("数据不存在，withDrawProfitApplyId={}", event.id());
            return;
        }

        BigDecimal withdrawAmount = new BigDecimal(withDrawProfitApply.getFee()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        EventSender.sendApplicationEvent(new SiteMsgNotify(event.shopId(),
                event.userId(),
                SiteMessageTemplate.WITHDRAW_APPLY_REJECT.getName(),
                null,
                Map.of("date", DateTimeFormatter.ofPattern("yyyy年MM月dd日HH:mm:ss")
                                .format(LocalDateTime.ofInstant(withDrawProfitApply.getCreatedAt().toInstant(), ZoneId.systemDefault())),
                        "profit", withdrawAmount.toString()),
                Collections.emptyList(),
                new Date(), 0));
    }

    /**
     * 提现申请人的名称（取门店名或服务商名）
     *
     * @param withDrawProfitApply
     * @return
     */
    private String findApplicantName(WithDrawProfitApply withDrawProfitApply) {
        var subStore = subStoreCache.findByShopIdAndUserId(withDrawProfitApply.getSourceId(), withDrawProfitApply.getUserId());
        if (subStore.isPresent()) {
            return subStore.get().getName();
        }

        var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(
                withDrawProfitApply.getUserId(), withDrawProfitApply.getSourceId());
        if (serviceProvider != null) {
            return serviceProvider.getName();
        }

        var guider = guiderCache.findByShopIdAndUserId(withDrawProfitApply.getSourceId(), withDrawProfitApply.getUserId());
        if (guider.isPresent()) {
            return guider.get().getStoreGuiderNickname();
        }

        return "未知申请人名称";
    }
}
