package moonstone.web.core.events.trade.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.event.OrderIntegralEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.user.model.StoreIntegral;
import moonstone.user.service.StoreIntegralReadService;
import moonstone.user.service.StoreIntegralWriteService;
import moonstone.web.core.component.order.PaymentLogic;
import moonstone.web.core.config.FunctionSwitch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/26 13:56
 * 积分支付扣款--同时通知订单计费
 */
@SuppressWarnings("ALL")
@Slf4j
public class OrderIntegralEventListener {

    @RpcConsumer
    private PaymentReadService paymentReadService;

    @RpcConsumer
    private PaymentWriteService paymentWriteService;

    @Autowired
    private OrderStatusUpdater orderStatusUpdater;

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;

    @RpcConsumer
    private OrderWriteService orderWriteService;

    @Autowired
    private FunctionSwitch functionSwitch;

    @RpcConsumer
    IntegralUseRecordWriteService integralUseRecordWriteService;

    @RpcConsumer
    StoreIntegralWriteService storeIntegralWriteService;

    @RpcConsumer
    StoreIntegralReadService storeIntegralReadService;

    @Autowired
    private PaymentLogic paymentLogic;

    /**
     * orderId --parana_payment  的id
     * userId--用户id
     * integralFee--计费积分
     */

    @EventListener(OrderIntegralEvent.class)
    public void onPayment(OrderIntegralEvent orderPaymentEvent) {

        Long paymentId = orderPaymentEvent.orderId();
        Response<Payment> rPayment = paymentReadService.findById(paymentId);
        if (!rPayment.isSuccess()) {
            log.error("failed to find Payment(id={}), error code:{}", paymentId, rPayment.getError());
            return;
        }
        final Payment payment = rPayment.getResult();
        if (Objects.equal(payment.getStatus(), OrderStatus.PAID.getValue())) { //已经支付成功事件, 直接返回吧
            log.error("failed to find Payment(id={}), error code:{}", paymentId, rPayment.getError());
            return;
        }
        //进行积分账户扣除积分
        Either<Boolean> booleanResult = updateIntegralAcconut(orderPaymentEvent);


        if (!booleanResult.take()) {
            log.info("[failed to integral for payment] IntegralFe:{} OrderId:{} UserId:{}",
                    orderPaymentEvent.integralFee(), orderPaymentEvent.orderId(), orderPaymentEvent.userId());
            return;
        }
        //自己调方法通知自己
        updateIntegralAcconutPayment(orderPaymentEvent);


        Response<List<OrderPayment>> rOrderPayments = paymentReadService.findOrderIdsByPaymentId(paymentId);
        if (!rOrderPayments.isSuccess()) {
            log.error("failed to find orderIds for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
            return;
        }
        List<OrderPayment> orderPayments = rOrderPayments.getResult();

        payment.setPushStatus(PaymentPushStatus.NO_NEED_PUSH.getValue());
        for (OrderPayment orderPayment : orderPayments) {
            if (orderPayment.getOrderType() == 1) {
                // 店铺级别
                ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId()).getResult();
                List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
                for (SkuOrder skuOrder : skuOrderList) {
                    if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) { // 跨境商品，需要海关推送
                        payment.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                        orderWriteService.updateOrderExtra(skuOrder.getId(), OrderLevel.SKU, skuOrder.getExtra());
                    }
                }
            } else if (orderPayment.getOrderType() == 2) {
                // sku级别
                SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId()).getResult();
                if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) { // 跨境商品，需要海关推送
                    payment.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                    orderWriteService.updateOrderExtra(skuOrder.getId(), OrderLevel.SKU, skuOrder.getExtra());
                }
            }
        }
        paymentWriteService.update(payment);

        if (CollectionUtils.isEmpty(orderPayments)) {
            return;
        }
        orderStatusUpdater.update(orderPayments, OrderEvent.PAY.toOrderOperation());


    }

    private Either<Boolean> updateIntegralAcconutPayment(OrderIntegralEvent orderPaymentEvent) {
        Payment payment = new Payment();
        payment.setPaidAt(new Date());
        payment.setOutId(orderPaymentEvent.outId());
        payment.setPaySerialNo(orderPaymentEvent.outId());
        payment.setPayResponse("");
        log.info("[Payments](callBack) construct payment:{}", JSON.toJSONString(payment));
        Boolean b = paymentLogic.postPay(payment);
        return Either.ok(b);
    }

    private Either<Boolean> updateIntegralAcconut(OrderIntegralEvent orderPaymentEvent) {

        Map<String, Long> map = getLonginShopAndTradeNum(orderPaymentEvent.orderIds());


        Either<Optional<StoreIntegral>> availableGrade = storeIntegralReadService.findAvailableGradeByUserIdAndShopId(orderPaymentEvent.userId(), map.get("shopId"));
        if (!availableGrade.isSuccess()) {
            log.error("[IntegralGoods-useGrade] userId:{} ", orderPaymentEvent.userId());
            throw new JsonResponseException(new Translate("查找用户可用积分失败").toString());
        }
        if (!availableGrade.take().isPresent()) {
            log.error("[IntegralGoods-useGrade] userId:{} ", orderPaymentEvent.userId());
            return Either.ok(false);
        }

        if (availableGrade.take().get().getAvailableGrade() < orderPaymentEvent.integralFee()) {
            log.error("[IntegralGoods-useGrade] userId:{} ", orderPaymentEvent.userId());
            return Either.ok(false);
        }

        Either<Boolean> booleanResult = Either.ok(false);
        //
        IntegralUseRecord integralUseRecord = new IntegralUseRecord();
        integralUseRecord.setCreateAt(System.currentTimeMillis());
        integralUseRecord.setStatus(IntegralStatus.USEED.value());
        integralUseRecord.setUseIntegral(orderPaymentEvent.integralFee());
        integralUseRecord.setUserId(orderPaymentEvent.userId());
        integralUseRecord.setTradeId(orderPaymentEvent.orderId());
        booleanResult = integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);

        if (!booleanResult.isSuccess()) {
            log.error("[IntegralData-genAccept-update] userId:{} faceValue:{} getOrderId:{}",
                    orderPaymentEvent.userId(), orderPaymentEvent.integralFee(), orderPaymentEvent.orderId());
            return Either.ok(false);
        }


        availableGrade.take().get().setAvailableGrade(availableGrade.take().get().getAvailableGrade() - orderPaymentEvent.integralFee());
        availableGrade.take().get().setUseGrade(availableGrade.take().get().getUseGrade() + orderPaymentEvent.integralFee());
        booleanResult = storeIntegralWriteService.updateStoreIntegral(availableGrade.take().get());
        return booleanResult;
    }


    private Map<String, Long> getLonginShopAndTradeNum(List<Long> orderIds) {
        Long shopId = null;
        Map<String, Long> map = new HashMap<>();

        Response<List<ShopOrder>> shopOrder = shopOrderReadService.findByIds(orderIds);
        if (!shopOrder.isSuccess()) {
            log.error("[getLonginShopAndTradeNum] orderIds:{} ", orderIds);
            throw new JsonResponseException(new Translate("查找shopId失败").toString());
        }
        for (ShopOrder shopOrders : shopOrder.getResult()) {
            map.put("shopId", shopOrders.getShopId());
        }
        Response<List<SkuOrder>> skuOrder = skuOrderReadService.findByShopOrderIds(orderIds);
        if (!skuOrder.isSuccess()) {
            log.error("[getLonginShopAndTradeNum] orderIds:{} ", orderIds);
            throw new JsonResponseException(new Translate("查找skuOrder失败").toString());
        }

        for (SkuOrder skuOrders : skuOrder.getResult()) {
            if (map.containsKey("quantity")) {
                map.put("quantity", map.get("quantity") + skuOrders.getQuantity());
            } else {
                map.put("quantity", Long.valueOf(skuOrders.getQuantity()));
            }
        }
        return map;
    }


}
