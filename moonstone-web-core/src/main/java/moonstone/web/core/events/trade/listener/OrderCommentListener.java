package moonstone.web.core.events.trade.listener;

import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.event.OrderCommentEvent;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.context.event.EventListener;

import java.util.List;

/**
 * Author:cp
 * Created on 9/13/16.
 */
@Slf4j
public class OrderCommentListener {

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private OrderWriteService orderWriteService;

    @EventListener(OrderCommentEvent.class)
    public void onCommented(OrderCommentEvent orderCommentEvent) {
        final List<Long> skuOrderIds = orderCommentEvent.getSkuOrderIds();
        final Long skuOrderId = skuOrderIds.get(0);

        SkuOrder skuOrder = findSkuOrder(skuOrderId);
        final Long shopOrderId = skuOrder.getOrderId();

        List<SkuOrder> skuOrders = findSkuOrders(shopOrderId);
        if (!isAllCommented(skuOrders)) {
            return;
        }

        //所有子订单已评价,则标记总单已评价
        Response<Boolean> updateResp = orderWriteService.markShopOrderCommented(shopOrderId);
        if (!updateResp.isSuccess()) {
            log.error("fail to mark shop order(id={}) commented,cause:{}",
                    shopOrderId, updateResp.getError());
        }

    }

    private List<SkuOrder> findSkuOrders(Long shopOrderId) {
        Response<List<SkuOrder>> findSkuOrders = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!findSkuOrders.isSuccess()) {
            log.error("fail to find sku orders by shopOrderId={},cause:{}",
                    shopOrderId, findSkuOrders.getError());
            throw new JsonResponseException(findSkuOrders.getError());
        }
        return findSkuOrders.getResult();
    }

    private SkuOrder findSkuOrder(Long skuOrderId) {
        Response<SkuOrder> findSkuOrder = skuOrderReadService.findById(skuOrderId);
        if (!findSkuOrder.isSuccess()) {
            log.error("fail to find sku order by id={},cause:{}",
                    skuOrderId, findSkuOrder.getError());
            throw new JsonResponseException(findSkuOrder.getError());
        }
        return findSkuOrder.getResult();
    }

    private boolean isAllCommented(List<SkuOrder> skuOrders) {
        for (SkuOrder skuOrder : skuOrders) {
            if (!Objects.equal(skuOrder.getCommented(), 2)) {
                return false;
            }
        }
        return true;
    }

}
