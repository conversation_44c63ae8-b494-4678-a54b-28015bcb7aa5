package moonstone.web.core.events.shop;

import lombok.Getter;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/10.
 */
public class ShopUpdateOpenWeShopEvent implements Serializable {
    private static final long serialVersionUID = -5077645784561770491L;

    @Getter
    private final Long shopId;

    @Getter
    private final Boolean openWeShop;

    public ShopUpdateOpenWeShopEvent(Long shopId, Boolean openWeShop){
        this.shopId = shopId;
        this.openWeShop = openWeShop;
    }
}
