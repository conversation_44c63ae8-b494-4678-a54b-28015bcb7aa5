package moonstone.web.core.events.item.listener;

import lombok.AllArgsConstructor;
import moonstone.cache.WeShopItemCacher;
import moonstone.web.core.events.weShopItem.WeShopItemUpdatedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WeShopItemDetailRefreshListener {
    WeShopItemCacher weShopItemCacher;

    @EventListener(WeShopItemUpdatedEvent.class)
    public void updateWeShopItemCache(WeShopItemUpdatedEvent weShopItemUpdatedEvent) {
        weShopItemCacher.invalidateWeShopItemById(weShopItemUpdatedEvent.getWeShopItemId());
    }
}
