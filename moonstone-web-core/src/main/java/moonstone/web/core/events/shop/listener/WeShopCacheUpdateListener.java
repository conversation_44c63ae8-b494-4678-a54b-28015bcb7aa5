package moonstone.web.core.events.shop.listener;

import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.shop.WeShopUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class WeShopCacheUpdateListener extends AbstractVerticle {
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;

    @VertxEventBusListener(WeShopUpdateEvent.class)
    public void updateWeShopCache(WeShopUpdateEvent weShopUpdateEvent) {
        weShopCacheHolder.invalidateWeShop(weShopUpdateEvent.getWeShopId());
    }
}
