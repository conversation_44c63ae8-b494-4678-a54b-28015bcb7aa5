package moonstone.web.core.events.trade.listener;

import com.hazelcast.core.HazelcastInstance;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.TaskWithLock;
import moonstone.event.PaymentPaidEvent;
import moonstone.order.api.GatherOrderMaker;
import moonstone.order.api.OrderNeedGatherJudge;
import moonstone.order.dto.RichGatherOrder;
import moonstone.order.model.OrderBase;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.GatherOrderWriteService;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.model.User;
import moonstone.weShop.model.WeShop;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.Lock;
import java.util.function.Consumer;
import java.util.function.Function;

@Slf4j
@Component
public class AutoGatherListener {
    @Autowired
    private GatherOrderMaker gatherOrderMaker;
    @Autowired
    private GatherOrderWriteService gatherOrderWriteService;

    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private UserCacheHolder userCacheHolder;
    @Autowired
    private OrderNeedGatherJudge orderNeedGatherJudge;

    @Resource
    private RedissonClient redissonClient;

    private final Function<Long, String> lockForGather = orderId -> String.format("AUTO-GATHER-LOCK-%s", orderId);

    @EventListener(PaymentPaidEvent.class)
    public void autoGatherOrder(PaymentPaidEvent paymentPaidEvent) {
        try {
            for (OrderBase orderBase : paymentReadService.findOrdersByPaymentId(paymentPaidEvent.getPaymentId()).getResult()) {
                if (Objects.isNull(orderBase))
                    continue;
                if (!(orderBase instanceof ShopOrder))
                    continue;
                ShopOrder shopOrder = (ShopOrder) orderBase;
                if (!Objects.equals(OrderOutFrom.WE_SHOP.Code(), shopOrder.getOutFrom()))
                    continue;
                if (skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult().stream().noneMatch(orderNeedGatherJudge::needGather)) {
                    continue;
                }
                if (Objects.nonNull(shopOrder.getGatherOrderId()))
                    continue;
                Optional<WeShop> weShopOpt = Optional.ofNullable(shopOrder.getOutShopId()).map(Long::parseLong).flatMap(weShopCacheHolder::findByWeShopId);
                if (!weShopOpt.isPresent()) return;
                Optional<User> userOpt = weShopOpt.map(WeShop::getUserId).flatMap(userCacheHolder::findByUserId);
                if (!userOpt.isPresent()) return;
                List<RichGatherOrder> richGatherOrderList = gatherOrderMaker.full(userOpt.get(), Collections.singletonList(shopOrder)).take();
                Lock gatherLock = redissonClient.getLock(lockForGather.apply(shopOrder.getId()));
                TaskWithLock.processWithLock(gatherLock, lockForGather.apply(shopOrder.getId()), richGatherOrderList, shopOrder.getId()
                        , (Consumer<? super List<RichGatherOrder>>) orderList -> gatherOrderWriteService.create(orderList));
            }
        } catch (Exception ex) {
            log.error("{} fail to auto-gather paymentId[{}]", LogUtil.getClassMethodName(), paymentPaidEvent.getPaymentId(), ex);
        }
    }
}
