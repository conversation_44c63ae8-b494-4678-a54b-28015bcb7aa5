package moonstone.web.core.events.promotion.listener;

import com.google.common.base.Objects;
import moonstone.cache.PromotionCacher;
import moonstone.common.utils.EventSender;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.model.Promotion;
import moonstone.web.core.component.publisher.CacheInvalidateContext;
import moonstone.web.core.component.publisher.CacheInvalidatePublisher;
import moonstone.web.core.component.publisher.CacheInvalidateType;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.events.promotion.PromotionPublishEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Optional;

/**
 * Author:cp
 * Created on 8/1/16.
 */
@Component
public class PromotionPublishListener {

    @Autowired
    private CacheInvalidatePublisher cacheInvalidatePublisher;
    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private PromotionCacher promotionCacher;

    /**
     * 营销活动发布后，若其为进行中，则失效该营销活动相关的缓存数据
     *
     * @param event
     */
    @EventListener(PromotionPublishEvent.class)
    @VertxEventBusListener(PromotionPublishEvent.class)
    public void onPublish(PromotionPublishEvent event) {
        final Long promotionId = event.getPromotionId();
        final Integer targetStatus = event.getTargetStatus();

        if (Objects.equal(targetStatus, PromotionStatus.ONGOING.getValue())) {
            CacheInvalidateContext context = new CacheInvalidateContext(CacheInvalidateType.PROMOTION.toString(),
                    String.valueOf(promotionId));
            cacheInvalidatePublisher.invalidate(context);
        }
    }

    /**
     * 营销活动发布后，若其为进行中，刷新该商家所有上架商品的相关数据（商品缓存、商品目录缓存、商品es、商品库存缓存）
     *
     * @param event
     */
    @EventListener(PromotionPublishEvent.class)
    @VertxEventBusListener(PromotionPublishEvent.class)
    public void onPublishPreviewItem(PromotionPublishEvent event) {
        final Long promotionId = event.getPromotionId();
        final Integer targetStatus = event.getTargetStatus();

        if (!Objects.equal(targetStatus, PromotionStatus.ONGOING.getValue())) {
            return;
        }

        Promotion promotion = promotionCacher.findByPromotionId(promotionId);

        // 操作比较脏,尽可能不要出现这种行为,因为这个ItemUpdate 事件会影响到下单测试库存
        Optional.ofNullable(itemReadService.findByShopIdsAndStatus(Collections.singletonList(promotion.getShopId()), 1).getResult())
                .orElse(new ArrayList<>())
                .stream()
                .map(Item::getId)
                .map(ItemUpdateEvent::new)
                .forEach(EventSender::publish);
    }
}
