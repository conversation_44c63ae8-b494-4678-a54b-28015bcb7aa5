package moonstone.web.core.events.settle.listener;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.service.SettleAbnormalTrackWriteService;
import moonstone.web.core.events.settle.SettleAbnormalEvent;
import org.springframework.context.event.EventListener;

/**
 * DATE: 16/8/8 下午3:49 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
public class SettleAbnormalListener {

    @RpcConsumer
    private SettleAbnormalTrackWriteService settleAbnormalTrackWriteService;


    @EventListener(SettleAbnormalEvent.class)
    public void onAbnormal(SettleAbnormalEvent settleAbnormalEvent){

        Response<Long> trackRes = settleAbnormalTrackWriteService.createSettleAbnormalTrack(settleAbnormalEvent.getAbnormalInfo(), settleAbnormalEvent.getDesc(), settleAbnormalEvent.getType().value());
        if(!trackRes.isSuccess()){
            log.error("create settle abnormal track fail, params={},  error:{}", settleAbnormalEvent,trackRes.getError());
        }
    }
}
