/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.spu;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-03
 */
@Data
@NoArgsConstructor
public class SpuEvent implements Serializable {
    private static final long serialVersionUID = -2497411450927503831L;

    protected Long spuId;

    protected SpuEvent(Long spuId) {
        this.spuId = spuId;
    }
}
