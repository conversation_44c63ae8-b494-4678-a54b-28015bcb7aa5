package moonstone.web.core.events.trade.listener;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ParanaConstants;
import moonstone.event.OrderAutoRefundEvent;
import moonstone.order.enu.RefundExtraIndexEnum;
import moonstone.order.enu.RefundReasonType;
import moonstone.order.enu.Y800V3DeliveryConfirmCallbackTypeEnum;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRelation;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.refund.application.RefundAgreeActionChainApplication;
import moonstone.web.core.refund.application.RefundForBuyerApplication;
import moonstone.web.core.refund.application.RefundForSellerApplication;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * 只适用于v3接口的订单
 */
@Slf4j
@Component
public class OrderAutoRefundListener {

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private RefundForSellerApplication refundForSellerApplication;

    @Resource
    private RefundForBuyerApplication refundForBuyerApplication;

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private RefundWriteService refundWriteService;

    @Resource
    private RefundAgreeActionChainApplication refundAgreeActionChainApplication;

    /**
     * 让订单自动申请退款、同意、完成退款
     *
     * @param event
     */
    @Async
    @EventListener(OrderAutoRefundEvent.class)
    public void onOrderAutoRefund(OrderAutoRefundEvent event) {
        log.debug("OrderAutoRefundListener.onOrderAutoRefund, receive event={}", JSON.toJSONString(event));

        // 查询订单
        var shopOrder = shopOrderReadService.findById(event.getShopOrderId()).getResult();
        if (shopOrder == null) {
            log.error("OrderAutoRefundListener.onOrderAutoRefund, shopOrderId={}, 订单数据查询结果为空", event.getShopOrderId());
            return;
        }

        // 操作人
        var admin = userReadService.findById(ParanaConstants.SUPER_ADMIN_USER_ID).getResult();
        if (admin == null) {
            log.error("OrderAutoRefundListener.onOrderAutoRefund, 超级管理员数据不存在");
            return;
        }

        // 走完整的退款流程
        processRefund(shopOrder, event.getOrderPushErrorType(), admin);
    }

    private void processRefund(ShopOrder shopOrder, String orderPushErrorType, User admin) {
        // 申请退款
        Long refundId = applyRefund(shopOrder, orderPushErrorType, admin);
        if (refundId == null) {
            log.error("OrderAutoRefundListener.onOrderAutoRefund, shopOrderId={}, 订单自动申请退款失败", shopOrder.getId());
            return;
        }

        // 同意退款
        agree(refundId);

        // 执行退款
        refundForSellerApplication.refund(refundId, admin);
    }

    private void agree(Long refundId) {
        refundWriteService.updateStatusForCompulsoryAgree(refundId);
        try {
            refundAgreeActionChainApplication.cancelOrderWhenAgreeRefund(refundReadService.findById(refundId).getResult());
        } catch (Exception ex) {
            log.error("refundAgreeActionChainApplication.cancelOrderWhenAgreeRefund error, refundId={}", refundId, ex);
        }
    }

    /**
     * 发起退款申请
     *
     * @param shopOrder
     * @param orderPushErrorType
     * @param admin
     * @return
     */
    private Long applyRefund(ShopOrder shopOrder, String orderPushErrorType, BaseUser admin) {
        // 申请
        var orderInfo = new OrderRelation() {
        };
        orderInfo.setOrderId(shopOrder.getId());
        orderInfo.setOrderLevel(OrderLevel.SHOP);

        var applyResult = refundForBuyerApplication.apply(admin,
                List.of(orderInfo),
                Y800V3DeliveryConfirmCallbackTypeEnum.parseErrorMessage(orderPushErrorType),
                Collections.emptyList(),
                Refund.RefundType.ON_SALE_REFUND,
                RefundReasonType.OTHER,
                shopOrder.getFee());
        if (!applyResult.isSuccess() || applyResult.getResult() == null) {
            log.error("OrderAutoRefundListener.applyRefund error, shopOrderId={}, 申请退款失败, error={}",
                    shopOrder.getId(), applyResult.getErrorMsg());
            return null;
        }
        Long refundId = applyResult.getResult();

        // 打个标
        addTag(refundId, orderPushErrorType);

        return refundId;
    }

    private void addTag(Long refundId, String orderPushErrorType) {
        var refund = refundReadService.findById(refundId).getResult();
        var extra = refund.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        extra.put(RefundExtraIndexEnum.orderPushErrorType.name(), orderPushErrorType);

        var updateObject = new Refund();
        updateObject.setId(refundId);
        updateObject.setExtra(extra);
        refundWriteService.update(updateObject);
    }
}
