package moonstone.web.core.events.user.listener;

import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.web.core.component.publisher.CacheInvalidateContext;
import moonstone.web.core.component.publisher.CacheInvalidatePublisher;
import moonstone.web.core.component.publisher.CacheInvalidateType;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.user.LoginUserCacheInvalidateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Author:cp
 * Created on 09/11/2016.
 */
@Component
@Slf4j
public class LoginUserCacheInvalidateListener extends AbstractVerticle {

    @Autowired
    private CacheInvalidatePublisher cacheInvalidatePublisher;

    @Autowired
    private UserCacheHolder userCacheHolder;


    @EventListener(LoginUserCacheInvalidateEvent.class)
    @VertxEventBusListener(LoginUserCacheInvalidateEvent.class)
    public void invalidate(LoginUserCacheInvalidateEvent event) {
        userCacheHolder.invalidate(event.getUserId());
        CacheInvalidateContext context = new CacheInvalidateContext(CacheInvalidateType.LOGIN_USER.toString(),
                event.getUserId().toString());
        cacheInvalidatePublisher.invalidate(context);
    }

    @EventListener(UserUpdateEvent.class)
    @VertxEventBusListener(UserUpdateEvent.class)
    public void invalidateAtUserUpdated(UserUpdateEvent userUpdateEvent) {
        userCacheHolder.invalidate(userUpdateEvent.getUserId());
    }

}
