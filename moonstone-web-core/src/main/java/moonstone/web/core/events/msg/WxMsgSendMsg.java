package moonstone.web.core.events.msg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.web.core.component.WxMsgEventManager;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Deprecated
public class WxMsgSendMsg {
    WxMsgEventManager.WxMsgFormId source;
    String appId;
    String template;
    Long userId;
    String formId;
    List<Long> relatedIds;
    Map<String, String> data;
}
