package moonstone.web.core.events.msg.listener;

import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.msg.api.MsgChannelDetector;
import io.terminus.msg.enums.MsgChannel;
import io.terminus.msg.exception.MsgException;
import io.terminus.msg.service.MsgService;
import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.CountryCode;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.config.MsgEmailConfig;
import moonstone.web.core.config.MsgEmailYamlTemplate;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.sms.RawSmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.Serializable;
import java.util.Map;

@Slf4j
@Component
public class MsgSendRequestListener extends AbstractVerticle {
    @Autowired
    RawSmsService rawSmsService;
    @Autowired
    MsgService msgService;
    @Autowired
    MsgEmailConfig msgEmailConfig;
    @Autowired
    MsgChannelDetector msgChannelDetector;
    @Autowired
    TemplateEngine templateEngine;

    @VertxEventBusListener(MsgSendRequestEvent.class)
    @EventListener(MsgSendRequestEvent.class)
    public void msgSendRequest(MsgSendRequestEvent msgSendRequestEvent) {
        try {
            sendSms(msgSendRequestEvent);
        } finally {
            log.info("SEND SMS -> {}", msgSendRequestEvent);
        }
    }
    public void sendSms(MsgSendRequestEvent msgSendRequestEvent) {
        MsgChannel msgChannel = msgChannelDetector.detectChannel(msgSendRequestEvent.toes());
        switch (msgChannel) {
            case Sms -> {   /* 短信 */
                String mobile = msgSendRequestEvent.toes();
                String template = msgSendRequestEvent.template();
                Serializable code = msgSendRequestEvent.context().get("code");
                if (mobile.startsWith(CountryCode.PREFIX_CODE)) {
                    //not chinese number
                    if (mobile.startsWith(CountryCode.China.getCode())) {
                        mobile = (mobile.substring(CountryCode.PREFIX_CODE_LEN));
                    }
                }
                try {
                    String result = rawSmsService.postSms(msgSendRequestEvent);
                    log.info("sendSms result={}, host={}, mobile={}, template={}, message={}", result, msgSendRequestEvent.context().get("host"), mobile, template, code);
                } catch (MsgException e) {
                    log.error("sms send failed, mobile={}, cause:{}", mobile, Throwables.getStackTraceAsString(e));
                    throw new JsonResponseException("sms.send.fail");
                }
            }
            case Email -> { /* 邮件 */
                String email = msgSendRequestEvent.toes();
                String templateKey = msgSendRequestEvent.template();
                Map<String, Serializable> msgContext = msgSendRequestEvent.context();
                try {
                    MsgEmailYamlTemplate template = msgEmailConfig.getTemplateMap().get(templateKey);
                    if (template == null) {
                        log.error("can not to find msg template by key={}", templateKey);
                        throw new JsonResponseException("msg.email.template.find.fail");
                    }
                    String subject = template.getSubject();
                    Context context = new Context();
                    context.setVariable("title", msgSendRequestEvent.context().get("title"));
                    for (String contextKey : msgContext.keySet()) {
                        context.setVariable(contextKey, msgContext.get(contextKey));
                    }
                    // add the title check
                    if (context.getVariable("title") == null) {
                        context.setVariable("title", "异常通知");
                    }
                    String emailContent = templateEngine.process(template.getTemplateName(), context);
                    String result = msgService.send(email, subject, emailContent);
                    log.info("send email result={}, email={}, title={}", result, email, subject);
                } catch (MsgException e) {
                    log.error("email send failed, email={}, templateKey={}, msgContext={}, cause: {}",
                            email, templateKey, msgContext, Throwables.getStackTraceAsString(e));
                    throw new JsonResponseException("email.send.fail");
                }
            }
            default -> {
            }
        }
    }
}
