package moonstone.web.core.events.trade.listener;

import io.terminus.common.model.Response;
import io.vertx.core.Context;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopItemCacher;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.utils.EventSender;
import moonstone.event.OrderCreatedEvent;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemWriteService;
import moonstone.item.service.SkuReadService;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.storage.service.StorageService;
import moonstone.weShop.model.WeShopItem;
import moonstone.web.core.component.cache.SkuStockCacheAtRedisManager;
import moonstone.web.core.events.api.RefreshItemStockByTimePeriod;
import moonstone.web.core.events.item.ItemDeletedEvent;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.events.weShopItem.WeShopItemDumpEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
public class StorageDecreaseListener {
    @Autowired
    Vertx vertx;
    @Autowired
    private StorageService storageService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private ItemWriteService itemWriteService;
    @Autowired
    private WeShopItemCacher weShopItemCacher;
    @Autowired
    private SkuStockCacheAtRedisManager skuStockCacheAtRedisManager;

    @PostConstruct
    public void initVertx() {
        vertx.eventBus().<JsonArray>localConsumer(RefreshItemStockByTimePeriod.ADDRESS, msg -> {
            Context context = vertx.getOrCreateContext();
            if (context.get("queue") == null) {
                context.put("queue", new HashSet<>());
            }
            Set<Long> queue = context.get("queue");
            msg.body().forEach(itemId -> queue.add((Long) itemId));
            msg.reply(null);
        });

        vertx.periodicStream(100L)
                .handler(turn -> {
                    Set<Long> queue = vertx.getOrCreateContext().get("queue");
                    if (queue == null) {
                        return;
                    }
                    for (Long id : queue) {
                        EventSender.publish(new ItemUpdateEvent(id));
                    }
                    queue.clear();
                });
    }

    @EventListener(OrderCreatedEvent.class)
    public void decrStorageAndIncrSale(OrderCreatedEvent orderCreatedEvent) {
        Long shopOrderId = orderCreatedEvent.getOrderId();

        Response<List<SkuOrder>> findSkuOrders = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!findSkuOrders.isSuccess()) {
            log.error("fail to find sku orders by shopId={},cause:{}",
                    shopOrderId, findSkuOrders.getError());
            return;
        }
        List<SkuOrder> skuOrders = findSkuOrders.getResult();
        Set<Long> itemIdSet = new HashSet<>(8);
        try {
            for (SkuOrder skuOrder : skuOrders) {
                Response<Boolean> deltaR = storageService.decreaseBy(skuOrder.getSkuId(), null, null, skuOrder.getQuantity());
                if (!deltaR.isSuccess()) {
                    log.error("fail to decrease sku(id={})'s storage, delta={}, error code:{}",
                            skuOrder.getSkuId(), skuOrder.getQuantity(), deltaR.getError());
                }
                Response<Sku> rSku = skuReadService.findSkuById(skuOrder.getSkuId());
                if (rSku.isSuccess() && rSku.getResult().getStockQuantity() <= 0) {
                    clearSkuQuantity(skuOrder.getSkuId());
                    itemWriteService.updateStatusByItemId(rSku.getResult().getItemId(), -1);
                    EventSender.publish(new ItemDeletedEvent(rSku.getResult().getItemId()));
                    itemIdSet.add(rSku.getResult().getItemId());
                }
                ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
                if (Objects.nonNull(shopOrder.getOutShopId()) && Objects.equals(shopOrder.getOutFrom(), OrderOutFrom.WE_SHOP.Code())) {
                    long weShopId = Long.parseLong(shopOrder.getOutShopId());
                    WeShopItem weShopItem = weShopItemCacher.findWeShopItemByWeShopIdAndItemId(weShopId, skuOrder.getItemId());
                    weShopItemCacher.invalidateWeShopItemById(weShopItem.getId());
                    // weShopItemWriteService.increaseSellQuantity(skuOrder.getQuantity(), skuOrder.getItemId(), weShopId);
                    EventSender.send(new WeShopItemDumpEvent(weShopItem.getId()));
                    weShopItemCacher.invalidateWeShopItemById(weShopItem.getId());
                }
            }
        } catch (Exception e) {
            log.error("Fail to decrease Item Quantity By Order[{}]", orderCreatedEvent.getOrderId(), e);
        }
        JsonArray array = new JsonArray();
        itemIdSet.forEach(array::add);
        vertx.eventBus().request(RefreshItemStockByTimePeriod.ADDRESS, array);
    }

    /**
     * 清楚缓存迫使它重新获取单品数量
     *
     * @param skuId 单品Id
     */
    private void clearSkuQuantity(Long skuId) {
        skuStockCacheAtRedisManager.removeCache(Collections.singletonList(skuId));
    }
}
