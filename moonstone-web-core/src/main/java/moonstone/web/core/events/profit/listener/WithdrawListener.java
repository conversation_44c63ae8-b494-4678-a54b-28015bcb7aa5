package moonstone.web.core.events.profit.listener;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.web.core.component.WxMsgEventManager;
import moonstone.web.core.events.msg.WxMsgPreSendMsg;
import moonstone.web.core.events.msg.WxMsgSendMsg;
import moonstone.web.core.events.profit.WithdrawFailEvent;
import moonstone.web.core.events.profit.WithdrawSuccessEvent;
import org.joda.time.DateTime;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Component
public class WithdrawListener {
    @RpcConsumer
    private WithdrawAccountReadService withdrawAccountReadService;
    @RpcConsumer
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;
    @RpcConsumer
    private WxMsgEventManager wxMsgEventManager;

    /**
     * 发送提现失败的信息
     *
     * @param withdrawFailEvent 提现失败的事件
     */
    @EventListener(WithdrawFailEvent.class)
    private void withdrawFailed(WithdrawFailEvent withdrawFailEvent) {
        log.info("withdrawFailed {}", JSON.toJSONString(withdrawFailEvent));
        Consumer<WxMsgSendMsg> constructer = (msg) -> {
            Map<String, String> data = msg.getData();
            data.put("keyword1", withdrawFailEvent.getProfit().toString());
            data.put("keyword2", withdrawFailEvent.getReason());
            data.put("keyword3", new DateTime(withdrawFailEvent.getTime()).toString("YYYY-MM-dd HH:mm:ss"));
            data.put("keyword4", withdrawFailEvent.getAccount());
        };
        wxMsgEventManager.constructAndSend(new WxMsgPreSendMsg(withdrawFailEvent.getUserId(), withdrawFailEvent.getApplyId(), "WithdrawFail", WithDrawProfitApply.class), constructer);
    }

    /**
     * 发送体现成功的事件
     *
     * @param withdrawSuccessEvent 提现成功的事件
     */
    @EventListener(WithdrawSuccessEvent.class)
    private void withdrawSuccess(WithdrawSuccessEvent withdrawSuccessEvent) {
        log.info("withdrawSuccess {}", JSON.toJSONString(withdrawSuccessEvent));
        Consumer<WxMsgSendMsg> constructer = (msg) -> {
            Map<String, String> data = msg.getData();
            data.put("keyword1", withdrawSuccessEvent.getProfit().toString());
            data.put("keyword2", withdrawSuccessEvent.getAccount());
            data.put("keyword3", new DateTime(withdrawSuccessEvent.getTime()).toString("YYYY-MM-dd HH;mm:ss"));
            data.put("keyword4", new DateTime(withdrawSuccessEvent.getPaidAt()).toString("YYYY-MM-dd HH;mm:ss"));
        };
        wxMsgEventManager.constructAndSend(new WxMsgPreSendMsg(withdrawSuccessEvent.getUserId(), withdrawSuccessEvent.getApplyId(), "WithdrawSuccess", WithDrawProfitApply.class), constructer);
    }
}
