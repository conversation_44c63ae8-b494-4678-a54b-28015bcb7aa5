package moonstone.web.core.events.trade.listener.config;

import moonstone.web.core.events.trade.listener.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "trade.moonstone.web.core.component.listener.enable", havingValue = "true", matchIfMissing = true)
public class TradeListenerConfig {

    @Bean
    public CartRemovalListener cartRemovalListener() {
        return new CartRemovalListener();
    }

    @Bean
    public OrderCancelListener orderCancelListener() {
        return new OrderCancelListener();
    }

    @Bean
    public OrderCommentMarkListener orderCommentMarkListener() {
        return new OrderCommentMarkListener();
    }

    @Bean
    public OrderConfirmListener orderConfirmListener() {
        return new OrderConfirmListener();
    }

    @Bean
    public OrderCreationListener orderCreationListener() {
        return new OrderCreationListener();
    }

    @Bean
    public OrderPaymentListener orderPaymentListener() {
        return new OrderPaymentListener();
    }

    @Bean
    public OrderPromotionFreezeListener orderPromotionFreezeListener() {
        return new OrderPromotionFreezeListener();
    }

    @Bean
    public OrderRefundStockRollbackListener orderRefundListener() {
        return new OrderRefundStockRollbackListener();
    }

    @Bean
    public OrderShipmentListener orderShipmentListener() {
        return new OrderShipmentListener();
    }

    @Bean
    public OrderStatusUpdater orderStatusUpdater() {
        return new OrderStatusUpdater();
    }

    @Bean
    public PaymentCallBackListener paymentCallBackListener() {
        return new PaymentCallBackListener();
    }

    @Bean
    public PaymentPromotionFreezeListener paymentPromotionFreezeListener() {
        return new PaymentPromotionFreezeListener();
    }

    @Bean
    public StorageDecreaseListener storageDecreaseListener() {
        return new StorageDecreaseListener();
    }

    @Bean
    public OrderCommentListener orderCommentListener(){
        return new OrderCommentListener();
    }

    @Bean
    public WechatpayRefundCallbackListener wechatpayRefundCallbackListener(){
        return new WechatpayRefundCallbackListener();
    }

    @Bean
    public OrderIntegralEventListener orderIntegralEventListener(){
        return new OrderIntegralEventListener();
    }

    @Bean
    public OrderIntegralCountEventListener orderIntegralCountEventListener(){
        return new OrderIntegralCountEventListener();
    }
}
