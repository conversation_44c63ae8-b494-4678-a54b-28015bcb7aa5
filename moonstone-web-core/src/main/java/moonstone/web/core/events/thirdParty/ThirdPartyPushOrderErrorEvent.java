package moonstone.web.core.events.thirdParty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  <PERSON>aiZhy
 * Date:    2019/2/18
 */
@AllArgsConstructor
@NoArgsConstructor
public class ThirdPartyPushOrderErrorEvent implements Serializable {
    private static final long serialVersionUID = -2461918594189828069L;

    @Getter
    private List<Long> errorOrderIds;

    @Getter
    private String location;

    @Getter
    private String errorCode;
}
