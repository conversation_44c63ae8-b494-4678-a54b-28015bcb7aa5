package moonstone.web.core.events.trade.listener;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.OrderOutFrom;
import moonstone.event.OrderCreatedEvent;
import moonstone.item.api.ProfitGain;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SubStoreOrderListener extends AbstractVerticle {
    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;
    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;
    @RpcConsumer
    private SkuReadService skuReadService;
    @RpcConsumer
    private ProfitGain profitGain;

    @VertxEventBusListener(OrderCreatedEvent.class)
    @EventListener(OrderCreatedEvent.class)
    public void onCreatedOrder(OrderCreatedEvent orderCreatedEvent) {
        if (orderCreatedEvent.getOrderId() != null) {
            val rShopOrder = shopOrderReadService.findById(orderCreatedEvent.getOrderId());
            if (!rShopOrder.isSuccess()) {
                log.error("[SubStoreOrderListener](subscribe) find ShopOrder failed by shopOrderId:{}", orderCreatedEvent.getOrderId());
                return;
            }
            ShopOrder shopOrder = rShopOrder.getResult();
            if (ObjectUtils.isEmpty(shopOrder.getOutFrom())) {
                log.info("[ProfitPersit](onCreatedOrder) no design for null or empty outFrom of shopOrderId:{}", shopOrder.getId());
                return;
            }
            switch (shopOrder.getOutFrom()) {
                case "subStore": {
                    if (shopOrder.getOutFrom().equals(OrderOutFrom.SUB_STORE.Code())) {
                        persistProfit(shopOrder);
                    }
                }
            }
        }
    }

    private void persistProfit(ShopOrder shopOrder) {
        val skuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId());
        try {
            val rSkuList = skuReadService.findSkusByIds(Optional.of(skuOrders.getResult()).orElseThrow(() -> new RuntimeException("[persistSubStoreProfit] find SkuOrder failed by shopOrderId:" + shopOrder.getId()))
                    .stream().map(SkuOrder::getSkuId).collect(Collectors.toList()));
            List<Sku> skuList = Optional.of(rSkuList.getResult()).orElseThrow(() -> new RuntimeException("[persistSubStoreProfit] find sku by idList failed by ShopOrderId:" + shopOrder.getId()));
            // TODO: 5/8/19 添加订单结算利润
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
        }
    }
}
