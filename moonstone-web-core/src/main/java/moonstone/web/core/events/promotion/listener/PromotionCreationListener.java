package moonstone.web.core.events.promotion.listener;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.PromotionTrack;
import moonstone.promotion.service.PromotionReadService;
import moonstone.promotion.service.PromotionTrackWriteService;
import moonstone.web.core.events.promotion.PromotionCreatedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Author:cp
 * Created on 6/25/16.
 */
@Component
@Slf4j
public class PromotionCreationListener {

    @RpcConsumer
    private PromotionReadService promotionReadService;

    @RpcConsumer
    private PromotionTrackWriteService promotionTrackWriteService;

    /**
     * 用户营销创建成功后，异步创建营销跟踪记录
     *
     * @param event
     */
    @EventListener(PromotionCreatedEvent.class)
    public void onCreated(PromotionCreatedEvent event) {
        final Long promotionId = event.promotionId();
        Response<Promotion> findResp = promotionReadService.findById(promotionId);
        if (!findResp.isSuccess()) {
            log.error("fail to find promotion by id:{},cause:{}",
                    promotionId, findResp.getError());
            return;
        }
        Promotion promotion = findResp.getResult();

        if (PromotionType.isUserPromotion(promotion.getType())) {
            PromotionTrack promotionTrack = new PromotionTrack();
            promotionTrack.setPromotionId(promotionId);
            promotionTrack.setReceivedQuantity(0);
            promotionTrack.setUsedQuantity(0);

            Response<Long> createResp = promotionTrackWriteService.create(promotionTrack);
            if (!createResp.isSuccess()) {
                log.error("fail to create promotion track for promotion(id={}),cause:{}",
                        promotionId, createResp.getError());
            }
        }

    }
}
