package moonstone.web.core;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MeltDownProtectorBySession extends AbstractVerticle implements Filter {
    public static final String ADDRESS = "melt-down.protector.record";
    private static final String ADDRESS_SERVE = "melt-down.protector.serve";

    Map<String, List<LocalDateTime>> meltDownRecord = new HashMap<>();
    Map<String, List<LocalDateTime>> serveRecord = new HashMap<>();
    Map<String, LocalDateTime> blocked = new HashMap<>();

    @Override
    public void start() throws Exception {
        vertx.eventBus().<Buffer>consumer(ADDRESS)
                .handler(this::recordSession);
        vertx.eventBus().<Buffer>consumer(ADDRESS_SERVE)
                .handler(this::recordServeSession);
        vertx.setPeriodic(3000, delay -> removeTimeoutRecord(LocalDateTime.now().minusSeconds(3), meltDownRecord));
        vertx.setPeriodic(12000, delay -> removeTimeoutRecord(LocalDateTime.now().minusSeconds(12), serveRecord));
        vertx.setPeriodic(60000, this::clearBlocked);
    }

    private void clearBlocked(long delay) {
        List<String> forRemoved = new LinkedList<>();
        LocalDateTime timeout = LocalDateTime.now().minusMinutes(1);
        for (Map.Entry<String, LocalDateTime> entry : blocked.entrySet()) {

            if (entry.getValue().isAfter(timeout)) {
                forRemoved.add(entry.getKey());
            }
        }
        for (String key : forRemoved) {
            blocked.remove(key);
        }
    }


    private void removeTimeoutRecord(LocalDateTime timeout, Map<String, List<LocalDateTime>> record) {
        // remove timeout
        for (List<LocalDateTime> list : record.values()) {
            List<LocalDateTime> removed = new LinkedList<>();
            for (LocalDateTime time : list) {
                if (time.isBefore(timeout)) {
                    removed.add(time);
                }
            }
            list.removeAll(removed);
        }
        // remove empty entry
        List<String> shouldRemove = new LinkedList<>();
        record.forEach((index, list) -> {
            if (list.isEmpty()) {
                shouldRemove.add(index);
            }
        });
        shouldRemove.forEach(record::remove);
    }

    private void recordServeSession(Message<Buffer> session) {
        List<LocalDateTime> list = serveRecord.computeIfAbsent(session.body().toString(),
                sessionId -> new LinkedList<>());
        LocalDateTime now = LocalDateTime.now();
        if(!list.isEmpty()){
            LocalDateTime last = list.get(list.size()-1);
            if (last==null){
                return;
            }
            if (last.isAfter(now.minusSeconds(2))){
                return;
            }
        }
        list.add(now);
        LocalDateTime triggerTime = now.minusSeconds(12);
        if (list.size() > 10) {
            list.subList(0, list.size() - 10).clear();
        }
        int time = 0;
        for (LocalDateTime localDateTime : list) {
            if (localDateTime.isAfter(triggerTime)) {
                time++;
            }
            if (time >= 3) {
                blocked.put(session.body().toString(), now);
                return;
            }
        }
    }

    private void recordSession(Message<Buffer> session) {
        List<LocalDateTime> list = meltDownRecord.computeIfAbsent(session.body().toString(),
                sessionId -> new LinkedList<>());
        list.add(LocalDateTime.now());
        // limit 10
        if (list.size() > 10) {
            int left = list.size() - 10;
            list.subList(0, left).clear();
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (serveAttack(servletRequest)) {
            String sessionId = ((HttpServletRequest) servletRequest).getSession().getId();
            log.error("Attack detected [{}]", sessionId);
            return;
        }
        // first melt down for one second every three bug within one second
        if (blockMeltDown(servletRequest)) {
            String sessionId = ((HttpServletRequest) servletRequest).getSession().getId();
            log.warn("Guy [{}] has problem now", sessionId);
            // record the error if it happen three time within 12 second, block it for 60 second
            vertx.eventBus().publish(MeltDownProtectorBySession.ADDRESS_SERVE, Buffer.buffer(sessionId));
            return;
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private boolean serveAttack(ServletRequest servletRequest) {
        if (!(servletRequest instanceof HttpServletRequest)) {
            return false;
        }
        HttpSession session = ((HttpServletRequest) servletRequest).getSession();
        if (session == null) {
            return false;
        }
        String id = session.getId();
        LocalDateTime blockOut = blocked.get(id);
        if (blockOut == null) {
            return false;
        }
        return blockOut.isAfter(LocalDateTime.now().minusMinutes(1));
    }

    private boolean blockMeltDown(ServletRequest servletRequest) {
        if (!(servletRequest instanceof HttpServletRequest)) {
            return false;
        }
        HttpSession session = ((HttpServletRequest) servletRequest).getSession();
        if (session == null) {
            return false;
        }
        String id = session.getId();
        return checkTrigger(LocalDateTime.now().minusSeconds(1), 3, id, meltDownRecord);
    }

    private boolean checkTrigger(LocalDateTime out, int limit, String id, Map<String, List<LocalDateTime>> record) {
        List<LocalDateTime> meltDownList = record.get(id);
        if (meltDownList == null) {
            return false;
        }
        int time = 0;
        for (LocalDateTime error : meltDownList) {
            if (error.isAfter(out)) {
                time++;
            }
            if (time >= limit) {
                return true;
            }
        }
        return false;
    }
}
