package moonstone.web.core;

import blue.sea.moonstone.bridge.app.ShareDataHelper;
import com.hazelcast.core.HazelcastInstance;
import io.vertx.core.*;
import io.vertx.core.dns.AddressResolverOptions;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.dropwizard.DropwizardMetricsOptions;
import io.vertx.redis.client.RedisOptions;
import io.vertx.spi.cluster.hazelcast.HazelcastClusterManager;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.VertxInstance;
import moonstone.common.api.ConfiguredVerticle;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.session.SessionConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import javax.annotation.PreDestroy;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Configuration
public class VertxAutoLoader {
    @Autowired
    HazelcastInstance hazelcastInstance;

    @Autowired(required = false)
    VertxOptions vertxOptions;


    @Autowired(required = false)
    List<Verticle> verticleList;
    @Autowired
    SessionConfig sessionConfig;

    @Value("${spring.data.mongodb.uri}")
    String address;

    Vertx vertx;

    @PreDestroy
    public void close() throws Exception {
        Thread.sleep(5000L);
        vertx.close();
        hazelcastInstance.shutdown();
    }

    @Bean
    public Vertx vertx() throws Exception {
        if (vertxOptions == null) {
            log.info("VertxAutoLoader.vertx vertxOptions is null");
            if (hazelcastInstance == null) {
                log.warn("{} singleton VertX not cluster cause no configuration and no hazelcast", LogUtil.getClassMethodName());
                return Vertx.vertx();
            }
            vertxOptions = new VertxOptions();
            if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
                vertxOptions.setAddressResolverOptions(readResolveFromSystem(new FileInputStream("/etc/resolv.conf")));
            }
            vertxOptions.setClusterManager(new HazelcastClusterManager(hazelcastInstance));

            String host = hazelcastInstance.getCluster().getLocalMember().getAddress().getHost();
            log.info("VertxAutoLoader.vertx, used EventBusOptions.host={}", host);
            vertxOptions.getEventBusOptions().setHost(host);

            int availableProcessors = Runtime.getRuntime().availableProcessors();
            log.info("VertxAutoLoader.vertx, Runtime.getRuntime().availableProcessors()={}", availableProcessors);
            vertxOptions.setWorkerPoolSize(availableProcessors * 4);
            vertxOptions.setEventLoopPoolSize(availableProcessors * 8);
            vertxOptions.setMetricsOptions(new DropwizardMetricsOptions().setEnabled(true));
        }
        CompletableFuture<Vertx> vertXTaker = new CompletableFuture<>();
        Vertx.clusteredVertx(vertxOptions, clusterVertXResult -> {
            if (clusterVertXResult.succeeded()) {
                vertXTaker.complete(clusterVertXResult.result());
            } else {
                vertXTaker.completeExceptionally(clusterVertXResult.cause());
            }
        });
        vertx = vertXTaker.get().exceptionHandler(e -> log.error("Vertx Error:", e));
        putRedis();
        putMongo();
        VertxInstance.Main.setVertx(vertx);
        return vertx;
    }

    private void putMongo() {
        JsonObject mongoConfig = new JsonObject().put("connection_string", address)
                .put("maxPoolSize", 15).put("maxIdleTimeMS", 15000);
        ShareDataHelper.putMongoConfig(vertx, mongoConfig);
        vertx.sharedData().getAsyncMap("ShareDataHelper")
                .onSuccess(map -> map.put("mongo", mongoConfig));
    }

    private void putRedis() {
        String address = sessionConfig.getRedisHost();
        try {
            address = InetAddress.getByName(address).getHostAddress();
        } catch (Exception e) {
            log.warn("{} fail to look for Redis => {}", LogUtil.getClassMethodName(), address);
        }
        RedisOptions redisOptions = new RedisOptions()
                .setEndpoints(Collections.singletonList("redis://" + address + ":" + sessionConfig.getRedisPort()))
                .setPassword(sessionConfig.getRedisAuth());
        vertx.sharedData().getAsyncMap("ShareDataHelper")
                .onSuccess(map -> map.put("redis", redisOptions.toJson()));
    }

    private AddressResolverOptions readResolveFromSystem(InputStream inputStream) {
        AddressResolverOptions addressResolverOptions = new AddressResolverOptions();
        try {
            Scanner scanner = new Scanner(inputStream);
            while (scanner.hasNext()) {
                String line = scanner.nextLine();
                if (line.trim().startsWith("#")) {
                    continue;
                }
                int seperate = line.indexOf(' ');
                if (seperate == -1) {
                    seperate = line.indexOf('\t');
                }
                if (seperate == -1) {
                    continue;
                }
                switch (line.substring(0, seperate).toLowerCase()) {
                    case "nameserver" -> {
                        if (addressResolverOptions.getServers() == null) {
                            addressResolverOptions.setServers(new LinkedList<>());
                        }
                        addressResolverOptions.getServers().add(line.substring(seperate).trim());
                    }
                    case "search" -> {
                        if (addressResolverOptions.getSearchDomains() == null) {
                            addressResolverOptions.setSearchDomains(new LinkedList<>());
                        }
                        addressResolverOptions.getSearchDomains().addAll(Arrays.asList(line.substring(seperate).trim().split(" ")));
                    }
                    case "options" -> {
                        Scanner optionScanner = new Scanner(line);
                        // skip options
                        optionScanner.next();
                        while (optionScanner.hasNext()) {
                            String option = optionScanner.next();
                            if (option.startsWith("ndots:")) {
                                addressResolverOptions.setNdots(Integer.parseInt(option.split("ndots:")[1]));
                            }
                        }
                    }
                    default -> log.debug("{} unrecognized line[{}]", LogUtil.getClassMethodName(), line);
                }
            }
        } catch (Exception e) {
            log.error("{} fail to read conf, this only work at linux", LogUtil.getClassMethodName(), e);
        }
        return addressResolverOptions;
    }

    @EventListener(ContextRefreshedEvent.class)
    public void autoRegisterAtContextRefreshedEvent() {
        if (verticleList == null) {
            log.warn("{} not verticle is valid", LogUtil.getClassMethodName());
            return;
        }
        vertx.exceptionHandler(throwable -> log.error(LogUtil.express(throwable)));
        List<Future> verticleDeployRes = new ArrayList<>();
        for (Verticle verticle : verticleList) {
            Future<String> f;
            if (verticle instanceof ConfiguredVerticle configuredVerticle) {
                var opt = new DeploymentOptions(configuredVerticle.config());
                f = vertx.deployVerticle(verticle, opt);
            } else {
                f = vertx.deployVerticle(verticle);
            }
            verticleDeployRes.add(f);
        }
        CompositeFuture.join(verticleDeployRes)
                .toCompletionStage().toCompletableFuture().join();
        log.info("Service Complete Boot");
    }
}
