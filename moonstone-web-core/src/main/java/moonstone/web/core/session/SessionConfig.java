package moonstone.web.core.session;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConfigurationProperties(value = "session", ignoreInvalidFields = true)
@Data
public class SessionConfig {
    // 如果值为none 则忽略
    String cookieDomain;
    String cookieContextPath;
    String cookieName;
    Integer cookieMaxAge = 3000;
    String serializeType;
    String source;
    String redisHost;
    Long redisPort;
    Integer redisIndex;
    Boolean redisCluster;
    Boolean redisTestOnBorrow;
    Integer redisMaxTotal;
    Integer redisMaxIdle;
    String redisPrefix;
    Integer maxInactiveInterval = 3000;
    String redisAuth;
}
