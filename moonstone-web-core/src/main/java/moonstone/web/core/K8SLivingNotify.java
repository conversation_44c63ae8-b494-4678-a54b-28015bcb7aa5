package moonstone.web.core;

import com.hazelcast.cluster.Member;
import com.hazelcast.core.HazelcastInstance;
import io.vertx.core.Vertx;
import io.vertx.ext.dropwizard.MetricsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;

/**
 * 用于K8S检测 应用是否已经准备完毕,切勿修改
 */
@RestController
public class K8SLivingNotify {
    @Autowired
    private HazelcastInstance hazelcastInstance;

    @Autowired
    private Vertx vertx;

    MetricsService service;
    @PostConstruct
    public void init(){
        service = MetricsService.create(vertx);;
    }
    @GetMapping("/api/hzl/instance")
    public Set<Member> getNode() {
        return hazelcastInstance.getCluster().getMembers();
    }

    @GetMapping("/api/k8s/living")
    public String living() {
        return String.format("living at [%s]", new Date());
    }

    @GetMapping("/api/metrics")
    public Map<String, ?> metric(String name){
        return service.getMetricsSnapshot(name).getMap();
    }

    @GetMapping("/api/metrics/all")
    public Set<String> metricAll(){
        return service.metricsNames();
    }
}
