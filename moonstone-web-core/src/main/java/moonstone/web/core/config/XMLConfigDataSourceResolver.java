package moonstone.web.core.config;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.XML;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.env.OriginTrackedMapPropertySource;
import org.springframework.boot.env.PropertySourceLoader;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.*;

/**
 * not used at now, but xml is more easy configuration than yaml
 */
@Configuration
@Slf4j
public class XMLConfigDataSourceResolver implements PropertySourceLoader, EnvironmentPostProcessor {
    @Override
    public String[] getFileExtensions() {
        return new String[]{"xml"};
    }

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        try {
            for (String activeProfile : environment.getActiveProfiles()) {
                var r = application.getResourceLoader().getResource("/config-" + activeProfile + ".xml");
                if (!r.exists()) continue;
                for (PropertySource<?> propertySource : load(r.getFilename(), r)) {
                    environment.getPropertySources().addLast(propertySource);
                }
            }
        }catch (Exception e){
            log.error("FAIL TO LOAD THE CONFIG", e);
        }
    }

    @Override
    public List<PropertySource<?>> load(String name, Resource resource) throws IOException {
        log.debug("LOADING {} <- {}", name, resource.getURL());
        var config = XML.parseXML(resource.getInputStream());
        Map<String, String> property = new HashMap<>();
        Map<XML, String> prefixMap = new HashMap<>();
        Queue<XML> xmlQueue = new LinkedList<>();
        xmlQueue.add(config);
        prefixMap.put(config, "");
        while (!xmlQueue.isEmpty()) {
            var node = xmlQueue.poll();
            var prefix = prefixMap.get(node);
            if (node.getAllAttribute() != null) {
                for (String attr : node.getAllAttribute()) {
                    property.put(prefix + attr, node.getAttribute(attr));
                }
            }
            if (node.elements() == null) {
                if (node.value() != null) {
                    property.put(prefix, node.value().toString());
                }
                continue;
            }
            for (Map.Entry<String, List<XML>> entry : node.elements().entrySet()) {
                for (XML xml : entry.getValue()) {
                    xmlQueue.add(xml);
                    prefixMap.put(xml, prefix + xml.name() + ".");
                }
            }
        }
        List<PropertySource<?>> propertySources = new LinkedList<>();
        propertySources.add(new OriginTrackedMapPropertySource("CONFIG-XML", property));
        return propertySources;
    }
}
