package moonstone.web.core.config;

import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.Reflector;
import org.apache.ibatis.reflection.ReflectorFactory;
import org.apache.ibatis.reflection.invoker.Invoker;

import java.lang.reflect.InvocationTargetException;
import java.util.concurrent.ConcurrentHashMap;

public class RecordSupportReflectFactory implements ReflectorFactory {
    static final DefaultReflectorFactory DEFAULT = new DefaultReflectorFactory();
    ConcurrentHashMap<Class<?>, Reflector> map = new ConcurrentHashMap<>();

    @Override
    public boolean isClassCacheEnabled() {
        return DEFAULT.isClassCacheEnabled();
    }

    @Override
    public void setClassCacheEnabled(boolean classCacheEnabled) {
        DEFAULT.setClassCacheEnabled(classCacheEnabled);
    }

    @Override
    public Reflector findForClass(Class<?> type) {
        if (type.isRecord()) {
            return map.computeIfAbsent(type, RecordReflector::new);
        } else {
            return DEFAULT.findForClass(type);
        }
    }

    static class RecordReflector extends Reflector {
        Class<?> t;
        RecordReflector(Class<?> clazz) {
            super(clazz);
            t = clazz;
        }

        @Override
        public Invoker getSetInvoker(String propertyName) {
            return new Invoker() {
                @Override
                public Object invoke(Object target, Object[] args) throws IllegalAccessException, InvocationTargetException {
                   return target;
                }

                @Override
                public Class<?> getType() {
                    return t;
                }
            };
        }
    }
}
