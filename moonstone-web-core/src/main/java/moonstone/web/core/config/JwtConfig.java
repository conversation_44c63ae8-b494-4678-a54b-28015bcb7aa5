package moonstone.web.core.config;

import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.spec.SecretKeySpec;
import javax.validation.constraints.NotNull;
import java.security.Key;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Getter
@Component
public class JwtConfig {
    @Value("${session.jwt_key:}")
    private String jwtKey;
    @Value("${session.jwt_name:dev_app_token}")
    private String jwtCookieName;
    private final SignatureAlgorithm defaultAlg = SignatureAlgorithm.HS256;
    private final String SIGNER = "SA";


    /**
     * generate key for jwt decode
     *
     * @param jwtRaw jwtRaw with key agl information
     * @param key    secret key of jwt
     * @return Key the jwtParser accept
     */
    public Key prepareKey(@NotNull String jwtRaw, @NotNull String key) {
        return new SecretKeySpec(key.getBytes(), getAlg(jwtRaw));
    }

    /**
     * read alg from jwt string
     *
     * @param jwtRaw jwt string contain key
     * @return alg
     */
    public String getAlg(String jwtRaw) {
        String algName = JSONObject.parseObject(new String(Base64.getUrlDecoder().decode(jwtRaw.split("\\.")[0]))).getString("alg");
        for (SignatureAlgorithm alg : SignatureAlgorithm.values()) {
            if (alg.getValue().equals(algName.toUpperCase())) {
                return alg.getJcaName();
            }
        }
        return defaultAlg.getJcaName();
    }
}
