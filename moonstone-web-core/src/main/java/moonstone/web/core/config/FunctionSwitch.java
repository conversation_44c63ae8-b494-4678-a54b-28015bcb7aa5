package moonstone.web.core.config;

import com.google.gson.Gson;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.event.SwitchNotifyEvent;
import moonstone.common.utils.LogUtil;
import moonstone.event.MethodSwitchNotify;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import scala.Option;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.lang.reflect.Field;

/**
 * Author:  CaiZhy
 * Date:    2019/3/4
 */
@Data
@Configuration
@Slf4j
public class FunctionSwitch implements Serializable {
    private static final long serialVersionUID = -7310487759741710371L;

    @Value("${function.switch.aoXinPush: false}")
    private Boolean aoXinPush;      //澳新推送功能开关
    @Value("${function.switch.financePush: false}")
    private Boolean financePush;    //财务推送功能开关
    @Value("${function.switch.unifiedPayment: false}")
    private Boolean unifiedPayment; //统一支付功能开关
    @Value("${function.switch.editPrivilegeRelevel:false}")
    private Boolean revokeShopItemEditPrivilege; //商家编辑权限回收管理
    @Value("${function.switch.foreignSms: false}")
    private Boolean foreignSms; //国际短信功能开关
    @Value("${function.switch.thirdPartyStockLimit:false}")
    private Boolean thirdPartyStockLimit;//第三方库存限制商品创建
    @Value("${function.switch.allowCreateItem:true}")
    private Boolean allowCreateItem;//允许商家创建商品

    private Gson gson = new Gson();

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    void contextRefreshed() {
        SwitchNotifyEvent switchNotifyEvent = new SwitchNotifyEvent(Option.apply(aoXinPush)
                , Option.apply(financePush)
                , Option.apply(unifiedPayment)
                , Option.apply(revokeShopItemEditPrivilege)
                , Option.apply(foreignSms)
                , Option.apply(thirdPartyStockLimit)
                , Option.apply(allowCreateItem));
        applicationContext.publishEvent(switchNotifyEvent);
    }

    @EventListener(MethodSwitchNotify.class)
    void updateConfig(MethodSwitchNotify methodSwitchNotify) {
        String simpleName = FunctionSwitch.class.getSimpleName();
        if (!simpleName.equals(methodSwitchNotify.className())) return;
        for (Field declaredField : FunctionSwitch.class.getDeclaredFields()) {
            if (!declaredField.getName().equals(methodSwitchNotify.methodName())) continue;
            boolean access = declaredField.isAccessible();
            declaredField.setAccessible(true);
            Object value = gson.fromJson(methodSwitchNotify.methodVersion(), declaredField.getType());
            try {
                declaredField.set(this, value);
            } catch (Exception ex) {
                log.error("{} failed to update switch name:{} value:{}", LogUtil.getClassMethodName(), methodSwitchNotify.methodName(), methodSwitchNotify.methodVersion(), ex);
            }
            declaredField.setAccessible(access);
            return;
        }
    }
}
