package moonstone.web.core.config;

import moonstone.common.utils.XML;
import org.springframework.boot.context.config.ConfigDataResource;
import org.springframework.core.io.Resource;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class XMLConfigDataResource extends ConfigDataResource {
    Resource resource;
    Source reference;

    Map<String, List<XML>> xmlResource = new HashMap<>();

    public XMLConfigDataResource(Resource resource, Source reference) {
        this.resource = resource;
        this.reference = reference;
    }

    record Source(String profile,
                  String location,
                  String directory) {
    }

    public static class XMLPropertySource extends org.springframework.core.env.PropertySource<XML> {
        XML source;

        public XMLPropertySource(String name, XML source) {
            super(name, source);
            this.source = (XML) source;
        }

        @Override
        public String getName() {
            return source.name();
        }

        @Override
        public XML getSource() {
            return source;
        }

        @Override
        public boolean containsProperty(String name) {
            var attr = source.getAttribute(name);
            if (attr == null) {
                return null == source.getElement(name);
            }
            return true;
        }

        @Override
        public boolean equals(Object other) {
            if (other instanceof XML) {
                return source.equals(other);
            }
            if (other instanceof XMLPropertySource xml) {
                source.equals(xml.source);
            }
            return false;
        }

        @Override
        public int hashCode() {
            return source.hashCode();
        }

        @Override
        public String toString() {
            return source.toString();
        }

        @Override
        public Object getProperty(String name) {
            // split the name and get the data
            String[] nsPart = name.split("\\.");
            String current = name;
            int place = 0;
            XML node = source;
            while (node != null) {
                if (node.getAttribute(name.substring(place)) != null) {
                    return node.getAttribute(name.substring(place));
                }
                List<Object> data = new LinkedList<>();
                for (XML xml : node.getElement(name.substring(place))) {
                    if (null != xml.getAttribute("type")) {
                        try {
                            Class<?> type = Class.forName(xml.getAttribute("type"));
                            data.add(xml.toJava(type));
                        } catch (Exception e) {
                            logger.warn("Fail to convert the XML -> Java, " + xml.getAttribute("type"));
                        }
                    }else {
                        data.add(xml.value());
                    }
                }
            }
            return source.getAttribute(name);
        }
    }
}
