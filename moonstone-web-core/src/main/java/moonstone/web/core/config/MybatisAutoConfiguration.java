package moonstone.web.core.config;

import moonstone.web.core.RecordSupportFactory;
import moonstone.web.core.component.SpringBootVFS;
import moonstone.web.core.component.TypeAliasPackageParser;
import moonstone.web.core.hack.trap.PostgresqlLanguageConverterDriver;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({MybatisProperties.class})
@AutoConfigureAfter({DataSourceAutoConfiguration.class})
public class MybatisAutoConfiguration {
    private final DatabaseIdProvider databaseIdProvider;
    private final MybatisProperties mybatisProperties;
    private final DataSourceProperties dataSourceProperties;
    @Autowired(required = false)
    private List<Interceptor> interceptors;

    enum DriverType {
        /**
         * sql 驱动类型
         */
        postgres
    }

    @Autowired
    public MybatisAutoConfiguration(MybatisProperties mybatisProperties, ObjectProvider<DatabaseIdProvider> databaseIdProvider, DataSourceProperties dataSourceProperties) {
        this.databaseIdProvider = databaseIdProvider.getIfAvailable();
        this.mybatisProperties = mybatisProperties;
        this.dataSourceProperties = dataSourceProperties;
    }

    @Bean
    @ConditionalOnMissingBean({PlatformTransactionManager.class})
    public DataSourceTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        if (dataSourceProperties.getDriverClassName().contains(DriverType.postgres.name())) {
            configuration.getLanguageRegistry().setDefaultDriverClass(PostgresqlLanguageConverterDriver.class);
        }
        configuration.setObjectFactory(new RecordSupportFactory());
        configuration.setReflectorFactory(new RecordSupportReflectFactory());
        factory.setConfiguration(configuration);

        factory.setDataSource(dataSource);
        factory.setVfs(SpringBootVFS.class);
        if (!ObjectUtils.isEmpty(this.interceptors)) {
            factory.setPlugins(this.interceptors.toArray(new Interceptor[0]));
        }

        if (this.databaseIdProvider != null) {
            factory.setDatabaseIdProvider(this.databaseIdProvider);
        }

        if (StringUtils.hasLength(this.mybatisProperties.getTypeAliasesPackage())) {
            factory.setTypeAliasesPackage(TypeAliasPackageParser.flatPackageNames(this.mybatisProperties.getTypeAliasesPackage()));
        }

        if (StringUtils.hasLength(this.mybatisProperties.getTypeHandlersPackage())) {
            factory.setTypeHandlersPackage(this.mybatisProperties.getTypeHandlersPackage());
        }

        Resource[] mapperLocations = this.mybatisProperties.resolveMapperLocations();
        if (!ObjectUtils.isEmpty(mapperLocations)) {
            factory.setMapperLocations(mapperLocations);
        }

        return factory.getObject();
    }

    @Bean
    public SqlSessionTemplate sqlSessionTemplate(SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory, this.mybatisProperties.getExecutorType());
    }
}
