package moonstone.web.core.config;

import moonstone.common.model.image.slice.ImageViewBackend;
import moonstone.web.core.constants.ParanaConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Configuration
public class ImageBackendConfig {
    @Autowired
    private ParanaConfig paranaConfig;

    @Value("${image.base.url:http://dante-img.oss-cn-hangzhou.aliyuncs.com}")
    private String ossImageUrl;

    /**
     * 用于层次穿透, 在App层上的层次透传
     */
    @PostConstruct
    public void constructImageBackendUrl() {
        ImageViewBackend.App.imageUrlBackEnd(paranaConfig.getAppUrlBackend());
        ImageViewBackend.Aolipai.imageUrlBackEnd("http://www.aolipet.com").shopId(62L);
        ImageViewBackend.OSS.imageUrlBackEnd(ossImageUrl);
    }
}
