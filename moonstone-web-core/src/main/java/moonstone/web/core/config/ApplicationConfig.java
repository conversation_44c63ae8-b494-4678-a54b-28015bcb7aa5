package moonstone.web.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

/**
 * Author:  CaiZhy
 * Date:    2019/2/27
 */
@Data
@Configuration
public class ApplicationConfig implements Serializable {
    private static final long serialVersionUID = -6635179923694794458L;

    @Value("${application.name}")
    private String name;

    public Boolean isSupplyApplication() {
        return this.name.contains("supply-shop");
    }
}
