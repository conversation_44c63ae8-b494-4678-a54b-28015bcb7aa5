package moonstone.web.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author:  CaiZhy
 * Date:    2019/1/21
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "msg.email")
public class MsgEmailConfig implements Serializable {
    private static final long serialVersionUID = -2833652760785327878L;

    private List<MsgEmailYamlTemplate> templates;

    private Map<String, MsgEmailYamlTemplate> templateMap;

    @PostConstruct
    public void init(){
        Map<String, MsgEmailYamlTemplate> templateMap = new HashMap<>();
        for (MsgEmailYamlTemplate template : this.templates){
            templateMap.put(template.getKey(), template);
        }
        this.templateMap = templateMap;
    }
}
