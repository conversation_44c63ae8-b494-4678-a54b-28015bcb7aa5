/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.converters;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.GsonHttpMessageConverter;
import org.springframework.stereotype.Component;
import scala.Product;

import javax.annotation.PostConstruct;
import java.io.Reader;
import java.io.Writer;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.lang.reflect.WildcardType;
import java.util.TimeZone;
import java.util.stream.Stream;

/**
 * 默认先使用GSON,如果有数据异常 自动滚回Jackson, 并且添加log
 *
 * <AUTHOR>
 */
@Component
public class ParanaJsonMessageConverter extends GsonHttpMessageConverter { // MappingJackson2HttpMessageConverter {

    ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void initObjectMapper() {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setTimeZone(TimeZone.getDefault());
    }

    boolean isScalaObject(Type type) {
        if (type == null) {
            return false;
        }
        if (type instanceof ParameterizedType) {
            return isScalaObject(((ParameterizedType) type).getRawType()) || Stream.of(((ParameterizedType) type).getActualTypeArguments()).anyMatch(this::isScalaObject);
        }
        if (type instanceof Class) {
            return Product.class.isAssignableFrom((Class<?>) type);
        }
        if (type instanceof WildcardType) {
            return Stream.of(((WildcardType) type).getLowerBounds()).anyMatch(this::isScalaObject)
                    || Stream.of(((WildcardType) type).getUpperBounds()).anyMatch(this::isScalaObject);
        }
        if (type instanceof TypeVariable) {
            return Stream.of(((TypeVariable<?>) type).getBounds()).anyMatch(this::isScalaObject);
        }
        return false;
    }

    @Override
    public boolean canRead(Class<?> clazz, MediaType mediaType) {
        return super.canRead(clazz, mediaType);
    }

    @Override
    protected void writeInternal(Object object, Type type, Writer writer) throws Exception {
        if (object instanceof Response) {
            if (((Response<?>) object).isSuccess()) {
                object = ((Response<?>) object).getResult();
                type = ((ParameterizedType) type).getActualTypeArguments()[0];
            } else {
                throw new JsonResponseException(500, ((Response<?>) object).getError());
            }
        }
        boolean scalaObject = isScalaObject(type) || (object != null && isScalaObject(object.getClass()));
        if (scalaObject) {
            super.writeInternal(object, null, writer);
        } else {
            writer.write(objectMapper.writeValueAsString(object));
        }
    }



    /*
    public Object read(Type type, Class<?> contextClass, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        if (isScalaObject(type)) {
            return super.readInternal(type, contextClass, inputMessage);
        }
        if (MediaType.APPLICATION_FORM_URLENCODED.includes(inputMessage.getHeaders().getContentType())) {
            Map<String, String> form = readForm(inputMessage.getBody());
            return objectMapper.convertValue(form, objectMapper.constructType(type));
        }
        return objectMapper.readValue(inputMessage.getBody(), objectMapper.constructType(type));
    }
     */

    @Override
    protected Object readInternal(Type type, Reader reader) throws Exception {
        if (isScalaObject(type)) {
            return super.readInternal(type, reader);
        }
        return objectMapper.readValue(reader, objectMapper.constructType(type));
    }
}
