package moonstone.web.core.mirror.service;

import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.UserRole;
import moonstone.common.enums.UserType;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.*;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.weShop.service.WeShopShopAccountWriteService;
import moonstone.weShop.service.WeShopWriteService;
import moonstone.web.core.mirror.app.RemoteApiOfGongXiao;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.mirror.model.api.MirrorUserPersistService;
import moonstone.web.core.weShop.model.WeShopApplyForGx;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@AllArgsConstructor
@Slf4j
@Service
public class MirrorUserPersistServiceImplResultWrapped implements MirrorUserPersistService {
    UserWriteService<User> userUserWriteService;
    UserReadService<User> userUserReadService;
    UserProfileWriteService userProfileWriteService;
    UserProfileReadService userProfileReadService;
    ThirdPartyUserReadService thirdPartyUserReadService;
    ThirdPartyUserWriteService thirdPartyUserWriteService;
    WeShopShopAccountWriteService weShopShopAccountWriteService;
    WeShopShopAccountReadService weShopShopAccountReadService;
    WeShopReadService weShopReadService;
    WeShopWriteService weShopWriteService;
    ShopWxaProjectReadService shopWxaProjectReadService;
    ShopWxaReadService shopWxaReadService;
    SourceShopQuerySlice sourceShopQuerySlice;
    RemoteApiOfGongXiao remoteApiOfGongXiao;
    MongoTemplate mongoTemplate;

    @Override
    public Either<User> findUserByUserId(String userId, String source) {
        switch (MirrorSource.from(source)) {
            case GongXiao: {
                return thirdPartyUserReadService.findByTypeAndThirdPartyId(ThirdPartyUserType.OMS.getType(), userId).getResult()
                        .map(ThirdPartyUser::getUserId)
                        .map(userUserReadService::findById)
                        .map(Response::getResult)
                        .map(Either::ok)
                        .orElseGet(() -> Either.error(Translate.exceptionOf("不存在")));
            }
            case Unknown:
            default:
                return Either.error(Translate.exceptionOf("不支持"));
        }
    }

    @Override
    public Either<Boolean> updateUser(User update, UserProfile profileUpdate, String userId, String source) {
        switch (MirrorSource.from(source)) {
            case GongXiao: {
                if (update.getId() == null) {
                    return createUser(update.getName(), update.getMobile(), source)
                            .ifSuccess(user -> EventSender.publish(new UserUpdateEvent(user.getId())))
                            .flatMap(user -> linkUser(user.getId(), userId, source))
                            ;
                }
                userUserWriteService.update(update);
                UserProfile profile = userProfileReadService.findProfileByUserId(profileUpdate.getUserId()).getResult();
                if (profile != null) {
                    profileUpdate.setId(profile.getId());
                    userProfileWriteService.updateProfile(profileUpdate);
                } else {
                    userProfileWriteService.createProfile(profileUpdate);
                }
                return Either.ok(true);
            }
            case Unknown:
            default:
                return Either.error(Translate.exceptionOf("不支持"));
        }
    }

    @Override
    public Either<Boolean> linkUser(Long id, String userId, String source) {
        switch (MirrorSource.from(source)) {
            case GongXiao: {
                return thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), id)
                        .ifFail(() -> {
                            log.debug("{} create the user[{}, account => {}]", LogUtil.getClassMethodName(), id, userId);
                            ThirdPartyUser account = new ThirdPartyUser();
                            account.setUserId(id);
                            account.setThirdPartyId(userId);
                            account.setType(ThirdPartyUserType.OMS.getType());
                            thirdPartyUserWriteService.create(account);
                            return account;
                        })
                        .map(account -> account.getThirdPartyId().equals(userId));
            }
            case Unknown:
            default:
                return Either.error(Translate.exceptionOf("不支持"));
        }
    }

    @Override
    public Either<User> createUser(String name, String mobile, String source) {
        switch (MirrorSource.from(source)) {
            case GongXiao:
                User exists = userUserReadService.findByMobile(mobile).getResult();
                if (exists != null) {
                    return Either.ok(exists);
                }
                User createOne = new User();
                createOne.setType(UserType.NORMAL.value());
                createOne.setName(name + userUserReadService.countAll().take());
                createOne.setMobile(mobile);
                createOne.setStatus(1);
                createOne.setRoles(Collections.singletonList(UserRole.BUYER.name()));
                userUserWriteService.create(createOne);
                UserProfile userProfile = new UserProfile();
                userProfile.setRealName(name);
                userProfile.setUserId(createOne.getId());
                userProfileWriteService.createProfile(userProfile);
                return Either.ok(createOne);
            case Unknown:
            default:
                return Either.error(Translate.exceptionOf("不支持"));
        }
    }

    @Override
    public Either<User> findUserByMobile(String mobile, String source) {
        switch (MirrorSource.from(source)) {
            case GongXiao:
                return Either.ok(userUserReadService.findByMobile(mobile).getResult());
            case Unknown:
            default:
                return Either.error(Translate.exceptionOf("不支持"));
        }
    }

    @Override
    public void syncShop(String userCode, Long userId, String source) {
        try {
            Long projectId = sourceShopQuerySlice.queryProjectIdByShopIdAndSource(sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, source).take(), source).take();
            remoteApiOfGongXiao.queryGxShop(userCode)
                    .ifSuccess(gxShop -> {
                        // copy the data into our dto
                        gxShop.setId(gxShop.getShopId());
                        WeShopApplyForGx weShopApplyForGx = new WeShopApplyForGx();
                        weShopApplyForGx.setMobile(userUserReadService.findById(userId).getResult().getMobile());
                        weShopApplyForGx.setName(gxShop.getShopName());
                        weShopApplyForGx.setRealName(gxShop.getStoreName());
                        weShopApplyForGx.setIdCardImageFrontUrl(gxShop.getIdImageMain());
                        weShopApplyForGx.setIdCardImageBackUrl(gxShop.getIdImageVice());
                        weShopApplyForGx.setBusinessLicenseUrl(gxShop.getBusinessLicence());
                        switch (gxShop.getAuditType()) {
                            case 1:
                            case 3:
                            case 4:
                                weShopApplyForGx.setType(1);
                                break;
                            default:
                                weShopApplyForGx.setType(2);
                        }
                        weShopApplyForGx.setProjectId(projectId);
                        weShopApplyForGx.setUserId(userId);
                        weShopApplyForGx.setReason(gxShop.getRemarks());
                        // copy the area
                        BeanUtils.copyProperties(gxShop, weShopApplyForGx);
                        weShopApplyForGx.setStatus(gxShop.getUserStatus());
                        // update the display status of weShop
                        mongoTemplate.updateMulti(Query.query(Criteria.where("userId").is(userId))
                                        .addCriteria(Criteria.where("projectId").is(projectId))
                                , Update.update("status", -99)
                                , WeShopApplyForGx.class
                        );
                        mongoTemplate.insert(weShopApplyForGx);
                        // wrap the data
                        WeShop weShop = new WeShop();
                        BeanUtils.copyProperties(weShopApplyForGx, weShop);
                        weShop.setOutShopCode(gxShop.getIdCode());
                        weShop.setUserId(userId);
                        weShop.setName(weShopApplyForGx.getName());
                        if (ObjectUtils.isEmpty(weShop.getName())) {
                            Optional.ofNullable(userProfileReadService.findProfileByUserId(userId).getResult())
                                    .map(UserProfile::getRealName).ifPresent(weShop::setName);
                        }
                        weShop.setCreatedAt(null);
                        Optional.ofNullable(gxShop.getId())
                                .map(Objects::toString).ifPresent(weShop::setOutShopCode);
                        // create or update weShop
                        Long shopWxaId = shopWxaProjectReadService.findById(projectId).getResult().getShopWxaId();
                        Long shopId = shopWxaReadService.findById(shopWxaId).getResult().getShopId();
                        WeShopShopAccount account = weShopShopAccountReadService.findByUserId(userId).getResult()
                                .stream().filter(existsAccount -> existsAccount.getShopId().equals(shopId))
                                .findFirst().orElseGet(() -> {
                                    WeShopShopAccount weShopShopAccount = new WeShopShopAccount();
                                    weShopShopAccount.setUserId(userId);
                                    weShopShopAccount.setShopId(shopId);
                                    weShopShopAccount.setStatus(1);
                                    return weShopShopAccount;
                                });
                        if (account.getWeShopId() == null) {
                            // 同步过来的用户 认为是已经完成认证了
                            weShop.setStatus(1);
                            weShopWriteService.create(weShop);
                            account.setWeShopId(weShop.getId());
                            weShopShopAccountWriteService.create(account);
                        } else {
                            // 跳过用户审核状态复写
                            weShop.setStatus(null);
                            weShop.setId(account.getWeShopId());
                            weShopWriteService.update(weShop);
                        }
                    });
        } catch (Exception ex) {
            log.error("{} fail to sync the shop from GongXiao System", LogUtil.getClassMethodName(), ex);
        }
    }
}
