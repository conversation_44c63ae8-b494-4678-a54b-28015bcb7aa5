package moonstone.web.core.mirror.app;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.utils.Translate;
import moonstone.item.model.Sku;
import moonstone.item.model.WeShopSkuExtra;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.weShop.service.WeShopSkuWriteService;
import moonstone.web.core.mirror.model.MirrorSource;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Slf4j
@Component
public class GongXiaoAppItemInvocation {
    private final RemoteApiOfGongXiao remoteApiOfGongXiao;
    private final SourceShopQuerySlice sourceShopQuerySlice;
    private final ThirdPartyUserReadService thirdPartyUserReadService;
    private final WeShopCacheHolder weShopCacheHolder;
    private final WeShopSkuReadService weShopSkuReadService;
    private final WeShopSkuWriteService weShopSkuWriteService;

    /**
     * hack 同步商品更新到供销
     *
     * @param weShopId    店铺Id
     * @param sku         商品Id
     * @param weShopSkuId 子商品Id
     */
    public void createGongXiaoShopItem(Long weShopId, Sku sku, Long weShopSkuId) {
        sourceShopQuerySlice.queryProjectIdByShopIdAndSource(sku.getShopId(), MirrorSource.GongXiao.name())
                .map(project -> weShopCacheHolder.findByWeShopId(weShopId)
                        .map(weShop -> remoteApiOfGongXiao.choose(thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), weShop.getUserId()).getResult().getThirdPartyId(), sku.getSkuCode(), weShop.getOutShopCode()).elseThrow(() -> Translate.exceptionOf("供销平台上架失败")))
                        .orElseThrow(() -> Translate.exceptionOf("店铺查询失败")))
                .ifSuccess(outerSkuId -> {
                    WeShopSku weShopSku = weShopSkuReadService.findById(weShopSkuId).getResult();
                    weShopSku.getExtra().put(WeShopSkuExtra.OutSkuId.name(), outerSkuId);
                    weShopSkuWriteService.update(weShopSku);
                });
    }
}
