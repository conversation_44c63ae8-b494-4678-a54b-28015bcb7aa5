package moonstone.web.core.mirror.app.impl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@AllArgsConstructor
public class SourceShopQuerySliceApp implements SourceShopQuerySlice {
    MongoTemplate mongoTemplate;

    @Override
    public Either<Long> queryShopIdByOutShopCodeAndSource(String outShopCode, String source) {
        try {
            List<SourceShop> sourceShopList = mongoTemplate.find(Query.query(Criteria.where("source").is(source)), SourceShop.class);
            if (sourceShopList.isEmpty()) {
                return Either.error(Translate.exceptionOf("Source[%s]尚未被配置", source));
            }
            if (sourceShopList.size() == 1) {
                return Either.ok(sourceShopList.get(0).getShopId());
            }
            return Either.ok(sourceShopList.stream().filter(sourceShop -> sourceShop.getOutShopCode().equals(outShopCode))
                    .findFirst().map(SourceShop::getShopId).orElseGet(sourceShopList.get(0)::getShopId));
        } catch (Exception e) {
            log.error("{} fail to query shopId from OutShopId[{}] and Source[{}]", LogUtil.getClassMethodName(), outShopCode, source, e);
            return Either.error(Translate.exceptionOf("查询店铺失败"));
        }
    }

    @Override
    public Either<Long> queryProjectIdByShopIdAndSource(Long shopId, String source) {
        try {
            return Optional.ofNullable(mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)), SourceShop.class))
                    .filter(pair -> Objects.equals(source, pair.getSource()))
                    .map(SourceShop::getProjectId)
                    .map(Either::ok)
                    .orElseGet(() -> Either.error(Translate.exceptionOf("店铺[%s]未配置对应[%s]信息", shopId, source)));
        } catch (Exception e) {
            log.error("{} fail to query shop source[{}] from shop id [{}]", LogUtil.getClassMethodName(), source, shopId, e);
            return Either.error(Translate.exceptionOf("查询项目来源失败"));
        }
    }

    @Override
    public Either<String> queryShopSourceByProjectId(Long projectId) {
        try {
            return Optional.ofNullable(mongoTemplate.findOne(Query.query(Criteria.where("projectId").is(projectId)), SourceShop.class))
                    .map(SourceShop::getSource)
                    .map(Either::ok)
                    .orElseGet(() -> Either.error(Translate.exceptionOf("项目[%s]未配置信息", projectId)));
        } catch (Exception e) {
            log.error("{} fail to query shop source from project id [{}]", LogUtil.getClassMethodName(), projectId, e);
            return Either.error(Translate.exceptionOf("查询项目来源失败"));
        }
    }

    @Override
    public Either<Boolean> setSource(Long shopId, String source, Long projectId) {
        mongoTemplate.upsert(Query.query(Criteria.where("source").is(source)),
                Update.update("projectId", projectId).set("shopId", shopId), SourceShop.class);
        return Either.ok(true);
    }

    @Data
    public static class SourceShop {
        @Id
        String id;
        String outShopCode;
        String source;
        Long shopId;
        Long projectId;
    }
}
