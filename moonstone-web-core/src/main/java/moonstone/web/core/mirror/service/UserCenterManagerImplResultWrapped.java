package moonstone.web.core.mirror.service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.web.core.component.OmsApiGate;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.mirror.model.UserDetail;
import moonstone.web.core.mirror.model.api.UserCenterManager;
import moonstone.web.core.mirror.model.dto.Res;
import moonstone.web.core.mirror.model.dto.user.center.UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
public class UserCenterManagerImplResultWrapped implements UserCenterManager {
    @Autowired
    OmsApiGate omsApiGate;

    @Override
    public Either<UserDetail> findUserByUserId(String userId) {
        String url = omsApiGate.getApiGate() + OmsApiGate.Api.queryUserById.getUrl() + "?userId=" + userId;
        HttpRequest request = HttpRequest.get(url);
        log.debug("{} invoke with {}", LogUtil.getClassMethodName(), url);
        if (request.ok()) {
            String body = request.body();
            log.debug("{} result[{}]", LogUtil.getClassMethodName(), body);
            try {
                Res<UserInfo> userOfUserCenterRes = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Res<UserInfo>>() {
                }));
                if (!userOfUserCenterRes.isSuccess()) {
                    return Either.error(userOfUserCenterRes.getErrorMessage());
                }
                UserInfo userInfo = userOfUserCenterRes.getResult();
                UserDetail userDetail = new UserDetail();
                userDetail.setUserId(userId);
                userDetail.setName(Optional.ofNullable(userInfo.getNickName())
                        .filter(StringUtils::hasLength).orElseGet(userInfo::getUserName));
                userDetail.setMobile(userInfo.getMobile());
                /*
                 * 对状态进行特殊转换
                 */
                userDetail.setStatus(userInfo.getStatus() > 0 ? null : userInfo.getStatus());
                return Either.ok(userDetail);
            } catch (Exception e) {
                Res<Object> userOfUserCenterRes = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Res<Object>>() {
                }));
                if (!userOfUserCenterRes.isSuccess()) {
                    return Either.error(userOfUserCenterRes.getErrorMessage());
                }
                return Either.error(Optional.ofNullable(userOfUserCenterRes.getResult()).map(Objects::toString).orElse(null));
            }
        }
        return Either.error(Translate.exceptionOf("API 调用失败, code[%s]", request.code()));

    }

    @Override
    public Either<UserDetail> findUserByMobile(String mobile) {
        String url = omsApiGate.getApiGate() + OmsApiGate.Api.queryUserBy.getUrl() + "?mobile=" + mobile;
        HttpRequest request = HttpRequest.get(url);
        log.debug("{} invoke with {}", LogUtil.getClassMethodName(), url);
        if (request.ok()) {
            String body = request.body();
            log.debug("{} result[{}]", LogUtil.getClassMethodName(), body);
            try {
                Res<UserInfo> userOfUserCenterRes = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Res<UserInfo>>() {
                }));
                if (!userOfUserCenterRes.isSuccess()) {
                    return Either.error(userOfUserCenterRes.getErrorMessage());
                }
                if (!Objects.equals(userOfUserCenterRes.getResult().getStatus(), 1)) {
                    return Either.error(Translate.exceptionOf("用户状态不正常, 请联系客服"));
                }
                UserInfo userInfo = userOfUserCenterRes.getResult();
                UserDetail userDetail = new UserDetail();
                userDetail.setUserId(userInfo.getId().toString());
                userDetail.setName(Optional.ofNullable(userInfo.getNickName())
                        .filter(StringUtils::hasLength).orElseGet(userInfo::getUserName));
                userDetail.setMobile(userInfo.getMobile());
                /*
                 * 对状态进行特殊转换
                 */
                userDetail.setStatus(userInfo.getStatus() > 0 ? null : userInfo.getStatus());
                return Either.ok(userDetail);
            } catch (Exception e) {
                Res<Object> errorRes = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Res<Object>>() {
                }));
                if (!errorRes.isSuccess()) {
                    return Either.error(errorRes.getErrorMessage());
                }
                return Either.error(Optional.ofNullable(errorRes.getResult()).map(Objects::toString).orElse(null));
            }
        }
        return Either.error(Translate.exceptionOf("API 调用失败, code[%s]", request.code()));
    }

    @Override
    public Either<String> register(String mobile) {
        String url = omsApiGate.getApiGate() + OmsApiGate.Api.register.getUrl();
        HttpRequest request = HttpRequest.post(url);
        Map<String, Object> arg = ImmutableMap.of("mobile", mobile, "type", 2, "systemCode", "GongXiaoApp");
        request.form(arg);
        String content = Json.toJson(arg);
        //request.send(content);
        log.debug("{} invoke with {} data {}", LogUtil.getClassMethodName(), url, content);
        if (request.ok()) {
            String body = request.body();
            log.debug("{} result[{}]", LogUtil.getClassMethodName(), body);
            try {
                Res<JSONObject> userOfUserCenterRes = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Res<JSONObject>>() {
                }));
                if (!userOfUserCenterRes.isSuccess()) {
                    return Either.error(userOfUserCenterRes.getErrorMessage());
                }
                String userId = userOfUserCenterRes.getResult().get("userId").toString();
                return Either.ok(userId);
            } catch (Exception e) {
                Res<Object> errorRes = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Res<Object>>() {
                }));
                if (!errorRes.isSuccess()) {
                    return Either.error(errorRes.getErrorMessage());
                }
                return Either.error(Optional.ofNullable(errorRes.getResult()).map(Objects::toString).orElse(null));
            }
        }
        return Either.error(Translate.exceptionOf("API 调用失败, code[%s]", request.code()));
    }
}
