package moonstone.web.core.mirror.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.web.core.mirror.model.api.DomainLifeCycle;
import moonstone.web.core.mirror.model.domain.ShopMirrorDomain;

@EqualsAndHashCode(callSuper = true)
@Data
@DomainLifeCycle(statusFactory = ShopMirrorDomain.ShopStatus.class)
public class ShopDetail extends MirrorDetail {
    String shopId;
    String userId;
    /**
     * 用户数据冗余
     */
    String mobile;

    String name;
    String accessCode;
    String secret;
    String reason;
}
