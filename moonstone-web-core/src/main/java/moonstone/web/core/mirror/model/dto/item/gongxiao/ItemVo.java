package moonstone.web.core.mirror.model.dto.item.gongxiao;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class ItemVo {
    List<String> backCategoryRelation;
    Long id;
    String name;
    String mainPic;
    List<String> subPics;
    Long shopId;
    String shopName;
    Long isBonded;
    String advertise;
    /**
     * 状态  -1 已删除，1 下架 2 上架
     */
    Long status;
    Long type;
    Long stockQuantity;
    Long saleQuantity;
    List<String> detail;
    OriginVo origin;
    List<SkuVo> skusVO;
    Long price;
    Long originPrice;
    List<CategoryVo> categoriesList;
    Long openWholesale;
    String openWholesaleStr;
    Long openValidityType;
    String openValidityTypeStr;
    Long lowestCount;

    @Data
    public static class SkuVo {
        Long skuId;
        // 销量
        Long sales;
        // 采购价
        Long originPrice;
        // 零售价
        Long price;
        String validityPeriod;
        String validityPeriodStr;
        Integer stockQuantity;
        // 逻辑仓编码
        String logicWareCode;
        // 实际仓编码
        String realWareCode;
        // 海关编码
        String hsCode;
        boolean expired;
    }

    @Data
    public static class CategoryVo {
        Long id;
        String name;
        String logo;
    }

    @Data
    public static class OriginVo {
        String origin;
        String originUrl;
    }

    @AllArgsConstructor
    @Getter
    public enum ItemStatus {
        DELETE(-1), OFF_SELL(1), ON_SELL(2), UNKNOWN(-99);
        int status;

        public static ItemStatus from(int status) {
            for (ItemStatus value : ItemStatus.values()) {
                if (value.getStatus() == status) {
                    return value;
                }
            }
            return UNKNOWN;
        }
    }
}