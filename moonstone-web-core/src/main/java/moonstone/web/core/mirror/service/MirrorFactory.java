package moonstone.web.core.mirror.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.mirror.model.domain.MirrorDomain;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;

@Component
@Slf4j
public class MirrorFactory {
    @Autowired
    ApplicationContext applicationContext;

    public <T extends MirrorDomain> T build(Class<T> mirrorTarget) {
        T mirrorDomain = null;
        try {
            // construct the object
            for (Constructor<?> constructor : mirrorTarget.getConstructors()) {
                if (constructor.getParameterCount() == 0) {
                    mirrorDomain = (T) constructor.newInstance();
                    break;
                }
                Object[] parameter = new Object[constructor.getParameterCount()];
                int i = 0;
                for (Class<?> parameterType : constructor.getParameterTypes()) {
                    if (applicationContext.getBeansOfType(parameterType).isEmpty()) {
                        i = -1;
                        break;
                    }
                    parameter[i++] = applicationContext.getBean(parameterType);
                }
                if (i < 0) {
                    continue;
                }
                mirrorDomain = (T) constructor.newInstance(parameter);
                break;
            }
            if (mirrorDomain == null) {
                return null;
            }
            // inject the bean
            for (Field field : mirrorTarget.getDeclaredFields()) {
                if (field.getType().isPrimitive()) {
                    continue;
                }
                boolean acc = field.isAccessible();
                field.setAccessible(true);
                try {
                    if (field.get(mirrorDomain) != null) {
                        continue;
                    }
                    if (applicationContext == null || applicationContext.getBeansOfType(field.getType()).isEmpty()) {
                        continue;
                    }
                    field.set(mirrorDomain, applicationContext.getBean(field.getType()));
                } finally {
                    field.setAccessible(acc);
                }
            }
        } catch (Exception e) {
            log.error("{} fail to create a instance for mirror[{}]", LogUtil.getClassMethodName(), mirrorTarget.getName(), e);
        }
        return mirrorDomain;
    }
}