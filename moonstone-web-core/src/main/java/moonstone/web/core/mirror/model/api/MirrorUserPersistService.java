package moonstone.web.core.mirror.model.api;

import moonstone.common.model.Either;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;

public interface MirrorUserPersistService {
    Either<User> findUserByUserId(String userId, String source);

    /**
     * update the user
     *
     * @param update        user
     * @param profileUpdate profile related to user
     * @param userId        the userId from source
     * @param source        source
     * @return update result
     */
    Either<Boolean> updateUser(User update, UserProfile profileUpdate, String userId, String source);

    Either<Boolean> linkUser(Long id, String userId, String source);

    /**
     * create user with name and mobile
     *
     * @param name   user name => userProfile
     * @param mobile mobile unique
     * @param source source
     * @return user
     * @see UserProfile#setRealName(String) name
     */
    Either<User> createUser(String name, String mobile, String source);

    Either<User> findUserByMobile(String mobile, String source);

    /**
     * sync the WeShop from GongXiao System
     *
     * @param userId userId
     */
    void syncShop(String userCode, Long userId, String source);
}
