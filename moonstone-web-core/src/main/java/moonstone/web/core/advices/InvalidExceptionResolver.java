/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.advices;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.common.utils.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-20
 */
@Slf4j
@Component
public class InvalidExceptionResolver implements CustomExceptionHandler<InvalidException> {

    private final MessageSource messageSource;

    @Autowired
    public InvalidExceptionResolver(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @ExceptionHandler(value = {InvalidException.class})
    public void handleResolver(InvalidException e, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String path = request.getRequestURI();
        //处理app异常
        if (EmptyUtils.isNotEmpty(path) && path.startsWith("/api/app/")) {
            Locale locale = LocaleContextHolder.getLocale();
            log.debug("InvalidException happened,locale={}, cause={}", locale, Throwables.getStackTraceAsString(e));
            String message = e.getMessage();
            try {
                message = messageSource.getMessage(e.getError(), null, e.getMessage(), locale);
                if(EmptyUtils.isNotEmpty(message)) {
                    message = MessageFormat.format(message, e.getParams());
                }
            } catch (Exception ex) {
                log.warn("translate json response exception message fail, message = {}, cause {}", e.getMessage(), Throwables.getStackTraceAsString(ex));
            }
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/json; charset=utf-8");
            PrintWriter writer = response.getWriter();
            Map<String, Object> map = new HashMap<>();
            map.put("code", -1);
            map.put("errorMsg", EmptyUtils.isEmpty(message)?"系统异常":message);
            writer.write(JSONUtil.toJsonStr(map));
            //处理小程序新接口异常
        } else if (EmptyUtils.isNotEmpty(path) && path.startsWith("/api/new")) {
            Locale locale = LocaleContextHolder.getLocale();
            log.debug("InvalidException happened,locale={}, cause={}", locale, Throwables.getStackTraceAsString(e));
            String message = e.getMessage();
            try {
                message = messageSource.getMessage(e.getError(), null, e.getMessage(), locale);
                if(EmptyUtils.isNotEmpty(message)) {
                    message = MessageFormat.format(message, e.getParams());
                }
            } catch (Exception ex) {
                log.warn("translate json response exception message fail, message = {}, cause {}", e.getMessage(), Throwables.getStackTraceAsString(ex));
            }
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/json; charset=utf-8");
            PrintWriter writer = response.getWriter();
            Map<String, Object> map = new HashMap<>();
            map.put("code", -1);
            map.put("msg", EmptyUtils.isEmpty(message)?"系统异常":message);
            writer.write(JSONUtil.toJsonStr(map));
        } else {
            log.debug("InvalidException happened, cause={}", Throwables.getStackTraceAsString(e));
            Locale locale = LocaleContextHolder.getLocale();
            String message = e.getMessage();
            try {
                message = messageSource.getMessage(e.getMessage(), null, e.getMessage(), locale);
            } catch (Exception ex) {
                log.warn("translate json response exception message fail, message = {}, cause {}", e.getMessage(), Throwables.getStackTraceAsString(ex));
            }
            response.sendError(e.getStatus(), message);
        }
    }
}
