package moonstone.web.core.express.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.io.BaseEncoding;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.JsonMapper;
import io.terminus.msg.common.StringUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.EmptyUtils;
import moonstone.common.utils.MD5Util;
import moonstone.common.utils.Translate;
import moonstone.order.api.ExpressTrackInfo;
import moonstone.order.model.*;
import moonstone.order.service.ShipmentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.SubStore;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.web.core.constants.ThirdPartyExpressUtils;
import moonstone.web.core.express.component.ExpressService;
import moonstone.web.core.express.dto.ExpressTrack;
import moonstone.web.core.express.dto.OrderExpressTrack;
import moonstone.web.core.express.service.OrderExpressDetailService;
import moonstone.web.core.user.application.IdentitySpecifyController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Author:cp
 * Created on 5/30/16.
 */
@Slf4j
@Component
public class OrderExpress implements OrderExpressDetailService {

    @Resource
    ShipmentReadService shipmentReadService;

    @Resource
    ShopOrderReadService shopOrderReadService;

    @Resource
    SkuOrderReadService skuOrderReadService;

    @Resource
    ThirdPartyUserShopReadService thirdPartyUserShopReadService;

    @Autowired(required = false)
    ExpressService expressService;

    @Value("${cache.duration.in.minutes: 5}")
    Integer duration;

    @Value("${cache.mongo.duration.in.minutes: 30}")
    Integer mongoDuration;

    @Value("${Y800.open.api.gate}")
    String y800YangSupport;

    @Value("${Y800.partnerCode}")
    String y800PartnerCode;

    @Value("${Y800.partnerKey}")
    String y800PartnerKey;

    @Autowired
    ThirdPartyExpressUtils thirdPartyExpressUtils;

    @Autowired
    IdentitySpecifyController identitySpecifyController;

    @Autowired
    MongoTemplate mongoTemplate;

    LoadingCache<Map<String, String>, ExpressTrack> expressTrackCache;

    /**
     * combine the query the shipment of the complex order
     *
     * @param orderId order-id
     * @return the express track
     */
    @Override
    public List<? extends ExpressTrackInfo> queryOrderExpressDetail(Long orderId) {
        // query if the order-shipment exists
        log.info("从原始订单物流轨迹中获取");
        var exists = shipmentReadService.findByOrderIdAndOrderLevel(orderId, OrderLevel.SHOP).getResult();
        if (exists == null || exists.isEmpty()) {
            var skuOrders = skuOrderReadService.findByShopOrderId(orderId).getResult();
            List<ExpressTrackInfo> info = new ArrayList<>();
            for (SkuOrder skuOrder : skuOrders) {
                info.addAll(findExpressTrack(skuOrder.getId(), OrderLevel.SKU.getValue()));
            }
            return info;
        }
        return findExpressTrack(orderId, OrderLevel.SHOP.getValue());
    }

    @Override
    public boolean queryAble(Long orderId) {
        // now it always works
        return true;
    }

    @Override
    public boolean confirmAble(List<? extends ExpressTrackInfo> orderExpressDetails) {
        for (ExpressTrackInfo orderExpressDetail : orderExpressDetails) {
            if (orderExpressDetail.state() != null) {
                var state = orderExpressDetail.state();
                if (state.contains("退签")) {
                    return false;
                }
                if (state.contains("已签收") || state.contains("完成取件")) {
                    return true;
                }
            }

            var shipmentDetail = orderExpressDetail.toString();
            if (shipmentDetail.contains("退件") || shipmentDetail.contains("退回") || shipmentDetail.contains("异常件")) {
                return false;
            }
            if (shipmentDetail.contains("已签收") || shipmentDetail.contains("完成取件")) {
                return true;
            }
            if ((shipmentDetail.contains("签收人") || shipmentDetail.contains("完成") ||
                    shipmentDetail.contains("取件")) && (shipmentDetail.contains("派件") ||
                    shipmentDetail.contains("派送") || shipmentDetail.contains("投递"))) {
                return true;
            }
        }

        return false;
    }

    @PostConstruct
    public void init() {
        expressTrackCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .build(codeAndMailNoPair -> {
                    if (expressService == null) {
                        ExpressTrack expressTrack = new ExpressTrack();
                        expressTrack.setSteps(Collections.emptyList());
                        return expressTrack;
                    }

                    Map.Entry<String, String> entry = codeAndMailNoPair.entrySet().iterator().next();
                    String expressCompanyCode = entry.getKey();
                    String mailNo = entry.getValue();
                    //从mongodb读取物流信息
                    Query query = new Query(Criteria.where("mailNo").is(mailNo)).addCriteria(Criteria.where("companyCode").is(expressCompanyCode));
                    ExpressTrack mongoExpressTrack = mongoTemplate.findOne(query, ExpressTrack.class);
                    // 若存在且最近一次拉取时间在mongodb缓存范围内，则直接返回此物流信息；
                    if (null != mongoExpressTrack &&
                            EmptyUtils.isNotEmpty(mongoExpressTrack.getLastFetchTime()) &&
                            Duration.between(mongoExpressTrack.getLastFetchTime(), Instant.now()).toMinutes() < mongoDuration) {
                        return mongoExpressTrack;
                    }
                    // 否则调用快递100接口重新拉取物流信息，并将物流信息和最近拉取时间更新到mongodb
                    ExpressTrack expressTrack = expressService.fetchExpressTrack(expressCompanyCode, mailNo);
                    if (null != mongoExpressTrack && null != expressTrack) {
                        Update update = new Update().set("state", expressTrack.getState()).set("steps", expressTrack.getSteps()).set("lastFetchTime", Instant.now());
                        mongoTemplate.updateFirst(query, update, ExpressTrack.class);
                        return expressTrack;
                    } else if (null != expressTrack) {
                        expressTrack.setCompanyCode(expressCompanyCode);
                        expressTrack.setMailNo(mailNo);
                        mongoTemplate.insert(expressTrack);
                        return expressTrack;
                    }
                    return new ExpressTrack();
                });
    }

    public List<OrderExpressTrack> backFindExpressTrack(Long orderId, Integer orderType) {
        return findExpressTrack(orderId, orderType);
    }

    public List<OrderExpressTrack> frontFindExpressTrack(CommonUser user, Long orderId, Integer orderType) {
        checkAuth(user, orderId, orderType);
        return findExpressTrack(orderId, orderType);
    }

    public List<SkuOrder> findSkuOrders(Long shipmentId) {
        Response<List<OrderShipment>> findOrderShipments = shipmentReadService.findOrderIdsByShipmentId(shipmentId);
        if (!findOrderShipments.isSuccess()) {
            log.error("fail to find orderShipments by shipmentId[{}],cause:{}",
                    shipmentId, findOrderShipments.getError());
            throw new JsonResponseException(findOrderShipments.getError());
        }
        List<OrderShipment> orderShipments = findOrderShipments.getResult();

        List<Long> orderIds = Lists.newArrayListWithCapacity(orderShipments.size());
        for (OrderShipment orderShipment : orderShipments) {
            orderIds.add(orderShipment.getOrderId());
        }

        OrderLevel orderLevel = orderShipments.get(0).getOrderLevel();
        switch (orderLevel) {
            case SHOP -> {
                Response<List<SkuOrder>> findByShopIds = skuOrderReadService.findByShopOrderIds(orderIds);
                if (!findByShopIds.isSuccess()) {
                    log.error("fail to find skuOrders by shopIds:{},cause:{}",
                            orderIds, findByShopIds.getError());
                    throw new JsonResponseException(findByShopIds.getError());
                }
                return findByShopIds.getResult();
            }
            case SKU -> {
                Response<List<SkuOrder>> findByIds = skuOrderReadService.findByIds(orderIds);
                if (!findByIds.isSuccess()) {
                    log.error("fail to find skuOrders by ids:{},cause:{}",
                            orderIds, findByIds.getError());
                    throw new JsonResponseException(findByIds.getError());
                }
                return findByIds.getResult();
            }
            default -> throw new JsonResponseException("unknown.order.type");
        }

    }

    private List<OrderExpressTrack> findExpressTrack(Long orderId, Integer orderType) {
        OrderLevel orderLevel = OrderLevel.fromInt(MoreObjects.firstNonNull(orderType, 1));

        Response<List<Shipment>> shipmentResp = shipmentReadService.findByOrderIdAndOrderLevel(orderId, orderLevel);
        if (!shipmentResp.isSuccess()) {
            log.error("fail to find shipment by order id={} and order lever={},cause:{}",
                    orderId, orderLevel, shipmentResp.getError());
            throw new ServiceException(shipmentResp.getError());
        }
        List<Shipment> shipments = shipmentResp.getResult();

        //如果根据主单查询不到,则根据子单再查一把
        if (orderLevel == OrderLevel.SHOP && CollectionUtils.isEmpty(shipments)) {
            Response<List<SkuOrder>> findSkuOrders = skuOrderReadService.findByShopOrderId(orderId);
            if (!findSkuOrders.isSuccess()) {
                log.error("fail to find skuOrders by shopOrderId:{},cause:{}",
                        orderId, findSkuOrders.getError());
                throw new JsonResponseException(findSkuOrders.getError());
            }
            List<SkuOrder> skuOrders = findSkuOrders.getResult();

            List<Long> skuOrderIds = Lists.newArrayListWithCapacity(skuOrders.size());
            for (SkuOrder skuOrder : skuOrders) {
                skuOrderIds.add(skuOrder.getId());
            }
            Response<List<Shipment>> findSkuOrderShipments = shipmentReadService.findByOrderIdsAndOrderLevel(skuOrderIds, OrderLevel.SKU);
            if (!findSkuOrderShipments.isSuccess()) {
                log.error("fail to find shipments by orderIds:{},level:{},cause:{}",
                        skuOrderIds, OrderLevel.SKU, findSkuOrderShipments.getError());
                throw new JsonResponseException(findSkuOrderShipments.getError());
            }
            shipments = findSkuOrderShipments.getResult();
        }

        if (CollectionUtils.isEmpty(shipments)) {
            return Collections.emptyList();
        }

        List<OrderExpressTrack> orderExpressTracks = Lists.newArrayListWithCapacity(shipments.size());
        for (Shipment shipment : shipments) {
            OrderExpressTrack orderExpressTrack = new OrderExpressTrack();
            orderExpressTrack.setShipmentId(shipment.getId());
            orderExpressTrack.setShipmentCorpName(shipment.getShipmentCorpName());
            orderExpressTrack.setShipmentSerialNo(shipment.getShipmentSerialNo());
            orderExpressTrack.setOrderId(orderId);
            try {
                ExpressTrack expressTrack = expressTrackCache.get(ImmutableMap.of(shipment.getShipmentCorpCode(), shipment.getShipmentSerialNo()));
                orderExpressTrack.setState(Objects.requireNonNull(expressTrack).getState());
                orderExpressTrack.setOfficialQueryUrl(expressTrack.getOfficialQueryUrl());
                orderExpressTrack.setSteps(expressTrack.getSteps());
            } catch (Exception e) {
                log.error("fail to find order express track for order(id={}),order type:{},cause:{}",
                        orderId, orderType, Throwables.getStackTraceAsString(e));
            }
            orderExpressTracks.add(orderExpressTrack);
        }
        return orderExpressTracks;
    }

    private void checkAuth(CommonUser user, Long orderId, Integer orderType) {
        OrderLevel orderLevel = OrderLevel.fromInt(MoreObjects.firstNonNull(orderType, 1));
        OrderBase orderBase = null;
        switch (orderLevel) {
            case SHOP:
                Response<ShopOrder> shopOrderResp = shopOrderReadService.findById(orderId);
                if (!shopOrderResp.isSuccess()) {
                    log.error("fail to find shop  order by id:{},cause:{}", orderId, shopOrderResp.getError());
                    throw new ServiceException(shopOrderResp.getError());
                }
                orderBase = shopOrderResp.getResult();
                break;
            case SKU:
                Response<SkuOrder> skuOrderResp = skuOrderReadService.findById(orderId);
                if (!skuOrderResp.isSuccess()) {
                    log.error("fail to find sku order by id:{},cause:{}", orderId, skuOrderResp.getError());
                    throw new ServiceException(skuOrderResp.getError());
                }
                orderBase = skuOrderResp.getResult();
                break;
        }
        doCheck(user, orderBase);
    }

    private void doCheck(CommonUser user, OrderBase orderBase) {
        if (orderBase == null) {
            throw Translate.exceptionOf("订单不存在");
        }
        if (OrderOutFrom.SUB_STORE.Code().equals(orderBase.getOutFrom())) {
            if (java.util.Objects.equals(user.getId(), orderBase.getBuyerId())) {
                return;
            }
            IdentitySpecifyController.IdentityView identity = identitySpecifyController.getIdentity(orderBase.getShopId(), false);
            if (identity.getSubStoreOwner() != null && java.util.Objects.equals(((SubStore) identity.getSubStoreOwner()).getId() + "", orderBase.getOutShopId())) {
                return;
            }
            if (!(orderBase instanceof ShopOrder)) {
                ShopOrder shopOrder = shopOrderReadService.findById(((SkuOrder) orderBase).getOrderId()).getResult();
                if (shopOrder != null) {
                    doCheck(user, shopOrder);
                }
            }
        }
    }

    public Response<JSONObject> frontFindExpressFindWmsId(Long orderId, Integer orderType) {
        JSONObject json = new JSONObject();
        json.put("code", "fail");
        json.put("actionCode", "fail");
        json.put("errorMsg", "【查询物流系统正忙,请稍后再试,或登录物流官网进行自助查询】");
        try {
//        thirdPartyExpressUtils.getY800Utils().get("zhongtong");

            OrderLevel orderLevel = OrderLevel.fromInt(MoreObjects.firstNonNull(orderType, 1));

            Response<List<Shipment>> shipmentResp = shipmentReadService.findByOrderIdAndOrderLevel(orderId, orderLevel);
            if (!shipmentResp.isSuccess()) {
                log.error("frontFindExpressFindWmsId getExpress fail findByOrderIdAndOrderLevel id={} and order lever={},cause:{}",
                        orderId, orderLevel, shipmentResp.getError());
                return Response.ok(json);
            }
            List<Shipment> shipments = shipmentResp.getResult();
            Response<ShopOrder> findShopOrders = shopOrderReadService.findById(orderId);
            if (!findShopOrders.isSuccess() || findShopOrders.getResult() == null) {
                log.error("frontFindExpressFindWmsId getExpress fail findById id={} and order lever={},cause:{}",
                        orderId, orderLevel, shipmentResp.getError());
                return Response.ok(json);
            }
            if (!CollectionUtils.isEmpty(shipments)) {
                json.put("errorMsg", "【查询物流系统正忙，请稍后再试，或登录物流" + shipments.get(0).getShipmentCorpName() + "官网进行自助查询】");
            }
            //获取accessCode
            Response<List<ThirdPartyUserShop>> thirdPartyUserShop = thirdPartyUserShopReadService.findByShopId(findShopOrders.getResult().getShopId());
            if (!thirdPartyUserShop.isSuccess() || thirdPartyUserShop.getResult().isEmpty()) {
                log.error("frontFindExpressFindWmsId getExpress fail thirdPartyUserShop id={} and order lever={},cause:{}",
                        orderId, orderLevel, shipmentResp.getError());
                return Response.ok(json);
            }
            String accessCode = thirdPartyUserShop.getResult().get(0).getThirdPartyCode();
//        accessCode = thirdPartyExpressUtils.getY800Utils().get(accessCode);

            //如果根据主单查询不到,则根据子单再查一把
            if (CollectionUtils.isEmpty(shipments)) {
                Response<List<SkuOrder>> findSkuOrders = skuOrderReadService.findByShopOrderId(orderId);
                if (!findSkuOrders.isSuccess()) {
                    log.error("frontFindExpressFindWmsId getExpress fail findByShopOrderId shopOrderId:{},cause:{}",
                            orderId, findSkuOrders.getError());
                    return Response.ok(json);
                }
                List<SkuOrder> skuOrders = findSkuOrders.getResult();

                List<Long> skuOrderIds = Lists.newArrayListWithCapacity(skuOrders.size());
                for (SkuOrder skuOrder : skuOrders) {
                    skuOrderIds.add(skuOrder.getId());
                }
                Response<List<Shipment>> findSkuOrderShipments = shipmentReadService.findByOrderIdsAndOrderLevel(skuOrderIds, OrderLevel.SKU);
                if (!findSkuOrderShipments.isSuccess()) {
                    log.error("frontFindExpressFindWmsId getExpress fail findByOrderIdsAndOrderLevel orderIds:{},level:{},cause:{}",
                            skuOrderIds, OrderLevel.SKU, findSkuOrderShipments.getError());
                    return Response.ok(json);
                }
                if (!CollectionUtils.isEmpty(shipments)) {
                    json.put("errorMsg", "【查询物流系统正忙，请稍后再试，或登录物流" + findSkuOrderShipments.getResult().get(0).getShipmentCorpName() + "官网进行自助查询】");
                }
                shipments = findSkuOrderShipments.getResult();


            }

            if (CollectionUtils.isEmpty(shipments) || StringUtil.isBlank(shipments.get(0).getShipmentSerialNo())
                    || StringUtil.isBlank(shipments.get(0).getShipmentCorpCode())) {
                return Response.ok(json);
            }
            //去洋800获取物流信息--
            Either<JSONObject> expressInfo = getFindExpressFindWmsId(shipments.get(0).getShipmentSerialNo(), shipments.get(0).getShipmentCorpCode(), json, accessCode);
            return Response.ok(expressInfo.take());
        } catch (Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
            return Response.ok(json);
        }

    }

    //去洋800获取物流信息
    private Either<JSONObject> getFindExpressFindWmsId(String shipmentSerialNo, String shipmentCorpCode, JSONObject jsons, String accessCode) {
        JSONObject json = new JSONObject();
        json.put("expComCode", thirdPartyExpressUtils.getY800Utils().get(shipmentCorpCode));
        json.put("expNo", shipmentSerialNo);
        json.put("accessCode", accessCode);
        String jsonRawString = json.toString();
        log.info("frontFindExpressFindWmsId getExpress push Express begin, pushOrder:({})", jsonRawString);

        BaseEncoding baseEncoding = BaseEncoding.base64();
        String bizData = baseEncoding.encode(jsonRawString.getBytes());

        String partnerId = this.y800PartnerCode;
        String token = this.y800PartnerKey;

        String v = "2.0";

        String serviceName = "express.detail";
        String sb = "bizData=" + bizData +
                "partnerId=" + partnerId +
                "serviceName=" + serviceName +
                "v=" + v +
                token;
        String sign = MD5Util.MD5(sb);

        Map<String, Object> params = new HashMap<>();
        params.put("sign", sign);
        params.put("v", v);
        params.put("partnerId", partnerId);
        params.put("serviceName", serviceName);
        params.put("bizData", bizData);
        log.info("push params:{}", params);

        HttpRequest request = HttpRequest.post(y800YangSupport, params, true);
        if (request.ok()) {
            Map<String, Object> map = JsonMapper.nonDefaultMapper().fromJson(request.body(), JSONObject.class);
            log.debug("frontFindExpressFindWmsId getExpress push Express order yang800 map, parameter={}, result={}",
                    json.toJSONString(), JSON.toJSONString(map));
            if (map.get("code").toString().equals("success")) {
                return Either.ok(new JSONObject(map));
            } else {
                return Either.ok(jsons);
            }
        }
        return Either.ok(jsons);
    }
}
