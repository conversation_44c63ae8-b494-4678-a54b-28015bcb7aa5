package moonstone.web.core.express.service.impl;

import com.dt.tms.sdk.api.response.BatchQueryWaybillTrackResponse;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.api.ExpressTrackInfo;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Shipment;
import moonstone.order.service.ShipmentReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.express.component.TmsExpressService;
import moonstone.web.core.express.dto.ExpressTrack;
import moonstone.web.core.express.dto.TmsExpressTrack;
import moonstone.web.core.express.service.OrderExpressDetailService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Tms的物流轨迹查询服务
 */
@Slf4j
@Component
public class TmsOrderExpress implements OrderExpressDetailService {

    @Autowired
    private TmsExpressService tmsExpressService;

    @Resource
    private ShipmentReadService shipmentReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    public int order() {
        return 1;
    }

    @Override
    public boolean queryAble(Long shopOrderId) {
        var shipments = findShipment(shopOrderId);
        if (CollectionUtils.isEmpty(shipments)) {
            return false;
        }

        var supportedCodes = tmsExpressService.getSupportedExpressCode();
        if (CollectionUtils.isEmpty(supportedCodes)) {
            return false;
        }

        return shipments.stream()
                .map(Shipment::getOriginShipmentCorpCode)
                .filter(StringUtils::isNotBlank)
                .anyMatch(supportedCodes::contains);
    }

    @Override
    public boolean confirmAble(List<? extends ExpressTrackInfo> orderExpressDetails) {
        if (CollectionUtils.isEmpty(orderExpressDetails)) {
            return false;
        }

        var tmsTrackList = (List<TmsExpressTrack>) orderExpressDetails;

        return tmsTrackList.stream().map(TmsExpressTrack::getTrackList)
                .anyMatch(trackList -> trackList.stream()
                        .anyMatch(trackItem -> TmsExpressService.TMS_SCAN_TYPE_USER_SIGNED.contains(trackItem.getTmsScanType())));
    }

    @Override
    public List<? extends ExpressTrackInfo> queryOrderExpressDetail(Long shopOrderId) {
        log.info("从TMS当中获取物流轨迹信息");
        var shipments = findShipment(shopOrderId);
        if (CollectionUtils.isEmpty(shipments)) {
            return Collections.emptyList();
        }

        var tmsTrackList = tmsExpressService.findWaybillTrack(build(shipments));
        if (CollectionUtils.isEmpty(tmsTrackList)) {
            return null;
        }

        tmsTrackList = tmsTrackList.stream().filter(track -> !track.getInvalidExpress()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmsTrackList)) {
            return null;
        }

        return tmsTrackList.stream().map(e -> convert(e, shipments)).collect(Collectors.toList());
    }

    private List<Pair<String, String>> build(List<Shipment> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream()
                .map(this::build)
                .filter(subList -> !CollectionUtils.isEmpty(subList))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<Pair<String, String>> build(Shipment shipment) {
        if (shipment == null) {
            return Collections.emptyList();
        }

        var list = new ArrayList<Pair<String, String>>();
        list.add(Pair.of(shipment.getShipmentSerialNo(), shipment.getOriginShipmentCorpCode()));

        if ("ZTO".equals(shipment.getOriginShipmentCorpCode())) {
            list.add(Pair.of(shipment.getShipmentSerialNo(), "ZTOINTER"));
        }
        if ("ZTOINTER".equals(shipment.getOriginShipmentCorpCode())) {
            list.add(Pair.of(shipment.getShipmentSerialNo(), "ZTO"));
        }

        return list;
    }

    /**
     * 查询订单的物流发货信息
     *
     * @param shopOrderId
     * @return
     */
    private List<Shipment> findShipment(Long shopOrderId) {
        var shipments = shipmentReadService.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP).getResult();
        if (!CollectionUtils.isEmpty(shipments)) {
            return shipments;
        }

        var skuOrders = skuOrderReadService.findByShopOrderId(shopOrderId).getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            return Collections.emptyList();
        }

        return shipmentReadService.findByOrderIdsAndOrderLevel(
                skuOrders.stream().map(OrderBase::getId).collect(Collectors.toList()), OrderLevel.SKU).getResult();
    }

    private TmsExpressTrack convert(BatchQueryWaybillTrackResponse.WaybillTrack waybillTrack, List<Shipment> shipmentList) {
        TmsExpressTrack target = new TmsExpressTrack();

        var shipmentMap = buildMap(shipmentList);

        target.setOrderNo(waybillTrack.getOrderNo());
        target.setTmsExpressCode(waybillTrack.getTmsExpressCode());
        target.setWaybillNo(waybillTrack.getWaybillNo());

        if (!CollectionUtils.isEmpty(waybillTrack.getTrackList())) {
            target.setTrackList(waybillTrack.getTrackList().stream().map(source -> {
                TmsExpressTrack.TmsTrackItem item = new TmsExpressTrack.TmsTrackItem();

                item.setContent(source.getContent());
                item.setScanSite(source.getScanSite());
                item.setTime(new Date(source.getTime()));
                item.setTmsScanDesc(source.getTmsScanDesc());
                item.setTmsScanType(source.getTmsScanType());

                return item;
            }).collect(Collectors.toList()));
        }

        //兼容页面的快递信息展示
        var shipment = shipmentMap.get(target.getTmsExpressCode() + "-" + target.getWaybillNo());
        if (shipment != null) {
            target.setShipmentId(shipment.getId());
            target.setShipmentCorpName(shipment.getShipmentCorpName());
            target.setShipmentSerialNo(shipment.getShipmentSerialNo());
            target.setCompanyCode(shipment.getShipmentCorpCode());
        }
        target.setState(waybillTrack.getContent());
        target.setSteps(convert(waybillTrack.getTrackList()));

        return target;
    }

    private Map<String, Shipment> buildMap(List<Shipment> shipments) {
        if (CollectionUtils.isEmpty(shipments)) {
            return Collections.emptyMap();
        }

        var map = new HashMap<String, Shipment>();
        for (var shipment : shipments) {
            map.put(shipment.getOriginShipmentCorpCode() + "-" + shipment.getShipmentSerialNo(), shipment);

            if ("ZTO".equals(shipment.getOriginShipmentCorpCode())) {
                map.put("ZTOINTER" + "-" + shipment.getShipmentSerialNo(), shipment);
            }
            if ("ZTOINTER".equals(shipment.getOriginShipmentCorpCode())) {
                map.put("ZTO" + "-" + shipment.getShipmentSerialNo(), shipment);
            }
        }

        return map;
    }

    private List<ExpressTrack.ExpressStep> convert(List<BatchQueryWaybillTrackResponse.TrackItem> trackList) {
        if (CollectionUtils.isEmpty(trackList)) {
            return Collections.emptyList();
        }

        return trackList.stream()
                .map(source -> {
                    ExpressTrack.ExpressStep step = new ExpressTrack.ExpressStep();
                    step.setTime(new Date(source.getTime()));
                    step.setDesc(!StringUtils.isBlank(source.getContent()) ? source.getContent() : source.getTmsScanDesc());

                    return step;
                }).sorted(Comparator.comparing(ExpressTrack.ExpressStep::getTime).reversed())
                .collect(Collectors.toList());
    }
}
