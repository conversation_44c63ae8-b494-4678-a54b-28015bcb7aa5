package moonstone.web.core.express.component;

import moonstone.web.core.express.dto.ExpressTrack;
import org.springframework.util.StringUtils;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * Author:cp
 * Created on 5/30/16.
 */
public abstract class ExpressService {

    /**
     * 拉取物流流转信息
     *
     * @param expressCompanyCode 快递公司代码
     * @param mailNo             运单号
     * @return 物流流转信息
     */
    public ExpressTrack fetchExpressTrack(String expressCompanyCode, String mailNo) {
        checkInput(expressCompanyCode, mailNo);
        return fetch(expressCompanyCode, mailNo);
    }

    public void checkInput(String expressCompanyCode, String mailNo) {
        checkArgument(StringUtils.hasText(expressCompanyCode), "express.company.code.not.provided");
        checkArgument(StringUtils.hasText(mailNo), "mail.no.not.provided");
    }

    public abstract ExpressTrack fetch(String expressCompanyCode, String mailNo);

}
