package moonstone.web.core.settle.impl;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.Payment;
import moonstone.order.service.PaymentReadService;
import moonstone.settle.api.SettleFeeDetailCalculator;
import moonstone.settle.enums.CheckStatus;
import moonstone.settle.enums.TradeType;
import moonstone.settle.model.*;
import moonstone.settle.service.*;
import moonstone.web.core.settle.SettleUpdateService;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * DATE: 16/11/13 下午2:59 <br>
 * MAIL: zhang<PERSON><PERSON>@terminus.io <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@Component
public class SettleUpdateServiceImpl implements SettleUpdateService {

    @RpcConsumer
    protected SettlementWriteService settlementWriteService;

    @RpcConsumer
    protected SettleOrderDetailWriteService settleOrderDetailWriteService;

    @RpcConsumer
    protected PayChannelDetailWriteService payChannelDetailWriteService;

    @RpcConsumer
    protected SettleRefundOrderDetailWriteService refundOrderDetailWriteService;

    @RpcConsumer
    protected SettlementReadService settlementReadService;

    @RpcConsumer
    protected PaymentReadService paymentReadService;

    @RpcConsumer
    protected SettleOrderDetailReadService settleOrderDetailReadService;

    @RpcConsumer
    protected SettleRefundOrderDetailReadService refundOrderDetailReadService;

    @RpcConsumer
    protected PayChannelDetailReadService payChannelDetailReadService;

    @Override
    public void refreshPlatformCommission(Long shopOrderId) throws JsonResponseException {

    }

    @Override
    public void checkSettleDetails(PayTrans payTrans) throws JsonResponseException {
        if(payTrans.getTradeType().equals(TradeType.Pay.value())){
            checkPayChannelDetail(payTrans);
            checkPaymentSettlement(payTrans);
            checkOrderDetail(payTrans);
        }else{
            checkPayChannelDetail(payTrans);
            checkRefundSettlement(payTrans);
            checkRefundOrderDetail(payTrans);
        }
    }

    @Override
    public void checkPaymentSettlement(PayTrans payTrans){
        try {
            log.debug("check settlement for payment, payTrans={}", payTrans);

            Response<Settlement> rSettlement = settlementReadService.findSettlementByTradeNo(payTrans.getTradeNo());
            if(!rSettlement.isSuccess()) {
                log.error("findSettlementByTradeNo fail, tradeNo={}, cause={}", payTrans.getTradeNo(), rSettlement.getError());
                return;
            }
            Settlement toUpdate = new Settlement();
            toUpdate.setId(rSettlement.getResult().getId());
            toUpdate.setGatewayCommission(payTrans.getCommission());
            toUpdate.setChannelAccount(payTrans.getAccountNo());
            toUpdate.setCheckStatus(CheckStatus.CHECK_SUCCESS.value());
            toUpdate.setCheckFinishedAt(payTrans.getLoadAt());

            val rUpdate = settlementWriteService.updateSettlement(toUpdate);
            if(!rUpdate.isSuccess()){
                log.error("updateSettlement fail, toUpdate={}, cause={}", toUpdate, rUpdate.getError());
            }
        }catch (Exception e){
            log.error("check settlement for payment fail, payTrans={}, cause={}", payTrans, Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public void checkRefundSettlement(PayTrans payTrans){
        try {
            log.debug("check settlement for refund, payTrans={}", payTrans);

            Response<Settlement> rSettlement = settlementReadService.findSettlementByRefundNo(payTrans.getRefundNo());
            if(!rSettlement.isSuccess()) {
                log.error("findSettlementByRefundNo fail, refundNo={}, cause={}", payTrans.getRefundNo(), rSettlement.getError());
                return;
            }
            Settlement toUpdate = new Settlement();
            toUpdate.setId(rSettlement.getResult().getId());
            toUpdate.setChannelAccount(payTrans.getAccountNo());
            toUpdate.setCheckStatus(CheckStatus.CHECK_SUCCESS.value());
            toUpdate.setCheckFinishedAt(payTrans.getLoadAt());
            toUpdate.setGatewayCommission(payTrans.getCommission());
            val rUpdate = settlementWriteService.updateSettlement(toUpdate);
            if(!rUpdate.isSuccess()){
                log.error("updateSettlement fail, toUpdate={}, cause={}", toUpdate, rUpdate.getError());
            }

        }catch (Exception e){
            log.error("check settlement for refund fail, payTrans={}, cause={}", payTrans, Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public void checkOrderDetail(PayTrans payTrans) {
        try {
            log.debug("check SettleOrderDetail, payTrans={}", payTrans);

            Response<Payment> rPayment = paymentReadService.findByOutId(payTrans.getTradeNo());
            if(!rPayment.isSuccess()){
                log.error("findPaymentByOutId fail, tradeNo={}, cause={}", payTrans.getTradeNo(), rPayment.getError());
                return;
            }
            Payment payment = rPayment.getResult();

            Response<List<OrderPayment>> rOrderList = paymentReadService.findOrderIdsByPaymentId(payment.getId());
            if(!rOrderList.isSuccess()){
                log.error("findOrderIdsByPaymentId fail, paymentId={}, cause={}", payment.getId(), rOrderList.getError());
                return;
            }
            for(OrderPayment orderPayment : rOrderList.getResult()){
                Response<SettleOrderDetail> rOrderDetail =  settleOrderDetailReadService.findSettleOrderDetailByOrderId(orderPayment.getOrderId(),orderPayment.getOrderLevel().getValue());
                if(!rOrderDetail.isSuccess()){
                    log.error("findSettleOrderDetailByOrderId fail, orderId={}, cause={}", orderPayment.getOrderId(), rOrderDetail.getError());
                    continue;
                }
                SettleOrderDetail orderDetail = rOrderDetail.getResult();
                Long splitCommission = SettleFeeDetailCalculator.split(payTrans.getCommission(), payment.getFee(), orderDetail.getActualPayFee());

                SettleOrderDetail toUpdate = new SettleOrderDetail();
                toUpdate.setId(orderDetail.getId());
                toUpdate.setGatewayCommission(splitCommission);
                toUpdate.setChannelAccount(payTrans.getAccountNo());
                toUpdate.setCheckAt(payTrans.getLoadAt());
                toUpdate.setSellerReceivableFee(orderDetail.getActualPayFee()+orderDetail.getPlatformDiscount()-orderDetail.getPlatformCommission()-splitCommission);
                toUpdate.setCheckStatus(CheckStatus.CHECK_SUCCESS.value());

                //汇总的时间应该是对账时间的前一天, 这样才能在对账完成后,立马汇总刚对账的订单。
                toUpdate.setSumAt(new DateTime(payTrans.getLoadAt()).minusDays(1).toDate());

                val rUpdate = settleOrderDetailWriteService.updateSettleOrderDetail(toUpdate);
                if(!rUpdate.isSuccess()){
                    log.error("updateSettleOrderDetail fail, toUpdate={}, cause={}", toUpdate, rUpdate.getError());
                }
            }

        }catch (Exception e){
            log.error("check SettleOrderDetail fail, payTrans={}, cause={}", payTrans, Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public void checkPayChannelDetail(PayTrans payTrans) throws JsonResponseException {
        try{
            log.debug("check PayChannelDetail, payTrans={}", payTrans);

            PayChannelDetail payChannelDetail=null;
            if(payTrans.getRefundNo()!=null){ //退款对账
                Response<PayChannelDetail> rPayChannelDetail= payChannelDetailReadService.findPayChannelDetailByRefundNo(payTrans.getRefundNo());
                if(!rPayChannelDetail.isSuccess()){
                    log.error("findPayChannelDetailByRefundNo fail, payTrans={}, cause={}", payTrans, rPayChannelDetail.getError());
                    return;
                }
                payChannelDetail = rPayChannelDetail.getResult();
            }else{ //支付对账
                Response<PayChannelDetail> rPayChannelDetail= payChannelDetailReadService.findPayChannelDetailByTradeNo(payTrans.getTradeNo());
                if(!rPayChannelDetail.isSuccess()){
                    log.error("findPayChannelDetailByTradeNo fail, payTrans={}, cause={}", payTrans, rPayChannelDetail.getError());
                    return;
                }
                payChannelDetail = rPayChannelDetail.getResult();
            }

            if(payChannelDetail==null){
                log.error("payChannelDetail not exist, payTrans={}", payTrans);
                return;
            }

            PayChannelDetail toUpdate=new PayChannelDetail();
            toUpdate.setId(payChannelDetail.getId());
            toUpdate.setCheckStatus(CheckStatus.CHECK_SUCCESS.value());
            toUpdate.setCheckFinishedAt(new Date());
            toUpdate.setGatewayCommission(payTrans.getCommission());
            toUpdate.setGatewayRate(payTrans.getRate().intValue());
            toUpdate.setChannelAccount(payChannelDetail.getChannelAccount());

            /**
             * 计算实际收入: tradeFee-gatewayCommission
             */
            toUpdate.setActualIncomeFee(payChannelDetail.getTradeFee()-payTrans.getCommission());

            Response<Boolean> rUpdate = payChannelDetailWriteService.updatePayChannelDetail(toUpdate);
            if(!rUpdate.isSuccess()) {
                log.error("updatePayChannelDetail fail, toUpdate={}, cause={}", toUpdate, rUpdate.getError());
            }


        }catch (Exception e){
            log.error("check PayChannelDetail fail, payTrans={}, cause={}",
                    payTrans, Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public void checkRefundOrderDetail(PayTrans payTrans){
        try {
            log.debug("check SettleRefundOrderDetail, payTrans={}", payTrans);

            Response<SettleRefundOrderDetail> rRefund = refundOrderDetailReadService.findByRefundNo(payTrans.getRefundNo());
            if(rRefund.isSuccess()){
                SettleRefundOrderDetail exist = rRefund.getResult();

                SettleRefundOrderDetail toUpdate = new SettleRefundOrderDetail();
                toUpdate.setId(rRefund.getResult().getId());
                toUpdate.setChannelAccount(payTrans.getAccountNo());
                toUpdate.setCheckAt(payTrans.getLoadAt());

                toUpdate.setGatewayCommission(payTrans.getCommission());
                toUpdate.setSellerDeductFee(exist.getActualRefundFee()+exist.getPlatformDiscount()-exist.getPlatformCommission()-payTrans.getCommission());
                toUpdate.setCheckStatus(CheckStatus.CHECK_SUCCESS.value());

                //汇总的时间应该是对账时间的前一天, 这样才能在对账完成后,立马汇总刚对账的退款单。
                toUpdate.setSumAt(new DateTime(payTrans.getLoadAt()).minusDays(1).toDate());

                refundOrderDetailWriteService.updateSettleRefundOrderDetail(toUpdate);
            }
        }catch (Exception e){
            log.error("check SettleRefundOrderDetail fail, payTrans={}, cause={}", payTrans, Throwables.getStackTraceAsString(e));
        }
    }
}
