package moonstone.web.core.decoration.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WechatAppInfoDTO {
    /**
     * 企业名
     */
    String name;
    /**
     * 企业代码
     */
    String code;
    /**
     * 企业代码类型（1：统一社会信用代码， 2：组织机构代码，3：营业执照注册号）
     */
    @SerializedName("code_type")
    @JsonProperty("code_type")
    Integer codeType;
    /**
     * 法人微信
     */
    @SerializedName("legal_persona_wechat")
    @JsonProperty("legal_persona_wechat")
    String legalPersonaWechat;
    /**
     * 法人姓名
     */
    @SerializedName("legal_persona_name")
    @JsonProperty("legal_persona_name")
    String legalPersonaName;
    /**
     * 第三方联系电话
     */
    @SerializedName("component_phone")
    @JsonProperty("component_phone")
    String componentPhone;
}
