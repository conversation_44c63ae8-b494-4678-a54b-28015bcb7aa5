package moonstone.web.core.decoration.app;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import moonstone.common.model.Either;
import moonstone.common.utils.Json;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.decoration.model.IllegalRecordView;
import moonstone.web.core.decoration.model.WechatAppInfoDTO;
import moonstone.web.core.decoration.slice.WxResultWrapper;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;

@AllArgsConstructor
@Component
public class WeiXinApiApp {
    WxOpenParanaComponentService wxOpenParanaComponentService;
    ShopWxaCacheHolder shopWxaCacheHolder;

    /**
     * 修改域名
     *
     * @param projectId 项目Id
     * @return 成功
     */
    public Either<Boolean> modifyDomain(Long projectId) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            return WxResultWrapper.wrap(wxOpenParanaComponentService.modifyDomain(wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false)));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 获取体验版二维码
     *
     * @param projectId 项目Id
     * @param path      路径可为null
     * @return 二维码
     */
    public Either<BufferedImage> getPreviewQrCode(Long projectId, @Nullable String path) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            String accessCode = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            HttpRequest request = HttpRequest.get(String.format("https://api.weixin.qq.com/wxa/get_qrcode?access_token=%s&path=%s", accessCode, Optional.ofNullable(path).orElse("")));
            if (request.ok()) {
                ByteArrayOutputStream buff = new ByteArrayOutputStream();
                request.receive(buff);
                try {
                    return Either.ok(ImageIO.read(new ByteArrayInputStream(buff.toByteArray())));
                } catch (java.lang.Exception e) {
                    return WxResultWrapper.wrap(buff.toString(), null);
                }
            }
            return Either.error("HTTP ERROR CODE:" + request.code());
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    public Either<BufferedImage> getUnlimitedCode(Long projectId, String scene, String page) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            String accessCode = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            HttpRequest request = HttpRequest.post(String.format("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s"
                    , accessCode));
            request.contentType(HttpRequest.CONTENT_TYPE_JSON);
            request.send(Json.toJson(ImmutableMap.of("scene", scene, "page", page)));
            if (request.ok()) {
                ByteArrayOutputStream buff = new ByteArrayOutputStream();
                request.receive(buff);
                try {
                    return Either.ok(ImageIO.read(new ByteArrayInputStream(buff.toByteArray())));
                } catch (Exception e) {
                    return WxResultWrapper.wrap(buff.toString(), null);
                }
            }
            return Either.error(Translate.exceptionOf("HTTP CONNECTION ERROR CODE[%s]", request.code()));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 增加体验者
     *
     * @param projectId 项目Id
     * @param account   帐号
     * @return 回馈
     */
    public Either<Boolean> addExperienceUser(Long projectId, String account) {
        try {
            String apiUrl = "https://api.weixin.qq.com/wxa/bind_tester?access_token=";
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            String accessCode = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            HttpRequest request = HttpRequest.post(apiUrl + accessCode);
            request.contentType(HttpRequest.CONTENT_TYPE_JSON);
            request.send(Json.toJson(ImmutableMap.of("wechatid", account)));
            if (request.ok()) {
                return Either.ok(WxResultWrapper.wrap(request.body()).take());
            }
            return Either.error(Translate.exceptionOf("HTTP 错误 CODE[%S]", request.code()));
        } catch (Exception e) {
            if (e.getMessage().contains("user already bind")) {
                return Either.ok(true);
            }
            return Either.error(e);
        }
    }

    /**
     * 快速创建小程序
     *
     * @param wechatAppInfoDTO 小程序信息
     * @return 成功
     */
    public Either<Boolean> createWxMicroApp(WechatAppInfoDTO wechatAppInfoDTO) {
        Map<String, String> errorMsgA = ImmutableMap.of(
                "89251", "模板消息已下发，待法人人脸核身校验",
                "89252", "法人&企业信息一致性校验中",
                "89253", "缺少参数"
        );

        Map<String, String> errorMsgB = ImmutableMap.of(
                "86004", "微信号无效",
                "61070", "法人微信号不一致",
                "89254", "权限不足",
                "89249", "该主体已有任务执行中，距上次任务 24h 后再试"
        );
        Map<String, String> queryErrorMsgMap = new LinkedHashMap<>(7);
        queryErrorMsgMap.putAll(errorMsgA);
        queryErrorMsgMap.putAll(errorMsgB);
        try {
            String componentAccessToken = wxOpenParanaComponentService.getComponentAccessToken(false);
            String query = "https://api.weixin.qq.com/cgi-bin/component/fastregisterweapp?action=search&component_access_token=";
            HttpRequest queryRequest = HttpRequest.post(query + componentAccessToken);
            queryRequest.contentType(HttpRequest.CONTENT_TYPE_JSON);
            queryRequest.send(Json.toJson(wechatAppInfoDTO));
            if (!queryRequest.ok()) {
                return Either.error(Translate.exceptionOf("HTTP 错误 Code[%s]", queryRequest.code()));
            }
            JSONObject res = JSONObject.parseObject(queryRequest.body());
            if (Objects.equals(res.get(WxResultWrapper.ERR_CODE).toString(), "89250")) {
                String url = "https://api.weixin.qq.com/cgi-bin/component/fastregisterweapp?action=create&component_access_token=";
                HttpRequest request = HttpRequest.post(url + componentAccessToken);
                request.contentType(HttpRequest.CONTENT_TYPE_JSON);
                request.send(Json.toJson(wechatAppInfoDTO));
                if (!request.ok()) {
                    return Either.error(Translate.exceptionOf("HTTP 错误 Code[%s]", request.code()));
                }
                res = JSONObject.parseObject(request.body());
                if (Objects.equals(res.get(WxResultWrapper.ERR_CODE).toString(), WxResultWrapper.SUCCESS_CODE)) {
                    return Either.ok(true);
                }
            }
            return Either.error(queryErrorMsgMap.getOrDefault(res.get(WxResultWrapper.ERR_CODE).toString(), res.get(WxResultWrapper.ERR_MSG).toString()));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 查询违规记录
     *
     * @param projectId 项目Id
     * @return 记录
     */
    public Either<List<IllegalRecordView>> queryIllegalRecord(Long projectId) {
        try {
            String appId = shopWxaCacheHolder.findShopWxaByProjectId(projectId).getAppId();
            String url = "https://api.weixin.qq.com/wxa/getillegalrecords?access_token=" + wxOpenParanaComponentService.getAuthorizerAccessToken(appId, false);
            HttpRequest request = HttpRequest.post(url);
            if (!request.ok()) {
                return Either.error(Translate.exceptionOf("HTTP 错误 [%s]", request.code()));
            }
            return WxResultWrapper.wrap(request.body(), res -> Json.OBJECT_MAPPER.convertValue(res.get("records"), new TypeReference<List<IllegalRecordView>>() {
            }));
        } catch (Exception e) {
            return Either.error(e);
        }
    }
}
