package moonstone.web.core.decoration;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.wxa.model.WxaPage;
import moonstone.wxa.service.WxaPageReadService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by CaiZhy on 2018/11/20.
 */
@Slf4j
@RestController
@RequestMapping("/api/decoration/wxaPage")
public class CoreWxaPages {
    @RpcConsumer
    private WxaPageReadService wxaPageReadService;

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WxaPage findById(@PathVariable Long id){
        try{
            Response<WxaPage> response = wxaPageReadService.findById(id);
            if (!response.isSuccess()) {
                log.error("failed to find wxaPage by id={}, error code: {}", id, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to find wxaPage by id={}, cause: {}", id, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/findByTemplateId/{templateId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<WxaPage> findByTemplateId(@PathVariable Long templateId){
        try{
            Response<List<WxaPage>> response = wxaPageReadService.findByTemplateId(templateId);
            if (!response.isSuccess()) {
                log.error("failed to find wxaPages by templateId={}, error code: {}", templateId, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to find wxaPages by templateId={}, cause: {}", templateId, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/findByTemplateIdAndPath/{templateId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WxaPage findByTemplateIdAndPath(@PathVariable Long templateId, @RequestParam String path){
        try{
            Response<WxaPage> response = wxaPageReadService.findByTemplateIdAndPath(templateId, path);
            if (!response.isSuccess()) {
                log.error("failed to find wxaPage by templateId={}, path={}, error code: {}", templateId, path, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to find wxaPage by templateId={}, path={}, cause: {}", templateId, path, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }
}
