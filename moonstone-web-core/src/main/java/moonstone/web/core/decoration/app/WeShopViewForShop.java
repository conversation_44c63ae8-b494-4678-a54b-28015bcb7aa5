package moonstone.web.core.decoration.app;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.order.dto.view.ProfitView;
import moonstone.weShop.dto.WeShopApplyView;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

public interface WeShopViewForShop extends WeShopApplyView {
    /**
     * @return 余额
     */
    Long getBalance();

    /**
     * @return 待收益
     */
    Long getForeseeProfit();

    /**
     * 构筑店铺级微店显示层
     *
     * @param weShopApplyView 微店显示层
     * @param profitViewList  利润列表
     * @return 带有利润的显示层
     */
    static WeShopViewForShop build(WeShopApplyView weShopApplyView, List<ProfitView> profitViewList) {
        WeShopViewForShopImpl weShopViewForShop = new WeShopViewForShopImpl();
        BeanUtils.copyProperties(weShopApplyView, weShopViewForShop);
        if (Objects.isNull(profitViewList)) return weShopViewForShop;
        for (ProfitView view : profitViewList) {
            if (view.isPresent()) {
                weShopViewForShop.setBalance(view.getProfit());
            } else {
                weShopViewForShop.setForeseeProfit(view.getProfit());
            }
        }
        return weShopViewForShop;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class WeShopViewForShopImpl extends WeShopApplyViewImpl implements WeShopViewForShop {
        Long balance;
        Long foreseeProfit;
    }
}
