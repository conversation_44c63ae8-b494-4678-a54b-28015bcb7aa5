package moonstone.web.core.decoration;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.wxa.model.WxaComponent;
import moonstone.wxa.service.WxaComponentReadService;
import moonstone.wxa.service.WxaComponentWriteService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by CaiZhy on 2018/11/13.
 */
@Slf4j
@RestController
@RequestMapping("/api/decoration/wxaComponent")
public class CoreWxaComponents {
    @RpcConsumer
    private WxaComponentReadService wxaComponentReadService;

    @RpcConsumer
    private WxaComponentWriteService wxaComponentWriteService;

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WxaComponent findById(@PathVariable Long id){
        try{
            Response<WxaComponent> response = wxaComponentReadService.findById(id);
            if (!response.isSuccess()) {
                log.error("failed to find wxaComponent by id={}, error code: {}", id, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to find wxaComponent by id={}, cause: {}", id, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/findByTemplateId/{templateId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<WxaComponent> findByTemplateId(@PathVariable Long templateId){
        try{
            Response<List<WxaComponent>> response = wxaComponentReadService.findByTemplateId(templateId);
            if (!response.isSuccess()) {
                log.error("failed to find wxaComponents by templateId={}, error code: {}", templateId, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to find wxaComponents by templateId={}, cause: {}", templateId, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }
}
