package moonstone.web.core.decoration.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableBiMap;
import lombok.AllArgsConstructor;
import moonstone.common.model.Either;
import moonstone.common.utils.Json;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.decoration.model.DraftView;
import moonstone.web.core.decoration.model.TemplateView;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@AllArgsConstructor
@Component
public class WeiXinTemplateApp {
    WxOpenParanaComponentService wxOpenParanaComponentService;
    ShopWxaCacheHolder shopWxaCacheHolder;
    private static final String ERROR_CODE = "errcode";
    private static final String ERROR_MSG = "errmsg";
    private static final String SUCCESS_CODE = "0";

    /**
     * 将草稿加入到模板列表中
     *
     * @param projectId 项目Id
     * @param draftId   草稿Id
     * @return 成功与否
     */
    public Either<Boolean> addDraftIntoTemplate(Long projectId, String draftId) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            String accessCode = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            HttpRequest request = HttpRequest.post("https://api.weixin.qq.com/wxa/addtotemplate?access_token=" + accessCode);
            request.contentType(HttpRequest.CONTENT_TYPE_JSON);
            request.send(Json.toJson(ImmutableBiMap.of("draft_id", draftId)));
            if (request.ok()) {
                Map<String, Object> res = Json.parseObject(request.body(), new TypeReference<Map<String, Object>>() {
                });
                if (SUCCESS_CODE.equals(Objects.requireNonNull(res).get(ERROR_CODE).toString())) {
                    return Either.ok(true);
                }
                return Either.error(res.get(ERROR_MSG).toString());
            }
            return Either.error("HTTP ERROR CODE:" + request.code());
        } catch (Exception e) {
            return Either.error(e);
        }
    }


    /**
     * 查询草稿列表
     *
     * @param projectId 项目Id
     * @return 草稿列表
     */
    public Either<List<DraftView>> queryDraftList(Long projectId) {
        try {
            String accessToken = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxaCacheHolder.findShopWxaByProjectId(projectId).getAppId(), false);
            String url = "https://api.weixin.qq.com/wxa/gettemplatedraftlist?access_token=" + accessToken;
            HttpRequest request = HttpRequest.get(url);
            if (request.ok()) {
                Map<String, Object> res = Json.parseObject(request.body(), new TypeReference<Map<String, Object>>() {
                });
                if (SUCCESS_CODE.equals(Objects.requireNonNull(res).get(ERROR_CODE).toString())) {
                    return Either.ok(Json.OBJECT_MAPPER.convertValue(res.get("draft_list"), new TypeReference<List<DraftView>>() {
                    }));
                }
                return Either.error(Translate.exceptionOf(res.get(ERROR_MSG).toString()));
            }
            return Either.error(Translate.exceptionOf("HTTP错误 CODE [%s]", request.code()));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 查询模板列表
     *
     * @param projectId 项目Id
     * @return 模板列表
     */
    public Either<List<TemplateView>> queryTemplateList(Long projectId) {
        try {
            String accessToken = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxaCacheHolder.findShopWxaByProjectId(projectId).getAppId(), false);
            String url = "https://api.weixin.qq.com/wxa/gettemplatelist?access_token=" + accessToken;
            HttpRequest request = HttpRequest.get(url);
            if (request.ok()) {
                Map<String, Object> res = Json.parseObject(request.body(), new TypeReference<Map<String, Object>>() {
                });
                if (SUCCESS_CODE.equals(Objects.requireNonNull(res).get(ERROR_CODE).toString())) {
                    return Either.ok(Json.OBJECT_MAPPER.convertValue(res.get("template_list"), new TypeReference<List<TemplateView>>() {
                    }));
                }
                return Either.error(res.get(ERROR_MSG).toString());
            }
            return Either.error(Translate.exceptionOf("HTTP错误 CODE [%s]", request.code()));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    public Either<Boolean> deleteTemplate(Long projectId, Long templateId) {
        try {
            String accessToken = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxaCacheHolder.findShopWxaByProjectId(projectId).getAppId(), false);
            String url = "https://api.weixin.qq.com/wxa/deletetemplate?access_token=" + accessToken;
            HttpRequest request = HttpRequest.post(url);
            request.contentType(HttpRequest.CONTENT_TYPE_JSON);
            request.send(Json.toJson(ImmutableBiMap.of("template_id", templateId)));
            if (request.ok()) {
                Map<String, Object> res = Json.parseObject(request.body(), new TypeReference<Map<String, Object>>() {
                });
                if (SUCCESS_CODE.equals(Objects.requireNonNull(res).get(ERROR_CODE).toString())) {
                    return Either.ok(true);
                }
                return Either.error(Translate.exceptionOf(res.get(ERROR_MSG).toString()));
            }
            return Either.error(Translate.exceptionOf("HTTP错误 CODE [%s]", request.code()));
        } catch (Exception e) {
            return Either.error(e);
        }
    }
}
