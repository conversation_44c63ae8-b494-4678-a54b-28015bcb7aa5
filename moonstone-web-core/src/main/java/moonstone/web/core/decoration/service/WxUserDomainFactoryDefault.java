package moonstone.web.core.decoration.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserWriteService;
import moonstone.web.core.decoration.api.AlipayLoginApi;
import moonstone.web.core.decoration.api.WxUserDomainFactory;
import moonstone.web.core.decoration.model.WxUserDomain;
import moonstone.wxOpen.service.WxOpenLoginApi;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class WxUserDomainFactoryDefault implements WxUserDomainFactory {
    private final UserWriteService<User> userWriteService;
    private final WxOpenLoginApi wxOpenLoginApi;

    private final ShopWxaProjectReadService shopWxaProjectReadService;

    private final AlipayLogin<PERSON><PERSON> alipayLoginApi;

    @Override
    public WxUserDomain create(String wxCode, Long projectId) {
        return new WxUserDomain(projectId, wxCode, wxOpenLoginApi, userWriteService, shopWxaProjectReadService,
                alipayLoginApi);
    }
}
