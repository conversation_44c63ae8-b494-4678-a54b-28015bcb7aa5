package moonstone.web.core.decoration.app;

import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.dto.ViewShopWxa;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaProjectWriteService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.shopWxa.service.ShopWxaWriteService;
import moonstone.web.core.events.shop.ShopWxaProjectUpdateEvent;
import moonstone.web.core.shop.events.WechatAuditFailEvent;
import moonstone.web.core.shop.events.WechatAuditSuccessEvent;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
@Slf4j
public class ShopWxaSimpleManager {
    ShopCacheHolder shopCacheHolder;
    ShopWxaReadService shopWxaReadService;
    ShopWxaWriteService shopWxaWriteService;
    ShopWxaProjectWriteService shopWxaProjectWriteService;
    ShopWxaProjectReadService shopWxaProjectReadService;

    WeiXinMPVersionApp weiXinMPVersionApp;

    /**
     * 查询小程序项目
     *
     * @param pageNo   页
     * @param pageSize 页大小
     * @return 结果
     */
    public Either<Paging<ViewShopWxa>> list(Long shopId, List<Integer> statuses, int pageNo, int pageSize) {
        List<ViewShopWxa> viewShopWxaList = new LinkedList<>();
        Paging<ShopWxa> shopWxaPaging = shopWxaReadService.findBy(null, shopId, null, null, statuses, null, pageNo, pageSize).getResult();
        List<Future<Void>> done = new LinkedList<>();
        for (ShopWxa shopWxa : shopWxaPaging.getData()) {
            ViewShopWxa viewShopWxa = ViewShopWxa.convert(shopWxa);
            shopWxaProjectReadService.findByShopWxaId(viewShopWxa.getId())
                    .getResult().stream().filter(validate -> validate.getStatus() != -1)
                    .findFirst()
                    .ifPresent(viewShopWxa::setProject);
            if (Optional.ofNullable(viewShopWxa.getProject()).map(ShopWxaProject::getStatus).orElse(1) > 1 && shopWxa.getStatus() == 3) {
                done.add(CompletableFuture.runAsync(() ->
                        Either.ok(viewShopWxa.getProject()).map(ShopWxaProject::getId)
                                .flatMap(weiXinMPVersionApp::getLastAuthResult)
                                // set pass
                                .ifSuccess(viewShopWxa::setLastAudit)
                                // set fail reason
                                .logErrorStr(viewShopWxa::setReason)
                                // send pass event
                                .ifSuccess(passed -> Optional.of(passed).filter(Predicate.isEqual(true)).ifPresent(p -> EventSender.sendApplicationEvent(new WechatAuditSuccessEvent(shopWxa.getId()))))
                                // send fail event
                                .logErrorStr(reason -> EventSender.sendApplicationEvent(new WechatAuditFailEvent(shopWxa.getId(), reason)))
                ));
            }
            Optional.ofNullable(viewShopWxa.getProject())
                    .map(ShopWxaProject::getExtra)
                    .map(extra -> extra.get("auditId"))
                    .ifPresent(viewShopWxa::setAuditId);
            viewShopWxaList.add(viewShopWxa);
        }
        try {
            for (Future<Void> voidFuture : done) {
                voidFuture.get(2, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error("{} fail to query the app reason", LogUtil.getClassMethodName(), e);
        }
        Paging<ViewShopWxa> paging = new Paging<>(shopWxaPaging.getTotal(), viewShopWxaList);
        return Either.ok(paging);
    }

    /**
     * 创建项目
     *
     * @param shopWxaId 店铺Id
     * @return 项目Id
     */
    public Either<Long> createProject(Long userId, String name, Long shopWxaId) {
        try {
            if (shopWxaProjectReadService.findByShopWxaId(shopWxaId).getResult().isEmpty()) {
                ShopWxa shopWxa = shopWxaReadService.findById(shopWxaId).getResult();
                // update the shopWxa shopId for it's create by system auto
                if (shopWxa.getShopId() == null || shopWxa.getName() == null) {
                    long shopId = Optional.ofNullable(shopWxa.getShopId()).orElseGet(() -> shopCacheHolder.findShopByUserId(userId).getId());
                    ShopWxa updateShopId = new ShopWxa();
                    updateShopId.setName(name);
                    updateShopId.setId(shopWxaId);
                    updateShopId.setShopId(shopId);
                    shopWxaWriteService.update(updateShopId);
                }
                ShopWxaProject project = new ShopWxaProject();
                project.setName(name);
                project.setShopWxaId(shopWxaId);
                project.setDescription("");
                project.setStatus(1);
                shopWxaProjectWriteService.create(project);
                EventSender.sendApplicationEvent(new ShopWxaProjectUpdateEvent(project.getId()));
                return Either.ok(project.getId());
            }
            return Either.error(Translate.exceptionOf("已经存在相关项目"));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    public Either<Integer> deleteShopWxa(String appId, Long[] ids) {
        try {

            Integer deleted = 0;
            if (appId != null) {
                ShopWxa shopWxa = shopWxaReadService.findByAppId(appId).getResult();
                while (shopWxa != null) {
                    shopWxaWriteService.delete(shopWxa.getId());
                    shopWxa = shopWxaReadService.findByAppId(appId).getResult();
                    deleted++;
                }
            }
            if (ids != null) {
                for (Long id : ids) {
                    shopWxaWriteService.delete(id);
                    deleted++;
                }
            }
            return Either.ok(deleted);
        } catch (Exception e) {
            return Either.error(e);
        }
    }
}
