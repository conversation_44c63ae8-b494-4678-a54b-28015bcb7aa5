package moonstone.web.core.decoration.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableBiMap;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.open.bean.message.WxOpenMaSubmitAuditMessage;
import me.chanjar.weixin.open.bean.result.WxOpenMaSubmitAuditResult;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.enums.ShopWxaProjectStatus;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaProjectWriteService;
import moonstone.shopWxa.service.ShopWxaWriteService;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.decoration.slice.WxResultWrapper;
import moonstone.web.core.shop.events.WechatAuditApplyEvent;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import moonstone.wxOpen.service.WxOpenParanaMaService;
import moonstone.wxa.model.WxaTemplate;
import moonstone.wxa.service.WxaTemplateReadService;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * 微信小程序版本管理器
 */
@Component
@Slf4j
@AllArgsConstructor
public class WeiXinMPVersionApp {
    WxOpenParanaComponentService wxOpenParanaComponentService;
    WxOpenParanaMaService wxOpenParanaMaService;
    ShopWxaProjectReadService shopWxaProjectReadService;
    ShopWxaProjectWriteService shopWxaProjectWriteService;
    WxaTemplateReadService wxaTemplateReadService;

    ShopWxaWriteService shopWxaWriteService;
    ShopWxaCacheHolder shopWxaCacheHolder;
    static final Gson GSON = new Gson();

    /**
     * 回滚代码版本
     *
     * @param projectId 项目Id
     * @return 回滚结果
     */
    public Either<Boolean> revertVersion(Long projectId) {
        ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
        try {
            String apiUrl = "https://api.weixin.qq.com/wxa/revertcoderelease?access_token=" + wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            HttpRequest request = HttpRequest.get(apiUrl);
            if (request.ok()) {
                return WxResultWrapper.wrap(request.body());
            }
            return Either.error("fail to invoke revert api for Network Error : " + request.code());
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 提交代码
     *
     * @param projectId   项目Id
     * @param templateId  项目使用的模板Id
     * @param userVersion 版本
     * @param userDesc    描述
     * @param extInfo     额外数据
     * @return 操作成功
     */
    public Either<Boolean> commitCode(Long projectId, Long templateId, String userVersion, String userDesc, Map<String, Object> extInfo) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            ShopWxaProject updateTemplateId = new ShopWxaProject();
            JsonObject ext = GSON.fromJson(GSON.toJson(extInfo), JsonObject.class);
            WxaTemplate template = wxaTemplateReadService.findById(templateId).getResult();
            if (template == null) {
                return Either.error(Translate.of("模板[Id => %s] 查找失败", templateId));
            }
            JsonObject templateExt = Optional.ofNullable(template.getExtJson())
                    .map(extJson -> GSON.fromJson(extJson, JsonObject.class))
                    .orElseGet(JsonObject::new);
            templateExt.addProperty("shopWxaProjectId", projectId);
            templateExt.addProperty("shopId", shopWxa.getShopId());
            if (ext.get("ext") == null || ext.get("ext").isJsonNull()) {
                ext.add("ext", templateExt);
            } else {
                JsonElement element = ext.get("ext");
                if (element.isJsonObject()) {
                    JsonObject container = element.getAsJsonObject();
                    for (Map.Entry<String, JsonElement> pair : templateExt.entrySet()) {
                        if (container.get(pair.getKey()) == null) {
                            container.add(pair.getKey(), pair.getValue());
                        }
                    }
                }
            }
            ext.addProperty("extAppid", shopWxa.getAppId());
            updateTemplateId.setId(projectId);
            updateTemplateId.setTemplateId(templateId);
            updateTemplateId.setStatus(ShopWxaProjectStatus.UPLOADED.getValue());
            shopWxaProjectWriteService.update(updateTemplateId);
            // update status
            shopWxaWriteService.updateStatus(shopWxa.getId(), ShopWxaStatus.WAITING_SUBMIT.getValue());
            EventSender.sendApplicationEvent(new WechatAuditApplyEvent(projectId, shopWxa.getId(), templateId));
            return WxResultWrapper.wrap(wxOpenParanaMaService.codeCommit(template.getTemplateId(), userVersion, userDesc, ext.toString(), shopWxa.getAppId()));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 提交审核
     *
     * @param projectId 项目Id
     * @return 调用结果
     */
    public Either<Boolean> submitAuth(Long projectId, WxOpenMaSubmitAuditMessage submitAuditMessage) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            WxOpenMaSubmitAuditResult result = wxOpenParanaMaService.submitAudit(submitAuditMessage, shopWxa.getAppId());
            if ("0".equals(result.getErrcode())) {
                ShopWxaProject shopWxaProject = shopWxaProjectReadService.findById(projectId).getResult();
                shopWxaProject.setExtra(Optional.ofNullable(shopWxaProject.getExtra()).orElseGet(HashMap::new));
                shopWxaProject.getExtra().put("authId", result.getAuditId().toString());
                shopWxaProjectWriteService.update(shopWxaProject);
                shopWxaWriteService.updateStatus(shopWxa.getId(), ShopWxaStatus.WAITING_AUDIT.getValue());
                ShopWxa removeAuthReason = new ShopWxa();
                removeAuthReason.setId(shopWxa.getId());
                removeAuthReason.setAuditFailReason("");
                shopWxaWriteService.update(removeAuthReason);
                return Either.ok(true);
            }
            return Either.error(result.getErrmsg());
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 部署, 将已经审核通过的代码部署至线上
     *
     * @param projectId 项目Id
     * @return 部署成功
     */
    public Either<Boolean> deploy(Long projectId) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            return WxResultWrapper.wrap(wxOpenParanaMaService.releaseAuthed(shopWxa.getAppId()))
                    .ifSuccess(success -> {
                        ShopWxa updateStatus = new ShopWxa();
                        updateStatus.setId(shopWxa.getId());
                        updateStatus.setStatus(ShopWxaStatus.RELEASED.getValue());
                        shopWxaWriteService.update(shopWxa);
                    });
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * 获取最后一次审核状态
     *
     * @param projectId 项目Id
     * @return 返回true 如果审核通过
     * 返回 false 如果还在审核中
     * 携带错误信息 如果审核失败(错误信息为审核失败的原因)
     */
    public Either<Boolean> getLastAuthResult(Long projectId) {
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            Map<String, Object> result = Optional.ofNullable(Json.parseObject(
                    wxOpenParanaMaService.getLatestAuditStatus(shopWxa.getAppId()),
                    new TypeReference<Map<String, Object>>() {
                    })).orElseGet(HashMap::new);
            log.debug("{} last auth of project[{}] => Result[{}]", LogUtil.getClassMethodName(), projectId,
                    Json.toJson(result));
            if (WxResultWrapper.SUCCESS_CODE.equals(result.get("status").toString())) {
                return Either.ok(true);
            }
            if ("2".equals(result.get("status").toString())) {
                return Either.ok(false);
            }
            return Either.error(Optional.ofNullable(result.get("reason")).map(Objects::toString).orElse("原因丢失 请检查日志"));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    public Either<Boolean> urgentSubmit(Long projectId) {
        try {
            ShopWxaProject shopWxaProject = shopWxaProjectReadService.findById(projectId).getResult();
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
            return Optional.ofNullable(shopWxaProject.getExtra())
                    .map(extra -> extra.get("authId"))
                    .map(Long::parseLong)
                    .map(auditId -> {
                        try {
                            HttpRequest request = HttpRequest.post("https://api.weixin.qq.com/wxa/speedupaudit?access_token=" + wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false));
                            request.send(Json.toJson(ImmutableBiMap.of("auditid", auditId)));
                            if (request.ok()) {
                                return WxResultWrapper.wrap(request.body());
                            }
                            throw new RuntimeException("HTTP 请求失败 CODE:" + request.code());
                        } catch (Exception e) {
                            return Either.<Boolean>error(e);
                        }
                    })
                    .orElseThrow(() -> Translate.exceptionOf("小程序需要处于审核中, 才能提升为紧急审核"));
        } catch (Exception e) {
            return Either.error(e);
        }
    }
}
