package moonstone.web.core.decoration.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWriteService;
import moonstone.wxOpen.model.WxOpenUserTokenVO;
import moonstone.web.core.decoration.api.AlipayLoginApi;
import moonstone.wxOpen.service.WxOpenLoginApi;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
@NoArgsConstructor
public class WxUserDomain {
    private Long projectId;
    private String wxCode;

    /**
     * 微信登录用的AppId
     */
    private String appId;
    /**
     * 微信登录目标的店铺Id
     */
    private Long shopId;
    /**
     * 包含openId和sessionKey
     */
    private UserWx userWx;

    @Autowired
    private WxOpenLoginApi wxOpenLoginApi;
    @Autowired
    private UserWriteService<User> userWriteService;

    private ShopWxaProjectReadService shopWxaProjectReadService;

    private AlipayLoginApi alipayLoginApi;

    public WxUserDomain(Long projectId, String wxCode, WxOpenLoginApi wxOpenLoginApi, UserWriteService<User> userWriteService,
                        ShopWxaProjectReadService shopWxaProjectReadService,
                        AlipayLoginApi alipayLoginApi) {
        this.projectId = projectId;
        this.wxCode = wxCode;
        this.wxOpenLoginApi = wxOpenLoginApi;
        this.userWriteService = userWriteService;
        this.shopWxaProjectReadService = shopWxaProjectReadService;
        this.alipayLoginApi = alipayLoginApi;
    }

    /**
     * login, return login UserId
     */
    public Either<Long> login() {
        try {
            var appType = shopWxaProjectReadService.findAppType(projectId);
            WxOpenUserTokenVO wxOpenUserTokenVO = switch (appType) {
                case WECHAT -> wxOpenLoginApi.login(wxCode, projectId).take();
                case ALIPAY -> alipayLoginApi.login(wxCode, projectId);
            };

            appId = wxOpenUserTokenVO.getAppId();
            shopId = wxOpenUserTokenVO.getShopId();
            userWx = userWriteService.initUserWx(wxOpenUserTokenVO.getOpenId(), wxOpenUserTokenVO.getSessionKey(),
                    wxOpenUserTokenVO.getUnionId(), appId, null, appType).getResult();
            return Either.ok(userWx.getUserId());
        } catch (Exception exception) {
            log.error("{} fail to login at Shop[Id => {}, AppId => {}, Code => {}]", LogUtil.getClassMethodName(),
                    shopId, appId, wxCode, exception);
            return Either.error(exception);
        }
    }

}
