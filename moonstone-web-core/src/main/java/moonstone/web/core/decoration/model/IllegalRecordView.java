package moonstone.web.core.decoration.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class IllegalRecordView {
    @JSONField(name = "illegal_record_id")
    @JsonProperty("illegal_record_id")
    String illegalRecordId;
    @JSONField(name = "illegal_reason")
    @JsonProperty("illegal_reason")
    String illegalReason;
    @JSONField(name = "illegal_content")
    @SerializedName("illegal_content")
    @JsonProperty("illegal_content")
    String illegalContent;
    @JSONField(name = "rule_url")
    @JsonProperty("rule_url")
    @SerializedName("rule_url")
    String ruleUrl;
    @JSONField(name = "rule_name")
    @JsonProperty("rule_name")
    @SerializedName("rule_name")
    String ruleName;
    @JSONField(name = "create_time")
    @JsonProperty("create_time")
    @SerializedName("create_time")
    Date createTime;
}