package moonstone.web.core.decoration.slice;

import com.fasterxml.jackson.core.type.TypeReference;
import moonstone.common.model.Either;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public interface WxResultWrapper {
    Logger LOG = LoggerFactory.getLogger(WxResultWrapper.class);
    String ERR_CODE = "errcode";
    String ERR_MSG = "errmsg";
    String SUCCESS_CODE = "0";

    /**
     * wrap the simple result from wx
     *
     * @param res http res
     * @return Either
     */
    static Either<Boolean> wrap(String res) {
        Map<String, Object> resMap = Json.parseObject(res, new TypeReference<Map<String, Object>>() {
        });
        if (SUCCESS_CODE.equals(Objects.requireNonNull(resMap).get(ERR_CODE).toString())) {
            return Either.ok(true);
        }
        LOG.error("{} receive a error situation for Wechat API [{}]", LogUtil.getClassMethodName(), res);
        return Either.error(Translate.exceptionOf(resMap.get(ERR_MSG).toString()));
    }

    /**
     * wrap the simple result from wx
     *
     * @param res http res
     * @return Either
     */
    static <T> Either<T> wrap(String res, Function<Map<String, Object>, T> mapper) {
        Map<String, Object> resMap = Json.parseObject(res, new TypeReference<Map<String, Object>>() {
        });
        if (SUCCESS_CODE.equals(Objects.requireNonNull(resMap).get(ERR_CODE))) {
            return Either.ok(mapper).map(map -> map.apply(resMap));
        }
        LOG.error("{} receive a error situation for Wechat API [{}]", LogUtil.getClassMethodName(), res);
        return Either.error(Translate.exceptionOf(resMap.get(ERR_MSG).toString()));
    }
}
