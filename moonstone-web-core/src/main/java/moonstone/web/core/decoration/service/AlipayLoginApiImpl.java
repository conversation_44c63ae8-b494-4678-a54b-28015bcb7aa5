package moonstone.web.core.decoration.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.web.core.decoration.api.AlipayLoginApi;
import moonstone.web.core.util.AlipayOpenAPIUtil;
import moonstone.wxOpen.model.WxOpenUserTokenVO;
import moonstone.wxToken.model.AlipayAuthToken;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Slf4j
@Component
public class AlipayLoginApiImpl implements AlipayLoginApi {

    @Resource
    private ShopWxaProjectReadService shopWxaProjectReadService;

    @Resource
    private ShopWxaReadService shopWxaReadService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AlipayOpenAPIUtil alipayOpenAPIUtil;

    @Override
    public WxOpenUserTokenVO login(String authCode, Long projectId) {
        var project = shopWxaProjectReadService.findById(projectId).getResult();
        if (project == null) {
            throw new RuntimeException(String.format("projectId=%s小程序项目配置为空", projectId));
        }
        var shopWxa = shopWxaReadService.findById(project.getShopWxaId()).getResult();
        if (shopWxa == null) {
            throw new RuntimeException(String.format("projectId=%s小程序配置为空", projectId));
        }

        String appAuthToken = StringUtils.hasText(shopWxa.getAuthorizerAccessToken()) ? shopWxa.getAuthorizerAccessToken() :
                mongoTemplate.findOne(Query.query(Criteria.where("appId").is(alipayOpenAPIUtil.getServiceAppId())), AlipayAuthToken.class).getAppAuthToken();
        var response = alipayOpenAPIUtil.getAlipayAuthToken(authCode, null, alipayOpenAPIUtil.getServiceAppId(),
                alipayOpenAPIUtil.getServiceAppPrivateKey(), alipayOpenAPIUtil.getServiceAppPublicKey(), appAuthToken);
        if (!response.isSuccess()) {
            throw new RuntimeException("alipay.system.oauth.token调用结果失败");
        }

        String openId = StringUtils.hasText(response.getOpenId()) ? response.getOpenId() : response.getUserId();
        String unionId = StringUtils.hasText(response.getUnionId()) ? response.getUnionId() : openId;
        return new WxOpenUserTokenVO(shopWxa.getShopId(), shopWxa.getAppId(), openId, response.getAccessToken(), unionId);
    }
}
