package moonstone.web.core.fileNew.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2024/12/19
 * @Description:
 */
@Data
public class ItemCheckStockDto implements Serializable {

    private Long shopId;

    @Serial
    private static final long serialVersionUID = 1L;

    private List<SkuQuantity> skuQuantityList;

    private Address address;

    @Data
    public static class SkuQuantity implements Serializable {

        @Serial
        private static final long serialVersionUID = 2L;

        private String outerSkuId;

        private Integer quantity;
    }

    @Data
    public static class Address implements Serializable {

        @Serial
        private static final long serialVersionUID = 3L;

        private String province;

        private String city;

        private String region;

        private String street;

        private String detail;
    }
}
