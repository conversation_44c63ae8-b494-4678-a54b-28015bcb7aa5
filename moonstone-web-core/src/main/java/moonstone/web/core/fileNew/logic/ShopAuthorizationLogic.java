package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;
import moonstone.common.model.vo.PageVo;
import moonstone.shop.model.ShopAuthorization;
import moonstone.shop.service.ShopAuthorizationService;
import moonstone.web.core.fileNew.dto.ShopAuthorizationCreateOrUpdateDto;
import moonstone.web.core.fileNew.vo.ShopAuthorizationVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Service
@Slf4j
public class ShopAuthorizationLogic {

    @Resource
    private ShopAuthorizationService shopAuthorizationService;

    /**
     * 保存或修改授权书信息
     *
     * @param req 请求参数
     * @return 是否成功
     */
    public Boolean saveOrUpdate(ShopAuthorizationCreateOrUpdateDto req) {
        if (req.getId() == null) {
            Map<String, Object> query = new HashMap<>();
            query.put("shopId", req.getShopId());
            query.put("authorizationName", req.getAuthorizationName());
            ShopAuthorization shopAuthorization = shopAuthorizationService.getOneByShopIdAndAuthorizationName(query);
            if (shopAuthorization != null) {
                throw new ApiException("保存授权书信息失败，授权书名称已存在，请重命名");
            }
            shopAuthorization = new ShopAuthorization();
            BeanUtil.copyProperties(req, shopAuthorization);
            shopAuthorizationService.save(shopAuthorization);
        } else {
            Map<String, Object> query = new HashMap<>();
            query.put("id", req.getId());
            ShopAuthorization update = shopAuthorizationService.getOne(query);
            if (update == null) {
                throw new ApiException("未查询到授权书信息");
            }
            query = new HashMap<>();
            query.put("shopId", req.getShopId());
            query.put("authorizationName", req.getAuthorizationName());
            ShopAuthorization shopAuthorization = shopAuthorizationService.getOneByShopIdAndAuthorizationName(query);
            if (shopAuthorization != null) {
                if (!update.getId().equals(shopAuthorization.getId())) {
                    throw new ApiException("修改授权书信息失败：授权书名称已存在，请重命名");
                }
            }
            update.setAuthorizationName(req.getAuthorizationName());
            update.setAuthorizationUrl(req.getAuthorizationUrl());
            shopAuthorizationService.updateById(update);
        }
        return true;
    }


    /**
     * 分页查询店铺授权书信息列表
     *
     * @param current           当前页
     * @param size              每页显示数
     * @param shopId            店铺id
     * @param authorizationName 授权书名称
     * @return 授权书信息列表
     */
    public PageVo<ShopAuthorizationVo> pages(long current, long size, Long shopId, String authorizationName) {
        Paging<ShopAuthorization> paging = shopAuthorizationService.pages(current, size, shopId, authorizationName);
        Long total = paging.getTotal();
        long pages = total / size + (total % size == 0 ? 0 : 1);
        if (total == 0) {
            return PageVo.build(total, size, current, pages, Collections.emptyList());
        }
        List<ShopAuthorizationVo> dataList = paging.getData().stream().map(shopAuthorization -> {
            ShopAuthorizationVo entity = new ShopAuthorizationVo();
            BeanUtil.copyProperties(shopAuthorization, entity);
            String authorizationUrl = shopAuthorization.getAuthorizationUrl();
            int index = authorizationUrl.lastIndexOf(".");
            String authorizationFileName = shopAuthorization.getAuthorizationName() + authorizationUrl.substring(index);
            entity.setAuthorizationFileName(authorizationFileName);
            return entity;
        }).toList();
        return PageVo.build(total, size, current, pages, dataList);
    }

    /**
     * 删除店铺授权书信息
     *
     * @param id 授权书id
     * @return true：删除成功 false：删除失败
     */
    public Boolean removeById(Long id) {
        return shopAuthorizationService.removeById(id);
    }
}
