package moonstone.web.core.fileNew.strategy.factory;

import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.fileNew.strategy.api.RefundStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefundStrategyFactory {

	@Resource
	private List<RefundStrategy> refundStrategyList;


	/**
	 * 获取退款策略
	 * @param sourceType 来源类型
	 * @return 退款策略
	 */
	public RefundStrategy getRefundStrategy(int sourceType) {
		RefundStrategy target = null;
		for (RefundStrategy refundStrategy : refundStrategyList) {
			if (refundStrategy.isSupport(sourceType)) {
				target = refundStrategy;
			}
		}
		return target;
	}

}
