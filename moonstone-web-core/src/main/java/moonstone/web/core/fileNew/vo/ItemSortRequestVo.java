package moonstone.web.core.fileNew.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2025/01/02
 * @Description:
 */
@Data
public class ItemSortRequestVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1;

    private Long shopId;

    private List<Long> itemIds;

    private Long categoryId;
}
