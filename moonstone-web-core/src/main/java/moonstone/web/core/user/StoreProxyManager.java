package moonstone.web.core.user;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.AuthAble;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.HonestFanSum;
import moonstone.order.service.HonestFanDataReadService;
import moonstone.shop.model.Shop;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.StoreProxyWriteService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.component.StoreProxyRegisterComponent;
import moonstone.web.core.component.WxSubscribeMsgSender;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.wx.WxSubscribeMsg;
import moonstone.web.core.util.StoreProxyAuthProxy;
import moonstone.web.core.util.StoreProxyJXAuth;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Stream;

@Slf4j
@Service
@AllArgsConstructor
public class StoreProxyManager {
    private final HonestFanDataReadService honestFanDataReadService;
    private final UserRelationEntityReadService userRelationEntityReadService;
    private final StoreProxyReadService storeProxyReadService;
    private final StoreProxyWriteService storeProxyWriteService;

    private final StoreProxyRegisterComponent storeProxyRegisterComponent;
    private final WxSubscribeMsgSender wxSubscribeMsgSender;
    private final ShopWxaCacheHolder shopWxaCacheHolder;

    public Optional<StoreProxy> getStoreProxyByShopIdAndUserId(long shopId, long userId) {
        return storeProxyRegisterComponent.getStoreProxyByShopIdAndUserId(shopId, userId);
    }

    public List<Long> getProxyShopIdFromUserId(Long userId) {
        return storeProxyRegisterComponent.getProxyShopIdFromUserId(userId);
    }

    public Either<Long> regStoreProxy(Shop shop, User applier, Map<String, String> storeProxys) {
        return storeProxyRegisterComponent.registerStoreProxyLevelOne(shop, applier, storeProxys);
    }

    public Either<Long> inviteSubGuider(Shop shop, User invitor, User beInvited, Map<String, String> storeProxys) {
        return storeProxyRegisterComponent.registerStoreProxyLevelTwo(shop, invitor, beInvited, storeProxys);
    }

    public LoadingCache<Long, Optional<StoreProxy>> getProxyCachedById() {
        return storeProxyRegisterComponent.getProxyCachedById();
    }

    /**
     * 根据门店码Id获取代理信息
     * 如果开启了粉丝则开启找爸爸?(回朔购买链路)
     *
     * @param shopId    店铺Id
     * @param refererId 门店码Id
     * @return 代理
     */
    public Optional<StoreProxy> findValidStoreProxyFromRefererIdAndShopId(Long shopId, Long refererId) {
        return Optional.ofNullable(getStoreProxyByShopIdAndUserId(shopId, refererId).orElse(pullOutInvitor(shopId, refererId))).filter(guider -> {
            if (!StoreProxyAuthProxy.build(guider).isAuthed() || guider.isFreeze()) {
                log.warn("{} shopId:{} guider:{}", LogUtil.getClassMethodName("profit-skip-proxy"), shopId, JSON.toJSONString(guider));
                return false;
            }
            return true;
        });
    }

    /**
     * 拉取该用户的忠诚代理
     *
     * @param buyerId      买家Id
     * @param shopId       店铺Id
     * @param defaultProxy 如果找不到返回的默认代理
     * @return 为了支持默认代理为空使用Optional
     */
    public Optional<StoreProxy> getHonestedProxy(Long buyerId, Long shopId, StoreProxy defaultProxy) {
        return honestFanDataReadService.findByUserId(buyerId, shopId)
                .map(opt -> opt.map(HonestFanSum::getProxyId).orElse(null))
                .flatMap(userId -> storeProxyReadService.findByShopIdAndUserId(shopId, userId))
                // 如果不存在第一次拉新人 则这个导买成为第一次拉新人
                .orElse(Optional.ofNullable(defaultProxy));
    }

    /**
     * 拉取邀请请人 或者说回朔购买链路获取 代理
     *
     * @param shopId 店铺ID
     * @param userId refererId 目前门店码
     * @return 代理
     */
    public StoreProxy pullOutInvitor(Long shopId, Long userId) {
        Set<Long> searchedSet = new HashSet<>();
        boolean notFind = true;
        StoreProxy notFound = new StoreProxy();
        notFound.setRealName("查找不到");
        StoreProxyAuthProxy storeProxyAuthProxy = StoreProxyAuthProxy.build(notFound);
        storeProxyAuthProxy.initAuth();
        storeProxyAuthProxy.reject();
        notFound.freeze();
        StoreProxy found = notFound;
        while (notFind) {
            if (searchedSet.contains(userId)) {
                return notFound;
            }
            UserRelationEntity relationEntity = Optional.ofNullable(userRelationEntityReadService.listByUserIdAndType(userId, UserRelationEntity.UserRelationType.INVITOR).getResult())
                    .map(Collection::stream)
                    .flatMap(Stream::findFirst)
                    .orElse(null);
            if (relationEntity == null || relationEntity.getInvitorId() == null) {
                return getHonestedProxy(userId, shopId, notFound).orElse(notFound);
            }
            found = storeProxyReadService.findByShopIdAndUserId(shopId, relationEntity.getInvitorId()).orElse(Optional.empty()).orElse(null);
            searchedSet.add(userId);
            if (found != null && StoreProxyJXAuth.convertStoreProxy(found).isAuthed() && StoreProxyAuthProxy.build(found).isAuthed() && !found.isFreeze()) {
                notFind = false;
            } else {
                userId = relationEntity.getInvitorId();
            }
        }
        return found;
    }

    public Either<Boolean> authStoreProxy(long shopId, long userId, String mdRemark) {
        Long proxyIndexId = null;
        try {
            StoreProxy storeProxy = getStoreProxyByShopIdAndUserId(shopId, userId).orElseThrow(() -> new JsonResponseException(new Translate("查找代理员信息失败").toString()));
            StoreProxyAuthProxy storeProxyAuthProxy = StoreProxyAuthProxy.build(storeProxy);
            if (storeProxyAuthProxy.isAuthed()) {
                return Either.ok(true);
            }
            proxyIndexId = storeProxy.getId();
            AuthAble authStoreProxy = StoreProxyAuthProxy.build(storeProxy);
            // 经销商级别 一次审核便通过
            if (storeProxy.getLevel() == 1) {
                sendAuthMsg(storeProxy, true, mdRemark);
            }
            authStoreProxy.auth();
            Map<String, String> map = storeProxy.getExtra();
            map.put("auditingRemark", mdRemark);
            log.info("{} StoreProxy[UserId => {}, ShopId => {}] auth today", LogUtil.getClassMethodName(), userId, shopId);
            return storeProxyWriteService.updateWithStatus(storeProxy);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(new Translate("查找代理员信息失败").toString());
        } finally {
            Optional.ofNullable(proxyIndexId).ifPresent(getProxyCachedById()::invalidate);
        }
    }

    /**
     * 向用户发送审核通知
     *
     * @param storeProxy 代理信息
     * @param allow      是否允许
     */
    private void sendAuthMsg(StoreProxy storeProxy, boolean allow, String remark) {
        try {
            WxSubscribeMsg subscribeMsg = new WxSubscribeMsg();
            subscribeMsg.setUserId(storeProxy.getUserId());
            subscribeMsg.setShopId(storeProxy.getShopId());
            subscribeMsg.setTemplate_id("65tse4Cl-6SgsrsiENjQxoL-EeTEvEC81Iq5Qn7DHR8");
            subscribeMsg.setData(ImmutableMap.of("thing1", storeProxy.getLevel() == 1 ? "经销商申请" : "门店申请", "date2", new DateTime().toString("YYYY-MM-dd HH:mm:ss")
                    , "phrase3", allow ? "通过" : "拒绝"
                    , "thing4", limitString(remark)));
            if (shopWxaCacheHolder.findReleaseOneForShopId(storeProxy.getShopId()) == null) {
                return;
            }
            if (wxSubscribeMsgSender.getSupportedAppId().contains(shopWxaCacheHolder.findReleaseOneForShopId(storeProxy.getShopId()).getAppId())) {
                wxSubscribeMsgSender.send(subscribeMsg);
            }
        }
        catch (Exception e){
            log.error("{} Fail to Send Auth Info for StoreProxy(UserId -> {}, ShopId -> {}), Auth[Pass -> {}] Reason -> {}", LogUtil.getClassMethodName(), storeProxy.getUserId(), storeProxy.getShopId(), allow, remark, e);
        }
    }

    private String limitString(String str) {
        if (str.length() > 20) {
            return str.substring(0, 18) + "..";
        }
        return str;
    }

    public Either<Boolean> jxAuthStoreProxy(long shopId, long userId, String jxRemark) {
        Long proxyIndexId = null;
        try {
            StoreProxyJXAuth.StoreProxyAuthProxyJXAuth storeProxy = StoreProxyJXAuth.convertStoreProxy(getStoreProxyByShopIdAndUserId(shopId, userId).orElseThrow(() -> new JsonResponseException(new Translate("查找代理员信息失败").toString())));
            if (storeProxy.isAuthed()) {
                return Either.ok(true);
            }
            proxyIndexId = storeProxy.getId();
            storeProxy.auth();
            Map<String, String> map = storeProxy.getExtra();
            map.put("jxRemark", jxRemark);
            log.info("{} StoreProxy[UserId => {}, ShopId => {}] jxAuth today", LogUtil.getClassMethodName(), userId, shopId);
            return storeProxyWriteService.updateWithStatus(storeProxy);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(new Translate("查找代理员信息失败").toString());
        } finally {
            Optional.ofNullable(proxyIndexId).ifPresent(getProxyCachedById()::invalidate);
        }
    }

    public Either<Boolean> rejectStoreProxy(Long shopId, long userId, String mdRemark) {
        Long proxyIndexId = null;
        try {
            log.info("[rejectStoreProxy] shopId:{} userId:{} mdRemark:{}", shopId, userId, mdRemark);
            StoreProxy storeProxy = getStoreProxyByShopIdAndUserId(shopId, userId).orElseThrow(() -> new JsonResponseException(new Translate("查找代理员信息失败").toString()));
            StoreProxyAuthProxy storeProxyAuthProxy = StoreProxyAuthProxy.build(storeProxy);
            if (storeProxyAuthProxy.isReject() || storeProxy.getStatus() == -1) {
                return Either.ok(true);
            }
            proxyIndexId = storeProxy.getId();
//            storeProxy.reject();// 被下面的假删代替
//            storeProxy.setStatus(-1);
            AuthAble authStoreProxy = StoreProxyAuthProxy.build(storeProxy);
            authStoreProxy.reject();
            sendAuthMsg(storeProxy, false, mdRemark);
            Map<String, String> map = storeProxy.getExtra();
            map.put("auditingRemark", mdRemark);
            return storeProxyWriteService.updateWithStatus(storeProxy);
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{},ex:{}", LogUtil.getClassMethodName("rejectStoreProxy"), shopId, userId, ex.getMessage(), ex);
            return Either.error(new Translate("查找代理员信息失败").toString());
        } finally {
            Optional.ofNullable(proxyIndexId).ifPresent(getProxyCachedById()::invalidate);
        }
    }

    public Either<Boolean> jxRejectStoreProxy(Long shopId, long userId, String jxRemark) {
        Long proxyIndexId = null;
        try {
            StoreProxyJXAuth.StoreProxyAuthProxyJXAuth storeProxy = StoreProxyJXAuth.convertStoreProxy(getStoreProxyByShopIdAndUserId(shopId, userId).orElseThrow(() -> new JsonResponseException(new Translate("查找代理员信息失败").toString())));
            if (storeProxy.isReject()) {
                return Either.ok(true);
            }
            proxyIndexId = storeProxy.getId();
            storeProxy.reject();
            sendAuthMsg(storeProxy, false, jxRemark);
            Map<String, String> map = storeProxy.getExtra();
            map.put("jxRemark", jxRemark);
            return storeProxyWriteService.updateWithStatus(storeProxy);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(new Translate("查找代理员信息失败").toString());
        } finally {
            Optional.ofNullable(proxyIndexId).ifPresent(getProxyCachedById()::invalidate);
        }
    }
}
