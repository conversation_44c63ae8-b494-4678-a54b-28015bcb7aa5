package moonstone.web.core.files;

import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.shop.CertFileUploadEvent;
import moonstone.web.core.files.config.UploadConfig;
import moonstone.web.core.files.service.OSSClientService;
import moonstone.web.core.shop.application.ShopPayInfoComponent;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 图片|文件上传接口
 *
 * <AUTHOR>
 * 2017年3月10日 下午3:55:43
 */
@Slf4j
@Controller
public class UploadAjaxController {

    private List<String> allowedFileTypes;

    @Autowired
    private UploadConfig uploadConfig;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private OSSClientService ossClientService;

    @Resource
    private ShopPayInfoComponent shopPayInfoComponent;


    @PostConstruct
    public void init() {
        if (StringUtils.isNotBlank(uploadConfig.getAllowedFileTypes())) {
            String[] types = uploadConfig.getAllowedFileTypes().split(",");
            allowedFileTypes = new ArrayList<>(types.length);
            allowedFileTypes.addAll(Arrays.asList(types));
        }
        allowedFileTypes.addAll(Arrays.asList(ImageIO.getReaderFormatNames()));
    }

    /**
     * umeditor组件上传图片
     */
    @RequestMapping("/api/file/umeditor")
    @ResponseBody
    public String umeditor(@RequestParam("upfile") MultipartFile multipartFile) {
        try {
            String url = uploadMultiFile(multipartFile);
            return String.format("{\"originalName\":\"%s\"," +
                            "\"name\":\"%s\"," +
                            "\"url\":\"%s\"," +
                            "\"size\":\"%s\"," +
                            "\"type\":\"%s\"," +
                            "\"status\":\"%s\"}",
                    multipartFile.getOriginalFilename(),
                    multipartFile.getName(),
                    url,
                    multipartFile.getSize(),
                    multipartFile.getContentType(),
                    "SUCCESS");
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 用于bui组件上传
     */
    @RequestMapping("/api/file/uploadFliePlugin")
    @ResponseBody
    public Map<String, Object> uploadFliePlugin(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> res = new HashMap<>();
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile multipartFile = multiRequest.getFile("filedata");
        try {
            String url = uploadMultiFile(multipartFile);
            res.put("code", "success");
            res.put("message", "上传成功");
            res.put("path", url);
        } catch (Exception e) {
            log.error("[op:uploadFliePlugin] upload file to OSS failed, cause:{}", Throwables.getStackTraceAsString(e));
            res.put("code", "fail");
            res.put("message", e.getMessage());
            res.put("path", "");
        }
        return res;
    }

    /**
     * 上传图片(跨越上传使用)
     */
    @RequestMapping(value = "/api/file/uploadCORS", produces = "application/json; charset=utf-8")
    @ResponseBody
    public Map<String, Object> uploadCORS(@RequestParam(required = false) MultipartFile file) {
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("code", "success");
            map.put("imgUrl", uploadMultiFile(file));
            return map;
        } catch (Exception e) {
            log.error("[op:uploadCORS] upload file to OSS failed, cause:{}", Throwables.getStackTraceAsString(e));
            map.put("code", "fail");
            return map;
        }
    }

    /**
     * 多图片上传(返回带IP带端口全路径)
     */
    @RequestMapping(value = "/api/file/uploadImgs", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, Object> uploadImgs(@RequestParam(value = "file", required = false) MultipartFile[] files) {
        Map<String, Object> result = new HashMap<>();
        for (MultipartFile file : files) {
            try {
                result.put(file.getOriginalFilename(), uploadMultiFile(file));
            } catch (Exception e) {
                log.error("[op:uploadImg] upload file to OSS failed, cause:", e);
                throw new JsonResponseException(500, "upload.failed");
            }
        }
        return result;
    }

    @RequestMapping(value = "/api/user/files/upload", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, Object> upload(@RequestParam(value = "file", required = false) MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("name", file.getOriginalFilename());
            result.put("image", uploadMultiFile(file));
        } catch (Exception e) {
            log.error("[op:uploadImg] upload file to OSS failed, cause:", e);
            throw new JsonResponseException(500, "upload.failed");
        }
        return result;
    }

    /**
     * 重写api, 规范api返回的格式,单个文件上传。
     */
    @RequestMapping(value = "/api/file/uploadImg", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, Object> uploadImg(@RequestParam(value = "file", required = false) MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        try {
            String imgUrl = uploadMultiFile(file);
            result.put("code", 0);
            result.put("msg", "操作成功");
            result.put("data", imgUrl);
            result.put("success", true);
        } catch (Exception e) {
            result.put("code", -1);
            result.put("msg", e.getMessage());
            result.put("success", false);
        }
        return result;
    }

    /**
     * 证书文件上传
     */
    @RequestMapping(value = "/api/file/certFile/upload", method = RequestMethod.POST)
    public @ResponseBody
    Boolean uploadCertFile(@RequestParam("file") MultipartFile file,
                           @RequestParam("shopPayInfoId") Long shopPayInfoId,
                           HttpServletRequest request) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();

        //调用文件处理类FileUtil，处理文件，将文件写入指定位置
        try {
            shopPayInfoComponent.uploadWechatPayCertFile(shopId, shopPayInfoId, file);
        } catch (Exception e) {
            log.error("[op:upload] upload certFile failed, cause:", e);
            throw new JsonResponseException("certFile.upload.failed");
        }

        EventSender.sendApplicationEvent(new CertFileUploadEvent(shopPayInfoId));
        return Boolean.TRUE;
    }


    private String uploadMultiFile(MultipartFile file) {
        if (file == null) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file empty");
            throw new JsonResponseException(507, "upload.file.empty");
        }
        log.debug("[op:uploadMultiFile] filename={}", file.getOriginalFilename());
        String extName = FilenameUtils.getExtension(file.getOriginalFilename());
        if (extName == null || !checkFileType(extName)) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file extension wrong");
            throw new JsonResponseException(508, "upload.file.type.illegal");
        }
        // 生成key
        for (String ext : ImageIO.getReaderFormatNames()) {
            if (extName.toUpperCase(Locale.ROOT).endsWith(ext.toUpperCase(Locale.ROOT))) {
                extName = "webp";
                break;
            }
        }
        String key = ossClientService.genKeyByFileName(file.getOriginalFilename(), extName);
        // 上传
        try {
            return ossClientService.upload(environmentConfig.getEnv(), key, file.getInputStream());
        } catch (IOException e) {
            log.error("[op:uploadMultiFile] upload file to OSS failed, cause: {}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(500, "upload.failed");
        }
    }

    /**
     * 检查文件的扩展名是否正确
     */
    private boolean checkFileType(String extName) {
        return allowedFileTypes.contains(extName.toLowerCase());
    }


    /**
     * 文件上传
     */
    @PostMapping("api/file/upload")
    @ResponseBody
    public Result<String> fileUpload(@RequestParam("file") MultipartFile file) {
        log.info("文件上传");
        return Result.data(ossClientService.fileUpload(file));
    }

}

