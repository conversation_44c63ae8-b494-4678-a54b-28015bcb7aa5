package moonstone.web.core.model;

import lombok.Data;

import java.util.Set;

@Data
public class SkuOrderAuthRequireJudgeRule {
    String _id;
    String name;
    Long shopId;    // 店铺Id
    Long skuId;     // 单品Id
    Long itemId;    // 商品Id
    //  以下为额外检查项,如果全为空,符合上面三者中的条件则返回requireAuth
    String itemCode;  // 商品编号
    Long priceBelow; // 价格低于
    Integer thirdPartySkuStockBelow;    // 第三方库存低于
    Long userBoughtAbove;   // 用户购买数量超过
    Set<Long> userIdSet;  // 用户Id在内
    Set<Long> userIdExcludeSet;  // 用户Id排除A
    String mobileLike;  //  手机号码类似

    boolean requireAuth = true;
}
