package moonstone.web.core;

import moonstone.web.core.express.component.CainiaoService;
import moonstone.web.core.express.component.Express100Service;
import moonstone.web.core.express.component.ExpressService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 快递服务配置
 * Author:cp
 * Created on 7/7/16.
 */
@Configuration
@ComponentScan(basePackages = {"moonstone.web.core.express"})
public class ExpressConfig {

    @ConditionalOnMissingBean(ExpressService.class)
    @ConditionalOnProperty({"express.100.key", "express.100.customer"})
    @Bean
    public ExpressService express100Service(@Value("${express.100.key}") String key,
                                            @Value("${express.100.customer}") String customer,
                                            @Value("${express.100.regularUrl}") String regularUrl) {
        return new Express100Service(key, customer, regularUrl);
    }

    @ConditionalOnMissingBean(ExpressService.class)
    @ConditionalOnProperty({"express.cainiao.key", "express.cainiao.appName"})
    @Bean
    public ExpressService cainiaoService(@Value("${express.cainiao.key}") String key,
                                         @Value("${express.cainiao.appName}") String appName) {
        return new CainiaoService(key, appName);
    }

}
