package moonstone.web.core;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Configuration
public class RedisConfig {
	@Bean
	@ConditionalOnMissingBean(JedisPool.class)
	public JedisPool initJedisPoolSource(@Value("${session.redis-host}") String redisHost
			, @Value("${session.redis-port}") int port
			, @Value("${session.redis-auth:}") String auth
			, @Value("${session.redis-max-total}") int maxTotal
			, @Value("${session.redis-max-idle}") int maxIdle
	) {
		JedisPoolConfig config = new JedisPoolConfig();
		// 八路～
		config.setMaxTotal(8000 * maxTotal);
		config.setMaxIdle(config.getMaxTotal());
		config.setMinIdle(config.getMaxTotal() / 50);
		config.setTestWhileIdle(true);
		config.setTestOnCreate(true);
		return auth.isEmpty()
				? new JedisPool(config, redisHost, port, 3000)
				: new JedisPool(config, redisHost, port, 3000, auth);
	}

}
