package moonstone.web.core;

//@Configuration
//public class MongoOperationProducer {
//
//    @Bean
//    MongoTemplate mongoTemplate(@Value("${spring.data.mongodb.uri}") String uri){
//        var settings =  MongoClientSettings.builder()
//                .applyConnectionString(new ConnectionString(uri))
//                .applyToConnectionPoolSettings(c -> c
//                        .maxConnecting(20)
//                        .maxConnectionIdleTime(15, TimeUnit.SECONDS)
//                        .maxConnectionLifeTime(3, TimeUnit.MINUTES))
//                .build();
//        MongoClient mongoClient = MongoClients.create(settings);
//        var name = uri.substring(uri.lastIndexOf("/") + 1);
//        return new MongoTemplate(new SimpleMongoClientDatabaseFactory(mongoClient, name));
//    }
//}
