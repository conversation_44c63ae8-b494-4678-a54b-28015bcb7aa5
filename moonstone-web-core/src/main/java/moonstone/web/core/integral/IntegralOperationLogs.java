package moonstone.web.core.integral;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.IPUtil;
import moonstone.common.utils.UserUtil;
import moonstone.order.model.IntegralOperationLog;
import moonstone.order.service.IntegralOperationLogReadService;
import moonstone.order.service.IntegralOperationLogWriteService;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/2 20:12
 * 积分商城相关的操作记录
 */
@Component
@Slf4j
public class IntegralOperationLogs {

    @RpcConsumer
    IntegralOperationLogReadService integralOperationLogReadService;

    @RpcConsumer
    IntegralOperationLogWriteService integralOperationLogWriteService;

    public Response<Boolean> recordWriteOperationLog(HttpServletRequest request, IntegralOperationLog operationLog){
        String ip = IPUtil.getIpAddr(request);
        CommonUser user = UserUtil.getCurrentUser();
        operationLog.setIp(ip);
        operationLog.setCreateAt(System.currentTimeMillis());
        operationLog.setUserId(user.getId());
        integralOperationLogWriteService.create(operationLog);

        return Response.ok();
    }

}
