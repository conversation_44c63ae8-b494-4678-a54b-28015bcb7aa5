package moonstone.web.core.integral;

import com.alibaba.fastjson.JSONObject;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.lock.IntegralLock;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.model.Either;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.IntegralUseRecord;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.IntegralUseRecordWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.user.model.StoreIntegral;
import moonstone.user.model.StoreIntegralRecord;
import moonstone.user.service.StoreIntegralReadService;
import moonstone.user.service.StoreIntegralRecordReadService;
import moonstone.user.service.StoreIntegralRecordWriteService;
import moonstone.user.service.StoreIntegralWriteService;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.function.Supplier;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/19 9:13
 * 积分账户所有的处理放在这里
 * parana_store_integral
 */
@Component
@Slf4j
public class IntegralAccount {

    @Resource
    StoreIntegralWriteService storeIntegralWriteService;

    @Resource
    StoreIntegralReadService storeIntegralReadService;

    @Resource
    StoreIntegralRecordWriteService storeIntegralRecordWriteService;

    @Resource
    StoreIntegralRecordReadService storeIntegralRecordReadService;

    @Resource
    IntegralUseRecordWriteService integralUseRecordWriteService;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * @param map  --积分账户实体(map 至少包含 user_id 和 shop_id)
     * @param type 类型 1-初始化积分账户 2-扫码增加积分 3-交易成功增加核减数 4-退款减少核减数
     *             5-交易成功增加积分 6-退款减少积分 7-扫码增加冻结积分 8积分订单交易扣除积分
     * @return
     * @throws Exception
     */
//    @RequestMapping(value = "/api/integral/wuxianS", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Transactional
    public Either<StoreIntegral> initializeIntegralAccount(@RequestParam Map<String, String> map, @RequestParam Integer type) {
        log.info("[initializeIntegralAccount] map:{},type:{}", map, type);
        if (ObjectUtils.isEmpty(type) || StringUtils.isEmpty(map) ||
                ObjectUtils.isEmpty(map.containsKey("userId")) || StringUtils.isEmpty(map.containsKey("shopId")) ||
                ObjectUtils.isEmpty(map.get("userId")) || StringUtils.isEmpty(map.get("shopId"))) {
            log.error("initializeIntegralAccount Param is empty map:{} type;{}", map, type);
            return Either.fail();
        }
        StoreIntegral returnR = new StoreIntegral();
        switch (type) {
            case 1://初始化积分账户
                returnR = initialize(map);
                break;
            case 2://扫码增加积分+解冻其他段积分 integralFee ---交易积分数  积分码 贝拉米
                returnR = scanAddIntegral(map);
                break;
            case 3://交易成功增加核减数  integralCount 交易的购买数量 积分码 贝拉米
                returnR = addTradeNumIntegral(map);
                break;
            case 4://退款减少核减数  integralCount 交易的购买数量 积分码 贝拉米
                returnR = reduceTradeNumIntegral(map);
                break;
            case 5://交易成功增加积分  integralCount 交易的购买数量 累计积分 皇家
                returnR = tradeAddIntegral(map);
                break;
            case 6://退款减少积分  integralCount 交易的购买数量 累计积分 皇家
                returnR = tradeReduceIntegral(map);
                break;
            case 7://扫码增加冻结积分 integralFee ---交易积分数 积分码 贝拉米
                returnR = scanFrozenIntegral(map);
                break;
            case 8://积分订单交易扣除积分 integralFee ---交易积分数 贝拉米+皇家
                returnR = tradeDeductionIntegral(map);
                break;
            case 9://首次购买的机会是否已用 firstBuy  累计积分 皇家
                returnR = firstBuyIntegral(map);
                break;
            case 10://积分账户有效    积分码 贝拉米
                returnR = updateIntegral(map);
                break;
            case 11://商家修改积分账户 积分码 贝拉米
                returnR = buyerUpdateIntegral(map);
                break;
            default:
                break;
        }
        if (returnR == null || ObjectUtils.isEmpty(returnR.getId())) {
            log.error("initializeIntegralAccount is fial map:{} type;{}", map, type);
            return Either.fail();
        }
        return Either.ok(returnR);
    }

    private StoreIntegral buyerUpdateIntegral(Map<String, String> map) {

        Long fee = Long.valueOf(map.get("integralFee"));
        Long userId = Long.valueOf(map.get("userId"));
        Long shopId = Long.valueOf(map.get("shopId"));
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        if (fee >= 0) {
            //修改积分账户
            storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() + fee);
            storeIntegral.setTotalGrade(storeIntegral.getTotalGrade() + fee);
            Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);

            if (booleanResult.isSuccess() && booleanResult.take()) {
                //增加修改记录---为获取记录
                StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                storeIntegralRecord.setIntegralId(storeIntegral.getId());
                storeIntegralRecord.setFaceValue(fee);
                storeIntegralRecord.setRemainValue(fee);
                storeIntegralRecord.setStatus(IntegralStatus.NORMAL.value());
                storeIntegralRecord.setOrigin(1);
                Either<Long> s = storeIntegralRecordWriteService.create(storeIntegralRecord);
                if (s.isSuccess() && !ObjectUtils.isEmpty(s.take())) {
                    //记录扫码记录-mongodb
                    IntegralUseRecord integralUseRecord = new IntegralUseRecord();
                    integralUseRecord.setCreateAt(System.currentTimeMillis());
                    integralUseRecord.setUpdateAt(System.currentTimeMillis());
                    integralUseRecord.setStatus(IntegralStatus.AVAILABLE.value());
                    integralUseRecord.setUseIntegral(fee);
                    integralUseRecord.setUserId(userId);
                    integralUseRecord.setShopId(shopId);
                    integralUseRecord.setCode("");
                    integralUseRecord.setTradeId(s.take());
                    integralUseRecord.setIp(map.get("ip"));
                    integralUseRecord.setOperatorId(Long.valueOf(map.get("operatorId")));
                    integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
                }

            }
            return storeIntegral;
        } else {

            Response<List<StoreIntegralRecord>> recordList = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.NORMAL.value());
            if (recordList.isSuccess() && !recordList.getResult().isEmpty()) {

                List<Long> listred = new ArrayList<>();
                List<Long> listLj = new ArrayList<>();
                Long redFee = 0L;
                fee = -fee;
                for (StoreIntegralRecord s : recordList.getResult()) {
                    redFee += s.getRemainValue();
                    if (redFee >= fee) {
                        listLj.add(s.getId());
                        break;
                    } else {
                        listred.add(s.getId());
                    }
                }
                if (redFee < fee) {
                    log.error("buyerUpdateIntegral is error account is not empty map:{}", map);
                    return new StoreIntegral();
                }
                for (Long s : listred) {
                    StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                    storeIntegralRecord.setId(s);
                    storeIntegralRecord.setRemainValue(0L);
                    storeIntegralRecord.setOrigin(4);
                    storeIntegralRecord.setStatus(IntegralStatus.USEED.value());
                    storeIntegralRecordWriteService.update(storeIntegralRecord);
                }
                for (Long s : listLj) {
                    if (fee == redFee) {
                        StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                        storeIntegralRecord.setId(s);
                        storeIntegralRecord.setRemainValue(0L);
                        storeIntegralRecord.setStatus(IntegralStatus.USEED.value());
                        storeIntegralRecord.setOrigin(4);
                        storeIntegralRecordWriteService.update(storeIntegralRecord);
                    } else {
                        StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                        storeIntegralRecord.setId(s);
                        storeIntegralRecord.setRemainValue(redFee - fee);
                        storeIntegralRecord.setOrigin(4);
                        storeIntegralRecordWriteService.update(storeIntegralRecord);
                    }
                }

                //增加修改记录---为减少积分账户获取记录
                StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                storeIntegralRecord.setIntegralId(storeIntegral.getId());
                storeIntegralRecord.setFaceValue(-fee);
                storeIntegralRecord.setRemainValue(-fee);
                storeIntegralRecord.setStatus(IntegralStatus.BUYER_REDUCE.value());
                storeIntegralRecord.setOrigin(4);
                Either<Long> s = storeIntegralRecordWriteService.create(storeIntegralRecord);

                if (s.isSuccess() && !ObjectUtils.isEmpty(s.take())) {
                    //记录扫码记录-mongodb
                    IntegralUseRecord integralUseRecord = new IntegralUseRecord();
                    integralUseRecord.setCreateAt(System.currentTimeMillis());
                    integralUseRecord.setUpdateAt(System.currentTimeMillis());
                    integralUseRecord.setStatus(IntegralStatus.AVAILABLE.value());
                    integralUseRecord.setUseIntegral(-fee);
                    integralUseRecord.setUserId(userId);
                    integralUseRecord.setShopId(shopId);
                    integralUseRecord.setCode("");
                    integralUseRecord.setTradeId(s.take());
                    integralUseRecord.setIp(map.get("ip"));
                    integralUseRecord.setOperatorId(Long.valueOf(map.get("operatorId")));
                    integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
                }


                //修改积分账户
                storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() - fee);
                storeIntegral.setTotalGrade(storeIntegral.getTotalGrade() - fee);
                Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
                if (!booleanResult.isSuccess() || !booleanResult.take()) {
                    log.error("buyerUpdateIntegral updateStoreIntegral is error  map:{}", map);
                    return new StoreIntegral();
                }
                return storeIntegral;
            } else {
                return new StoreIntegral();
            }
        }
    }

    private StoreIntegral updateIntegral(Map<String, String> map) {
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            return new StoreIntegral();
        }
        return storeIntegral;
    }

    private StoreIntegral firstBuyIntegral(Map<String, String> map) {
        String firstBuy = map.get("firstBuy");
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        JSONObject json = new JSONObject();
        json.put("firstBuy", firstBuy);
        storeIntegral.setExtraStr(json.toString());
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
//        Result<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegralExtra(userId, shopId, json.toJSONString());
        if (!booleanResult.isSuccess()) {
            return new StoreIntegral();
        }
        return storeIntegral;
    }

    /**
     * 积分订单交易扣除积分  先处理过期积分
     *
     * @param map
     * @return
     */
    private StoreIntegral tradeDeductionIntegral(Map<String, String> map) {
        Long userId = Long.valueOf(map.get("userId"));
        Long shopId = Long.valueOf(map.get("shopId"));
        Long integralFee = Long.valueOf(map.get("integralFee"));
        Long tradeId = Long.valueOf(map.get("tradeId"));
        StoreIntegral storeIntegral = initialize(map);
        //判断是否可以扣除积分
        if (storeIntegral == null || (storeIntegral.getAvailableGrade() - integralFee < 0)) {
            log.error("storeIntegral is not more integral map:{}", map);
            return new StoreIntegral();
        }
        Shop rShop = Optional.ofNullable(shopReadService.findById(shopId).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
        Map<String, String> extras = rShop.getExtra();
        //处理皇家
        if (extras != null && !extras.isEmpty()
                && extras.containsKey("startIntegral")
                && extras.get("startIntegral").equals("3")
        ) {
            storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() - integralFee);
            storeIntegral.setUseGrade(storeIntegral.getUseGrade() + integralFee);
            Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
            if (booleanResult.isSuccess() && booleanResult.take()) {
                //记录使用记录
                IntegralUseRecord integralUseRecord = new IntegralUseRecord();
                integralUseRecord.setCreateAt(System.currentTimeMillis());
                integralUseRecord.setStatus(IntegralStatus.USEED.value());
                integralUseRecord.setUseIntegral(integralFee);
                integralUseRecord.setUserId(userId);
                integralUseRecord.setShopId(shopId);
                integralUseRecord.setTradeId(tradeId);
                integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
            }

            return storeIntegral;
        }
        //处理贝拉米
        if (extras != null && !extras.isEmpty()
                && extras.containsKey("startIntegral")
                && extras.get("startIntegral").equals("2")
        ) {

            Response<List<StoreIntegralRecord>> listResponse = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.NORMAL.value());
            if (!listResponse.isSuccess() || listResponse.getResult().isEmpty()) {
                return new StoreIntegral();
            }
            Long bonusPoints = 0l;//消费积分
            List<Long> consumerId = new ArrayList<>();//完全兑换掉的积分
            List<Long> consumerlj = new ArrayList<>();//兑换临界的积分Id
//            List<Long> expire = new ArrayList<>();//兑换过期的Id

            for (StoreIntegralRecord sr : listResponse.getResult()) {
                if (DateUtil.compareTime(sr.getValidAt(), new Date())) {
                    bonusPoints += sr.getRemainValue();
                    if (bonusPoints >= integralFee) {
                        consumerlj.add(sr.getId());
                        break;
                    } else {
                        consumerId.add(sr.getId());
                    }
                }
            }

            //将使用的积分修改为已使用
            for (Long s : consumerId) {
                StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                storeIntegralRecord.setId(s);
                storeIntegralRecord.setStatus(IntegralStatus.USEED.value());
                storeIntegralRecord.setRemainValue(0L);
                storeIntegralRecordWriteService.update(storeIntegralRecord);
                //同步mongodb
//                integralUseRecordWriteService.updateUserRecord(userId, shopId, s, IntegralStatus.NORMAL.value(), IntegralStatus.USEED.value());

            }
            //将临界扣除的积分扣掉
            for (Long s : consumerlj) {
                StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                storeIntegralRecord.setId(s);
                storeIntegralRecord.setRemainValue(bonusPoints - integralFee);
                storeIntegralRecordWriteService.update(storeIntegralRecord);
                if (storeIntegralRecord.getRemainValue() == 0) {
                    storeIntegralRecord.setStatus(IntegralStatus.USEED.value());
                    storeIntegralRecordWriteService.update(storeIntegralRecord);
                    //同步mongodb
//                    integralUseRecordWriteService.updateUserRecord(userId, shopId, s, IntegralStatus.NORMAL.value(), IntegralStatus.USEED.value());
                } else {
                    storeIntegralRecordWriteService.update(storeIntegralRecord);
                }
            }
            storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() - integralFee);
            storeIntegral.setUseGrade(storeIntegral.getUseGrade() + integralFee);
            Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
            if (booleanResult.isSuccess() && booleanResult.take()) {
                //记录使用记录
                IntegralUseRecord integralUseRecord = new IntegralUseRecord();
                integralUseRecord.setCreateAt(System.currentTimeMillis());
                integralUseRecord.setStatus(IntegralStatus.USEED.value());
                integralUseRecord.setUseIntegral(integralFee);
                integralUseRecord.setUserId(userId);
                integralUseRecord.setShopId(shopId);
                integralUseRecord.setTradeId(tradeId);
//                integralUseRecord.setRemainder(storeIntegral.getAvailableGrade());
                integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
            }
            return storeIntegral;
        }
        return new StoreIntegral();
    }

    /**
     * 扫码冻结积分
     *
     * @param map
     * @return
     */
    private StoreIntegral scanFrozenIntegral(Map<String, String> map) {
        Long userId = Long.valueOf(map.get("userId"));
        Long shopId = Long.valueOf(map.get("shopId"));
        Long integralFee = Long.valueOf(map.get("integralFee"));
        String code = map.get("code");
        StoreIntegral storeIntegral = initialize(map);
        if (storeIntegral == null || ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        storeIntegral.setFrozenGrade(storeIntegral.getFrozenGrade() + integralFee);
        storeIntegral.setTotalGrade(storeIntegral.getTotalGrade() + integralFee);
//        storeIntegral.setTradeAll(storeIntegral.getTradeAll() + 1);//扫码次数加一（用于核减数）
//        if(storeIntegral.getTradeNum()>0){
//            storeIntegral.setTradeNum(storeIntegral.getTradeNum()-1);
//        }
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            return new StoreIntegral();
        }

        //增加扫码记录
        StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
        storeIntegralRecord.setIntegralId(storeIntegral.getId());
        storeIntegralRecord.setFaceValue(integralFee);
        storeIntegralRecord.setRemainValue(integralFee);
        storeIntegralRecord.setStatus(IntegralStatus.FROZEN.value());
        storeIntegralRecord.setOrigin(0);
        Either<Long> longResult = storeIntegralRecordWriteService.create(storeIntegralRecord);
        if (longResult.isSuccess() && !ObjectUtils.isEmpty(longResult.take())) {
            //记录扫码记录-mongodb
            IntegralUseRecord integralUseRecord = new IntegralUseRecord();
            integralUseRecord.setCreateAt(System.currentTimeMillis());
            integralUseRecord.setUpdateAt(System.currentTimeMillis());
            integralUseRecord.setStatus(IntegralStatus.FROZEN.value());
            integralUseRecord.setUseIntegral(integralFee);
            integralUseRecord.setUserId(userId);
            integralUseRecord.setShopId(shopId);
            integralUseRecord.setCode(code);
            integralUseRecord.setTradeId(longResult.take());
//            integralUseRecord.setRemainder(storeIntegral.getAvailableGrade());
            integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
        }

        return storeIntegral;
    }

    /**
     * 退款减少 移除付款是的记录
     *
     * @param map
     * @return
     */
    private StoreIntegral tradeReduceIntegral(Map<String, String> map) {
        String integralCountIds = map.get("integralCountIds");
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        String[] icIds = integralCountIds.split("#");
        if (ObjectUtils.isEmpty(icIds) || icIds.length == 0) {
            log.error("integralCountIds is fail icIds:{}", icIds.length);
            return new StoreIntegral();
        }
        for (String s : icIds) {
            storeIntegralRecordWriteService.updateByThirdAndIntegralId(Long.valueOf(s.substring(0, s.indexOf("_"))), storeIntegral.getId(), IntegralStatus.DELETED.value());
        }
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("updateScanIntegral is fial normalFee:{} frozenFee:{} ", storeIntegral.getAvailableGrade(), storeIntegral.getFrozenGrade());
            return new StoreIntegral();
        }
        return storeIntegral;
    }

    /**
     * 交易成功增加积分记录 确认收货增加积分
     *
     * @param map
     * @return
     */
    private StoreIntegral tradeAddIntegral(Map<String, String> map) {
        String integralCountIds = map.get("integralCountIds");
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            log.error("integralCountIds is fail ids:{}", integralCountIds);
            return new StoreIntegral();
        }
        String[] icIds = integralCountIds.split("#");
        if (ObjectUtils.isEmpty(icIds) || icIds.length == 0) {
            log.error("integralCountIds is fail icIds:{}", icIds.length);
            return new StoreIntegral();
        }
        for (String s : icIds) {
            //增加扫码记录
            StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
            storeIntegralRecord.setIntegralId(storeIntegral.getId());
            storeIntegralRecord.setFaceValue(Long.valueOf(s.substring(s.indexOf("_") + 1)));
            storeIntegralRecord.setRemainValue(Long.valueOf(s.substring(s.indexOf("_") + 1)));
            storeIntegralRecord.setStatus(IntegralStatus.FROZEN.value());
            storeIntegralRecord.setOrigin(3);
            storeIntegralRecord.setThirdId(s.substring(0, s.indexOf("_")));
            storeIntegralRecordWriteService.create(storeIntegralRecord);
        }
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("updateScanIntegral is fial normalFee:{} frozenFee:{} ", storeIntegral.getAvailableGrade(), storeIntegral.getFrozenGrade());
            return new StoreIntegral();
        }
        return storeIntegral;
    }

    /**
     * 退款减少核减数
     * tradeNum
     *
     * @param map
     * @return
     */
    private StoreIntegral reduceTradeNumIntegral(Map<String, String> map) {
        Long integralCount = Long.valueOf(map.get("integralCount"));
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        storeIntegral.setTradeNum(storeIntegral.getTradeNum() - integralCount);
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess()) {
            return new StoreIntegral();
        }

        return storeIntegral;//storeIntegralReadService.findAvailableGradeByUserIdAndShopId(userId, shopId).getResult().get();

    }

    /**
     * 交易成功增加核减数
     * tradeNum
     *
     * @param map
     * @return
     */
    private StoreIntegral addTradeNumIntegral(Map<String, String> map) {
        Long integralCount = Long.valueOf(map.get("integralCount"));
        StoreIntegral storeIntegral = initialize(map);
        if (ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }
        storeIntegral.setTradeNum(storeIntegral.getTradeNum() + integralCount);
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess()) {
            return new StoreIntegral();
        }
        return storeIntegral;//storeIntegralReadService.findAvailableGradeByUserIdAndShopId(userId, shopId).getResult().get();
    }

    /**
     * 扫码增加积分
     *
     * @param map
     * @return
     */
    private StoreIntegral scanAddIntegral(Map<String, String> map) {
        Long userId = Long.valueOf(map.get("userId"));
        Long shopId = Long.valueOf(map.get("shopId"));
        Long integralFee = Long.valueOf(map.get("integralFee"));
        String code = map.get("code");

        StoreIntegral storeIntegral = initialize(map);
        if (storeIntegral == null || ObjectUtils.isEmpty(storeIntegral.getId())) {
            return new StoreIntegral();
        }

        //处理冻结过期的积分
        Response<List<StoreIntegralRecord>> frozenList = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.FROZEN.value());
        if (frozenList.isSuccess() && !frozenList.getResult().isEmpty()) {
            List<Long> expireFrozenNomal = new ArrayList<>();//解冻冻结id
            for (StoreIntegralRecord sr : frozenList.getResult()) {
                if (DateUtil.compareTime(sr.getValidAt(), new Date())) {
                    expireFrozenNomal.add(sr.getId());//解冻的id
                }
            }
            //扫码解冻一段的积分
            for (Long s : expireFrozenNomal) {
                StoreIntegralRecord storeIntegralRecords = new StoreIntegralRecord();
                storeIntegralRecords.setId(s);
                storeIntegralRecords.setStatus(IntegralStatus.NORMAL.value());
                storeIntegralRecordWriteService.update(storeIntegralRecords);
            }
        }
        //更新积分账户
        storeIntegral.setFrozenGrade(0L);
        storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() + storeIntegral.getFrozenGrade() + integralFee);
        storeIntegral.setTotalGrade(storeIntegral.getTotalGrade() + integralFee);
//        storeIntegral.setTradeAll(storeIntegral.getTradeAll() + 1);//扫码次数加一（用于核减数）
//        if(storeIntegral.getTradeNum()>0){
//            storeIntegral.setTradeNum(storeIntegral.getTradeNum()-1);
//        }
        Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("updateScanIntegral is fial normalFee:{} frozenFee:{} integralFee:{}", storeIntegral.getAvailableGrade(), storeIntegral.getFrozenGrade(), integralFee);
            return new StoreIntegral();
        }
        //增加扫码记录
        StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
        storeIntegralRecord.setIntegralId(storeIntegral.getId());
        storeIntegralRecord.setFaceValue(integralFee);
        storeIntegralRecord.setRemainValue(integralFee);
        storeIntegralRecord.setStatus(IntegralStatus.NORMAL.value());
        storeIntegralRecord.setOrigin(0);//用户正常扫码-0  1-商家手动修改积分数
        Either<Long> longResult = storeIntegralRecordWriteService.create(storeIntegralRecord);
        if (longResult.isSuccess() && !ObjectUtils.isEmpty(longResult.take())) {
            //记录扫码记录-mongodb
            IntegralUseRecord integralUseRecord = new IntegralUseRecord();
            integralUseRecord.setCreateAt(System.currentTimeMillis());
            integralUseRecord.setUpdateAt(System.currentTimeMillis());
            integralUseRecord.setStatus(IntegralStatus.AVAILABLE.value());
            integralUseRecord.setUseIntegral(integralFee);
            integralUseRecord.setUserId(userId);
            integralUseRecord.setShopId(shopId);
            integralUseRecord.setCode(code);
            integralUseRecord.setTradeId(longResult.take());
            integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
            //解冻mongodb所有的冻结记录
            integralUseRecordWriteService.updateFrozenUserRecord(userId, shopId, IntegralStatus.AVAILABLE.value());
        }
        return storeIntegral;
    }

    /**
     * 初始化积分账户
     *
     * @param map
     * @return
     */

    private StoreIntegral initialize(Map<String, String> map) {
        long userId = Long.parseLong(map.get("userId"));
        long shopId = Long.parseLong(map.get("shopId"));

        Either<Optional<StoreIntegral>> optionalResult = storeIntegralReadService.findAvailableGradeByUserIdAndShopId(userId, shopId);
        if (!optionalResult.isSuccess()) {
            log.error("查询用户失败！！map:{}", map);
            return new StoreIntegral();
        }
        if (optionalResult.take().isPresent()) {
            return initializeStoreIntegralAccount(optionalResult.take().get(), map);
        } else {
            String integralAccountInitLockName = IntegralLock.create.format(shopId, userId);
            Lock lock = redissonClient.getLock(integralAccountInitLockName);
            lock.lock();
            try {
                Supplier<StoreIntegral> createAccountIntoDB = () -> {
                    StoreIntegral storeIntegral = initializeStoreIntegral(userId, shopId);
                    Either<Long> booleanResult = storeIntegralWriteService.addStoreIntegral(storeIntegral);
                    if (!booleanResult.isSuccess()) {
                        log.error("{} addStoreIntegral-is-fail！！map:{}", LogUtil.getClassMethodName(), map);// throw new JsonResponseException(new Translate("addStoreIntegral-is-fail").toString());
                        return new StoreIntegral();
                    }
                    if (ObjectUtils.isEmpty(booleanResult.take())) {
                        log.error("{} addStoreIntegral-is-id empty！！map:{}", LogUtil.getClassMethodName(), map);// throw new JsonResponseException(new Translate("addStoreIntegral-is-fail").toString());
                        return new StoreIntegral();
                    }
                    return initializeStoreIntegralAccount(storeIntegral, map);
                };
                return storeIntegralReadService.findAvailableGradeByUserIdAndShopId(userId, shopId).orElse(Optional.empty())
                        .map(storeIntegral -> initializeStoreIntegralAccount(storeIntegral, map))
                        .orElseGet(createAccountIntoDB);
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * 提纯所有积分账户的效期和积分确认收货之后的产生积分逻辑
     * 只处理过期和皇家收货产生的积分码
     *
     * @return
     */
    public StoreIntegral initializeStoreIntegralAccount(StoreIntegral storeIntegral, Map<String, String> map) {
        Long userId = Long.valueOf(map.get("userId"));
        Long shopId = Long.valueOf(map.get("shopId"));

        Shop rShop = Optional.ofNullable(shopReadService.findById(shopId).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
        Map<String, String> extras = rShop.getExtra();
        //处理积分扫码模式
        if ((extras != null
                && !extras.isEmpty()
                && extras.containsKey("startIntegral")
                && extras.get("startIntegral").equals("2")
        )) {
            //处理正常过期的积分
            Response<List<StoreIntegralRecord>> expireList = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.NORMAL.value());
            Long normalFee = 0L;//正常积分
            if (expireList.isSuccess() && !expireList.getResult().isEmpty()) {
                List<Long> expire = new ArrayList<>();//过期的Id
                for (StoreIntegralRecord sr : expireList.getResult()) {
                    if (DateUtil.compareTime(sr.getValidAt(), new Date())) {
                        normalFee += sr.getRemainValue();
                    } else {
                        expire.add(sr.getId());//过期的积分id
                    }
                }
                //将过期的积分设置过期
                for (Long s : expire) {
                    StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                    storeIntegralRecord.setId(s);
                    storeIntegralRecord.setStatus(IntegralStatus.EXPIRE.value());
                    storeIntegralRecordWriteService.update(storeIntegralRecord);
                }
            }
            //处理冻结过期的积分
            Response<List<StoreIntegralRecord>> frozenList = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.FROZEN.value());
            Long frozenFee = 0L;//冻结积分
            if (frozenList.isSuccess() && !frozenList.getResult().isEmpty()) {
                List<Long> expireFrozen = new ArrayList<>();//冻结过期的Id
                for (StoreIntegralRecord sr : frozenList.getResult()) {
                    if (DateUtil.compareTime(sr.getValidAt(), new Date())) {
                        frozenFee += sr.getRemainValue();
                    } else {
                        expireFrozen.add(sr.getId());//过期的积分id
                    }
                }
                //将过期的积分设置过期
                for (Long s : expireFrozen) {
                    StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                    storeIntegralRecord.setId(s);
                    storeIntegralRecord.setStatus(IntegralStatus.FROZEN_EXPIRE.value());
                    storeIntegralRecordWriteService.update(storeIntegralRecord);
                    integralUseRecordWriteService.updateUserRecord(userId, shopId, s, IntegralStatus.FROZEN.value(), IntegralStatus.FROZEN_EXPIRE.value());
                }
            }
            storeIntegral.setFrozenGrade(frozenFee);
            storeIntegral.setAvailableGrade(normalFee);
            return storeIntegral;
        }
        //处理积分累计模式
        if ((extras != null
                && !extras.isEmpty()
                && extras.containsKey("startIntegral")
                && extras.get("startIntegral").equals("3")
        )) {
            Response<List<StoreIntegralRecord>> recordList = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.FROZEN.value());
            Long countFee = 0L;
            if (recordList.isSuccess() && !recordList.getResult().isEmpty()) {
                for (StoreIntegralRecord s : recordList.getResult()) {
                    if (!ObjectUtils.isEmpty(s.getThirdId())) {
                        Response<SkuOrder> skuRes = skuOrderReadService.findById(Long.valueOf(s.getThirdId()));
                        if (skuRes.isSuccess()
                                && !ObjectUtils.isEmpty(skuRes.getResult().getId())
                                && Objects.equals(skuRes.getResult().getStatus(), OrderStatus.CONFIRMED.getValue())) {
                            countFee += s.getRemainValue();
                            StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                            storeIntegralRecord.setId(s.getId());
                            storeIntegralRecord.setStatus(IntegralStatus.AVAILABLE.value());
                            storeIntegralRecordWriteService.update(storeIntegralRecord);
                            //记录扫码记录-mongodb
                            IntegralUseRecord integralUseRecord = new IntegralUseRecord();
                            integralUseRecord.setCreateAt(System.currentTimeMillis());
                            integralUseRecord.setUpdateAt(System.currentTimeMillis());
                            integralUseRecord.setStatus(IntegralStatus.AVAILABLE.value());
                            integralUseRecord.setUseIntegral(s.getRemainValue());
                            integralUseRecord.setUserId(userId);
                            integralUseRecord.setShopId(shopId);
                            integralUseRecord.setTradeId(Long.valueOf(s.getThirdId()));
                            integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);
                        }
                    }
                }
            }
            storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() + countFee);
            storeIntegral.setTotalGrade(storeIntegral.getTotalGrade() + countFee);
            return storeIntegral;
        }
        return new StoreIntegral();
    }


    private StoreIntegral initializeStoreIntegral(Long userId, Long shopId) {
        StoreIntegral storeIntegral = new StoreIntegral();
        storeIntegral.setUserId(userId);
        storeIntegral.setShopId(shopId);
        storeIntegral.setTotalGrade(0L);
        storeIntegral.setTradeNum(0l);
        storeIntegral.setTradeAll(0l);
        storeIntegral.setFrozenGrade(0l);
        storeIntegral.setUseGrade(0l);
        storeIntegral.setAvailableGrade(0l);
        storeIntegral.setStatus(IntegralStatus.NORMAL.value());
        return storeIntegral;
    }

}
