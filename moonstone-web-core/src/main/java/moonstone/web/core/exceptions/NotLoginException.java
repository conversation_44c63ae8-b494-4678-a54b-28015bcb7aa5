/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-13
 */
@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class NotLoginException extends RuntimeException {
    private static final long serialVersionUID = 5424651419263810916L;

    private final String target;

    public NotLoginException(String target) {
        this.target = target;
    }

    public String getTarget() {
        return target;
    }
}
