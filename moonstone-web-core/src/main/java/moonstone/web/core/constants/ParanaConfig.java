package moonstone.web.core.constants;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Created by CaiZhy on 2018/10/16.
 */
@Configuration
@Data
public class ParanaConfig {

    @Value("${parana.app_backend.url:http://mall.yang800.com/backend/}")
    private String appUrlBackend;
    //h5
    @Value("${parana.h5.url}")
    private String paranaH5Url;

    //但丁商城
    @Value("${parana.wxa.url}")
    private String paranaWxaUrl;

    @Value("${parana.wxa.appId}")
    private String paranaWxaAppId;

    @Value("${parana.wxa.appSecret}")
    private String paranaWxaAppSecret;

    //微分销商城店主端
    @Value("${parana.we.seller.url}")
    private String paranaWeSellerUrl;

    @Value("${parana.we.seller.appId}")
    private String paranaWeSellerAppId;

    @Value("${parana.we.seller.appSecret}")
    private String paranaWeSellerAppSecret;

    //微分销商城消费端
    @Value("${parana.we.buyer.url}")
    private String paranaWeBuyerUrl;

    @Value("${parana.we.buyer.appId}")
    private String paranaWeBuyerAppId;

    @Value("${parana.we.buyer.appSecret}")
    private String paranaWeBuyerAppSecret;

    /// 门店端
    @Value("${parana.substore.appId}")
    private String paranaSubStoreAppId;

    @Value("${parana.substore.appSecret}")
    private String paranaSubStoreAppSecret;

    /// 门店端
    @Value("${parana.levelDistribution.appId}")
    private String paranaLevelDistributionAppId;

    @Value("${parana.levelDistribution.appSecret}")
    private String paranaLevelDistributionAppSecret;
    //访问的url
    @Value("${m.mall.jifen.url}")
    private String paranaLevelDistributionUrl;
    //创建文件夹
    @Value("${m.mall.jifen.path}")
    private String paranaLevelDistributionPath;
}
