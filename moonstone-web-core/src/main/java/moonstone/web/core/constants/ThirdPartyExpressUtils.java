package moonstone.web.core.constants;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@PropertySource(value = {"classpath:/thirdPartyExpressUtils.properties"})
@ConfigurationProperties(prefix = "third-party-express")
public class ThirdPartyExpressUtils {
    private Map<String,String> Y800Utils;
}
