package moonstone.web.core.constants;

import moonstone.common.event.EnvEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.concurrent.CompletableFuture;

@Configuration
public class EnvironmentConfig {
    @Value("${environment}")
    private String env;
    @Value("${application.name}")
    private String app;

    public String getApp() {
        return app;
    }

    public final static CompletableFuture<String> ENV = new CompletableFuture<>();

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public boolean isOnline() {
        return env.equalsIgnoreCase("online");
    }

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    void contextRefreshed() {
        applicationContext.publishEvent(new EnvEvent(isOnline(), getEnv()));
        ENV.complete(env);
    }
}
