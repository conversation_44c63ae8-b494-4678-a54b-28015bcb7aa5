/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-31
 *
 * <AUTHOR>
 */
public class AppConstants {

    /**
     * 用户的Session在Jedis中的缓存
     */
    public static final String SESSION_USER_REG_PREFIX = "[user_map]";
    /**
     * 登录用户ID
     */
    public static final String SESSION_USER_ID = "userId";
    /**
     * 登录的店铺Id(如果是店铺帐号)
     */
    public static final String SESSION_SHOP_ID = "shopId";
    public static final String COOKIE_SHOP_ID = "shopId";

    /**
     * 是由OMS登录的
     */
    public static final String SESSION_LOGIN_BY_OMS = "oms_login";
    /**
     * 登录用户openId
     */
    public static final String SESSION_OPEN_ID = "openId";

    public static final String SESSION_APP_TYPE = "appType";

    /**
     * 手机登录验证码
     */
    public static final String SESSION_SMS_CODE_LOGIN = "sms-code-login";

    /**
     * 手机注册验证码
     */
    public static final String SESSION_SMS_CODE_REG = "sms-code-reg";

    /**
     * 修改手机号验证码
     */
    public static final String SESSION_SMS_CODE_CHG_MOB = "sms-code-chg-mob";

    /**
     * 修改手机号给原手机号发验证码
     */
    public static final String SESSION_SMS_CODE_CHG_MOB_BY_OLD = "sms-code-chg-mob-by-old";

    /**
     * 手机重置密码
     */
    public static final String SESSION_SMS_CODE_RESET_PW = "sms-code-reset-pw";

    /**
     * 微分销店主端设置提现密码验证码
     */
    public static final String SESSION_SMS_CODE_SET_WALLET_PW = "sms-code-set-wallet-pw";

    /**
     * 微分销店主端实名认证验证码
     */
    public static final String SESSION_SMS_CODE_CERTIFICATE = "sms-code-certificate";
}
