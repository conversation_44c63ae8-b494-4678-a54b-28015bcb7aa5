package moonstone.web.core.design;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.msg.common.StringUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.R;
import moonstone.file.model.ShopFile;
import moonstone.file.model.ShopFileGroup;
import moonstone.file.service.ShopFileReadService;
import moonstone.file.service.ShopFileWriteService;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.design.utils.PageUtils;
import moonstone.web.core.files.config.UploadConfig;
import moonstone.web.core.files.service.OSSClientService;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;


/**
 * 文件库记录表
 */
@Slf4j
@RestController
@RequestMapping("/api/{shopId}/upload/library")
public class ShopFileController {
    private List<String> allowedFileTypes;

    @Autowired
    private UploadConfig uploadConfig;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private OSSClientService ossClientService;
    @RpcConsumer
    private ShopFileReadService shopFileReadService;
    @RpcConsumer
    private ShopFileWriteService shopFileWriteService;

    @PostConstruct
    public void init() {
        if (StringUtils.isNotBlank(uploadConfig.getAllowedFileTypes())) {
            String[] types = uploadConfig.getAllowedFileTypes().split(",");
            allowedFileTypes = new ArrayList<>(types.length);
            allowedFileTypes.addAll(Arrays.asList(types));
        }
    }

    /**
     * 列表
     */
    @RequestMapping("/fileList")
    public R list(@PathVariable("shopId") Long shopId, @RequestParam Long groupId, @RequestParam(required = false) Integer page) {
        page = page == null ? 1 : page;
        groupId = groupId == null || groupId == -1 ? null : groupId;
        int pageSize = 12;
        Response<Paging<ShopFile>> shopFileResponse = shopFileReadService.getShopFileByGroupId(shopId, groupId, "image", page, pageSize);
        Paging<ShopFile> shopFilePaging = shopFileResponse.getResult();
        PageUtils pageUtils = new PageUtils(shopFilePaging.getData(), (shopFilePaging.getTotal()).intValue(), pageSize, page);
        Response<List<ShopFileGroup>> response = shopFileReadService.getShopFileGroupByShopId(shopId, "image");
        Map<String, Object> data = new HashMap<>();
        data.put("file_list", pageUtils);
        data.put("group_list", response.getResult());
        return R.ok().add("data", data);
    }

    /**
     * 列表
     */
    @RequestMapping("/fileList-page")
    public R listAll(@PathVariable("shopId") Long shopId, @RequestParam(defaultValue = "image") String type, @RequestParam(defaultValue = "0") int page
            , @RequestParam(defaultValue = "20") int pageSize) {
        //Long userId=getLoginUserId();
        Response<Paging<ShopFile>> shopFileResponse = shopFileReadService.getShopFileByShopIdAll(shopId, type, page, pageSize);

        if (!shopFileResponse.isSuccess()) {
            log.error("[op:uploadImg] upload file to createShopFiles failed, cause:{}", shopFileResponse.getError());
            return R.error();
        }
        List<String> shopUrlList = new ArrayList<>();
        for (ShopFile responses : shopFileResponse.getResult().getData()) {
            if (StringUtil.isNotBlank(responses.getFileUrl())) {
                shopUrlList.add(responses.getFileUrl());
            }
        }
        return R.ok().add("data", shopUrlList).add("page", page).add("pageSize", pageSize)
                .add("pageTotal", shopFileResponse.getResult().getTotal())
                .add("type", type);

    }

    /**
     * 批量分组
     */
    @RequestMapping("/moveFiles")
    public R moveFiles(@PathVariable("shopId") Long shopId, @RequestParam("groupId") Long groupId, @RequestParam(value = "fileIds[]") Long[] fileIds) {
        shopFileWriteService.moveShopFiles(shopId, groupId, Arrays.asList(fileIds));
        return R.ok();
    }


    /**
     * 删除
     */
    @RequestMapping("/deleteFiles")
    public R delete(@PathVariable("shopId") Long shopId, @RequestParam(value = "fileIds[]") Long[] fileIds) {
        shopFileWriteService.deleteShopFiles(shopId, Arrays.asList(fileIds));

        return R.ok();
    }
    /**
     * 移除图库--by fileUrl
     */
    @RequestMapping("/deleteFilesByUrl")
    public R deleteByFileUrl(@PathVariable("shopId") Long shopId, @RequestParam(value = "url[]") String[] url) {
        if(org.springframework.util.ObjectUtils.isEmpty(url)){
            return R.error(-1,"url组空参数");
        }
        for(String fileUrl:url){
            shopFileWriteService.deleteShopFilesByFileUrl(shopId, fileUrl);
        }

        return R.ok();
    }

    @RequestMapping("/image")
    public R uploadFile(@PathVariable("shopId") Long shopId, @RequestParam Long groupId, @RequestParam(value = "iFile", required = false) MultipartFile[] files) {
        List<ShopFile> shopFiles = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                shopFiles.add(uploadMultiFile(groupId, shopId, file));
            } catch (Exception e) {
                log.error("[op:uploadImg] upload file to OSS failed, cause:{}", Throwables.getStackTraceAsString(e));
                throw new JsonResponseException(500, "upload.failed");
            }
        }
        Response<List<ShopFile>> response = shopFileWriteService.createShopFiles(shopFiles);

        return R.ok().add("data", response.getResult());
    }

    @RequestMapping("/uploadFile")
    public R uploadFileUtils(@PathVariable("shopId") Long shopId, @RequestParam(defaultValue = "image", required = false) String type,
                             @RequestParam(value = "iFile", required = false) MultipartFile[] files, HttpServletRequest request) {
        log.info("[uploadFileUtils] shopId:{}", shopId);
        Long groupId = 0L;
        List<ShopFile> shopFiles = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                shopFiles.add(uploadMultiFileS(groupId, shopId, file, type));
            } catch (Exception e) {
                log.error("[op:uploadImg] upload file to OSS failed, cause:{}", Throwables.getStackTraceAsString(e));
                throw new JsonResponseException(500, "upload.failed");
            }
        }
        log.info("[shopFiles] shopFiles:{}", shopFiles.get(0).getFileUrl());
        Response<List<ShopFile>> response = shopFileWriteService.createShopFiles(shopFiles);
        if (!response.isSuccess()) {
            log.error("[op:uploadImg] upload file to createShopFiles failed, cause:{}", response.getError());
            return R.error();
        }
        List<String> shopUrlList = new ArrayList<>();

        for (ShopFile responses : response.getResult()) {
            shopUrlList.add(responses.getFileUrl());
        }
        if (shopUrlList.size() == 1) {
            return R.ok().add("data", shopUrlList.get(0));
        } else {
            return R.ok().add("data", shopUrlList);
        }
    }


    @RequestMapping("/uploadFileVideo")
    public R uploadFileUtilsProtocol(@PathVariable("shopId") Long shopId, @RequestParam(defaultValue = "image", required = false) String type,
                                     @RequestParam(value = "iFile", required = false) MultipartFile files, HttpServletRequest request) {
        // Long userId=getLoginUserId();
        log.info("[uploadFileUtils] shopId:{}", shopId);
        Long groupId = 0L;
        List<ShopFile> shopFiles = new ArrayList<>();
        try {
            shopFiles.add(uploadMultiFileSVideo(groupId, shopId, files, type));
        } catch (Exception e) {
            log.error("[op:uploadImg] upload file to OSS failed, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(500, "upload.failed");
        }
        log.info("[shopFiles] shopFiles:{}", shopFiles.get(0).getFileUrl());
        Response<List<ShopFile>> response = shopFileWriteService.createShopFiles(shopFiles);
        if (!response.isSuccess()) {
            log.error("[op:uploadImg] upload file to createShopFiles failed, cause:{}", response.getError());
            return R.error();
        }
        List<String> shopUrlList = new ArrayList<>();

        for (ShopFile responses : response.getResult()) {
            shopUrlList.add(responses.getFileUrl());
        }
        if (shopUrlList.size() == 1) {
            return R.ok().add("data", shopUrlList.get(0));
        } else {
            return R.ok().add("data", shopUrlList);
        }
    }

    private ShopFile uploadMultiFile(Long groupId, Long shopId, MultipartFile file) {
        ShopFile shopFile = new ShopFile();
        shopFile.setGroupId(groupId == -1 ? 0 : groupId);
        shopFile.setShopId(shopId);
        shopFile.setIsDelete(0);
        shopFile.setIsUser(0);
        if (file == null) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file empty");
            throw new JsonResponseException(507, "upload.file.empty");
        }
        log.debug("[op:uploadMultiFile] filename={}", file.getOriginalFilename());
        String extName = FilenameUtils.getExtension(file.getOriginalFilename());
        if (extName == null || checkFileType(extName)) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file extension wrong");
            throw new JsonResponseException(508, "upload.file.type.illegal");
        }
        // 生成key
        for (String ext : ImageIO.getReaderFormatNames()) {
            if (extName.toUpperCase(Locale.ROOT).endsWith(ext.toUpperCase(Locale.ROOT))) {
                extName = "webp";
                break;
            }
        }
        String key = ossClientService.genKeyByFileName(file.getOriginalFilename(), extName);
        // 上传
        try {
            shopFile.setFileName(key);
            shopFile.setExtension(extName);
            shopFile.setFileType("image");
            shopFile.setStorage("aliyun");
            shopFile.setFileSize(file.getSize());
            String url = ossClientService.upload(environmentConfig.getEnv(), key, file.getInputStream());
            shopFile.setFileUrl(url);
            return shopFile;
        } catch (IOException e) {
            log.error("[op:uploadMultiFile] upload file to OSS failed, cause: {}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(500, "upload.failed");
        }
    }

    /**
     * 上传文件
     */
    private ShopFile uploadMultiFileS(Long groupId, Long shopId, MultipartFile file, String type) {
        ShopFile shopFile = new ShopFile();
        shopFile.setGroupId(groupId == -1 ? 0 : groupId);
        shopFile.setShopId(shopId);
        shopFile.setIsDelete(0);
        shopFile.setIsUser(0);
        if (file == null) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file empty");
            throw new JsonResponseException(507, "upload.file.empty");
        }
        log.debug("[op:uploadMultiFile] filename={}", file.getOriginalFilename());
        String extName = FilenameUtils.getExtension(file.getOriginalFilename());
        if (extName == null || checkFileType(extName)) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file extension wrong");
            throw new JsonResponseException(508, "upload.file.type.illegal");
        }
        // 生成key
        String originFileName = file.getOriginalFilename();
        String key = ossClientService.genKeyByFileName(file.getOriginalFilename(), FilenameUtils.getExtension(originFileName));
        // 上传
        try {
            shopFile.setFileName(key);
            shopFile.setExtension(extName);
            shopFile.setFileType(type);
            shopFile.setStorage("aliyun");
            shopFile.setFileSize(file.getSize());
            String url = ossClientService.upload(environmentConfig.getEnv(), key, file.getInputStream());
            shopFile.setFileUrl(url);
            return shopFile;
        } catch (IOException e) {
            log.error("[op:uploadMultiFile] upload file to OSS failed, cause: {}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(500, "upload.failed");
        }
    }

    /**
     * 上传文件Video
     */
    private ShopFile uploadMultiFileSVideo(Long groupId, Long shopId, MultipartFile file, String type) {
        ShopFile shopFile = new ShopFile();
        shopFile.setGroupId(groupId == -1 ? 0 : groupId);
        shopFile.setShopId(shopId);
        shopFile.setIsDelete(0);
        shopFile.setIsUser(0);
        if (file == null) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file empty");
            throw new JsonResponseException(507, "upload.file.empty");
        }
        log.debug("[op:uploadMultiFile] filename={}", file.getOriginalFilename());
        String extName = FilenameUtils.getExtension(file.getOriginalFilename());
        if (extName == null || checkFileType(extName)) {
            log.error("[op:uploadMultiFile] upload file to OSS failed.file extension wrong");
            throw new JsonResponseException(508, "upload.file.type.illegal");
        }
        // 生成key
        String originFileName = file.getOriginalFilename();
        String key = ossClientService.genKeyByFileName(file.getOriginalFilename(), FilenameUtils.getExtension(originFileName));
        // 上传
        try {
            shopFile.setFileName(key);
            shopFile.setExtension(extName);
            shopFile.setFileType(type);
            shopFile.setStorage("aliyun");
            shopFile.setFileSize(file.getSize());
            String url = ossClientService.uploadVideo(environmentConfig.getEnv(), key, file, type);
            shopFile.setFileUrl(url);
            return shopFile;
        } catch (Exception e) {
            log.error("[op:uploadMultiFile] upload file to OSS failed, cause: {}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(500, "upload.failed");
        }
    }

    /**
     * 检查文件的扩展名是否正确
     */
    private boolean checkFileType(String extName) {
        return !allowedFileTypes.contains(extName.toLowerCase());
    }
}
