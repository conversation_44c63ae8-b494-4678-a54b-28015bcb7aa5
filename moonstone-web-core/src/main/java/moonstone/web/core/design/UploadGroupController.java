package moonstone.web.core.design;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import moonstone.common.utils.R;
import moonstone.file.model.ShopFileGroup;
import moonstone.file.service.ShopFileReadService;
import moonstone.file.service.ShopFileWriteService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * 文件库分组记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-16 09:56:22
 */
@RestController
@RequestMapping("/api/{shopId}/uploadGroup")
public class UploadGroupController {
    @RpcConsumer
    private ShopFileWriteService shopFileWriteService;
    @RpcConsumer
    private ShopFileReadService shopFileReadService;


    /**
     * 保存
     */
    @RequestMapping("/addGroup")
    public R save(@PathVariable("shopId") Long shopId, @RequestParam Map<String, String> params){
            ShopFileGroup shopFileGroup = new ShopFileGroup();
            shopFileGroup.setGroupName(params.get("groupName"));
            shopFileGroup.setGroupType(params.get("groupType"));
            shopFileGroup.setShopId(shopId);
             shopFileGroup.setStatus(1);
            shopFileGroup.setSort(100);
            shopFileWriteService.createShopFileGroup(shopFileGroup);

        return R.ok("添加分组成功").add("data", shopFileGroup);
    }

    /**
     * 修改
     */
    @RequestMapping("/editGroup")

    public R update(@PathVariable("shopId") Long shopId, @RequestParam Map<String, String> params){

        Response<ShopFileGroup> response = shopFileReadService.getShopFileGroupById(shopId,Long.valueOf(params.get("groupId")));
            ShopFileGroup shopFileGroup =response.getResult();
            shopFileGroup.setGroupName(params.get("groupName"));
            shopFileWriteService.updateShopFileGroup(shopFileGroup);

        return R.ok("修改分组成功");
    }

    /**
     * 删除
     */
    @RequestMapping("/deleteGroup")
    public R delete(@RequestParam Map<String, String> params){
        shopFileWriteService.deleteShopFileGroup(Long.valueOf(params.get("groupId")));

        return R.ok("删除分组成功");
    }

}
