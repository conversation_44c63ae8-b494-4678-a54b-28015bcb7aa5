package moonstone.web.core.design.view;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by miluo on 2019/3/21.
 * <AUTHOR>
public class Goods implements Serializable {
    private static final long serialVersionUID = -7825575290564173461L;
    @Getter
    @Setter
    private  Long goodsId;
    @Getter
    @Setter
    private  String goodsName;
    @Getter
    @Setter
    private  String image;
    @Getter
    @Setter
    private  String goodsPrice;
    @Getter
    @Setter
    private  String linePrice;
    @Getter
    @Setter
    @JsonIgnore
    private  String categoryName;
    @Getter
    @Setter
    @JsonIgnore
    private Date createdAt;




}
