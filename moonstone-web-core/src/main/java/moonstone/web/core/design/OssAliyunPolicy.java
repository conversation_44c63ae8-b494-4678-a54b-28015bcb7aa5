package moonstone.web.core.design;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.R;
import moonstone.common.utils.UUID;
import moonstone.common.utils.UserUtil;
import moonstone.file.model.ShopFile;
import moonstone.file.model.UploadInfoModel;
import moonstone.file.service.ShopFileWriteService;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.files.config.OSSConfig;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/11 13:09
 */
@Slf4j
@RestController
@RequestMapping("/api/oss/upload/")
public class OssAliyunPolicy {

    @Autowired
    private OSSConfig ossConfig;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @RpcConsumer
    private ShopFileWriteService shopFileWriteService;

    @RequestMapping("/getToken")
    public R doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        Long userId = UserUtil.getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            return R.error(-1001, "为登录");
        }
        String accessId = ossConfig.getAppKey();//"LTAIzH2kt3oukSR9"; // 请填写您的AccessKeyId。
        String accessKey = ossConfig.getAppSecret(); //"t1A0AEkytGPCTMlciV6EnEmzAJAMIS"; // 请填写您的AccessKeySecret。
        String endpoint = "oss-cn-hangzhou.aliyuncs.com"; // 请填写您的 endpoint。 ossConfig.getEndpoint();//
        String bucket = ossConfig.getBucketName();//"dante-img"; // 请填写您的 bucketname 。
        String host = "https://" + bucket + "." + endpoint; // host的格式为 bucketname.endpoint
        String dir = environmentConfig.getEnv();//"dev/"; // 用户上传文件时指定的前缀。
        Map<String, String> respMap = new LinkedHashMap<String, String>();
        OSSClient client = new OSSClient(endpoint, accessId, accessKey);
        try {
//            long expireTime =30; //toCurrentTime();
            long expireEndTime = System.currentTimeMillis() + 2592000000L;//+ expireTime* 10000000; //目前一个月的有效期
            Date expiration = new Date(expireEndTime);
            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 209715200);//1048576   1048576000   209715200 200M
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

            String postPolicy = client.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = client.calculatePostSignature(postPolicy);

            respMap.put("accessid", accessId);
            respMap.put("policy", encodedPolicy);
            respMap.put("signature", postSignature);
            respMap.put("dir", dir);
            respMap.put("host", host);
            respMap.put("expire", String.valueOf(expireEndTime / 1000));
//            JSONObject ja1 = JSONObject.fromObject(respMap);
//            PrintWriter pw = response.getWriter();
//            pw.write(ja1.toString());
        } catch (Exception e) {
            log.error("[op:getToken] upload file  getToken fail, cause: {}", Throwables.getStackTraceAsString(e));
            return R.error(-1, "系统异常");
        }
        return R.ok().add("data", JSONObject.fromObject(respMap));
    }

    public static Long toCurrentTime() {
        Calendar c = new GregorianCalendar();
        Date date = new Date();
        c.setTime(date);//设置参数时间
        c.add(Calendar.SECOND, 100);//把日期往后增加SECOND 秒.整数往后推,负数往前移动
        return c.getTimeInMillis();
    }

    @RequestMapping(value = "/save-info/{shopId}", method = RequestMethod.POST)
    public R uploadFile(@PathVariable("shopId") Long shopId, @RequestBody UploadInfoModel uploadInfoModel) {

        try {
            Long userId = UserUtil.getUserId();
            if (ObjectUtils.isEmpty(userId)) {
                return R.error(-1001, "未登录");
            }
            if (ObjectUtils.isEmpty(uploadInfoModel.getUrl())) {
                return R.error(-1, "url不能为空");
            }
//            String extName = "";
            String url = uploadInfoModel.getUrl();
//            extName = url.substring(url.lastIndexOf("/") + 1);
            String type = url.substring(url.lastIndexOf(".") + 1);
//            List<String> typeList=Arrays.asList("jpeg","jpg","gif","png");
            String fileType = "";
            if (ObjectUtils.isEmpty(uploadInfoModel.getType())) {
                if (Objects.equals(type, "mp4")) {
                    fileType = "mp4";
                } else {
                    fileType = "image";
                }
            }

            ShopFile shopFile = new ShopFile();
            shopFile.setGroupId(0L);
            shopFile.setShopId(shopId);
            shopFile.setIsDelete(0);
            shopFile.setIsUser(0);
            shopFile.setFileName(UUID.randomUUID().toString());
            shopFile.setExtension(type);
            if (!ObjectUtils.isEmpty(uploadInfoModel.getType())) {
                shopFile.setFileType(uploadInfoModel.getType());
            } else {
                shopFile.setFileType(fileType);
            }
            shopFile.setStorage("aliyun");
            shopFile.setFileSize(0l);
            shopFile.setFileUrl(uploadInfoModel.getUrl());
            Response<ShopFile> response = shopFileWriteService.createShopFile(shopFile);
            if (!response.isSuccess()) {
                return R.error(-1, "插入失败");
            }
        } catch (Exception e) {
            log.error("[op:getToken] upload file  getToken fail, cause: {}", Throwables.getStackTraceAsString(e));
            return R.error(-1, "系统异常");
        }
        return R.ok();
    }

}
