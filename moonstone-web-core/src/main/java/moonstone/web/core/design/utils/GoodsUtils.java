package moonstone.web.core.design.utils;

import moonstone.item.model.Item;
import moonstone.web.core.design.view.Goods;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by miluo on 2019/3/22.
 */
public  class GoodsUtils {
    /**
     * 2位小数
     */
    public static final DecimalFormat DECIMAL_FMT_2 = new DecimalFormat("0.00");

    public static List<Goods> getGoods(List<Item> items){
        List<Goods> goodsList= new ArrayList<>();
        for (Item item:items){
            Goods goods= new Goods();
            goods.setGoodsId(item.getId());
            goods.setGoodsName(item.getName());
            goods.setImage(item.getMainImage());
            if (item.getLowPrice()!=null) {
                goods.setGoodsPrice(DECIMAL_FMT_2.format(item.getLowPrice() / 100));
            }
            if(item.getHighPrice()!=null) {
                goods.setLinePrice(DECIMAL_FMT_2.format(item.getHighPrice() / 100));
            }
            goods.setCreatedAt(item.getCreatedAt());
            goodsList.add(goods);
        }
        return goodsList;
    }
}
