package moonstone.web.core.design;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.common.utils.R;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.web.core.design.utils.GoodsUtils;
import moonstone.wxapp.model.WxappPage;
import moonstone.wxapp.service.WxappPageReadService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * 微信小程序diy页面表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-16 09:56:22
 */
@RestController
@RequestMapping("/api/pages")
public class WxappPageApiController {
    @RpcConsumer
    private WxappPageReadService wxappPageReadService;
    @RpcConsumer
    private ItemReadService itemReadService;
    @RpcConsumer
    private ShopCategoryItemReadService shopCategoryItemReadService;


    /**
     * 删除
     */
    @RequestMapping("/index/{shopId}")
    public R index(@PathVariable Long shopId){
        Response<WxappPage>  response = wxappPageReadService.findHomePageByShopId(shopId);
        WxappPage wxappPage=response.getResult();
        //todo  异常逻辑处理
        return R.ok().add("data", getPageData(wxappPage));
    }
    @RequestMapping("/custom/index")
    public R custom(@RequestParam Long pageId){
        Response<WxappPage>  response = wxappPageReadService.findHomePageByShopId(pageId);
        WxappPage wxappPage=response.getResult();
        JSONObject pageJson= JSON.parseObject(wxappPage.getPageData());
        //todo  异常逻辑处理
        return R.ok();
    }


    private String getPageData(WxappPage wxappPage)
    {
        JSONObject pageJson= JSON.parseObject(wxappPage.getPageData());
        pageJson.getJSONObject("page");
        JSONArray items=pageJson.getJSONArray("items");
        for (Object item:items){
            JSONObject  itemJSON= (JSONObject)item;
            if ("window".equals(itemJSON.get("type"))){
                itemJSON.put("data",itemJSON.getJSONObject("data"));

            }else if ("goods".equals(itemJSON.get("type"))){

                getGoodList(wxappPage.getShopId(),itemJSON);
            }else if("coupon".equals(itemJSON.get("type"))){
                getCouponList(itemJSON);
            }
            else if("article".equals(itemJSON.get("type"))){
                getArticleList(wxappPage.getShopId(),itemJSON);
            }else if("special".equals(itemJSON.get("type"))){
                itemJSON.put("data",getSpecialList(wxappPage.getShopId(),itemJSON));
            }
        }
       return  null;
    }
    private JSONArray getGoodList(Long shopId,JSONObject item){
                if ("choice".equalsIgnoreCase(item.getJSONObject("params").getString("source"))){
                    JSONObject data= item.getJSONObject("data");
                   Set<String> goodsIdSet=data.keySet();
                   List<Long> goodsIdList= new ArrayList<Long>();
                   for (String goodsIds:goodsIdSet){
                       goodsIdList.add(Long.valueOf(goodsIds));

                   }
                    Response<List<Item>> response=itemReadService.findByIdsAndStatus(goodsIdList,1);
                    item.put("data", GoodsUtils.getGoods(response.getResult()));

                }else {
                    Long categoryId = item.getJSONObject("params").getJSONObject("auto").getLong("categroy");
                    String sort = item.getJSONObject("params").getJSONObject("auto").getString("goodsSort");
                    int showNum = item.getJSONObject("params").getJSONObject("auto").getIntValue("showNum");
                    //Todo 排序目前不支持
                    Response<Paging<Long>>  response=shopCategoryItemReadService.findByShopIdAndCategoryId(shopId,categoryId,1,showNum);
                    Response<List<Item>> itemResponse=itemReadService.findByIdsAndStatus(response.getResult().getData(),1);
                    item.put("data", GoodsUtils.getGoods(itemResponse.getResult()));
                }

                return new JSONArray();
            }

            private JSONArray getArticleList(Long shopId,JSONObject item){
//                Long categoryId = item.getJSONObject("params").getJSONObject("auto").getLong("categroy");
//                int showNum = item.getJSONObject("params").getJSONObject("auto").getIntValue("showNum");
//                Response<List<Article>>  response=articleReadService.findByCategoryIdAndShopId(shopId,categoryId,showNum);
//                item.put("data",response.getResult());
                //JSONArray jsonArray =
                return new JSONArray();
            }

            private JSONArray getSpecialList(Long ShopId,JSONObject item){

                return new JSONArray();
            }
            private JSONArray getCouponList(JSONObject item){

                return new JSONArray();
            }

}
