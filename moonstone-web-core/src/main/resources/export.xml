<?xml version="1.0" encoding="UTF-8"?>
<tables>
    <item display="门店详细信息" className="SubStoreFullData">
        <column width="2000" display="服务商名称" name="serviceName"/>
        <column width="2000" display="服务商地址" name="serviceAddress"/>
        <column width="2000" display="服务商手机号" name="serviceMobile"/>
        <column width="2000" display="授权地区" name="authAddress"/>
        <column width="2000" display="审核状态" name="authStatus_"/>
        <column width="2000" display="门店id" name="id"/>
        <column width="8500" display="门店名称" name="name"/>
        <column width="3500" display="手机号码" name="mobile"/>
        <column width="2000" display="省" name="province"/>
        <column width="2000" display="市" name="city"/>
        <column width="2000" display="县" name="county"/>
        <column width="3000" display="银行名称" name="bankName"/>
        <column width="3000" display="银行账户" name="bankNo"/>
        <column width="3000" display="法人名称" name="nameInLaw"/>
        <column width="6000" display="法人身份证：正面" name="frontImg" img="true"/>
        <column width="6000" display="法人身份证：国徽" name="backImg" img="true"/>
        <column width="6000" display="食品许可证" name="foodSellAllowImg" img="true"/>
        <column width="6000" display="营业执照" name="businessImg" img="true"/>
        <column width="2000" display="订单数量" name="orderNum"/>
        <column width="2000" display="订单总额" name="feeSum_"/>
        <column width="2000" display="累计收益" name="profit_"/>
        <column width="2000" display="待收益" name="foreseeProfit_"/>
        <column width="2000" display="已收益" name="availedWithDrawFee_"/>
        <column width="2000" display="已提现金额" name="withDrawFee_"/>
        <column width="2000" display="导购人数" name="guiderNum"/>
        <column width="2000" display="创建时间" name="createdAt" format="isoDate"/>
        <column width="2000" display="审核时间" name="authAt" format="isoDate"/>
    </item>
    <item display="商家商品列表" className="SkuWithItemForList">
        <column width="1800" display="商品ID" name="itemId"/>
        <column width="5000" display="商品代码(货号)" name="itemCode"/>
        <column width="3500" display="商品名称" name="name"/>
        <column width="1800" display="店铺ID" name="shopId"/>
        <column width="2500" display="店铺名称" name="shopName"/>
        <column width="1800" display="是否保税（1为保税，0位完税）" name="isBonded"/>
        <column width="2000" display="是否为第三方商品（1：第三方商品，0：为自建商品）" name="isThirdPartyItem"/>
        <column format="price" width="1800" display="最低实际售价" name="lowPrice"/>
        <column format="price" width="1800" display="最高实际售价" name="highPrice"/>
        <column width="3500" display="微信小程序链接" name="wxaUrl"/>
        <column width="1800" display="skuId" name="id"/>
        <column width="1800" display="sku编码" name="skuCode"/>
        <column width="3000" display="外部sku编号" name="outerSkuId"/>
        <column format="price" width="1800" display="sku售价" name="price"/>
        <column format="price" width="1800" display="sku微分销价" name="distributionPrice"/>
        <column format="price" width="1800" display="sku微分销收益" name="profit"/>
        <column width="3500" display="sku销售属性" name="attrsStr"/>
        <column width="1800" display="sku计量单位数量" name="unitQuantity"/>
        <column width="1800" display="sku状态" name="skuStatus"/>
        <column width="1800" display="sku库存" name="stockQuantity"/>
        <column width="1800" display="商品库存" name="itemStockQuantity"/>
        <column width="1800" display="商品销量" name="saleQuantity"/>
        <column width="1000" display="是否微分销" name="sellInWeShop"/>
        <column width="1800" display="商品状态" name="itemStatus"/>
        <column format="isoDate" width="2500" display="创建时间" name="createdAt"/>
        <column format="isoDate" width="2500" display="修改时间" name="updatedAt"/>
    </item>
    <item display="微分销店铺列表" className="WeShopForSeller">
        <column width="1800" display="微分销店铺ID" name="weShopId"/>
        <column width="5000" display="微分销店铺名称" name="weShopName"/>
        <column width="3000" display="微分销店主手机号" name="mobile"/>
        <column format="isoDate" width="5000" display="注册时间" name="registeredAt"/>
        <column format="price" width="2000" display="可用余额" name="balance"/>
        <column format="price" width="2000" display="代收收益" name="collectedProfit"/>
        <column width="3000" display="邀请人手机号" name="inviteMobile"/>
    </item>
    <item display="订单明细报表" className="SettleOrderDetail">
        <column width="1800" display="ID" name="id"/>
        <column format="isoDate" width="1800" display="对账时间" name="checkAt"/>
        <column format="isoDate" width="7200" display="支付时间" name="paidAt"/>
        <column width="1800" display="订单ID" name="orderId"/>
        <column width="1800" display="店铺ID" name="sellerId"/>
        <column format="price" width="1800" display="应收货款" name="originFee"/>
        <column format="price" width="1800" display="商家优惠" name="sellerDiscount"/>
        <column format="price" width="1800" display="平台优惠" name="platformDiscount"/>
        <column format="price" width="1800" display="运费" name="shipFee"/>
        <column format="price" width="1800" display="运费优惠" name="shipFeeDiscount"/>
        <column format="price" width="1800" display="税费" name="tax"/>
        <column width="1800" display="差价" name="diffFee"/>
        <column format="price" width="1800" display="实收货款" name="actualPayFee"/>
        <column format="price" width="1800" display="支付平台佣金" name="gatewayCommission"/>
        <column format="price" width="1800" display="平台佣金" name="platformCommission"/>
        <column format="price" width="1800" display="商家应收" name="sellerReceivableFee"/>
    </item>
    <item display="退款订单导出表" className="RefundExportVo">
        <column width="8400" display="退款单号" name="outId"/>
        <column width="4800" display="订单号" name="orderId"/>
        <column width="6000" display="申报推送单号" name="declaredId"/>
        <column width="2800" display="店铺名" name="shopName"/>
        <column width="2000" display="订单状态" name="coStatus"/>
        <column width="4000" display="支付流水号" name="paySerialNo"/>
        <column width="4000" display="下单时间" name="createAt"/>
        <column width="4000" display="支付时间" name="paidAt"/>
        <column width="1800" display="总价" name="sellFee"/>
        <!--  <column width="2500" display="买家微信昵称" name="wxBuyerName"/>  -->
        <column width="1800" display="微信openId" name="openId"/>
        <column width="1800" display="收件人" name="receiveUserName"/>
        <column width="3800" display="手机号" name="mobile"/>
        <column width="4800" display="收货地址" name="address"/>
        <column width="4000" display="发货时间" name="shipmentTime"/>
        <column width="2800" display="商品名称" name="itemName"/>
        <column width="1800" display="商品价格" name="originFee"/>
        <column width="4000" display="退款额度" name="coFee"/>
        <column width="1800" display="数量" name="quantity"/>
        <column width="1800" display="税费" name="tax"/>
        <column width="1800" display="运费" name="shipFee"/>
        <column width="8400" display="退款交易流水号" name="refundSerialNo"/>
        <column width="8400" display="退货地址" name="expressAddress"/>
        <column width="8400" display="退货快递单号" name="expressNo"/>
        <column width="4000" display="退货快递公司名" name="expressName"/>
        <column width="4000" display="退货收货人" name="expressReceiverName"/>
        <column width="8000" display="退货收货手机号" name="expressReceiverMobile"/>
        <column width="4000" display="操作时间" name="operaAt"/>
        <column width="4000" display="申请时间" name="applyAt"/>
        <column width="3500" display="退款理由" name="buyerNote"/>
        <column width="1800" display="优惠金额" name="discount"/>
        <column width="3000" display="推送状态" name="pushStatus"/>
    </item>
    <item display="订单导出表" className="OrderExportView">
        <column width="4300" display="订单号" name="orderId"/>
        <column width="6000" display="海关申报订单号" name="declaredNo"/>
        <column width="2800" display="店铺名" name="shopName"/>
        <column width="3000" display="订单下级来源" name="outFromName"/>
        <column width="2000" display="订单状态" name="status"/>
        <column width="4000" display="支付流水号" name="paySerialNo"/>
        <column width="4000" display="下单时间" name="createAt"/>
        <column width="4000" display="支付时间" name="paidAt"/>
        <column width="2500" display="买家微信昵称" name="wxBuyerName"/>
        <column width="1800" display="微信openId" name="openId"/>
        <column width="2000" display="买家Id" name="buyerId"/>
        <column width="2000" display="是否为首次购买订单" name="firstOrder"/>
        <column width="1800" display="收件人" name="receiveUserName"/>
        <column width="3800" display="手机号" name="mobile"/>
        <column width="1800" display="支付人" name="payerName"/>
        <column width="3800" display="支付人身份证" name="payerNo"/>
        <column width="4800" display="收货地址" name="address"/>
        <column width="3000" display="推送状态" name="pushStatus"/>
        <column width="3000" display="快递公司" name="shipmentName"/>
        <column width="5000" display="快递编码" name="shipmentId"/>
        <column width="4000" display="发货时间" name="shipmentTime"/>
        <column width="4000" display="确认收货时间" name="confirmAt"/>
        <column width="2500" display="身份" name="ARole"/>
        <column width="2500" display="名称" name="AName"/>
        <column width="2500" display="佣金" name="AProfit"/>
        <column width="2500" display="提现状态" name="AStatus"/>
        <column width="2500" display="身份" name="BRole"/>
        <column width="2500" display="名称" name="BName"/>
        <column width="2500" display="佣金" name="BProfit"/>
        <column width="2500" display="提现状态" name="BStatus"/>
        <column width="2500" display="身份" name="CRole"/>
        <column width="2500" display="名称" name="CName"/>
        <column width="2500" display="佣金" name="CProfit"/>
        <column width="2500" display="提现状态" name="CStatus"/>
        <column width="3500" display="商品代码(货号)" name="itemCode"/>
        <column width="4000" display="商品名称" name="itemName"/>
        <column width="4000" display="商品分类" name="categoryName"/>
        <column width="1800" display="商品价格" name="originFee"/>
        <column width="1800" display="数量" name="quantity"/>
        <column width="1500" display="条形码" name="barCode"/>
        <column width="5000" display="外部SKU编码" name="outerSkuId"/>
        <column width="1800" display="总价" name="fee"/>
        <column width="1800" display="税费" name="tax"/>
        <column width="1800" display="运费" name="shipFee"/>
        <column width="1800" display="优惠金额" name="discount"/>
    </item>
    <item display="导购导入错误列表" className="WrongImportGuider">
        <column width="5000" display="门店手机号" name="storeMobile"/>
        <column width="5000" display="导购员名称" name="name"/>
        <column width="5000" display="导购员手机号码" name="mobile"/>
        <column width="5000" display="失败原因" name="reason"/>
    </item>
    <item display="提现导出列表" className="WithdrawExportDTO">
        <column width="5000" display="商户流水号" name="id"/>
        <column width="5000" display="收款方账户类型" name="accountType"/>
        <column width="5000" display="收款方开户机构" name="bankName"/>
        <column width="5000" display="收款方户名" name="ownerName"/>
        <column width="5000" display="收款方帐号" name="withdrawAccount"/>
        <column width="4500" display="金额(元)" name="fee"/>
        <column width="5000" display="平台名" name="shopName"/>
        <column width="5000" display="导出昵称模板" name="userName"/>
        <column width="5000" display="手机号" name="mobile"/>
        <column width="3000" display="身份" name="identity"/>
        <column width="4500" display="状态" name="showStatus"/>
        <column width="6000" display="提现支付交易流水号" name="paySerialNo"/>
        <column width="3000" display="提现方式" name="type"/>
        <column width="5000" display="提现帐号来源" name="from"/>
        <column width="5000" display="提现支付时间" name="withdrawAt"/>
        <column width="5000" display="提现发起时间" name="applyAt"/>
    </item>
    <item display="门店导入错误列表" className="WrongImportSubStore">
        <column width="5000" display="门店名" name="name"/>
        <column width="5000" display="手机号码" name="mobile"/>
        <column width="2000" display="省" name="province"/>
        <column width="2000" display="市" name="city"/>
        <column width="2000" display="县" name="county"/>
        <column width="8000" display="详细地址" name="address"/>
        <column width="5000" display="失败原因" name="reason"/>
    </item>
    <item display="订单导出表" className="WeShopOrderExportView">
        <column width="4800" display="订单号" name="orderId"/>
        <column width="8000" display="海关申报订单号" name="declaredNo"/>
        <column width="2800" display="店铺名" name="shopName"/>
        <column width="5000" display="订单下级来源" name="outFromName"/>
        <column width="2000" display="买家Id" name="buyerId"/>
        <column width="5000" display="商品代码(货号)" name="itemCode"/>
        <column width="2800" display="商品名称" name="itemName"/>
        <column width="1800" display="商品价格" name="originFee"/>
        <column width="1800" display="数量" name="quantity"/>
        <column width="1800" display="税费" name="tax"/>
        <column width="1800" display="运费" name="shipFee"/>
        <column width="1800" display="优惠金额" name="discount"/>
        <column width="1800" display="商品总价" name="fee"/>
        <column width="2000" display="订单状态" name="status"/>
        <column width="4000" display="支付流水号" name="paySerialNo"/>
        <column width="4000" display="下单时间" name="createAt"/>
        <column width="4000" display="支付时间" name="paidAt"/>
        <column width="2500" display="买家微信昵称" name="wxBuyerName"/>
        <column width="1800" display="买家" name="buyerName"/>
        <column width="1800" display="收件人" name="receiveUserName"/>
        <column width="3800" display="手机号" name="mobile"/>
        <column width="1800" display="支付人" name="payerName"/>
        <column width="3800" display="支付人身份证" name="payerNo"/>
        <column width="4800" display="收货地址" name="address"/>
        <column width="4000" display="快递公司" name="shipmentName"/>
        <column width="8000" display="快递编码" name="shipmentId"/>
        <column width="4000" display="发货时间" name="shipmentTime"/>
        <column width="4000" display="确认收货时间" name="confirmAt"/>
        <column width="4000" display="分销店铺名称" name="weShopName"/>
        <column width="4000" display="分销店铺佣金" name="weShopProfit"/>
        <column width="3000" display="推送状态" name="pushStatus"/>
    </item>
    <item display="积分交易明细" className="IntegralOrderExport">
        <column width="3000" display="用户名" name="userName"/>
        <column width="3500" display="手机号码" name="phone"/>
        <column width="5000" display="积分码" name="code"/>
        <column width="5000" display="扫码来源" name="codeName"/>
        <column width="3000" display="面值" name="faceValue"/>
        <column width="6000" display="交易时间" name="createAt"/>
    </item>
    <item display="微分销店铺收益变动日志" className="ProfitChangeRecordForSeller">
        <column width="3500" display="账户名称" name="accountName"/>
        <column width="3500" display="类型" name="typeName"/>
        <column width="3500" display="支付方式" name="payTypeName"/>
        <column format="price" width="2000" display="变动金额" name="amounts"/>
        <column format="price" width="2000" display="账户余额" name="balance"/>
        <column format="isoDate" width="5000" display="创建时间" name="createdAt"/>
        <column format="isoDate" width="5000" display="结算时间" name="settledAt"/>
        <column width="5000" display="备注" name="remark"/>
    </item>
    <item display="商家日汇总报表" className="SellerTradeDailySummary">
        <column width="1800" display="ID" name="id"/>
        <column format="isoDate" width="1800" display="汇总日期" name="sumAt"/>
        <column width="1800" display="店铺ID" name="sellerId"/>
        <column width="1800" display="店铺名称" name="sellerName"/>
        <column width="1800" display="订单数量" name="orderCount"/>
        <column width="1800" display="退款笔数" name="refundOrderCount"/>
        <column format="price" width="1800" display="应收货款" name="originFee"/>
        <column format="price" width="1800" display="商家优惠" name="sellerDiscount"/>
        <column format="price" width="1800" display="平台优惠" name="platformDiscount"/>
        <column format="price" width="1800" display="交易总退款" name="refundFee"/>
        <column format="price" width="1800" display="运费" name="shipFee"/>
        <column format="price" width="1800" display="运费优惠" name="shipFeeDiscount"/>
        <column format="price" width="1800" display="税费" name="tax"/>
        <column width="1800" display="差价" name="diffFee"/>
        <column format="price" width="1800" display="实收货款" name="actualPayFee"/>
        <column format="price" width="1800" display="支付平台佣金" name="gatewayCommission"/>
        <column format="price" width="1800" display="平台佣金" name="platformCommission"/>
        <column format="price" width="1800" display="商家应收" name="sellerReceivableFee"/>
    </item>
    <item display="服务商导入结果" className="ServiceProviderImportStruct">
        <column width="3000" display="服务商名称" name="name"/>
        <column width="3000" display="手机号码" name="mobile"/>
        <column width="3000" display="收款公司名称" name="bankAccountName"/>
        <column width="3000" display="银行名称" name="bankName"/>
        <column width="3000" display="银行账号" name="bankAccountNo"/>
        <column width="3000" display="省" name="province"/>
        <column width="3000" display="市" name="city"/>
        <column width="3000" display="县" name="county"/>
        <column width="6000" display="具体地址" name="address"/>
        <column width="6000" display="法人身份证：正面" name="frontImg" img="true"/>
        <column width="6000" display="法人身份证：国徽" name="backImg" img="true"/>
        <column width="6000" display="营业执照" name="businessImg" img="true"/>
        <column width="3000" display="导入结果" name="reason"/>
    </item>
    <item display="退款单明细报表" className="SettleRefundOrderDetail">
        <column width="1800" display="ID" name="id"/>
        <column format="isoDate" width="1800" display="对账时间" name="checkAt"/>
        <column format="isoDate" width="7200" display="退款完成时间" name="refundAt"/>
        <column width="1800" display="退款单号" name="refundId"/>
        <column width="1800" display="订单号" name="orderId"/>
        <column width="1800" display="店铺ID" name="sellerId"/>
        <column format="price" width="1800" display="应退货款" name="originFee"/>
        <column format="price" width="1800" display="商家优惠" name="sellerDiscount"/>
        <column format="price" width="1800" display="平台优惠" name="platformDiscount"/>
        <column format="price" width="1800" display="应退运费" name="shipFee"/>
        <column format="price" width="1800" display="运费优惠" name="shipFeeDiscount"/>
        <column format="price" width="1800" display="税费" name="tax"/>
        <column width="1800" display="差价" name="diffFee"/>
        <column format="price" width="1800" display="实退货款" name="actualRefundFee"/>
        <column format="price" width="1800" display="应退支付平台佣金" name="gatewayCommission"/>
        <column format="price" width="1800" display="应退平台佣金" name="platformCommission"/>
        <column format="price" width="1800" display="应扣商家" name="sellerDeductFee"/>
    </item>
    <item display="分销用户导出" className="DistributionUsersExport">
        <column width="3000" display="用户id" name="userId"/>
        <column width="3500" display="昵称" name="realName"/>
        <column width="5000" display="名称" name="proxyShopName"/>
        <column width="5000" display="手机号" name="mobile"/>
        <column width="3000" display="累计佣金" name="incomeSum"/>
        <column width="3000" display="可提现金额" name="outcomeSum"/>
        <column width="3000" display="累计销售额" name="finishedTradeFeeSum"/>
        <column width="6000" display="推荐人" name="parentUserName"/>
        <column width="3000" display="身份" name="level"/>
        <column width="3000" display="门店数量" name="fanNum"/>
        <column width="3000" display="忠实粉丝数量" name="honestFanNum"/>
        <column width="3000" display="普通粉丝数量" name="normalFanNum"/>
        <column width="6000" display="成为时间" name="times"/>
    </item>
    <item display="品牌代言审核" className="StoreProxyExport">
        <column width="3000" display="申请编号" name="id"/>
        <column width="3500" display="昵称" name="nickName"/>
        <column width="5000" display="推荐人" name="referenceName"/>
        <column width="5000" display="申请名称" name="proxyCradName"/>
        <column width="5000" display="角色" name="type"/>
        <column width="6000" display="省" name="province"/>
        <column width="6000" display="市" name="city"/>
        <column width="6000" display="区/县" name="county"/>
        <column width="3000" display="合作状态" name="flag"/>
        <column width="3000" display="审核状态" name="status"/>
        <column width="6000" display="审核时间" name="shenheTime"/>
        <column width="6000" display="创建时间" name="createTime"/>
    </item>
    <item display="服务商列表" className="ServiceProviderView">
        <column width="3000" display="服务商名称" name="name"/>
        <column width="3000" display="手机号" name="mobile"/>
        <column width="3000" display="收款公司名称" name="bankAccountName"/>
        <column width="3000" display="银行名称" name="bankName"/>
        <column width="3000" display="银行账号" name="bankAccountNo"/>
        <column width="3000" display="省" name="province"/>
        <column width="3000" display="市" name="city"/>
        <column width="3000" display="县" name="county"/>
        <column width="3000" display="详细地址" name="address"/>
        <column width="3000" display="授权地区" name="authAddress"/>
        <column width="6000" display="法人身份证：正面" name="frontImg" img="true"/>
        <column width="6000" display="法人身份证：国徽" name="backImg" img="true"/>
        <column width="6000" display="营业执照" name="businessImg" img="true"/>
        <column width="6000" display="食品流通许可证" name="foodSellAllowImg" img="true"/>
        <column width="3000" display="创建时间" name="createdAt" format="isoDate"/>
    </item>
    <item display="订单导出表" className="OrderExportIntoDepot">
        <column width="3000" display="订单号" name="orderId"/>
        <column width="3000" display="国内物流订单号" name="shipmentSerialCode"/>
        <column width="3000" display="物流商名称" name="shipmentCorpName"/>
        <column width="3000" display="客户名称" name="corpName"/>
        <column width="3000" display="订单商品信息简述" name="itemName"/>
        <column width="3000" display="包裹总重量" name="packVolume"/>
        <column width="3000" display="收货人姓名" name="receiverName"/>
        <column width="3000" display="收货人电话" name="receiverMobile"/>
        <column width="3000" display="收货地省" name="province"/>
        <column width="3000" display="收货地市" name="city"/>
        <column width="3000" display="收货地区县" name="distinct"/>
        <column width="30000" display="收货地地址" name="address"/>
        <column width="3000" display="收货地邮编" name="distinctCode"/>
        <column width="3000" display="订购人姓名" name="payerName"/>
        <column width="3000" display="收件人身份证号码" name="payerIdentityCode"/>
        <column width="3000" display="发件方公司名称" name="senderCorpName"/>
        <column width="3000" display="发件地省/州名" name="senderProvince"/>
        <column width="3000" display="发件地城市" name="senderCity"/>
        <column width="3000" display="寄件人" name="senderName"/>
        <column width="3000" display="寄件人联系方式" name="senderMobile"/>
        <column width="3000" display="寄件人地址" name="senderAddress"/>
        <column width="3000" display="寄件人邮编" name="senderPlaceCode"/>
        <column width="3000" display="所在发货国家" name="senderCountry"/>
        <column width="3000" display="备注" name="remark"/>
        <column width="3000" display="提货单号" name="withdrawGoodCode"/>
        <column width="3000" display="支付流水号" name="paySerial"/>
        <column width="3000" display="支付id" name="payId"/>
        <column width="3000" display="商品名称" name="itemName"/>
        <column width="3000" display="商品编码" name="itemCode"/>
        <column width="3000" display="商品HSCode" name="hsCode"/>
        <column width="3000" display="商品数量" name="quantity"/>
        <column width="3000" display="单价" name="price"/>
        <column width="3000" display="单件重量" name="volume"/>
        <column width="13000" display="计量单位" name="quantityUnit"/>
        <column width="13000" display="原产国" name="country"/>
        <column width="13000" display="条形码" name="qrCode"/>
        <column width="13000" display="第一计量单位" name="firstQuantityUnit"/>
        <column width="13000" display="第一计量单位数量" name="firstQuantity"/>
        <column width="13000" display="第一计量单位" name="secondQuantityUnit"/>
        <column width="13000" display="第一计量单位数量" name="secondQuantity"/>
    </item>
    <item display="商家会员价列表" className="MembershipPriceInfo">
        <column width="1800" display="会员价ID" name="id"/>
        <column width="1800" display="用户ID" name="userId"/>
        <column width="2000" display="用户名" name="userName"/>
        <column format="price" width="2000" display="会员价" name="price"/>
        <column width="1800" display="商品ID" name="itemId"/>
        <column width="2000" display="商品名称" name="itemName"/>
        <column width="1800" display="规格字符串" name="attrs"/>
        <column width="2000" display="微信小程序链接" name="wxaUrl"/>
    </item>
    <item display="门店列表" className="SubStore">
        <column width="2000" display="服务商名称" name="serviceName"/>
        <column width="2000" display="服务商地址" name="serviceAddress"/>
        <column width="2000" display="服务商手机号" name="serviceMobile"/>
        <column width="3000" display="门店名称" name="name"/>
        <column width="3000" display="手机号" name="mobile"/>
        <column width="3000" display="省份" name="province"/>
        <column width="3000" display="城市" name="city"/>
        <column width="3000" display="地区" name="county"/>
        <column width="3000" display="银行名称" name="bankName"/>
        <column width="3000" display="银行账户" name="bankNo"/>
        <column width="3000" display="法人名称" name="nameInLaw"/>
        <column width="6000" display="法人身份证：正面" name="frontImg" img="true"/>
        <column width="6000" display="法人身份证：国徽" name="backImg" img="true"/>
        <column width="6000" display="营业执照" name="businessImg" img="true"/>
        <column width="6000" display="食品许可证" name="foodSellAllowImg" img="true"/>
        <column width="3000" display="创建时间" name="createdAt"/>
    </item>
    <item display="店铺导出" className="ShopView">
        <column width="3000" display="名称" name="name"/>
        <column width="3000" display="用户名称" name="userName"/>
        <column width="3000" display="手机" name="phone"/>
        <column width="3000" display="分销类型" name="shopType"/>
    </item>
    <item display="门店导购列表" className="GuiderDetailExport">
        <column width="2000" display="服务商名称" name="serviceName"/>
        <column width="2000" display="服务商地址" name="serviceAddress"/>
        <column width="2000" display="服务商手机号" name="serviceMobile"/>
        <column width="5000" display="门店名称" name="substoreName"/>
        <column width="5000" display="导购名称" name="guiderName"/>
        <column width="4000" display="手机号" name="phone"/>
        <column width="3000" display="订单数量" name="orderNum"/>
        <column width="3000" display="订单金额" name="feeSum"/>
    </item>
</tables>
