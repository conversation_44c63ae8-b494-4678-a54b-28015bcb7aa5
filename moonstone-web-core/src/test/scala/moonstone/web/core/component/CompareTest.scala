package moonstone.web.core.component

import com.fasterxml.jackson.databind.ObjectMapper
import moonstone.web.core.model.dto.SkuGroupByNameAndId

class CompareTest {
  @junit.Test
  def compareTest(): Unit = {
    val a = 123L
    val b = 456L
    val c = null
    print(null == c)
  }

  @junit.Test
  def combineModel(): Unit = {
    print(SkuGroupByNameAndId.build(Array("123", "456"), 1234L))
    val entity = SkuGroupByNameAndId.build(Array("123", "456"), 1234L)
    val om = new ObjectMapper
    print(om.writeValueAsString(entity))
    assert(SkuGroupByNameAndId.build(Array("123", "456"), 1234L).name == "123,456")
  }
}
