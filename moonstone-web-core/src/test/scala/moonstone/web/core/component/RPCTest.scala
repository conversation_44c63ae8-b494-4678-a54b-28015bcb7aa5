package moonstone.web.core.component

import moonstone.common.api.Y800PlatformAPI
import moonstone.common.model.Either
import moonstone.common.model.rpcAPI.y800dto.{ListDTO, Y800SkuQueryResponse, Y800TaxResultDTO}
import moonstone.web.core.component.api.Y800Rpc

class RPCTest {
  @Test
  def rpcTest(): Unit = {
    val rpc = new Y800Rpc {
      /**
       * 获取Api Http真实访问接口
       *
       * @return
       */
      override def getApiUrl: String = "Fukcu"
    }
    assert(rpc.convertServiceName("aAa").equals("a.aa"))
  }

  @Test
  def convert(): Unit = {
    val json: String = "{\"code\":\"success\",\"actionCode\":\"success\",\"errorMsg\":\"请求成功\",\"data\":[{\"image\":\"\",\"depotName\":\"天津-测试环境\",\"code\":\"S2017120716094986760013\",\"outCode\":\"S2017120716094986760013\",\"origin\":\"日本\",\"resultCode\":\"SUCCESS\",\"specification\":\"38片/包\",\"weight\":\"1.4190\",\"volume\":\"60\",\"unit\":\"125\",\"grossWeight\":\"1.7000\",\"name\":\"尤妮佳拉拉裤 PB38 女\",\"depotCode\":\"D2017081000005656\",\"brand\":\"\",\"tradeType\":\"1\",\"status\":0}]}"
    val rpc = new Y800Rpc {
      /**
       * 获取Api Http真实访问接口
       *
       * @return
       */
      override def getApiUrl: String = "Fukcu"
    }
    val methods = classOf[Y800PlatformAPI].getMethods
    val data = rpc.decodeAfterInvoke(methods.filter(_.getName.startsWith("goodsQuery"))(0), Either.ok(json)).asInstanceOf[Either[java.util.List[Y800SkuQueryResponse]]]
    assert(data.take.get(0).getDepotName == "天津-测试环境")
  }
  @Test
  def convert2():Unit={
    val json: String = "{\"code\":\"success\",\"actionCode\":\"success\",\"errorMsg\":\"请求成功\",\"data\":{\"list\":[{\"code\":\"S2017120716094986760013\",\"outCode\":\"S2017120716094986760013\",\"price\":1.00,\"tax\":0.09}]}}"
    val rpc = new Y800Rpc {
      /**
       * 获取Api Http真实访问接口
       *
       * @return
       */
      override def getApiUrl: String = "Fukcu"
    }
    val methods = classOf[Y800PlatformAPI].getMethods
    val data = rpc.decodeAfterInvoke(methods.filter(_.getName.startsWith("goodsTax"))(0), Either.ok(json)).asInstanceOf[Either[ListDTO[Y800TaxResultDTO]]]
    assert(data.take().list.get(0).getTax.toString.equals("0.09"))
  }
}
