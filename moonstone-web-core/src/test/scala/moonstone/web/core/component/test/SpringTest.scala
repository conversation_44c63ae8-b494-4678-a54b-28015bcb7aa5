package moonstone.web.core.component.test

import org.slf4j.{Lo<PERSON>, LoggerFactory}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.json.GsonHttpMessageConverter
import org.springframework.web.bind.annotation.{PathVariable, PostMapping, RequestBody, RestController}
import org.springframework.web.servlet.config.annotation.EnableWebMvc

@RestController("/api")
@SpringBootApplication
@EnableWebMvc
@Configuration
class SpringTest {
  @Autowired
  var gsonHttpMessageConverter: GsonHttpMessageConverter = _
  val log: Logger = LoggerFactory.getLogger(getClass);

  @PostMapping(Array("/{api}"))
  def test(@PathVariable("api") api: String, @RequestBody data: String): Unit = {
    log.debug(s"$api invoke with $data")
  }
}

object SpringTest extends App {

  val application = new SpringApplication(classOf[SpringTest])
  application.run()

}
