package moonstone.web.core.component

import com.alibaba.fastjson.{JSON, TypeReference}
import io.terminus.common.utils.JsonMapper

import java.util

class JSONTest {
  @Test
  def test(): Unit = {
    val map = new util.HashMap[String, Object]()
    map.put("title", "JSON_Object_Convert_Map_Test")
    map.put("map", Map("data" -> "None", "size" -> "0", "Joke" -> <PERSON><PERSON><PERSON>("Funny")))
    val json = JSON.toJSONString(map)
    val JSONParsedObject = JSON.parseObject(json, new TypeReference[util.Map[String, Object]]() {})
    val JsonMapperParsedObject = JsonMapper.nonEmptyMapper().fromJson(json, classOf[util.Map[String, Object]])
    assert(JSONParsedObject.equals(JsonMapperParsedObject))
  }
}
