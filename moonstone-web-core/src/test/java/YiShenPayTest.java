import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import moonstone.web.core.component.pay.bhecard.domain.CommonRequest;
import moonstone.web.core.component.pay.bhecard.domain.JsPayRequest;
import moonstone.web.core.component.pay.bhecard.domain.PayNotify;
import org.joda.time.DateTime;
import org.junit.Test;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

public class YiShenPayTest {
    @Test
    public void test() {
        CommonRequest<String> request = new CommonRequest<>();
        request.setBizContent("test");
        request.setKeyByBase("MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAIqUuxd92eEBXVneDWhfNP6XCkLcGBO1YAulexKX+OdlfZzB/4NNHkOAQQy84k3ZgIUPIk5hewLbA+XGrk9Wih5HG3ZQeFugeoTcx3vwo7AQv7KnmcKEWFNlOr/EhB3JndmcQnBRsIRRdCP+7nobfBqU0jS8dnpcQX1AtBRZRnkfAgMBAAECgYAe+u70ansZ1Q9EduKycY5MWAHAPqnXRhXppJ3l4zmOqV6ye6Aef1ADsRlZuqQw2S3lESQPN7WjRskRRiBTtjn8Atul9YeC7+QirP1K8seUP5gKB4bcjlzzl1m5dmxldkptJAmdzwYn8PRTW0+tFVyEaD/B8hKGxij4Gew0e8bwCQJBAOboG3ttBESsG2cAtmP1MfKRTjVdY7qRMXzBybcAeobBbmgCQgybVXXgjbGai+qwrQqcVRIp6p1yDWTZxVSuDWsCQQCZpBhcayOCMZR6F8dQJSuSSSIJw/GGN7IXfMYIqLxA2oGzlQ0B1DffOUe2wrid+WdpLuYCz2LYPQHDEgYM1dwdAkEAnfwhEYm9ad73wLnUEQAqdHTGtex316aP3XQZt4Q0UQ73o2IoHsgI6OYDDIlZQfIv8xqTeiIDzEXEtEPrp8yOkQJBAIWAzFZKFqHD2UO6M8vVcKX9fGFF7TH2ZX75Qc82Z9ZmyDs2sgW71QzX5hPN4cQLeqswQFeCw14orMZHfBBdKJUCQQDiWYk85okRugsWtxeJFhMEt2oUT+Kd8Yz5Aiz3J9XIS+zWtJrFlv+hXkVedPJ3xtBF32DZrCbxDn3UjXipRaCP");
        assert request.toString().length() > 0;
    }

    @Test
    public void serialize() {
        JsPayRequest request = new JsPayRequest();
        request.setBusinessTime(new Date());
        ObjectMapper objectMapper = new ObjectMapper();
        assert JSONObject.parseObject(JSON.toJSONString(request)).containsKey("business_time");
        assert objectMapper.convertValue(request, Map.class).containsKey("business_time");
    }

    @Test
    public void enumSerialize() throws IOException {
        PayNotify payNotify = new PayNotify();
        payNotify.setTradeStatus(PayNotify.TradeStatus.WAIT_BUYER_PAY);
        assert JSON.parseObject(JSON.toJSONString(payNotify), PayNotify.class).getTradeStatus().equals(PayNotify.TradeStatus.WAIT_BUYER_PAY);
        ObjectMapper objectMapper = new ObjectMapper();
        assert objectMapper.convertValue(objectMapper.convertValue(payNotify, Map.class), PayNotify.class).getTradeStatus().equals(PayNotify.TradeStatus.WAIT_BUYER_PAY);

        String data = "{\"trade_status\":\"WAIT_BUYER_PAY\"}";
        assert JSON.parseObject(data, PayNotify.class).getTradeStatus().equals(PayNotify.TradeStatus.WAIT_BUYER_PAY);
        assert objectMapper.readValue(data, PayNotify.class).getTradeStatus().equals(PayNotify.TradeStatus.WAIT_BUYER_PAY);
    }

    @Test
    public void dateTest() {
        Date date = new Date();
        String dateStr = new DateTime(date).toString("yyyy-MM-dd HH:mm:ss");
        assert LocalDateTime.from(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").parse(dateStr)).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() == (date.toInstant().getEpochSecond());
    }
}
