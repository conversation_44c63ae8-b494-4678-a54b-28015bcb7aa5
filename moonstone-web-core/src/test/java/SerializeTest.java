import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableBiMap;
import moonstone.order.dto.OrderCriteria;
import moonstone.user.model.User;
import moonstone.web.core.component.pay.bhecard.domain.CommonRequest;
import org.junit.Test;

import java.util.Arrays;
import java.util.Map;

public class SerializeTest {

    static ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Test
    public void jsonCompare() throws Exception {
        String str = "{\"test\":123,\"id\":123}";
        User user = objectMapper.readValue(str, User.class);
        assert user.getId().equals(JSON.parseObject(str, User.class).getId());
    }

    @Test
    public void setterTest() throws Exception {
        String str = "{\"statusStr\":\"1,2,3,4,\",\"u\":1}";
        OrderCriteria orderCriteria = A.O.readValue(str, OrderCriteria.class);
        assert Arrays.equals(JSON.parseObject(str, OrderCriteria.class).getStatus().toArray(), orderCriteria.getStatus().toArray());
    }

    @Test
    public void test() throws Exception {
        CommonRequest<Map<String, String>> commonRequest = new CommonRequest<>();
        commonRequest.setBizContent(ImmutableBiMap.of("test", "1"));
        System.out.println(A.O.writeValueAsString(commonRequest));
    }


    interface A {
        ObjectMapper O = new ObjectMapper();
        Config c = new Config();

        class Config {
            static {
                System.out.println("config object mapper over");
                O.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            }
        }
    }
}
