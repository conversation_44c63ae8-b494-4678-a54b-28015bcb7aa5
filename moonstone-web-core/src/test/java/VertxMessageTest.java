import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;
import io.vertx.core.eventbus.EventBusOptions;
import io.vertx.spi.cluster.hazelcast.HazelcastClusterManager;
import org.junit.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class VertxMessageTest {
    @Test
    public void send() throws Exception {
        CompletableFuture<Object> done = new CompletableFuture<>();
        long start = System.currentTimeMillis();
        Vertx.clusteredVertx(cultist(), c -> {
            AtomicInteger count = new AtomicInteger();
            c.result().exceptionHandler(done::completeExceptionally);
            for (int j=0;j<100;j++) {
                c.result().executeBlocking(p -> {
                    for (int i = 0; i < 10_000; i++) {
                        c.result().eventBus().request("test", "done", h -> {
                            if (h.succeeded()) {
                                if (count.incrementAndGet() == 1000_000) {
                                    done.complete(true);
                                }
                            }
                            h.result();
                        });
                    }
                    System.out.println();
                    p.complete();
                }, h -> {
                });
            }
        });
        done.get(100, TimeUnit.SECONDS);
        System.out.println("done at " + (System.currentTimeMillis() - start));
    }

    private VertxOptions cultist() {
        HazelcastInstance hazelcast = Hazelcast.newHazelcastInstance();
        VertxOptions vertxOptions = new VertxOptions();
        EventBusOptions eventBusOptions = new EventBusOptions();
        eventBusOptions.setHost("127.0.0.1");
        vertxOptions.setEventBusOptions(eventBusOptions);
        vertxOptions.setClusterManager(new HazelcastClusterManager(hazelcast));
        vertxOptions.setEventLoopPoolSize(Runtime.getRuntime().availableProcessors());
        vertxOptions.setInternalBlockingPoolSize(Runtime.getRuntime().availableProcessors());
        return vertxOptions;
    }

    @Test
    public void receive() throws Exception {
        CompletableFuture<Object> done = new CompletableFuture<>();
        long start = System.currentTimeMillis();
        AtomicInteger count = new AtomicInteger();
        Vertx.clusteredVertx(cultist(), c -> {
            c.result().exceptionHandler(done::completeExceptionally);
            c.result().eventBus()
                    .consumer("test")
                    .handler(message -> {
                        if (count.incrementAndGet() == 1000_000) {
                            done.complete(true);
                        } else {
                            if (count.get() % 5432 == 0) {
                                System.out.println("get : " + message.body() + "#" + message.address() + "@" + message.replyAddress() + " cost:" + (System.currentTimeMillis() - start)+" at: "+count.get());
                            }
                        }
                        message.reply("receive");
                    });
        });
        done.get(100, TimeUnit.SECONDS);
        System.out.println("done at " + (System.currentTimeMillis() - start));
    }
}
