package moonstone.web.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.core.model.enu.CertificationEnu;
import org.junit.Test;

import java.util.Map;

public class SerializeTest {
    @Test
    public void serialize() throws Exception {
        CurrentProfitVO currentProfitVO = new CurrentProfitVO();
        currentProfitVO.setCertification(CertificationEnu.NONE_NEED);
        ObjectMapper objectMapper = new ObjectMapper();
        assert objectMapper.convertValue(currentProfitVO, Map.class).get("certification").toString().equals("0");
    }
}
