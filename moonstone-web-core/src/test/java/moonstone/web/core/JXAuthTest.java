package moonstone.web.core;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.AuthAble;
import moonstone.user.model.StoreProxy;
import moonstone.web.core.util.StoreProxyAuthProxy;
import moonstone.web.core.util.StoreProxyJXAuth;
import org.junit.Test;

import java.util.Date;

@Slf4j
public class JXAuthTest {
    @Test
    public void guiderStoreProxyAuth() {
        StoreProxy origin = new StoreProxy();
        StoreProxyJXAuth authInit = StoreProxyJXAuth.convertStoreProxy(origin);
        assert authInit.initAuth();

        StoreProxyJXAuth.StoreProxyAuthProxyJXAuth jxAuth = StoreProxyJXAuth.convertStoreProxy((StoreProxy) authInit);
        assert !jxAuth.isAuthed();
        assert jxAuth.auth();
        log.debug("Status -> {}", authInit.getStatus());
        assert jxAuth.isAuthed();
        AuthAble authAble = StoreProxyAuthProxy.build(jxAuth);
        assert !authAble.isAuthed();
        assert jxAuth.isAuthed();
        assert authAble.auth();
        log.debug("Status -> {}", authInit.getStatus());
        assert authAble.isAuthed();
        assert jxAuth.isAuthed();
        assert new Date().toString().equals(jxAuth.getAuthAt().toString());
        assert new Date().toString().equals(authAble.getAuthAt().toString());
        log.debug("{}", jxAuth.getAuthAt());
        log.debug("{}", authAble.getAuthAt());
    }

    @Test
    public void jxStoreProxyAuth() {
        StoreProxy origin = new StoreProxy();
        StoreProxyJXAuth authInit = StoreProxyJXAuth.convertStoreProxy(origin);
        assert authInit.initAuth();
        assert authInit.auth();

        StoreProxyJXAuth.StoreProxyAuthProxyJXAuth jxAuth = StoreProxyJXAuth.convertStoreProxy((StoreProxy) authInit);
        assert jxAuth.isAuthed();
        AuthAble authAble = StoreProxyAuthProxy.build(jxAuth);
        assert !authAble.isAuthed();
        assert jxAuth.isAuthed();
        log.debug("Status -> {}", authInit.getStatus());
        assert authAble.auth();
        assert authAble.isAuthed();
        assert jxAuth.isAuthed();
        log.debug("Status -> {}", authInit.getStatus());
        assert new Date().toString().equals(jxAuth.getAuthAt().toString());
        assert new Date().toString().equals(authAble.getAuthAt().toString());
        log.debug("{}", jxAuth.getAuthAt());
        log.debug("{}", authAble.getAuthAt());
    }

    @Test
    public void compatibaleEqual() {
        StoreProxyJXAuth jxAuth = StoreProxyJXAuth.convertStoreProxy(new StoreProxy());
        jxAuth.initAuth();
        jxAuth.auth();
        assert jxAuth.getStatus() == (1 | AuthAble.AuthStatus.MASK_CODE.getValue() + 1);
    }

    @Test
    public void statusJudge(){
        StoreProxy storeProxy = new StoreProxy();
        storeProxy.setStatus(3);
        assert StoreProxyAuthProxy.build(storeProxy).isAuthed();

    }
}
