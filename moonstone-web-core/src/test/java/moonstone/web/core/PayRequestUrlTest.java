package moonstone.web.core;

import moonstone.web.core.component.pay.xinbada.domain.dto.PayRequest;
import org.junit.Test;

public class PayRequestUrlTest {
    @Test
    public void urlTest() {
        PayRequest.CallBackUrl url = PayRequest.CallBackUrl.build("www.baidu.com/ashash");
        assert ("www.baidu.com".equals(url.getHost()));
        assert url.getPort().equals("80");
        assert url.getUrl().equals("/ashash");
        assert url.getIs_https().equals(0);

        url = PayRequest.CallBackUrl.build("HTtp://www.baidu.com/ashash");
        assert ("www.baidu.com".equals(url.getHost()));
        assert url.getPort().equals("80");
        assert url.getUrl().equals("/ashash");
        assert url.getIs_https().equals(0);


        // https match
        url = PayRequest.CallBackUrl.build("https://www.baidu.com:8080/ashash/123");
        assert ("www.baidu.com".equals(url.getHost()));
        assert url.getPort().equals("8080");
        assert url.getUrl().equals("/ashash/123");
        assert url.getIs_https().equals(1);
    }
}
