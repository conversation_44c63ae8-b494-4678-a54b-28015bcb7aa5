package moonstone.web.core;

import com.alibaba.fastjson.JSON;
import moonstone.web.core.component.pay.xinbada.domain.dto.PayRequest;
import org.junit.Test;

public class XinBaDaPayRequestTest {
    @Test
    public void test() {
        PayRequest payRequest = new PayRequest();
        payRequest.setSecret("nope");
        payRequest.setAccount("here");
        assert JSON.toJSONString(payRequest).equals("{\"is_to_confirm_order\":0}");
    }
}
