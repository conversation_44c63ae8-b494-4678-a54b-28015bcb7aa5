package moonstone.web.core;

import com.github.kevinsawicki.http.HttpRequest;
import moonstone.web.core.component.ImageRedirect;
import org.junit.Test;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Proxy;
import java.net.URL;
import java.util.Random;

public class ImageRedirectTest {
    @Test
    public void testRedirect() throws Exception {
        ImageRedirect bean = new ImageRedirect();
        String imageUrl = "http://dante-img.oss-cn-hangzhou.aliyuncs.com/test/bc234497e9c7605825eb387806db173a.jpg";
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HttpServletResponse response = (HttpServletResponse) Proxy.newProxyInstance(ClassLoader.getSystemClassLoader(), new Class[]{HttpServletResponse.class}
                , (b, m, a) -> {
                    if (m.getName().startsWith("getOutput")) {
                        return new ServletOutputStream() {
                            @Override
                            public boolean isReady() {
                                return true;
                            }

                            @Override
                            public void setWriteListener(WriteListener writeListener) {

                            }

                            @Override
                            public void write(int b) throws IOException {
                                outputStream.write(b);
                            }
                        };
                    }
                    return null;
                });
        bean.redirect(response, imageUrl);
        HttpRequest request = HttpRequest.get(new URL(imageUrl));
        assert request.ok();
        ByteArrayOutputStream compare = new ByteArrayOutputStream();
        request.receive(compare);

        BufferedImage compareImage = ImageIO.read(new ByteArrayInputStream(compare.toByteArray()));
        BufferedImage redirectImage = ImageIO.read(new ByteArrayInputStream(outputStream.toByteArray()));
        assert compareImage.getHeight() == redirectImage.getHeight();
        assert compareImage.getWidth() == redirectImage.getWidth();
        Random random = new Random();
        for (int i = 0; i < 100; i++) {
            for (int v = 0; v < 10; v++) {
                int x = random.nextInt(compareImage.getWidth());
                int y = random.nextInt(compareImage.getHeight());
                assert compareImage.getRGB(x, y) == redirectImage.getRGB(x, y);
            }
        }
    }

}
