package moonstone.web.core;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * DATE: 16/9/13 上午10:05 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
public class LongPatternTest {
    @Test
    public void test() {
        String pattern = "^[1-9]\\d*$";
        Assert.assertTrue("123".matches(pattern));
        Assert.assertFalse("123hello".matches(pattern));
    }

    @Test
    public void testmd5() {
        Map<String, String> params = new HashMap<>();
        params.put("", "");
        Map<String, ?> sortedParams = new TreeMap<>(params);
        sortedParams.remove("sign");
        sortedParams.remove("payChannelAccountNo");
        sortedParams.remove("sign_type");
        String toVerify = Joiner.on('&').withKeyValueSeparator("=").join(sortedParams);
        //String expect = Hashing.md5().newHasher().putString(toVerify, Charsets.UTF_8).putString(key, Charsets.UTF_8).hash().toString();
    }

    @Test
    public void testTheFuckingAsshole() {
        String asshole = "{\"ash\":\"[1,2,3,4]\"}";
        Map<String, Object> assholeImprove = JSON.parseObject(asshole, new TypeReference<Map<String, Object>>() {
        }.getType());
        (JSON.parseObject(assholeImprove.get("ash").toString(), new TypeReference<List<Integer>>() {
        })).forEach(System.out::println);
        assholeImprove.forEach((k, v) -> Assert.assertTrue("Fuck U!", v instanceof String));
    }
}
