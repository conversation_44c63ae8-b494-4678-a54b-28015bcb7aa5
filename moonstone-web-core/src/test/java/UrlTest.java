import cn.hutool.http.HttpRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.net.URL;

@Slf4j
public class UrlTest {
    @Test
    public void test() throws Exception {
        String test = "https://test_nucc.bhecard.com:9088/api_gateway.do";
        String gateway = "https://newpay.bhecard.com/api_gateway.do";
        HttpRequest request = HttpRequest.get(gateway);
        com.github.kevinsawicki.http.HttpRequest.get(test).ok();
        log.error("{}", request.execute().body());

    }

    @Test
    public void port() throws Exception {
        URL url = new URL("https://test_nucc.bhecard.com:9088/api_gateway.do");
        ObjectMapper objectMapper = new ObjectMapper();
        System.out.println(objectMapper.writeValueAsString(url));
        System.out.println(objectMapper.writeValueAsString(new URL("https://www.baidu.com")));
    }
}
