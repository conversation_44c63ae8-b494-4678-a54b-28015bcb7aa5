import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.mirror.model.api.DomainAction;
import moonstone.web.core.mirror.model.api.DomainLifeCycle;
import moonstone.web.core.mirror.model.api.DomainStatus;
import moonstone.web.core.mirror.model.api.LifeAction;
import moonstone.web.core.mirror.model.domain.MirrorDomain;
import org.junit.Test;

import java.util.Map;
import java.util.function.Function;

@DomainLifeCycle(autoDiscovery = true)
@Slf4j
@Data
public class DomainTest implements MirrorDomain {
    String source;
    DomainStatus status;

    @Override
    public Function<String, DomainAction> parseAction() {
        return null;
    }

    @LifeAction
    public Either<Boolean> jobDone(int job) {
        return Either.ok(job > 0);
    }

    @LifeAction("sure")
    public Either<Boolean> ok() {
        return Either.ok(true);
    }

    @LifeAction
    public Either<Integer> shouldSkip(int a, int b) {
        return Either.error("skip it");
    }


    public Either<Boolean> complex(Map<String, Either<Boolean>> a) {
        return a.get("success");
    }


    @Test
    public void complexParameterPass() {
        ((Either<Boolean>) chain("complex").apply(type -> Json.parseObject(Json.toJson(ImmutableMap.of("success", Either.ok(true))), type)))
                .ifSuccess(b -> log.info("{} we reach {}", LogUtil.getClassMethodName(2), b))
                .take();
    }

    @Test
    public void intParameterPass() {
        ((Either<Boolean>) chain("jobDone").apply(type -> Json.parseObject("15", type)))
                .ifSuccess(b -> log.info("{} we reach {}", LogUtil.getClassMethodName(2), b))
                .take();
    }

    @Test
    public void noneParameterPass() {
        ((Either<Boolean>) chain("sure").apply(type -> Json.parseObject("15", type)))
                .ifSuccess(b -> log.info("{} we reach {}", LogUtil.getClassMethodName(2), b))
                .take();
    }

    @Test(expected = NullPointerException.class)
    public void moreParameterDrop() {
        ((Either<Boolean>) chain("shouldSkip").apply(type -> Json.parseObject("15", type))).take();
    }
}
