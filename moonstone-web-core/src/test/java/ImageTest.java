import moonstone.common.model.CommonUser;
import moonstone.common.model.image.ImageViewVerify;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.image.application.ImageDisplayAuthApp;
import org.junit.Test;

import java.awt.image.RenderedImage;
import java.util.Base64;
import java.util.Random;

public class ImageTest {
    @Test
    public void imageUrlDecodeTest() {
        ParanaConfig paranaConfig = new ParanaConfig();
        paranaConfig.setAppUrlBackend("");
        ImageDisplayAuthApp authApp = new ImageDisplayAuthApp();
        ImageUrlHandler handler = new ImageUrlHandler();
        handler.setDomain("dante-img.oss-cn-hangzhou.aliyuncs.com");
        handler.setProtocol("https://");
        String url = new String(Base64.getUrlDecoder().decode("dGVzdC85NTUwNzU1OS0xNzBjODA1MzMyNDA4ZDMxYmMyNzM5ZTFmYThkN2QxNC5wbmcK"));
        CommonUser u = new CommonUser();
        u.setId(1L);
        RenderedImage image = authApp.decodeIfAuth(url, u.getId()).take();
    }

    @Test
    public void testEncode() {
        Random random = new Random(System.currentTimeMillis());
        for (int i = 0; i < 100000; i++) {
            long clientId = Math.abs(random.nextLong());
            long ownerId = Math.abs(random.nextLong());
            String data = ImageViewVerify.encode(clientId, ownerId);
            String verify = ImageViewVerify.generateVerify(data);
            System.out.printf("%s %s%n", data, verify);
            assert ImageViewVerify.verify(ownerId, data, verify).equals(clientId);
        }
    }
}
