package moonstone.weShop.enums;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by CaiZhy on 2018/12/17.
 */
public enum WeShopProfitType {

    ORDER_PROFIT(1),    //零售利润
    INVITE_PROFIT(2),   //邀请返现
    REFUND_CHANGE(3);   //零售利润（扣减）

    private final int value;

    WeShopProfitType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static WeShopProfitType fromInt(int value){
        for (WeShopProfitType weShopProfitType : WeShopProfitType.values()) {
            if(Objects.equal(weShopProfitType.getValue(), value)){
                return weShopProfitType;
            }
        }
        throw new IllegalArgumentException("unknown type : " + value);
    }

    public Boolean isDeduction() {
        List<Integer> deductionValueList = Lists.newArrayList(3);
        if (deductionValueList.contains(this.value)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}
