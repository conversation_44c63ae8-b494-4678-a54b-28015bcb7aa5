/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.search.core.ESClient;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.*;
import moonstone.category.impl.dao.ShopCategoryItemDao;
import moonstone.common.config.ImageConfig;
import moonstone.countryImage.impl.dao.CountryImageDao;
import moonstone.item.impl.ESClient7;
import moonstone.item.impl.dao.ItemDao;
import moonstone.item.impl.dao.SkuDao;
import moonstone.search.IndexTemplateProperties;
import moonstone.search.dto.IndexedItem;
import moonstone.search.dto.IndexedShop;
import moonstone.search.item.IndexedItemFactory;
import moonstone.search.item.SearchItemProperties;
import moonstone.search.item.impl.*;
import moonstone.search.shop.SearchShopProperties;
import moonstone.search.shop.impl.*;
import moonstone.search.template.IndexTemplateInitiator;
import moonstone.storage.impl.manager.DefaultStorageManager;
import moonstone.storage.impl.service.DefaultStorageServiceImpl;
import moonstone.storage.service.StorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-14
 */
@Slf4j
@Configuration
@ComponentScan({"moonstone.item.impl",
        "moonstone.category.impl",
        "moonstone.brand.impl",
        "moonstone.shop.impl",
        "moonstone.spu.impl",
        "moonstone.delivery.impl",
        "moonstone.cache",
        "moonstone.membership.impl",
        "moonstone.thirdParty.impl",
        "moonstone.countryImage.impl",
        "moonstone.adv.impl"
})
@Import({ImageConfig.class})
public class ItemAutoConfig {

    @Bean
    public ObjectMapper nonNullObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper;
    }

    @Bean
    public ESClient esClient(@Value("${search.host:localhost}") String host,
                             @Value("${search.port:9200}") Integer port,
                             @Value("${search.username:''}") String username,
                             @Value("${search.password:''}") String password) {
        ESClient esClient = new ESClient7(host, port, username, password);
        log.info("连接es hostname:{} port:{},username:{},password:{}", host, port, username, password);
        return esClient;
    }

    @Configuration
    @ConditionalOnClass(ESClient.class)
    public static class BaseSearchConfiguration {

        @Configuration
        @ConditionalOnProperty(value = "enable.index.template", havingValue = "true", matchIfMissing = true)
        @ComponentScan({"moonstone.search.template"})
        @EnableConfigurationProperties(IndexTemplateProperties.class)
        public static class IndexTemplateInitiatorConfiguration {
            @Bean
            public IndexTemplateInitiator indexTemplateInitiator(ESClient esClient, IndexTemplateProperties indexTemplateProperties) {
                return new IndexTemplateInitiator(esClient, indexTemplateProperties);
            }
        }

    }

    @Configuration
    @EnableConfigurationProperties(SearchItemProperties.class)
    @ConditionalOnClass(ESClient.class)
    @ConditionalOnProperty(value = "enable.item.search", havingValue = "true", matchIfMissing = true)
    @ComponentScan({"io.terminus.search.api",
            "moonstone.search.item.impl",
            "moonstone.cache"})
    public static class ItemSearchConfiguration {

        @Bean
        @ConditionalOnMissingBean(IndexedItemGuarder.class)
        public IndexedItemGuarder indexedItemGuarder() {
            return new DefaultIndexedItemGuarder();
        }


        @Configuration
        @ConditionalOnMissingBean(IndexedItemFactory.class)
        protected static class IndexItemFactoryConfiguration {

            @Bean
            public IndexedItemFactory<? extends IndexedItem> indexedItemFactory(
                    BackCategoryCacher backCategoryCacher,
                    ShopCategoryItemDao shopCategoryItemDao,
                    CountryImageDao countryImageDao,
                    BrandCacher brandCacher,
                    CategoryAttributeCacher categoryAttributeCacher) {
                return new DefaultIndexedItemFactory(backCategoryCacher, shopCategoryItemDao, countryImageDao,
                        brandCacher, categoryAttributeCacher);
            }
        }

        @Configuration
        @ConditionalOnMissingBean(BaseItemQueryBuilder.class)
        protected static class ItemQueryBuilderConfiguration {
            @Bean
            public BaseItemQueryBuilder itemQueryBuilder() {
                return new DefaultItemQueryBuilder();
            }
        }

        @Configuration
        protected static class ItemSearchResultComposerConfiguration {
            @Bean
            @ConditionalOnMissingBean(ItemSearchResultComposer.class)
            public ItemSearchResultComposer itemSearchResultComposer(
                    BackCategoryCacher backCategoryCacher,
                    BrandCacher brandCacher,
                    OriginCacher originCacher) {
                return new DefaultItemSearchResultComposer(backCategoryCacher, brandCacher, originCacher);
            }

            @Bean
            @ConditionalOnMissingBean(ItemSearchInShopResultComposer.class)
            public ItemSearchInShopResultComposer itemSearchInShopResultComposer(
                    ShopCategoryCacher shopCategoryCacher) {
                return new DefaultItemSearchInShopResultComposer(shopCategoryCacher);
            }
        }

    }

    @Configuration
    @EnableConfigurationProperties(SearchShopProperties.class)
    @ConditionalOnClass(ESClient.class)
    @ConditionalOnProperty(value = "enable.shop.search", havingValue = "true", matchIfMissing = true)
    @ComponentScan({"io.terminus.search.api",
            "moonstone.search.shop.impl"})
    public static class ShopSearchConfiguration {

        @Bean
        @ConditionalOnMissingBean(IndexedShopGuarder.class)
        public IndexedShopGuarder indexedShopGuarder() {
            return new DefaultIndexedShopGuarder();
        }

        @Configuration
        @ConditionalOnMissingBean(IndexedShopFactory.class)
        protected static class IndexShopFactoryConfiguration {
            @Bean
            public IndexedShopFactory<? extends IndexedShop> indexedShopFactory(ItemDao itemDao) {
                return new DefaultIndexedShopFactory(itemDao);
            }
        }

        @Configuration
        @ConditionalOnMissingBean(BaseShopQueryBuilder.class)
        protected static class ShopQueryBuilderConfiguration {
            @Bean
            public BaseShopQueryBuilder shopQueryBuilder() {
                return new DefaultShopQueryBuilder();
            }
        }

    }

    @Configuration
    public static class ItemDefaultImplConfiguration {
        @Bean
        @ConditionalOnMissingBean(StorageService.class)
        public StorageService storageService(ItemDao itemDao, SkuDao skuDao) {
            DefaultStorageManager storageManager = new DefaultStorageManager(itemDao, skuDao);
            return new DefaultStorageServiceImpl(storageManager);
        }
    }
}
