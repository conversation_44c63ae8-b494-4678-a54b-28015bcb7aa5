package moonstone.shop.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.shop.dto.SubStoreCriteria;
import moonstone.shop.impl.dao.SubStoreDao;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RpcProvider
public class SubStoreReadServiceImpl implements SubStoreReadService {
    @Autowired
    private SubStoreDao subStoreDao;

    public Response<List<SubStore>> findByUserIdAndStatus(long userId, @Nullable Integer status) {
        try {
            return Response.ok(subStoreDao.findByUserId(userId, status));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findByUserId failed by {}", userId);
            ex.printStackTrace();
            return Response.fail("fail.find.subStore");
        }
    }

    public Response<List<SubStore>> findByUserId(long userId) {
        try {
            return Response.ok(subStoreDao.findByUserId(userId, -1));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findByUserId failed by {}", userId);
            ex.printStackTrace();
            return Response.fail("fail.find.subStore");
        }
    }

    public Response<List<SubStore>> findByShopId(long shopId) {
        try {
            return Response.ok(subStoreDao.findByShopId(shopId));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findByShopId failed by {}", shopId);
            ex.printStackTrace();
            return Response.fail("fail.find.subStore");
        }
    }

    public Response<SubStore> findById(Long id) {
        try {
            return Response.ok(subStoreDao.findById(id));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findById failed by {}", id);
            ex.printStackTrace();
            return Response.fail("fail.find.subStore");
        }
    }

    public Response<List<SubStore>> findByIds(List<Long> ids) {
        try {
            return Response.ok(subStoreDao.findByIds(ids));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findByIds failed by {}", ids);
            ex.printStackTrace();
            return Response.fail("fail.find.subStore");
        }
    }


    public Response<Paging<SubStore>> paging(SubStoreCriteria criteria) {
        if("0".equals(criteria.getAllinpayRoleType())){
            criteria.setAllinpayRoleType(null);
        }
        return paging(criteria.toMap());
    }

    @Override
    public Response<List<Long>> findSubStoreIds(SubStoreCriteria criteria) {
        try {
            return Response.ok(subStoreDao.findSubStoreIds(criteria.toMap()));
        } catch (Exception ex) {
            log.error("SubStoreReadServiceImpl.findSubStoreIds error, criteria={}", JSON.toJSONString(criteria), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<SubStore>> findByUserIds(List<Long> userIds, Long shopId) {
        try {
            if (CollectionUtils.isEmpty(userIds) || shopId == null) {
                return Response.fail("入参皆不能为空");
            }

            return Response.ok(subStoreDao.findByUserIds(userIds, shopId));
        } catch (Exception ex) {
            log.error("SubStoreReadServiceImpl.findByUserIds error, userIds={}, shopId={}",
                    JSON.toJSONString(userIds), shopId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    public Response<Paging<SubStore>> paging(Map<String, Object> criteria) {
        try {
            return Response.ok(subStoreDao.paging(criteria));

        } catch (Exception ex) {
            log.error("[SubStoreService](read) paging failed by {}", criteria.toString());
            ex.printStackTrace();
            return Response.fail("fail.find.subStore");
        }
    }

    @Override
    public Response<SubStore> findUserIdAndShopId(long userId, long shopId) {
        try {
            return Response.ok(subStoreDao.findByUserIdAndShopId(userId, shopId));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) find by userId:{} and shopId:{} failed", userId, shopId, ex);
            return Response.fail("fail.find.subStore");
        }
    }

    @Override
    public Response<Boolean> isSubStoreOwner(long userId) {
        try {
            return Response.ok(subStoreDao.countByUserId(userId) > 0);
        } catch (Exception ex) {
            log.error("[SubStoreService](read) verify user failed by userId:{}", userId);
            ex.printStackTrace();
            return Response.fail("fail.query.subStore");
        }
    }

    @Override
    public Response<List<SubStore>> findByMobileLikeAndUserIdsAndShopId(String mobile, @Nullable List<Long> userIds, @Nullable Long shopId) {
        try {
            return Response.ok(subStoreDao.findByMobileLikeAndUserIdsAndShopId(mobile, userIds, shopId));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findByMobileLikeAndUserIds failed by mobile:{} userIds:{}", mobile, Arrays.toString(userIds.toArray()));
            ex.printStackTrace();
            return Response.fail("fail.query.subStore");
        }
    }


    public Response<List<SubStore>> findByMobileAndNameLikeAndShopId(long shopId, @Nullable String mobile, @Nullable String name) {
        try {
            return Response.ok(subStoreDao.findByMobileAndNameLikeAndShopId(shopId, mobile, name));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findByMobileAndNameLikeAndShopId failed by shopId:{} mobile:{} name:{}", shopId, mobile, name);
            ex.printStackTrace();
            return Response.fail("fail.query.subStore");
        }
    }

    public Response<List<Long>> findIdsByName(String name, @Nullable Integer status, @Nullable Integer notStatus) {
        try {
            return Response.ok(subStoreDao.findIdsByName(name, status, notStatus));
        } catch (Exception ex) {
            log.error("[SubStoreService](read) findIdsByName failed by name:{}", name);
            ex.printStackTrace();
            return Response.fail("fail.query.subStore");
        }
    }

    @Override
    public Response<List<Long>> findUserIdsByName(Long shopId, String name) {
        try {
            return Response.ok(subStoreDao.findUserIdsByName(shopId, name));
        } catch (Exception ex) {
            log.error("SubStoreReadServiceImpl.findUserIdsByName failed by shopId={}， name={}", shopId, name, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> count(SubStoreCriteria subStoreCriteria) {
        try {
            return Response.ok(subStoreDao.count(subStoreCriteria));
        } catch (Exception ex) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName(), subStoreCriteria);
            ex.printStackTrace();
            return Response.fail("fail.count.subStore");
        }
    }

    @Override
    public Response<Boolean> existsByMobile(Long shopId, String mobile) {
        try {
            return Response.ok(subStoreDao.existsByMobile(shopId, mobile));
        } catch (Exception ex) {
            log.error("{} shopId:{} mobile:{}", LogUtil.getClassMethodName(), shopId, mobile);
            ex.printStackTrace();
            return Response.fail("fail.count.by.mobile");
        }
    }

    @Override
    public Response<Boolean> existsByName(Long shopId, String name) {
        try {
            return Response.ok(subStoreDao.existsByName(shopId, name));
        } catch (Exception ex) {
            log.error("{} shopId:{} name:{}", LogUtil.getClassMethodName(), shopId, name);
            ex.printStackTrace();
            return Response.fail("fail.count.by.name");
        }
    }

    @Override
    public Response<SubStore> findByMobile(Long shopId, String mobile) {
        try {
            return Response.ok(subStoreDao.findByMobile(shopId, mobile));
        } catch (Exception ex) {
            log.error("{} shopId:{} mobile:{}", LogUtil.getClassMethodName(), shopId, mobile);
            ex.printStackTrace();
            return Response.fail("fail.query.by.mobile");
        }
    }

    @Override
    public Map<Long, String> findSubStoreNameMap(List<Long> subStoreUserIds, Long shopId) {
        if (CollectionUtils.isEmpty(subStoreUserIds)) {
            return Collections.emptyMap();
        }

        var list = findByUserIds(subStoreUserIds, shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(SubStore::getUserId, SubStore::getName, (k1, k2) -> k1));
    }

    @Override
    public Map<Long, SubStore> findMapByIds(List<Long> subStoreIds) {
        if (CollectionUtils.isEmpty(subStoreIds)) {
            return Collections.emptyMap();
        }

        var list = findByIds(subStoreIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(SubStore::getId, o -> o, (k1, k2) -> k1));
    }

    @Override
    public SubStore findBySubStoreNameAndShopId(long shopId, String mobile) {
        return subStoreDao.findByShopIdAndMobile(shopId, mobile);
    }
}
