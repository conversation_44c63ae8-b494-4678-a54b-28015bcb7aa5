package moonstone.item.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.search.core.*;
import io.terminus.search.model.AliasAction;
import io.terminus.search.model.CutWords;
import io.terminus.search.model.ESSearchResponse;
import io.terminus.search.model.Token;
import io.terminus.search.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.*;

@Slf4j
public class ESClient6 extends ESClient {
    private final ObjectMapper objectMapper;
    private final String hostname;
    private final int port;

    public ESClient6(String hostname, int port) {
        super(hostname, port);
        this.objectMapper = JsonUtil.JSON_NON_EMPTY_MAPPER;
        this.hostname = hostname;
        this.port = port;
    }

    public boolean health() {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/_cluster/health";
            HttpRequest request = HttpRequest.get(url).contentType(HttpRequest.CONTENT_TYPE_JSON);
            if (this.isOk(request.code())) {
                log.info("cluster status :{}", request.body());
                return true;
            } else {
                return false;
            }
        } catch (Exception var3) {
            log.error("failed to elasticsearch check status ", var3);
            return false;
        }
    }

    public void createIndexIfNotExists(String indexName) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName;
        if (this.isOk(HttpRequest.head(url).contentType(HttpRequest.CONTENT_TYPE_JSON)
                .code())) {
            log.info("index(name={}) has exists, skip", indexName);
        } else {
            HttpRequest request = HttpRequest.put(url).contentType(HttpRequest.CONTENT_TYPE_JSON);
            if (!this.isOk(request.code())) {
                log.error("failed to create index (indexName={}), http status:{}", indexName, request.code());
                throw new IndexException(request.body());
            } else {
                log.info("create index(name={}) success", indexName);
            }
        }
    }

    public void deleteIndex(String indexName) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName;
        HttpRequest request = HttpRequest.delete(url).contentType(HttpRequest.CONTENT_TYPE_JSON);
        if (!this.isOk(request.code())) {
            log.error("failed to delete index (indexName={}), http status:{}", indexName, request.code());
            throw new IndexException(request.body());
        } else {
            log.info("delete index(name={}) sucess", indexName);
        }
    }

    public void createMappingIfNotExists(String indexName, String indexType, String mapping) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/" + indexType;
        if (this.isOk(HttpRequest.head(url).contentType(HttpRequest.CONTENT_TYPE_JSON).code())) {
            log.info("index mapping(indexName={}, indexType={}) has exists, skip", indexName, indexType);
        } else {
            HttpRequest request = HttpRequest.put(url + "/_mapping").contentType(HttpRequest.CONTENT_TYPE_JSON).send(mapping);
            if (!this.isOk(request.code())) {
                log.error("failed to create mapping for  (indexName={}, indexType={}, id={}), error:{}", indexName, indexType, request.code());
                throw new IndexException(request.body());
            } else {
                log.info("create mapping for Index(name={}, type={}) success, mapping:{}", indexName, indexType, mapping);
            }
        }
    }

    public void index(String indexName, String indexType, Object id, String document) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/" + indexType + "/" + id;

        Integer code = null;
        String body = null;
        try {
            HttpRequest request = HttpRequest.post(url).contentType(HttpRequest.CONTENT_TYPE_JSON).send(document);
            code = request.code();
            body = request.body();
            log.debug("ESClient6.index, url={}, code={}, body={}", url, code, body);

            if (!this.isOk(code)) {
                throw new IndexException(body);
            }
        } catch (Exception ex) {
            log.error("ESClient6.index, failed to index document (indexName={}, indexType={}, id={}), errorCode={}, body={}",
                    indexName, indexType, id, code, body, ex);
            throw ex;
        }
    }

    public void update(String indexName, String indexType, Object id, Object document) throws Exception {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/" + indexType + "/" + id + "/_update";
        Map<String, Object> doc = new HashMap<>();
        doc.put("doc", document);
        HttpRequest request = HttpRequest.post(url).contentType(HttpRequest.CONTENT_TYPE_JSON).send(this.objectMapper.writeValueAsString(doc));
        if (!this.isOk(request.code())) {
            log.error("fail to update document (indexName={}, indexType={}, id={}), error:{}", indexName, indexType, id, request.code());
            throw new UpdateException(request.body());
        }
    }

    private boolean isOk(int code) {
        return code >= 200 && code < 300;
    }

    private boolean notFound(int code) {
        return code == 404;
    }

    public void delete(String indexName, String indexType, Object id) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/" + indexType + "/" + id;

        Integer code = null;
        String body = null;
        try {
            HttpRequest request = HttpRequest.delete(url).contentType(HttpRequest.CONTENT_TYPE_JSON);
            code = request.code();
            body = request.body();
            log.debug("ESClient.delete, url={}, code={}, body={}", url, code, body);

            if (!this.isOk(code)) {
                throw new IndexException(body);
            }
        } catch (Exception ex) {
            log.error("ESClient.delete, failed to delete document (indexName={}, indexType={}, id={}),errorCode={}, body={}",
                    indexName, indexType, id, code, body, ex);
            throw ex;
        }
    }

    public void deleteByQuery(String indexName, String indexType, String criteria) {
    }

    public void bulk(String indexName, String indexType, String payload) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/" + indexType + "/_bulk";
        HttpRequest request = HttpRequest.post(url)
                .contentType(HttpRequest.CONTENT_TYPE_JSON)
                .send(payload);
        if (!this.isOk(request.code())) {
            log.error("failed to bulk index (indexName={}, indexType={}, payload={}),error:{}", indexName, indexType, payload, request.code());
            throw new IndexException(request.body());
        }
    }

    public ESSearchResponse search(String indexName, String indexType, String criteria) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/" + indexType + "/_search";
        log.info("ESClient6.search, url={}, criteria={}", url, criteria);

        HttpRequest request;
        String body;
        try {
            request = HttpRequest.post(url)
                    .contentType(HttpRequest.CONTENT_TYPE_JSON)
                    .send(criteria);
            body = request.body();
        } catch (Exception ex) {
            log.error("ESClient6.search, post request with error: ", ex);
            throw ex;
        }
        log.info("ESClient6.search, request.body={}", body);

        if (!this.isOk(request.code())) {
            log.error("failed to search (indexName={}, indexType={}, criteria={}),code={}, body={}",
                    indexName, indexType, criteria, request.code(), body);
            throw new SearchException(body);
        } else {
            try {
                return this.objectMapper.readValue(body, ESSearchResponse.class);
            } catch (Exception var8) {
                log.error("failed to deserialize es response:{}, cause:{}", body, Throwables.getStackTraceAsString(var8));
                throw new SearchException(var8);
            }
        }
    }

    public boolean addWord(String httpHost, Integer httpPort, String word) {
        String url = "http://" + httpHost + ":" + httpPort + "/_dict";
        HttpRequest request = HttpRequest.get(HttpRequest.encode(url + "?word=" + word)).contentType(HttpRequest.CONTENT_TYPE_JSON);
        if (!this.isOk(request.code())) {
            log.error("failed to add word to node(host={},port={}),word:{} code:{}", httpHost, httpPort, word, request.code());
            throw new SearchException(request.body());
        } else {
            return true;
        }
    }

    public List<String> getNodesIp() {
        String url = "http://" + this.hostname + ":" + this.port + "/_cat/nodes?h=ip";
        HttpRequest request = HttpRequest.get(url).contentType(HttpRequest.CONTENT_TYPE_JSON);
        if (!this.isOk(request.code())) {
            log.error("failed to get node ips from (host={},port={}),error:{}", this.hostname, this.port, request.code());
            throw new SearchException(request.body());
        } else {
            return Splitter.on(CharMatcher.breakingWhitespace()).omitEmptyStrings().trimResults().splitToList(request.body());
        }
    }

    public boolean addWord(String word) {
        List<String> nodes = this.getNodesIp();
        Iterator var3 = nodes.iterator();

        while (var3.hasNext()) {
            String node = (String) var3.next();
            this.addWord(node, this.port, word);
        }

        return true;
    }

    public List<String> cutWords(String sentence, String mode) throws Exception {
        String url = "http://" + this.hostname + ":" + this.port + "/_analyze";
        Map<String, String> params = Maps.newHashMap();
        params.put("analyzer", mode);
        params.put("text", sentence);
        HttpRequest request = HttpRequest.post(url)
                .contentType(HttpRequest.CONTENT_TYPE_JSON)
                .send(JsonUtil.JSON_NON_EMPTY_MAPPER.writeValueAsString(params));
        if (!this.isOk(request.code())) {
            log.error("failed to cut words for ({}) from (host={},port={}),error:{}", sentence, this.hostname, this.port, request.code());
            throw new SearchException(request.body());
        } else {
            CutWords cutWords = JsonUtil.JSON_NON_EMPTY_MAPPER.readValue(request.body(), CutWords.class);
            List<String> result = Lists.newArrayListWithCapacity(cutWords.getTokens().size());
            Iterator var8 = cutWords.getTokens().iterator();

            while (var8.hasNext()) {
                Token token = (Token) var8.next();
                result.add(token.getToken());
            }

            return result;
        }
    }

    public boolean addAlias(String indexName, String aliasName) throws Exception {
        AliasAction action = new AliasAction(AliasAction.Operate.ADD, indexName, aliasName);
        return this.alias(action);
    }

    public boolean removeAlias(String indexName, String aliasName) throws Exception {
        AliasAction action = new AliasAction(AliasAction.Operate.REMOVE, indexName, aliasName);
        return this.alias(action);
    }

    public boolean renameAlias(String indexName, String oldAliasName, String newAliasName) throws Exception {
        AliasAction remove = new AliasAction(AliasAction.Operate.REMOVE, indexName, oldAliasName);
        AliasAction add = new AliasAction(AliasAction.Operate.ADD, indexName, newAliasName);
        return this.alias(remove, add);
    }

    public boolean changeAliasIndex(String aliasName, String oldIndexName, String newIndexName) throws Exception {
        AliasAction remove = new AliasAction(AliasAction.Operate.REMOVE, oldIndexName, aliasName);
        AliasAction add = new AliasAction(AliasAction.Operate.ADD, newIndexName, aliasName);
        return this.alias(remove, add);
    }

    public boolean alias(AliasAction... aliasActions) throws Exception {
        String url = "http://" + this.hostname + ":" + this.port + "/_aliases";
        List<Map> actions = Lists.newArrayListWithCapacity(aliasActions.length);
        AliasAction[] var4 = aliasActions;
        int var5 = aliasActions.length;

        for (int var6 = 0; var6 < var5; ++var6) {
            AliasAction aliasAction = var4[var6];
            Map mappedAlias = ImmutableMap.of("index", aliasAction.getIndexName(), "alias", aliasAction.getAliasName());
            Map mappedAction = ImmutableMap.of(aliasAction.getOperate().value(), mappedAlias);
            actions.add(mappedAction);
        }

        Map params = ImmutableMap.of("actions", actions);
        HttpRequest request = HttpRequest.post(url).contentType(HttpRequest.CONTENT_TYPE_JSON)
                .send(JsonUtil.JSON_NON_EMPTY_MAPPER.writeValueAsString(params));
        if (log.isDebugEnabled()) {
            log.debug("alias add url calling {}", url);
            log.debug("alias add payload {}", JsonUtil.JSON_NON_EMPTY_MAPPER.writeValueAsString(actions));
        }

        if (!this.isOk(request.code())) {
            log.error("failed to add alias from host:{} port:{} actions:{} error:{}", this.hostname, this.port, aliasActions, request.code());
            throw new SearchException(request.body());
        } else {
            return true;
        }
    }

    public List<String> getIndexesOfAlias(String aliasName) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + aliasName;
        if (log.isDebugEnabled()) {
            log.debug("alias add url calling {}", url);
        }

        HttpRequest request = HttpRequest.get(url).contentType(HttpRequest.CONTENT_TYPE_JSON);
        if (!this.isOk(request.code())) {
            if (this.notFound(request.code())) {
                return Collections.emptyList();
            } else {
                log.error("failed to query alias {} host:{} port:{} error:{}", aliasName, this.hostname, this.port, request.code());
                throw new SearchException(request.body());
            }
        } else {
            String body = request.body();

            try {
                List<String> indexNames = Lists.newArrayList();
                TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {
                };
                Map<String, Object> mappedAlias = this.objectMapper.readValue(body, typeRef);
                Iterator var8 = mappedAlias.entrySet().iterator();

                while (var8.hasNext()) {
                    Map.Entry<String, Object> entry = (Map.Entry) var8.next();
                    indexNames.add(entry.getKey());
                }

                return indexNames;
            } catch (IOException var10) {
                log.error("fail to convert json to map: \n {}", body);
                throw new AliasException(var10.getMessage());
            }
        }
    }

    public void createIndexTemplateIfNotExists(String templateName, String template) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + "_template" + "/" + templateName;
        if (this.isOk(HttpRequest.head(url).contentType(HttpRequest.CONTENT_TYPE_JSON).code())) {
            log.info("template(name={}) has exists, skip", templateName);
        } else {
            HttpRequest request = HttpRequest.put(url).contentType(HttpRequest.CONTENT_TYPE_JSON).send(template);
            if (!this.isOk(request.code())) {
                log.error("failed to create template(templateName={}, template={}), error:{}", templateName, template, request.code());
                throw new IndexException(request.body());
            } else {
                log.info("create template(name={}) success, template:{}", templateName, template);
            }
        }
    }
}
