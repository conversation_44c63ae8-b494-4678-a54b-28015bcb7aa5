package moonstone.web.core.component.api

import com.github.kevinsawicki.http.HttpRequest
import com.github.kevinsawicki.http.HttpRequest.HttpRequestException
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import moonstone.common.factory.api.RpcInvokeAdvise.log
import moonstone.common.factory.api.{OmsAPI, RpcInvokeAdvise}
import moonstone.common.model.{Either, Y800OpenRequestV3, Y800OpenResV4}
import moonstone.common.utils.{LogUtil, Translate}
import moonstone.web.core.component.api.OmsRPCCore.{gson, logger, storage}
import org.slf4j.{Logger, LoggerFactory}

import java.lang.reflect.{Method, ParameterizedType, WildcardType}
import java.util.Objects
import scala.collection.mutable

object OmsRPCCore {
  final val storage: ThreadLocal[mutable.Map[String, String]] = ThreadLocal.withInitial(() => mutable.HashMap.empty[String, String])
  final val logger: Logger = LoggerFactory.getLogger(classOf[OmsRPCCore])
  final val gson: Gson = new Gson()
}

trait OmsRPCCore extends OmsAPI with RpcInvokeAdvise with AutoCloseable {

  /**
   * http-RPC网关地址
   */
  def apiUrl: String


  /**
   * 执行远程调用
   *
   * @param method 方法
   * @param args   参数
   * @return 返回远程调用的结果
   */
  override def proceed(method: Method, args: Array[Any]): AnyRef
  = {
    predicateArgs(method, args)
    val openRequest = new Y800OpenRequestV3
    openRequest.setAppId(getAppId)
    if (!args.isEmpty)
      openRequest.setBizData(args(0))
    openRequest.setServiceName(convertMethodIntoServiceName(method))
    openRequest.sign(getSecret)
    logger.debug(s"${LogUtil.getClassMethodName} invoke [${openRequest.toMap}] at gateWay[$apiUrl]")
    try {
      val httpRequest = HttpRequest.post(apiUrl)
        .contentType(HttpRequest.CONTENT_TYPE_FORM)
        .form(openRequest.toMap)
      if (!httpRequest.ok()) return Left(httpRequest.message())
      val body = httpRequest.body()
      logger.debug(s"${LogUtil.getClassMethodName} service[${openRequest.getServiceName}] response[$body]")
      val response = gson.fromJson(body, classOf[Y800OpenResV4[Map[String, String]]])
      if (response.isSuccess)
        Right(gson.toJson(response.getResult))
      else
        Left(Option.apply(response.getError).getOrElse(response.getErrorMessage))
    }
    catch {
      case httpRequestException: HttpRequestException =>
        logger.error(s"${LogUtil.getClassMethodName} http request failed at request[${openRequest.toMap} url[$apiUrl]", httpRequestException)
        Left(httpRequestException.getMessage)
      case ex: Exception =>
        logger.error(s"${LogUtil.getClassMethodName} fail to invoke rpc [${openRequest.toMap} url[$apiUrl]", ex)
        Left(ex.getMessage)
    }
  }

  /**
   * 将数据解析为方法名指定的类型
   *
   * @param method RPC本地方法,携带返回数据类型
   * @param result 回调结果
   * @return 回调结果
   */
  override def decodeAfterInvoke(method: Method, result: AnyRef): AnyRef = {
    try {
      val targetType = method.getReturnType match {
        case noResult if noResult == null || !classOf[Either[Any]].isAssignableFrom(noResult) => Left(noResult)
        case _ => Right(method.getGenericReturnType match {
          case parameterizedType: ParameterizedType => parameterizedType.getActualTypeArguments()(0)
          case wildcardType: WildcardType => wildcardType.getLowerBounds()(0)
          case other => other
        })
      }
      targetType match {
        case Right(realType) => result match {
          case Left(error) => Either.error(new RuntimeException(new Translate("[RPC] 服务端返回错误 原因[%s]", error.toString).toString), error.toString)
          case Right(data) => Either.ok(gson.fromJson(Objects.toString(data), TypeToken.get(realType).getType))
        }
        case Left(rawType) => result match {
          case Left(error) => throw new RuntimeException(new Translate("[RPC] 调用错误 原因[%s]", error.toString).toString)
          case Right(data) => gson.fromJson(Objects.toString(data), TypeToken.get(rawType).getType)
        }
      }
    }
    catch {
      case ex: Exception =>
        logger.error(s"${LogUtil.getClassMethodName} can't decode the target [{}]", result, ex)
        throw ex
    }
  }

  /**
   * 转换method进入为服务名
   *
   * @param method 服务名来源,将方法名转换为ServiceName,每个大写字母转换为.小写
   *               例子: serviceName => service.name
   * @return RPC使用的service名
   */
  def convertMethodIntoServiceName(method: Method): String = {
    val builder = new mutable.StringBuilder()
    method.getName.foreach({
      case lower if lower.isLower => builder.append(lower)
      case upperChar if upperChar.isUpper => builder.append('.').append(upperChar.toLower)
      case other => builder.append(other)
    })
    builder.toString()
  }

  /**
   * 判断参数
   *
   * @param method 调用的目标RPC方法
   * @param array  参数
   */
  private def predicateArgs(method: Method, array: Array[Any]): Unit = {
    if (array.length > 1) log.warning(s"give more than one args when invoke method [${method.getName}] args size [${array.length}] by appId [$getAppId] with secret [$getSecret] at apiUrl [$apiUrl]")
  }

  /**
   * 清除这个线程里对appId的使用,防止内存泄漏
   */
  override def close(): Unit = storage.remove()

  /**
   * 设置从洋800 平台获取的appId
   *
   * @param appId 洋800区分渠道凭证
   */
  def setAppId(appId: String): Unit = storage.get().put("appId", appId)

  private def getAppId: String = storage.get().getOrElse("appId", null)

  /**
   * 设置密钥
   *
   * @param secret 密钥
   */
  def setSecret(secret: String): Unit = storage.get().put("secret", secret)

  private def getSecret: String = storage.get().getOrElse("secret", null)

}
