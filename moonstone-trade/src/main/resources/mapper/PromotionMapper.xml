<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Promotion">

    <resultMap id="PromotionMap" type="Promotion">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="promotion_def_id" property="promotionDefId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="start_at" property="startAt"/>
        <result column="end_at" property="endAt"/>
        <result column="user_scope_params_json" property="userScopeParamsJson"/>
        <result column="sku_scope_params_json" property="skuScopeParamsJson"/>
        <result column="condition_params_json" property="conditionParamsJson"/>
        <result column="behavior_params_json" property="behaviorParamsJson"/>
        <result column="extra_json" property="extraJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="issue_status" property="issueStatus"/>
    </resultMap>

    <sql id="tb">
        parana_promotions
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shop_id`,`name`,`description`,`promotion_def_id`,`type`,`status`,`start_at`,`end_at`,
        `user_scope_params_json`,`sku_scope_params_json`,`condition_params_json`,`behavior_params_json`,`extra_json`,
        `created_at`,`updated_at`, `issue_status`
    </sql>

    <sql id="vals">
        #{shopId},#{name},#{description},#{promotionDefId},#{type},#{status},#{startAt},#{endAt},
        #{userScopeParamsJson},#{skuScopeParamsJson},#{conditionParamsJson},#{behaviorParamsJson},#{extraJson},
        now(),now(), #{issueStatus}
    </sql>

    <sql id="criteria">
        <if test="name != null">AND name LIKE CONCAT(#{name} ,'%')</if>
        <if test="status != null"> AND `status`=#{status}</if>
        <if test="type != null"> AND `type`=#{type}</if>
        <if test="promotionDefId != null"> AND `promotion_def_id`=#{promotionDefId}</if>
    </sql>

    <insert id="create" parameterType="Promotion" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="PromotionMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="PromotionMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByShopIdAndStatus" parameterType="map" resultMap="PromotionMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
        AND
        `status`=#{status}
        order by id desc
    </select>

    <select id="findByShopIdAndStatuses" parameterType="map" resultMap="PromotionMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
        AND `status` IN
        <foreach item="status" collection="statuses" open="(" separator="," close=")">
            #{status}
        </foreach>
        <if test="type != null">
            and `type` = #{type}
        </if>
        <if test="issueStatus != null">
            and issue_status = #{issueStatus}
        </if>
    </select>

    <select id="paging" parameterType="map" resultMap="PromotionMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        shop_id = #{shopId}
        <include refid="criteria"/>
        order by id desc
        LIMIT #{offset}, #{limit}
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        WHERE
        shop_id = #{shopId}
        <include refid="criteria"/>
    </select>


    <update id="update" parameterType="Promotion">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="name != null">,`name` = #{name}</if>
            <if test="description != null">,`description` = #{description}</if>
            <if test="status != null">,`status` = #{status}</if>
            <if test="startAt != null">,`start_at` = #{startAt}</if>
            <if test="endAt != null">,`end_at` = #{endAt}</if>
            <if test="userScopeParamsJson != null">,`user_scope_params_json` = #{userScopeParamsJson}</if>
            <if test="skuScopeParamsJson != null">,`sku_scope_params_json` = #{skuScopeParamsJson}</if>
            <if test="conditionParamsJson != null">,`condition_params_json` = #{conditionParamsJson}</if>
            <if test="behaviorParamsJson != null">,`behavior_params_json` = #{behaviorParamsJson}</if>
            <if test="extraJson != null">,`extra_json` = #{extraJson}</if>
            <if test="issueStatus != null">,`issue_status` = #{issueStatus}</if>
        </set>
        WHERE id=#{id}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="countBy" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        where shop_id=#{shopId} and promotion_def_id=#{promotionDefId}
        <if test="statuses!=null">
            and `status` IN
            <foreach item="status" collection="statuses" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <update id="setOngoing">
        UPDATE <include refid="tb"/>
        SET status=2,updated_at=now() WHERE status=1 and start_at &lt;=now() and end_at &gt;=now()
    </update>

    <update id="setExpire">
        UPDATE <include refid="tb"/>
        SET status=-1,updated_at=now() WHERE status=2 and end_at &lt;now()
    </update>

</mapper>