<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="DepotView">
    <resultMap id="DepotViewMapper" type="moonstone.stock.model.DepotView">
        <id column="id" property="id"/>
        <result column="depot_code" property="depotCode"/>
        <result column="depot_name" property="depotName"/>
        <result column="hash" property="hash"/>
    </resultMap>
    <sql id="tb">
        depot_view
    </sql>

    <select id="findById" parameterType="long" resultMap="DepotViewMapper">
        SELECT
        `id`, `depot_code`, `depot_name`, `hash`
        FROM
        <include refid="tb"/>
        <where>
            `id` = #{id}
        </where>
    </select>

    <insert id="create" parameterType="moonstone.stock.model.DepotView" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb"/>
        (`hash`, `depot_code`, `depot_name`)
        VALUES (#{hash}, #{depotCode}, #{depotName}) on duplicate key  update `hash` = #{hash}
    </insert>

    <select id="findByHash" parameterType="map" resultMap="DepotViewMapper">
        SELECT
        `id`, `hash`, `depot_code`, `depot_name`
        FROM
        <include refid="tb"/>
        <where>
            `hash` = #{hash}
        </where>
    </select>

</mapper>