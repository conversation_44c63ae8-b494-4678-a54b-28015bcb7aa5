<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="FundsTransferPayment">
    <resultMap id="BaseResultMap" type="FundsTransferPayment">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>

        <result column="related_order_id" property="relatedOrderId"/>
        <result column="related_order_type" property="relatedOrderType"/>
        <result column="order_no" property="orderNo"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="status" property="status"/>
        <result column="pay_channel" property="payChannel"/>

        <result column="amount" property="amount"/>
        <result column="paid_at" property="paidAt"/>
        <result column="pay_request" property="payRequest"/>
        <result column="pay_response" property="payResponse"/>
        <result column="pay_callback" property="payCallback"/>

        <result column="extra_json" property="extraJson"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <sql id="tb">
        parana_funds_transfer_payment
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shop_id`,
        `related_order_id`,
        `related_order_type`,
        `order_no`,
        `trade_no`,
        `status`,
        `pay_channel`,
        `amount`,
        `paid_at`,
        `pay_request`,
        `pay_response`,
        `pay_callback`,
        `extra_json`,
        `is_valid`,
        `created_at`,
        `updated_at`,
        `created_by`,
        `updated_by`
    </sql>

    <insert id="insertSelective" parameterType="FundsTransferPayment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
        <if test="shopId != null">
            `shop_id`,
        </if>
        <if test="relatedOrderId != null">
            `related_order_id`,
        </if>
        <if test="relatedOrderType != null">
            `related_order_type`,
        </if>
        <if test="orderNo != null">
            `order_no`,
        </if>
        <if test="tradeNo != null">
            `trade_no`,
        </if>
        <if test="status != null">
            `status`,
        </if>
        <if test="payChannel != null">
            `pay_channel`,
        </if>
        <if test="amount != null">
            `amount`,
        </if>
        <if test="paidAt != null">
            `paid_at`,
        </if>
        <if test="payRequest != null">
            `pay_request`,
        </if>
        <if test="payResponse != null">
            `pay_response`,
        </if>
        <if test="payCallback != null">
            `pay_callback`,
        </if>
        <if test="extraJson != null">
            `extra_json`,
        </if>
        <if test="isValid != null">
            `is_valid`,
        </if>
        <if test="createdBy != null">
            `created_by`,
        </if>
        <if test="updatedBy != null">
            `updated_by`,
        </if>
        `created_at`,
        `updated_at`
        )
        VALUES
        (
        <if test="shopId != null">
            #{shopId},
        </if>
        <if test="relatedOrderId != null">
            #{relatedOrderId},
        </if>
        <if test="relatedOrderType != null">
            #{relatedOrderType},
        </if>
        <if test="orderNo != null">
            #{orderNo},
        </if>
        <if test="tradeNo != null">
            #{tradeNo},
        </if>
        <if test="status != null">
            #{status},
        </if>
        <if test="payChannel != null">
            #{payChannel},
        </if>
        <if test="amount != null">
            #{amount},
        </if>
        <if test="paidAt != null">
            #{paidAt},
        </if>
        <if test="payRequest != null">
            #{payRequest},
        </if>
        <if test="payResponse != null">
            #{payResponse},
        </if>
        <if test="payCallback != null">
            #{payCallback},
        </if>
        <if test="extraJson != null">
            #{extraJson},
        </if>
        <if test="isValid != null">
            #{isValid},
        </if>
        <if test="createdBy != null">
            #{createdBy},
        </if>
        <if test="updatedBy != null">
            #{updatedBy},
        </if>
        now(),
        now()
        )
    </insert>

    <update id="update" parameterType="FundsTransferPayment">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="orderNo != null">
                `order_no` = #{orderNo},
            </if>
            <if test="tradeNo != null">
                `trade_no` = #{tradeNo},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="payRequest != null">
                `pay_request`= #{payRequest},
            </if>
            <if test="payResponse != null">
                `pay_response`= #{payResponse},
            </if>
            <if test="payCallback != null">
                `pay_callback`= #{payCallback},
            </if>
            <if test="extraJson != null">
                `extra_json`= #{extraJson},
            </if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findBy" resultMap="BaseResultMap" parameterType="map">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where is_valid = 1
        and related_order_id = #{relatedOrderId}
        and related_order_type = #{relatedOrderType}
    </select>

    <select id="findById" resultMap="BaseResultMap" parameterType="long">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where is_valid = 1
        and id = #{id}
    </select>
</mapper>