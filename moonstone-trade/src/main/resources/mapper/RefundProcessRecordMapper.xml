<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="RefundProcessRecord">
    <resultMap id="refundProcessRecordMap" type="RefundProcessRecord">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="refund_id" property="refundId"/>
        <result column="refund_round" property="refundRound"/>
        <result column="type" property="type"/>
        <result column="operator_type" property="operatorType"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operation_time" property="operationTime"/>
        <result column="refund_fee" property="refundFee"/>
        <result column="refund_type" property="refundType"/>
        <result column="content" property="content"/>
        <result column="deleted" property="deleted"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_refund_process_record
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shop_id`,`refund_id`,`refund_round`,`type`,`operator_type`,`operator_id`,`operator_name`,`operation_time`,
        `refund_fee`,`refund_type`,`content`,`deleted`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{shopId},
        #{refundId},
        #{refundRound},
        #{type},
        #{operatorType},
        #{operatorId},
        #{operatorName},
        #{operationTime},
        #{refundFee},
        #{refundType},
        #{content},
        0,
        now(),
        now()
    </sql>

    <sql id="criteria">
        deleted = 0
        <if test="id !=null"> AND id= #{id}</if>
        <if test="ids != null"> AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="shopId != null"> AND shop_id = #{shopId}</if>
        <if test="refundId != null"> AND refund_id = #{refundId}</if>
        <if test="refundRound != null"> AND refund_round = #{refundRound}</if>
        <if test="type != null"> AND type = #{type}</if>
        <if test="operatorType != null"> AND operator_type = #{operatorType}</if>
        <if test="operatorId != null"> AND operator_id = #{operatorId}</if>
        <if test="operatorName != null"> AND operator_name = #{operatorName}</if>
        <if test="operationTime != null"> AND operation_time = #{operationTime}</if>
        <if test="refundFee != null"> AND refund_fee = #{refundFee}</if>
        <if test="refundType != null"> AND refund_type = #{refundType}</if>
        <if test="content != null"> AND content = #{content}</if>
        <if test="createdAt !=null"> AND created_at = #{createdAt}</if>
        <if test="updatedAt !=null"> AND updated_at = #{updatedAt}</if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'refundRound'"> ORDER BY refund_round
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'operationTime'">
                ORDER BY operation_time
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <insert id="create" parameterType="RefundProcessRecord" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="RefundProcessRecord">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="id !=null">id= #{id},</if>
            <if test="refundId != null">  refund_id = #{refundId},</if>
            <if test="refundRound != null">  refund_round = #{refundRound},</if>
            <if test="type != null">  type = #{type},</if>
            <if test="operatorType != null">  operator_type = #{operatorType},</if>
            <if test="operatorId != null">  operator_id = #{operatorId},</if>
            <if test="operatorName != null">  operator_name = #{operatorName},</if>
            <if test="operationTime != null">  operation_time = #{operationTime},</if>
            <if test="refundFee != null">  refund_fee = #{refundFee},</if>
            <if test="refundType != null">  refund_type = #{refundType},</if>
            <if test="content != null">  content = #{content},</if>
            <if test="deleted !=null">  deleted = #{deleted},</if>
            <if test="createdAt !=null">  created_at = #{createdAt},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
        and deleted = 0
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <delete id="deletes" parameterType="list">
        DELETE FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDelete" parameterType="long">
        UPDATE
        <include refid="tb"/>
        SET deleted = 1
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="refundProcessRecordMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} and deleted = 0
    </select>

    <select id="selectOne" parameterType="map" resultMap="refundProcessRecordMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="selectList" parameterType="map" resultMap="refundProcessRecordMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
    </select>

    <select id="paging" parameterType="map" resultMap="refundProcessRecordMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>


</mapper>