<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="BalanceDetail">

    <resultMap id="BalanceDetailMap" type="BalanceDetail">
        <id column="id" property="id"/>
        <result column="fee" property="fee"/>
        <result column="change_fee" property="changeFee"/>
        <result column="related_id" property="relatedId"/>
        <result column="user_id" property="userId"/>
        <result column="source_id" property="sourceId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="extra_str" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_balance_detail
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `fee`,`change_fee`,`related_id`,`source_id`,`type`,`user_id`,`status`,
        `extra_str`,`created_at`,`updated_at`
    </sql>

    <sql id="cols_all_with_table_alias">
        t.`id`, t.`fee`, t.`change_fee`, t.`related_id`, t.`source_id`, t.`type`, t.`user_id`, t.`status`,
        t.`extra_str`, t.`created_at`, t.`updated_at`
    </sql>

    <sql id="vals">
        #{fee},#{changeFee},#{relatedId},#{sourceId},#{type},#{userId},#{status}
        ,#{extraStr},now(),now()
    </sql>

    <insert id="create" parameterType="BalanceDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.fee}, #{i.changeFee}, #{i.relatedId}, #{i.sourceId}, #{i.type}, #{i.userId}, #{i.status},
            #{i.extraStr}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findProfitView" parameterType="map" resultMap="BalanceDetailMap">
        SELECT user_id, change_fee, `status`
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by id des
    </select>
    <select id="findById" parameterType="long" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByRelatedIdAndSourceId" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `related_id`= #{relatedId} and `source_id`=#{sourceId}
    </select>

    <select id="findOrderProfit" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `related_id` = #{orderId}
        and `source_id` = #{shopId}
        and `type` = 1
        <foreach collection="status" item="i">
            and ((`status` <![CDATA[&]]> #{i}) = #{i})
        </foreach>
        and 512 != (512 <![CDATA[&]]> `status`)
    </select>

    <select id="findByRelatedId" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `related_id`= #{relatedId}
    </select>
    <select id="findByUserIdAndSourceId" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id= #{userId} and `source_id`=#{sourceId} and `status` != -1
    </select>
    <select id="findByUserId" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id= #{userId} and `status` != -1
    </select>

    <select id="findByIds" parameterType="list" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="BalanceDetail">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="fee != null">,`fee` = #{fee}</if>
            <if test="changeFee != null">,`change_fee` = #{changeFee}</if>
            <if test="relatedId!=null">, `related_id`=#{relatedId}</if>
            <if test="sourceId!=null">, `source_id`=#{sourceId}</if>
            <if test="status != null">,`status` = #{status}</if>
            <if test="type!= null">,`type` = #{type}</if>
            <if test="userId!= null">,`user_id` = #{userId}</if>
            <if test="extraStr!= null">,`extra_str` = #{extraStr}</if>
        </set>
        WHERE id=#{id}
    </update>
    <select id="findByRelateIdAndTypeAndSourceAndStatusMask" parameterType="map" resultMap="BalanceDetailMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        <where>
            `related_id`=#{relatedId}
            and `user_id`=#{userId}
            and `source_id`=#{sourceId}
            and `type`=#{type}
            and <![CDATA[
            `status`&#{status}=#{status}
            ]]>
        </where>
    </select>
    <select id="findByRelatedIdsAndTypeAndSourceIdAndStatusMask" parameterType="map" resultMap="BalanceDetailMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        <where>
            `type` = #{type}
            and
            <![CDATA[
            `status` & #{status} =#{status}
            ]]>
            and `user_id` = #{userId}
            and `source_id`=#{sourceId}
            <if test="relatedIds!= null">
                and `related_id` in
                <foreach collection="relatedIds" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>

        </where>
    </select>
    <sql id="criteria">
        <if test="ids!= null">AND `id` in
            <foreach collection="ids" item="i" separator="," open="(" close=")">
                #{i}
            </foreach>
        </if>
        <if test="userId!= null">AND `user_id`= #{userId}</if>
        <if test="sourceId!= null">AND `source_id`= #{sourceId}</if>
        <if test="changeFee!= null">AND `change_fee`= #{changeFee}</if>
        <if test="fee!= null">AND `fee`= #{fee}</if>
        <if test="status!= null">AND `status`= #{status}</if>
        <if test="notStatus!= null">AND `status`!= #{notStatus}</if>
        <if test="notStatusBitMarks != null">
            <foreach collection="notStatusBitMarks" item="bit">
                and <![CDATA[
                    `status` & #{bit} != #{bit}
                ]]>
            </foreach>
        </if>
        <if test="statusList!= null">AND `status` in
            <foreach collection="statusList" item="i" separator="," open="(" close=")">
                #{i}
            </foreach>
        </if>
        <if test="statusBitMarks!=null">
            <foreach collection="statusBitMarks" item="bit">
                and <![CDATA[
                    `status` & #{bit} =#{bit}
                ]]>
            </foreach>
        </if>
        <if test="relatedId!= null">AND `related_id` = #{relatedId}</if>
        <if test="relatedIds!= null">AND `related_id` in
            <foreach collection="relatedIds" open="(" separator=","
                     close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="type!= null">AND `type`=#{type}</if>
        <if test="createdStartAt!= null">AND <![CDATA[created_at>= #{createdStartAt}]]> </if>
        <if test="createdEndAt!= null">AND <![CDATA[created_at<= #{createdEndAt}]]> </if>
    </sql>
    <update id="updateCache" parameterType="map">
        update
        <include refid="tb"/>
        set `fee`=`fee`+#{fee},
        `updated_at` = now()
        where
        `user_id`=#{userId}
        and `source_id`=#{sourceId}
        and `type` = #{type}
        <if test="present == true">
            and `status`=512
        </if>
        <if test="present != true">
            and `status`=1
        </if>
        and `fee`+#{fee} <![CDATA[
        >= 0
        ]]>
    </update>
    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by id desc
        LIMIT #{offset}, #{limit}
    </select>

    <select id="list" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by id desc
    </select>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>
    <update id="increaseCash" parameterType="map">
        UPDATE
        <include refid="tb"/>
        set
        `fee`=`fee` + #{changeFee},
        `updated_at` = now()
        <where>
            `user_id`=#{userId}
            and `source_id`=#{sourceId}
            and `type` = 3
            <if test="present!=false">
                and `status` = 512
            </if>
            <if test="present!=true">
                and `status` = 1
            </if>
        </where>
    </update>

    <update id="increaseRecord" parameterType="map">
        UPDATE
        <include refid="tb"/>
        set
        `fee`=`fee` + #{changeFee},
        `updated_at` = now()
        <where>
            `user_id`=#{userId}
            and `source_id`=#{sourceId}
            and `type` = 4
            and `status` = 512
        </where>
    </update>

    <select id="findPresentProfitAfter" parameterType="map" resultMap="BalanceDetailMap">
        SELECT * FROM
        <include refid="tb"/>
        <where>
            source_id = #{shopId}
            and created_at >= #{at}
            and <![CDATA[
status & 8 = 8 AND status & 512 = 512
            ]]>
            and `type` = 1
        </where>
    </select>


    <select id="findUnPresentOrder" parameterType="map" resultMap="BalanceDetailMap">
        SELECT * FROM
        <include refid="tb"/>
        <where>
            related_id = #{orderId}
            and user_id = #{userId}
            and <![CDATA[
status & #{statusMask} = #{statusMask}
and status & 8 = 8
and status & 512 != 512
            ]]>
            and `type` = 1
        </where>
        limit 1
    </select>

    <select id="countNotPaid" parameterType="map" resultType="long">
        SELECT count(t.id)

        FROM `parana_balance_detail` t

        LEFT JOIN profit_withdraw_record t_wr ON t.`id` = t_wr.`profit_id`

        LEFT JOIN parana_with_draw_profit_apply t_wrpa ON t_wr.`withdraw_id` = t_wrpa.`id`

        WHERE t.`related_id` IN
        <foreach collection="orderIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        <![CDATA[
          AND t.`status` & 8 = 8
          and t.`status` & 512 = 512
          and t.`type` = 1
          and t.`change_fee` > 0
        ]]>
          AND  (t_wr.`profit_id` IS NULL OR t_wr.`status` != 2)
        <![CDATA[
          AND  (t_wrpa.`id` IS NULL OR t_wrpa.`status` & 32 != 32)
        ]]>
    </select>

    <resultMap id="ShopOrderWithdrawStatusMap" type="moonstone.order.model.result.ShopOrderWithdrawStatusDO">
        <result column="shop_order_id" property="shopOrderId"/>
        <result column="withdraw_status" property="withdrawStatus"/>
        <result column="user_id" property="userId"/>
    </resultMap>
    <select id="findShopOrderWithdrawStatus" parameterType="map" resultMap="ShopOrderWithdrawStatusMap">
        SELECT t_bd.`related_id` shop_order_id,
               t_bd.`user_id` user_id,
               t_pwr.`status` withdraw_status

        FROM parana_balance_detail t_bd

        LEFT JOIN profit_withdraw_record t_pwr ON t_bd.`id` = t_pwr.`profit_id`

        WHERE t_bd.`source_id` = #{shopId}
        AND t_bd.`related_id` IN
        <foreach collection="shopOrderIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
        AND t_bd.`type` = 1
        <![CDATA[
        AND t_bd.`status` & (2 << 2) = (2 << 2)
        AND t_bd.`status` & (2 << 8) = (2 << 8)
        ]]>
    </select>

    <select id="findForAccountStatement" resultMap="BalanceDetailMap" parameterType="map">
        SELECT <include refid="cols_all_with_table_alias"/>

          FROM `parana_balance_detail` t

          JOIN `parana_shop_orders` t_so ON t.related_id = t_so.id
                                        AND t.`type` = 1
                                        AND <![CDATA[ t.`status` & (2 << 2) = (2 << 2) ]]>
                                        AND <![CDATA[ t.`status` & (2 << 8) = (2 << 8) ]]>
                                        AND t_so.`status` NOT IN (-1, -2, -6, -13, -14, -16, -17, 500)

     LEFT JOIN `profit_withdraw_record` t_pwr ON t.id = t_pwr.profit_id

         WHERE t_pwr.status IS NULL
           AND <![CDATA[ t.`created_at` >= #{startTime} ]]>
           AND <![CDATA[ t.`created_at` <= #{endTime} ]]>
           AND t.`source_id` = #{shopId}
           AND t.`user_id` = #{profitBelongUserId}
        <if test="queryServiceProviderProfit != null">
           AND (( t_so.referer_id is not null and <![CDATA[ t.`status` & (2 << 11) != (2 << 11) ]]> )
                or
                ( t_so.referer_id is null ))
        </if>
        <if test="queryGuiderProfit != null">
           AND <![CDATA[ t.`status` & (2 << 11) = (2 << 11) ]]>
           AND t_so.referer_id = #{guiderUserId}
        </if>

        order by t.id asc

        LIMIT #{offset}, #{limit}
    </select>

    <select id="findOrderSummary" parameterType="map" resultType="moonstone.order.model.result.ShopOrderSummaryDO">
        SELECT COUNT(tt.id) totalCount,
               SUM(tt.fee)  totalFee
          FROM (
                SELECT DISTINCT t_so.*

                 FROM parana_balance_detail t

                 JOIN parana_shop_orders t_so ON t.`related_id` = t_so.`id`
                                             AND <![CDATA[ t.`status` & (2 << 2) = (2 << 2)  ]]>
                                             AND <![CDATA[ t.`status` & (2 << 8) != (2 << 8) ]]>
                                             AND t.`type` = 1
                                             AND t.`source_id` = t_so.`shop_id`
                                             AND t_so.`status` IN
                                                <foreach collection="statusList" open="(" item="item" separator="," close=")">
                                                    #{item}
                                                </foreach>

                WHERE t.`source_id` = #{shopId}
                  AND t.`user_id` = #{profitBelongUserId}
                  AND <![CDATA[ t.`created_at` >= #{startAt} ]]>
                  AND <![CDATA[ t.`created_at` <= #{endAt} ]]>
        ) tt
    </select>

    <select id="findOrderProfitSummary" parameterType="map" resultType="java.lang.Long">
        SELECT SUM(tt.profit) FROM (
        SELECT SUM(t_bd_1.change_fee) `profit`

          FROM parana_balance_detail t_bd_1

          JOIN parana_shop_orders t_so ON t_bd_1.`related_id` = t_so.`id`
                                      AND <![CDATA[ t_bd_1.`status` & (2 << 2) = (2 << 2) ]]>
                                      AND t_bd_1.`type` = 1
                                      AND t_so.`status` IN (1, 2)
                                      AND <![CDATA[ t_bd_1.`status` & (2 << 8) != (2 << 8) ]]>
                                      AND t_bd_1.user_id = #{profitBelongUserId}
                                      AND t_bd_1.source_id = #{shopId}
                                      AND (( t_so.referer_id IS NOT NULL AND  <![CDATA[ t_bd_1.`status` & (2 << 11) != (2 << 11) ]]> )
                                            OR
                                            ( t_so.referer_id IS NULL ))
                                      AND <![CDATA[ t_so.`created_at` >= #{startAt} ]]>
                                      AND <![CDATA[ t_so.`created_at` <= #{endAt} ]]>

        UNION

        SELECT SUM(t_bd_2.change_fee) `profit`

          FROM parana_balance_detail t_bd_2

          JOIN parana_shop_orders t_so ON t_bd_2.`related_id` = t_so.`id`
                                      AND <![CDATA[ t_bd_2.`status` & (2 << 2) = (2 << 2) ]]>
                                      AND t_bd_2.`type` = 1
                                      AND t_so.`status` IN ( 3 )
                                      AND <![CDATA[ t_bd_2.`status` & (2 << 8) = (2 << 8) ]]>
                                      AND t_bd_2.user_id = #{profitBelongUserId}
                                      AND t_bd_2.source_id = #{shopId}
                                      AND (( t_so.referer_id IS NOT NULL AND <![CDATA[ t_bd_2.`status` & (2 << 11) != (2 << 11) ]]> )
                                            OR
                                            ( t_so.referer_id IS NULL ))
                                      AND <![CDATA[ t_so.`created_at` >= #{startAt} ]]>
                                      AND <![CDATA[ t_so.`created_at` <= #{endAt} ]]>
                                   ) tt
    </select>

    <select id="findRefundedProfitList" resultMap="BalanceDetailMap" parameterType="map">
        SELECT <include refid="cols_all_with_table_alias"/>
        FROM <include refid="tb"/> t

        JOIN profit_withdraw_record t_pwr ON t.id = t_pwr.`profit_id`
                                         AND t_pwr.`withdraw_id` = #{withdrawApplyId}

        JOIN parana_shop_orders t_so ON t.`related_id` = t_so.`id`
                                    AND t_so.`status` IN
                                    <foreach collection="refundedStatus" open="(" item="item" separator="," close=")">
                                        #{item}
                                    </foreach>
    </select>

    <select id="selectList" parameterType="map" resultMap="BalanceDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>


    <delete id="deleteByRelatedId" parameterType="map">
        DELETE
        FROM
        <include refid="tb"/>
        where related_id = #{relatedId}
    </delete>



</mapper>