<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2015 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SettleOrderDetail">
    <resultMap id="SettleOrderDetailMap" type="SettleOrderDetail">
        <id column="id" property="id" />
        <result column="order_id" property="orderId"/>
		<result column="order_type" property="orderType"/>
		<result column="seller_id" property="sellerId"/>
		<result column="seller_name" property="sellerName"/>
		<result column="origin_fee" property="originFee"/>
		<result column="seller_discount" property="sellerDiscount"/>
		<result column="platform_discount" property="platformDiscount"/>
		<result column="ship_fee" property="shipFee"/>
        <result column="ship_fee_discount" property="shipFeeDiscount"/>
        <result column="tax" property="tax"/>
		<result column="actual_pay_fee" property="actualPayFee"/>
		<result column="gateway_commission" property="gatewayCommission"/>
		<result column="platform_commission" property="platformCommission"/>
		<result column="seller_receivable_fee" property="sellerReceivableFee"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="gateway_trade_no" property="gatewayTradeNo"/>
        <result column="channel" property="channel"/>
        <result column="channel_account" property="channelAccount"/>
        <result column="order_created_at" property="orderCreatedAt"/>
        <result column="order_finished_at" property="orderFinishedAt"/>
        <result column="paid_at" property="paidAt"/>
        <result column="check_status" property="checkStatus"/>
        <result column="check_at" property="checkAt"/>
        <result column="sum_at" property="sumAt"/>
        <result column="extra_json" property="extraJson"/>

        <result column="diff_fee" property="diffFee"/>
        <result column="commission1" property="commission1"/>
        <result column="commission2" property="commission2"/>
        <result column="commission3" property="commission3"/>
        <result column="commission4" property="commission4"/>
        <result column="commission5" property="commission5"/>

        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="table_name">
        parana_settle_order_details
    </sql>

    <sql id="columns_exclude_id">
        `order_id`,`order_type`,`seller_id`,`seller_name`,`origin_fee`,
		`seller_discount`,`platform_discount`,`ship_fee`,`ship_fee_discount`,`tax`,`actual_pay_fee`,
		`gateway_commission`,`platform_commission`,`seller_receivable_fee`,`trade_no`,`gateway_trade_no`,
		`channel`,`channel_account`,`order_created_at`,`order_finished_at`,`paid_at`,`check_at`, `sum_at`,
		`extra_json`,`check_status`,`diff_fee`, `commission1`,`commission2`,`commission3`,`commission4`,`commission5`,
        created_at, updated_at
    </sql>

    <sql id="columns">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="values">
        #{orderId},#{orderType},#{sellerId},#{sellerName},#{originFee},
		#{sellerDiscount},#{platformDiscount},#{shipFee},#{shipFeeDiscount},#{tax},#{actualPayFee},
		#{gatewayCommission},#{platformCommission},#{sellerReceivableFee},#{tradeNo},#{gatewayTradeNo},
		#{channel},#{channelAccount},#{orderCreatedAt},#{orderFinishedAt},#{paidAt},
		#{checkAt},#{sumAt},#{extraJson},#{checkStatus},
		#{diffFee}, #{commission1}, #{commission2}, #{commission3}, #{commission4}, #{commission5},
        now(),now()
    </sql>

    <sql id="criteria">
        1 = 1
        <if test="orderId != null"> and `order_id` = #{orderId}</if>
		<if test="orderType != null"> and `order_type` = #{orderType}</if>
		<if test="sellerId != null"> and `seller_id` = #{sellerId}</if>
		<if test="sellerName != null"> and `seller_name` = #{sellerName}</if>
		<if test="originFee != null"> and `origin_fee` = #{originFee}</if>
		<if test="sellerDiscount != null"> and `seller_discount` = #{sellerDiscount}</if>
		<if test="platformDiscount != null"> and `platform_discount` = #{platformDiscount}</if>
		<if test="shipFee != null"> and `ship_fee` = #{shipFee}</if>
		<if test="shipFeeDiscount != null"> and `ship_fee_discount` = #{shipFeeDiscount}</if>
        <if test="tax != null"> and `tax` = #{tax}</if>
		<if test="actualPayFee != null"> and `actual_pay_fee` = #{actualPayFee}</if>
		<if test="gatewayCommission != null"> and `gateway_commission` = #{gatewayCommission}</if>
		<if test="platformCommission != null"> and `platform_commission` = #{platformCommission}</if>
		<if test="sellerReceivableFee != null"> and `seller_receivable_fee` = #{sellerReceivableFee}</if>
		<if test="paidAtStart != null"> and `paid_at` &gt;= #{paidAtStart}</if>
        <if test="paidAtEnd != null"> and `paid_at` &lt;= #{paidAtEnd}</if>
        <if test="checkAtStart != null"> and `check_at` &gt;= #{checkAtStart}</if>
		<if test="checkAtEnd != null"> and `check_at` &lt;= #{checkAtEnd}</if>
        <if test="tradeNo != null"> and `trade_no` = #{tradeNo}</if>
        <if test="gatewayTradeNo != null"> and `gateway_trade_no` = #{gatewayTradeNo}</if>
        <if test="channel != null"> and `channel` = #{channel}</if>
        <if test="channelAccount != null"> and `channel_account` = #{channelAccount}</if>
        <if test="orderCreatedAt != null"> and `order_created_at` = #{orderCreatedAt}</if>
        <if test="orderFinishedAt != null"> and `order_finished_at` = #{orderFinishedAt}</if>
        <if test="sumAtStart != null">and `sum_at` &gt;= #{sumAtStart}</if>
        <if test="sumAtEnd != null">and `sum_at` &lt;= #{sumAtEnd}</if>
        <if test="checkStatus != null">and `check_status` = #{checkStatus}</if>
    </sql>

    <insert id="create" parameterType="SettleOrderDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES (<include refid="values"/>)
    </insert>

    <update id="update" parameterType="SettleOrderDetail">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="orderId != null"> `order_id` = #{orderId}, </if>
			<if test="orderType != null"> `order_type` = #{orderType}, </if>
			<if test="sellerId != null"> `seller_id` = #{sellerId}, </if>
			<if test="sellerName != null"> `seller_name` = #{sellerName}, </if>
			<if test="originFee != null"> `origin_fee` = #{originFee}, </if>
			<if test="sellerDiscount != null"> `seller_discount` = #{sellerDiscount}, </if>
			<if test="platformDiscount != null"> `platform_discount` = #{platformDiscount}, </if>
			<if test="shipFee != null"> `ship_fee` = #{shipFee}, </if>
			<if test="shipFeeDiscount != null"> `ship_fee_discount` = #{shipFeeDiscount}, </if>
            <if test="tax != null"> `tax` = #{tax}, </if>
			<if test="actualPayFee != null"> `actual_pay_fee` = #{actualPayFee}, </if>
			<if test="gatewayCommission != null"> `gateway_commission` = #{gatewayCommission}, </if>
			<if test="platformCommission != null"> `platform_commission` = #{platformCommission}, </if>
			<if test="sellerReceivableFee != null"> `seller_receivable_fee` = #{sellerReceivableFee}, </if>
            <if test="tradeNo != null"> `trade_no` = #{tradeNo}, </if>
            <if test="gatewayTradeNo != null"> `gateway_trade_no` = #{gatewayTradeNo}, </if>
            <if test="channel != null"> `channel` = #{channel}, </if>
            <if test="channelAccount != null"> `channel_account` = #{channelAccount}, </if>
            <if test="orderCreatedAt != null"> `order_created_at` = #{orderCreatedAt}, </if>
            <if test="orderFinishedAt != null"> `order_finished_at` = #{orderFinishedAt}, </if>
            <if test="paidAt != null"> `paid_at` = #{paidAt}, </if>
            <if test="checkAt != null"> `check_at` = #{checkAt}, </if>
            <if test="sumAt != null">`sum_at` = #{sumAt},</if>
            <if test="extraJson != null">`extra_json` =  #{extraJson},</if>
            <if test="checkStatus != null"> `check_status` = #{checkStatus}, </if>
            <if test="checkStatus == 0">
                `check_at` = null,
                `sum_at` = null,
            </if>
            <if test="diffFee != null">`diff_fee` = #{diffFee},</if>
            <if test="commission1 != null">`commission1` = #{commission1},</if>
            <if test="commission1 != null">`commission2` = #{commission2},</if>
            <if test="commission1 != null">`commission3` = #{commission3},</if>
            <if test="commission1 != null">`commission4` = #{commission4},</if>
            <if test="commission1 != null">`commission5` = #{commission5},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="findById" parameterType="long" resultMap="SettleOrderDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="SettleOrderDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach collection="list" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="SettleOrderDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
        <if test="limit != null">
            LIMIT
            <if test="offset != null">#{offset},</if>
            #{limit}
        </if>
    </select>

    <select id="list" parameterType="map" resultMap="SettleOrderDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
    </select>

    <delete id="delete" parameterType="map">
        delete from <include refid="table_name"/>
        where id=#{id}
    </delete>


    <select id="sumSellerSettlement" parameterType="map" resultMap="SellerTradeDailySummary.SellerTradeDailySummaryMap">
        select  count(1) as order_count,
        0 as refund_order_count,
        seller_id as seller_id,
        max(seller_name) as seller_name,
        sum(origin_fee) as origin_fee,
        sum(seller_discount) as seller_discount,
        sum(platform_discount) as platform_discount,
        sum(ship_fee) as ship_fee,
        sum(ship_fee_discount) as ship_fee_discount,
        sum(tax) as tax,
        0 as refund_fee,
        sum(actual_pay_fee) as actual_pay_fee,
        sum(gateway_commission) as gateway_commission,
        sum(platform_commission) as platform_commission,
        sum(seller_receivable_fee) as seller_receivable_fee,
        1 as summary_type,
        sum(diff_fee) as diff_fee,
        sum(commission1) as commission1,
        sum(commission2) as commission2,
        sum(commission3) as commission3,
        sum(commission4) as commission4,
        sum(commission5) as commission5,
        now() as sum_at

        from
        <include refid="table_name"/>
        WHERE <![CDATA[ paid_at >= #{startAt} ]]> AND <![CDATA[ paid_at < #{endAt}]]>
        group by seller_id
    </select>


    <select id="sumPlatformSettlement" parameterType="map" resultMap="PlatformTradeDailySummary.PlatformTradeDailySummaryMap">
        select  count(1) as order_count,
        0 as refund_order_count,
        sum(origin_fee) as origin_fee,
        sum(seller_discount) as seller_discount,
        sum(platform_discount) as platform_discount,
        sum(ship_fee) as ship_fee,
        sum(ship_fee_discount) as ship_fee_discount,
        sum(actual_pay_fee) as actual_pay_fee,
        sum(tax) as tax,
        0 as refund_fee,
        sum(gateway_commission) as gateway_commission,
        sum(platform_commission) as platform_commission,
        sum(seller_receivable_fee) as seller_receivable_fee,
        1 as summary_type,
        sum(diff_fee) as diff_fee,
        sum(commission1) as commission1,
        sum(commission2) as commission2,
        sum(commission3) as commission3,
        sum(commission4) as commission4,
        sum(commission5) as commission5,
        now() as sum_at

        from
        <include refid="table_name"/>
        WHERE <![CDATA[ paid_at >= #{startAt} ]]> AND <![CDATA[ paid_at < #{endAt}]]>
    </select>

    <select id="findByShopOrderId" parameterType="map" resultMap="SettleOrderDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE order_id = #{shopOrderId} LIMIT 1
    </select>

</mapper>