<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2016 杭州端点网络科技有限公司, Code Generated by terminus code gen
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ShopOrder">

    <resultMap id="ShopOrderMap" type="ShopOrder">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="fee" property="fee"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="buyer_name" property="buyerName_"/>
        <result column="out_buyer_id" property="outBuyerId"/>
        <result column="shop_name" property="shopName"/>
        <result column="out_shop_id" property="outShopId"/>
        <result column="gather_order_id" property="gatherOrderId"/>
        <result column="company_id" property="companyId"/>
        <result column="referer_id" property="refererId"/>
        <result column="referer_name" property="refererName"/>
        <result column="origin_fee" property="originFee"/>
        <result column="discount" property="discount"/>
        <result column="ship_fee" property="shipFee"/>
        <result column="origin_ship_fee" property="originShipFee"/>
        <result column="shipment_promotion_id" property="shipmentPromotionId"/>
        <result column="integral" property="integral"/>
        <result column="balance" property="balance"/>
        <result column="promotion_id" property="promotionId"/>
        <result column="shipment_type" property="shipmentType"/>
        <result column="pay_type" property="payType"/>
        <result column="channel" property="channel"/>
        <result column="has_refund" property="hasRefund"/>
        <result column="commented" property="commented"/>
        <result column="buyer_note" property="buyerNote"/>
        <result column="seller_note" property="sellerNote"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="out_id" property="outId"/>
        <result column="depot_custom_name" property="depotCustomName"/>
        <result column="declared_id" property="declaredId"/>
        <result column="out_id" property="outId"/>
        <result column="out_id" property="outId"/>
        <result column="out_from" property="outFrom"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="distribution_rate" property="distributionRate"/>
        <result column="diff_fee" property="diffFee"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="first_shipment_at" property="firstShipmentAt"/>
        <result column="last_shipment_at" property="lastShipmentAt"/>
        <result column="profit_id" property="profitId"/>
        <result column="fee_id" property="feeId"/>
        <result column="extra_id" property="extraId"/>
        <result column="out_from_type" property="outFromType"/>
        <result column="first_confirm_at" property="firstConfirmAt"/>
        <result column="last_confirm_at" property="lastConfirmAt"/>
        <result column="exception_type" property="exceptionType"/>
        <result column="exception_reason" property="exceptionReason"/>
        <result column="flag" property="flag"/>
        <result column="categoryNameSnapshot" property="categoryNameSnapshot"/>
    </resultMap>

    <sql id="tb">
        parana_shop_orders
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shop_id`,`buyer_id`,`fee`,`status`,`type`,`buyer_name`,`out_buyer_id`,`shop_name`,`out_shop_id`,`company_id`,referer_id,referer_name,
        `origin_fee`,`discount`,`ship_fee`,`origin_ship_fee`,`shipment_promotion_id`,`integral`,`balance`,`promotion_id`,
        `shipment_type`,`pay_type`,`channel`,`has_refund`,`commented`,`buyer_note`,
        `seller_note`,`extra_json`,`tags_json`,`out_id`,
        `out_from`,
        `commission_rate`,`distribution_rate`,`diff_fee`,`created_at`,`updated_at`,`first_shipment_at`,`last_shipment_at`,`depot_custom_name`,`declared_id`,
        `fee_id`, `extra_id`, `profit_id`, `out_from_type`
        ,`first_confirm_at`
        ,`last_confirm_at`
        ,`exception_type`, `exception_reason`
        ,`flag`
        , `created_by`, `updated_by`
    </sql>

    <sql id="cols_all_with_table_alias">
        t.id, t.`shop_id`, t.`buyer_id`, t.`fee`, t.`status`, t.`type`, t.`buyer_name`, t.`out_buyer_id`, t.`shop_name`,
        t.`out_shop_id`, t.`company_id`, t.referer_id, t.referer_name,
        t.`origin_fee`, t.`discount`, t.`ship_fee`, t.`origin_ship_fee`, t.`shipment_promotion_id`, t.`integral`, t.`balance`, t.`promotion_id`,
        t.`shipment_type`, t.`pay_type`, t.`channel`, t.`has_refund`, t.`commented`, t.`buyer_note`,
        t.`seller_note`, t.`extra_json`, t.`tags_json`, t.`out_id`,
        t.`out_from`,
        t.`commission_rate`, t.`distribution_rate`, t.`diff_fee`, t.`created_at`, t.`updated_at`, t.`first_shipment_at`,
        t.`last_shipment_at`, t.`depot_custom_name`, t.`declared_id`,
        t.`fee_id`, t.`extra_id`, t.`profit_id`, t.`out_from_type`
        ,t.`first_confirm_at`
        ,t.`last_confirm_at`
        ,t.`exception_type`, t.`exception_reason`
        ,t.`flag`
    </sql>

    <sql id="vals">
        #{shopId},#{buyerId},#{fee},#{status},#{type},#{buyerName_},#{outBuyerId},#{shopName},#{outShopId},#{companyId},#{refererId},#{refererName},
        #{originFee},#{discount},#{shipFee},#{originShipFee},#{shipmentPromotionId},#{integral},#{balance},#{promotionId},
        #{shipmentType}, #{payType}, #{channel},#{hasRefund},#{commented},#{buyerNote},
        #{sellerNote},#{extraJson},#{tagsJson},#{outId},
        #{outFrom},#{commissionRate},#{distributionRate},#{diffFee},now(),now(),NULL,NULL,#{depotCustomName},#{declaredId},
        #{feeId}, #{extraId}, #{profitId}, #{outFromType}
        , #{firstConfirmAt}
        , #{lastConfirmAt}
        , #{exceptionType}, #{exceptionReason}
        , #{flag}
        , #{createdBy}, ${updatedBy}
    </sql>

    <insert id="create" parameterType="ShopOrder" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findFirstOrderByShopCategory" resultType="java.util.Map" parameterType="map">
     select min(A.order_id) as "order_id", A.buyer_id, A.shop_id, min(A.created_at) as "created_at", B.shop_category_id
      from parana_sku_orders A left join parana_shop_category_items B on A.item_id = B.item_id
      left join payer_info C on A.order_id = C.order_id
        where (A.status > 0 or (-2 > A.status and A.status != -6 and A.status != -8 and A.status != -4 and A.status > -13 )) and A.shop_id = #{shopId}
        and A.created_at > #{date} group by A.buyer_id, A.shop_id, B.shop_category_id, C.hash order by A.order_id asc
    </select>

    <select id="groupOrderCount" parameterType="map" resultType="map">
        SELECT shop_name "Name", count(id) "Count", sum(fee) "Fee", shop_id "ShopId"
        FROM
        <include refid="tb"/>
        WHERE
        `status` > 0
        AND created_at > #{startFrom}
        GROUP BY shop_id
    </select>

    <select id="findDeclaredIdInfo" parameterType="map" resultMap="ShopOrderMap">
        select shop_id, declared_id
        FROM <include refid="tb"/>
        WHERE id = #{id}
    </select>
    <select id="findOrderIdThatNotMatch" parameterType="map" resultMap="ShopOrderMap">
        select o.id from parana_shop_orders o where o.shop_id = #{shopId} and (o.status >0 or (-1 > o.status and o.status >= -6 )) and not exists ( select 1 from payer_info i where o.id = i.order_id limit 1);
    </select>
    <select id="findById" parameterType="long" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="listIdsBy" parameterType="map" resultType="long">
        SELECT id
        FROM
        <include refid="tb"/> t
        <where>
            <include refid="criteria"/>
        </where>
        <if test="limit != null and offset !=null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="listShopOrdersBy" parameterType="map" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        order by id asc
        <if test="limit != null and offset !=null">
            limit #{offset}, #{limit}
        </if>
    </select>



    <select id="listDeliveredShopOrdersWithoutShipmentTime" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            status IN (2,3,4) AND ( first_shipment_at IS NULL OR last_shipment_at IS NULL)
        </where>
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <sql id="criteria">
        <if test="status != null">AND status IN ( -9999
            <foreach collection="status" open="," separator="," item="st">
                #{st}
            </foreach>
            )
        </if>
        <if test="status == null">AND status != -14</if>
        <if test="id!=null">AND `id` = #{id}</if>
        <if test="updatedAt != null">AND `updated_at` > #{updatedAt}</if>
        <if test="buyerId != null">AND `buyer_id` = #{buyerId}</if>
        <if test="shopId != null">AND `shop_id` = #{shopId}</if>
        <if test="shopName != null">AND `shop_name` like concat(#{shopName},'%')</if>
        <if test="type != null">AND `type` = #{type}</if>
        <if test="companyId != null">AND `company_id` = #{companyId}</if>
        <if test="refererId != null">AND (
            `referer_id` = #{refererId}
            <if test="orBuyerId !=null">or `buyer_id` = #{orBuyerId}</if>
            )
        </if>
        <if test="orderIds != null">AND `id` in (-1
            <foreach collection="orderIds" item="oId" open="," separator=",">#{oId}</foreach>)
        </if>
        <if test="orderId != null">AND `id` = #{orderId}</if>
        <if test="refererIds!= null  and refererIds.size() > 0">
            and (
            `referer_id` in
            <foreach collection="refererIds" item="ri" open="(" close=")" separator=",">
                #{ri}
            </foreach>
            <if test="orBuyerId != null">or `buyer_id` = #{orBuyerId}</if>
            )
        </if>

        <if test="outId != null">AND `out_id` = #{outId}</if>
        <if test="outFrom != null">AND `out_from` = #{outFrom}</if>
        <if test="shopName != null">AND `shop_name` = #{shopName}</if>
        <if test="outShopId != null">
            <choose>
                <when test="orCreatedBy != null and orCreatedBy">
                    AND (`out_shop_id` = #{outShopId} OR created_by = #{createdBy})
                </when>
                <otherwise>
                    AND `out_shop_id` = #{outShopId}
                </otherwise>
            </choose>
        </if>

        <if test="outShopIds != null or snapshotSubStoreUserIds != null or snapshotServiceProviderUserIds != null">
            and (
            <if test="outShopIds != null">
            `out_shop_id` in (-1
            <foreach collection="outShopIds" item="ri" open="," separator=",">
                #{ri}
            </foreach>)
            </if>

            <if test="snapshotSubStoreUserIds != null">
                <if test="outShopIds != null">
                    or
                </if>
                id IN (
                SELECT shop_order_id
                FROM parana_order_role_snapshot
                WHERE shop_id = t.`shop_id`
                AND order_type = 1
                AND user_role = 2
                AND user_id IN
                <foreach collection="snapshotSubStoreUserIds" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="snapshotServiceProviderUserIds != null">
                <if test="outShopIds != null or snapshotSubStoreUserIds != null">
                    or
                </if>
                id IN (
                    SELECT shop_order_id
                    FROM parana_order_role_snapshot
                    WHERE shop_id = t.`shop_id`
                    AND order_type = 1
                    AND user_role = 3
                    AND user_id IN
                    <foreach collection="snapshotServiceProviderUserIds" open="(" item="item" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
            )
        </if>

<!--        <if test="orderId != null or pushStatus != null">-->
        <if test="pushStatus != null and pushStatus !=0">
            and
                id IN (
                SELECT order_id
                FROM parana_sku_orders
                WHERE 1=1
<!--                <if test="orderId != null">AND `id` = #{orderId}</if>-->
                <if test="pushStatus != null">AND `push_status` = #{pushStatus}</if>
                )
        </if>

        <if test="weShopId != null and weShopUserId != null">AND (`out_shop_id` = #{weShopId} OR `buyer_id` =
            #{weShopUserId})
        </if>
        <if test="hasRefund != null">AND `has_refund` = #{hasRefund}</if>
        <if test="startAt != null">AND <![CDATA[created_at >= #{startAt}]]> </if>
        <if test="endAt != null">AND <![CDATA[created_at <= #{endAt}]]> </if>
        <if test="orderTimeStart != null">AND <![CDATA[created_at >= #{orderTimeStart}]]> </if>
        <if test="orderTimeEnd != null">AND <![CDATA[created_at <= #{orderTimeEnd}]]> </if>
        <if test="shipmentStartAt != null">AND (<![CDATA[first_shipment_at >= #{shipmentStartAt}]]> or
            <![CDATA[last_shipment_at >= #{shipmentStartAt}]]> )
        </if>
        <if test="shipmentEndAt != null">AND ( <![CDATA[first_shipment_at <= #{shipmentEndAt}]]> or
            <![CDATA[last_shipment_at <= #{shipmentEndAt}]]> )
        </if>
        <if test="shipTimeStart != null">AND (<![CDATA[first_shipment_at >= #{shipTimeStart}]]> or
            <![CDATA[last_shipment_at >= #{shipTimeStart}]]> )
        </if>
        <if test="shipTimeEnd != null">AND ( <![CDATA[first_shipment_at <= #{shipTimeEnd}]]> or
            <![CDATA[last_shipment_at <= #{shipTimeEnd}]]> )
        </if>
        <if test="depotCustomName!= null">AND `depot_custom_name` = #{depotCustomName}</if>
        <if test="declaredId!= null">AND `declared_id` = #{declaredId}</if>
        <if test="declaredIds != null">AND `declared_id` IN
            <foreach collection="declaredIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="confirmAtStart != null">
            AND ( first_confirm_at >= #{confirmAtStart}  or last_confirm_at >= #{confirmAtStart}  )
        </if>
        <if test="confirmAtEnd != null">
            AND ( first_confirm_at <![CDATA[ <= ]]> #{confirmAtEnd} or last_confirm_at <![CDATA[ <= ]]> #{confirmAtEnd} )
        </if>
        <if test="orderFlags != null">
            <foreach collection="orderFlags" item="i">
                and ((`flag` <![CDATA[&]]> #{i}) = #{i})
            </foreach>
        </if>
        <if test="noRefererId != null">
            and referer_id is null
        </if>
        <if test="exceptionType != null">
            and exception_type = #{exceptionType}
        </if>
    </sql>

    <select id="pagingId" parameterType="map" resultType="long">
        select
        id
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <if test="orderBy == null">
            order by `id` desc
        </if>
        <if test="orderBy != null">
            <foreach collection="orderBy" item="seq" open="order by" separator=",">
                #{seq}
            </foreach>
        </if>
        <if test="orderId != null">
            limit 1
        </if>
        <if test="orderId == null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="findShopOrderBy" parameterType="map" resultMap="ShopOrderMap">
        SELECT DISTINCT
        id, shop_id, buyer_id, promotion_id, profit_id, fee_id, extra_id, type, out_from_type, declared_id,
        out_shop_id, promotion_id, referer_id, has_refund, first_shipment_at, last_shipment_at, `status`,
        created_at, updated_at
        , extra_json
        FROM
        <include refid="tb"/> t
        <where>
            <include refid="criteria"/>
            <if test="orderBy == null">
                order by `id` desc
            </if>
            <if test="orderBy != null">
                <foreach collection="orderBy" item="seq" open="order by" separator=",">
                    #{seq}
                </foreach>
            </if>
            limit #{offset}, #{limit}
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ShopOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <if test="orderBy == null">
            order by `id` desc
        </if>
        <if test="orderBy != null">
            <foreach collection="orderBy" item="seq" open="order by" separator=",">
                #{seq}
            </foreach>
        </if>
        limit #{offset}, #{limit}
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(id) from
        <include refid="tb"/> t
        <where>
            <include refid="criteria"/>
        </where>
    </select>


    <update id="update" parameterType="ShopOrder">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="feeId != null">, `fee_id` = #{feeId}</if>
            <if test="extraId != null">, `extra_id` = #{extraId}</if>
            <if test="profitId != null">, `profit_id` = #{profitId}</if>
            <if test="outFromType != null">, `out_from_type` = #{outFromType}</if>
            <if test="refererId != null">,`referer_id` = #{refererId}</if>
            <if test="fee != null">,`fee` = #{fee}</if>
            <if test="status!=null">, `status`=#{status}</if>
            <if test="type!=null">, `type`=#{type}</if>
            <if test="outBuyerId != null">,`out_buyer_id` = #{outBuyerId}</if>
            <if test="outShopId != null">,`out_shop_id` = #{outShopId}</if>
            <if test="companyId != null">,`company_id` = #{companyId}</if>
            <if test="originFee != null">,`origin_fee` = #{originFee}</if>
            <if test="gatherOrderId != null">gather_order_id = #{gatherOrderId},</if>
            <if test="discount != null">,`discount` = #{discount}</if>
            <if test="shipFee != null">,`ship_fee` = #{shipFee}</if>
            <if test="originShipFee != null">,`origin_ship_fee` = #{originShipFee}</if>
            <if test="integral != null">,`integral` = #{integral}</if>
            <if test="balance != null">,`balance` = #{balance}</if>
            <if test="shipmentType != null">,`shipment_type` = #{shipmentType}</if>
            <if test="payType != null">,`pay_type` = #{payType}</if>
            <if test="channel != null">,`channel` = #{channel}</if>
            <if test="hasRefund != null">,`has_refund` = #{hasRefund}</if>
            <if test="commented != null">,`commented` = #{commented}</if>
            <if test="sellerNote != null">,`seller_note` = #{sellerNote}</if>
            <if test="extraJson != null">,`extra_json` = #{extraJson}</if>
            <if test="tagsJson != null">,`tags_json` = #{tagsJson}</if>
            <if test="outId != null">,`out_id` = #{outId}</if>
            <if test="outFrom!=null">,`out_from`=#{outFrom}</if>
            <if test="commissionRate != null">, `commission_rate` = #{commissionRate}</if>
            <if test="distributionRate != null">, `distribution_rate` = #{distributionRate}</if>
            <if test="diffFee != null">, `diff_fee` = #{diffFee}</if>
            <if test="firstShipmentAt != null">, `first_shipment_at` = #{firstShipmentAt}</if>
            <if test="lastShipmentAt != null">, `last_shipment_at` = #{lastShipmentAt}</if>
            <if test="depotCustomName!= null">, `depot_custom_name` = #{depotCustomName}</if>
            <if test="declaredId!= null">, `declared_id` = #{declaredId}</if>
            <if test="firstConfirmAt != null">, `first_confirm_at` = #{firstConfirmAt}</if>
            <if test="lastConfirmAt != null">, `last_confirm_at` = #{lastConfirmAt}</if>
            <if test="exceptionType != null">, `exception_type` = #{exceptionType}</if>
            <if test="exceptionReason != null">, `exception_reason` = #{exceptionReason}</if>
            <if test="flag != null">, `flag` = #{flag}</if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `status`=#{newStatus}, `updated_at`=now()
        WHERE `id`=#{id}
        <if test="currentStatus!=null">
            and `status`=#{currentStatus}
        </if>
    </update>

    <update id="updateShopInfoById" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET shop_id =#{shopId},shop_name =#{shopName}, `updated_at`=now()
        WHERE id = #{id}
    </update>

    <update id="updateExtraJson" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET extra_json =#{extraJson}, `updated_at`=now()
        WHERE id = #{id}
    </update>

    <update id="updateTagsJson" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET tags_json =#{tagsJson}, `updated_at`=now()
        WHERE id = #{id}
    </update>


    <update id="batchMarkHasRefundByIds" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET `has_refund`=#{hasRefund} , `updated_at`=now()
        WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="sumPriceByCriteria" parameterType="map" resultType="long">
        select sum(fee)
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="findFirstOrderFromCriteria" parameterType="map" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY created_at ASC LIMIT 1
    </select>

    <select id="findShopOrderByRefererId" parameterType="map" resultType="long">
        select count(id)
        from
        <include refid="tb"/>
        WHERE referer_id = #{refererId}
        and shop_id=#{shopId}
        and status>0
    </select>

    <select id="sumFeeBy" parameterType="map" resultType="long">
        select sum(fee)
        from
        <include refid="tb"/>
        WHERE
        shop_id = #{shopId}
        and referer_id = #{referenceId}
        and `status` = #{status}
        and out_from = #{orderOutFrom}
    </select>

    <select id="queryOrderStatus" parameterType="map" resultType="map">
        SELECT `status`
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
    </select>


    <update id="removeGatherId" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            gather_order_id = null
        </set>
        <where>
            gather_order_id = #{gatherOrderId}
        </where>
    </update>

    <select id="findStrangeOrderBefore" parameterType="map" resultType="long">
        SELECT id
        FROM
        <include refid="tb"/>
        <where>
            #{date} > `created_at`
            AND `status` != 3
            AND `status` != 4
            AND `status` != -6
            AND `status` != -1
            AND `status` != -2
            AND `status` != -14
            AND `status` != -17
            AND 400 > `status`
        </where>
    </select>
    <select id="findCreatedById" parameterType="map" resultMap="ShopOrderMap">
        select fee, created_at from <include refid="tb"/>
        <where>id = #{id}</where>
    </select>
    <select id="findFeeByReferenceId" parameterType="map" resultType="long">
        SELECT fee
        FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId}
            AND referer_id = #{referenceId}
            AND `status` in
            <foreach collection="status" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </where>
    </select>

    <select id="findStatisticsByReferenceId" parameterType="map" resultType="moonstone.order.model.result.ShopOrderGuiderSummaryDO">
        SELECT referer_id as guiderUserId,
               SUM(fee) as totalOrderFee,
               COUNT(1) as totalOrderCount
        FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId}
            AND referer_id in
            <foreach collection="guiderUserIds" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
            AND `status` in
            <foreach collection="statusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </where>
        GROUP BY referer_id
    </select>

    <select id="listOrderIdAndCreatedAtByShopId" parameterType="map" resultType="long">
        SELECT id
        FROM
        <include refid="tb"/>
        <where>
            `id` > #{id}
            AND shop_id = #{shopId}
            AND `status` > 0
        </where>
        order by id asc
        limit #{limit}
    </select>

    <select id="findOrderSummary" parameterType="map" resultType="moonstone.order.model.result.ShopOrderSummaryDO">
        SELECT COUNT(id) totalCount,
               SUM(fee)  totalFee

        FROM parana_shop_orders

        WHERE shop_id = #{shopId}
        AND <![CDATA[ created_at >= #{startAt} ]]>
        AND <![CDATA[ created_at <= #{endAt} ]]>
        AND status IN
        <foreach collection="statusList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
        <if test="guiderUserId != null">
            AND ( referer_id = #{guiderUserId} OR buyer_id = #{guiderUserId} )
        </if>
        <if test="subStoreId != null">
            AND out_shop_id = #{subStoreId}
        </if>
        <if test="subStoreIdList != null">
            AND out_shop_id in
            <foreach collection="subStoreIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findOrderProfitSummary" parameterType="map" resultType="moonstone.order.model.result.ShopOrderProfitSummaryDO">
        SELECT SUM(t_bd_1.change_fee) foreseeProfit,
               SUM(t_bd_2.change_fee) presentProfit

          FROM parana_shop_orders t_so

     LEFT JOIN parana_balance_detail t_bd_1 ON t_bd_1.`related_id` = t_so.`id`
                                           AND <![CDATA[ t_bd_1.`status` & (2 << 2) = (2 << 2) ]]>
                                           AND t_bd_1.`type` = 1
                                           AND t_so.`status` IN (1, 2)
                                           AND <![CDATA[ t_bd_1.`status` & (2 << 8) != (2 << 8) ]]>
                                           AND t_bd_1.user_id = #{profitBelongUserId}
                                    <if test="queryGuiderProfit != null">
                                           AND <![CDATA[ t_bd_1.`status` & (2 << 11) = (2 << 11) ]]>
                                    </if>
                                    <if test="queryServiceProviderProfit != null">
                                        AND (( t_so.referer_id is not null and <![CDATA[ t_bd_1.`status` & (2 << 11) != (2 << 11) ]]> )
                                             or
                                             ( t_so.referer_id is null ))
                                    </if>

     LEFT JOIN parana_balance_detail t_bd_2 ON t_bd_2.`related_id` = t_so.`id`
                                           AND <![CDATA[ t_bd_2.`status` & (2 << 2) = (2 << 2) ]]>
                                           AND t_bd_2.`type` = 1
                                           AND t_so.`status` IN ( 3 )
                                           AND <![CDATA[ t_bd_2.`status` & (2 << 8) = (2 << 8) ]]>
                                           AND t_bd_2.user_id = #{profitBelongUserId}
                                     <if test="queryGuiderProfit != null">
                                           AND <![CDATA[ t_bd_2.`status` & (2 << 11) = (2 << 11) ]]>
                                     </if>
                                     <if test="queryServiceProviderProfit != null">
                                           AND (( t_so.referer_id is not null and <![CDATA[ t_bd_2.`status` & (2 << 11) != (2 << 11) ]]> )
                                                or
                                                ( t_so.referer_id is null ))
                                     </if>

         WHERE t_so.shop_id = #{shopId}
        <if test="guiderUserId != null">
           AND t_so.`referer_id` = #{guiderUserId}
        </if>

        <if test="subStoreId != null and guiderUserIdList != null">
            AND t_so.out_shop_id = #{subStoreId}
            AND t_so.`referer_id` in
                    <foreach collection="guiderUserIdList" open="(" item="item" separator="," close=")">
                        #{item}
                    </foreach>
        </if>

        <if test="subStoreId != null and guiderUserIdExcludeList != null">
           and t_so.out_shop_id = #{subStoreId}
           and t_so.referer_id is null
        </if>

        <if test="subStoreIdList != null">
           and t_so.out_shop_id in
            <foreach collection="subStoreIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>

           AND <![CDATA[ t_so.`created_at` >= #{startAt} ]]>
           AND <![CDATA[ t_so.`created_at` <= #{endAt} ]]>
    </select>

    <select id="findSimpleOrderView" resultType="moonstone.order.model.result.SimpleShopOrderView" parameterType="map">
        SELECT t.`id` id,
               t.`declared_id` declaredId,
               t.`created_at` createdAt,
               t.`fee` fee,

               t_so.`item_name` itemName,
               t_so.`sku_image` skuImage,
               SUM(t_so.`quantity`) quantity

        FROM parana_shop_orders t

        JOIN parana_sku_orders t_so ON t.`id` = t_so.`order_id`

       WHERE t.id IN
        <foreach collection="shopOrderIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>

    GROUP BY t.`id`
    </select>

    <select id="findServiceProviderGmvTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select snapshot_table.user_id as resId ,
               snapshot_table.name as resName,
               snapshot_table.province as province,
               sum(order_table.shop_order_fee) as sumCol,
               count(order_table.shop_order_fee) as countCol
        from(
            select A.id as shop_order_id, A.fee as shop_order_fee
            from parana_shop_orders as A
            where A.status in (1,2,3)
            AND <![CDATA[ A.created_at >= #{startTime} ]]>
            AND <![CDATA[ A.created_at <= #{endTime} ]]>
            and A.shop_id = #{shopId}) as order_table
        inner join (
            select B.shop_order_id as shop_order_id,
                   B.user_id as user_id,
                   B.name as `name`,
                   B.province as province
            from parana_order_role_snapshot as B
            where B.order_type = 1
            and B.user_role = 3) as snapshot_table
        on order_table.shop_order_id = snapshot_table.shop_order_id
        group by snapshot_table.user_id
        order by sum(order_table.shop_order_fee) desc
        limit 10
    </select>

    <select id="findServiceProviderOrderTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select snapshot_table.user_id as resId ,
               snapshot_table.name as resName,
               snapshot_table.province as province,
               sum(order_table.shop_order_fee) as sumCol,
               count(order_table.shop_order_fee) as countCol
        from(
            select A.id as shop_order_id, A.fee as shop_order_fee
            from parana_shop_orders as A
            where A.status in (1,2,3)
            AND <![CDATA[ A.created_at >= #{startTime} ]]>
            AND <![CDATA[ A.created_at <= #{endTime} ]]>
            and A.shop_id = #{shopId}) as order_table
        inner join (
            select B.shop_order_id as shop_order_id,
                   B.user_id as user_id,
                   B.name as `name`,
                   B.province as province
            from parana_order_role_snapshot as B
            where B.order_type = 1
            and B.user_role = 3) as snapshot_table
        on order_table.shop_order_id = snapshot_table.shop_order_id
        group by snapshot_table.user_id
        order by count(order_table.shop_order_fee) desc
        limit 10
    </select>

    <select id="findSubStoreGmvTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select snapshot_table.user_id as resId,
               snapshot_table.name as resName ,
               sum(order_table.shop_order_fee) as sumCol,
               count(order_table.shop_order_fee) as countCol
        from(
            select A.id as shop_order_id, A.fee as shop_order_fee
            from parana_shop_orders as A
            where A.status in (1,2,3)
            AND <![CDATA[ A.created_at >= #{startTime} ]]>
            AND <![CDATA[ A.created_at <= #{endTime} ]]>
            and A.shop_id = #{shopId}) as order_table
        inner join (
            select B.shop_order_id as shop_order_id,
                   B.user_id as user_id,
                   B.name as name
            from parana_order_role_snapshot as B
            where B.order_type = 1
            and B.user_role = 2) as snapshot_table
        on order_table.shop_order_id = snapshot_table.shop_order_id
        group by snapshot_table.user_id
        order by sum(order_table.shop_order_fee) desc
        limit 10
    </select>


    <select id="findSubStoreOrderTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select snapshot_table.user_id as resId,
               snapshot_table.name as resName,
               sum(order_table.shop_order_fee) as sumCol,
               count(order_table.shop_order_fee) as countCol
        from(
            select A.id as shop_order_id, A.fee as shop_order_fee
            from parana_shop_orders as A
            where A.status in (1,2,3)
            AND <![CDATA[ A.created_at >= #{startTime} ]]>
            AND <![CDATA[ A.created_at <= #{endTime} ]]>
            and A.shop_id = #{shopId}) as order_table
        inner join (
            select B.shop_order_id as shop_order_id,
                   B.user_id as user_id,
                   B.name as name
            from parana_order_role_snapshot as B
            where B.order_type = 1
            and B.user_role = 2) as snapshot_table
        on order_table.shop_order_id = snapshot_table.shop_order_id
        group by snapshot_table.user_id
        order by count(order_table.shop_order_fee) desc
        limit 10
    </select>

    <select id="findSkuGmvTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select tableA.sku_id as resId, sum(tableA.fee) as sumCol, count(tableA.fee) as countCol
        from(
            select sku_id , fee from parana_sku_orders
            where status in (1,2,3)
            and <![CDATA[ created_at >= #{startTime} ]]>
            and <![CDATA[ created_at <= #{endTime} ]]>
            and shop_id = #{shopId}) as tableA
        group by tableA.sku_id
        order by sum(tableA.fee) desc
        limit 10
    </select>

    <select id="findSkuOrderTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select tableA.sku_id as resId, sum(tableA.fee) as sumCol, count(tableA.fee) as countCol
        from(
            select sku_id , fee from parana_sku_orders
            where status in (1,2,3)
            and <![CDATA[ created_at >= #{startTime} ]]>
            and <![CDATA[ created_at <= #{endTime} ]]>
            and shop_id = #{shopId}) as tableA
        group by tableA.sku_id
        order by count(tableA.fee) desc
        limit 10
    </select>

    <select id="findProvinceGmvTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select snapshot_table.province as province, sum(order_table.shop_order_fee) as sumCol, count(order_table.shop_order_fee) as countCol
        from(
            select A.id as shop_order_id, A.fee as shop_order_fee
            from parana_shop_orders as A
            where A.status in (1,2,3)
            and <![CDATA[ A.created_at >= #{startTime} ]]>
            AND <![CDATA[ A.created_at <= #{endTime} ]]>
            and A.shop_id = #{shopId}) as order_table
        inner join (
            select B.shop_order_id as shop_order_id, B.province as province
            from parana_order_role_snapshot as B
            where B.order_type = 1
            and B.user_role = 3
            and B.province is not null) as snapshot_table
        on order_table.shop_order_id = snapshot_table.shop_order_id
        group by snapshot_table.province
        order by sum(order_table.shop_order_fee) desc
        limit 10;
    </select>

    <select id="findProvinceOrderTop" resultType="moonstone.order.dto.DataBoardDto" parameterType="map">
        select snapshot_table.province as province, sum(order_table.shop_order_fee) as sumCol, count(order_table.shop_order_fee) as countCol
        from(
            select A.id as shop_order_id, A.fee as shop_order_fee
            from parana_shop_orders as A
            where A.status in (1,2,3)
            and <![CDATA[ A.created_at >= #{startTime} ]]>
            AND <![CDATA[ A.created_at <= #{endTime} ]]>
            and A.shop_id = #{shopId}) as order_table
        inner join (
            select B.shop_order_id as shop_order_id, B.province as province
            from parana_order_role_snapshot as B
            where B.order_type = 1
            and B.user_role = 3
            and B.province is not null) as snapshot_table
        on order_table.shop_order_id = snapshot_table.shop_order_id
        group by snapshot_table.province
        order by count(order_table.shop_order_fee) desc
        limit 10;
    </select>

    <select id="findConfirmedOrderWithNoConfirmAt" resultMap="ShopOrderMap" parameterType="map">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where id > #{minId}
        and status = 3
        and first_confirm_at is null
        and last_confirm_at is null

        order by id asc
        limit #{limit}
    </select>

    <update id="batchUpdateConfirmAtInfo">
        update <include refid="tb"/>
        set  first_confirm_at =
        <foreach collection="updateList" item="item" separator=" " open="case id" close="end">
            when #{item.id} then #{item.firstConfirmAt}
        </foreach>
             ,last_confirm_at =
        <foreach collection="updateList" item="item" separator=" " open="case id" close="end">
            when #{item.id} then #{item.lastConfirmAt}
        </foreach>
        where id in
        <foreach collection="updateList" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="findByPaymentIds" resultType="moonstone.order.model.result.ShopOrderWithPaymentDO" parameterType="map">
        SELECT t.id as id,
               t.declared_id as declaredId,
               t.fee as fee,
               t.created_at as createdAt,
               t.first_shipment_at as firstShipmentAt,
               t.last_shipment_at as lastShipmentAt,
               t.first_confirm_at as firstConfirmAt,
               t.last_confirm_at as lastConfirmAt,
               t_op.payment_id as paymentId,
               t.status as status

        FROM parana_shop_orders t

        JOIN parana_order_payments t_op ON t.`id` = t_op.`order_id`
                                       AND t_op.`order_type` = 1
                                       AND t_op.`payment_id` IN
                                    <foreach collection="paymentIdList" open="(" item="item" separator="," close=")">
                                        #{item}
                                    </foreach>
    </select>

    <!--    subUserId是门店表的id 这里只查询未付款的 -->
    <select id="getPendingPaymentOrderCountLastHour" resultType="java.lang.Long">
        select count(id) from parana_shop_orders where shop_id = #{shopId} and out_shop_id = #{subStoreId} and status = 0 and created_at >= NOW() - INTERVAL 1 HOUR;
    </select>
    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">
                ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'created_at'">
                ORDER BY created_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType == null">
            ASC
        </if>
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <select id="selectList" parameterType="map" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
    </select>


    <select id="countWithSnapshot" parameterType="map" resultType="java.lang.Long">
        select count(distinct a.id)
        from parana_shop_orders a
        <if test="serviceProviderName != null and serviceProviderName != ''">
            left join parana_order_role_snapshot b1 on a.id = b1.shop_order_id
        </if>
        <if test="subStoreName != null and subStoreName != ''">
            left join parana_order_role_snapshot b2 on a.id = b2.shop_order_id
        </if>

        <if test="guiderName != null and guiderName != ''">
            left join parana_order_role_snapshot b3 on a.id = b3.shop_order_id
        </if>
        <if test="(paymentOutId != null and paymentOutId != '') or thirdPartyTransactionNoList != null or (paySerialNo != null and paySerialNo != '') ">
        left join parana_order_payments c on a.id = c.order_id
            left join parana_payments d on c.payment_id = d.id
        </if>
        <if test="skuOrderShippingWarehouseType != null">
            left join parana_sku_orders e on a.id = e.order_id
        </if>
        <where>
            a.shop_id = #{shopId}
            <if test="id != null ">
               and a.id = #{id}
            </if>
            <if test="status != null ">
                and a.status = #{status}
            </if>
            <if test="statusList != null">
                AND a.status IN
                <foreach collection="statusList" open="(" separator="," close=")" item="status">#{status}</foreach>
            </if>
            <if test="declareId != null ">
                and a.declared_id = #{declareId}
            </if>
            <if test="type != null ">
                and  a.type = #{type}
            </if>
            <if test="outFrom != null and outFrom != ''">
                and a.out_from = #{outFrom}
            </if>
            <if test="exceptionType != null ">
                and a.exception_type = #{exceptionType}
            </if>
            <if test="orderTimeStart != null and orderTimeStart != '' and orderTimeEnd != null and orderTimeEnd != ''">
                and (a.created_at between #{orderTimeStart} and #{orderTimeEnd})
            </if>
            <if test="shipTimeStart != null and shipTimeStart != '' and shipTimeEnd != null and shipTimeEnd != ''">
                and (a.last_shipment_at between #{shipTimeStart} and #{shipTimeEnd})
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != '' and confirmTimeEnd != null and confirmTimeEnd != ''">
                and (a.last_confirm_at between #{confirmTimeStart} and #{confirmTimeEnd})
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and b1.name LIKE concat('%',#{serviceProviderName},'%')
                and b1.user_role = 3
                and b1.order_type = 1
            </if>
            <if test="subStoreName != null and subStoreName != ''">
                and b2.name LIKE concat('%',#{subStoreName},'%')
                and b2.user_role = 2
                and b2.order_type = 1
            </if>
            <if test="guiderName != null and guiderName != ''">
                and b3.name LIKE concat('%',#{guiderName},'%')
                and b3.user_role = 1
                and b3.order_type = 1
            </if>
            <if test="paymentOutId != null and paymentOutId != ''">
                and d.out_id = #{paymentOutId}
            </if>
            <if test="orderFlags != null">
                <foreach collection="orderFlags" item="i">
                    and ((a.`flag` <![CDATA[&]]> #{i}) = #{i})
                </foreach>
            </if>
            <if test="thirdPartyTransactionNoList != null">
                AND d.`third_party_transaction_no` IN
                <foreach collection="thirdPartyTransactionNoList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </if>
            <if test="paySerialNo != null and paySerialNo != ''">
                AND d.`pay_serial_no` = #{paySerialNo}
            </if>
            <if test="skuOrderShippingWarehouseType != null">
                AND e.`shipping_warehouse_type` = #{skuOrderShippingWarehouseType}
            </if>
        </where>

    </select>

    <select id="pagesWithSnapshot" parameterType="map" resultMap="ShopOrderMap">
        select distinct a.*
        from parana_shop_orders a
        <if test="serviceProviderName != null and serviceProviderName != ''">
            left join parana_order_role_snapshot b1 on a.id = b1.shop_order_id
        </if>
        <if test="subStoreName != null and subStoreName != ''">
            left join parana_order_role_snapshot b2 on a.id = b2.shop_order_id
        </if>

        <if test="guiderName != null and guiderName != ''">
            left join parana_order_role_snapshot b3 on a.id = b3.shop_order_id
        </if>
        <if test="(paymentOutId != null and paymentOutId != '') or thirdPartyTransactionNoList != null or (paySerialNo != null and paySerialNo != '') ">
            left join parana_order_payments c on a.id = c.order_id
            left join parana_payments d on c.payment_id = d.id
        </if>
        <if test="skuOrderShippingWarehouseType != null">
            left join parana_sku_orders e on a.id = e.order_id
        </if>
        <where>
            a.shop_id = #{shopId}
            <if test="id != null ">
               and a.id = #{id}
            </if>
            <if test="status != null ">
                and a.status = #{status}
            </if>
            <if test="statusList != null">
                AND a.status IN
                <foreach collection="statusList" open="(" separator="," close=")" item="status">#{status}</foreach>
            </if>
            <if test="declareId != null ">
                and a.declared_id = #{declareId}
            </if>
            <if test="type != null ">
                and  a.type = #{type}
            </if>
            <if test="outFrom != null and outFrom != '' ">
                and a.out_from = #{outFrom}
            </if>
            <if test="exceptionType != null ">
                and a.exception_type = #{exceptionType}
            </if>
            <if test="orderTimeStart != null and orderTimeStart != '' and orderTimeEnd != null and orderTimeEnd != ''">
                and (a.created_at between #{orderTimeStart} and #{orderTimeEnd})
            </if>
            <if test="shipTimeStart != null and shipTimeStart != '' and shipTimeEnd != null and shipTimeEnd != ''">
                and (a.last_shipment_at between #{shipTimeStart} and #{shipTimeEnd})
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != '' and confirmTimeEnd != null and confirmTimeEnd != ''">
                and (a.last_confirm_at between #{confirmTimeStart} and #{confirmTimeEnd})
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and b1.name LIKE concat('%',#{serviceProviderName},'%')
                and b1.user_role = 3
                and b1.order_type = 1
            </if>
            <if test="subStoreName != null and subStoreName != ''">
                and b2.name LIKE concat('%',#{subStoreName},'%')
                and b2.user_role = 2
                and b2.order_type = 1
            </if>
            <if test="guiderName != null and guiderName != ''">
                and b3.name LIKE concat('%',#{guiderName},'%')
                and b3.user_role = 1
                and b3.order_type = 1
            </if>
            <if test="paymentOutId != null and paymentOutId != ''">
                and d.out_id = #{paymentOutId}
            </if>
            <if test="orderFlags != null">
                <foreach collection="orderFlags" item="i">
                    and ((a.`flag` <![CDATA[&]]> #{i}) = #{i})
                </foreach>
            </if>
            <if test="thirdPartyTransactionNoList != null">
                AND d.`third_party_transaction_no` IN
                <foreach collection="thirdPartyTransactionNoList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </if>
            <if test="paySerialNo != null and paySerialNo != ''">
                AND d.`pay_serial_no` = #{paySerialNo}
            </if>
            <if test="skuOrderShippingWarehouseType != null">
                AND e.`shipping_warehouse_type` = #{skuOrderShippingWarehouseType}
            </if>
        </where>
        <if test="sortBy != null">
            <if test="sortBy == 'id'">
                ORDER BY a.id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'created_at'">
                ORDER BY a.created_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
        limit #{offset}, #{limit}
    </select>

    <select id="listIdsWithSnapshot" parameterType="map" resultType="Long">
        select distinct a.id
        from parana_shop_orders a
        <if test="serviceProviderName != null and serviceProviderName != ''">
            left join parana_order_role_snapshot b1 on a.id = b1.shop_order_id
        </if>
        <if test="subStoreName != null and subStoreName != ''">
            left join parana_order_role_snapshot b2 on a.id = b2.shop_order_id
        </if>

        <if test="guiderName != null and guiderName != ''">
            left join parana_order_role_snapshot b3 on a.id = b3.shop_order_id
        </if>
        <if test="(paymentOutId != null and paymentOutId != '') or thirdPartyTransactionNoList != null or (paySerialNo != null and paySerialNo != '') ">
            left join parana_order_payments c on a.id = c.order_id
            left join parana_payments d on c.payment_id = d.id
        </if>
        <if test="skuOrderShippingWarehouseType != null">
            left join parana_sku_orders e on a.id = e.order_id
        </if>
        <where>
            <if test="shopId != null">
                and a.shop_id = #{shopId}
            </if>
            <if test="id != null ">
                and a.id = #{id}
            </if>
            <if test="status != null ">
                and a.status = #{status}
            </if>
            <if test="statusList != null">
                AND a.status IN
                <foreach collection="statusList" open="(" separator="," close=")" item="status">#{status}</foreach>
            </if>
            <if test="declareId != null ">
                and a.declared_id = #{declareId}
            </if>
            <if test="type != null ">
                and  a.type = #{type}
            </if>
            <if test="outFrom != null and outFrom != '' ">
                and a.out_from = #{outFrom}
            </if>
            <if test="exceptionType != null ">
                and a.exception_type = #{exceptionType}
            </if>
            <if test="orderTimeStart != null and orderTimeStart != '' and orderTimeEnd != null and orderTimeEnd != ''">
                and (a.created_at between #{orderTimeStart} and #{orderTimeEnd})
            </if>
            <if test="shipTimeStart != null and shipTimeStart != '' and shipTimeEnd != null and shipTimeEnd != ''">
                and (a.last_shipment_at between #{shipTimeStart} and #{shipTimeEnd})
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != '' and confirmTimeEnd != null and confirmTimeEnd != ''">
                and (a.last_confirm_at between #{confirmTimeStart} and #{confirmTimeEnd})
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and b1.name LIKE concat('%',#{serviceProviderName},'%')
                and b1.user_role = 3
                and b1.order_type = 1
            </if>
            <if test="subStoreName != null and subStoreName != ''">
                and b2.name LIKE concat('%',#{subStoreName},'%')
                and b2.user_role = 2
                and b2.order_type = 1
            </if>
            <if test="guiderName != null and guiderName != ''">
                and b3.name LIKE concat('%',#{guiderName},'%')
                and b3.user_role = 1
                and b3.order_type = 1
            </if>
            <if test="paymentOutId != null and paymentOutId != ''">
                and d.out_id = #{paymentOutId}
            </if>
            <if test="orderFlags != null">
                <foreach collection="orderFlags" item="i">
                    and ((a.`flag` <![CDATA[&]]> #{i}) = #{i})
                </foreach>
            </if>
            <if test="thirdPartyTransactionNoList != null">
                AND d.`third_party_transaction_no` IN
                <foreach collection="thirdPartyTransactionNoList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </if>
            <if test="paySerialNo != null and paySerialNo != ''">
                AND d.`pay_serial_no` = #{paySerialNo}
            </if>
            <if test="skuOrderShippingWarehouseType != null">
                AND e.`shipping_warehouse_type` = #{skuOrderShippingWarehouseType}
            </if>
        </where>
        <if test="sortBy != null">
            <if test="sortBy == 'id'">
                ORDER BY a.id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'created_at'">
                ORDER BY a.created_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </select>


    <update id="updateOrderPushException" parameterType="map" >
        update parana_shop_orders
        set exception_type = #{exceptionType},
            exception_reason =#{exceptionReason}
        where id = #{id};
    </update>

    <select id="selectOne" parameterType="map" resultMap="ShopOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="countAdminWithSnapshot" parameterType="map" resultType="java.lang.Long">
        select count(distinct a.id)
        from parana_shop_orders a
        <if test="serviceProviderName != null and serviceProviderName != ''">
            left join parana_order_role_snapshot b1 on a.id = b1.shop_order_id
        </if>
        <if test="subStoreName != null and subStoreName != ''">
            left join parana_order_role_snapshot b2 on a.id = b2.shop_order_id
        </if>

        <if test="guiderName != null and guiderName != ''">
            left join parana_order_role_snapshot b3 on a.id = b3.shop_order_id
        </if>
        <if test="thirdPartyTransactionNoList != null or (paySerialNo != null and paySerialNo != '') ">
        left join parana_order_payments c on a.id = c.order_id
            left join parana_payments d on c.payment_id = d.id
        </if>
        <if test="skuOrderShippingWarehouseType != null">
            left join parana_sku_orders e on a.id = e.order_id
        </if>
        <if test="paymentPushStatus != null">
            left join parana_order_payments f1 on a.id = f1.order_id
            left join parana_payments f2 on f1.payment_id = f2.id
        </if>
        <if test="skuOrderPushStatus != null">
            left join parana_sku_orders g on a.id = g.order_id
        </if>
        <where>
            <if test="ids != null">
                AND a.id IN
                <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
            </if>
            <if test="statusList != null">
                AND a.status IN
                <foreach collection="statusList" open="(" separator="," close=")" item="status">#{status}</foreach>
            </if>
            <if test="declareIds != null">
                AND a.declared_id IN
                <foreach collection="declareIds" open="(" separator="," close=")" item="declareId">#{declareId}</foreach>
            </if>
            <if test="type != null ">
                and  a.type = #{type}
            </if>
            <if test="outFrom != null and outFrom != ''">
                and a.out_from = #{outFrom}
            </if>
            <if test="exceptionTypes != null">
                AND a.exception_type IN
                <foreach collection="exceptionTypes" open="(" separator="," close=")" item="exceptionType">#{exceptionType}</foreach>
            </if>
            <if test="orderTimeStart != null and orderTimeStart != '' and orderTimeEnd != null and orderTimeEnd != ''">
                and (a.created_at between #{orderTimeStart} and #{orderTimeEnd})
            </if>
            <if test="shipTimeStart != null and shipTimeStart != '' and shipTimeEnd != null and shipTimeEnd != ''">
                and (a.last_shipment_at between #{shipTimeStart} and #{shipTimeEnd})
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != '' and confirmTimeEnd != null and confirmTimeEnd != ''">
                and (a.last_confirm_at between #{confirmTimeStart} and #{confirmTimeEnd})
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and b1.name LIKE concat('%',#{serviceProviderName},'%')
                and b1.user_role = 3
                and b1.order_type = 1
            </if>
            <if test="subStoreName != null and subStoreName != ''">
                and b2.name LIKE concat('%',#{subStoreName},'%')
                and b2.user_role = 2
                and b2.order_type = 1
            </if>
            <if test="guiderName != null and guiderName != ''">
                and b3.name LIKE concat('%',#{guiderName},'%')
                and b3.user_role = 1
                and b3.order_type = 1
            </if>
            <if test="paymentOutIds != null ">
                AND d1.out_id IN
                <foreach collection="paymentOutIds" open="(" separator="," close=")" item="paymentOutId">#{paymentOutId}</foreach>
            </if>
            <if test="orderFlags != null">
                <foreach collection="orderFlags" item="i">
                    and ((a.`flag` <![CDATA[&]]> #{i}) = #{i})
                </foreach>
            </if>
            <if test="thirdPartyTransactionNoList != null">
                AND d.`third_party_transaction_no` IN
                <foreach collection="thirdPartyTransactionNoList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </if>
            <if test="paySerialNo != null and paySerialNo != ''">
                AND d.`pay_serial_no` = #{paySerialNo}
            </if>
            <if test="skuOrderShippingWarehouseType != null">
                AND e.`shipping_warehouse_type` = #{skuOrderShippingWarehouseType}
            </if>
            <if test="paymentPushStatus != null">
                AND f2.`push_status` = #{paymentPushStatus}
            </if>
            <if test="skuOrderPushStatus != null">
                AND g.`push_status` = #{skuOrderPushStatus}
            </if>
            <if test="shopIds != null">
                AND a.shop_id IN
                <foreach collection="shopIds" open="(" separator="," close=")" item="shopId">
                    #{shopId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="adminListIdsWithSnapshot" parameterType="map" resultType="Long">
        select distinct a.id
        from parana_shop_orders a
        <if test="serviceProviderName != null and serviceProviderName != ''">
            left join parana_order_role_snapshot b1 on a.id = b1.shop_order_id
        </if>
        <if test="subStoreName != null and subStoreName != ''">
            left join parana_order_role_snapshot b2 on a.id = b2.shop_order_id
        </if>

        <if test="guiderName != null and guiderName != ''">
            left join parana_order_role_snapshot b3 on a.id = b3.shop_order_id
        </if>
        <if test="(paymentOutId != null and paymentOutId != '') or thirdPartyTransactionNoList != null or (paySerialNo != null and paySerialNo != '') ">
            left join parana_order_payments c on a.id = c.order_id
            left join parana_payments d on c.payment_id = d.id
        </if>
        <if test="skuOrderShippingWarehouseType != null">
            left join parana_sku_orders e on a.id = e.order_id
        </if>
        <if test="paymentPushStatus != null">
            left join parana_order_payments f1 on a.id = f1.order_id
            left join parana_payments f2 on f1.payment_id = f2.id
        </if>
        <if test="skuOrderPushStatus != null">
            left join parana_sku_orders g on a.id = g.order_id
        </if>
        <if test="paymentOutIds != null">
            left join parana_order_payments c1 on a.id = c1.order_id
            left join parana_payments d1 on c1.payment_id = d1.id
        </if>
        <where>
            <if test="ids != null">
                AND a.id IN
                <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
            </if>
            <if test="statusList != null">
                AND a.status IN
                <foreach collection="statusList" open="(" separator="," close=")" item="status">#{status}</foreach>
            </if>
            <if test="declareIds != null">
                AND a.declared_id IN
                <foreach collection="declareIds" open="(" separator="," close=")" item="declareId">#{declareId}</foreach>
            </if>
            <if test="type != null ">
                and  a.type = #{type}
            </if>
            <if test="outFrom != null and outFrom != ''">
                and a.out_from = #{outFrom}
            </if>
            <if test="exceptionTypes != null">
                AND a.exception_type IN
                <foreach collection="exceptionTypes" open="(" separator="," close=")" item="exceptionType">#{exceptionType}</foreach>
            </if>
            <if test="orderTimeStart != null and orderTimeStart != '' and orderTimeEnd != null and orderTimeEnd != ''">
                and (a.created_at between #{orderTimeStart} and #{orderTimeEnd})
            </if>
            <if test="shipTimeStart != null and shipTimeStart != '' and shipTimeEnd != null and shipTimeEnd != ''">
                and (a.last_shipment_at between #{shipTimeStart} and #{shipTimeEnd})
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != '' and confirmTimeEnd != null and confirmTimeEnd != ''">
                and (a.last_confirm_at between #{confirmTimeStart} and #{confirmTimeEnd})
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and b1.name LIKE concat('%',#{serviceProviderName},'%')
                and b1.user_role = 3
                and b1.order_type = 1
            </if>
            <if test="subStoreName != null and subStoreName != ''">
                and b2.name LIKE concat('%',#{subStoreName},'%')
                and b2.user_role = 2
                and b2.order_type = 1
            </if>
            <if test="guiderName != null and guiderName != ''">
                and b3.name LIKE concat('%',#{guiderName},'%')
                and b3.user_role = 1
                and b3.order_type = 1
            </if>
            <if test="paymentOutIds != null ">
                AND d1.out_id IN
                <foreach collection="paymentOutIds" open="(" separator="," close=")" item="paymentOutId">#{paymentOutId}</foreach>
            </if>
            <if test="orderFlags != null">
                <foreach collection="orderFlags" item="i">
                    and ((a.`flag` <![CDATA[&]]> #{i}) = #{i})
                </foreach>
            </if>
            <if test="thirdPartyTransactionNoList != null">
                AND d.`third_party_transaction_no` IN
                <foreach collection="thirdPartyTransactionNoList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </if>
            <if test="paySerialNo != null and paySerialNo != ''">
                AND d.`pay_serial_no` = #{paySerialNo}
            </if>
            <if test="skuOrderShippingWarehouseType != null">
                AND e.`shipping_warehouse_type` = #{skuOrderShippingWarehouseType}
            </if>
            <if test="paymentPushStatus != null">
                AND f2.`push_status` = #{paymentPushStatus}
            </if>
            <if test="skuOrderPushStatus != null">
                AND g.`push_status` = #{skuOrderPushStatus}
            </if>
            <if test="shopIds != null">
                AND a.shop_id IN
                <foreach collection="shopIds" open="(" separator="," close=")" item="shopId">
                    #{shopId}
                </foreach>
            </if>
        </where>
        <if test="sortBy != null">
            <if test="sortBy == 'id'">
                ORDER BY a.id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'created_at'">
                ORDER BY a.created_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </select>

    <select id="pagesAdminWithSnapshot" parameterType="map" resultMap="ShopOrderMap">
        select distinct a.*
        from parana_shop_orders a
        <if test="serviceProviderName != null and serviceProviderName != ''">
            left join parana_order_role_snapshot b1 on a.id = b1.shop_order_id
        </if>
        <if test="subStoreName != null and subStoreName != ''">
            left join parana_order_role_snapshot b2 on a.id = b2.shop_order_id
        </if>

        <if test="guiderName != null and guiderName != ''">
            left join parana_order_role_snapshot b3 on a.id = b3.shop_order_id
        </if>
        <if test="thirdPartyTransactionNoList != null or (paySerialNo != null and paySerialNo != '') ">
            left join parana_order_payments c on a.id = c.order_id
            left join parana_payments d on c.payment_id = d.id
        </if>
        <if test="skuOrderShippingWarehouseType != null">
            left join parana_sku_orders e on a.id = e.order_id
        </if>
        <if test="paymentPushStatus != null">
            left join parana_order_payments f1 on a.id = f1.order_id
            left join parana_payments f2 on f1.payment_id = f2.id
        </if>
        <if test="skuOrderPushStatus != null">
            left join parana_sku_orders g on a.id = g.order_id
        </if>
        <if test="paymentOutIds != null">
            left join parana_order_payments c1 on a.id = c1.order_id
            left join parana_payments d1 on c1.payment_id = d1.id
        </if>
        <where>
            <if test="ids != null">
                AND a.id IN
                <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
            </if>
            <if test="statusList != null">
                AND a.status IN
                <foreach collection="statusList" open="(" separator="," close=")" item="status">#{status}</foreach>
            </if>
            <if test="declareIds != null">
                AND a.declared_id IN
                <foreach collection="declareIds" open="(" separator="," close=")" item="declareId">#{declareId}</foreach>
            </if>
            <if test="type != null ">
                and  a.type = #{type}
            </if>
            <if test="outFrom != null and outFrom != ''">
                and a.out_from = #{outFrom}
            </if>
            <if test="exceptionTypes != null">
                AND a.exception_type IN
                <foreach collection="exceptionTypes" open="(" separator="," close=")" item="exceptionType">#{exceptionType}</foreach>
            </if>
            <if test="orderTimeStart != null and orderTimeStart != '' and orderTimeEnd != null and orderTimeEnd != ''">
                and (a.created_at between #{orderTimeStart} and #{orderTimeEnd})
            </if>
            <if test="shipTimeStart != null and shipTimeStart != '' and shipTimeEnd != null and shipTimeEnd != ''">
                and (a.last_shipment_at between #{shipTimeStart} and #{shipTimeEnd})
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != '' and confirmTimeEnd != null and confirmTimeEnd != ''">
                and (a.last_confirm_at between #{confirmTimeStart} and #{confirmTimeEnd})
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and b1.name LIKE concat('%',#{serviceProviderName},'%')
                and b1.user_role = 3
                and b1.order_type = 1
            </if>
            <if test="subStoreName != null and subStoreName != ''">
                and b2.name LIKE concat('%',#{subStoreName},'%')
                and b2.user_role = 2
                and b2.order_type = 1
            </if>
            <if test="guiderName != null and guiderName != ''">
                and b3.name LIKE concat('%',#{guiderName},'%')
                and b3.user_role = 1
                and b3.order_type = 1
            </if>
            <if test="paymentOutIds != null ">
                AND d1.out_id IN
                <foreach collection="paymentOutIds" open="(" separator="," close=")" item="paymentOutId">#{paymentOutId}</foreach>
            </if>
            <if test="orderFlags != null">
                <foreach collection="orderFlags" item="i">
                    and ((a.`flag` <![CDATA[&]]> #{i}) = #{i})
                </foreach>
            </if>
            <if test="thirdPartyTransactionNoList != null">
                AND d.`third_party_transaction_no` IN
                <foreach collection="thirdPartyTransactionNoList" open="(" separator="," close=")" item="item">#{item}</foreach>
            </if>
            <if test="paySerialNo != null and paySerialNo != ''">
                AND d.`pay_serial_no` = #{paySerialNo}
            </if>
            <if test="skuOrderShippingWarehouseType != null">
                AND e.`shipping_warehouse_type` = #{skuOrderShippingWarehouseType}
            </if>
            <if test="paymentPushStatus != null">
                AND f2.`push_status` = #{paymentPushStatus}
            </if>
            <if test="skuOrderPushStatus != null">
                AND g.`push_status` = #{skuOrderPushStatus}
            </if>
            <if test="shopIds != null">
                AND a.shop_id IN
                <foreach collection="shopIds" open="(" separator="," close=")" item="shopId">
                    #{shopId}
                </foreach>
            </if>
        </where>
        <if test="sortBy != null">
            <if test="sortBy == 'id'">
                ORDER BY a.id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'created_at'">
                ORDER BY a.created_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
        limit #{offset}, #{limit}
    </select>

</mapper>
