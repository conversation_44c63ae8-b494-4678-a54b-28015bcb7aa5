<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="OrderPayment">

    <resultMap id="OrderPaymentMap" type="OrderPayment">
        <id column="id" property="id"/>
        <result column="payment_id" property="paymentId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_type" property="orderType"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_order_payments
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `payment_id`,`order_id`,`order_type`,`status`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{paymentId},#{orderId},#{orderType},#{status},now(),now()
    </sql>

    <insert id="create" parameterType="OrderPayment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="OrderPayment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.paymentId}, #{i.orderId}, #{i.orderType}, #{i.status}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="OrderPaymentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByPaymentId" parameterType="long" resultMap="OrderPaymentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE payment_id = #{paymentId}
        ORDER BY id DESC
    </select>

    <select id="findByOrderIdAndOrderType" parameterType="map" resultMap="OrderPaymentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId} AND order_type=#{orderType}
        ORDER BY id DESC
    </select>


    <update id="updateStatusByPaymentId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        updated_at = now()
        ,`status` = #{status}
        WHERE payment_id=#{paymentId}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="findOrderPaymentByOrderIdAndOrderLevel" parameterType="map" resultMap="OrderPaymentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId} AND order_type=#{orderType} AND status=1
        limit 1
    </select>

    <update id="updateOrderStatusOnPaid" parameterType="map">
        UPDATE parana_shop_orders t

        JOIN parana_order_payments t_p ON t.id = t_p.`order_id`
                                      AND t_p.`order_type` = 1
                                      AND t_p.`payment_id` = #{paymentId}

        JOIN parana_sku_orders t_s ON t.id = t_s.order_id

        SET t.`status` = IF(t.`status` = #{beforeStatus}, #{afterStatus}, t.`status`),
            t_s.`status` = IF(t_s.`status` = #{beforeStatus}, #{afterStatus}, t_s.`status`),
            t.updated_at = IF(t.`status` = #{beforeStatus}, now(), t.updated_at),
            t_s.updated_at = IF(t_s.`status` = #{beforeStatus}, now(), t_s.updated_at),
            t_p.`status` = #{afterStatus},
            t_p.updated_at = now()
    </update>
</mapper>