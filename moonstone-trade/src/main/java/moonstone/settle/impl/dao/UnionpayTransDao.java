/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.settle.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.UnionpayTrans;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <pre>
 *   功能描述:UnionPay账务明细 dao
 * </pre>
 *
 * <AUTHOR> on 2014-12-24.
 */
@Repository
public class UnionpayTransDao extends MyBatisDao<UnionpayTrans> {


    /**
     * 根据交易流水查询支付交易明细(第三方流水号唯一，退款和支付不同)
     *
     * @param queryId 交易流水
     * @return 交易明细
     */
    public UnionpayTrans findByQueryId(String queryId) {
        return getSqlSession().selectOne(sqlId("findByQueryId"), queryId);
    }

    /**
     * 根据交易流水查询支付交易明细
     *
     * @param queryId 交易流水 每次交易都产生新的
     * @return 交易明细
     */
    public UnionpayTrans findForwardByQueryId(String queryId) {
        return getSqlSession().selectOne(sqlId("findForwardByQueryId"), queryId);
    }


    /**
     * 根据交易流水查询退款交易明细
     *
     * @param queryId 交易流水要与原queryId相等
     * @return 交易明细
     */
    public List<UnionpayTrans> findReverseByQueryId(String queryId) {
        return getSqlSession().selectList(sqlId("findReverseByQueryId"), queryId);
    }


}
