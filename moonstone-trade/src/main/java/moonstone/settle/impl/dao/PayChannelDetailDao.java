package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.enums.TradeType;
import moonstone.settle.model.PayChannelDailySummary;
import moonstone.settle.model.PayChannelDetail;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/24/16
 * Time: 10:43 PM
 */
@Repository
public class PayChannelDetailDao extends MyBatisDao<PayChannelDetail> {

    public PayChannelDetail findPayChannelDetailByTradeNo(String tradeNo){
        return getSqlSession().selectOne(sqlId("findPayChannelDetailByTradeNo"),
                ImmutableMap.of("tradeNo", tradeNo, "tradeType", TradeType.Pay.value()));
    }

    public PayChannelDetail findPayChannelDetailByRefundNo(String refundNo){
        return getSqlSession().selectOne(sqlId("findPayChannelDetailByTradeNo"),
                ImmutableMap.of("tradeNo", refundNo, "tradeType", TradeType.Refund.value()));
    }

    public List<PayChannelDailySummary> generatePayChannelDetail(Date startAt, Date endAt, Integer tradeType) {
        return getSqlSession().selectList(sqlId("sumPayChannelDetail"),
                ImmutableMap.of("startAt", startAt, "endAt", endAt, "tradeType", tradeType));
    }
}
