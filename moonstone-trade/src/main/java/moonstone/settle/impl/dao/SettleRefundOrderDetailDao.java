package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.PlatformTradeDailySummary;
import moonstone.settle.model.SellerTradeDailySummary;
import moonstone.settle.model.SettleRefundOrderDetail;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: Dao类
 * Date: 2016-07-24
 */
@Repository
public class SettleRefundOrderDetailDao extends MyBatisDao<SettleRefundOrderDetail> {

    //############################ 商家日汇总  ###############################
    /**
     * 封装日汇总信息 group by seller id
     * @param startAt 开始日期
     * @param endAt 截止日期
     * @return 日汇总对象
     */
    public List<SellerTradeDailySummary> generateSellerTradeDailySummary(Date startAt,Date endAt){
        return getSqlSession().selectList(sqlId("sumSellerSettlement"),
                ImmutableMap.of("startAt", startAt, "endAt", endAt));
    }

    /**
     * 封装日汇总信息
     * @param startAt 开始日期
     * @param endAt 截止日期
     * @return 日汇总对象
     */
    public PlatformTradeDailySummary generatePlatformTradeDailySummary(Date startAt,Date endAt){
        return getSqlSession().selectOne(sqlId("sumPlatformSettlement"),
                ImmutableMap.of("startAt", startAt, "endAt", endAt));
    }

    /**
     * 根据退款单查找
     * @param refundId
     * @return
     */
    public SettleRefundOrderDetail findByRefundId(Long refundId) {
        return getSqlSession().selectOne(sqlId("findByRefundId"), ImmutableMap.of("refundId", refundId));
    }

    /**
     * 根据订单ID查找
     * @param shopOrderId 订单ID
     * @return
     */
    public List<SettleRefundOrderDetail> findByShopOrderId(Long shopOrderId){
        return getSqlSession().selectList(sqlId("findByOrderId"), ImmutableMap.of("orderId", shopOrderId));
    }
}
