package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.PlatformTradeDailySummary;
import moonstone.settle.model.SellerTradeDailySummary;
import moonstone.settle.model.SettleOrderDetail;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: Dao类
 * Date: 2016-07-24
 */
@Repository
public class SettleOrderDetailDao extends MyBatisDao<SettleOrderDetail> {


    //############################ 商家日汇总  ###############################
    /**
     * 封装日汇总信息 group by seller id
     * @param startAt 开始日期
     * @param endAt 截止日期
     * @return 日汇总对象
     */
    public List<SellerTradeDailySummary> generateSellerTradeDailySummary(Date startAt,Date endAt){
        return getSqlSession().selectList(sqlId("sumSellerSettlement"),
                ImmutableMap.of("startAt", startAt, "endAt", endAt));
    }


    /**
     * 封装日汇总信息
     * @param startAt 开始日期
     * @param endAt 截止日期
     * @return 日汇总对象
     */
    public PlatformTradeDailySummary generatePlatformTradeDailySummary(Date startAt,Date endAt){
        return getSqlSession().selectOne(sqlId("sumPlatformSettlement"),
                ImmutableMap.of("startAt", startAt, "endAt", endAt));
    }


    public SettleOrderDetail findByShopOrderId(Long orderId) {
        return getSqlSession().selectOne(sqlId("findByShopOrderId"), ImmutableMap.of("shopOrderId", orderId));
    }
}
