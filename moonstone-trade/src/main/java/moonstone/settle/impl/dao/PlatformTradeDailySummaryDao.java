package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.common.utils.Arguments;
import moonstone.settle.model.PlatformTradeDailySummary;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * Code generated by terminus code gen
 * Desc: Dao类
 * Date: 2016-07-24
 */
@Repository
public class PlatformTradeDailySummaryDao extends MyBatisDao<PlatformTradeDailySummary> {
    public PlatformTradeDailySummary findBySumAtAndSummaryType(Date sumAt,Integer summaryType){
        return getSqlSession().selectOne(sqlId("findBySumAtAndSummaryType"), ImmutableMap.of("sumAt", sumAt, "summaryType", summaryType));
    }

    public Boolean createOrderUpdate(PlatformTradeDailySummary summary){
        PlatformTradeDailySummary exist = findBySumAtAndSummaryType(summary.getSumAt(), summary.getSummaryType());
        if(Arguments.notNull(exist)) {
            summary.setId(exist.getId());
            return this.update(summary);
        }else {
            return this.create(summary);
        }
    }
}
