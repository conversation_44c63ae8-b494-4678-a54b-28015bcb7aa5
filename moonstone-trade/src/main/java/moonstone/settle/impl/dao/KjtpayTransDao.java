package moonstone.settle.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.KjtpayTrans;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 15/3/3
 * Time: 下午4:25
 */
@Repository
public class KjtpayTransDao extends MyBatisDao<KjtpayTrans> {
    /**
     * 根据商户订单号查询kjtpayTrans
     *
     * @param outerNo 商户订单号
     * @return kjtpayTrans
     */
    public KjtpayTrans findByOuterNo(String outerNo) {
        return getSqlSession().selectOne(sqlId("findByOuterNo"), outerNo);
    }

    /**
     * 根据商户原订单号查询kjtpayTrans
     *
     * @param origOuterNo 商户原订单号
     * @return kjtpayTrans
     */
    public List<KjtpayTrans> findByOrigOuterNo(String origOuterNo) {
        return getSqlSession().selectList(sqlId("findByOrigOuterNo"), origOuterNo);
    }

    /**
     * 根据交易流水号查询kjtpayTrans
     *
     * @param innerNo 交易流水号
     * @return kjtpayTrans
     */
    public KjtpayTrans findByInnerNo(String innerNo) {
        return getSqlSession().selectOne(sqlId("findByInnerNo"), innerNo);
    }


    public KjtpayTrans findRefundTrans(String batchNo){
        return getSqlSession().selectOne(sqlId("findRefundTrans"),batchNo);
    }

}
