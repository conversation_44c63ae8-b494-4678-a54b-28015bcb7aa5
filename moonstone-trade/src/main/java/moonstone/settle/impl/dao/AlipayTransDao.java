/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.AlipayTrans;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 *   功能描述:支付宝账务明细 dao
 * </pre>
 *
 * <AUTHOR> on 2014-12-24.
 */
@Repository
public class AlipayTransDao extends MyBatisDao<AlipayTrans> {


    /**
     * 创建支付宝交易明细到商户系统
     *
     * @param alipayTrans 支付宝交易明细
     * @return 是否创建成功, 如果已经存在则返回失败
     */
    public Boolean createNonexists(AlipayTrans alipayTrans) {
        int countAccountLogId = getSqlSession().<Integer>selectOne("countAccountLogId", alipayTrans.getIwAccountLogId());
        if (countAccountLogId>0) return Boolean.FALSE;
        return create(alipayTrans);
    }


    /**
     * 根据支付宝交易流水查询支付交易明细
     *
     * @param paymentCode 支付宝交易流水
     * @return 支付宝交易明细
     */
    public List<AlipayTrans> findByTradeNo(String paymentCode) {
        return getSqlSession().selectList(sqlId("findByTradeNo"), paymentCode);
    }

    /**
     * 根据商户交易流水查询支付交易明细
     *
     * @param merchantOuterTradeNo 商户交易流水
     * @return 支付宝交易明细
     */
    public List<AlipayTrans> findByMerchantNo(String merchantOuterTradeNo) {
        return getSqlSession().selectList(sqlId("findByMerchantNo"), merchantOuterTradeNo);
    }

    public List<AlipayTrans> findIncomeFee(Date startAt, Date endAt){
        return getSqlSession().selectList(sqlId("findIncomeFee"), ImmutableMap.of("startAt", startAt, "endAt", endAt));
    }

}
