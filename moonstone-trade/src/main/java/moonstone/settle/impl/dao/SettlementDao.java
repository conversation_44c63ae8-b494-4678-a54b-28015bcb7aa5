package moonstone.settle.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.common.utils.MapUtil;
import moonstone.settle.model.Settlement;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: Dao类
 * Date: 2016-07-24
 */
@Repository
public class SettlementDao extends MyBatisDao<Settlement> {

    public Settlement findSettlementByRefundNo(String refundNo){
        return getSqlSession().selectOne(sqlId("findSettlementByRefundNo"), refundNo);
    }

    public Settlement findSettlementByTradeNo(String tradeNo){
        return getSqlSession().selectOne(sqlId("findSettlementByTradeNo"), tradeNo);
    }

    public List<Settlement> findByChannelAndCheckStatus(String channel, Integer checkStatus){
        return getSqlSession().selectList(sqlId("findByChannelAndCheckStatus"),
                MapUtil.from().of("channel", channel, "checkStatus", checkStatus).toMap());
    }
}
