package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.CommissionRule;
import org.springframework.stereotype.Repository;

/**
 * 平台抽佣规则dao
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 14/12/22
 * Time: 下午4:47
 */
@Repository
public class CommissionRuleDao extends MyBatisDao<CommissionRule> {


    /**
     * 根据业务id 业务类型 抽佣类型确定唯一的一条规则
     * @param businessId 业务id
     * @param businessType 业务类型
     * @return 佣金规则
     */
    public CommissionRule findByBusinessIdAndBusinessType(Long businessId,Integer businessType) {
        return getSqlSession().selectOne(sqlId("findByBusinessIdAndBusinessType"), ImmutableMap.of("businessId",businessId,"businessType",businessType));
    }

}
