package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.PayChannelDailySummary;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * Code generated by terminus code gen
 * Desc: Dao类
 * Date: 2016-07-24
 */
@Repository
public class PayChannelDailySummaryDao extends MyBatisDao<PayChannelDailySummary> {

    public PayChannelDailySummary findByChannelAndSumAt(String channel, Date sumAt){
        return getSqlSession().selectOne(sqlId("findByChannelAndSumAt"),
                ImmutableMap.of("channel",channel, "sumAt", sumAt));
    }

}
