/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */
package moonstone.settle.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.settle.model.WechatpayTrans;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 提供对表 `parana_wechatpay_trans`  的增删改查操作<BR>
 * <p/>
 * Created by wanggen 2015-01-13 16:27:07
 */
@Repository
public class WechatpayTransDao extends MyBatisDao<WechatpayTrans> {


    /**
     * 插入微信支付账务数据
     * @param wechatpayTrans    微信账务数据
     * @return  新增ID
     */
    public Long createNotExists(WechatpayTrans wechatpayTrans) {
        int count = countByOutTradeNo(wechatpayTrans.getOutTradeNo(), wechatpayTrans.getTradeTime());
        if (count > 0) return 0l;
        create(wechatpayTrans);
        return wechatpayTrans.getId();
    }

    /**
     * 根据商户交易流水+交易处理时间统计数量
     *
     * @param outTradeNo 商户交易流水
     * @param tradeTime  交易时间
     * @return 已存在记录数量
     */
    public int countByOutTradeNo(String outTradeNo, String tradeTime) {
        return getSqlSession().<Integer>selectOne("countByOutTradeNo", ImmutableMap.of("outTradeNo", outTradeNo, "tradeTime", tradeTime));
    }


    /**
     * 根据 outTradeNo 查询 WechatpayTrans 列表
     *
     * @param outTradeNo 商户系统的订单号，与请求一致
     * @return 结果列
     */
    public List<WechatpayTrans> findByOutTradeNo(String outTradeNo) {
        return getSqlSession().selectList(sqlId("findByOutTradeNo"), outTradeNo);
    }


    public List<WechatpayTrans> findByRefundeNo(String outRefundNo) {
        return getSqlSession().selectList(sqlId("findByOutRefundNo"), outRefundNo);
    }




    /**
     * 根据 transactionId 查询 WechatpayTrans 列表
     *
     * @param transactionId 微信支付订单号
     * @return 结果列
     */
    public List<WechatpayTrans> findByTransactionId(String transactionId) {
        return getSqlSession().selectList(sqlId("findByTransactionId"), transactionId);
    }


    public List<WechatpayTrans> findIncomeFee(Date startAt, Date endAt){
        return getSqlSession().selectList(sqlId("findIncomeFee"), ImmutableMap.of("startAt", startAt, "endAt", endAt));
    }

}
