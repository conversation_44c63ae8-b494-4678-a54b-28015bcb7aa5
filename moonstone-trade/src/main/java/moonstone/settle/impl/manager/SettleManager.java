package moonstone.settle.impl.manager;

import moonstone.settle.enums.CheckStatus;
import moonstone.settle.enums.SummaryType;
import moonstone.settle.enums.TradeType;
import moonstone.settle.impl.dao.*;
import moonstone.settle.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/27/16
 * Time: 2:52 PM
 */
@Component
public class SettleManager {
    @Autowired
    private SettlementDao settlementDao;

    @Autowired
    private PayChannelDetailDao payChannelDetailDao;
    @Autowired
    private SellerTradeDailySummaryDao sellerTradeDailySummaryDao;
    @Autowired
    private PlatformTradeDailySummaryDao platformTradeDailySummaryDao;
    @Autowired
    private SettleOrderDetailDao settleOrderDetailDao;

    @Autowired
    private SettleRefundOrderDetailDao settleRefundOrderDetailDao;

    @Autowired
    private PayChannelDailySummaryDao payChannelDailySummaryDao;

    @Transactional
    public void createOrUpdateSettlement(Settlement settlement) {
        Settlement exist;
        if (settlement.getTradeType().equals(TradeType.Pay.value())) {
            exist = settlementDao.findSettlementByTradeNo(settlement.getTradeNo());
        } else {
            exist = settlementDao.findSettlementByRefundNo(settlement.getRefundNo());
        }
        if (exist == null) {
            settlementDao.create(settlement);
        } else {
            settlement.setId(exist.getId());
            if (exist.getCheckStatus().equals(CheckStatus.CHECK_SUCCESS.value())) {
                settlement.setCheckStatus(exist.getCheckStatus());
                settlement.setCheckFinishedAt(exist.getCheckFinishedAt());
                settlement.setGatewayCommission(exist.getGatewayCommission());
                settlement.setChannelAccount(exist.getChannelAccount());
            }
            settlementDao.update(settlement);

        }
    }

    @Transactional
    public void createOrUpdatePayChannelDetail(PayChannelDetail payChannelDetail) {
        PayChannelDetail exist;
        if (payChannelDetail.getTradeType().equals(TradeType.Pay.value())) {
            exist = payChannelDetailDao.findPayChannelDetailByTradeNo(payChannelDetail.getTradeNo());
        } else {
            exist = payChannelDetailDao.findPayChannelDetailByRefundNo(payChannelDetail.getTradeNo());
        }
        if (exist == null) {
            payChannelDetailDao.create(payChannelDetail);
        } else {
            payChannelDetail.setId(exist.getId());
            if (exist.getCheckStatus().equals(CheckStatus.CHECK_SUCCESS.value())) {
                payChannelDetail.setCheckStatus(exist.getCheckStatus());
                payChannelDetail.setCheckFinishedAt(exist.getCheckFinishedAt());
                payChannelDetail.setGatewayCommission(exist.getGatewayCommission());
                payChannelDetail.setChannelAccount(exist.getChannelAccount());
                payChannelDetail.setActualIncomeFee(payChannelDetail.getTradeFee() - exist.getGatewayCommission());
            }
            payChannelDetailDao.update(payChannelDetail);
        }
    }

    @Transactional
    public void createOrUpdateSettlement(Settlement settlement, PayChannelDetail detail) {
        settlementDao.create(settlement);
        payChannelDetailDao.create(detail);

    }

    @Transactional
    public void batchCreateSellerTradeDailySummary(List<SellerTradeDailySummary> summaryList) {
        for (SellerTradeDailySummary summary : summaryList) {
            sellerTradeDailySummaryDao.createOrderUpdate(summary);
        }
    }

    @Transactional
    public void batchCreateSellerDaily(List<SellerTradeDailySummary> forwardSummarys, List<SellerTradeDailySummary> reverseSummarys, List<SellerTradeDailySummary> mergeSummarys) {
        for (SellerTradeDailySummary summary : forwardSummarys) {
            summary.setSummaryType(SummaryType.FORWARD.value());
            sellerTradeDailySummaryDao.createOrderUpdate(summary);
        }
        for (SellerTradeDailySummary summary : reverseSummarys) {
            summary.setSummaryType(SummaryType.BACKWARD.value());
            sellerTradeDailySummaryDao.createOrderUpdate(summary);
        }
        for (SellerTradeDailySummary summary : mergeSummarys) {
            summary.setSummaryType(SummaryType.ALL.value());
            sellerTradeDailySummaryDao.createOrderUpdate(summary);
        }
    }

    @Transactional
    public void batchCreatePlatformDaily(PlatformTradeDailySummary forwardSummary, PlatformTradeDailySummary reverseSummary, PlatformTradeDailySummary mergeSummary) {
        if (forwardSummary.getOrderCount() > 0) {
            platformTradeDailySummaryDao.createOrderUpdate(forwardSummary);
        }
        if (reverseSummary.getRefundOrderCount() > 0) {
            platformTradeDailySummaryDao.createOrderUpdate(reverseSummary);
        }
        if (mergeSummary.getOrderCount() > 0 || mergeSummary.getRefundOrderCount() > 0) {
            platformTradeDailySummaryDao.createOrderUpdate(mergeSummary);
        }
    }

    @Transactional
    public void createSettleOrderDetails(List<SettleOrderDetail> details) {
        for (SettleOrderDetail detail : details) {
            settleOrderDetailDao.create(detail);
        }
    }

    @Transactional
    public void createSettleRefundOrderDetails(List<SettleRefundOrderDetail> details) {
        for (SettleRefundOrderDetail detail : details) {
            settleRefundOrderDetailDao.create(detail);
        }
    }

    @Transactional
    public void batchCreateOrUpdatePayChannelDailySummary(List<PayChannelDailySummary> payChannelDailySummaryList) {
        for (PayChannelDailySummary summary : payChannelDailySummaryList) {
            PayChannelDailySummary exists = payChannelDailySummaryDao.findByChannelAndSumAt(summary.getChannel(), summary.getSumAt());
            if (exists == null) {
                payChannelDailySummaryDao.create(summary);
            } else {
                summary.setId(exists.getId());
                payChannelDailySummaryDao.update(summary);
            }
        }
    }

    @Transactional
    public void batchCreateOrUpdatePlatformTradeDailySummary(List<PlatformTradeDailySummary> summaryList) {
        for (PlatformTradeDailySummary summary : summaryList) {
            PlatformTradeDailySummary exists = platformTradeDailySummaryDao.findBySumAtAndSummaryType(summary.getSumAt(), summary.getSummaryType());
            if (exists == null) {
                platformTradeDailySummaryDao.create(summary);
            } else {
                summary.setId(exists.getId());
                platformTradeDailySummaryDao.update(summary);
            }
        }
    }

    @Transactional
    public void createOrUpdateOrderDetail(SettleOrderDetail settleOrderDetail) {
        SettleOrderDetail exists = settleOrderDetailDao.findByShopOrderId(settleOrderDetail.getOrderId());
        if (exists == null) {
            settleOrderDetailDao.create(settleOrderDetail);
        } else {
            settleOrderDetail.setId(exists.getId());
            if (exists.getCheckStatus().equals(CheckStatus.CHECK_SUCCESS.value())) {
                settleOrderDetail.setCheckStatus(exists.getCheckStatus());
                settleOrderDetail.setCheckAt(exists.getCheckAt());
                settleOrderDetail.setGatewayCommission(exists.getGatewayCommission());
                settleOrderDetail.setChannelAccount(exists.getChannelAccount());
                //设置商家应收
                settleOrderDetail.setSellerReceivableFee(settleOrderDetail.getSellerReceivableFee() - exists.getGatewayCommission());
            }
            settleOrderDetailDao.update(settleOrderDetail);
        }
    }

    @Transactional
    public void createOrUpdateRefundDetail(SettleRefundOrderDetail settleRefundOrderDetail) {
        SettleRefundOrderDetail exists = settleRefundOrderDetailDao.findByRefundId(settleRefundOrderDetail.getRefundId());
        if (exists == null) {
            settleRefundOrderDetailDao.create(settleRefundOrderDetail);
        } else {
            settleRefundOrderDetail.setId(exists.getId());
            if (exists.getCheckStatus().equals(CheckStatus.CHECK_SUCCESS.value())) {
                settleRefundOrderDetail.setCheckStatus(exists.getCheckStatus());
                settleRefundOrderDetail.setCheckAt(exists.getCheckAt());
                settleRefundOrderDetail.setGatewayCommission(exists.getGatewayCommission());
                settleRefundOrderDetail.setChannelAccount(exists.getChannelAccount());
                //设置商家应收
                settleRefundOrderDetail.setSellerDeductFee(settleRefundOrderDetail.getSellerDeductFee() - exists.getGatewayCommission());
            }
            settleRefundOrderDetailDao.update(settleRefundOrderDetail);
        }
    }
}
