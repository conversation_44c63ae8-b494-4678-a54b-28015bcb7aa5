package moonstone.settle.impl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Throwables;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.MapUtil;
import moonstone.common.utils.Translate;
import moonstone.order.component.PaymentAccountUtil;
import moonstone.order.impl.dao.OrderPaymentDao;
import moonstone.order.impl.dao.PaymentDao;
import moonstone.order.model.*;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.settle.dto.SettlementDto;
import moonstone.settle.dto.paging.SettlementCriteria;
import moonstone.settle.enums.CheckStatus;
import moonstone.settle.enums.TradeBusinessType;
import moonstone.settle.impl.dao.SettlementDao;
import moonstone.settle.impl.event.SettlementUpdateEvent;
import moonstone.settle.model.Settlement;
import moonstone.settle.service.SettlementReadService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Code generated by terminus code gen
 * Desc: 读服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@AllArgsConstructor
public class SettlementReadServiceImpl implements SettlementReadService {

    private final SettlementDao settlementDao;

    private final OrderPaymentDao orderPaymentDao;

    private final PaymentDao paymentDao;

    private final ShopOrderReadService shopOrderReadService;

    private final SkuOrderReadService skuOrderReadService;

    private final ShopCacheHolder shopCacheHolder;

    private final RefundReadService refundReadService;

    @Override
    public Response<Paging<Settlement>> pagingSettlements(SettlementCriteria criteria) {
        // set the account name
        if (criteria.getNeedManualCheck() != null) {
            if (criteria.getNeedManualCheck()) {
                criteria.setRecpAccounts(new String[]{"**********", "<EMAIL>", "****************"});
                if (criteria.getChannel() != null) {
                    if (criteria.getChannel().startsWith("alipay")) {
                        criteria.setRecpAccounts(new String[]{"<EMAIL>", "****************"});
                    } else if (criteria.getChannel().startsWith("wechat")) {
                        criteria.setRecpAccounts(new String[]{"**********"});
                    }
                }
            } else {
                criteria.setRecpAccountExclude(new String[]{"**********", "<EMAIL>", "****************"});
                if (criteria.getChannel() != null) {
                    if (criteria.getChannel().startsWith("alipay")) {
                        criteria.setRecpAccountExclude(new String[]{"<EMAIL>", "****************"});
                    } else if (criteria.getChannel().startsWith("wechat")) {
                        criteria.setRecpAccountExclude(new String[]{"**********"});
                    }
                }
            }
        }
        try {
            // set the trade no
            if (criteria.getOrderId() != null) {
                List<OrderPayment> orderPaymentList = orderPaymentDao.findByOrderIdAndOrderType(criteria.getOrderId(), criteria.getOrderType());
                if (CollectionUtils.isEmpty(orderPaymentList)) {
                    return Response.ok(Paging.empty());
                }
                Set<String> tradeNo = new HashSet<>();
                for (OrderPayment orderPayment : orderPaymentList) {
                    Optional.ofNullable(paymentDao.findById(orderPayment.getPaymentId()))
                            .map(Payment::getOutId).ifPresent(tradeNo::add);
                }
                List<Refund> refunds = refundReadService.findByOrderIdAndOrderLevel(criteria.getOrderId(), OrderLevel.fromInt(criteria.getOrderType())).getResult();
                if (CollectionUtils.isEmpty(refunds)) {
                    for (Refund refund : refunds) {
                        tradeNo.add(refund.getTradeNo());
                    }
                }
                if (tradeNo.size() > 1) {
                    log.warn("{} we got different TradeNo[{}] for orderId[{}]", LogUtil.getClassMethodName(),
                            tradeNo.size() > 4 ? tradeNo.size() : JSON.toJSON(tradeNo)
                            , criteria.getOrderId());
                }
                if (!tradeNo.isEmpty()) {
                    criteria.setTradeNo(tradeNo.iterator().next());
                }
            }
            Paging<Settlement> settlementPaging = settlementDao.paging(criteria.toMap());
            if (settlementPaging != null && settlementPaging.getData() != null) {
                settlementPaging.getData().stream().filter(Objects::nonNull).forEach(this::validShipment);
                settlementPaging.getData().stream().filter(Objects::nonNull).forEach(this::addShopName);
                settlementPaging.getData().stream().filter(Objects::nonNull).forEach(this::validSupplierName);
                //  recpAccount会被污染 所以请不要放在前面
                settlementPaging.getData().stream().filter(Objects::nonNull).forEach(this::validRecpAccount);
            }
            return Response.ok(settlementPaging);
        } catch (Exception e) {
            log.error("pagingSettlements fail, criteria={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("paging.settlement.fail");
        }
    }

    private void addShopName(Settlement settlement) {
        if (Objects.isNull(settlement.getShopId()))
            return;
        try {
            settlement.setShopName(shopCacheHolder.findShopById(settlement.getShopId()).getName());
        } catch (Exception ex) {
            log.error("{} failed to read shopCache by shopId:{} msg:{}", LogUtil.getClassMethodName(), settlement.getShopId(), ex.getMessage());
        }
    }

    private void validShipment(Settlement settlement) {
        if (settlement.getShipmentAt() != null) return;
        List<OrderRelation> orderRelations = new ArrayList<>();
        if (settlement.getTradeBusinessType() == null) return;
        switch (TradeBusinessType.from(settlement.getTradeBusinessType())) {
            case Refund: {
                List<OrderRefund> orderRefunds = refundReadService.findOrderIdsByRefundId(settlement.getPaymentOrRefundId()).getResult();
                if (orderRefunds == null) {
                    log.error("{} fail to find data from refundId:{}", LogUtil.getClassMethodName(), settlement.getPaymentOrRefundId());
                    return;
                }
                orderRelations.addAll(orderRefunds);
                break;
            }
            case Pay: {
                List<OrderPayment> orderPayments = orderPaymentDao.findByPaymentId(settlement.getPaymentOrRefundId());
                if (orderPayments == null) {
                    log.error("{} fail to find data from paymentId:{}", LogUtil.getClassMethodName(), settlement.getPaymentOrRefundId());
                    return;
                }
                orderRelations.addAll(orderPayments);
                break;
            }
        }
        for (OrderRelation orderRelation : orderRelations) {
            Optional<Date> shipmentAt;
            if (OrderLevel.SKU.getValue() == orderRelation.getOrderType())
                shipmentAt = Optional.ofNullable((SkuOrder) findOrder(orderRelation.getOrderId(), OrderLevel.SKU))
                        .map(SkuOrder::getOrderId)
                        .map(orderId -> (ShopOrder) findOrder(orderId, OrderLevel.SHOP))
                        .map(ShopOrder::getFirstShipmentAt);
            else
                shipmentAt = Optional.ofNullable((ShopOrder) findOrder(orderRelation.getOrderId(), OrderLevel.SHOP))
                        .map(ShopOrder::getFirstShipmentAt);
            if (shipmentAt.isPresent()) {
                settlement.setShipmentAt(shipmentAt.get());
                return;
            }
        }
        if (settlement.getShipmentAt() != null && settlement.getShipmentAt().after(settlement.getTradeFinishedAt() != null ? settlement.getTradeFinishedAt() : settlement.getShipmentAt())) {
            EventSender.sendApplicationEvent(new SettlementUpdateEvent(settlement));
        }
    }

    OrderBase findOrder(Long orderId, OrderLevel orderLevel) {
        switch (orderLevel) {
            case SKU:
                return skuOrderReadService.findById(orderId).getResult();
            case SHOP:
                return shopOrderReadService.findById(orderId).getResult();
        }
        return null;
    }


    private void validSupplierName(Settlement settlement) {
        if (settlement == null || StringUtils.hasText(settlement.getDepotNames())) return;
        try {
            final Long paymentId = paymentDao.findByOutId(settlement.getTradeNo()).getId();
            List<SkuOrder> skuOrders = new ArrayList<>();
            for (OrderPayment orderPayment : orderPaymentDao.findByPaymentId(paymentId)) {
                if (orderPayment.getOrderLevel().equals(OrderLevel.SKU))
                    skuOrders.add((SkuOrder) findOrder(orderPayment.getOrderId(), OrderLevel.SKU));
                else
                    skuOrders.addAll(findSkuOrderByOrderId(orderPayment.getOrderId()));
            }
            settlement.setSupplierNames(Joiner.on(",").join(skuOrders.stream().map(SkuOrder::getSupplierName).filter(Objects::nonNull).distinct().collect(Collectors.toList())));
            settlement.setDepotNames(Joiner.on(",").join(skuOrders.stream().map(SkuOrder::getDepotName).filter(Objects::nonNull).distinct().collect(Collectors.toList())));
            EventSender.sendApplicationEvent(new SettlementUpdateEvent(settlement));
        } catch (Exception ex) {
            log.error("{} fail to trim the settlement[{}]", LogUtil.getClassMethodName(), settlement.getId(), ex);
        }
    }

    private Collection<? extends SkuOrder> findSkuOrderByOrderId(Long orderId) {
        return skuOrderReadService.findByShopOrderId(orderId).getResult();
    }

    private void validRecpAccount(Settlement settlement) {
        Optional<Payment> paymentStorage = Optional.empty();
        Function<OrderPayment, OrderBase> findOrder = (op) -> {
            try {
                return findOrder(op.getOrderId(), op.getOrderLevel());
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("{} op:{}", LogUtil.getClassMethodName(), op);
                return null;
            }
        };
        Supplier<Payment> paymentSupplier = () -> paymentDao.findBySerialNoAndChannel(settlement.getGatewayTradeNo(), settlement.getChannel());
        if (settlement.getShopId() == null) {
            paymentStorage = Optional.ofNullable(paymentStorage.orElseGet(paymentSupplier));
            paymentStorage.map(Payment::getId)
                    .map(orderPaymentDao::findByPaymentId)
                    .map(List::iterator)
                    .filter(Iterator::hasNext)
                    .map(Iterator::next)
                    .map(findOrder)
                    .map(OrderBase::getShopId)
                    .ifPresent(settlement::setShopId);
        }
        if (settlement.getRecpAccount() == null || settlement.getRecpAccount().isEmpty()) {
            Payment payment = paymentStorage.orElseGet(paymentSupplier);
            if (payment == null) {
                log.error("{} payment-paySerial:{} channel:{}", LogUtil.getClassMethodName(), settlement.getGatewayTradeNo(), settlement.getChannel());
                return;
            }
            PaymentAccountUtil.genRecpAccount(payment.getPayRequest()).ifPresent(settlement::setRecpAccount);
        }

        /// 正常情况下为空，只有当数据成功更新了，那么payment才有值
        if (paymentStorage.isPresent()) {
            EventSender.sendApplicationEvent(new SettlementUpdateEvent(settlement));
        }
        /// 以下两者皆为公司的收款帐号
        if (Objects.equals("**********", settlement.getRecpAccount()) || Objects.equals("<EMAIL>", settlement.getRecpAccount()) || Objects.equals("****************", settlement.getRecpAccount())) {
            settlement.setRecpAccount(settlement.getRecpAccount() + "(" + new Translate("但丁") + ")");
        }
    }

    @Override
    public Response<SettlementDto> findSettlementDetailById(Long settlementId) {
        try {
            Settlement settlement = settlementDao.findById(settlementId);
            if (settlement == null) {
                log.error("findSettlementDetailById fail, cause={}, params={}", "settlement.not.exists", settlementId);
                return Response.fail("settlement.not.exists");
            }
            SettlementDto dto = new SettlementDto();
            dto.setSettlement(settlement);
            return Response.ok(dto);
        } catch (Exception e) {
            log.error("findSettlementDetailById fail, settlementId={}, cause={}", settlementId, Throwables.getStackTraceAsString(e));
            return Response.fail("find.settlement.detail.fail");
        }
    }

    @Override
    public Response<Settlement> findSettlementById(Long settlementId) {
        try {
            Settlement settlement = settlementDao.findById(settlementId);
            if (settlement == null) {
                log.error("findSettlementDetailById fail, cause={}, params={}", "settlement.not.exists", settlementId);
                return Response.fail("settlement.not.exists");
            }
            return Response.ok(settlement);
        } catch (Exception e) {
            log.error("find settlement by id failed, settlementId:{}, cause:{}", settlementId, Throwables.getStackTraceAsString(e));
            return Response.fail("find.settlement.fail");
        }
    }

    @Override
    public Response<List<Settlement>> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<Settlement> settlements = settlementDao.findByIds(ids);
            return Response.ok(settlements);
        } catch (Exception e) {
            log.error("fail to find settle by ids {}, cause:{}",
                    ids, Throwables.getStackTraceAsString(e));
            return Response.fail("find.settlement.fail");
        }
    }

    @Override
    public Response<List<Settlement>> findByChannelAndCheckStatus(String channel, CheckStatus checkStatus) {
        try {
            List<Settlement> settlements = settlementDao.findByChannelAndCheckStatus(channel, checkStatus.value());
            return Response.ok(settlements);
        } catch (Exception e) {
            log.error("findByChannelAndCheckStatus fail, param={}, cause={}", MapUtil.from().of("channel", channel, "checkStatus", checkStatus), Throwables.getStackTraceAsString(e));
            return Response.fail("settlement.find.by.channel.and.check.status.fail");
        }
    }

    @Override
    public Response<Settlement> findSettlementByRefundNo(String refundNo) {
        try {
            Settlement settlement = settlementDao.findSettlementByRefundNo(refundNo);
            if (settlement == null) {
                log.error("findSettlementByRefundNo fail, cause={}, params={}", "settlement.not.exists", refundNo);
                return Response.fail("settlement.not.exists");
            }
            return Response.ok(settlement);
        } catch (Exception e) {
            log.error("find settlement by id failed, refundNo:{}, cause:{}", refundNo, Throwables.getStackTraceAsString(e));
            return Response.fail("find.settlement.fail");
        }
    }

    @Override
    public Response<Settlement> findSettlementByTradeNo(String tradeNo) {
        try {
            Settlement settlement = settlementDao.findSettlementByTradeNo(tradeNo);
            if (settlement == null) {
                log.error("findSettlementByTradeNo fail, cause={}, params={}", "settlement.not.exists", tradeNo);
                return Response.fail("settlement.not.exists");
            }
            return Response.ok(settlement);
        } catch (Exception e) {
            log.error("find settlement by id failed, tradeNo:{}, cause:{}", tradeNo, Throwables.getStackTraceAsString(e));
            return Response.fail("find.settlement.fail");
        }
    }
}
