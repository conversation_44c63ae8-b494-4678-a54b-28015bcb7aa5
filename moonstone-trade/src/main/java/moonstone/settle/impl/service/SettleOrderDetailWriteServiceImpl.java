package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OrderPaymentDao;
import moonstone.settle.impl.dao.SettleOrderDetailDao;
import moonstone.settle.impl.manager.SettleManager;
import moonstone.settle.model.SettleOrderDetail;
import moonstone.settle.service.SettleOrderDetailWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: 写服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class SettleOrderDetailWriteServiceImpl implements SettleOrderDetailWriteService {

    @Autowired
    private SettleOrderDetailDao settleOrderDetailDao;

    @Autowired
    private OrderPaymentDao orderPaymentDao;

    @Autowired
    private SettleManager settleManager;


    @Override
    public Response<Long> createSettleOrderDetail(SettleOrderDetail settleOrderDetail) {
        try {
            settleManager.createOrUpdateOrderDetail(settleOrderDetail);
            return Response.ok(settleOrderDetail.getId());
        } catch (Exception e) {
            log.error("create settleOrderDetail failed, settleOrderDetail:{}, cause:{}", settleOrderDetail, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.detail.create.fail");
        }
    }


    @Override
    public Response<Boolean> createSettleOrderDetails(List<SettleOrderDetail> settleOrderDetails) {
        try {
            settleManager.createSettleOrderDetails(settleOrderDetails);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("create settleOrderDetail failed cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.detail.create.fail");
        }
    }


    @Override
    public Response<Boolean> updateSettleOrderDetail(SettleOrderDetail settleOrderDetail) {
        try {
            return Response.ok(settleOrderDetailDao.update(settleOrderDetail));
        } catch (Exception e) {
            log.error("update settleOrderDetail failed, settleOrderDetail:{}, cause:{}", settleOrderDetail, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.detail.update.fail");
        }
    }

    @Override
    public Response<Boolean> deleteSettleOrderDetailById(Long settleOrderDetailId) {
        try {
            return Response.ok(settleOrderDetailDao.delete(settleOrderDetailId));
        } catch (Exception e) {
            log.error("delete settleOrderDetail failed, settleOrderDetailId:{}, cause:{}", settleOrderDetailId, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.detail.delete.fail");
        }
    }
}
