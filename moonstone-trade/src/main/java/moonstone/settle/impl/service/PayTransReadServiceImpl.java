package moonstone.settle.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.service.PayTransReadService;
import org.springframework.stereotype.Service;

/**
 * DATE: 16/7/25 上午11:09 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Service
@Slf4j
@RpcProvider
public class PayTransReadServiceImpl implements PayTransReadService {

}
