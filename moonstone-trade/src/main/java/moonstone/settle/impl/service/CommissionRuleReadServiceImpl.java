package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Arguments;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.dto.paging.CommissionRuleCriteria;
import moonstone.settle.impl.dao.CommissionRuleDao;
import moonstone.settle.model.CommissionRule;
import moonstone.settle.service.CommissionRuleReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkState;
import static io.terminus.common.utils.Arguments.notNull;

/**
 * Code generated by terminus code gen
 * Desc: 读服务实现类
 * Date: 2016-07-25
 */
@Slf4j
@Service
@RpcProvider
public class CommissionRuleReadServiceImpl implements CommissionRuleReadService {

    private final CommissionRuleDao commissionRuleDao;

    @Autowired
    public CommissionRuleReadServiceImpl(CommissionRuleDao commissionRuleDao) {
        this.commissionRuleDao = commissionRuleDao;
    }

    @Override
    public Response<CommissionRule> findCommissionRuleById(Long commissionRuleId) {
        try {
            return Response.ok(commissionRuleDao.findById(commissionRuleId));
        } catch (Exception e) {
            log.error("find commissionRule by id failed, commissionRuleId:{}, cause:{}", commissionRuleId, Throwables.getStackTraceAsString(e));
            return Response.fail("commission.rule.find.fail");
        }
    }


    @Override
    public Response<CommissionRule> findMatchCommissionRule(Long businessId, Integer businessType) {
        Response<CommissionRule> result = new Response<CommissionRule>();
        try {
            checkArgument(notNull(businessId),"param.business.id.invalid");
            checkArgument(notNull(businessType),"param.business.type.invalid");
            result.setResult(commissionRuleDao.findByBusinessIdAndBusinessType(businessId, businessType));
        }catch (Exception e){
            log.error("find match commission rule where businessId: {} businessType: {} fail,cause: {}",businessId,businessType,Throwables.getStackTraceAsString(e));
            result.setError("commission.rule.find.fail");
        }
        return result;
    }

    @Override
    public Response<Long> countCommission(Long businessId, Integer businessType, Long fee) {
        Response<Long> result = new Response<Long>();
        try {
            checkArgument(notNull(businessId),"commission.rule.business.id.invalid");
            checkArgument(notNull(businessType),"commission.rule.business.type.invalid");
            CommissionRule rule = commissionRuleDao.findByBusinessIdAndBusinessType(businessId,businessType);
            checkState(Arguments.notNull(rule),"commission.rule.not.exist");
            checkState(Arguments.notNull(rule.getRate()),"commission.rule.rate.is.null");

            result.setResult(getCommission(fee,rule.getRate()));

        }catch (IllegalStateException | IllegalArgumentException e){
            log.error("find match commission rule where businessId: {} businessType: {} fail,error: {}",businessId,businessType,e.getMessage());
            result.setError(e.getMessage());
        }catch (Exception e){
            log.error("find match commission rule where businessId: {} businessType: {} fail,cause: {}",businessId,businessType,Throwables.getStackTraceAsString(e));
            result.setError("commission.rule.find.fail");
        }
        return result;
    }

    private Long getCommission(Long fee,Integer rate){
        return new BigDecimal(fee).multiply(new BigDecimal(rate).divide(new BigDecimal(10000))).setScale(0, BigDecimal.ROUND_UP).longValue();//取0位小数 向上取整

    }




    @Override
    public Response<Paging<CommissionRule>> pagingCommissionRules(CommissionRuleCriteria criteria) {
        try{
            return Response.ok(commissionRuleDao.paging(criteria.toMap()));
        }catch (Exception e){
            log.error("pagingCommissionRules fail, params={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("commission.rules.paging.fail");
        }
    }
}
