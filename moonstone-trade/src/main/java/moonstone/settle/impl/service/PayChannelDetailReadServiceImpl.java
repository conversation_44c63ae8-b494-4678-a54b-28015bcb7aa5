package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.dto.paging.PayChannelDetailCriteria;
import moonstone.settle.impl.dao.PayChannelDetailDao;
import moonstone.settle.model.PayChannelDetail;
import moonstone.settle.service.PayChannelDetailReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Code generated by terminus code gen
 * Desc: 读服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class PayChannelDetailReadServiceImpl implements PayChannelDetailReadService {

    private final PayChannelDetailDao payChannelDetailDao;

    @Autowired
    public PayChannelDetailReadServiceImpl(PayChannelDetailDao payChannelDetailDao) {
        this.payChannelDetailDao = payChannelDetailDao;
    }

    @Override
    public Response<PayChannelDetail> findPayChannelDetailById(Long payChannelDetailId) {
        try {
            return Response.ok(payChannelDetailDao.findById(payChannelDetailId));
        } catch (Exception e) {
            log.error("find payChannelDetail by id failed, payChannelDetailId:{}, cause:{}", payChannelDetailId, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.detail.find.fail");
        }
    }

    @Override
    public Response<Paging<PayChannelDetail>> pagingPayChannelDetails(PayChannelDetailCriteria criteria) {
        try{
            return Response.ok(payChannelDetailDao.paging(criteria.toMap()));
        } catch (Exception e){
            log.error("pagingPayChannelDetails fail, params={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.details.paging.fail");
        }
    }

    @Override
    public Response<PayChannelDetail> findPayChannelDetailByRefundNo(String refundNo) {
        try{
            return Response.ok(payChannelDetailDao.findPayChannelDetailByRefundNo(refundNo));
        }catch (Exception e){
            log.error("findPayChannelDetailByRefundNo fail, refundNo={}, cause={}",
                    refundNo, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.detail.find.fail");
        }
    }

    @Override
    public Response<PayChannelDetail> findPayChannelDetailByTradeNo(String tradeNo) {
        try{
            return Response.ok(payChannelDetailDao.findPayChannelDetailByTradeNo(tradeNo));
        }catch (Exception e){
            log.error("findPayChannelDetailByTradeNo fail, tradeNo={}, cause={}",
                    tradeNo, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.detail.find.fail");
        }
    }
}
