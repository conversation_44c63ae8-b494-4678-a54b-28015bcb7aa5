package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.dto.paging.SettleAbnormalTrackCriteria;
import moonstone.settle.impl.dao.SettleAbnormalTrackDao;
import moonstone.settle.model.SettleAbnormalTrack;
import moonstone.settle.service.SettleAbnormalTrackReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Code generated by terminus code gen
 * Desc: 读服务实现类
 * Date: 2016-07-26
 */
@Slf4j
@Service
@RpcProvider
public class SettleAbnormalTrackReadServiceImpl implements SettleAbnormalTrackReadService {

    private final SettleAbnormalTrackDao settleAbnormalTrackDao;

    @Autowired
    public SettleAbnormalTrackReadServiceImpl(SettleAbnormalTrackDao settleAbnormalTrackDao) {
        this.settleAbnormalTrackDao = settleAbnormalTrackDao;
    }

    @Override
    public Response<SettleAbnormalTrack> findSettleAbnormalTrackById(Long settleAbnormalTrackId) {
        try {
            return Response.ok(settleAbnormalTrackDao.findById(settleAbnormalTrackId));
        } catch (Exception e) {
            log.error("find settleAbnormalTrack by id failed, settleAbnormalTrackId:{}, cause:{}", settleAbnormalTrackId, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.abnormal.track.find.fail");
        }
    }

    @Override
    public Response<Paging<SettleAbnormalTrack>> pagingSettleAbnormalTracks(SettleAbnormalTrackCriteria criteria) {
        try{
            return Response.ok(settleAbnormalTrackDao.paging(criteria.toMap()));
        }catch (Exception e){
            log.error("pagingSettleAbnormalTracks fail, param={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.abnormal.tracks.paging.fail");
        }
    }
}
