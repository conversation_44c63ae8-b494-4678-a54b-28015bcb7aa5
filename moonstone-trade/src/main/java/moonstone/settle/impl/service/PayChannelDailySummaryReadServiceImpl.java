package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.dto.paging.PayChannelDailySummaryCriteria;
import moonstone.settle.impl.dao.PayChannelDailySummaryDao;
import moonstone.settle.model.PayChannelDailySummary;
import moonstone.settle.service.PayChannelDailySummaryReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Code generated by terminus code gen
 * Desc: 读服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class PayChannelDailySummaryReadServiceImpl implements PayChannelDailySummaryReadService {

    private final PayChannelDailySummaryDao payChannelDailySummaryDao;

    @Autowired
    public PayChannelDailySummaryReadServiceImpl(PayChannelDailySummaryDao payChannelDailySummaryDao) {
        this.payChannelDailySummaryDao = payChannelDailySummaryDao;
    }

    @Override
    public Response<PayChannelDailySummary> findPayChannelDailySummaryById(Long payChannelDailySummaryId) {
        try {
            return Response.ok(payChannelDailySummaryDao.findById(payChannelDailySummaryId));
        } catch (Exception e) {
            log.error("find payChannelDailySummary by id failed, payChannelDailySummaryId:{}, cause:{}", payChannelDailySummaryId, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.daily.summary.find.fail");
        }
    }

    @Override
    public Response<Paging<PayChannelDailySummary>> pagingPayChannelDailySummarys(PayChannelDailySummaryCriteria criteria) {
        try{
            return Response.ok(payChannelDailySummaryDao.paging(criteria.toMap()));
        }catch (Exception e){
            log.error("pagingPayChannelDailySummarys fail, params={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.daily.summarys.paging.fail");
        }
    }
}
