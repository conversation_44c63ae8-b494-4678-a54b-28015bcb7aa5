package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.api.SummaryRule;
import moonstone.settle.impl.dao.SellerTradeDailySummaryDao;
import moonstone.settle.impl.dao.SettleOrderDetailDao;
import moonstone.settle.impl.dao.SettleRefundOrderDetailDao;
import moonstone.settle.impl.manager.SettleManager;
import moonstone.settle.model.SellerTradeDailySummary;
import moonstone.settle.service.SellerTradeDailySummaryWriteService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: 写服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class SellerTradeDailySummaryWriteServiceImpl implements SellerTradeDailySummaryWriteService {

    @Autowired
    private  SellerTradeDailySummaryDao sellerTradeDailySummaryDao;

    @Autowired
    private SettleOrderDetailDao settleOrderDetailDao;

    @Autowired
    private SettleRefundOrderDetailDao settleRefundOrderDetailDao;

    @Autowired
    private SettleManager settleManager;

    @Autowired
    private SummaryRule summaryRule;


    @Override
    public Response<Long> createSellerTradeDailySummary(SellerTradeDailySummary sellerTradeDailySummary) {
        try {
            sellerTradeDailySummaryDao.create(sellerTradeDailySummary);
            return Response.ok(sellerTradeDailySummary.getId());
        } catch (Exception e) {
            log.error("create sellerTradeDailySummary failed, sellerTradeDailySummary:{}, cause:{}", sellerTradeDailySummary, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.trade.daily.summary.create.fail");
        }
    }

    @Override
    public Response<Boolean> updateSellerTradeDailySummary(SellerTradeDailySummary sellerTradeDailySummary) {
        try {
            return Response.ok(sellerTradeDailySummaryDao.update(sellerTradeDailySummary));
        } catch (Exception e) {
            log.error("update sellerTradeDailySummary failed, sellerTradeDailySummary:{}, cause:{}", sellerTradeDailySummary, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.trade.daily.summary.update.fail");
        }
    }

    @Override
    public Response<Boolean> deleteSellerTradeDailySummaryById(Long sellerTradeDailySummaryId) {
        try {
            return Response.ok(sellerTradeDailySummaryDao.delete(sellerTradeDailySummaryId));
        } catch (Exception e) {
            log.error("delete sellerTradeDailySummary failed, sellerTradeDailySummaryId:{}, cause:{}", sellerTradeDailySummaryId, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.trade.daily.summary.delete.fail");
        }
    }

    @Override
    public Response<Boolean> batchCreate(List<SellerTradeDailySummary> forwardSummarys, List<SellerTradeDailySummary> reverseSummarys, List<SellerTradeDailySummary> mergeSummarys) {
        Response<Boolean> result = new Response<Boolean>();
        try {

            settleManager.batchCreateSellerDaily(forwardSummarys, reverseSummarys, mergeSummarys);

            result.setResult(Boolean.TRUE);
        }catch (Exception e){
            log.error("batch create seller trade daily summary fail,cause: {}",Throwables.getStackTraceAsString(e));
            result.setError("seller.trade.daily.summary.create.fail");

        }
        return result;
    }

    @Override
    public Response<Boolean> generateSellerTradeDailySummary(Date sumAt) {
        try{
            Date startAt = sumAt;
            Date endAt = new DateTime(sumAt.getTime()).plusDays(1).toDate();

            List<SellerTradeDailySummary> forwardList = settleOrderDetailDao.generateSellerTradeDailySummary(startAt, endAt);
            List<SellerTradeDailySummary> backwardList = settleRefundOrderDetailDao.generateSellerTradeDailySummary(startAt, endAt);

            List<SellerTradeDailySummary> allSummaryList = summaryRule.sellerDaily(forwardList, backwardList);

            for(SellerTradeDailySummary summary : allSummaryList){
                summary.setSumAt(sumAt);
            }
            settleManager.batchCreateSellerTradeDailySummary(allSummaryList);

            return Response.ok(Boolean.TRUE);
        }catch (Exception e){
            log.error("generateSellerTradeDailySummary fail, sumAt={}, cause={}", sumAt , Throwables.getStackTraceAsString(e));
            return Response.fail("generate.seller.trade.daily.summary.fail");
        }
    }


}
