package moonstone.settle.impl.service;

import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.impl.dao.SettleAbnormalTrackDao;
import moonstone.settle.model.SettleAbnormalTrack;
import moonstone.settle.service.SettleAbnormalTrackWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Code generated by terminus code gen
 * Desc: 写服务实现类
 * Date: 2016-07-26
 */
@Slf4j
@Service
@RpcProvider
public class SettleAbnormalTrackWriteServiceImpl implements SettleAbnormalTrackWriteService {

    private final SettleAbnormalTrackDao settleAbnormalTrackDao;

    @Autowired
    public SettleAbnormalTrackWriteServiceImpl(SettleAbnormalTrackDao settleAbnormalTrackDao) {
        this.settleAbnormalTrackDao = settleAbnormalTrackDao;
    }

    @Override
    public Response<Long> createSettleAbnormalTrack(SettleAbnormalTrack settleAbnormalTrack) {
        try {
            String desc=settleAbnormalTrack.getDescription();
            if(Strings.isNullOrEmpty(desc) && desc.length()>256){
                desc=desc.substring(0,255);
            }
            settleAbnormalTrack.setDescription(desc);
            settleAbnormalTrackDao.create(settleAbnormalTrack);
            return Response.ok(settleAbnormalTrack.getId());
        } catch (Exception e) {
            log.error("create settleAbnormalTrack failed, settleAbnormalTrack:{}, cause:{}", settleAbnormalTrack, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.abnormal.track.create.fail");
        }
    }

    @Override
    public Response<Long> createSettleAbnormalTrack(String abnormalInfo,String desc,Integer type) {
        SettleAbnormalTrack track = new SettleAbnormalTrack();
        try {
            if(!Strings.isNullOrEmpty(desc) && desc.length()>256){
                desc=desc.substring(0,255);
            }
            track.setIsHandle(Boolean.FALSE);
            track.setAbnormalInfo(abnormalInfo);
            track.setAbnormalType(type);
            track.setDescription(desc);
            settleAbnormalTrackDao.create(track);
            return Response.ok(track.getId());
        } catch (Exception e) {
            log.error("create settleAbnormalTrack failed, settleAbnormalTrack:{}, cause:{}", track, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.abnormal.track.create.fail");
        }
    }


    @Override
    public Response<Boolean> updateSettleAbnormalTrack(SettleAbnormalTrack settleAbnormalTrack) {
        try {
            return Response.ok(settleAbnormalTrackDao.update(settleAbnormalTrack));
        } catch (Exception e) {
            log.error("update settleAbnormalTrack failed, settleAbnormalTrack:{}, cause:{}", settleAbnormalTrack, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.abnormal.track.update.fail");
        }
    }

    @Override
    public Response<Boolean> deleteSettleAbnormalTrackById(Long settleAbnormalTrackId) {
        try {
            return Response.ok(settleAbnormalTrackDao.delete(settleAbnormalTrackId));
        } catch (Exception e) {
            log.error("delete settleAbnormalTrack failed, settleAbnormalTrackId:{}, cause:{}", settleAbnormalTrackId, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.abnormal.track.delete.fail");
        }
    }
}
