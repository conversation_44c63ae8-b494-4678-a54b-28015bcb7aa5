package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.impl.dao.SettlementDao;
import moonstone.settle.impl.event.SettlementUpdateEvent;
import moonstone.settle.impl.manager.SettleManager;
import moonstone.settle.model.PayChannelDetail;
import moonstone.settle.model.Settlement;
import moonstone.settle.service.SettlementWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * Code generated by terminus code gen
 * Desc: 写服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class SettlementWriteServiceImpl implements SettlementWriteService {

    @Autowired
    private SettlementDao settlementDao;

    @Autowired
    private SettleManager settleManager;

    @EventListener(SettlementUpdateEvent.class)
    public void updateRecpAccount(SettlementUpdateEvent settlementUpdateEvent) {
        if (settlementUpdateEvent == null || settlementUpdateEvent.getUpdate() == null) {
            return;
        }
        Settlement settlement = new Settlement();
        settlement.setId(settlementUpdateEvent.getUpdate().getId());
        settlement.setShopId(settlementUpdateEvent.getUpdate().getShopId());
        settlement.setRecpAccount(settlementUpdateEvent.getUpdate().getRecpAccount());
        settlement.setShipmentAt(settlementUpdateEvent.getUpdate().getShipmentAt());
        settlement.setDepotNames(settlementUpdateEvent.getUpdate().getDepotNames());
        settlement.setSupplierNames(settlementUpdateEvent.getUpdate().getSupplierNames());
        settlementDao.update(settlement);
    }

    @Override
    public Response<Long> createOrUpdateSettlement(Settlement settlement) {
        try {
            settleManager.createOrUpdateSettlement(settlement);
            return Response.ok(settlement.getId());
        } catch (Exception e) {
            log.error("create settlement failed, settlement:{}, cause:{}", settlement, Throwables.getStackTraceAsString(e));
            return Response.fail("settlement.create.fail");
        }
    }

    @Override
    public Response<Boolean> createSettlementAndPayChannelDetail(Settlement settlement, PayChannelDetail detail) {
        try {
            settleManager.createOrUpdateSettlement(settlement, detail);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("create settlement and pay channel detail failed, settlement:{},detail:{}, cause:{}", settlement, detail, Throwables.getStackTraceAsString(e));
            return Response.fail("settlement.and.pay.channel.detail.create.fail");
        }
    }

    @Override
    public Response<Boolean> updateSettlement(Settlement settlement) {
        try {
            return Response.ok(settlementDao.update(settlement));
        } catch (Exception e) {
            log.error("update settlement failed, settlement:{}, cause:{}", settlement, Throwables.getStackTraceAsString(e));
            return Response.fail("settlement.update.fail");
        }
    }

    @Override
    public Response<Boolean> deleteSettlementById(Long settlementId) {
        try {
            return Response.ok(settlementDao.delete(settlementId));
        } catch (Exception e) {
            log.error("delete settlement failed, settlementId:{}, cause:{}", settlementId, Throwables.getStackTraceAsString(e));
            return Response.fail("settlement.delete.fail");
        }
    }
}
