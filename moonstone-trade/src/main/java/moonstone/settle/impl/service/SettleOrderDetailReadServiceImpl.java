package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OrderPaymentDao;
import moonstone.order.impl.dao.PaymentDao;
import moonstone.order.impl.dao.ShopOrderDao;
import moonstone.order.impl.dao.SkuOrderDao;
import moonstone.settle.dto.paging.SettleOrderDetailCriteria;
import moonstone.settle.impl.dao.SettleOrderDetailDao;
import moonstone.settle.model.PlatformTradeDailySummary;
import moonstone.settle.model.SellerTradeDailySummary;
import moonstone.settle.model.SettleOrderDetail;
import moonstone.settle.service.SettleOrderDetailReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: 读服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class SettleOrderDetailReadServiceImpl implements SettleOrderDetailReadService {

    private final SettleOrderDetailDao settleOrderDetailDao;

    private final PaymentDao paymentDao;

    private final OrderPaymentDao orderPaymentDao;

    private final SkuOrderDao skuOrderDao;

    private final ShopOrderDao shopOrderDao;

    @Autowired
    public SettleOrderDetailReadServiceImpl(SettleOrderDetailDao settleOrderDetailDao, PaymentDao paymentDao, OrderPaymentDao orderPaymentDao, SkuOrderDao skuOrderDao, ShopOrderDao shopOrderDao) {
        this.settleOrderDetailDao = settleOrderDetailDao;
        this.paymentDao = paymentDao;
        this.orderPaymentDao = orderPaymentDao;
        this.skuOrderDao = skuOrderDao;
        this.shopOrderDao = shopOrderDao;
    }

    @Override
    public Response<SettleOrderDetail> findSettleOrderDetailById(Long settleOrderDetailId) {
        try {
            return Response.ok(settleOrderDetailDao.findById(settleOrderDetailId));
        } catch (Exception e) {
            log.error("find settleOrderDetail by id failed, settleOrderDetailId:{}, cause:{}", settleOrderDetailId, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.detail.find.fail");
        }
    }

    @Override
    public Response<SettleOrderDetail> findSettleOrderDetailByOrderId(Long orderId, Integer orderLevel) {
        try{
            SettleOrderDetail settleOrderDetail=settleOrderDetailDao.findByShopOrderId(orderId);
            if(settleOrderDetail==null){
                log.error("findSettleOrderDetailByOrderId fail, orderId={}, orderLevel={}, cause={}",
                        orderId, orderLevel, "settle.order.detail.not.exist");
                return Response.fail("settle.order.detail.not.exist");
            }
            return Response.ok(settleOrderDetail);
        }catch (Exception e){
            log.error("findSettleOrderDetailByOrderId fail, orderId={}, orderLevel={}, cause={}", orderId, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.detail.find.fail");
        }
    }

    @Override
    public Response<Paging<SettleOrderDetail>> pagingSettleOrderDetails(SettleOrderDetailCriteria criteria) {
        try{

            return Response.ok(settleOrderDetailDao.paging(criteria.toMap()));
        }catch (Exception e){
            log.error("pagingSettleOrderDetails fail, param={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.details.paging.fail");
        }
    }


    @Override
    public Response<List<SellerTradeDailySummary>> generateSellerTradeDailySummary(Date startAt,Date endAt) {

        try {
            return Response.ok(settleOrderDetailDao.generateSellerTradeDailySummary(startAt, endAt));

        }catch (Exception e){
            log.error("generate settlement sum of daily seller fail cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("order.generate.settlement.sum.seller.daily.fail");
        }
    }


    @Override
    public Response<PlatformTradeDailySummary> generatePlatformTradeDailySummary(Date startAt,Date endAt) {

        try {

            return Response.ok(settleOrderDetailDao.generatePlatformTradeDailySummary(startAt, endAt));

        }catch (Exception e){
            log.error("generate settlement sum of daily platform fail cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("order.generate.settlement.sum.platform.daily.fail");
        }
    }
}
