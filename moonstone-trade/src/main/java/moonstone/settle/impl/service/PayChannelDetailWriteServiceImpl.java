package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.impl.dao.PayChannelDetailDao;
import moonstone.settle.impl.manager.SettleManager;
import moonstone.settle.model.PayChannelDetail;
import moonstone.settle.service.PayChannelDetailWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Code generated by terminus code gen
 * Desc: 写服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class PayChannelDetailWriteServiceImpl implements PayChannelDetailWriteService {

    private final PayChannelDetailDao payChannelDetailDao;

    private final SettleManager settleManager;

    @Autowired
    public PayChannelDetailWriteServiceImpl(PayChannelDetailDao payChannelDetailDao, SettleManager settleManager) {
        this.payChannelDetailDao = payChannelDetailDao;
        this.settleManager = settleManager;
    }

    @Override
    public Response<Long> createPayChannelDetail(PayChannelDetail payChannelDetail) {
        try {
            settleManager.createOrUpdatePayChannelDetail(payChannelDetail);
            return Response.ok(payChannelDetail.getId());
        } catch (Exception e) {
            log.error("create payChannelDetail failed, payChannelDetail:{}, cause:{}", payChannelDetail, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.detail.create.fail");
        }
    }

    @Override
    public Response<Boolean> updatePayChannelDetail(PayChannelDetail payChannelDetail) {
        try {
            return Response.ok(payChannelDetailDao.update(payChannelDetail));
        } catch (Exception e) {
            log.error("update payChannelDetail failed, payChannelDetail:{}, cause:{}", payChannelDetail, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.detail.update.fail");
        }
    }

    @Override
    public Response<Boolean> deletePayChannelDetailById(Long payChannelDetailId) {
        try {
            return Response.ok(payChannelDetailDao.delete(payChannelDetailId));
        } catch (Exception e) {
            log.error("delete payChannelDetail failed, payChannelDetailId:{}, cause:{}", payChannelDetailId, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.detail.delete.fail");
        }
    }
}
