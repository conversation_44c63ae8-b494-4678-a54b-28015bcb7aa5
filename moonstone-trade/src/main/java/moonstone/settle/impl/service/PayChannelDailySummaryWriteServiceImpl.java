package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.api.SummaryRule;
import moonstone.settle.enums.TradeType;
import moonstone.settle.impl.dao.PayChannelDailySummaryDao;
import moonstone.settle.impl.dao.PayChannelDetailDao;
import moonstone.settle.impl.manager.SettleManager;
import moonstone.settle.model.PayChannelDailySummary;
import moonstone.settle.service.PayChannelDailySummaryWriteService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: 写服务实现类
 * Date: 2016-07-24
 */
@Slf4j
@Service
@RpcProvider
public class PayChannelDailySummaryWriteServiceImpl implements PayChannelDailySummaryWriteService {

    private final PayChannelDailySummaryDao payChannelDailySummaryDao;

    private final PayChannelDetailDao payChannelDetailDao;

    private final SettleManager settleManager;

    private final SummaryRule summaryRule;

    @Autowired
    public PayChannelDailySummaryWriteServiceImpl(PayChannelDailySummaryDao payChannelDailySummaryDao, PayChannelDetailDao payChannelDetailDao, SettleManager settleManager, SummaryRule summaryRule) {
        this.payChannelDailySummaryDao = payChannelDailySummaryDao;
        this.payChannelDetailDao = payChannelDetailDao;
        this.settleManager = settleManager;
        this.summaryRule = summaryRule;
    }

    @Override
    public Response<Long> createPayChannelDailySummary(PayChannelDailySummary payChannelDailySummary) {
        try {
            payChannelDailySummaryDao.create(payChannelDailySummary);
            return Response.ok(payChannelDailySummary.getId());
        } catch (Exception e) {
            log.error("create payChannelDailySummary failed, payChannelDailySummary:{}, cause:{}", payChannelDailySummary, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.daily.summary.create.fail");
        }
    }

    @Override
    public Response<Boolean> updatePayChannelDailySummary(PayChannelDailySummary payChannelDailySummary) {
        try {
            return Response.ok(payChannelDailySummaryDao.update(payChannelDailySummary));
        } catch (Exception e) {
            log.error("update payChannelDailySummary failed, payChannelDailySummary:{}, cause:{}", payChannelDailySummary, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.daily.summary.update.fail");
        }
    }

    @Override
    public Response<Boolean> deletePayChannelDailySummaryById(Long payChannelDailySummaryId) {
        try {
            return Response.ok(payChannelDailySummaryDao.delete(payChannelDailySummaryId));
        } catch (Exception e) {
            log.error("delete payChannelDailySummary failed, payChannelDailySummaryId:{}, cause:{}", payChannelDailySummaryId, Throwables.getStackTraceAsString(e));
            return Response.fail("pay.channel.daily.summary.delete.fail");
        }
    }

    @Override
    public Response<Boolean> generatePayChannelDailySummary(Date sumAt) {
        try{
            Date startAt = sumAt;
            Date endAt = new DateTime(sumAt.getTime()).plusDays(1).toDate();

            List<PayChannelDailySummary> forward=payChannelDetailDao.generatePayChannelDetail(startAt, endAt, TradeType.Pay.value());
            List<PayChannelDailySummary> backward=payChannelDetailDao.generatePayChannelDetail(startAt, endAt, TradeType.Refund.value());

            List<PayChannelDailySummary> merged = summaryRule.channelDaily(forward, backward);
            for(PayChannelDailySummary summary : merged){
                summary.setSumAt(sumAt);
            }

            settleManager.batchCreateOrUpdatePayChannelDailySummary(merged);

            return Response.ok(Boolean.TRUE);
        }catch (Exception e){
            log.error("generatePayChannelDailySummary fail, sumAt={}, cause={}", sumAt , Throwables.getStackTraceAsString(e));
            return Response.fail("generate.pay.channel.daily.summary.fail");
        }
    }
}
