package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.component.PaymentAccountUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.impl.dao.*;
import moonstone.order.model.*;
import moonstone.settle.dto.RefundEntry;
import moonstone.settle.dto.SettleOrderAndRefundDetail;
import moonstone.settle.dto.SettleTrade;
import moonstone.settle.dto.TradeCriteria;
import moonstone.settle.enums.PayType;
import moonstone.settle.enums.RefundLevelType;
import moonstone.settle.impl.dao.SettleOrderDetailDao;
import moonstone.settle.impl.dao.SettleRefundOrderDetailDao;
import moonstone.settle.service.SettleRichOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * DATE: 16/8/8 下午5:03 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@Service
@RpcProvider
public class SettleRichOrderReadServiceImpl implements SettleRichOrderReadService {

    private final RefundDao refundDao;

    private final PaymentDao paymentDao;

    private final SkuOrderDao skuOrderDao;

    private final ShopOrderDao shopOrderDao;

    private final OrderRefundDao orderRefundDao;

    private final OrderPaymentDao orderPaymentDao;

    private final OrderShipmentDao orderShipmentDao;

    private final SettleOrderDetailDao settleOrderDetailDao;

    private final SettleRefundOrderDetailDao refundOrderDetailDao;

    @Autowired
    public SettleRichOrderReadServiceImpl(RefundDao refundDao,
                                          PaymentDao paymentDao,
                                          SkuOrderDao skuOrderDao,
                                          ShopOrderDao shopOrderDao,
                                          OrderRefundDao orderRefundDao,
                                          OrderPaymentDao orderPaymentDao,
                                          OrderShipmentDao orderShipmentDao,
                                          SettleOrderDetailDao settleOrderDetailDao,
                                          SettleRefundOrderDetailDao refundOrderDetailDao) {
        this.refundDao = refundDao;
        this.paymentDao = paymentDao;
        this.skuOrderDao = skuOrderDao;
        this.shopOrderDao = shopOrderDao;
        this.orderRefundDao = orderRefundDao;
        this.orderPaymentDao = orderPaymentDao;
        this.orderShipmentDao = orderShipmentDao;
        this.settleOrderDetailDao = settleOrderDetailDao;
        this.refundOrderDetailDao = refundOrderDetailDao;
    }

    @Override
    public Response<SettleTrade> findBy(TradeCriteria criteria) {
        try{
            SettleTrade trade = null;
            if(criteria.getPaymentId()!=null){
                trade = buildByPaymentId(criteria.getPaymentId());
            }
            if(criteria.getRefundId()!=null){
                trade = buildByRefundId(criteria.getRefundId());
            }
            return Response.ok(trade);
        }catch (Exception e){
            log.error("findBy TradeCriteria fail, criteria={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("find.by.trade.criteria.fail");
        }
    }

    @Override
    public Response<SettleOrderAndRefundDetail> findByShopOrderId(Long shopOrderId) {
        try{
            SettleOrderAndRefundDetail orderAndRefundDetail = new SettleOrderAndRefundDetail();
            orderAndRefundDetail.setOrderDetail(settleOrderDetailDao.findByShopOrderId(shopOrderId));
            orderAndRefundDetail.setRefundOrderDetailList(refundOrderDetailDao.findByShopOrderId(shopOrderId));
            return Response.ok(orderAndRefundDetail);
        }catch (Exception e){
            log.error("find detail by shopOrderId fail, shopOrderId={}, cause={}",
                    shopOrderId, Throwables.getStackTraceAsString(e));
            return Response.fail("find.detail.by.shop.order.id.fail");
        }
    }

    protected SettleTrade buildByPaymentId(Long paymentId){
        SettleTrade trade = new SettleTrade();

        //payment
        Payment payment = paymentDao.findById(paymentId);
        if(payment.getPaidAt() == null){ //如果支付单并未成功, 则报错
            log.error("payment status invalid to settle, payment={}",payment);
            throw new ServiceException("payment.status.invalid");
        }

        List<OrderPayment> orderPaymentList = orderPaymentDao.findByPaymentId(paymentId);

        if(payment.getStage()>0){//分阶段支付
            trade.setPayType(PayType.StagePay.value());
            OrderPayment orderPayment = orderPaymentList.get(0);
            List<OrderPayment> allStages = orderPaymentDao.findByOrderIdAndOrderType(
                    orderPayment.getOrderId(), orderPayment.getOrderType());
            for(OrderPayment stage : allStages){
                Payment currentPayment = paymentDao.findById(stage.getPaymentId());
                if(Objects.equals(currentPayment.getStatus(), 1)){ //只有支付成功的订单才返回
                    trade.getPaymentByIdMap().put(stage.getPaymentId(), currentPayment);
                }
            }
        }else {
            if(orderPaymentList.size()>1){ //合并支付
                trade.setPayType(PayType.MergePay.value());
            }else{ //普通支付
                trade.setPayType(PayType.NormalPay.value());
            }
            trade.getPaymentByIdMap().put(paymentId, payment);
        }

        //shopOrder and skuOrder, 支付只能在订单级别.
        for(OrderPayment orderPayment : orderPaymentList){
            trade.getShopOrderByIdMap().put(orderPayment.getOrderId(), shopOrderDao.findById(orderPayment.getOrderId()));
            trade.getSkuOrderList().addAll(skuOrderDao.findByOrderId(orderPayment.getOrderId()));
        }

        //shipment
        for(ShopOrder shopOrder : trade.getShopOrderByIdMap().values()){
            List<OrderShipment> shopOrderShipments = orderShipmentDao.findByOrderIdAndOrderType(shopOrder.getId(), OrderLevel.SHOP.getValue());
            trade.getShopOrderShipmentList().addAll(shopOrderShipments);

            Map<Long,List<SkuOrder>> skuOrderGroups = trade.getSkuOrdersGroupByShopOrderId();
            for(SkuOrder skuOrder : skuOrderGroups.get(shopOrder.getId())){
                List<OrderShipment> skuOrderShipments = orderShipmentDao.findByOrderIdAndOrderType(skuOrder.getId(), OrderLevel.SKU.getValue());
                trade.getSkuOrderShipmentList().addAll(skuOrderShipments);
            }
        }

        //shop refund
        for(ShopOrder shopOrder : trade.getShopOrderByIdMap().values()){ //查询是否存在店铺退款
            List<OrderRefund> orderRefunds = orderRefundDao.findByOrderIdAndOrderType(shopOrder.getId(), OrderLevel.SHOP);
            if(orderRefunds.isEmpty()){
                continue;
            }
            for(OrderRefund orderRefund : orderRefunds){
                Refund refund = refundDao.findById(orderRefund.getRefundId());
                if(! refund.getStatus().equals(OrderStatus.REFUND.getValue())){ //只有退款完成的订单才返回
                    continue;
                }
                RefundEntry refundEntry = new RefundEntry();
                refundEntry.setRefund(refund);
                refundEntry.setPayment(trade.getPaymentByIdMap().get(refund.getPaymentId()));
                refundEntry.setShopOrder(shopOrder);
                if(trade.getPayType().equals(PayType.StagePay.value())){
                    refundEntry.setRefundType(RefundLevelType.StagePerShopOrder.value());
                }else{
                    refundEntry.setRefundType(RefundLevelType.PerShopOrder.value());
                }
                trade.getRefundByIdMap().put(refund.getId(), refundEntry);
            }
        }

        //sku refund
        List<OrderRefund> allRefunds = new ArrayList<>();
        for(SkuOrder skuOrder : trade.getSkuOrderList()){ //查询是否存在子订单退款
            allRefunds.addAll(orderRefundDao.findByOrderIdAndOrderType(skuOrder.getId(), OrderLevel.SKU));
        }
        Map<Long, List<OrderRefund>>  groups = allRefunds.stream().collect(Collectors.groupingBy(OrderRefund::getRefundId));
        for(Long refundId : groups.keySet()){
            RefundEntry refundEntry = new RefundEntry();

            Refund refund = refundDao.findById(refundId);
            if(! refund.getStatus().equals(OrderStatus.REFUND.getValue())){ //只有退款完成的订单才返回
                continue;
            }
            refundEntry.setRefund(refund);
            refundEntry.setPayment(paymentDao.findById(refund.getPaymentId()));

            List<OrderRefund> orderRefundList = groups.get(refundId).stream().distinct().collect(Collectors.toList());
            if(orderRefundList.size()>1){ //多个sku退款
                List<SkuOrder>  skuOrderList = orderRefundList.stream()
                        .map(x-> skuOrderDao.findById(x.getOrderId()))
                        .collect(Collectors.toList());
                refundEntry.setSkuOrderList(skuOrderList);
                refundEntry.setShopOrder(shopOrderDao.findById(skuOrderList.get(0).getOrderId()));
                refundEntry.setRefundType(RefundLevelType.MultiSkuOrder.value());
            }else{ //单个sku退款
                OrderRefund orderRefund = orderRefundList.get(0);
                SkuOrder skuOrder = skuOrderDao.findById(orderRefund.getOrderId());
                refundEntry.setRefundType(RefundLevelType.PerSkuOrder.value());
                refundEntry.setSkuOrder(skuOrder);
                refundEntry.setShopOrder(shopOrderDao.findById(skuOrder.getOrderId()));
            }
            trade.getRefundByIdMap().put(refundId, refundEntry);
        }

        trade.getPaymentByIdMap().values().forEach(this::normalize);
        trade.getRefundByIdMap().values().forEach(x-> normalize(x.getRefund()));
        trade.getShopOrderByIdMap().values().forEach(this::normalize);
        trade.getSkuOrderList().forEach(this::normalize);
        return trade;
    }


    /**
     * 规范化数据 名字以后再改
     *
     * @param payment
     */
    protected void normalize(Payment payment) {
        if (payment.getDiscount() == null) {
            payment.setDiscount(0);
        }
        if (payment.getRecpAccount() == null || payment.getRecpAccount().isEmpty()) {
            PaymentAccountUtil.genRecpAccount(payment.getPayRequest()).ifPresent(payment::setRecpAccount);
        }
    }
    protected void normalize(Refund refund){
        if(refund.getDiffFee()==null){
            refund.setDiffFee(0L);
        }
    }

    protected void normalize(ShopOrder shopOrder){
        if(shopOrder.getDiscount()==null){
            shopOrder.setDiscount(0);
        }
        if(shopOrder.getDiffFee()==null){
            shopOrder.setDiffFee(0);
        }
    }
    protected void normalize(SkuOrder skuOrder){
        if(skuOrder.getDiscount()==null){
            skuOrder.setDiscount(0L);
        }
        if(skuOrder.getDiffFee()==null){
            skuOrder.setDiffFee(0);
        }
    }

    private SettleTrade buildByRefundId(Long refundId){
        Refund refund = refundDao.findById(refundId);
        return buildByPaymentId(refund.getPaymentId());
    }


}
