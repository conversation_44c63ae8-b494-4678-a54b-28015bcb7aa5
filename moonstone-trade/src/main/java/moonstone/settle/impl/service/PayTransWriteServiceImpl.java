package moonstone.settle.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.impl.dao.AlipayTransDao;
import moonstone.settle.impl.dao.KjtpayTransDao;
import moonstone.settle.impl.dao.UnionpayTransDao;
import moonstone.settle.impl.dao.WechatpayTransDao;
import moonstone.settle.model.AlipayTrans;
import moonstone.settle.model.KjtpayTrans;
import moonstone.settle.model.UnionpayTrans;
import moonstone.settle.model.WechatpayTrans;
import moonstone.settle.service.PayTransWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static io.terminus.common.utils.Arguments.isNull;

/**
 * DATE: 16/7/25 上午11:06 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Service
@Slf4j
@RpcProvider
public class PayTransWriteServiceImpl implements PayTransWriteService {

    private final AlipayTransDao alipayTransDao;
    private final WechatpayTransDao wechatpayTransDao;
    private final UnionpayTransDao unionpayTransDao;
    private final KjtpayTransDao kjtpayTransDao;

    @Autowired
    public PayTransWriteServiceImpl(AlipayTransDao alipayTransDao, WechatpayTransDao wechatpayTransDao, UnionpayTransDao unionpayTransDao, KjtpayTransDao kjtpayTransDao) {
        this.alipayTransDao = alipayTransDao;
        this.wechatpayTransDao = wechatpayTransDao;
        this.unionpayTransDao = unionpayTransDao;
        this.kjtpayTransDao = kjtpayTransDao;
    }

    @Override
    public Response<Long> createAlipayTrans(AlipayTrans trans) {
        Response<Long> result = new Response<Long>();

        try {
            alipayTransDao.createNonexists(trans);
            result.setResult(trans.getId());
        }catch (Exception e){
            log.error("create alipay trans fail,cause: {}", Throwables.getStackTraceAsString(e));
            result.setError("create.alipay.trans.fail");
        }
        return result;
    }

    @Override
    public Response<Boolean> createWechatPayTrans(WechatpayTrans trans) {
        Response<Boolean> result = new Response<Boolean>();

        try {
            wechatpayTransDao.createNotExists(trans);
            result.setResult(Boolean.TRUE);

        }catch (Exception e){
            log.error("create wechat pay trans fail,cause: {}",Throwables.getStackTraceAsString(e));
            result.setError("create.wechat.pay.trans.fail");
        }
        return result;
    }

    @Override
    public Response<Boolean> createUnionpayTrans(UnionpayTrans trans) {
        Response<Boolean> result = new Response<Boolean>();

        try {
            if(isNull(unionpayTransDao.findByQueryId(trans.getQueryId()))){
                unionpayTransDao.create(trans);
            }
            result.setResult(Boolean.TRUE);

        }catch (Exception e){
            log.error("create unionpay trans fail,cause: {}",Throwables.getStackTraceAsString(e));
            result.setError("create.unionpay.trans.fail");

        }
        return result;
    }

    @Override
    public Response<Boolean> createKjtpayTrans(KjtpayTrans trans) {
        Response<Boolean> result = new Response<Boolean>();

        try {

            if(isNull(kjtpayTransDao.findByInnerNo(trans.getInnerNo()))){
                kjtpayTransDao.create(trans);
            }
            result.setResult(Boolean.TRUE);

        }catch (Exception e){
            log.error("create kjtpay trans fail,cause: {}",Throwables.getStackTraceAsString(e));
            result.setError("create.kjtpay.trans.fail");

        }
        return result;
    }

}
