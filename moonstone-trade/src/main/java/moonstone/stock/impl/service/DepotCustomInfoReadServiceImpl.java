package moonstone.stock.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.DepotCustomInfo;
import moonstone.stock.impl.dao.DepotCustomInfoDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RpcProvider
public class DepotCustomInfoReadServiceImpl implements DepotCustomInfoReadService {
    @Autowired
    private DepotCustomInfoDao depotCustomInfoDao;

    @Override
    public Response<DepotCustomInfo> findByDepotCode(String DepotCode) {
        try {
            return Response.ok(depotCustomInfoDao.findByDepotCode(DepotCode));
        } catch (Exception ex) {
            log.error("[DepotCustom](query) fail to findByDepotCode data:{}", DepotCode);
            return Response.fail("fail.find.depotCustomInfo");
        }
    }

    @Override
    public Response<DepotCustomInfo> findById(Long id) {
        try {
            return Response.ok(depotCustomInfoDao.findById(id));
        } catch (Exception ex) {
            log.error("[DepotCustom](query) fail to findById data:{}", id);
            return Response.fail("fail.find.depotCustomInfo");
        }
    }

    @Override
    public Response<List<DepotCustomInfo>> findByIds(List<Long> ids) {
        try {
            return Response.ok(depotCustomInfoDao.findByIds(ids));
        } catch (Exception ex) {
            log.error("[DepotCustom](query) fail to findByIds data:{}", ids);
            return Response.fail("fail.find.depotCustomInfo");
        }
    }

    @Override
    public Response<List<DepotCustomInfo>> findAll() {
        try {
            return Response.ok(depotCustomInfoDao.findAll());
        } catch (Exception ex) {
            log.error("[DepotCustom](query) fail to findAll");
            return Response.fail("fail.find.depotCustomInfo");
        }
    }
}
