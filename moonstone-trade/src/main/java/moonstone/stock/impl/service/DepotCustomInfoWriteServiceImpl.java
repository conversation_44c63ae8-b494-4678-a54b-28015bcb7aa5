package moonstone.stock.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.DepotCustomInfo;
import moonstone.stock.impl.dao.DepotCustomInfoDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DepotCustomInfoWriteServiceImpl implements DepotCustomInfoWriteService {
    @Autowired
    private DepotCustomInfoDao depotCustomInfoDao;

    @Override
    public Response<Long> create(DepotCustomInfo depotCustomInfo) {
        try {
            if (depotCustomInfoDao.create(depotCustomInfo)) {
                return Response.ok(depotCustomInfo.getId());
            }
        } catch (Exception ex) {
            log.error("fail.create.depotCustomInfo {}", depotCustomInfo);
            ex.printStackTrace();
        }
        return Response.fail("fail.create.depotCustomInfo");
    }

    @Override
    public Response<Boolean> update(DepotCustomInfo depotCustomInfo) {
        try {
            if (depotCustomInfoDao.update(depotCustomInfo)) {
                return Response.ok();
            }
            return Response.fail("fail.update.depotCustomInfo");
        } catch (Exception ex) {
            log.error("fail.update.depotCustomInfo {}", depotCustomInfo);
            ex.printStackTrace();
            return Response.fail("fail.update.depotCustomInfo");
        }
    }

    @Override
    public Response<Boolean> delete(Long id) {
        try {
            if (depotCustomInfoDao.delete(id)) {
                return Response.ok();
            }
            return Response.fail("fail.delete.depotCustomInfo");
        } catch (Exception ex) {
            log.error("fail.delete.depotCustomInfo id:{}", id);
            ex.printStackTrace();
            return Response.fail("fail.delete.depotCustomInfo");
        }
    }
}
