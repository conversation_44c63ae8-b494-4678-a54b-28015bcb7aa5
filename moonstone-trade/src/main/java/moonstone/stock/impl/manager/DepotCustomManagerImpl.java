package moonstone.stock.impl.manager;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Sku;
import moonstone.order.model.DepotCustomInfo;
import moonstone.order.model.OrderBase;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderReadService;
import moonstone.stock.StockChooser;
import moonstone.stock.impl.service.DepotCustomInfoReadService;
import moonstone.stock.impl.service.DepotCustomInfoWriteService;
import moonstone.stock.impl.service.DepotCustomManager;
import moonstone.thirdParty.enums.ThirdPartyUserShopExtraIndex;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.ThirdPartyJobService;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;


/**
 * <AUTHOR>
 * 仓库获取海关代码信息管理器
 */
@Service
@Slf4j
public class DepotCustomManagerImpl implements DepotCustomManager {
    private final static Cache<Long, LocalDateTime> lastSyncFIFO = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(1000).build();
    @Getter
    private final Map<String, CustomCode> customCodeMapByName = new LinkedHashMap<>();
    @Autowired
    private DepotCustomInfoWriteService depotCustomInfoWriteService;
    @Autowired
    private DepotCustomInfoReadService depotCustomInfoReadService;
    /**
     * 关区编码对应关区信息,因为不常更新,由于错误频繁, 缩短缓存寿命
     */
    LoadingCache<String, String> customCodeCacheByDepotCode = Caffeine.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES)
            .build(this::findAndCacheDepotCode);
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private StockChooser stockChooser;
    @Autowired
    private ThirdPartyJobService thirdPartyJobService;
    @Autowired
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @Autowired
    private ThirdPartyUserShopReadService thirdPartyUserShopReadService;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private MongoTemplate mongoTemplate;

    private String queryCustomCodeBySku(Long skuId) {
        var stock = getValidateSkuStock(skuCacheHolder.findSkuById(skuId), true);
        log.info("DepotCustomManagerImpl.queryCustomCodeBySku, skuId={}, found thirdPartySkuStock={}", skuId, JSON.toJSONString(stock));

        var result = customCodeCacheByDepotCode.get(stock.getDepotCode());
        log.info("DepotCustomManagerImpl.queryCustomCodeBySku, skuId={}, found depotCode={}, result customCode={}",
                skuId, stock.getDepotCode(), result);

        return result;
    }

    @PostConstruct
    public void registerCustomCode() {
        // 进行初始关区名称注册
        for (originCustomConfig c : originCustomConfig.values()) {
            /// 找到对应值 设置对应编码 返回
            customCodeMapByName.put(c.name(), new CustomCode(c.getCode(), c.getName()));
        }
        for (CustomCode customCode : mongoTemplate.findAll(CustomCode.class)) {
            customCodeMapByName.put(customCode.name, customCode);
        }
    }

    /**
     * 由仓库编码获取海关代号
     *
     * @param depotCode 仓库编码
     * @return 海关信息
     */
    private String findAndCacheDepotCode(String depotCode) {
        if (VirtualDepot.GongXiao.getCode().equals(depotCode)) {
            return originCustomConfig.CHONGQING.getCode();
        }
        DepotCustomInfo depotCustomInfo = depotCustomInfoReadService.findByDepotCode(depotCode).getResult();
        if (depotCustomInfo != null && StringUtils.hasText(depotCustomInfo.getCustomCode()) && StringUtils.hasText(depotCustomInfo.getDepotCode())) {
            log.info("DepotCustomManagerImpl.findAndCacheDepotCode, depotCode={}, depotCustomInfoReadService.findByDepotCode={}",
                    depotCode, JSON.toJSONString(depotCustomInfo));
            return depotCustomInfo.getCustomCode();
        }
        throw Translate.exceptionOf("仓库编码[%s]匹配关区失败, 请手动配置", depotCode);
    }

    /**
     * 由主订单查询子订单获取单品信息的仓库编码以查询海关信息
     * > 由拆单逻辑保证主订单与子订单的海关信息一致
     *
     * @param shopOrder 订单
     * @return 关区编号
     * see moonstone.web.core.component.order.OrderTaxSplitManager 拆单逻辑
     */
    @Override
    public String getCustomCodeByOrder(ShopOrder shopOrder) {
        return Optional.ofNullable(shopOrder)
                .map(OrderBase::getId)
                .map(skuOrderReadService::findByShopOrderId)
                .map(Response::getResult)
                .stream()
                .flatMap(Collection::stream)
                .findFirst()
                .map(this::getCustomCodeByOrder)
                .orElseGet(() -> {
                    log.warn("{} shopOrder [Id => {}] can't query depotCode", LogUtil.getClassMethodName(), Optional.ofNullable(shopOrder).map(ShopOrder::getId).orElse(null));
                    return "HANGZHOU";
                });
    }

    @Override
    public void registerCustom(String code, String name) {
        mongoTemplate.insert(new CustomCode(code, name));
        getCustomCodeMapByName().put(name, new CustomCode(code, name));
    }

    @Override
    public void registerDepotCustomInfo(String depotCode, String customCode) {
        if (depotCode == null) {
            return;
        }
        DepotCustomInfo depotCustomInfo = new DepotCustomInfo();
        depotCustomInfo.setCustomCode(customCode);
        depotCustomInfo.setDepotCode(depotCode);
        for (CustomCode value : getCustomCodeMapByName().values()) {
            if (Objects.equals(value.getCode(), customCode)) {
                depotCustomInfo.setCustomName(value.name);
            }
        }
        depotCustomInfoWriteService.create(depotCustomInfo);
        customCodeCacheByDepotCode.invalidate(depotCode);
    }

    /**
     * getCustomCodeByOrder 由SkuOrder获取海关信息
     *
     * @param skuOrder 单品订单信息
     * @return 关区编码
     */
    public String getCustomCodeByOrder(SkuOrder skuOrder) {
        // 如果设置了手动关区则使用手动的
        if (Optional.ofNullable(skuOrder.getExtra()).map(extra -> extra.get(CustomBySkuExtra.custom.name())).filter(StringUtils::hasLength).isPresent()) {
            return skuOrder.getExtra().get(CustomBySkuExtra.custom.name());
        }
        // 只有第三方商品有关区
        boolean notBondedItemOrCustomNotQueryAbleItem = !BondedType.fromInt(skuOrder.getIsBonded()).isBonded()
                ;
        if (notBondedItemOrCustomNotQueryAbleItem) {
            return "NO_CUSTOM";
        }
        if (Optional.of(skuOrder).map(SkuOrder::getIsThirdPartyItem).filter(Predicate.isEqual(1)).isEmpty()) {
            log.error("{} skuOrder[id => {}] error", LogUtil.getClassMethodName(), Optional.of(skuOrder).map(OrderBase::getId).orElse(null));
            return null;
        }
        // 试图预判关区
        long cost = System.currentTimeMillis();
        try {
            try {
                // 可能找不到库存了
                String custom = queryCustomCodeBySku(skuOrder.getSkuId());
                log.info("shopOrderId={}, 由SkuOrder获取海关信息 queryCustomCodeBySku 结果为={}", skuOrder.getOrderId(), custom);
                if (custom != null) {
                    return custom;
                }
            } catch (Exception e) {
                log.error("NO STOCK FOUND", e);
                // 特殊店铺使用杭州关区
                if (skuOrder.getShopId() == 264L || skuOrder.getShopId() == 263L) {
                    return "HANGZHOU";
                }
            }
            // 试图直接从SKU-ID预读关区
            for (originCustomConfig customConfig : originCustomConfig.values()) {
                if (skuOrder.getOuterSkuId().startsWith(customConfig.getShortName())
                        && customConfig.getShortName().length() > 0) {
                    log.info("shopOrderId={}, 图直接从OuterSkuId预读关区, MATCH {} -> {}", skuOrder.getOrderId(), skuOrder.getOuterSkuId(), customConfig);
                    return customConfig.getCode();
                }
            }
            // extract the pushSystem from Sku
            ThirdPartySkuStock stock = getValidateSkuStock(skuCacheHolder.findSkuById(skuOrder.getSkuId()), true);
            // 使用最短的单号
            String depotName = Optional.ofNullable(stock.getDepotName()).orElse("上海");
            log.info("{} Trying extract the Custom Information from DepotName[{}]", LogUtil.getClassMethodName(), depotName);
            // match the name
            for (CustomCode customCode : customCodeMapByName.values()) {
                String name = customCode.getName();
                if (customCode.getName().endsWith("海关")) {
                    name = name.substring(0, name.indexOf("海关"));
                }
                if (depotName.contains(name)) {
                    if (stock.getDepotCode() != null) {
                        registerDepotCustomInfo(stock.getDepotCode(), customCode.getCode());
                        customCodeCacheByDepotCode.invalidate(stock.getDepotCode());
                    }
                    log.info("shopOrderId={}, 试图从库存数据中的depotName={}来匹配关区, 结果={}", skuOrder.getOrderId(), depotName, JSON.toJSONString(customCode));
                    return customCode.getCode();
                }
            }
            return CUSTOM_NOT_FOUND;
        } catch (Exception ex) {
            log.error("getCustomCodeByOrder find custom for shopOrderId={} failed", skuOrder.getOrderId(), ex);
            var defaultHangZhou = Lists.newArrayList(264L, 263L);
            if (defaultHangZhou.contains(skuOrder.getShopId())) {
                log.error("shopOrderId={}, 于异常代码块中返回默认的关区=HANGZHOU", skuOrder.getOrderId());
                return "HANGZHOU";
            }

            // 试图直接从SKU-ID预读关区
            for (originCustomConfig customConfig : originCustomConfig.values()) {
                if (skuOrder.getOuterSkuId().startsWith(customConfig.getShortName())
                        && customConfig.getShortName().length() > 0) {
                    log.error("异常代码块中, shopOrderId={}, 图直接从OuterSkuId预读关区, MATCH {} -> {}", skuOrder.getOrderId(), skuOrder.getOuterSkuId(), customConfig);
                    return customConfig.getCode();
                }
            }

            var byUserShop = findDefaultCustomsByShop(skuOrder.getShopId());
            if (StringUtils.hasText(byUserShop)) {
                log.error("shopOrderId={}, 于异常代码块中从 parana_third_party_user_shop 返回关区={}", skuOrder.getOrderId(), byUserShop);
                return byUserShop;
            }

            log.error("shopOrderId={}, 于异常代码块中返回默认的关区=HANGZHOU", skuOrder.getOrderId());
            return "HANGZHOU";
        } finally {
            cost = System.currentTimeMillis() - cost;
            if (cost > 200)
                log.debug("Depot Code Query Cost a lot -> {}", cost);
        }
    }

    /**
     * 从 parana_third_party_user_shop 里读一下关区， 兜底用
     *
     * @param shopId
     * @return
     */
    private String findDefaultCustomsByShop(Long shopId) {
        var result = thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(
                ThirdPartySystem.Y800_V2.Id(), shopId).getResult();
        if (result == null) {
            return null;
        }

        var extra = result.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            return null;
        }

        return extra.get(ThirdPartyUserShopExtraIndex.DEFAULT_CUSTOMS.getCode());
    }

    /**
     * 获取单品可用的库存信息
     * 如果获取失败, 则刷新库存信息 然后再重试
     *
     * @param sku      单品
     * @param firstTry 第一次尝试
     * @return 库存信息
     */
    private ThirdPartySkuStock getValidateSkuStock(Sku sku, boolean firstTry) {
        /// 初始化虚拟实体
        Map<String, String> skuTagsMap = Optional.ofNullable(sku.getTags()).orElseGet(Collections::emptyMap);
        int skuThirdPartySystem = Integer.parseInt(skuTagsMap.get(SkuTagIndex.pushSystem.name()).split(",")[0]);
        Either<ThirdPartySkuStock> stockEither = stockChooser.judge(sku).filter(sku,
                Optional.ofNullable(thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(sku.getShopId(), skuThirdPartySystem, sku.getOuterSkuId()).getResult())
                        .orElseThrow(() -> Translate.exceptionOf("查询单品[Id => %s]库存信息失败", sku.getId())));

        return !firstTry ? stockEither.take() : stockEither.orElseGet(() -> {
            syncStock(sku, skuThirdPartySystem);
            return getValidateSkuStock(sku, false);
        });
    }

    /**
     * 同步单品的库存信息
     * wont's sync any time, only sync one shop at one minutes possibly
     *
     * @param sku                 单品信息
     * @param skuThirdPartySystem 第三方系统信息
     */
    public void syncStock(Sku sku, int skuThirdPartySystem) {
        // not sync if the it has sync in one min pass
        // give a initial time that has longer than time
        LocalDateTime lastSync = Objects.requireNonNull(lastSyncFIFO.get(sku.getShopId(), shopId -> LocalDateTime.now().minusMinutes(5)));
        if (lastSync.isAfter(LocalDateTime.now().minusMinutes(1))) {
            return;
        }
        ThirdPartyUserShop thirdPartyUserShop = thirdPartyUserShopReadService.findByShopId(sku.getShopId()).getResult()
                .stream()
                .filter(account -> account.getStatus() > 0 && skuThirdPartySystem == account.getThirdPartyId())
                .findFirst().orElse(null);
        try {
            Future<Boolean> syncFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    log.warn("{} sync stock for sku[Id => {}, outerSkuId => {}, system => {}] fail to find stock", LogUtil.getClassMethodName(), sku.getId(), sku.getOuterSkuId(), ThirdPartySystem.fromInt(skuThirdPartySystem).name());
                    thirdPartyJobService.synchronize(ThirdPartySystem.fromInt(skuThirdPartySystem), thirdPartyUserShop);
                } catch (Exception ignore) {
                }
                return true;
            });
            try {
                // 5 秒超时时间, 库存通常同步不应该超过5秒
                syncFuture.get(5, TimeUnit.MILLISECONDS);
            } catch (Exception ex) {
                log.warn("{} sync timeout [{}]", LogUtil.getClassMethodName(), ex);
            }
        } catch (Exception ex) {
            log.error("{} fail to sync stock from DepotCustomManager fail to find validate sku[Id => {}] stock", LogUtil.getClassMethodName(), sku.getId(), ex);
        } finally {
            lastSyncFIFO.put(sku.getShopId(), LocalDateTime.now());
        }
    }

    @Override
    public void deleteDepotCustomInfo(String depotCode) {
        try {
            Long id = depotCustomInfoReadService.findByDepotCode(depotCode).getResult().getId();
            depotCustomInfoWriteService.delete(id);
        } finally {
            customCodeCacheByDepotCode.invalidate(depotCode);
        }
    }
}
