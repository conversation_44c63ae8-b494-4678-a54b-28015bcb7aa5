package moonstone.stock.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.DepotCustomInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class DepotCustomInfoDao extends MyBatisDao<DepotCustomInfo> {
    public DepotCustomInfo findByDepotCode(String depotCode) {
        return getSqlSession().selectOne(sqlId("findByDepotCode"), ImmutableMap.of("depotCode", depotCode));
    }

    public List<DepotCustomInfo> findAll() {
        return getSqlSession().selectList(sqlId("findAll"));
    }
}
