package moonstone.stock.impl.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.DepotCustomInfo;
import moonstone.stock.impl.service.DepotCustomInfoReadService;
import moonstone.stock.impl.service.DepotCustomInfoWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Deprecated
@Service
@RpcProvider
@Slf4j
public class CustomQueryUtils {
    @Autowired
    private DepotCustomInfoReadService depotCustomInfoReadService;
    @Autowired
    private DepotCustomInfoWriteService depotCustomInfoWriteService;
    @Value("${Y800.yang.support}")
    private String Y800_SUPPORT_URL;

    /// ## queryByDepotCode 由仓库编号查找海关信息
    public Response<Custom> queryByDepotCode(String depotCode) {
        String url = Y800_SUPPORT_URL + "/xhr/depot/getCus?depotCode=" + depotCode;
        log.info("[custom-query](http-get) query Y800 Support,url:{},depotCode:{}", url, depotCode);
        HttpRequest httpRequest = HttpRequest.get(url);

        try {
            if (httpRequest.ok()) {
                String data = httpRequest.body();
                JSONObject jsonObject = JSON.parseObject(data);
                if ("success".equals(jsonObject.getString("code"))) {
                    /// **注意** JSONObject后,所有可解析字符串全部被解析为JSONObject,而不是一部分或者一层
                    Custom custom = new Custom(jsonObject.getJSONObject("data"));
                    //Custom custom = JSON.parseObject(jsonObject.getString("data"), Custom.class);
                    return Response.ok(custom);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("[custom-query](http-error) query Y800 Support error,depotCode:{},cause:{}", depotCode, ex.getMessage());
        }
        return Response.fail("获取海关信息失败");
    }

    /// ## getDepotCustomInfoByDepotCode 从仓库编号获取新的仓库海关实体
    public Response<DepotCustomInfo> getDepotCustomInfoByDepotCode(String depotCode) {
        Response<Custom> customResponse = queryByDepotCode(depotCode);
        if (customResponse.isSuccess()) {
            DepotCustomInfo depotCustomInfo = new DepotCustomInfo();
            Custom custom = customResponse.getResult();
            depotCustomInfo.setCustomName(custom.getName());
            depotCustomInfo.setCustomCode(custom.getCode());
            depotCustomInfo.setDepotCode(depotCode);
            return Response.ok(depotCustomInfo);
        } else {
            return Response.fail(customResponse.getError());
        }
    }

    @Data
    public class Custom {
        String code;
        String name;
        Custom(){

        }
        Custom(JSONObject jsonObject)
        {
            code=jsonObject.getString("code");
            name=jsonObject.getString("name");
        }
    }
}
