package moonstone.express.impl.service;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.express.impl.dao.ExpressCompanyDao;
import moonstone.express.model.ExpressCompany;
import moonstone.express.service.ExpressCompanyWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Date: 6/23/16
 * Time: 12:12 PM
 * Author: 2016年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Slf4j
@Service
@RpcProvider
public class ExpressCompanyWriteServiceImpl implements ExpressCompanyWriteService {
    private final ExpressCompanyDao expressCompanyDao;

    @Autowired
    public ExpressCompanyWriteServiceImpl(ExpressCompanyDao expressCompanyDao) {
        this.expressCompanyDao = expressCompanyDao;
    }

    /**
     * 创建快递公司
     *
     * @param expressCompany 快递公司信息
     * @return 成功返回记录的id
     */
    @Override
    public Response<Long> create(ExpressCompany expressCompany) {
        try {
            if (!StringUtils.hasText(expressCompany.getCode()) || !StringUtils.hasText(expressCompany.getName())) {
                log.error("express company code or name cannot be blank, got:{}", expressCompany);
                return Response.fail("express.info.uncompleted");
            }

            // 查重
            ExpressCompany exist = expressCompanyDao.findByCode(expressCompany.getCode());
            if (exist != null) {
                log.error("express company code already exist:{}", expressCompany);
                return Response.fail("express.code.duplicate");
            }

            expressCompanyDao.create(expressCompany);
            return Response.ok(expressCompany.getId());
        } catch (Exception e) {
            log.error("fail to create express company:{}, cause:{}", expressCompany, Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.create.fail");
        }
    }

    /**
     * 更新快递公司
     *
     * @param expressCompany 快递公司信息
     * @return 成功返回记录的id
     */
    @Override
    public Response<Long> update(ExpressCompany expressCompany) {
        try {
            if (expressCompany.getId() == null) {
                log.error("fail to update express company:{}, id is null", expressCompany);
                return Response.fail("express.company.id.is.null");
            }

            if (StringUtils.hasText(expressCompany.getCode())) {
                ExpressCompany exist = expressCompanyDao.findByCode(expressCompany.getCode());
                if (exist != null && !Objects.equal(exist.getId(), expressCompany.getId())) {
                    log.error("express company code already exist:{}", expressCompany);
                    return Response.fail("express.code.duplicate");
                }
            }

            expressCompanyDao.update(expressCompany);
            return Response.ok(expressCompany.getId());
        } catch (Exception e) {
            log.error("fail to update express company:{}, cause:{}", expressCompany, Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.update.fail");
        }
    }

    /**
     * 删除快递公司
     *
     * @param id 快递公司记录的id
     * @return 成功返回记录的id
     */
    @Override
    public Response<Long> delete(Long id) {
        try {
            if (id == null) {
                log.error("fail to delete express company, id is null");
                return Response.fail("express.company.id.is.null");
            }

            Boolean success = expressCompanyDao.delete(id);
            if (!success) {
                log.warn("delete express company by id fail, cause: not exist");
            }
            return Response.ok(id);
        } catch (Exception e) {
            log.error("fail to update express company(id={}), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.delete.fail");
        }
    }
}
