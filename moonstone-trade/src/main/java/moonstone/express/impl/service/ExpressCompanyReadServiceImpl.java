package moonstone.express.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.express.impl.dao.ExpressCompanyDao;
import moonstone.express.model.ExpressCompany;
import moonstone.express.service.ExpressCompanyReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 5/27/16.
 */
@Slf4j
@Service
@RpcProvider
public class ExpressCompanyReadServiceImpl implements ExpressCompanyReadService {

    @Autowired
    private ExpressCompanyDao expressCompanyDao;

    @Override
    public Response<List<ExpressCompany>> listActiveExpressCompanies() {
        try {
            List<ExpressCompany> expressCompanies = expressCompanyDao.findActive();
            return Response.ok(expressCompanies);
        } catch (Exception e) {
            log.error("fail to list active express companies, cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.find.fail");
        }
    }

    @Override
    public Response<List<ExpressCompany>> findExpressCompanyByFuzzyName(String fuzzyName) {
        try {
            if (!StringUtils.hasText(fuzzyName)) {
                return Response.ok(Collections.emptyList());
            }

            List<ExpressCompany> expressCompanies = expressCompanyDao.findByFuzzyName(fuzzyName);
            return Response.ok(expressCompanies);
        } catch (Exception e) {
            log.error("fail to find express company by fuzzyName:{}, cause:{}", fuzzyName, Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.find.fail");
        }
    }

    @Override
    public Response<ExpressCompany> findExpressCompanyByCode(String code) {
        try {
            ExpressCompany expressCompany = expressCompanyDao.findByCode(code);
            if (expressCompany == null) {
                log.error("express company not found where code={}", code);
                return Response.fail("express.company.not.found");
            }
            return Response.ok(expressCompany);
        } catch (Exception e) {
            log.error("fail to find express company by code:{},cause:{}", code, Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.find.fail");
        }
    }

    /**
     * 分页查找物流公司
     *
     * @param pageNo   当前页
     * @param pageSize 每页数量
     * @return 分页的物流公司信息
     */
    @Override
    public Response<Paging<ExpressCompany>> paging(Integer pageNo, Integer pageSize, String name, String code, Integer status) {
        try {
            PageInfo page = PageInfo.of(pageNo, pageSize);

            Map<String, Object> params = Maps.newHashMap();
            if (StringUtils.hasText(code)) {
                params.put("code", code);
            }
            if (StringUtils.hasText(name)) {
                params.put("name", name);
            }
            if (null != status && ExpressCompany.Status.from(status) != null) {
                params.put("status", status);
            }

            return Response.ok(expressCompanyDao.paging(page.getOffset(), page.getLimit(), params));
        } catch (Exception e) {
            log.error("fail to paging express company by pageNo:{}, pageSize:{}, cause:{}", pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail("express.company.find.fail");
        }
    }
}
