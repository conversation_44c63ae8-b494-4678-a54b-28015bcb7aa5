/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.promotion.model.Promotion;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 营销活动dao
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-30
 */
@Repository
public class PromotionDao extends MyBatisDao<Promotion> {

    /**
     * 根据店铺id和状态查找对应的营销活动列表
     *
     * @param shopId 店铺id
     * @param status 活动状态
     * @return 营销活动列表
     */
    public List<Promotion> findByShopIdAndStatus(Long shopId, Integer status) {
        return getSqlSession().selectList(sqlId("findByShopIdAndStatus"),
                ImmutableMap.of("shopId", shopId, "status", status));
    }

    /**
     * 根据店铺id和状态列表查找对应的营销活动列表
     *
     * @param shopId   店铺id
     * @param statuses 活动状态列表
     * @return 营销活动列表
     */
    public List<Promotion> findByShopIdAndStatuses(Long shopId, List<Integer> statuses) {
        return getSqlSession().selectList(sqlId("findByShopIdAndStatuses"),
                ImmutableMap.of("shopId", shopId, "statuses", statuses));
    }

    public List<Promotion> findByShopIdAndStatuses(Long shopId, Integer type, List<Integer> statuses) {
        return getSqlSession().selectList(sqlId("findByShopIdAndStatuses"),
                ImmutableMap.of("shopId", shopId,
                        "type", type,
                        "statuses", statuses));
    }

    public List<Promotion> findByShopIdAndStatuses(Long shopId, Integer type, List<Integer> statuses, Integer issueStatus) {
        return getSqlSession().selectList(sqlId("findByShopIdAndStatuses"),
                ImmutableMap.of("shopId", shopId,
                        "type", type,
                        "statuses", statuses,
                        "issueStatus", issueStatus));
    }

    /**
     * 根据条件统计营销活动
     *
     * @param shopId         店铺id
     * @param promotionDefId 营销活动定义id
     * @param statuses       活动状态
     * @return 统计数目
     */
    public long countBy(Long shopId, Long promotionDefId, List<Integer> statuses) {
        return getSqlSession().selectOne(sqlId("countBy"),
                ImmutableMap.of("shopId", shopId, "promotionDefId", promotionDefId, "statuses", statuses));
    }

    /**
     * 将已发布且当前时间处于有效期的活动设为进行中
     */
    public void setOngoing() {
        getSqlSession().update(sqlId("setOngoing"));
    }

    /**
     * 将状态为进行中但当前时间已不在有效期的活动设为已过期
     */
    public void setExpire() {
        getSqlSession().update(sqlId("setExpire"));
    }
}
