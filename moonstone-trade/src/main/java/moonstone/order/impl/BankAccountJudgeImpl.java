package moonstone.order.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.ParanaDefaultThreadFactory;
import moonstone.common.utils.Translate;
import moonstone.order.api.BankAccountJudge;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.*;

@Component
@Slf4j
public class BankAccountJudgeImpl implements BankAccountJudge {
    private final Map<String, BankType> bankTypeMap = new ConcurrentHashMap<>();
    private Map<String, String> codeMapName = new HashMap<>();
    private final Map<String, BankType> codeMapBank = new ConcurrentHashMap<>();
    @Autowired
    private MongoTemplate mongoTemplate;
    ExecutorService executorService = Executors.newCachedThreadPool(new ParanaDefaultThreadFactory("bankAccountJudge"));

    @PostConstruct
    public void init() {
        codeMapName = readStream(getClass().getResourceAsStream("/bank.prefix")).map(ByteArrayOutputStream::toString)
                .map(json -> JSON.parseObject(json, new TypeReference<Map<String, String>>() {
                })).orElse(new HashMap<>());
        if (codeMapName.isEmpty()) {
            log.info("{} empty bankName map? check the resouce bank.prefix please,url:{}", LogUtil.getClassMethodName(), getClass().getResource("/bank.prefix"));
        }
    }

    @PreDestroy
    public void close() throws Exception {
        Thread.sleep(5000L);
        executorService.shutdownNow();
        log.info("{} streamRead executor closed,status:{}", LogUtil.getClassMethodName(), executorService.isShutdown());
    }

    /**
     * 从支付宝查询银行信息
     *
     * @param account 银行卡号
     * @return 成功查询的银行信息
     */
    private Either<BankType> queryFromAlipay(String account) {
        try {
            String url = String.format("https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo=%s&cardBinCheck=true", account);
            HttpRequest request = HttpRequest.get(url);
            if (!request.ok())
                throw new RuntimeException(new Translate("Http访问错误,请检查网关或者地址:%s", url).toString());
            String body = request.body();
            log.debug("{} decode body:{} for account:{}", LogUtil.getClassMethodName(), body, account);
            BankQueryResult result = JSON.parseObject(body, BankQueryResult.class);
            if (!result.isValidated()) {
                log.error("{} read bank type failed:{} account:{}", LogUtil.getClassMethodName(), result, account);
                return Either.error(new Translate(result.getMessages() == null || result.getMessages().length == 0 ? "未知错误" : result.getMessages()[0]).toString());
            }
            // query one from database or init one
            BankType bankType = getBankType(result.getBank());
            // add prefix
            bankType.getPrefix().add(account.substring(0, 6));
            // register the bank
            bankTypeMap.putIfAbsent(account.substring(0, 6), bankType);
            codeMapBank.putIfAbsent(bankType.getCode(), bankType);
            updateBank(bankType);
            return Either.ok(bankType);
        } catch (Exception ex) {
            log.error("{} query failed cause:{} account:{}", LogUtil.getClassMethodName(), ex.getMessage(), account);
            ex.printStackTrace();
            return Either.error(ex, new Translate("查询失败").toString());
        }
    }

    /**
     * 更新数据进入mongodb数据库
     *
     * @param bankType 银行信息
     */
    private void updateBank(BankType bankType) {
        if (mongoTemplate.count(Query.query(Criteria.where("code").is(bankType.getCode())), BankType.class) > 0) {
            Update updateEntity = Update.update("code", bankType.getCode())
                    .set("prefix", bankType.getPrefix());
            mongoTemplate.updateFirst(Query.query(Criteria.where("code").is(bankType.getCode())), updateEntity, BankType.class);
        } else {
            mongoTemplate.insert(bankType);
        }
    }

    /**
     * 通过帐号判断其属于哪个银行
     * 至少6位帐号
     *
     * @param account 帐号
     */
    public BankType judge(String account) {
        if (account == null || account.isEmpty())
            return UNKNOW;
        if (account.length() < 6)
            return UNKNOW;
        String prefix = account.substring(0, 6);
        return Optional.ofNullable(bankTypeMap.get(prefix)).orElseGet(() -> queryFromAlipay(account).orElse(UNKNOW));
    }

    /**
     * 从数据库中读取一个数据或者默认返回一个
     *
     * @param code 银行代号
     */
    private BankType getBankType(String code) {
        BankType bankType = codeMapBank.get(code);
        if (bankType == null) {
            bankType = mongoTemplate.findOne(new Query(Criteria.where("code").is(code)), BankType.class);
            if (bankType != null) {
                bankType.setPrefix(Collections.synchronizedSet(bankType.getPrefix()));
            } else {
                bankType = new BankType();
                bankType.setCode(code);
                bankType.setName(readNameByCode(code));
                bankType.setPrefix(Collections.synchronizedSet(new HashSet<>()));
                bankType.setImgUrl(String.format("https://apimg.alipay.com/combo.png?d=cashier&t=%s", bankType.getCode()));
                encodeImg(bankType).ifSuccess(bankType::setImg);
            }
        }
        return bankType;
    }

    /**
     * 读取银行的图片信息写为base64转码的数据
     *
     * @param bankType 银行信息
     * @return 成功转码后的图片
     * @see Base64
     */
    private Either<String> encodeImg(BankType bankType) {
        if (bankType.getImgUrl() == null || bankType.getImgUrl().isEmpty()) {
            bankType.setImgUrl(String.format("https://apimg.alipay.com/combo.png?d=cashier&t=%s", bankType.getCode()));
        }
        String url = bankType.getImgUrl();
        try {
            HttpRequest request = HttpRequest.get(url);
            if (!request.ok())
                throw new RuntimeException(new Translate("Http访问错误,请检查网关或者地址:%s", url).toString());
            return readStream(request.stream()).map(ByteArrayOutputStream::toByteArray).map(Base64.getEncoder()::encodeToString);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} get img bank:{}", LogUtil.getClassMethodName(), JSON.toJSON(bankType));
            return Either.error(ex);
        }
    }

    /**
     * 将有限的inputStream读取为byteArrayOutPutStream 以便于处理
     * (3秒钟timeOut)
     *
     * @param stream inputStream必须要为有限流,若是无限流则会陷入死循环
     */
    private Either<ByteArrayOutputStream> readStream(InputStream stream) {
        Future<Either<ByteArrayOutputStream>> timeOutWatcher = executorService.submit(() -> {
            try {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] rom = new byte[1024];
                while (true) {
                    int size = stream.read(rom);
                    if (size <= 0)
                        break;
                    byteArrayOutputStream.write(rom, 0, size);
                }
                return Either.ok(byteArrayOutputStream);
            } catch (Exception ex) {
                log.error("{} failed to read stream,cause:{}", LogUtil.getClassMethodName(), ex.getMessage());
                return Either.error(ex);
            }
        });
        try {
            return timeOutWatcher.get(10, TimeUnit.SECONDS);
        } catch (TimeoutException ex) {
            ex.getStackTrace();
            return Either.error(ex, new Translate("流读取超时,请检查流的来源").toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Data
    public static class BankQueryResult {
        // 发卡行代码
        String bank;
        // 卡片类型
        String cardType;
        // 卡号
        String key;
        // 不知道是什么东西
        String[] messages;
        // 查询状态(目前只以ok为基准)
        String stat;
        // 是否验证成功
        boolean validated;
    }

    /**
     * 从配置文件中读取银行名字进行
     *
     * @param code 代号
     */
    private String readNameByCode(String code) {
        return codeMapName.getOrDefault(code, new Translate("银行").toString());
    }
}
