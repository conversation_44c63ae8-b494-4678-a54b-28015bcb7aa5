package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.WithDrawProfitApplyCriteria;
import moonstone.order.impl.dao.WithDrawProfitApplyDao;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.service.WithDrawProfitApplyReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@RpcProvider
@Slf4j
public class WithDrawProfitApplyReadServiceImpl implements WithDrawProfitApplyReadService {
    @Autowired
    private WithDrawProfitApplyDao withDrawProfitApplyDao;
    private final static String FAIL_FIND_ENTITY = "fail.find.withdrawProfitApply";

    @Override
    public Response<WithDrawProfitApply> findByPaySerialId(String paySerialId) {
        try {
            return Response.ok(withDrawProfitApplyDao.findByPaySerialId(paySerialId));
        } catch (Exception ex) {
            log.error("[WithDrawProfitApplyService](read) findByPaySerialId failed by {}", paySerialId);
            ex.printStackTrace();
            return Response.fail("fail.find.withDrawProfitApply");
        }
    }

    @Override
    public Response<List<WithDrawProfitApply>> findByUserIdAndSourceId(long userId, long sourceId) {
        try {
            return Response.ok(withDrawProfitApplyDao.findByUserIdAndSourceId(userId, sourceId));
        } catch (Exception ex) {
            log.error("{} userId:{}", LogUtil.getClassMethodName("findByUserID"), userId);
            ex.printStackTrace();
            return Response.fail(FAIL_FIND_ENTITY);
        }
    }

    @Override
    public Response<List<WithDrawProfitApply>> findByUserId(long userId) {
        try {
            return Response.ok(withDrawProfitApplyDao.findByUserId(userId));
        } catch (Exception ex) {
            log.error("[WithDrawProfitApplyService](read) findByUserId failed by {}", userId);
            ex.printStackTrace();
            return Response.fail("fail.find.withDrawProfitApply");
        }
    }

    @Override
    public Response<WithDrawProfitApply> findById(Long id) {
        try {
            return Response.ok(withDrawProfitApplyDao.findById(id));

        } catch (Exception ex) {
            log.error("[WithDrawProfitApplyService](read) findById failed by {}", id);
            ex.printStackTrace();
            return Response.fail("fail.find.withDrawProfitApply");
        }
    }

    @Override
    public Response<List<WithDrawProfitApply>> findByIds(List<Long> ids) {
        try {
            return Response.ok(withDrawProfitApplyDao.findByIds(ids));

        } catch (Exception ex) {
            log.error("[WithDrawProfitApplyService](read) findByIds failed by {}", ids);
            ex.printStackTrace();
            return Response.fail("fail.find.withDrawProfitApply");
        }
    }

    @Override
    public Either<List<WithDrawProfitApply>> getRequireQuery(Long sourceId, Integer limit) {
        try {
            return Either.ok(withDrawProfitApplyDao.getRequireQuery(sourceId, limit));
        } catch (Exception exception) {
            return Either.error(exception);
        }
    }

    @Override
    public long getPendingWithdrawalRequestCount(Long shopId, Long subUserId) {
        return withDrawProfitApplyDao.getPendingWithdrawalRequestCount(shopId, subUserId);
    }

    @Override
    public Response<Paging<WithDrawProfitApply>> paging(WithDrawProfitApplyCriteria criteria) {
        try {
            if (Objects.nonNull(criteria.getIds()) && criteria.getIds().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            if (Objects.nonNull(criteria.getUserIds()) && criteria.getUserIds().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            if (Objects.nonNull(criteria.getBitmasks()) && criteria.getBitmasks().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            if (Objects.nonNull(criteria.getNotBitMasks()) && criteria.getNotBitMasks().isEmpty()) {
                criteria.setNotBitMasks(Collections.singletonList(-99999));
            }
            log.info("criteria info msg:{}",criteria.toMap());
            return Response.ok(withDrawProfitApplyDao.paging(criteria.toMap()));
        } catch (Exception ex) {
            log.error("[WithDrawProfitApplyService](read) paging failed by {} cause:{}", criteria.toString(), ex.getMessage(), ex);
            ex.printStackTrace();
            return Response.fail("fail.find.withDrawProfitApply");
        }
    }

    @Override
    public Response<List<WithDrawProfitApply>> pageList(WithDrawProfitApplyCriteria criteria) {
        try {
            if (Objects.nonNull(criteria.getIds()) && criteria.getIds().isEmpty()) {
                return Response.ok(Collections.emptyList());
            }
            if (Objects.nonNull(criteria.getUserIds()) && criteria.getUserIds().isEmpty()) {
                return Response.ok(Collections.emptyList());
            }
            if (Objects.nonNull(criteria.getBitmasks()) && criteria.getBitmasks().isEmpty()) {
                return Response.ok(Collections.emptyList());
            }
            if (Objects.nonNull(criteria.getNotBitMasks()) && criteria.getNotBitMasks().isEmpty()) {
                criteria.setNotBitMasks(Collections.singletonList(-99999));
            }

            return Response.ok(withDrawProfitApplyDao.pageList(criteria.toMap()));
        } catch (Exception ex) {
            log.error("WithDrawProfitApplyReadServiceImpl.pageList failed by {} cause:{}", criteria.toString(), ex.getMessage(), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Paging<WithDrawProfitApply>> pagingBySearch(WithDrawProfitApplyCriteria criteria) {
        try {
            if (Objects.nonNull(criteria.getIds()) && criteria.getIds().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            if (Objects.nonNull(criteria.getUserIds()) && criteria.getUserIds().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            if (Objects.nonNull(criteria.getBitmasks()) && criteria.getBitmasks().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            if (Objects.nonNull(criteria.getNotBitMasks()) && criteria.getNotBitMasks().isEmpty()) {
                criteria.setNotBitMasks(Collections.singletonList(-99999));
            }
            log.info("criteria info msg:{}",criteria.toMap());
            return Response.ok(withDrawProfitApplyDao.getBySearch(criteria.toMap()));
        } catch (Exception ex) {
            log.error("[WithDrawProfitApplyService](read) paging failed by {} cause:{}", criteria.toString(), ex.getMessage(), ex);
            ex.printStackTrace();
            return Response.fail("fail.find.withDrawProfitApply");
        }
    }
}
