package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AccountStatementWithdrawStatusEnum;
import moonstone.order.impl.dao.AccountStatementWithdrawRelationDao;
import moonstone.order.model.AccountStatementWithdrawRelation;
import moonstone.order.service.AccountStatementWithdrawRelationWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class AccountStatementWithdrawRelationWriteServiceImpl implements AccountStatementWithdrawRelationWriteService {

    @Autowired
    private AccountStatementWithdrawRelationDao accountStatementWithdrawRelationDao;

    @Override
    public Response<Boolean> batchInsert(List<AccountStatementWithdrawRelation> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return Response.ok(true);
            }

            if (accountStatementWithdrawRelationDao.batchInsert(list) != list.size()) {
                return Response.fail("记录插入数量不对");
            }

            return Response.ok(true);
        } catch (Exception ex) {
            log.error("AccountStatementWithdrawRelationWriteServiceImpl.batchInsert error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateWithdrawStatus(Long withdrawApplyId, AccountStatementWithdrawStatusEnum sourceStatus,
                                                  AccountStatementWithdrawStatusEnum targetStatus) {
        try {
            if (withdrawApplyId == null || sourceStatus == null || targetStatus == null) {
                return Response.fail("参数缺失");
            }

            return Response.ok(accountStatementWithdrawRelationDao.updateWithdrawStatus(withdrawApplyId,
                    sourceStatus.getCode(), targetStatus.getCode()) > 0);
        } catch (Exception ex) {
            log.error("AccountStatementWithdrawRelationWriteServiceImpl.updateWithdrawStatus error, withdrawApplyId={}, sourceStatus={}, targetStatus={}",
                    withdrawApplyId, sourceStatus.getCode(), targetStatus.getCode(), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
