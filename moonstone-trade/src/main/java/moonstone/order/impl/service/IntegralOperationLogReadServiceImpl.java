package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.service.IntegralOperationLogReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/2 20:05
 */
@Slf4j
@Service
@RpcProvider
public class IntegralOperationLogReadServiceImpl implements IntegralOperationLogReadService {
    @Autowired
    private MongoTemplate mongoTemplate;
}
