/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.component.PaymentAccountUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.enu.PaymentExtraIndexEnum;
import moonstone.order.impl.dao.PaymentDao;
import moonstone.order.impl.manager.PaymentManager;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.Payment;
import moonstone.order.service.PaymentWriteService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 支付单写服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-11
 */
@Service
@Slf4j
@RpcProvider
public class PaymentWriteServiceImpl implements PaymentWriteService {

    private final PaymentManager paymentManager;

    private final PaymentDao paymentDao;

    @Autowired
    public PaymentWriteServiceImpl(PaymentManager paymentManager,
                                   PaymentDao paymentDao) {
        this.paymentManager = paymentManager;
        this.paymentDao = paymentDao;
    }

    /**
     * 创建支付单, 同时也会创建相应的订单和支付单的关联关系
     *
     * @param payment    支付单信息
     * @param orderIds   关联的(子)订单id列表
     * @param orderLevel 订单对应的级别
     * @return 支付单id
     */
    @Override
    public Response<Long> create(Payment payment, List<Long> orderIds, OrderLevel orderLevel) {
        try {
            payment.setStatus(MoreObjects.firstNonNull(payment.getStatus(), OrderStatus.NOT_PAID.getValue()));
            List<OrderPayment> orderPayments = Lists.newArrayListWithCapacity(orderIds.size());
            for (Long orderId : orderIds) {
                OrderPayment orderPayment = new OrderPayment();
                orderPayment.setOrderId(orderId);
                orderPayment.setOrderLevel(orderLevel);
                orderPayment.setStatus(payment.getStatus());
                orderPayments.add(orderPayment);
            }
            Long paymentId = paymentManager.create(payment, orderPayments);
            return Response.ok(paymentId);
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", payment, Throwables.getStackTraceAsString(e));
            return Response.fail("payment.create.fail");
        }
    }

    /**
     * 更新支付单的支付流水号和状态
     *
     * @param paymentId   支付单id
     * @param paySerialNo 支付流水号
     * @param paidAt      支付成功时间
     * @param status      支付的状态
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> payCallback(Long paymentId, String paySerialNo, Date paidAt, Integer status, String payResponse) {
        try {
            paymentManager.payCallback(paymentId, paySerialNo, paidAt, status, payResponse);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update payment(id={}) to paySerialNo={}, status={}, cause:{}",
                    paymentId, paySerialNo, status, Throwables.getStackTraceAsString(e));
            return Response.fail("payment.update.fail");
        }
    }

    /**
     * 更新支付单的状态,同时也会更新和订单关联表记录的状态
     *
     * @param paymentId 支付单id
     * @param status    状态
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateStatus(Long paymentId, Integer status) {
        try {
            paymentManager.updateStatus(paymentId, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update payment(id={}) to status={}, cause:{}",
                    paymentId, status, Throwables.getStackTraceAsString(e));
            return Response.fail("payment.update.fail");
        }
    }

    @Override
    public Response<Boolean> updatePushStatus(Long paymentId, PaymentPushStatus pushStatus) {
        try {
            if (paymentId == null || pushStatus == null) {
                return Response.fail("入参皆不能为空");
            }

            Payment update = new Payment();
            update.setId(paymentId);
            update.setPushStatus(pushStatus.getValue());

            return Response.ok(paymentDao.update(update));
        } catch (Exception ex) {
            log.error("PaymentWriteServiceImpl.updatePushStatus error, paymentId={}, pushStatus={}",
                    paymentId, pushStatus, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> delete(Long paymentId) {
        try {
            boolean success = paymentDao.delete(paymentId);
            return Response.ok(success);
        } catch (Exception e) {
            log.error("fail to delete payment(id={}), cause:{}",
                    paymentId, Throwables.getStackTraceAsString(e));
            return Response.fail("payment.delete.fail");
        }
    }

    @Override
    public Response<Boolean> update(Payment payment) {
        if (!ObjectUtils.isEmpty(payment.getPayRequest())) {
            PaymentAccountUtil.genRecpAccount(payment.getPayRequest()).ifPresent(payment::setRecpAccount);
        }
        return Response.ok(paymentDao.update(payment));
    }

    @Override
    public Response<Boolean> updateAllInPayCusId(String outId, String cusId) {
        try {
            if (StringUtils.isBlank(outId) || StringUtils.isBlank(cusId)) {
                return Response.fail("入参缺失");
            }

            Payment payment = paymentDao.findByOutId(outId);
            if (payment == null) {
                return Response.fail("指定支付单不存在");
            }

            var extra = payment.getExtra();
            if (CollectionUtils.isEmpty(extra)) {
                extra = new HashMap<>();
            }
            extra.put(PaymentExtraIndexEnum.allInPayCusId.getCode(), cusId);

            var update = new Payment();
            update.setId(payment.getId());
            update.setExtra(extra);
            return Response.ok(paymentDao.update(update));
        } catch (Exception ex) {
            log.error("PaymentWriteServiceImpl.updateAllInPayCusId error, outId={}, cusId={}", outId, cusId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> pushedWaitCallback(Long paymentId) {
        try {
            if (paymentId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(paymentDao.pushedWaitCallback(paymentId));
        } catch (Exception ex) {
            log.error("PaymentWriteServiceImpl.pushedWaitCallback error, paymentId={}", paymentId, ex);
            return Response.fail(ex.getMessage());
        }
    }

}
