package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.EnterpriseWithdrawDetailDao;
import moonstone.order.model.EnterpriseWithdrawDetail;
import moonstone.order.service.EnterpriseWithdrawDetailWriteService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class EnterpriseWithdrawDetailWriteServiceImpl implements EnterpriseWithdrawDetailWriteService {

    @Resource
    private EnterpriseWithdrawDetailDao enterpriseWithdrawDetailDao;

    @Override
    public Response<Boolean> creates(List<EnterpriseWithdrawDetail> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(enterpriseWithdrawDetailDao.creates(list) == list.size());
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawDetailWriteServiceImpl.creates error, ", ex);
            return Response.fail(ex.getMessage());
        }
    }
}
