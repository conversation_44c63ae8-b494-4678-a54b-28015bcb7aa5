package moonstone.order.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemSnapShotCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.impl.dao.*;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.cache.UserCacheHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Mail: <EMAIL>
 * Data: 16/3/3
 * Author: yangzefeng
 */
@Service
@Slf4j
public record SkuOrderReadServiceImpl(SkuOrderDao skuOrderDao,
                                      FeeViewDao feeViewDao,
                                      ImageInfoDao imageInfoDao,
                                      ExtraViewDao extraViewDao,
                                      ProfitViewDao profitViewDao,
                                      ShopCacheHolder shopCacheHolder,
                                      UserCacheHolder userCacheHolder,
                                      ItemSnapShotCacheHolder itemSnapShotCacheHolder,
                                      DepotViewDao depotViewDao
) implements SkuOrderReadService {

    @Override
    public Long findOrderIdById(Long orderId) {
        return skuOrderDao.findOrderIdById(orderId);
    }

    @Override
    public Long findUserIdByOrderId(Long orderId, Integer orderType) {
        if (orderType == OrderLevel.SHOP.getValue()) {
            return skuOrderDao.findByOrderId(orderId).get(0).getBuyerId();
        }
        return skuOrderDao.findById(orderId).getBuyerId();
    }

    @Override
    public Map<Long, List<SkuOrder>> getSkuOrderMap(List<Long> shopOrderIds) {
        var list = findByShopOrderIds(shopOrderIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(SkuOrder::getOrderId));
    }

    @Override
    public Map<Long, SkuOrder> findMapByIds(List<Long> skuOrderIds) {
        var list = findByIds(skuOrderIds).getResult();

        return CollectionUtils.isEmpty(list) ?
                Collections.emptyMap() : list.stream().collect(Collectors.toMap(SkuOrder::getId, o -> o, (k1, k2) -> k1));
    }

    @Override
    public SkuOrder getOne(Map<String, Object> query) {
        return skuOrderDao.selectOne(query);
    }

    @Override
    public List<SkuOrder> list(Map<String, Object> query) {
        return skuOrderDao.selectList(query);
    }

    @Override
    public Paging<SkuOrder> pagingSkuOrder(OrderCriteria orderCriteria) {
        var total = skuOrderDao.count(orderCriteria);
        var orders = skuOrderDao.findSkuOrderBy(orderCriteria);
        for (var order : orders) {
            // read the sku-info from the extra
            long id = order.getId();
            var feeView = feeViewDao.cacheId(order.getFeeId());
            var depotView = depotViewDao.cacheId(order.getDepotId());
            var profitView = profitViewDao.cacheId(order.getProfitId());
            var extraView = extraViewDao.cacheId(order.getExtraId());
            var itemSnapShot = itemSnapShotCacheHolder.findBySnapShotId(order.getItemSnapshotId());
            if (itemSnapShot != null) {
                BeanUtils.copyProperties(itemSnapShot, order);
            }
            if (feeView != null) {
                BeanUtils.copyProperties(feeView, order);
            }
            if (depotView != null) {
                BeanUtils.copyProperties(depotView, order);
            }
            if (profitView != null) {
                BeanUtils.copyProperties(profitView, order);
            }
            if (extraView != null) {
                BeanUtils.copyProperties(extraView, order);

                var extra = extraView.extraJson() == null ? null : new JsonObject(new String(extraView.extraJson()));
                if (extra != null && StringUtils.hasText(extra.getString("itemName"))) {
                    order.setItemName(extra.getString("itemName"));
                }
            }
            var image = imageInfoDao.cacheId(order.getSkuImageId());
            if (image != null) {
                order.setSkuImage(new String(image.image()));
            }
            order.setId(id);
        }
        return new Paging<>(total, orders);
    }

    @Override
    public Response<List<SkuOrder>> findByOrderIdWithStatus(Long orderId, Integer status) {
        try {
            return Response.ok(skuOrderDao.findByOrderIdWithStatus(orderId, status));
        } catch (Exception e) {
            log.error("{} fail to query order[{}] status[{}]", LogUtil.getClassMethodName(), orderId, status, e);
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<SkuOrder> findById(Long id) {
        try {
            SkuOrder skuOrder = skuOrderDao.findById(id);
            if (null == skuOrder) {
                log.error("sku order id = {} not found", id);
                return Response.fail("sku.order.not.found");
            }
            return Response.ok(skuOrder);
        } catch (Exception e) {
            log.error("fail to find sku order by id {}, cause:{}",
                    id, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.find.fail");
        }
    }


    @Override
    public Response<List<SkuOrder>> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<SkuOrder> skuOrders = skuOrderDao.findByIds(ids);

            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("fail to find sku order by ids {}, cause:{}",
                    ids, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.find.fail");
        }
    }

    /**
     * sku订单分页列表
     *
     * @param pageNo        页码
     * @param size          每页大小
     * @param orderCriteria 查询条件
     * @return 分页订单
     */
    @Override
    public Response<Paging<SkuOrder>> findBy(Integer pageNo, Integer size,
                                             OrderCriteria orderCriteria) {
        try {
            PageInfo pageInfo = new PageInfo(pageNo, size);
            Map<String, Object> params = orderCriteria == null ? Maps.newHashMap() : orderCriteria.toMap();
            Paging<SkuOrder> skuOrders = skuOrderDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("failed to find sku orders by {}, pageNo={}, size={}, cause:{}",
                    orderCriteria, pageNo, size, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.find.fail");
        }
    }

    @Override
    public Either<List<SkuOrder>> findItemIdAndShopByShopOrderId(Long shopOrderId) {
        return Either.ok(skuOrderDao.findItemIdAndShopByShopOrderId(shopOrderId));
    }

    /**
     * 根据店铺订单id查找对应的sku子订单
     *
     * @param shopOrderId 店铺订单id
     * @return 对应的sku子订单
     */
    @Override
    public Response<List<SkuOrder>> findByShopOrderId(Long shopOrderId) {
        try {
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(shopOrderId);
            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("failed to find sku order by shop order(id={}), cause:{}",
                    shopOrderId, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.find.fail");
        }
    }

    /**
     * 根据店铺订单id查找对应的sku子订单
     *
     * @param shopOrderIds 店铺订单id列表
     * @return 对应的sku子订单
     */
    @Override
    public Response<List<SkuOrder>> findByShopOrderIds(List<Long> shopOrderIds) {
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderIds(shopOrderIds);

            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("fail to find sku order by shop order ids {}, cause:{}",
                    shopOrderIds, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.query.fail");
        }
    }

    /**
     * 根据店铺订单id和skuId查找唯一的sku子订单
     *
     * @param orderId 店铺订单id
     * @param skuId
     * @return 对应的sku子订单
     */
    @Override
    public Response<SkuOrder> findByOrderIdAndSkuId(Long orderId, Long skuId) {
        try {
            SkuOrder skuOrders = skuOrderDao.findByOrderIdAndSkuId(orderId, skuId);
            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("failed to find sku order by shop order(id={}) and skuId={}, cause:{}",
                    orderId, skuId, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.find.fail");
        }
    }

    /**
     * 店铺订单计数
     *
     * @param orderCriteria
     * @return 订单数量
     */
    @Override
    public Response<Long> countShopOrder(OrderCriteria orderCriteria) {
        try {
            orderCriteria.transformShopOrderId();
            return Response.ok(skuOrderDao.countShopOrder(orderCriteria));
        } catch (Exception e) {
            log.error("failed to count shop orders by orderCriteria={}, cause: {}", orderCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("order.count.fail");
        }
    }

    /**
     * 查询子订单列表
     *
     * @param orderCriteria
     * @return
     */
    @Override
    public Response<List<SkuOrder>> listSkuOrdersBy(OrderCriteria orderCriteria) {
        try {
            return Response.ok(skuOrderDao.listSkuOrdersBy(orderCriteria));
        } catch (Exception e) {
            log.error("failed to list skuOrder by orderCriteria={}, cause: {}", orderCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.list.fail");
        }
    }

    @Override
    public Response<SkuOrder> findByOrderIdAndOuterSkuId(Long orderId, String outerSkuId) {
        try {
            SkuOrder skuOrders = skuOrderDao.findByOrderIdAndOuterSkuId(orderId, outerSkuId);
            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("failed to find sku order by shop order(id={}) and outCode={}, cause:{}",
                    orderId, outerSkuId, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.find.fail");
        }

    }

    @Override
    public Either<List<SkuOrder>> findByGatherOrderId(Long gatherOrderId) {
        try {
            return Either.ok(skuOrderDao.findByGatherOrderId(gatherOrderId));
        } catch (Exception ex) {
            log.error("{} fail to find by gather Order[{}]", LogUtil.getClassMethodName(), gatherOrderId, ex);
            return Either.error(ex);
        }
    }

    @Override
    public Either<List<SkuOrder>> findNoItemSnapShotId(int count) {
        return Either.ok(skuOrderDao.findNoItemSnapShotId(count));
    }
}
