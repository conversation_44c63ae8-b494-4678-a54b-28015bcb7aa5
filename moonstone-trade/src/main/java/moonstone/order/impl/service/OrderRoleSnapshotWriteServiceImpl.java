package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OrderRoleSnapshotDao;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.service.OrderRoleSnapshotWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class OrderRoleSnapshotWriteServiceImpl implements OrderRoleSnapshotWriteService {

    @Autowired
    private OrderRoleSnapshotDao orderRoleSnapshotDao;

    @Override
    public Response<Boolean> create(List<OrderRoleSnapshot> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return Response.ok(true);
            }

            return Response.ok(orderRoleSnapshotDao.creates(list) == list.size());
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotWriteServiceImpl.create error, list={}", JSON.toJSONString(list), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> update(OrderRoleSnapshot parameter) {
        try {
            if (parameter == null || parameter.getId() == null) {
                return Response.fail("入参为空");
            }

            return Response.ok(orderRoleSnapshotDao.update(parameter));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotWriteServiceImpl.update error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
