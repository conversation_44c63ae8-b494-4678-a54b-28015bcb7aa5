package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.AccountStatementWithdrawRelationDao;
import moonstone.order.model.AccountStatementWithdrawRelation;
import moonstone.order.service.AccountStatementWithdrawRelationReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class AccountStatementWithdrawRelationReadServiceImpl implements AccountStatementWithdrawRelationReadService {

    @Autowired
    private AccountStatementWithdrawRelationDao accountStatementWithdrawRelationDao;

    @Override
    public Response<List<AccountStatementWithdrawRelation>> findByWithdrawApplyId(Long applyId) {
        try {
            if (applyId == null) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(accountStatementWithdrawRelationDao.findByWithdrawApplyId(applyId));
        } catch (Exception ex) {
            log.error("AccountStatementWithdrawRelationReadServiceImpl.findByWithdrawApplyId error, applyId={}", applyId, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
