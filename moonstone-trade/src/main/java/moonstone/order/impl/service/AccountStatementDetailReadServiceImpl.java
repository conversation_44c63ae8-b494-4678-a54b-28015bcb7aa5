package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.dto.AccountStatementDetailCriteria;
import moonstone.order.impl.dao.AccountStatementDetailDao;
import moonstone.order.model.AccountStatementDetail;
import moonstone.order.model.result.AccountStatementDetailDO;
import moonstone.order.service.AccountStatementDetailReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @since 2022-08-13 15:27
 */
@Slf4j
@Service
public class AccountStatementDetailReadServiceImpl implements AccountStatementDetailReadService {

    @Resource
    private AccountStatementDetailDao accountStatementDetailDao;

    @Override
    public Response<Paging<AccountStatementDetail>> findPage(AccountStatementDetailCriteria criteria) {
        try {
            return Response.ok(accountStatementDetailDao.paging(criteria.toMap()));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findPage error, criteria={}", JSON.toJSONString(criteria), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<AccountStatementDetail>> findPageList(AccountStatementDetailCriteria criteria) {
        try {
            return Response.ok(accountStatementDetailDao.findPageList(criteria.toMap()));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findPage error, criteria={}", JSON.toJSONString(criteria), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<AccountStatementDetail>> findByAccountStatementIds(List<Long> accountStatementIds) {
        try {
            return Response.ok(accountStatementDetailDao.findByAccountStatementIds(accountStatementIds));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findByAccountStatementIds error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<AccountStatementDetail>> findByBalanceDetailIds(List<Long> balanceDetailIds) {
        try {
            return Response.ok(accountStatementDetailDao.findByBalanceDetailIds(balanceDetailIds));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findByBalanceDetailIds error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> findMaxSkuOrderNum(Long accountStatementId) {
        try {
            return Response.ok(accountStatementDetailDao.findMaxSkuOrderNum(accountStatementId));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findMaxSkuOrderNum error, accountStatementId={}",
                    accountStatementId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<AccountStatementDetailDO>> findCompletedWithNoWithdrawRecord(Long shopId, Long userId, List<Integer> withdrawStatus,
                                                                                      Integer pageNo, Integer pageSize) {
        try {
            return Response.ok(accountStatementDetailDao.findCompletedWithNoWithdrawRecord(shopId, userId, withdrawStatus,
                    (pageNo - 1) * pageSize, pageSize));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findCompletedWithNoWithdrawRecord error, shopId={}, userId={}",
                    shopId, userId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<AccountStatementDetail> findValidByBalanceDetailId(Long balanceDetailId) {
        try {
            if (balanceDetailId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(accountStatementDetailDao.findValidByBalanceDetailId(balanceDetailId));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findValidByBalanceDetailId error, balanceDetailId={}",
                    balanceDetailId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<Long>> findOrderIds(Long accountStatementId, int pageNo, int pageSize) {
        try {
            if (accountStatementId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(accountStatementDetailDao.findOrderIds(accountStatementId, pageNo, pageSize));
        } catch (Exception ex) {
            log.error("AccountStatementDetailReadServiceImpl.findOrderIds error, balanceDetailId={}, pageNo={}, pageSize={}",
                    accountStatementId, pageNo, pageSize, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
