package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.ShopVideoMsg;
import moonstone.order.service.ShopVideosDataWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/18 11:22
 */
@RpcProvider
@Service
@Slf4j
public class ShopVideosDataWriteServiceImpl implements ShopVideosDataWriteService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Boolean> createShopVideoMsg(ShopVideoMsg shopVideoMsg) {
        try {
            mongoTemplate.insert(shopVideoMsg);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} entity:{}", LogUtil.getClassMethodName(), shopVideoMsg);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateShopVideoMsg(ShopVideoMsg shopVideoMsg) {
        try {
            Query query = Query.query(Criteria.where("shopId").is(shopVideoMsg.getShopId()).andOperator(Criteria.where("sourceId").is(shopVideoMsg.getSourceId())));
            Update update = Update.update("videoUrl", shopVideoMsg.getVideoUrl());
            mongoTemplate.updateFirst(query, update, ShopVideoMsg.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} shopVideoMsg:{} shopVideoMsg_videoUrl:{}", LogUtil.getClassMethodName(), shopVideoMsg, shopVideoMsg.getVideoUrl());
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
