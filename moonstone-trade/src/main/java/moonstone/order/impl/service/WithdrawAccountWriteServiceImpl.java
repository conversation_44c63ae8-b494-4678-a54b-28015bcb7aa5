package moonstone.order.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.impl.dao.WithdrawAccountDao;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.WithdrawAccountWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WithdrawAccountWriteServiceImpl implements WithdrawAccountWriteService {
    @Autowired
    WithdrawAccountDao dao;

    @Override
    public Either<WithdrawAccount> create(WithdrawAccount account) {
        try {
            dao.create(account);
            return Either.ok(account);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} account:{}", LogUtil.getClassMethodName(), account);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> update(WithdrawAccount account) {
        try {
            return Either.ok(dao.update(account));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} account:{}", LogUtil.getClassMethodName(), account);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Long> revokeDefault(Long shopId, Long userId) {
        try {
            return Either.ok(dao.revokeDefault(shopId, userId));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId);
            return Either.error(ex);
        }
    }
}
