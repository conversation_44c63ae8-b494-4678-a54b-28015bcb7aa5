package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AccountStatementWithdrawStatusEnum;
import moonstone.order.enu.WithdrawPaymentStatusEnum;
import moonstone.order.impl.dao.WithDrawProfitApplyDao;
import moonstone.order.impl.dao.WithdrawPaymentDao;
import moonstone.order.impl.dao.WithdrawPaymentReceiverDao;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawPayment;
import moonstone.order.model.WithdrawPaymentReceiver;
import moonstone.order.service.AccountStatementWithdrawRelationReadService;
import moonstone.order.service.AccountStatementWithdrawRelationWriteService;
import moonstone.order.service.WithdrawPaymentWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class WithdrawPaymentWriteServiceImpl implements WithdrawPaymentWriteService {

    @Autowired
    private WithdrawPaymentDao withdrawPaymentDao;

    @Autowired
    private WithdrawPaymentReceiverDao withdrawPaymentReceiverDao;

    @Autowired
    private WithDrawProfitApplyDao withDrawProfitApplyDao;

    @Autowired
    private AccountStatementWithdrawRelationReadService accountStatementWithdrawRelationReadService;

    @Autowired
    private AccountStatementWithdrawRelationWriteService accountStatementWithdrawRelationWriteService;

    @Override
    public Response<Boolean> create(WithdrawPayment parameter) {
        try {
            if (parameter == null) {
                return Response.fail("入参为空");
            }

            return Response.ok(withdrawPaymentDao.insertSelective(parameter) > 0);
        } catch (Exception ex) {
            log.error("WithdrawPaymentWriteServiceImpl.create error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> create(WithdrawPayment withdrawPayment, WithdrawPaymentReceiver receiver) {
        try {
            if (withdrawPayment == null || receiver == null) {
                return Response.fail("入参为空");
            }

            if (withdrawPaymentDao.insertSelective(withdrawPayment) <= 0 || withdrawPayment.getId() == null) {
                throw new RuntimeException("支付单信息插入失败");
            }

            receiver.setWithdrawPaymentId(withdrawPayment.getId());
            if (withdrawPaymentReceiverDao.insertSelective(receiver) <= 0) {
                throw new RuntimeException("收款方信息插入失败");
            }

            return Response.ok(true);
        } catch (Exception ex) {
            log.error("WithdrawPaymentWriteServiceImpl.create error, withdrawPayment={}, receiver={}",
                    JSON.toJSONString(withdrawPayment), JSON.toJSONString(receiver), ex);
            throw ex;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> update(WithDrawProfitApply applyOrder, WithdrawPayment withdrawPayment) {
        try {
            if (applyOrder == null || withdrawPayment == null) {
                return Response.fail("入参为空");
            }

            if (!withDrawProfitApplyDao.update(applyOrder)) {
                throw new RuntimeException("提现申请单更新失败");
            }
            if (!withdrawPaymentDao.update(withdrawPayment)) {
                throw new RuntimeException("提现支付单更新失败");
            }

            // 更新”账单与提现单的对应关系表“
            updateRelation(applyOrder.getId(), withdrawPayment.getStatus());

            return Response.ok(true);
        } catch (Exception ex) {
            log.error("WithdrawPaymentWriteServiceImpl.update error, applyOrder={}, withdrawPayment={}",
                    JSON.toJSONString(applyOrder), JSON.toJSONString(withdrawPayment), ex);
            throw ex;
        }
    }

    @Override
    public Response<Boolean> update(WithdrawPayment paymentUpdate) {
        try {
            if(paymentUpdate==null || paymentUpdate.getId()==null){
                return Response.fail("入参为空");
            }

            return Response.ok(withdrawPaymentDao.update(paymentUpdate));
        } catch (Exception ex) {
            log.error("WithdrawPaymentWriteServiceImpl.update error, parameter={}", JSON.toJSONString(paymentUpdate), ex);
            return Response.fail(ex.getMessage());
        }
    }

    private void updateRelation(Long applyId, Integer paymentStatus) {
        if (!WithdrawPaymentStatusEnum.PAY_IN_PROGRESS.getCode().equals(paymentStatus)) {
            return;
        }

        var response = accountStatementWithdrawRelationReadService.findByWithdrawApplyId(applyId);
        if (response == null || !response.isSuccess()) {
            throw new RuntimeException("提现单与账单的关系数据查询失败");
        }
        if (CollectionUtils.isEmpty(response.getResult())) {
            return;
        }

        var updated = accountStatementWithdrawRelationWriteService.updateWithdrawStatus(applyId,
                AccountStatementWithdrawStatusEnum.WAITING_FOR_PAY, AccountStatementWithdrawStatusEnum.PAY_IN_PROGRESS);
        if (updated != null && (!updated.isSuccess() || !updated.getResult())) {
            throw new RuntimeException("提现单与账单的关系数据更新失败");
        }
    }
}
