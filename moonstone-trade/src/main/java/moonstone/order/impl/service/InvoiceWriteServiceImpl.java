package moonstone.order.impl.service;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.InvoiceDao;
import moonstone.order.impl.manager.InvoiceManager;
import moonstone.order.model.Invoice;
import moonstone.order.service.InvoiceWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author:cp
 * Created on 6/21/16.
 */
@Slf4j
@Service
@RpcProvider
public class InvoiceWriteServiceImpl implements InvoiceWriteService {

    private final InvoiceDao invoiceDao;

    private final InvoiceManager invoiceManager;

    @Autowired
    public InvoiceWriteServiceImpl(InvoiceDao invoiceDao,
                                   InvoiceManager invoiceManager) {
        this.invoiceDao = invoiceDao;
        this.invoiceManager = invoiceManager;
    }

    @Override
    public Response<Long> createInvoice(Invoice invoice) {
        try {
            final Long userId = invoice.getUserId();
            if (Objects.equal(invoice.getIsDefault(), Boolean.TRUE)) {
                invoiceDao.notDefault(userId);
            } else {
                Invoice defaultInvoice = invoiceDao.findDefaultByUserId(userId);
                invoice.setIsDefault(defaultInvoice == null);
            }

            invoiceDao.create(invoice);
            return Response.ok(invoice.getId());
        } catch (Exception e) {
            log.error("fail to create invoice:{},cause:{}",
                    invoice, Throwables.getStackTraceAsString(e));
            return Response.fail("invoice.create.fail");
        }
    }

    @Override
    public Response<Boolean> deleteInvoice(Long invoiceId) {
        try {
            invoiceDao.updateStatus(invoiceId, -1);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to delete invoice by invoice id={},cause:{}",
                    invoiceId, Throwables.getStackTraceAsString(e));
            return Response.fail("invoice.delete.fail");
        }
    }

    @Override
    public Response<Boolean> setDefault(Long userId, Long invoiceId) {
        try {
            invoiceManager.setDefault(userId, invoiceId);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("user(id={}) fail to set invoice(id={}) as default,cause:{}",
                    userId, invoiceId, Throwables.getStackTraceAsString(e));
            return Response.fail("invoice.set.default.fail");
        }
    }
}
