package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.EnterpriseWithdrawDao;
import moonstone.order.model.EnterpriseWithdraw;
import moonstone.order.service.EnterpriseWithdrawWriteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class EnterpriseWithdrawWriteServiceImpl implements EnterpriseWithdrawWriteService {

    @Resource
    private EnterpriseWithdrawDao enterpriseWithdrawDao;

    @Override
    public Response<Boolean> create(EnterpriseWithdraw parameter) {
        try {
            return Response.ok(enterpriseWithdrawDao.insertSelective(parameter) > 0);
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawWriteServiceImpl.create error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> update(EnterpriseWithdraw parameter) {
        try {
            if (parameter == null || parameter.getId() == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(enterpriseWithdrawDao.update(parameter));
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawWriteServiceImpl.update error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
