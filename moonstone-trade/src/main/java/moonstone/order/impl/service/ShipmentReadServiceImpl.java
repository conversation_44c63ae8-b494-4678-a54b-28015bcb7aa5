/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.impl.dao.OrderShipmentDao;
import moonstone.order.impl.dao.ShipmentDao;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderShipment;
import moonstone.order.model.Shipment;
import moonstone.order.model.result.OrderShipmentInfoDO;
import moonstone.order.service.ShipmentReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货单读服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-23
 */
@Service
@Slf4j
@RpcProvider
public class ShipmentReadServiceImpl implements ShipmentReadService {

    private final ShipmentDao shipmentDao;

    private final OrderShipmentDao orderShipmentDao;

    @Autowired
    public ShipmentReadServiceImpl(ShipmentDao shipmentDao,
                                   OrderShipmentDao orderShipmentDao) {
        this.shipmentDao = shipmentDao;
        this.orderShipmentDao = orderShipmentDao;
    }

    @Override
    public Response<List<OrderShipment>> findOrderShipmentByOrderAndOrderLevel(long orderId, OrderLevel orderLevel) {
        try {
            return Response.ok(orderShipmentDao.findByOrderIdAndOrderType(orderId, orderLevel.getValue()));
        } catch (Exception e) {
            log.error("failed to find order shipment(orderId={}, orderLevel={}), cause:{}",
                    orderId, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.find.fail");
        }
    }

    /**
     * 根据(子)订单id和级别查找对应的发货单列表
     *
     * @param orderId    (子)订单id
     * @param orderLevel 级别
     * @return 对应的发货单列表
     */
    @Override
    public Response<List<Shipment>> findByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel) {
        try {
            List<OrderShipment> orderShipments = orderShipmentDao.findByOrderIdAndOrderType(orderId, orderLevel.getValue());
            if (CollectionUtils.isEmpty(orderShipments)) {
                return Response.ok(Collections.emptyList());
            }
            return Response.ok(findShipmentByOrderShipments(orderShipments));
        } catch (ServiceException se) {
            log.error("failed to find order shipment(orderId={}, orderLevel={}), cause:{}",
                    orderId, orderLevel, Throwables.getStackTraceAsString(se));
            return Response.fail(se.getMessage());
        } catch (Exception e) {
            log.error("failed to find order shipment(orderId={}, orderLevel={}), cause:{}",
                    orderId, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.find.fail");
        }
    }

    @Override
    public Response<List<Shipment>> findByOrderIdsAndOrderLevel(List<Long> orderIds, OrderLevel orderLevel) {
        try {
            List<OrderShipment> orderShipments = orderShipmentDao.findByOrderIdsAndOrderType(orderIds, orderLevel.getValue());
            if (CollectionUtils.isEmpty(orderShipments)) {
                return Response.ok(Collections.emptyList());
            }
            return Response.ok(findShipmentByOrderShipments(orderShipments));
        } catch (ServiceException se) {
            log.error("failed to find order shipment(orderIds={}, orderLevel={}), cause:{}",
                    orderIds, orderLevel, Throwables.getStackTraceAsString(se));
            return Response.fail(se.getMessage());
        } catch (Exception e) {
            log.error("failed to find order shipment(orderIds={}, orderLevel={}), cause:{}",
                    orderIds, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.find.fail");
        }
    }

    /**
     * 根据id查找对应的发货单
     *
     * @param shipmentId 发货单id
     * @return 对应的发货单
     */
    @Override
    public Response<Shipment> findById(Long shipmentId) {
        try {
            Shipment shipmentApply = shipmentDao.findById(shipmentId);
            if (shipmentApply == null) {
                log.error("no order shipment(id={}) found", shipmentId);
                return Response.fail("order.shipment.not.found");
            }
            return Response.ok(shipmentApply);
        } catch (Exception e) {
            log.error("failed to find order shipment(id={}), cause:{}", shipmentId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.find.fail");
        }
    }

    /**
     * 根据运费单号和公司查找对应的发货单
     *
     * @param shipmentSerialNo 发货单号
     * @param corpCode         承运公司
     * @return 对应的发货单
     */
    @Override
    public Response<Shipment> findBySerialNoAndCorpCode(String shipmentSerialNo, String corpCode) {
        try {
            Shipment shipmentApply = shipmentDao.findBySerialNoAndCorpCode(shipmentSerialNo, corpCode);
            if (shipmentApply == null) {
                log.error("no order shipment(shipSerialNo={}, shipmentCorpCode={}) found", shipmentSerialNo, corpCode);
                return Response.fail("order.shipment.not.found");
            }
            return Response.ok(shipmentApply);
        } catch (Exception e) {
            log.error("failed to find order shipment(shipSerialNo={}, shipmentCorpCode={}), cause:{}",
                    shipmentSerialNo, corpCode, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.find.fail");
        }
    }

    /**
     * 根据发货单id查找关联的(子)订单id列表
     *
     * @param shipmentId 发货单id
     * @return 对应发货单与(子)订单的关联关系
     */
    @Override
    public Response<List<OrderShipment>> findOrderIdsByShipmentId(Long shipmentId) {
        try {
            List<OrderShipment> orderShipments = orderShipmentDao.findByShipmentId(shipmentId);
            return Response.ok(orderShipments);
        } catch (Exception e) {
            log.error("failed to find orders for shipment(id={}), cause:{}",
                    shipmentId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.find.fail");
        }
    }

    private List<Shipment> findShipmentByOrderShipments(List<OrderShipment> orderShipments) {
        if (orderShipments.size() == 1) {
            OrderShipment orderShipment = orderShipments.get(0);
            Long shipmentId = orderShipment.getShipmentId();
            Shipment shipment = shipmentDao.findById(shipmentId);
            if (shipment == null) {
                log.error("no shipment(id={}) found", shipmentId);
                throw new ServiceException("shipment.not.found");
            }
            return Lists.newArrayList(shipment);
        } else {
            List<Long> shipmentIds = Lists.newArrayListWithCapacity(orderShipments.size());
            for (OrderShipment orderShipment : orderShipments) {
                shipmentIds.add(orderShipment.getShipmentId());
            }
            return shipmentDao.findByIds(shipmentIds);
        }
    }

    @Override
    public Either<List<Shipment>> findShipmentByStatusAndUpdatedAt(Integer status, Date updatedAt) {
        try {
            return Either.ok(shipmentDao.findByStatusAndUpdatedAt(status, updatedAt));
        } catch (Exception ex) {
            log.error("{} failed to find shipment by status [{}] updatedAt [{}]", LogUtil.getClassMethodName(), status, updatedAt, ex);
            return Either.error(ex);
        }
    }

    @Override
    public Response<List<OrderShipmentInfoDO>> findByOrderIds(List<Long> orderIds, OrderLevel orderLevel) {
        try {
            return Response.ok(shipmentDao.findByOrderIds(orderIds, orderLevel));
        } catch (Exception ex) {
            log.error("ShipmentReadServiceImpl.findByOrderIds error ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, OrderShipmentInfoDO> getShipmentMap(List<Long> orderIdList, OrderLevel orderLevel) {
        var shipmentList = findByOrderIds(orderIdList, orderLevel).getResult();
        if (CollectionUtils.isEmpty(shipmentList)) {
            return Collections.emptyMap();
        }

        return shipmentList.stream().collect(Collectors.toMap(OrderShipmentInfoDO::getOrderId, e -> e,
                (o1, o2) -> o1.getId() > o1.getId() ? o1 : o2));
    }
}
