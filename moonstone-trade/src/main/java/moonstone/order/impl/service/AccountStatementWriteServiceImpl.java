package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.AccountStatementDao;
import moonstone.order.impl.dao.AccountStatementDetailDao;
import moonstone.order.model.AccountStatement;
import moonstone.order.model.AccountStatementDetail;
import moonstone.order.service.AccountStatementWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccountStatementWriteServiceImpl implements AccountStatementWriteService {

    @Resource
    private AccountStatementDao accountStatementDao;

    @Resource
    private AccountStatementDetailDao accountStatementDetailDao;

    private static final int BATCH_PAGE_SIZE = 300;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> save(AccountStatement accountStatement, List<AccountStatementDetail> detailList) {
        try {
            if (accountStatement == null) {
                return Response.fail("账单对象为空");
            }
            if (accountStatementDao.insertSelective(accountStatement) <= 0) {
                return Response.fail("账单对象保存失败");
            }

            //保存账单明细
            batchSaveDetail(accountStatement, detailList);

            return Response.ok(true);
        } catch (Exception ex) {
            log.error("AccountStatementWriteServiceImpl.save error, accountStatement={}", JSON.toJSONString(accountStatement), ex);
            throw ex;
        }
    }

    private void batchSaveDetail(AccountStatement accountStatement, List<AccountStatementDetail> detailList) {
        if (accountStatement == null || CollectionUtils.isEmpty(detailList)) {
            return;
        }
        detailList.forEach(detail -> detail.setAccountStatementId(accountStatement.getId()));

        //分页处理
        int totalPage = detailList.size() / BATCH_PAGE_SIZE + (detailList.size() % BATCH_PAGE_SIZE > 0 ? 1 : 0);
        for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
            int offset = BATCH_PAGE_SIZE * (currentPage - 1);
            var subList = detailList.stream().skip(offset).limit(BATCH_PAGE_SIZE).collect(Collectors.toList());

            if (accountStatementDetailDao.batchInsert(subList) != subList.size()) {
                throw new RuntimeException("账单明细保存失败");
            }
        }
    }
}
