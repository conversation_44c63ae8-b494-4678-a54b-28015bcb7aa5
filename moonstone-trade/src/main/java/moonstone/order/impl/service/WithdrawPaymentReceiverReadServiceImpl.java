package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.WithdrawPaymentReceiverDao;
import moonstone.order.model.WithdrawPaymentReceiver;
import moonstone.order.service.WithdrawPaymentReceiverReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WithdrawPaymentReceiverReadServiceImpl implements WithdrawPaymentReceiverReadService {

    @Autowired
    private WithdrawPaymentReceiverDao withdrawPaymentReceiverDao;

    @Override
    public Response<WithdrawPaymentReceiver> findByWithdrawPaymentId(Long withdrawPaymentId) {
        try {
            if (withdrawPaymentId == null) {
                return Response.ok(null);
            }

            return Response.ok(withdrawPaymentReceiverDao.findByWithdrawPaymentId(withdrawPaymentId));
        } catch (Exception ex) {
            log.error("WithdrawPaymentReceiverReadServiceImpl.findByWithdrawPaymentId error, withdrawPaymentId={}",
                    withdrawPaymentId, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
