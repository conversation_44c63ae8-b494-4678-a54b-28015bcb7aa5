package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.EnterpriseWithdrawDetailDao;
import moonstone.order.model.EnterpriseWithdrawDetail;
import moonstone.order.service.EnterpriseWithdrawDetailReadService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class EnterpriseWithdrawDetailReadServiceImpl implements EnterpriseWithdrawDetailReadService {

    @Resource
    private EnterpriseWithdrawDetailDao enterpriseWithdrawDetailDao;

    @Override
    public Response<List<Long>> findValidPaymentIds(List<Long> paymentIds) {
        try {
            if (CollectionUtils.isEmpty(paymentIds)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(enterpriseWithdrawDetailDao.findValidPaymentIds(paymentIds));
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawDetailReadServiceImpl.findValidPaymentIds error, ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<EnterpriseWithdrawDetail>> findByEnterpriseWithdrawId(Long withdrawId, Integer pageNo, Integer pageSize) {
        try {
            if (withdrawId == null) {
                return Response.fail("入参缺失");
            }

            Integer offset = null, limit = null;
            if (pageNo != null && pageSize != null) {
                offset = (pageNo - 1) * pageSize;
                limit = pageSize;
            }

            return Response.ok(enterpriseWithdrawDetailDao.findByEnterpriseWithdrawId(withdrawId, offset, limit));
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawDetailReadServiceImpl.findByWithdrawId error, withdrawId={}, pageNo={}, pageSize={}",
                    withdrawId, pageNo, pageSize, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
