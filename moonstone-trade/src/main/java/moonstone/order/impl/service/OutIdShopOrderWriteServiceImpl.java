package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OutIdShopOrderDao;
import moonstone.order.model.OutIdShopOrder;
import moonstone.order.service.OutIdShopOrderWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@RpcProvider
@Service
@Slf4j
public class OutIdShopOrderWriteServiceImpl implements OutIdShopOrderWriteService {
    @Autowired
    OutIdShopOrderDao outIdShopOrderDao;
    @Override
    public Response<Boolean> save(OutIdShopOrder outIdShopOrder) {
        try
        {
            //todo 目前只有save没有update
                outIdShopOrderDao.create(outIdShopOrder);
            return Response.ok(true);
        }
        catch (Exception ex)
        {
            log.error("fail to save outIdShopOrder : {}", JSON.toJSONString(outIdShopOrder));
            return Response.fail("fail.save.outIdShopOrder");
        }
    }

    @Override
    public Response<Boolean> delete(Long outIdShopOrder) {
        try
        {
            outIdShopOrderDao.delete(outIdShopOrder);
            return Response.ok(true);
        }
        catch (Exception ex)
        {
            log.error("fail to delete outIdShopOrder : {}", JSON.toJSONString(outIdShopOrder));
            return Response.fail("fail.delete.outIdShopOrder");
        }
    }
}
