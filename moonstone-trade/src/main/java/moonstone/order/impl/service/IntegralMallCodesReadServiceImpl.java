package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.IntegralMallCodes;
import moonstone.order.service.IntegralMallCodesReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/4 12:04
 */
@Slf4j
@Service
@RpcProvider
public class IntegralMallCodesReadServiceImpl implements IntegralMallCodesReadService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Optional<IntegralMallCodes>> findBygenCode(String code) {
        try {
            Query query = new Query(Criteria.where("genCode").is(code));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralMallCodes.class)));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
