package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.enu.ProfitWithdrawRecordStatusEnum;
import moonstone.order.impl.dao.ProfitWithdrawRecordDao;
import moonstone.order.model.ProfitWithdrawRecord;
import moonstone.order.service.ProfitWithdrawRecordWriteService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ProfitWithdrawRecordWriteServiceImpl implements ProfitWithdrawRecordWriteService {

    @Resource
    private ProfitWithdrawRecordDao profitWithdrawRecordDao;

    @Override
    public Response<Boolean> creates(List<ProfitWithdrawRecord> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return Response.ok(true);
            }

            return Response.ok(profitWithdrawRecordDao.creates(list) == list.size());
        } catch (Exception ex) {
            log.error("ProfitWithdrawRecordWriteServiceImpl.creates error, list={}", JSON.toJSONString(list), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> removeByWithdrawId(Long withdrawId) {
        try {
            return Response.ok(profitWithdrawRecordDao.removeByWithdrawId(withdrawId) > 0);
        } catch (Exception ex) {
            log.error("ProfitWithdrawRecordWriteServiceImpl.removeByWithdrawId error, withdrawId={}", withdrawId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateStatusByWithdrawId(Long withdrawId, ProfitWithdrawRecordStatusEnum statusEnum) {
        try {
            return Response.ok(profitWithdrawRecordDao.updateStatusByWithdrawId(withdrawId, statusEnum.getCode()) > 0);
        } catch (Exception ex) {
            log.error("ProfitWithdrawRecordWriteServiceImpl.updateStatusByWithdrawId error, withdrawId={}, statusEnum={}",
                    withdrawId, statusEnum, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
