package moonstone.order.impl.service;

import io.vertx.core.json.JsonObject;
import moonstone.order.model.FirstOrderMark;
import moonstone.order.service.FirstOrderMarkService;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public record FirstOrderMarkServiceImpl(SqlSession sqlSession) implements FirstOrderMarkService {

  @Override
  public boolean isFirstOrder(Long orderId) {
    return sqlSession.<Long>selectOne("FirstOrderMark.exists", Map.of("orderId", orderId)) > 0;
  }

  @Override
  public List<Map<String, Object>> findPossibleFirstOrderMark(LocalDate date, Long shopId) {
    return sqlSession.selectList("ShopOrder.findFirstOrderByShopCategory",
      Map.of("date", date.toString() + " 00:00:00", "shopId", 274));
  }

  @Override
  public void removeByOrderId(Long orderId) {
    sqlSession.delete("FirstOrderMark.delete", Map.of("orderId", orderId));
  }

  @Override
  public void save(FirstOrderMark mark) {
    sqlSession.delete("FirstOrderMark.remove-it", JsonObject.mapFrom(mark).getMap());
    sqlSession.insert("FirstOrderMark.save", JsonObject.mapFrom(mark).getMap());
  }
}
