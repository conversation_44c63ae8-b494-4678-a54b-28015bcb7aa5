package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.ShopVipInformation;
import moonstone.order.service.ShopVipInformationWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/3 14:09
 */
@RpcProvider
@Service
@Slf4j
public class ShopVipInformationWriteServiceImpl implements ShopVipInformationWriteService {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Boolean> create(ShopVipInformation shopVipInformation) {
        try {
            shopVipInformation.setCreateAt(System.currentTimeMillis());
            shopVipInformation.setUpdateAt(System.currentTimeMillis());
            shopVipInformation.setStatus(IntegralStatus.NORMAL.value());
            mongoTemplate.insert(shopVipInformation);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} shopVipInformation:{}", LogUtil.getClassMethodName(), shopVipInformation);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> update(ShopVipInformation shopVipInformation) {
        try {
            Query query = Query.query(Criteria.where("userId").is(shopVipInformation.getUserId()).andOperator(Criteria.where("shopId").is(shopVipInformation.getShopId())
                    , Criteria.where("status").is(IntegralStatus.NORMAL.value())));
            Update update = new Update();
            update.set("vipName", shopVipInformation.getVipName());
            update.set("sex", shopVipInformation.getSex());
            update.set("birthDate", shopVipInformation.getBirthDate());
            update.set("city", shopVipInformation.getCity());
            update.set("cityId", shopVipInformation.getCityId());
            update.set("provinceId", shopVipInformation.getProvinceId());
            update.set("province", shopVipInformation.getProvince());
            update.set("countyId", shopVipInformation.getCountyId());
            update.set("county", shopVipInformation.getCounty());
            update.set("addDetail", shopVipInformation.getAddDetail());
            if (!ObjectUtils.isEmpty(shopVipInformation.getSonVipInformations())) {
                update.set("sonVipInformations", shopVipInformation.getSonVipInformations());
            }
            update.set("phone", shopVipInformation.getPhone());
            update.set("referrerName", shopVipInformation.getReferrerName());
            update.set("updateAt", System.currentTimeMillis());
            update.set("babyBirth", shopVipInformation.getBabyBirth());
            mongoTemplate.updateFirst(query, update, ShopVipInformation.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  shopVipInformation:{} ", LogUtil.getClassMethodName(), shopVipInformation);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateById(ShopVipInformation shopVipInformation) {
        try {
            Query query = Query.query(Criteria.where("_id").is(shopVipInformation.get_id()).andOperator(Criteria.where("shopId").is(shopVipInformation.getShopId())));
            Update update = new Update();
            update.set("vipName", shopVipInformation.getVipName());
            update.set("phone", shopVipInformation.getPhone());
            update.set("sex", shopVipInformation.getSex());
            update.set("birthDate", shopVipInformation.getBirthDate());
            update.set("city", shopVipInformation.getCity());
            update.set("cityId", shopVipInformation.getCityId());
            update.set("provinceId", shopVipInformation.getProvinceId());
            update.set("province", shopVipInformation.getProvince());
            update.set("countyId", shopVipInformation.getCountyId());
            update.set("county", shopVipInformation.getCounty());
            update.set("addDetail", shopVipInformation.getAddDetail());
            update.set("referrerName", shopVipInformation.getReferrerName());
            update.set("babyBirth", shopVipInformation.getBabyBirth());
            update.set("updateAt", System.currentTimeMillis());
            mongoTemplate.updateFirst(query, update, ShopVipInformation.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  shopVipInformation:{} ", LogUtil.getClassMethodName(), shopVipInformation);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> deleteById(String id) {
        try {
            Query query = Query.query(Criteria.where("_id").is(id));
            Update update = new Update();
            update.set("status", IntegralStatus.LOCKED.value());
            update.set("updateAt", System.currentTimeMillis());
            mongoTemplate.updateFirst(query, update, ShopVipInformation.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  id:{} ", LogUtil.getClassMethodName(), id);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
