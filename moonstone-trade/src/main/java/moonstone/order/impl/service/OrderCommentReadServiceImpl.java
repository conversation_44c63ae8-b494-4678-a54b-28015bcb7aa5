package moonstone.order.impl.service;

import com.google.common.base.Function;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.DateUtil;
import moonstone.order.dto.CommentCriteria;
import moonstone.order.dto.OrderCommentWithReply;
import moonstone.order.impl.dao.OrderCommentDao;
import moonstone.order.model.OrderComment;
import moonstone.order.service.OrderCommentReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Author:cp
 * Created on 4/22/16.
 */
@Slf4j
@Service
@RpcProvider
public class OrderCommentReadServiceImpl implements OrderCommentReadService {

    private final OrderCommentDao orderCommentDao;

    @Autowired
    public OrderCommentReadServiceImpl(OrderCommentDao orderCommentDao) {
        this.orderCommentDao = orderCommentDao;
    }

    @Override
    public Response<Paging<OrderComment>> paging(CommentCriteria commentCriteria) {

        try {
            normalizeDateCriteria(commentCriteria);
            PageInfo pageInfo = new PageInfo(commentCriteria.getPageNo(), commentCriteria.getSize());
            Map<String, Object> params = commentCriteria.toMap();
            Paging<OrderComment> orderCommentPaging = orderCommentDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            return Response.ok(orderCommentPaging);
        } catch (Exception e) {
            log.error("fail to find order comment paging by comment criteria {},cause:{}",
                    commentCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.find.fail");
        }
    }

    @Override
    public Response<OrderComment> findById(Long commentId) {
        try {
            if (commentId == null) {
                log.error("comment id is required");
                return Response.fail("comment.id.not.found");
            }
            return Response.ok(orderCommentDao.findById(commentId));
        } catch (Exception e) {
            log.error("fail to find comment by id {}, cause:{}",
                    commentId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.find.fail");
        }
    }

    @Override
    public Response<List<OrderComment>> findByItemIdAndSkuOrderId(Long itemId, Long skuOrderId) {
        try {
            if (itemId == null || skuOrderId == null) {
                log.error("skuId and skuOrderId is required");
                return Response.fail("order.comment.find.fail");
            }
            List<OrderComment> orderComments = orderCommentDao.findByItemIdAndSkuOrderId(itemId, skuOrderId);
            return Response.ok(filterDeleted(orderComments));
        } catch (Exception e) {
            log.error("fail to find comment by itemId {}, skuOrderId {}, cause:{}",
                    itemId, skuOrderId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.find.fail");
        }
    }

    @Override
    public Response<Paging<OrderCommentWithReply>> findDetail(CommentCriteria commentCriteria) {
        try {
            normalizeDateCriteria(commentCriteria);
            PageInfo pageInfo = new PageInfo(commentCriteria.getPageNo(), commentCriteria.getSize());
            Map<String, Object> params = commentCriteria.toMap();
            Paging<OrderComment> orderCommentPaging = orderCommentDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            if (orderCommentPaging.isEmpty()) {
                return Response.ok(Paging.empty());
            }

            List<OrderComment> orderComments = orderCommentPaging.getData();
            Multimap<Long, OrderComment> repliesBySkuOrderIdIndex = findReplies(orderComments);

            List<OrderCommentWithReply> orderCommentWithReplies = Lists.newArrayListWithCapacity(orderComments.size());
            for (OrderComment orderComment : orderComments) {
                OrderCommentWithReply orderCommentWithReply = new OrderCommentWithReply();
                orderCommentWithReply.setComment(orderComment);
                Collection<OrderComment> replies = repliesBySkuOrderIdIndex.get(orderComment.getSkuOrderId());
                orderCommentWithReply.setReplies(replies == null ? Collections.EMPTY_LIST : Lists.newArrayList(replies));
                orderCommentWithReplies.add(orderCommentWithReply);
            }
            return Response.ok(new Paging<>(orderCommentPaging.getTotal(), orderCommentWithReplies));
        } catch (Exception e) {
            log.error("fail to find order comment detail by criteria:{},cause:{}",
                    commentCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.find.fail");
        }
    }

    private Multimap<Long, OrderComment> findReplies(List<OrderComment> orderComments) {
        List<Long> skuOrderIds = Lists.transform(orderComments, new Function<OrderComment, Long>() {
            @Override
            public Long apply(OrderComment orderComment) {
                return orderComment.getSkuOrderId();
            }
        });

        List<OrderComment> replies = orderCommentDao.findRepliesBySkuOrderIds(skuOrderIds);
        return Multimaps.index(replies, new Function<OrderComment, Long>() {
            @Override
            public Long apply(OrderComment orderComment) {
                return orderComment.getSkuOrderId();
            }
        });
    }

    private List<OrderComment> filterDeleted(List<OrderComment> source) {
        List<OrderComment> result = new ArrayList<>();
        for (OrderComment oc : source) {
            if (!Objects.equals(oc.getStatus(), OrderComment.Status.DELETE.getValue())) {
                result.add(oc);
            }
        }
        return result;
    }

    private void normalizeDateCriteria(CommentCriteria commentCriteria) {
        if (commentCriteria.getStartAt() != null) {
            commentCriteria.setStartAt(DateUtil.withTimeAtStartOfDay(commentCriteria.getStartAt()));
        }
        if (commentCriteria.getEndAt() != null) {
            commentCriteria.setEndAt(DateUtil.withTimeAtEndOfDay(commentCriteria.getEndAt()));
        }
    }
}
