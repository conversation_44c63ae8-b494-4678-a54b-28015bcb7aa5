package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.WithdrawPaymentReceiverDao;
import moonstone.order.model.WithdrawPaymentReceiver;
import moonstone.order.service.WithdrawPaymentReceiverWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WithdrawPaymentReceiverWriteServiceImpl implements WithdrawPaymentReceiverWriteService {

    @Autowired
    private WithdrawPaymentReceiverDao withdrawPaymentReceiverDao;

    @Override
    public Response<Boolean> create(WithdrawPaymentReceiver parameter) {
        try {
            if (parameter == null) {
                return Response.fail("入参为空");
            }

            return Response.ok(withdrawPaymentReceiverDao.insertSelective(parameter) > 0);
        } catch (Exception ex) {
            log.error("WithdrawPaymentReceiverWriteServiceImpl.create error, parameter={} ",
                    JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
