package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.IntegralData;
import moonstone.order.service.IntegralDataReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/19 17:14
 * 积分二维码生成
 */
@Slf4j
@Service
@RpcProvider
public class IntegralDataReadServiceImpl implements IntegralDataReadService {
    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public Either<List<IntegralData>> findShopIdAndCode(String code, long shopId) {
        try {
            Query query = Query.query(Criteria.where("code").is(code).andOperator(Criteria.where("shopId").is(shopId)));
            return Either.ok(mongoTemplate.find(query, IntegralData.class));
        } catch (Exception ex) {
            log.error("{} code:{}  ", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<IntegralData>> findByShopIdAndCode(String code) {
        try {
            Query query = new Query(Criteria.where("status").is(IntegralStatus.NORMAL.value()).andOperator(Criteria.where("genCode").is(code)));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralData.class)));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<IntegralData>> findByShopIdAndCodeAndPwd(String code, String pwd) {
        try {
            Query query = new Query(Criteria.where("status").is(IntegralStatus.NORMAL.value())
                    .andOperator(Criteria.where("genCode").is(code), Criteria.where("pwd").is(pwd)));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralData.class)));
        } catch (Exception ex) {
            log.error("{}  code:{} pwd:{}", LogUtil.getClassMethodName(), code, pwd);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
//db.student.find().sort({stu_id:-1}).skip(0).limit(1);
    @Override
    public Long findByMaxNum() {
        try {
            Query query = new Query();
            query.with(Sort.by(new Sort.Order(Sort.Direction.DESC, "num")));
            return Optional.ofNullable(mongoTemplate.findOne(query, IntegralData.class).getNum()).orElse(0L);
        } catch (Exception ex) {
            log.error("findByMaxNum {} ", LogUtil.getClassMethodName());
            ex.printStackTrace();
            return 0L;
        }
    }
}
