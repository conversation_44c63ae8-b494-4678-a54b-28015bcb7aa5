package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.impl.dao.WithdrawAccountDao;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.WithdrawAccountReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class WithdrawAccountReadServiceImpl implements WithdrawAccountReadService {
    @Autowired
    WithdrawAccountDao dao;

    @Override
    public Either<List<WithdrawAccount>> findByShopIdAndUserId(Long shopId, Long userId) {
        try {
            return Either.ok(dao.findByShopIdAndUserId(shopId, userId));
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<WithdrawAccount>> findById(Long id) {
        try {
            return Either.ok(Optional.ofNullable(dao.findById(id)));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} id:{}", LogUtil.getClassMethodName(), id);
            return Either.error("fail.to.query");
        }
    }

    @Override
    public Response<List<WithdrawAccount>> findByIds(List<Long> idList) {
        try {
            return Response.ok(dao.findByIds(idList));
        } catch (Exception ex) {
            log.error("WithdrawAccountReadServiceImpl.findByIds error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<WithdrawAccount>> findAccount(Long shopId, Long userId, String from, String account) {
        try {
            return Response.ok(dao.find(shopId, userId, from, account));
        } catch (Exception ex) {
            log.error("WithdrawAccountReadServiceImpl.findAccount error, userId={}, account={}", userId, account, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
