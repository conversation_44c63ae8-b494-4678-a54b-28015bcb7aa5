package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.impl.dao.WithDrawProfitApplyDao;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.domain.AbstractWithdrawApplyDomain;
import moonstone.order.service.WithDrawProfitApplyWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WithDrawProfitApplyWriteServiceImpl implements WithDrawProfitApplyWriteService {
    @Autowired
    private WithDrawProfitApplyDao withDrawProfitApplyDao;
    @Autowired
    private BalanceDetailManager balanceDetailManager;

    @Override
    public Response<Boolean> create(WithDrawProfitApply withDrawProfitApply) {
        try {
            return Response.ok(withDrawProfitApplyDao.create(withDrawProfitApply));
        } catch (Exception ex) {
            log.error("[WithDrawProfitApply](write) create failed by {}", withDrawProfitApply);
            ex.printStackTrace();
            return Response.fail("fail.create.withDrawProfitApply");
        }
    }

    @Override
    public Response<Boolean> update(WithDrawProfitApply withDrawProfitApply) {
        try {
            return Response.ok(withDrawProfitApplyDao.update(withDrawProfitApply));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("[WithDrawProfitApply](write) update failed by {}", withDrawProfitApply == null ? "null" : withDrawProfitApply.toString());
            return Response.fail("fail.update.withDrawProfitApply");
        }
    }

    @Override
    public Either<Boolean> fulfillServiceFee(WithDrawProfitApply withDrawProfitApply, long serviceFee) {
        try {
            return Either.ok(withDrawProfitApplyDao.fulfillServiceFee(withDrawProfitApply.getId(), serviceFee));
        } catch (Exception ex) {
            log.error("{} fail to fullFill[{}] service fee [{}]", LogUtil.getClassMethodName(), withDrawProfitApply.getId(), serviceFee, ex);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> paidAt(String paySerialNo, LocalDateTime paidAt) {
        try {
            WithDrawProfitApply apply = withDrawProfitApplyDao.findByPaySerialId(paySerialNo);
            if (apply.isPaid()) {
                return Either.ok(false);
            }
            AbstractWithdrawApplyDomain.build(apply).requireQuery(false);
            apply.bePaid(apply.getPaidType(), paySerialNo);
            apply.setWithDrawAt(Date.from(paidAt.atZone(ZoneId.systemDefault()).toInstant()));
            apply.getExtra().put("realPaid", "true");
            withDrawProfitApplyDao.update(apply);
            return Either.ok(true);
        } catch (Exception exception) {
            log.error("{} fail to pay the withdraw[paySerial=>{}]", LogUtil.getClassMethodName(), paySerialNo, exception);
            return Either.error(exception.getMessage());
        }
    }

    @Override
    public Either<Boolean> reject(String paySerialNo, LocalDateTime rejectAt, String reason) {
        try {
            WithDrawProfitApply apply = withDrawProfitApplyDao.findByPaySerialId(paySerialNo);
            if (apply.isReject()) {
                return Either.ok(false);
            }
            if (apply.isPaid()) {
                return Either.error(Translate.of("订单已经被支付无法回滚"));
            }
            apply.initAuth();
            AbstractWithdrawApplyDomain.build(apply).requireQuery(false);
            if (!balanceDetailManager.rejectWithDraw(0L, apply, reason)) {
                throw new RuntimeException(Translate.of("提现失败 同时拒绝失败"));
            }
            return Either.ok(true);
        } catch (Exception exception) {
            log.error("{} fail to reject the withdraw [paySerial=>{}], reson[{}]", LogUtil.getClassMethodName(), paySerialNo, reason, exception);
            return Either.error(exception.getMessage());
        }
    }

    @Override
    public Either<Boolean> error(String paySerialNo, String reason, Boolean requireQuery) {
        try {
            AbstractWithdrawApplyDomain withdrawApplyDomain = AbstractWithdrawApplyDomain.build(withDrawProfitApplyDao.findByPaySerialId(paySerialNo));
            if (withdrawApplyDomain.isError()){
                return Either.ok(false);
            }
            withdrawApplyDomain.error(reason);
            if (requireQuery) {
                withdrawApplyDomain.requireQuery(true);
            }
            if (!withDrawProfitApplyDao.update(withdrawApplyDomain.buildUpdate())) {
                throw new RuntimeException("update failed");
            }
            return Either.ok(true);
        } catch (Exception exception) {
            log.error("{} fail to mark apply error [{}} reason [{}]", LogUtil.getClassMethodName(), paySerialNo, reason, exception);
            return Either.error(exception);
        }
    }
}
