package moonstone.order.impl.service;

import com.danding.common.search.PageParam;
import com.danding.common.search.Pagination;
import com.danding.common.search.SearchResult;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.IntegralDataStatus;
import moonstone.order.service.IntegralDataStatusReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/21 10:08
 */
@Slf4j
@Service
@RpcProvider
public class IntegralDataStatusReadServiceImpl implements IntegralDataStatusReadService {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Optional<IntegralDataStatus>> findByCode(String code, Integer status) {
        try {
            Query query = new Query(Criteria.where("status").is(status).andOperator(Criteria.where("code").is(code)));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralDataStatus.class)));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<IntegralDataStatus>> findIntegralDataStatusByCode(String codeFlag) {
        try {
            Query query = new Query(Criteria.where("code").is(codeFlag));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralDataStatus.class)));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName(), codeFlag);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<IntegralDataStatus>> findByLimitOneMaxTime() {
        try {
            Query query = new Query();
            query.with(Sort.by(Sort.Direction.DESC, "createAt"));
            List<IntegralDataStatus> integralDataStatusList = mongoTemplate.find(query, IntegralDataStatus.class);
            if (integralDataStatusList == null || integralDataStatusList.isEmpty()) {
                return Either.error("fail");
            }
            return Either.ok(Optional.ofNullable(integralDataStatusList.get(0)));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName());
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<List<IntegralDataStatus>> findBystatus(int status) {
        try {
            Query query = new Query(Criteria.where("status").is(status));
            return Either.ok(mongoTemplate.find(query, IntegralDataStatus.class));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName(), status);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<IntegralDataStatus>> findByCodeAndShopId(String batch, long shopId) {
        try {
            Query query = new Query(Criteria.where("code").is(batch).andOperator(Criteria.where("shopId").is(shopId)));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralDataStatus.class)));
        } catch (Exception ex) {
            log.error("{}  code:{},shopId:{}", LogUtil.getClassMethodName(), batch, shopId);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public SearchResult<IntegralDataStatus> getIntegralDataStatusReadList(PageParam pageParam, Map<String, String> map) {
        Query query = new Query();
        // 组装筛选条件
        if (!ObjectUtils.isEmpty(map.get("shopId"))) {
            Criteria criteria = Criteria.where("shopId").is(Long.valueOf(map.get("shopId")));
            query.addCriteria(criteria);
        }
        if (!ObjectUtils.isEmpty(map.get("status"))) {
            Criteria criteria1 = Criteria.where("status").is(Integer.valueOf(map.get("status")));
            query.addCriteria(criteria1);
        }

        // 分页查找
        query.skip(pageParam.getOffset());
        query.limit(pageParam.getSize());
        // 排序
        query.with(Sort.by(Sort.Direction.DESC, "createAt"));
        int total = (int) mongoTemplate.count(query, IntegralDataStatus.class);
        List<IntegralDataStatus> data = mongoTemplate.find(query, IntegralDataStatus.class);
        SearchResult<IntegralDataStatus> result = new SearchResult<>();
        result.setPagination(new Pagination(pageParam, total));
        result.setResult(data);
        return result;
    }

    @Override
    public Either<List<IntegralDataStatus>> findByType(int type) {
        try {
            Query query = new Query(Criteria.where("type").is(type));
            return Either.ok(mongoTemplate.find(query, IntegralDataStatus.class));
        } catch (Exception ex) {
            log.error("{}  code:{}", LogUtil.getClassMethodName(), type);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Optional<IntegralDataStatus>> findByCodeAndShopIdAndType(Long shopId, String code, int type) {
        try {
            Query query = new Query(Criteria.where("code").is(code).andOperator(Criteria.where("shopId").is(shopId), Criteria.where("type").is(type)));
            return Either.ok(Optional.ofNullable(mongoTemplate.findOne(query, IntegralDataStatus.class)));
        } catch (Exception ex) {
            log.error("{}  code:{},shopId:{}", LogUtil.getClassMethodName(), code, shopId);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
