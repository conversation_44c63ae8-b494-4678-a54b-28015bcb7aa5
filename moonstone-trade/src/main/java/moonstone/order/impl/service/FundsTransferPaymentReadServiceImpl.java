package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.enu.FundsTransferPaymentRelatedTypeEnum;
import moonstone.order.impl.dao.FundsTransferPaymentDao;
import moonstone.order.model.FundsTransferPayment;
import moonstone.order.service.FundsTransferPaymentReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class FundsTransferPaymentReadServiceImpl implements FundsTransferPaymentReadService {

    @Resource
    private FundsTransferPaymentDao fundsTransferPaymentDao;

    @Override
    public Response<List<FundsTransferPayment>> findBy(Long relatedOrderId, FundsTransferPaymentRelatedTypeEnum type) {
        try {
            if (relatedOrderId == null || type == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(fundsTransferPaymentDao.findBy(relatedOrderId, type.getCode()));
        } catch (Exception ex) {
            log.error("FundsTransferPaymentReadServiceImpl.findBy error, relatedOrderId={}, type={}",
                    relatedOrderId, type.getCode(), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<FundsTransferPayment> findById(Long id) {
        try {
            if (id == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(fundsTransferPaymentDao.findById(id));
        } catch (Exception ex) {
            log.error("FundsTransferPaymentReadServiceImpl.findById error, id={}", id, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
