package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.IntegralDataStatus;
import moonstone.order.service.IntegralDataStatusWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/21 10:08
 */
@Slf4j
@Service
@RpcProvider
public class IntegralDataStatusWriteServiceImpl implements IntegralDataStatusWriteService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Boolean> updateIntegralDataStatus(String code, int value) {
        try {
            Query query = Query.query(Criteria.where("code").is(code));
            Update update = new Update();
            update.set("status", value);
            mongoTemplate.updateFirst(query, update, IntegralDataStatus.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  code:{} ", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateIntegralDataStatusByCodeAndShopId(Long shopId, String code, int type) {
        try {
            Query query = Query.query(Criteria.where("code").is(code).andOperator(Criteria.where("shopId").is(shopId)));
            Update update = new Update();
            update.set("type", type);
            mongoTemplate.updateFirst(query, update, IntegralDataStatus.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  code:{} ", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateIntegralDataStatusAndType(String code, int status, int type) {
        try {
            Query query = Query.query(Criteria.where("code").is(code));
            Update update = new Update();
            update.set("status", status);
            update.set("type", type);
            mongoTemplate.updateFirst(query, update, IntegralDataStatus.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  code:{} ", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> createIntegralDataStatus(IntegralDataStatus integralDataStatus) {
        try {
            mongoTemplate.insert(integralDataStatus);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} entity:{}", LogUtil.getClassMethodName(), integralDataStatus);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
