package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.ProfitWithdrawRecordDao;
import moonstone.order.model.ProfitWithdrawRecord;
import moonstone.order.model.result.ProfitWithdrawRecordInfoDO;
import moonstone.order.service.ProfitWithdrawRecordReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ProfitWithdrawRecordReadServiceImpl implements ProfitWithdrawRecordReadService {

    @Autowired
    private ProfitWithdrawRecordDao profitWithdrawRecordDao;

    @Override
    public Response<List<ProfitWithdrawRecord>> findByProfitIdsAndStatus(List<Long> profitIds, List<Integer> statusList) {
        try {
            if (CollectionUtils.isEmpty(profitIds)) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(profitWithdrawRecordDao.findByProfitIdsAndStatus(profitIds, statusList));
        } catch (Exception ex) {
            log.error("ProfitWithdrawRecordReadServiceImpl.findByProfitIdsAndStatus error ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ProfitWithdrawRecord>> findByProfitIds(List<Long> profitIds) {
        try {
            if (CollectionUtils.isEmpty(profitIds)) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(profitWithdrawRecordDao.findByProfitIds(profitIds));
        } catch (Exception ex) {
            log.error("ProfitWithdrawRecordReadServiceImpl.findByProfitIds error ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ProfitWithdrawRecordInfoDO>> findProfitByStatus(Long shopId, Long userId, Integer status,
                                                                         Date createdStartAt, Date createdEndAt) {
        try {
            return Response.ok(profitWithdrawRecordDao.findProfitByStatus(shopId, userId, status, createdStartAt, createdEndAt));
        } catch (Exception ex) {
            log.error("ProfitWithdrawRecordReadServiceImpl.findProfitByStatus error ", ex);
            return Response.fail(ex.getMessage());
        }
    }
}
