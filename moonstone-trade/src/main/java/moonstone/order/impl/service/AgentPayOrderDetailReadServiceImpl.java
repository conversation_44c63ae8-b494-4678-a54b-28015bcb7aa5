package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.AgentPayOrderDetailDao;
import moonstone.order.model.AgentPayOrderDetail;
import moonstone.order.service.AgentPayOrderDetailReadService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AgentPayOrderDetailReadServiceImpl implements AgentPayOrderDetailReadService {

    @Resource
    private AgentPayOrderDetailDao agentPayOrderDetailDao;

    @Override
    public Response<List<AgentPayOrderDetail>> findByAgentPayOrderId(Long agentPayOrderId) {
        try {
            if (agentPayOrderId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(agentPayOrderDetailDao.findByAgentPayOrderId(agentPayOrderId));
        } catch (Exception ex) {
            log.error("AgentPayOrderDetailReadServiceImpl.findByAgentPayOrderId error, agentPayOrderId={}", agentPayOrderId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<AgentPayOrderDetail>> findByAgentPayOrderIds(List<Long> agentPayOrderIds) {
        try {
            if (CollectionUtils.isEmpty(agentPayOrderIds)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(agentPayOrderDetailDao.findByAgentPayOrderIds(agentPayOrderIds));
        } catch (Exception ex) {
            log.error("AgentPayOrderDetailReadServiceImpl.findByAgentPayOrderIds error, agentPayOrderIds={}",
                    JSON.toJSONString(agentPayOrderIds), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, List<AgentPayOrderDetail>> findMap(List<Long> agentPayOrderIds) {
        var list = findByAgentPayOrderIds(agentPayOrderIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(AgentPayOrderDetail::getAgentPayOrderId));
    }
}
