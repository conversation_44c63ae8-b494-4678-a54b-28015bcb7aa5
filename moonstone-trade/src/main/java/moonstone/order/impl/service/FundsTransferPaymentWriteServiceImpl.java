package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.FundsTransferPaymentDao;
import moonstone.order.model.FundsTransferPayment;
import moonstone.order.service.FundsTransferPaymentWriteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class FundsTransferPaymentWriteServiceImpl implements FundsTransferPaymentWriteService {

    @Resource
    private FundsTransferPaymentDao fundsTransferPaymentDao;

    @Override
    public Response<Boolean> create(FundsTransferPayment parameter) {
        try {
            if (parameter == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(fundsTransferPaymentDao.insertSelective(parameter) > 0);
        } catch (Exception ex) {
            log.error("FundsTransferPaymentWriteServiceImpl.create error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> update(FundsTransferPayment parameter) {
        try {
            if (parameter == null || parameter.getId() == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(fundsTransferPaymentDao.update(parameter));
        } catch (Exception ex) {
            log.error("FundsTransferPaymentWriteServiceImpl.update error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
