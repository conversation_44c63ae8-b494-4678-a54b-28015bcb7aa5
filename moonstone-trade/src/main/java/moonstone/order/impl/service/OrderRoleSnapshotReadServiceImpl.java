package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.impl.dao.OrderRoleSnapshotDao;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.service.OrderRoleSnapshotReadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderRoleSnapshotReadServiceImpl implements OrderRoleSnapshotReadService {

    @Autowired
    private OrderRoleSnapshotDao orderRoleSnapshotDao;

    @Override
    public Response<List<OrderRoleSnapshot>> findByShopOrderIds(List<Long> shopOrderIds, OrderRoleSnapshotOrderTypeEnum orderType) {
        try {
            if (CollectionUtils.isEmpty(shopOrderIds) || orderType == null) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(orderRoleSnapshotDao.findByShopOrderIds(shopOrderIds, orderType.getCode()));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findByShopOrderIds error, shopOrderIds={}",
                    JSON.toJSONString(shopOrderIds), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<OrderRoleSnapshot> findByShopOrderIdAndRole(Long shopOrderId, OrderRoleSnapshotOrderTypeEnum orderType,
                                                                SubStoreUserIdentityEnum role) {
        try {
            if (shopOrderId == null || role == null || orderType == null) {
                return Response.ok(null);
            }

            var list = orderRoleSnapshotDao.findByShopOrderIds(Lists.newArrayList(shopOrderId), orderType.getCode());
            if (CollectionUtils.isEmpty(list)) {
                return Response.ok(null);
            }

            return Response.ok(list.stream()
                    .filter(snapshot -> role.getCode().equals(snapshot.getUserRole()))
                    .findAny()
                    .orElse(null));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findByShopOrderIdAndRole error, shopOrderId={}",
                    JSON.toJSONString(shopOrderId), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, List<OrderRoleSnapshot>> findMapByShopOrderIds(List<Long> shopOrderIds, OrderRoleSnapshotOrderTypeEnum orderType) {
        try {
            if (CollectionUtils.isEmpty(shopOrderIds)) {
                return Collections.emptyMap();
            }

            var list = findByShopOrderIds(shopOrderIds, orderType).getResult();
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyMap();
            }

            return list.stream().collect(Collectors.groupingBy(OrderRoleSnapshot::getShopOrderId));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findMapByShopOrderIds error, shopOrderIds={}",
                    JSON.toJSONString(shopOrderIds), ex);
            return Collections.emptyMap();
        }
    }

    @Override
    public Response<List<Long>> findUserIdsByNameLike(Long shopId, String name, OrderRoleSnapshotOrderTypeEnum orderType,
                                                      SubStoreUserIdentityEnum userRole) {
        try {
            if (shopId == null || StringUtils.isBlank(name) || orderType == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(orderRoleSnapshotDao.findUserIdsLike(shopId, orderType.getCode(),
                    userRole != null ? userRole.getCode() : null, name, null));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findUserIds error, shopId={}, name={}, orderType={}",
                    shopId, name, orderType, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<Long>> findUserIdsByMobile(Long shopId, String mobile, OrderRoleSnapshotOrderTypeEnum orderType,
                                                    SubStoreUserIdentityEnum userRole) {
        try {
            if (shopId == null || StringUtils.isBlank(mobile) || orderType == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(orderRoleSnapshotDao.findUserIds(shopId, orderType.getCode(),
                    userRole != null ? userRole.getCode() : null, null, mobile));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findUserIdsByMobile error, shopId={}, mobile={}, orderType={}",
                    shopId, mobile, orderType, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<Long>> findUserIdsLike(Long shopId, String name, String mobile,
                                                OrderRoleSnapshotOrderTypeEnum orderType, SubStoreUserIdentityEnum userRole) {
        try {
            if (shopId == null || orderType == null ||
                    (StringUtils.isBlank(mobile) && StringUtils.isBlank(name))) {
                return Response.fail("入参缺失");
            }

            return Response.ok(orderRoleSnapshotDao.findUserIdsLike(shopId, orderType.getCode(),
                    userRole != null ? userRole.getCode() : null, name, mobile));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findUserIdsByMobile error, shopId={}, mobile={}, orderType={}",
                    shopId, mobile, orderType, ex);
            return Response.fail(ex.getMessage());
        }
    }


    @Override
    public Response<OrderRoleSnapshot> findByUserIdAndShopId(Long userId, Long shopId) {
        try {
            OrderRoleSnapshot orderRoleSnapshot = orderRoleSnapshotDao.findByUserIdAndShopId(userId, shopId);
            if (orderRoleSnapshot != null) {
                return Response.ok(orderRoleSnapshot);
            } else {
                return Response.ok(new OrderRoleSnapshot());
            }
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findByUserId error, userId={}, shopId={}",
                    userId, shopId);
            return Response.fail(ex.getMessage());
        }

    }

    @Override
    public Response<List<OrderRoleSnapshot>> findAllByPage(Long maxId, int page, int pageSize) {
        try {
            if (maxId == null) {
                return Response.fail("须指定最大id");
            }

            return Response.ok(orderRoleSnapshotDao.findAllByPage(maxId, page, pageSize));
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotReadServiceImpl.findAllByPage error, maxId={}, page={}, pageSize={}",
                    maxId, page, pageSize);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public List<OrderRoleSnapshot> listByShopOrderIdAndSnapshotType(Long orderId, OrderRoleSnapshotOrderTypeEnum orderRoleSnapshotOrderTypeEnum) {
        return orderRoleSnapshotDao.listByShopOrderIdAndSnapshotType(orderId, orderRoleSnapshotOrderTypeEnum.getCode());
    }
}
