package moonstone.order.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.ShopWithdrawPayInfoDao;
import moonstone.order.model.ShopWithdrawPayInfo;
import moonstone.order.service.ShopWithdrawPayInfoWriteService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShopWithdrawPayInfoWriteServiceImpl implements ShopWithdrawPayInfoWriteService {

    @Autowired
    private ShopWithdrawPayInfoDao shopWithdrawPayInfoDao;

    @Override
    public Response<Boolean> updateCertFilePath(Long payInfoId, String filePath) {
        try {
            if (payInfoId == null || StringUtils.isBlank(filePath)) {
                return Response.fail("入参皆不能为空");
            }

            var parameter = new ShopWithdrawPayInfo();
            parameter.setId(payInfoId);
            parameter.setCertFilePath(filePath);

            return Response.ok(shopWithdrawPayInfoDao.update(parameter));
        } catch (Exception ex) {
            log.error("ShopWithdrawPayInfoWriteServiceImpl.updateCertFilePath error, payInfoId={}, filePath={}",
                    payInfoId, filePath, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
