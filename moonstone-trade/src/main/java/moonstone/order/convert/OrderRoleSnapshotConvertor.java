package moonstone.order.convert;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.Json;
import moonstone.order.enu.OrderRoleSnapshotExtra;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class OrderRoleSnapshotConvertor {

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 构造导购快照
     *
     * @param guiderUserId
     * @param shopId
     * @return
     */
    public OrderRoleSnapshot convert(Long guiderUserId, Long shopId, Long buyerUserId) {
        var list = subStoreTStoreGuiderReadService.findByStoreGuiderUserId(guiderUserId, shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        var guider = list.get(0);

        OrderRoleSnapshot snapshot = new OrderRoleSnapshot();

        snapshot.setIsValid(DataValidEnum.VALID.getCode());
        snapshot.setMobile(guider.getStoreGuiderMobile());
        snapshot.setName(guider.getStoreGuiderNickname());
        snapshot.setShopId(shopId);

        snapshot.setUserId(guider.getStoreGuiderId());
        snapshot.setUserRole(SubStoreUserIdentityEnum.STORE_GUIDER.getCode());
        snapshot.setCreatedBy(buyerUserId);
        snapshot.setUpdatedBy(buyerUserId);

        return snapshot;
    }

    /**
     * 构造门店和服务商的快照
     *
     * @param subStoreId
     * @return
     */
    public List<OrderRoleSnapshot> convert(Long subStoreId, Long buyerUserId) {
        var subStore = subStoreReadService.findById(subStoreId).getResult();
        if (subStore == null) {
            return Collections.emptyList();
        }

        List<OrderRoleSnapshot> list = new ArrayList<>();

        //服务商
        var serviceProviderSnapshot = convertServiceProvider(subStore, buyerUserId);

        //门店
        var subStoreSnapshot = convertSubStore(subStore, buyerUserId, serviceProviderSnapshot);

        list.add(subStoreSnapshot);
        if (serviceProviderSnapshot != null) {
            list.add(serviceProviderSnapshot);
        }

        return list;
    }

    private OrderRoleSnapshot convertServiceProvider(SubStore subStore, Long buyerUserId) {
        var serviceProvider = findServiceProvider(subStore);
        if(serviceProvider==null){
            return null;
        }

        OrderRoleSnapshot snapshot = new OrderRoleSnapshot();

        //将服务商的省市区放快照中
        snapshot.setProvince(serviceProvider.getProvince());
        snapshot.setCity(serviceProvider.getCity());
        snapshot.setCounty(serviceProvider.getCounty());

        snapshot.setIsValid(DataValidEnum.VALID.getCode());
        snapshot.setMobile(serviceProvider.getMobile());
        snapshot.setName(serviceProvider.getName());
        snapshot.setShopId(serviceProvider.getShopId());
        snapshot.setUserId(serviceProvider.getUserId());
        snapshot.setUserRole(SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode());
        snapshot.setCreatedBy(buyerUserId);
        snapshot.setUpdatedBy(buyerUserId);

        return snapshot;
    }

    private ServiceProvider findServiceProvider(SubStore subStore) {
        var storeRelation = find(subStore.getUserId(), subStore.getShopId());
        if (storeRelation == null) {
            return null;
        }

        return findByMongo(storeRelation.getRelationId(), subStore.getShopId());
    }

    private ServiceProvider findByMongo(Long userId, Long shopId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId))
                        .addCriteria(Criteria.where("userId").is(userId))
                , ServiceProvider.class, "serviceProvider");
    }

    private UserRelationEntity find(Long userId, Long shopId) {
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        criteria.setUserId(userId);
        criteria.setRelationIdA(Collections.singletonList(shopId));

        var page = userRelationEntityReadService.paging(criteria).getResult();
        if (page != null && !CollectionUtils.isEmpty(page.getData())) {
            return page.getData().get(0);
        }

        return null;
    }

    private OrderRoleSnapshot convertSubStore(SubStore subStore, Long buyerUserId,
                                              OrderRoleSnapshot serviceProviderSnapshot) {
        OrderRoleSnapshot snapshot = new OrderRoleSnapshot();

        //将所属服务商的名称和省份 放入ExtraJson中
        if (serviceProviderSnapshot != null) {
            Map<Object, Object> map = new HashMap<>();
            map.put(OrderRoleSnapshotExtra.serviceProviderName.name(), serviceProviderSnapshot.getName());
            map.put(OrderRoleSnapshotExtra.serviceProviderProvince.name(), serviceProviderSnapshot.getProvince());
            snapshot.setExtraJson(Json.toJson(map));
        }

        snapshot.setIsValid(DataValidEnum.VALID.getCode());
        snapshot.setMobile(subStore.getMobile());
        snapshot.setName(subStore.getName());
        snapshot.setShopId(subStore.getShopId());

        snapshot.setUserId(subStore.getUserId());
        snapshot.setUserRole(SubStoreUserIdentityEnum.SUB_STORE.getCode());
        snapshot.setCreatedBy(buyerUserId);
        snapshot.setUpdatedBy(buyerUserId);

        snapshot.setProvince(subStore.getProvince());
        snapshot.setCity(subStore.getCity());
        snapshot.setCounty(subStore.getCounty());

        return snapshot;
    }

    /**
     * moonstone.web.core.shop.model.ServiceProvider 的复制品，方便查询用而已
     */
    @Data
    private class ServiceProvider {
        String id;
        String name;
        Long userId;
        Long shopId;
        String mobile;
        String address;
        String authAddress;
        String province;
        String city;
        String county;
        String frontImg;
        String backImg;
        String businessImg;
        String foodSellAllowImg;
        Long relationId;
        Long createdAt;
    }
}
