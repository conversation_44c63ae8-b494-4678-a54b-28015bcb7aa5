/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.strategy;

import moonstone.order.model.SkuOrder;

import java.util.List;

/**
 * 默认是选择各子订单中状态的最大值
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-03
 */
public class DefaultShopOrderStatusStrategy implements ShopOrderStatusStrategy {
    /**
     * 根据各sku订单的状态来确定shopOrder的状态
     *
     * @param skuOrders              各子订单
     * @return 目标店铺订单状态
     */
    @Override
    public Integer status(List<SkuOrder> skuOrders) {
        Integer result = Integer.MIN_VALUE;
        for (SkuOrder skuOrder : skuOrders) {
            if(skuOrder.getStatus() > result){
                result = skuOrder.getStatus();
            }
        }
        return result;
    }
}
