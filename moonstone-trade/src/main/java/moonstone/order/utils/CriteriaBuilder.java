package moonstone.order.utils;

import com.google.common.base.Strings;
import io.terminus.common.model.PageInfo;
import io.terminus.common.utils.Splitters;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Desc: 查询map构造器
 * Mail: <EMAIL>
 * Data: 16/3/3
 * Author: yangzefeng
 */
public class CriteriaBuilder {

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormat.forPattern("yyyy-MM-dd");

    private final Map<String, Object> criteria;

    private CriteriaBuilder(Map<String, Object> criteria) {
        this.criteria = criteria;
    }

    public static class Builder {
        private final Map<String, Object> params;

        public Builder() {
            params = new HashMap<>();
        }

        public Builder buyerId (Long buyerId) {
            if (null != buyerId) {
                params.put("buyerId", buyerId);
            }
            return this;
        }

        public Builder shopId (Long shopId) {
            if (null != shopId) {
                params.put("shopId", shopId);
            }
            return this;
        }

        public Builder orderId(Long orderId) {
            if (null != orderId) {
                params.put("id", orderId);
            }
            return this;
        }

        public Builder orderIds (List<Long> orderIds) {
            if (!CollectionUtils.isEmpty(orderIds)) {
                params.put("ids", orderIds);
            }
            return this;
        }

        public Builder parentIds (List<Long> parentIds) {
            if (!CollectionUtils.isEmpty(parentIds)) {
                params.put("parentIds", parentIds);
            }
            return this;
        }

        public Builder startAt (String startAt) {
            if (!Strings.isNullOrEmpty(startAt)) {
                Date start = DATE_TIME_FORMAT.parseDateTime(startAt).withTimeAtStartOfDay().toDate();
                params.put("startAt", start);
            }
            return this;
        }

        public Builder endAt (String endAt) {
            if (!Strings.isNullOrEmpty(endAt)) {
                Date end = DATE_TIME_FORMAT.parseDateTime(endAt).plusDays(1).withTimeAtStartOfDay().toDate();
                params.put("endAt", end);
            }
            return this;
        }

        public Builder status(String status) {
            if (!Strings.isNullOrEmpty(status)) {
                params.put("status", Splitters.COMMA.omitEmptyStrings().trimResults().splitToList(status));
            }
            return this;
        }

        public Builder pageInfo(Integer pageNo, Integer size) {
            PageInfo pageInfo = PageInfo.of(pageNo, size);
            params.put("offset", pageInfo.getOffset());
            params.put("limit", pageInfo.getLimit());
            return this;
        }

        public CriteriaBuilder build () {
            return new CriteriaBuilder(this.params);
        }
    }

    public Map<String, Object> toMap() {
        return criteria;
    }
}
