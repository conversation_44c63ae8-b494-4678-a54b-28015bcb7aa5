package moonstone.integral.impl.dao;

import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.integral.model.IntegralActivity;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class IntegralActivityDao extends MyBatisDao<IntegralActivity> {
    public IntegralActivity findByItemCode(String itemCode) {
        return getSqlSession().selectOne(sqlId("findByItemCode"), ImmutableBiMap.of("itemCode", itemCode));
    }

    public List<IntegralActivity> findByShopId(Long shopId) {
        return getSqlSession().selectList(sqlId("findByShopId"), ImmutableBiMap.of("shopId", shopId));
    }
}
