package moonstone.weCart.impl.service;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.weCart.impl.dao.WeCartItemDao;
import moonstone.weCart.model.WeCartItem;
import moonstone.weCart.service.WeCartReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by CaiZhy on 2018/12/19.
 */
@Slf4j
@Service
@RpcProvider
public class WeCartReadServiceImpl implements WeCartReadService {
    private final WeCartItemDao weCartItemDao;

    @Autowired
    public WeCartReadServiceImpl(WeCartItemDao weCartItemDao) {
        this.weCartItemDao = weCartItemDao;
    }

    /**
     * 查询当前登录用户购物车（某微分销店铺，某商家）的sku的总数
     *
     * @param userId 当前登录用户
     * @param weShopId 微分销店铺id （查全部则null）
     * @param shopId 商家id （查全部则null）
     */
    @Override
    public Response<Integer> countInWeShopIdAndShopId(Long userId, Long weShopId, Long shopId) {
        try {
            Integer total = weCartItemDao.countCartQuantity(userId, weShopId, shopId);
            return Response.ok(MoreObjects.firstNonNull(total, 0));
        } catch (Exception e) {
            log.error("fail to get weCart count by user(Id={}), weShop(id={}), shopId={}, cause:{}",
                    userId, weShopId, shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weCart.count.query.fail");
        }
    }

    /**
     * 查询当前登录用户购物车（某微分销店铺）的sku的总数
     *
     * @param userId 当前登录用户
     * @param weShopId 微分销店铺id （查全部则null）
     */
    @Override
    public Response<Integer> countInWeShopId(Long userId, Long weShopId) {
        try {
            Integer total = weCartItemDao.countCartQuantity(userId, weShopId, null);
            return Response.ok(MoreObjects.firstNonNull(total, 0));
        } catch (Exception e) {
            log.error("fail to get weCart count by user(Id={}), weShop(id={}), cause:{}",
                    userId, weShopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weCart.count.query.fail");
        }
    }

    /**
     * 列出用户某微分销店铺某商家的所有购物车商品记录
     *
     * @param buyerId    买家id
     * @param weShopId     微分销店铺id
     * @param shopId    商家id
     * @return 购物车商品记录
     */
    @Override
    public Response<List<WeCartItem>> findByUserIdAndWeShopIdAndShopId(Long buyerId, Long weShopId, Long shopId) {
        try {
            List<WeCartItem> weCartItems = weCartItemDao.list(ImmutableMap.of("buyerId", buyerId, "weShopId", weShopId, "shopId", shopId));
            return Response.ok(weCartItems);
        } catch (Exception e) {
            log.error("fail to list weCart by weShopId={}, shopId={} for user(id={}), cause:{}", weShopId, shopId, buyerId, Throwables.getStackTraceAsString(e));
            return Response.fail("weCart.list.fail");
        }
    }

    /**
     * 列出用户某微分销店铺的所有购物车商品记录
     *
     * @param buyerId    买家id
     * @param weShopId     微分销店铺id
     * @return 购物车商品记录
     */
    @Override
    public Response<List<WeCartItem>> findByUserIdAndWeShopId(Long buyerId, Long weShopId) {
        try {
            List<WeCartItem> weCartItems = weCartItemDao.list(ImmutableMap.of("buyerId", buyerId, "weShopId", weShopId));
            return Response.ok(weCartItems);
        } catch (Exception e) {
            log.error("fail to list weCart by weShopId={} for user(id={}), cause:{}", weShopId, buyerId, Throwables.getStackTraceAsString(e));
            return Response.fail("weCart.list.fail");
        }
    }

    @Override
    public Response<List<WeCartItem>> findByUserIdAndSkuId(Long buyerId, Long skuId) {
        try {
            List<WeCartItem> weCartItems = weCartItemDao.list(ImmutableMap.of("buyerId", buyerId, "skuId", skuId));
            return Response.ok(weCartItems);
        } catch (Exception e) {
            log.error("fail to list weCart for user(id={}) by sku(id={}), cause:{}", buyerId, skuId, Throwables.getStackTraceAsString(e));
            return Response.fail("weCart.list.fail");
        }
    }
}
