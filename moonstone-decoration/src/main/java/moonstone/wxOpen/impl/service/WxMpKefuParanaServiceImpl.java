package moonstone.wxOpen.impl.service;

import com.github.kevinsawicki.http.HttpRequest;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import moonstone.wxOpen.impl.storage.WxOpenTokenStorage;
import moonstone.wxOpen.service.WxMpKefuParanaService;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created by CaiZhy on 2018/10/31.
 */
@Slf4j
@Service
@RpcProvider
public class WxMpKefuParanaServiceImpl implements WxMpKefuParanaService {
    @RpcConsumer
    private WxOpenParanaComponentService wxOpenParanaComponentService;

    @Autowired
    private WxOpenTokenStorage wxOpenTokenStorage;

    private final int retrySleepMillis = 1000;
    private final int maxRetryTimes = 5;

    public boolean sendKefuMessage(WxMpKefuMessage message, String appId) throws Exception {
        String responseContent = this.post("https://api.weixin.qq.com/cgi-bin/message/custom/send", message.toJson(), appId);
        return responseContent != null;
    }

    public String post(String uri, String postData, String appId) throws Exception {
        int retryTimes = 0;

        while (true) {
            try {
                return this.postInternal(uri, postData, appId);
            } catch (WxErrorException var10) {
                if (retryTimes + 1 > this.maxRetryTimes) {
                    log.warn("重试达到最大次数【{}】", this.maxRetryTimes);
                    throw new RuntimeException("微信服务端异常，超出重试次数");
                }

                WxError error = var10.getError();
                if (error.getErrorCode() != -1) {
                    throw var10;
                }

                int sleepMillis = this.retrySleepMillis * (1 << retryTimes);

                try {
                    log.warn("微信系统繁忙，{} ms 后重试(第{}次)", sleepMillis, retryTimes + 1);
                    Thread.sleep(sleepMillis);
                } catch (InterruptedException var9) {
                    throw new RuntimeException(var9);
                }

                if (retryTimes++ >= this.maxRetryTimes) {
                    log.warn("重试达到最大次数【{}】", this.maxRetryTimes);
                    throw new RuntimeException("微信服务端异常，超出重试次数");
                }
            }
        }
    }

    public String postInternal(String uri, String postData, String appId) throws Exception {
        if (uri.contains("access_token=")) {
            throw new IllegalArgumentException("uri参数中不允许有access_token: " + uri);
        } else {
            String accessToken = this.getAccessToken(appId, false);
            String uriWithAccessToken = uri + (uri.contains("?") ? "&" : "?") + "access_token=" + accessToken;

            try {
                String result = this.realPost(uriWithAccessToken, postData);
                log.debug("\n【请求地址】: {}\n【请求参数】：{}\n【响应数据】：{}", uriWithAccessToken, postData, result);
                return result;
            } catch (WxErrorException var9) {
                WxError error = var9.getError();
                if (error.getErrorCode() == 42001 || error.getErrorCode() == 40001 || error.getErrorCode() == 40014) {
                    wxOpenTokenStorage.expireAuthorizerAccessToken(appId);
                    if (wxOpenTokenStorage.autoRefreshToken()) {
                        return this.realPost(uri, postData);
                    }
                }

                if (error.getErrorCode() != 0) {
                    WxMpKefuParanaServiceImpl.log.error("\n【请求地址】: {}\n【请求参数】：{}\n【错误信息】：{}", uriWithAccessToken, postData, error);
                    throw new WxErrorException(error, var9);
                } else {
                    return null;
                }
            } catch (Exception e) {
                WxMpKefuParanaServiceImpl.log.error("\n【请求地址】: {}\n【请求参数】：{}\n【异常信息】：{}", uriWithAccessToken, postData, e.getMessage());
                throw new WxErrorException(WxError.builder().errorMsg(e.getMessage()).build(), e);
            }
        }
    }

    private String realPost(String uri, String postData) throws WxErrorException {
        log.debug("[{}] postData: {}", uri, postData);
        HttpRequest request = HttpRequest.post(uri).contentType("application/json", "utf-8").send(postData);
        if (request.ok()) {
            String result = request.body();
            log.debug("[{}] result: {}", uri, result);
            Map<String, Object> map = JsonMapper.nonDefaultMapper().fromJson(result, Map.class);
            if (map.get("errcode") != null && (int) map.get("errcode") > 0) {
                throw new WxErrorException(WxError.builder().errorCode((int) map.get("errcode")).errorMsg(map.get("errmsg").toString()).build());
            }
            return result;
        } else {
            log.error("[{}] HttpRequest failed, postData( {})", uri, postData);
            throw new JsonResponseException(500, "http.request.fail");
        }
    }

    public String getAccessToken(String appId, boolean forceRefresh) throws Exception {
        return wxOpenParanaComponentService.getAuthorizerAccessToken(appId, forceRefresh);
    }
}
