CREATE TABLE `parana_addresses` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) DEFAULT NULL COMMENT '父级ID',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `level` int(11) DEFAULT NULL COMMENT '级别',
  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
  `english_name` varchar(100) DEFAULT NULL COMMENT '英文名',
  `unicode_code` varchar(200) DEFAULT NULL COMMENT 'ASCII码',
  `order_no` varchar(32) DEFAULT NULL COMMENT '排序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_alipay_trans
# ------------------------------------------------------------

CREATE TABLE `parana_alipay_trans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `balance` varchar(32) DEFAULT NULL COMMENT '账户余额',
  `bank_account_name` varchar(32) DEFAULT NULL COMMENT '银行账户名称',
  `bank_account_no` varchar(32) DEFAULT NULL COMMENT '银行账户',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '银行名',
  `buyer_name` varchar(127) DEFAULT NULL COMMENT '买家姓名',
  `buyer_account` varchar(32) DEFAULT NULL COMMENT '买家账户',
  `currency` varchar(16) DEFAULT NULL COMMENT '货币代码(156:人民币)',
  `deposit_bank_no` varchar(32) DEFAULT NULL COMMENT '充值网银流水',
  `income` varchar(32) DEFAULT NULL COMMENT '收入金额',
  `iw_account_log_id` varchar(32) DEFAULT NULL COMMENT '帐务流水',
  `memo` varchar(127) DEFAULT NULL COMMENT '备注信息',
  `merchant_out_order_no` varchar(64) DEFAULT NULL COMMENT '外部交易编号（订单号）',
  `other_account_email` varchar(127) DEFAULT NULL COMMENT '帐务对方邮箱',
  `other_account_fullname` varchar(127) DEFAULT NULL COMMENT '帐务对方全称',
  `other_user_id` varchar(32) DEFAULT NULL COMMENT '帐务对方支付宝用户号',
  `outcome` varchar(32) DEFAULT NULL COMMENT '支出金额',
  `partner_id` varchar(32) DEFAULT NULL COMMENT '合作者身份id',
  `seller_account` varchar(32) DEFAULT NULL COMMENT '买家支付宝人民币支付帐号(user_id+0156)',
  `seller_fullname` varchar(64) DEFAULT NULL COMMENT '卖家姓名',
  `service_fee` varchar(32) DEFAULT NULL COMMENT '交易服务费',
  `service_fee_ratio` varchar(16) DEFAULT NULL COMMENT '交易服务费率',
  `total_fee` varchar(32) DEFAULT NULL COMMENT '交易总金额',
  `trade_no` varchar(32) DEFAULT NULL COMMENT '支付宝交易流水',
  `trade_refund_amount` varchar(32) DEFAULT NULL COMMENT '累计退款金额',
  `trans_account` varchar(32) DEFAULT NULL COMMENT '帐务本方支付宝人民币资金帐号(user_id+0156)',
  `trans_code_msg` varchar(16) DEFAULT NULL COMMENT '业务类型',
  `trans_date` varchar(32) DEFAULT NULL COMMENT '交易发生日期',
  `trans_out_order_no` varchar(32) DEFAULT NULL COMMENT '商户订单号',
  `sub_trans_code_msg` varchar(32) DEFAULT NULL COMMENT '子业务类型代码，详见文档',
  `sign_product_name` varchar(32) DEFAULT NULL COMMENT '签约产品',
  `rate` varchar(16) DEFAULT NULL COMMENT '费率',
  `trade_at` datetime DEFAULT NULL COMMENT '交易时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_eat_iw_account_log_id` (`iw_account_log_id`),
  KEY `idx_eat_trans_no` (`trade_no`),
  KEY `idx_eat_merchant_no` (`merchant_out_order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_articles
# ------------------------------------------------------------

CREATE TABLE `parana_articles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `owner_id` bigint(20) NOT NULL COMMENT '创建人ID,即用户ID',
  `owner_name` varchar(64) DEFAULT NULL COMMENT '创建人姓名',
  `type` bigint(20) NOT NULL COMMENT '文章类别',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态 1:已发布, 0:未发布, -1:删除',
  `title` varchar(256) NOT NULL COMMENT '标题',
  `number` varchar(64) DEFAULT NULL COMMENT '编号',
  `description` varchar(256) NOT NULL COMMENT '描述',
  `extra_json` varchar(256) DEFAULT NULL COMMENT '其他信息,key-value对',
  `tags_json` varchar(256) DEFAULT NULL COMMENT '关键词[{value},{value}, ...]',
  `content` text COMMENT '文章内容',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_article_user_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文章信息表';



# Dump of table parana_back_categories
# ------------------------------------------------------------

CREATE TABLE `parana_back_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `pid` bigint(20) NOT NULL COMMENT '父级id',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `level` tinyint(1) NOT NULL COMMENT '级别',
  `status` tinyint(1) NOT NULL COMMENT '状态,1启用,-1禁用',
  `has_children` tinyint(1) NOT NULL COMMENT '是否有孩子',
  `has_spu` tinyint(1) NOT NULL COMMENT '是否有spu关联',
  `outer_id` varchar(256) DEFAULT NULL COMMENT '外部 id',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_back_categories_pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='后台类目表';



# Dump of table parana_brands
# ------------------------------------------------------------

CREATE TABLE `parana_brands` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `unique_name` varchar(100) DEFAULT NULL COMMENT '唯一小写名',
  `en_name` varchar(100) DEFAULT NULL COMMENT '英文名称',
  `en_cap` char(1) DEFAULT NULL COMMENT '首字母',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态,1启用,-1禁用',
  `logo` varchar(128) DEFAULT NULL COMMENT '品牌logo',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_brands_name` (`name`),
  KEY `idx_brands_en_name` (`en_name`),
  KEY `idx_brands_en_cap` (`en_cap`),
  KEY `idx_brands_unique_name` (`unique_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='品牌表';



# Dump of table parana_cart_items
# ------------------------------------------------------------

CREATE TABLE `parana_cart_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `buyer_id` bigint(20) unsigned DEFAULT NULL COMMENT '买家ID',
  `shop_id` bigint(20) unsigned NOT NULL COMMENT '店铺ID',
  `sku_id` bigint(20) unsigned NOT NULL COMMENT 'SKU ID',
  `quantity` int(10) unsigned NOT NULL COMMENT '商品数量',
  `snapshot_price` int(11) DEFAULT NULL COMMENT '快照价格',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT 'json储存的其他属性键值对',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cart_item_buyer_id` (`buyer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_category_attributes
# ------------------------------------------------------------

CREATE TABLE `parana_category_attributes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '类目id',
  `attr_key` varchar(20) NOT NULL COMMENT '属性名',
  `group` varchar(20) DEFAULT NULL COMMENT '所属组名',
  `index` smallint(3) DEFAULT NULL COMMENT '顺序编号',
  `status` tinyint(1) NOT NULL COMMENT '状态,1启用,-1删除',
  `attr_metas_json` varchar(255) DEFAULT NULL COMMENT 'json 格式存储的属性元信息',
  `attr_vals_json` varchar(4096) DEFAULT NULL COMMENT 'json 格式存储的属性值信息',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_pca_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_category_bindings
# ------------------------------------------------------------

CREATE TABLE `parana_category_bindings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `front_category_id` bigint(20) DEFAULT NULL COMMENT '前台叶子类目id',
  `back_category_id` bigint(20) DEFAULT NULL COMMENT '后台叶子类目id',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='前后台叶子类目映射表';



# Dump of table parana_commission_rules
# ------------------------------------------------------------

CREATE TABLE `parana_commission_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `business_type` smallint(6) NOT NULL COMMENT '业务类型 1:店铺,2:类目',
  `business_id` bigint(20) NOT NULL COMMENT '业务id',
  `business_name` varchar(64) DEFAULT NULL COMMENT '业务名称',
  `rate` int(11) DEFAULT NULL COMMENT '费率(针对每家店铺万分之几)',
  `description` varchar(64) DEFAULT NULL COMMENT '描述',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_delivery_fee_templates
# ------------------------------------------------------------

CREATE TABLE `parana_delivery_fee_templates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `name` varchar(64) NOT NULL COMMENT '模板名称',
  `is_free` tinyint(1) NOT NULL COMMENT '是否包邮',
  `deliver_method` tinyint(4) NOT NULL COMMENT '运送方式:1-快递,2-EMS,3-平邮',
  `charge_method` tinyint(4) NOT NULL COMMENT '计价方式:1-按计量单位,2-固定运费',
  `fee` int(11) DEFAULT NULL COMMENT '运费,当计价方式为固定运费时使用',
  `low_price` int(11) NULL COMMENT '订单不满该金额时，运费为lowFee',
  `low_fee` int(11) NULL COMMENT '订单不满low_price时，运费为lowFee',
  `high_price` int(11) NULL COMMENT '订单高于该金额时，运费为highFee',
  `high_fee` int(11) NULL COMMENT '订单高于high_price时，运费为highFee',
  `middle_fee` int(11) NULL COMMENT '订单价格在lowFee，highFee之间时，运费为middleFee',
  `init_amount` int(11) DEFAULT NULL COMMENT '首费数量',
  `init_fee` int(11) DEFAULT NULL COMMENT '首费金额',
  `incr_amount` int(11) DEFAULT NULL COMMENT '增费数量',
  `incr_fee` int(11) DEFAULT NULL COMMENT '曾费金额',
  `is_default` tinyint(1) DEFAULT NULL COMMENT '是否是默认模板',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_delivery_fee_template_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运费模板';



# Dump of table parana_express_companies
# ------------------------------------------------------------

CREATE TABLE `parana_express_companies` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `code` varchar(32) NOT NULL COMMENT '物流公司代号',
  `name` varchar(64) NOT NULL COMMENT '物流公司名',
  `status` tinyint(6) NOT NULL COMMENT '状态,-1:停用,1:启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_pec_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_front_categories
# ------------------------------------------------------------

CREATE TABLE `parana_front_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `pid` bigint(20) DEFAULT NULL COMMENT '父级id',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `level` tinyint(1) DEFAULT NULL COMMENT '级别',
  `has_children` tinyint(1) DEFAULT NULL COMMENT '是否有孩子',
  `logo` varchar(256) DEFAULT NULL COMMENT 'logo',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_front_categories_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='前台类目表';



# Dump of table parana_invoices
# ------------------------------------------------------------

CREATE TABLE `parana_invoices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `title` varchar(128) NOT NULL COMMENT '发票title',
  `detail_json` varchar(512) DEFAULT NULL COMMENT '发票详细信息',
  `status` bigint(20) NOT NULL COMMENT '发票状态',
  `is_default` tinyint(4) NOT NULL COMMENT '是否默认',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_invoice_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户发票表';



# Dump of table parana_item_attributes
# ------------------------------------------------------------

CREATE TABLE `parana_item_attributes` (
  `item_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商品id',
  `sku_attributes` varchar(8192) DEFAULT NULL COMMENT 'json存储的sku属性键值对',
  `other_attributes` varchar(8192) DEFAULT NULL COMMENT 'json存储的其他属性键值对',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品详情';



# Dump of table parana_item_delivery_fees
# ------------------------------------------------------------

CREATE TABLE `parana_item_delivery_fees` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL COMMENT '商品id',
  `delivery_fee` int(11) DEFAULT NULL COMMENT '运费, 不指定运费模板时用',
  `delivery_fee_template_id` bigint(20) DEFAULT NULL COMMENT '运费模板id',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_item_delivery_fee_item_id` (`item_id`),
  KEY `idx_item_delivery_fee_template_id` (`delivery_fee_template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品运费表';



# Dump of table parana_item_details
# ------------------------------------------------------------

CREATE TABLE `parana_item_details` (
  `item_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商品id',
  `images_json` varchar(2048) DEFAULT NULL COMMENT '图片列表, json表示',
  `detail` text COMMENT '富文本详情',
  `packing_json` varchar(1024) DEFAULT NULL COMMENT '包装清单,kv对, json表示',
  `service` varchar(1024) DEFAULT NULL COMMENT '售后服务',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品详情';



# Dump of table parana_item_snapshots
# ------------------------------------------------------------

CREATE TABLE `parana_item_snapshots` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `item_code` varchar(40) DEFAULT NULL COMMENT '外部商品编码',
  `item_info_md5` char(32) DEFAULT NULL COMMENT '商品信息的m5值, 商品快照需要和这个摘要进行对比',
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `main_image` varchar(128) DEFAULT NULL COMMENT '主图',
  `images_json` varchar(1024) DEFAULT NULL COMMENT '图片列表, json表示',
  `advertise` varchar(255) DEFAULT NULL COMMENT '广告语',
  `specification` varchar(128) DEFAULT NULL COMMENT '规格型号',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT '商品额外信息,建议json字符串',
  `tags_json` varchar(1024) DEFAULT NULL COMMENT '商品标签的json表示形式,只能运营操作, 对商家不可见',
  `sku_attributes` varchar(4096) DEFAULT NULL COMMENT '商品的sku属性, json存储',
  `other_attributes` varchar(8192) DEFAULT NULL COMMENT '商品的其他属性, json存储',
  `detail` text COMMENT '富文本详情',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `item_id_md5` (`item_id`,`item_info_md5`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品快照表';



# Dump of table parana_items
# ------------------------------------------------------------

CREATE TABLE `parana_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `item_code` varchar(40) DEFAULT NULL COMMENT '商品编码(可能外部)',
  `category_id` int(11) unsigned NOT NULL COMMENT '后台类目 ID',
  `spu_id` int(11) DEFAULT NULL COMMENT 'SPU编号',
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `brand_id` bigint(20) DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(100) DEFAULT '' COMMENT '品牌名称',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '商品名称',
  `main_image` varchar(128) DEFAULT NULL COMMENT '主图',
  `low_price` int(11) DEFAULT NULL COMMENT '实际售卖价格(所有sku的最低实际售卖价格)',
  `high_price` int(11) DEFAULT NULL COMMENT '实际售卖价格(所有sku的最高实际售卖价格)',
  `stock_type` tinyint(4) DEFAULT NULL COMMENT '库存类型, 0: 不分仓存储, 1: 分仓存储',
  `stock_quantity` int(11) DEFAULT NULL COMMENT '库存',
  `sale_quantity` int(11) DEFAULT NULL COMMENT '销量',
  `status` tinyint(1) NOT NULL COMMENT '状态',
  `on_shelf_at` datetime DEFAULT NULL COMMENT '上架时间',
  `advertise` varchar(255) DEFAULT NULL COMMENT '广告语',
  `specification` varchar(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '规格型号',
  `type` smallint(6) DEFAULT NULL COMMENT '商品类型 1为普通商品, 2为组合商品',
  `reduce_stock_type` smallint(6) DEFAULT '1' COMMENT '减库存方式, 1为拍下减库存, 2为付款减库存',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT '商品额外信息,建议json字符串',
  `tags_json` varchar(1024) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '商品标签的json表示形式,只能运营操作, 对商家不可见',
  `item_info_md5` char(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '商品信息的m5值, 商品快照需要和这个摘要进行对比',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_items_shop_id` (`shop_id`),
  KEY `idx_items_item_code` (`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品表';



# Dump of table parana_kjtpay_trans
# ------------------------------------------------------------

CREATE TABLE `parana_kjtpay_trans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `outer_no` varchar(64) DEFAULT NULL COMMENT '商户订单号',
  `orig_outer_no` varchar(64) DEFAULT NULL COMMENT '原商户订单号',
  `inner_no` varchar(64) DEFAULT NULL COMMENT '交易订单号',
  `type` varchar(64) DEFAULT NULL COMMENT '交易类型',
  `amount` varchar(64) DEFAULT NULL COMMENT '交易下单时间,支付时间',
  `rate` varchar(32) DEFAULT NULL COMMENT '费率',
  `rate_fee` varchar(32) DEFAULT NULL COMMENT '手续费',
  `status` varchar(16) DEFAULT NULL COMMENT '状态',
  `order_at` datetime DEFAULT NULL COMMENT '交易下单时间',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_ekt_outer_no` (`outer_no`),
  KEY `idx_ekt_inner_no` (`inner_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_merge_order_refunds
# ------------------------------------------------------------

CREATE TABLE `parana_merge_order_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) NOT NULL COMMENT '父订单id, 没有则填-1',
  `parent_type` int(11) NOT NULL COMMENT '父订单类型, 没有则填-1',
  `flow_id` bigint(20) NOT NULL COMMENT '流程id',
  `node_instance_id` bigint(20) NOT NULL COMMENT '订单当前所属节点id',
  `next_action_instance_ids` varchar(1024) DEFAULT NULL COMMENT '接下来可执行的actionId列表,用一个json对象描述每个角色可以进行的操作',
  `type` int(11) NOT NULL COMMENT '订单类型,同parent_type',
  `out_id` varchar(64) DEFAULT NULL COMMENT '外部订单id',
  `out_from` varchar(64) DEFAULT NULL COMMENT '外部订单来源',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '订单额外信息',
  `tags_json` varchar(2048) DEFAULT NULL COMMENT '订单tag信息',
  `buyer_id` bigint(20) DEFAULT NULL COMMENT '买家id',
  `buyer_name` varchar(64) DEFAULT NULL COMMENT '买家名称',
  `out_buyer_id` varchar(64) DEFAULT NULL COMMENT '外部买家id',
  `buyer_note` varchar(256) DEFAULT NULL COMMENT '买家申请退款理由',
  `seller_note` varchar(256) DEFAULT NULL COMMENT '卖家回复',
  `refund_amount` int(11) DEFAULT NULL COMMENT '退款金额',
  `created_at` datetime NOT NULL COMMENT '订单创建时间',
  `updated_at` datetime NOT NULL COMMENT '订单更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_merge_order_refund_parent_id` (`parent_id`),
  KEY `idx_merge_order_refund_buyer_id` (`buyer_id`),
  KEY `idx_merge_order_refund_out_id` (`out_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对应mergeOrder的退款单';



# Dump of table parana_merge_orders
# ------------------------------------------------------------

CREATE TABLE `parana_merge_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) NOT NULL COMMENT '父订单id, 没有则填-1',
  `parent_type` int(11) NOT NULL COMMENT '父订单类型, 没有则填-1',
  `flow_id` bigint(20) NOT NULL COMMENT '流程id',
  `node_instance_id` bigint(20) NOT NULL COMMENT '订单当前所属节点id',
  `next_action_instance_ids` varchar(1024) DEFAULT NULL COMMENT '接下来可执行的actionId列表,用一个json对象描述每个角色可以进行的操作',
  `type` int(11) NOT NULL COMMENT '订单类型,同parent_type',
  `out_id` varchar(64) DEFAULT NULL COMMENT '外部订单id',
  `out_from` varchar(64) DEFAULT NULL COMMENT '外部订单来源',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '订单额外信息',
  `tags_json` varchar(2048) DEFAULT NULL COMMENT '订单tag信息',
  `origin_fee` int(11) DEFAULT NULL COMMENT '原价',
  `fee` int(11) DEFAULT NULL COMMENT '实付金额',
  `discount` int(11) DEFAULT NULL COMMENT '优惠金额',
  `ship_fee` int(11) DEFAULT NULL COMMENT '运费',
  `ship_fee_discount` int(11) DEFAULT NULL COMMENT '运费优惠金额',
  `integral` int(11) DEFAULT NULL COMMENT '积分减免金额',
  `balance` int(11) DEFAULT NULL COMMENT '余额减免金额',
  `sale_tax` int(11) DEFAULT NULL COMMENT '消费税',
  `ship_fee_sale_tax` int(11) DEFAULT NULL COMMENT '运费中包含的消费税',
  `buyer_id` bigint(20) DEFAULT NULL COMMENT '买家id',
  `buyer_name` varchar(64) DEFAULT NULL COMMENT '买家名称',
  `out_buyer_id` varchar(64) DEFAULT NULL COMMENT '外部买家id',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `pay_type` smallint(6) NOT NULL COMMENT '支付类型, 1-在线支付 2-货到付款',
  `channel` smallint(6) NOT NULL COMMENT '订单渠道 1-手机 2-pc',
  `has_refund` tinyint(4) DEFAULT NULL COMMENT '是否申请过逆向流程',
  `created_at` datetime NOT NULL COMMENT '订单创建时间',
  `updated_at` datetime NOT NULL COMMENT '订单更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_merge_orders_parent_id` (`parent_id`),
  KEY `idx_merge_orders_buyer_id` (`buyer_id`),
  KEY `idx_merge_orders_company_id` (`company_id`),
  KEY `idx_merge_orders_out_id` (`out_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合并支付维度订单';



# Dump of table parana_operator_roles
# ------------------------------------------------------------

CREATE TABLE `parana_operator_roles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) DEFAULT NULL COMMENT '角色名',
  `desc` varchar(256) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(4) NOT NULL COMMENT '0. 未生效(冻结), 1. 生效, -1. 删除',
  `allow_json` varchar(2048) DEFAULT NULL COMMENT '角色对应选中权限树节点列表',
  `extra_json` varchar(4096) DEFAULT NULL COMMENT '用户额外信息,建议json字符串',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运营角色表';



# Dump of table parana_order_action_instances
# ------------------------------------------------------------

CREATE TABLE `parana_order_action_instances` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `node_instance_id` bigint(20) NOT NULL COMMENT '节点实例ID',
  `action` varchar(128) DEFAULT NULL COMMENT 'action bean id',
  `display` tinyint(4) NOT NULL COMMENT '该动作是否显示',
  `type` smallint(6) NOT NULL COMMENT '动作类型 1->普通, 2->定时任务, 3->通知',
  `belong_user_types` varchar(128) NOT NULL COMMENT '动作执行对象的用户类型 0:管理员, 1: 买家, 2: 卖家.一个用户类型的jsonList',
  `name` varchar(128) DEFAULT NULL COMMENT '动作名称',
  `desc` varchar(256) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_action_instance_node_instance_id` (`node_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='动作实例表';



# Dump of table parana_order_comments
# ------------------------------------------------------------

CREATE TABLE `parana_order_comments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) NOT NULL COMMENT '父评论id,以后评论叠加会用到,现在默认都是-1',
  `belong_user_type` smallint(6) NOT NULL COMMENT '1为买家发起评论,2为商家发起评论',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `user_name` varchar(64) NOT NULL COMMENT '用户名称',
  `sku_order_id` bigint(20) NOT NULL COMMENT 'sku订单id',
  `item_id` bigint(20) NOT NULL COMMENT '商品id',
  `item_name` varchar(128) NOT NULL COMMENT '商品名称',
  `sku_attributes` varchar(512) DEFAULT NULL COMMENT 'sku属性, json表示',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(128) NOT NULL COMMENT '店铺名称',
  `quality` int(11) DEFAULT NULL COMMENT '质量评分',
  `describe` int(11) DEFAULT NULL COMMENT '描述评分',
  `service` int(11) DEFAULT NULL COMMENT '服务评分',
  `express` int(11) DEFAULT NULL COMMENT '物流评分',
  `context` text COMMENT '评价内容',
  `status` int(11) NOT NULL COMMENT '评价状态 1->正常 -1->删除',
  `extra_json` text,
  `has_display` tinyint(4) DEFAULT '0' COMMENT '是否已晒单',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_comment_user_id` (`user_id`),
  KEY `idx_order_comment_item_id` (`item_id`),
  KEY `idx_order_comment_shop_id` (`shop_id`),
  KEY `idx_order_comments_sku_order_id` (`sku_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='评价表';



# Dump of table parana_order_flows
# ------------------------------------------------------------

CREATE TABLE `parana_order_flows` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(128) DEFAULT NULL COMMENT '订单流程名',
  `desc` varchar(256) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单流程表';



# Dump of table parana_order_invoices
# ------------------------------------------------------------

CREATE TABLE `parana_order_invoices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint(20) NOT NULL COMMENT '发票id',
  `order_id` bigint(20) NOT NULL COMMENT '(子)订单id',
  `order_type` smallint(6) NOT NULL COMMENT '1: 店铺订单, 2: 子订单',
  `status` bigint(20) NOT NULL COMMENT '0: 待开, 1: 已开, -1: 删除作废',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_oi_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单发票关联表';



# Dump of table parana_order_job_data
# ------------------------------------------------------------

CREATE TABLE `parana_order_job_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL COMMENT '订单id',
  `order_type` smallint(6) NOT NULL COMMENT '订单类型',
  `action_instance_id` bigint(20) NOT NULL COMMENT '需要执行的actionId',
  `status` smallint(6) NOT NULL COMMENT '定时任务状态 1-OK 2-FINISH',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单定时任务表';



# Dump of table parana_order_node_instances
# ------------------------------------------------------------

CREATE TABLE `parana_order_node_instances` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fid` bigint(20) NOT NULL COMMENT '订单流程ID',
  `names` varchar(128) DEFAULT NULL COMMENT '节点名称(状态名称),对于同一个节点,角色不同对应的显示文案也不同',
  `desc` varchar(256) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单流程节点实例表';



# Dump of table parana_order_payments
# ------------------------------------------------------------

CREATE TABLE `parana_order_payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` bigint(20) NOT NULL COMMENT '支付单id',
  `order_id` bigint(20) NOT NULL COMMENT '(子)订单id',
  `order_type` smallint(6) NOT NULL COMMENT '订单类型 1: 店铺订单支付, 2: 子订单支付',
  `status` smallint(6) NOT NULL COMMENT '0: 待支付, 1: 已支付, -1:已删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_payment_payment_id` (`payment_id`),
  KEY `idx_order_payment_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='支付单和(子)订单关联表';



# Dump of table parana_order_receiver_infos
# ------------------------------------------------------------

CREATE TABLE `parana_order_receiver_infos` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL COMMENT '(子)订单id',
  `order_type` smallint(6) NOT NULL COMMENT '1: 店铺订单, 2: 子订单',
  `receiver_info_json` varchar(512) NOT NULL COMMENT 'json表示的收货信息',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_receiver_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_order_refunds
# ------------------------------------------------------------

CREATE TABLE `parana_order_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `refund_id` bigint(20) NOT NULL COMMENT '退款单id',
  `order_id` bigint(20) NOT NULL COMMENT '(子)订单id',
  `order_type` smallint(6) NOT NULL COMMENT '1: 店铺订单, 2: 子订单',
  `status` bigint(20) DEFAULT NULL COMMENT '状态 0:待退款, 1:已退款, -1:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_refund_rid` (`refund_id`),
  KEY `idx_order_refund_oid` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='退款单和订单关联表';



# Dump of table parana_order_shipments
# ------------------------------------------------------------

CREATE TABLE `parana_order_shipments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `shipment_id` bigint(20) NOT NULL COMMENT '发货单id',
  `order_id` bigint(20) NOT NULL COMMENT '(子)订单id',
  `order_type` smallint(6) NOT NULL COMMENT '发货订单类型 1: 店铺订单发货, 2: 子订单发货',
  `status` smallint(6) NOT NULL COMMENT '0: 待发货, 1: 已发货, -1:已删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_shipment_shipment_id` (`shipment_id`),
  KEY `idx_order_shipment_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='发货单和(子)订单关联表';



# Dump of table parana_order_transfer_rules
# ------------------------------------------------------------

CREATE TABLE `parana_order_transfer_rules` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `start_node_instance_id` bigint(20) NOT NULL COMMENT '流转起始节点id',
  `end_node_instance_id` bigint(20) NOT NULL COMMENT '流转终止节点id',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流转规则表';



# Dump of table parana_pay_channel_daily_summarys
# ------------------------------------------------------------

CREATE TABLE `parana_pay_channel_daily_summarys` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel` varchar(512) DEFAULT NULL COMMENT '支付渠道名称',
  `trade_fee` bigint(20) DEFAULT NULL COMMENT '交易金额',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  `net_income_fee` bigint(20) DEFAULT NULL COMMENT '净收入',
  `sum_at` date DEFAULT NULL COMMENT '汇总时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_pay_channel_details
# ------------------------------------------------------------

CREATE TABLE `parana_pay_channel_details` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `pay_channel_code` varchar(512) DEFAULT NULL COMMENT '支付渠道Code',
  `channel` varchar(512) DEFAULT NULL COMMENT '支付渠道名称',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  `gateway_rate` bigint(20) DEFAULT NULL COMMENT '支付平台佣金比率',
  `trade_fee` bigint(20) DEFAULT NULL COMMENT '实际入账金额 正数（收入）负数（支出）',
  `actual_income_fee` bigint(20) DEFAULT NULL COMMENT '实际入账金额 正数（收入）负数（支出）',
  `trade_type` bigint(20) DEFAULT NULL COMMENT '交易类型1 支付 -1 退款',
  `trade_no` varchar(512) DEFAULT NULL COMMENT '电商平台交易流水号',
  `gateway_trade_no` varchar(512) DEFAULT NULL COMMENT '支付平台交易流水号',
  `check_status` bigint(20) DEFAULT NULL COMMENT '对账状态',
  `check_finished_at` datetime DEFAULT NULL COMMENT '对账完成时间',
  `trade_finished_at` datetime DEFAULT NULL COMMENT '支付成功或退款成功时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  `channel_account` varchar(128) DEFAULT NULL COMMENT '支付渠道账户',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_payments
# ------------------------------------------------------------

CREATE TABLE `parana_payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fee` bigint(20) NOT NULL COMMENT '实付金额',
  `out_id` varchar(128) DEFAULT NULL COMMENT '外部id',
  `pay_info_md5` char(32) DEFAULT NULL COMMENT '支付信息md5',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '原始金额',
  `discount` bigint(20) DEFAULT '0',
  `integral` int(11) DEFAULT NULL COMMENT '积分减免金额',
  `balance` int(11) DEFAULT NULL COMMENT '余额减免金额',
  `status` smallint(6) NOT NULL COMMENT '状态, 0:待支付, 1:已支付, -1:删除',
  `pay_serial_no` varchar(32) DEFAULT NULL COMMENT '外部支付流水号',
  `pay_account_no` varchar(32) DEFAULT NULL COMMENT '支付账号',
  `channel` varchar(16) DEFAULT NULL COMMENT '支付渠道',
  `promotion_id` bigint(20) DEFAULT NULL COMMENT '平台级别优惠活动id',
  `extra_json` varchar(512) DEFAULT NULL COMMENT '支付额外信息',
  `tags_json` varchar(512) DEFAULT NULL COMMENT '支付额外信息, 运营使用',
  `paid_at` datetime DEFAULT NULL COMMENT '订单支付时间',
  `created_at` datetime DEFAULT NULL COMMENT '支付单创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '支付单更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_payments_pay_info_md5` (`pay_info_md5`),
  KEY `idx_payments_pay_serial_no` (`pay_serial_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='支付单表';



# Dump of table parana_platform_trade_daily_summarys
# ------------------------------------------------------------

CREATE TABLE `parana_platform_trade_daily_summarys` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_count` bigint(20) DEFAULT NULL COMMENT '订单数',
  `refund_order_count` bigint(20) DEFAULT NULL COMMENT '退款单数',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '应收货款',
  `refund_fee` bigint(20) DEFAULT NULL COMMENT '退款金额',
  `seller_discount` bigint(20) DEFAULT NULL COMMENT '商家优惠',
  `platform_discount` bigint(20) DEFAULT NULL COMMENT '电商平台优惠',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '运费',
  `ship_fee_discount` bigint(20) DEFAULT NULL COMMENT '运费优惠',
  `actual_pay_fee` bigint(20) DEFAULT NULL COMMENT '实收货款',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  `platform_commission` bigint(20) DEFAULT NULL COMMENT '电商平台佣金',
  `seller_receivable_fee` bigint(20) DEFAULT NULL COMMENT '商家应收',
  `summary_type` int(11) DEFAULT NULL COMMENT '汇总类型: 0-所有, 1-正向, 2-逆向',
  `sum_at` datetime DEFAULT NULL COMMENT '汇总时间（该数据是某一天的）',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_promotion_defs
# ------------------------------------------------------------

CREATE TABLE `parana_promotion_defs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL COMMENT '营销工具唯一标识',
  `type` smallint(6) NOT NULL COMMENT '营销工具类型',
  `status` smallint(6) NOT NULL COMMENT '状态, , 1: 启用 -1:禁用',
  `user_scope_key` varchar(512) DEFAULT NULL COMMENT '用户选择',
  `sku_scope_key` varchar(512) DEFAULT NULL COMMENT 'sku选择',
  `behavior_key` varchar(512) DEFAULT NULL COMMENT '营销方式',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_promotion_tracks
# ------------------------------------------------------------

CREATE TABLE `parana_promotion_tracks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `promotion_id` bigint(20) NOT NULL COMMENT '营销id',
  `received_quantity` int(11) NOT NULL COMMENT '已领取数量',
  `used_quantity` int(11) NOT NULL COMMENT '已使用数量',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_pt_promotion_id` (`promotion_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销跟踪表';



# Dump of table parana_promotions
# ------------------------------------------------------------

CREATE TABLE `parana_promotions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id, 如果是平台级别的营销, 则为0',
  `name` varchar(32) NOT NULL COMMENT '营销活动名称',
  `promotion_def_id` bigint(20) NOT NULL COMMENT '营销工具定义id',
  `type` smallint(6) NOT NULL COMMENT '营销工具类型,冗余',
  `status` smallint(6) NOT NULL COMMENT '状态, 0: 初始化, 1: 可能生效, 需要根据生效开始和截至时间进一步判断 -1:已过期',
  `start_at` datetime NOT NULL COMMENT '营销活动开始时间',
  `end_at` datetime NOT NULL COMMENT '营销活动结束时间',
  `user_scope_params_json` varchar(512) DEFAULT NULL COMMENT '营销选择用户范围参数',
  `sku_scope_params_json` varchar(2048) DEFAULT NULL COMMENT '营销选择sku范围参数',
  `condition_params_json` varchar(512) DEFAULT NULL COMMENT '营销执行前提条件的参数, json表示',
  `behavior_params_json` varchar(2048) DEFAULT NULL COMMENT '营销方式参数',
  `extra_json` varchar(512) DEFAULT NULL COMMENT '附加信息',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_promotion_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_receive_addresses
# ------------------------------------------------------------

CREATE TABLE `parana_receive_addresses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `receive_user_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(32) DEFAULT NULL COMMENT '固定电话',
  `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
  `email` varchar(32) DEFAULT NULL COMMENT '邮箱地址',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认 1：默认',
  `status` tinyint(4) NOT NULL COMMENT '状态，1：正常，-1：删除',
  `province` varchar(50) NOT NULL COMMENT '省',
  `province_id` bigint(20) NOT NULL COMMENT '省ID',
  `city` varchar(50) NOT NULL COMMENT '市',
  `city_id` bigint(20) NOT NULL COMMENT '市ID',
  `region` varchar(50) NOT NULL COMMENT '区',
  `region_id` bigint(20) NOT NULL COMMENT '区ID',
  `street` varchar(50) DEFAULT NULL COMMENT '街道，可以为空',
  `street_id` bigint(20) DEFAULT NULL COMMENT '街道ID，可以为空',
  `detail` varchar(256) NOT NULL COMMENT '详细地址',
  `postcode` varchar(32) DEFAULT NULL COMMENT '邮政编码',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_receiver_infos
# ------------------------------------------------------------

CREATE TABLE `parana_receiver_infos` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `receive_user_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(32) DEFAULT NULL COMMENT '固定电话',
  `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
  `email` varchar(32) DEFAULT NULL COMMENT '邮箱地址',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认 1：默认',
  `status` tinyint(4) NOT NULL COMMENT '状态，1：正常，-1：删除',
  `province` varchar(50) NOT NULL COMMENT '省',
  `province_id` bigint(20) NOT NULL COMMENT '省ID',
  `city` varchar(50) NOT NULL COMMENT '市',
  `city_id` bigint(20) NOT NULL COMMENT '市ID',
  `region` varchar(50) NOT NULL COMMENT '区',
  `region_id` bigint(20) NOT NULL COMMENT '区ID',
  `street` varchar(50) DEFAULT NULL COMMENT '街道，可以为空',
  `street_id` bigint(20) DEFAULT NULL COMMENT '街道ID，可以为空',
  `detail` varchar(256) NOT NULL COMMENT '详细地址',
  `postcode` varchar(32) DEFAULT NULL COMMENT '邮政编码',
  `extra_json`   VARCHAR(256) NULL COMMENT '额外信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户收货信息表';



# Dump of table parana_refunds
# ------------------------------------------------------------

CREATE TABLE `parana_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fee` bigint(20) DEFAULT NULL COMMENT '实际退款金额',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(64) NOT NULL COMMENT '店铺名称',
  `buyer_id` bigint(20) NOT NULL COMMENT '买家id',
  `buyer_name` varchar(64) NOT NULL COMMENT '买家名称',
  `out_id` varchar(512) DEFAULT NULL COMMENT '外部业务id',
  `integral` bigint(20) DEFAULT NULL COMMENT '要退的积分',
  `balance` bigint(20) DEFAULT NULL COMMENT '要退的余额',
  `status` bigint(20) DEFAULT NULL COMMENT '状态',
  `refund_serial_no` varchar(512) DEFAULT NULL COMMENT '退款流水号',
  `payment_id` bigint(20) DEFAULT NULL COMMENT '对应的支付单id',
  `pay_serial_no` varchar(512) DEFAULT NULL COMMENT '对应支付单的交易流水号',
  `refund_account_no` varchar(512) DEFAULT NULL COMMENT '退款到哪个账号',
  `channel` varchar(512) DEFAULT NULL COMMENT '退款渠道',
  `promotion_id` bigint(20) DEFAULT NULL COMMENT '涉及到的平台级优惠id',
  `buyer_note` varchar(512) DEFAULT NULL COMMENT '买家备注',
  `seller_note` varchar(512) DEFAULT NULL COMMENT '商家备注',
  `extra_json` varchar(512) DEFAULT NULL COMMENT '附加信息, 商家使用',
  `tags_json` varchar(512) DEFAULT NULL COMMENT '标签信息, 运营使用',
  `refund_at` datetime DEFAULT NULL COMMENT '退款成功时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `trade_no` varchar(512) DEFAULT NULL COMMENT '对应支付单的电商平台交易流水号',
  PRIMARY KEY (`id`),
  KEY `idx_refunds_shop_id` (`shop_id`),
  KEY `idx_refunds_refund_serial_no` (`refund_serial_no`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='退款单表';



# Dump of table parana_seller_trade_daily_summarys
# ------------------------------------------------------------

CREATE TABLE `parana_seller_trade_daily_summarys` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seller_id` bigint(20) DEFAULT NULL COMMENT '商家ID',
  `seller_name` varchar(512) DEFAULT NULL COMMENT '商家名称',
  `order_count` bigint(20) DEFAULT NULL COMMENT '订单数',
  `refund_order_count` bigint(20) DEFAULT NULL COMMENT '退款单数',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '应收货款',
  `refund_fee` bigint(20) DEFAULT NULL COMMENT '退款金额',
  `seller_discount` bigint(20) DEFAULT NULL COMMENT '商家优惠',
  `platform_discount` bigint(20) DEFAULT NULL COMMENT '电商平台优惠',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '运费',
  `ship_fee_discount` bigint(20) DEFAULT NULL COMMENT '运费优惠',
  `actual_pay_fee` bigint(20) DEFAULT NULL COMMENT '实收货款',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  `platform_commission` bigint(20) DEFAULT NULL COMMENT '电商平台佣金',
  `seller_receivable_fee` bigint(20) DEFAULT NULL COMMENT '商家应收',
  `summary_type` int(11) DEFAULT NULL COMMENT '汇总类型: 0-所有, 1-正向, 2-逆向',
  `sum_at` datetime DEFAULT NULL COMMENT '汇总时间（该数据是某一天的）',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_settle_abnormal_tracks
# ------------------------------------------------------------

CREATE TABLE `parana_settle_abnormal_tracks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `abnormal_info` varchar(256) NOT NULL COMMENT '异常信息',
  `abnormal_type` smallint(6) DEFAULT NULL COMMENT '异常类型',
  `is_handle` bit(1) DEFAULT b'0' COMMENT '是否已处理',
  `description` varchar(256) DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_esat_abnormal_type` (`abnormal_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_settle_order_details
# ------------------------------------------------------------

CREATE TABLE `parana_settle_order_details` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单id,  也可以是子订单级别的, 由orderType决定',
  `order_type` bigint(20) DEFAULT NULL COMMENT '本次支付针对的订单类型, 1: 店铺订单支付, 2: 子订单支付',
  `seller_id` bigint(20) DEFAULT NULL COMMENT '商家ID',
  `seller_name` varchar(128) DEFAULT NULL COMMENT '商家名称',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '应收货款',
  `seller_discount` bigint(20) DEFAULT NULL COMMENT '商家优惠',
  `platform_discount` bigint(20) DEFAULT NULL COMMENT '电商平台优惠',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '运费',
  `ship_fee_discount` bigint(20) DEFAULT NULL COMMENT '运费优惠',
  `actual_pay_fee` bigint(20) DEFAULT NULL COMMENT '实收货款',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  `platform_commission` bigint(20) DEFAULT NULL COMMENT '电商平台佣金',
  `seller_receivable_fee` bigint(20) DEFAULT NULL COMMENT '商家应收',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '电商平台支付流水号',
  `gateway_trade_no` varchar(64) DEFAULT NULL COMMENT '支付平台支付流水号',
  `channel` varchar(32) DEFAULT NULL COMMENT '支付渠道',
  `channel_account` varchar(128) DEFAULT NULL COMMENT '支付渠道账号',
  `order_created_at` datetime DEFAULT NULL COMMENT '订单创建时间',
  `order_finished_at` datetime DEFAULT NULL COMMENT '订单完成时间',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `check_at` datetime DEFAULT NULL COMMENT '对账时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_settle_refund_order_details
# ------------------------------------------------------------

CREATE TABLE `parana_settle_refund_order_details` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单id,  也可以是子订单级别的, 由orderType决定',
  `sku_order_id` bigint(20) DEFAULT NULL COMMENT '子订单单Id',
  `seller_id` bigint(20) DEFAULT NULL COMMENT '商家ID',
  `seller_name` varchar(128) DEFAULT NULL COMMENT '商家名称',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '应退货款',
  `seller_discount` bigint(20) DEFAULT NULL COMMENT '商家优惠',
  `platform_discount` bigint(20) DEFAULT NULL COMMENT '电商平台优惠',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '应退运费',
  `ship_fee_discount` bigint(20) DEFAULT NULL COMMENT '运费优惠',
  `actual_refund_fee` bigint(20) DEFAULT NULL COMMENT '实退货款',
  `platform_commission` bigint(20) DEFAULT NULL COMMENT '应退电商平台佣金',
  `seller_deduct_fee` bigint(20) DEFAULT NULL COMMENT '商家应扣',
  `refund_id` bigint(20) DEFAULT NULL COMMENT '退款单号',
  `channel` varchar(64) DEFAULT NULL COMMENT '支付渠道',
  `channel_account` varchar(64) DEFAULT NULL COMMENT '支付渠道账号',
  `refund_no` varchar(64) DEFAULT NULL COMMENT '电商平台退款流水号',
  `gateway_refund_no` varchar(64) DEFAULT NULL COMMENT '支付平台退款流水号',
  `refund_created_at` datetime DEFAULT NULL COMMENT '退款订单创建时间',
  `refund_agreed_at` datetime DEFAULT NULL COMMENT '退款订单同意时间',
  `refund_at` datetime DEFAULT NULL COMMENT '退款时间',
  `check_at` datetime DEFAULT NULL COMMENT '对账时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_settlements
# ------------------------------------------------------------

CREATE TABLE `parana_settlements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel` varchar(512) DEFAULT NULL COMMENT '支付渠道',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '应收(退)货款',
  `seller_discount` bigint(20) DEFAULT NULL COMMENT '商家优惠',
  `platform_discount` bigint(20) DEFAULT NULL COMMENT '电商平台优惠',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '运费',
  `ship_fee_discount` bigint(20) DEFAULT NULL COMMENT '运费优惠',
  `actual_fee` bigint(20) DEFAULT NULL COMMENT '实收(退)货款',
  `gateway_commission` bigint(20) DEFAULT NULL COMMENT '支付平台佣金',
  `platform_commission` bigint(20) DEFAULT NULL COMMENT '电商平台佣金',
  `trade_type` bigint(20) DEFAULT NULL COMMENT '交易类型',
  `trade_no` varchar(512) DEFAULT NULL COMMENT '电商平台交易流水号 (支付和退款的流水号)',
  `gateway_trade_no` varchar(512) DEFAULT NULL COMMENT '支付平台交易流水号 (支付和退款的流水号)',
  `refund_no` varchar(512) DEFAULT NULL COMMENT '电商平台退款流水号',
  `gateway_refund_no` varchar(512) DEFAULT NULL COMMENT '支付平台退款流水号',
  `order_ids` varchar(512) DEFAULT NULL COMMENT '订单号, 当合并支付时会有多个, 用逗号分割, 如: 1,2,3',
  `payment_or_refund_id` bigint(20) DEFAULT NULL COMMENT '支付订单号或退款订单号',
  `check_status` bigint(20) DEFAULT NULL COMMENT '对账状态',
  `check_finished_at` datetime DEFAULT NULL COMMENT '对账完成时间',
  `trade_finished_at` datetime DEFAULT NULL COMMENT '支付成功或退款成功时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最新更新时间',
  `channel_account` varchar(128) DEFAULT NULL COMMENT '支付渠道账户',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_shipments
# ------------------------------------------------------------

CREATE TABLE `parana_shipments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `status` smallint(6) NOT NULL COMMENT '0: 待发货, 1:已发货, 2: 已收货, -1: 已删除',
  `shipment_serial_no` varchar(32) DEFAULT NULL COMMENT '物流单号',
  `shipment_corp_code` varchar(32) DEFAULT NULL COMMENT '物流公司编号',
  `shipment_corp_name` varchar(32) DEFAULT NULL COMMENT '物流公司名称',
  `sku_info_jsons` varchar(1024) DEFAULT NULL COMMENT '对应的skuId及数量, json表示',
  `receiver_infos` varchar(512) DEFAULT NULL COMMENT '收货人信息',
  `extra_json` varchar(512) DEFAULT NULL COMMENT '发货单额外信息',
  `tags_json` varchar(512) DEFAULT NULL COMMENT '发货单额外信息, 运营使用',
  `confirm_at` datetime DEFAULT NULL COMMENT '确认收货事件',
  `created_at` datetime DEFAULT NULL COMMENT '发货单创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '发货单更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_shipments_ship_serial_no` (`shipment_serial_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='发货单表';



# Dump of table parana_shop_categories
# ------------------------------------------------------------

CREATE TABLE `parana_shop_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `name` varchar(20) NOT NULL COMMENT '类目名称',
  `pid` bigint(20) NOT NULL COMMENT '父级id',
  `level` tinyint(1) NOT NULL COMMENT '级别',
  `has_children` tinyint(1) DEFAULT NULL COMMENT '是否有孩子',
  `has_item` tinyint(1) DEFAULT NULL COMMENT '是否有商品关联',
  `index` int(11) DEFAULT NULL COMMENT '排序',
  `disclosed` tinyint(1) DEFAULT NULL COMMENT '是否默认展开',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shopcats_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺内类目表';



# Dump of table parana_shop_category_items
# ------------------------------------------------------------

CREATE TABLE `parana_shop_category_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `item_id` bigint(20) NOT NULL COMMENT '商品id',
  `shop_category_id` bigint(20) NOT NULL COMMENT '店铺内类目id',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shopcis_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺内类目表';



# Dump of table parana_shop_extras
# ------------------------------------------------------------

CREATE TABLE `parana_shop_extras` (
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `deposit_cost` int(11) DEFAULT NULL COMMENT '保证金金额',
  `rate` int(4) DEFAULT NULL COMMENT '商家费率',
  `account` varchar(64) DEFAULT NULL COMMENT '账户名称',
  `account_type` smallint(1) DEFAULT NULL COMMENT '1:支付宝 2:银行卡',
  `account_name` varchar(64) DEFAULT NULL COMMENT '开户人姓名',
  `bank_name` varchar(32) DEFAULT NULL COMMENT '银行名称',
  `pre_im_id` varchar(32) DEFAULT NULL COMMENT '售前客服联系方式id',
  `post_im_id` varchar(32) DEFAULT NULL COMMENT '售后客服联系方式id',
  `commission_type` smallint(4) DEFAULT NULL COMMENT '抽佣类型 1 费率 2 差价',
  `billing_period` smallint(4) DEFAULT NULL COMMENT '帐期,  1、5、10、15、30五类',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺附加表';



# Dump of table parana_shop_order_refunds
# ------------------------------------------------------------

CREATE TABLE `parana_shop_order_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) NOT NULL COMMENT '父订单id, 没有则填-1',
  `parent_type` int(11) NOT NULL COMMENT '父订单类型, 没有则填-1',
  `flow_id` bigint(20) NOT NULL COMMENT '流程id',
  `node_instance_id` bigint(20) NOT NULL COMMENT '订单当前所属节点id',
  `next_action_instance_ids` varchar(1024) DEFAULT NULL COMMENT '接下来可执行的actionId列表,用一个json对象描述每个角色可以进行的操作',
  `type` int(11) NOT NULL COMMENT '订单类型,同parent_type',
  `out_id` varchar(64) DEFAULT NULL COMMENT '外部订单id',
  `out_from` varchar(64) DEFAULT NULL COMMENT '外部订单来源',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '订单额外信息',
  `tags_json` varchar(2048) DEFAULT NULL COMMENT '订单tag信息',
  `buyer_id` bigint(20) DEFAULT NULL COMMENT '买家id',
  `buyer_name` varchar(64) DEFAULT NULL COMMENT '买家名称',
  `out_buyer_id` varchar(64) DEFAULT NULL COMMENT '外部买家id',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `shop_name` varchar(64) DEFAULT NULL COMMENT '店铺名称',
  `out_shop_id` varchar(64) DEFAULT NULL COMMENT '外部店铺id',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `buyer_note` varchar(256) DEFAULT NULL COMMENT '买家申请退款理由',
  `seller_note` varchar(256) DEFAULT NULL COMMENT '卖家回复',
  `refund_amount` int(11) DEFAULT NULL COMMENT '退款金额',
  `created_at` datetime NOT NULL COMMENT '订单创建时间',
  `updated_at` datetime NOT NULL COMMENT '订单更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_order_refund_parent_id` (`parent_id`),
  KEY `idx_shop_order_refund_buyer_id` (`buyer_id`),
  KEY `idx_shop_order_refund_out_id` (`out_id`),
  KEY `idx_shop_order_refund_company_id` (`company_id`),
  KEY `idx_shop_order_refund_shop_id` (`shop_id`),
  KEY `idx_sku_order_refund_parent_id` (`parent_id`),
  KEY `idx_sku_order_refund_buyer_id` (`buyer_id`),
  KEY `idx_sku_order_refund_out_id` (`out_id`),
  KEY `idx_sku_order_refund_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对应shopOrder的退款单';



# Dump of table parana_shop_orders
# ------------------------------------------------------------

CREATE TABLE `parana_shop_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `buyer_id` bigint(20) NOT NULL COMMENT '买家id',
  `fee` bigint(20) NOT NULL COMMENT '实付金额',
  `status` smallint(6) NOT NULL COMMENT '状态',
  `buyer_name` varchar(64) NOT NULL COMMENT '买家名称',
  `out_buyer_id` varchar(64) DEFAULT NULL COMMENT '买家外部id',
  `shop_name` varchar(64) NOT NULL COMMENT '店铺名称',
  `out_shop_id` varchar(64) DEFAULT NULL COMMENT '店铺外部id',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '原价',
  `discount` bigint(20) DEFAULT '0',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '运费',
  `origin_ship_fee` bigint(20) DEFAULT NULL COMMENT '运费原始金额',
  `shipment_promotion_id` bigint(20) DEFAULT NULL COMMENT '运费营销活动id',
  `integral` int(11) DEFAULT NULL COMMENT '积分减免金额',
  `balance` int(11) DEFAULT NULL COMMENT '余额减免金额',
  `promotion_id` bigint(20) DEFAULT NULL COMMENT '店铺级别的优惠id',
  `shipment_type` smallint(6) DEFAULT NULL COMMENT '配送方式',
  `pay_type` smallint(6) NOT NULL COMMENT '支付类型, 1-在线支付 2-货到付款',
  `channel` smallint(6) NOT NULL COMMENT '订单渠道 1-手机 2-pc',
  `has_refund` tinyint(4) DEFAULT NULL COMMENT '是否申请过逆向流程',
  `commented` tinyint(4) DEFAULT '0' COMMENT '是否已评价',
  `buyer_note` varchar(512) DEFAULT NULL COMMENT '买家备注',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '子订单额外信息,json表示',
  `tags_json` varchar(2048) DEFAULT NULL COMMENT '子订单tag信息, json表示',
  `out_id` varchar(64) DEFAULT NULL COMMENT '外部订单id',
  `out_from` varchar(64) DEFAULT NULL COMMENT '外部订单来源',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `commission_rate` int(11) DEFAULT '0' COMMENT '电商平台佣金费率, 万分之一',
  PRIMARY KEY (`id`),
  KEY `idx_shop_orders_shop_id` (`shop_id`),
  KEY `idx_shop_orders_buyer_id` (`buyer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺维度订单';



# Dump of table parana_shops
# ------------------------------------------------------------

CREATE TABLE `parana_shops` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `outer_id` varchar(32) DEFAULT NULL COMMENT '外部店铺编码',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `user_name` varchar(32) NOT NULL COMMENT '商家名称',
  `name` varchar(64) NOT NULL COMMENT '店铺名称',
  `status` tinyint(1) NOT NULL COMMENT '状态 1:正常, -1:关闭, -2:冻结',
  `type` tinyint(1) NOT NULL COMMENT '店铺状态',
  `phone` varchar(32) DEFAULT NULL COMMENT '联系电话',
  `business_id` int(4) DEFAULT NULL COMMENT '行业id',
  `image_url` varchar(128) DEFAULT NULL COMMENT '店铺图片url',
  `address` varchar(128) DEFAULT NULL COMMENT '店铺地址',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT '商品额外信息,建议json字符串',
  `tags_json` varchar(1024) DEFAULT NULL COMMENT '商品标签的json表示形式,只能运营操作, 对商家不可见',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shop_user_id` (`user_id`),
  KEY `idx_shop_name` (`name`),
  KEY `idx_shop_outer_id` (`outer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺表';



# Dump of table parana_sku_order_refunds
# ------------------------------------------------------------

CREATE TABLE `parana_sku_order_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) NOT NULL COMMENT '父订单id, 没有则填-1',
  `parent_type` int(11) NOT NULL COMMENT '父订单类型, 没有则填-1',
  `flow_id` bigint(20) NOT NULL COMMENT '流程id',
  `node_instance_id` bigint(20) NOT NULL COMMENT '订单当前所属节点id',
  `next_action_instance_ids` varchar(1024) DEFAULT NULL COMMENT '接下来可执行的actionId列表,用一个json对象描述每个角色可以进行的操作',
  `type` int(11) NOT NULL COMMENT '订单类型,同parent_type',
  `out_id` varchar(64) DEFAULT NULL COMMENT '外部订单id',
  `out_from` varchar(64) DEFAULT NULL COMMENT '外部订单来源',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '订单额外信息',
  `tags_json` varchar(2048) DEFAULT NULL COMMENT '订单tag信息',
  `buyer_id` bigint(20) DEFAULT NULL COMMENT '买家id',
  `buyer_name` varchar(64) DEFAULT NULL COMMENT '买家名称',
  `out_buyer_id` varchar(64) DEFAULT NULL COMMENT '外部买家id',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `shop_name` varchar(64) DEFAULT NULL COMMENT '店铺名称',
  `out_shop_id` varchar(64) DEFAULT NULL COMMENT '外部店铺id',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `item_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `item_name` varchar(64) DEFAULT NULL COMMENT '商品名称',
  `item_image` varchar(128) DEFAULT NULL COMMENT '商品图片',
  `out_item_id` varchar(64) DEFAULT NULL COMMENT '外部商品id',
  `buyer_note` varchar(256) DEFAULT NULL COMMENT '买家申请退款理由',
  `seller_note` varchar(256) DEFAULT NULL COMMENT '卖家回复',
  `refund_amount` int(11) DEFAULT NULL COMMENT '退款金额',
  `created_at` datetime NOT NULL COMMENT '订单创建时间',
  `updated_at` datetime NOT NULL COMMENT '订单更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对应skuOrder的退款单';



# Dump of table parana_sku_orders
# ------------------------------------------------------------

CREATE TABLE `parana_sku_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sku_id` bigint(20) NOT NULL COMMENT 'sku id',
  `quantity` bigint(20) NOT NULL COMMENT 'sku数量',
  `fee` bigint(20) NOT NULL COMMENT '实付金额',
  `status` smallint(6) NOT NULL COMMENT '子订单状态',
  `order_id` bigint(20) NOT NULL COMMENT '订单id',
  `buyer_id` bigint(20) NOT NULL COMMENT '买家id',
  `out_id` varchar(64) DEFAULT NULL COMMENT '外部自订单id',
  `buyer_name` varchar(32) DEFAULT NULL COMMENT '买家姓名',
  `out_buyer_id` varchar(512) DEFAULT NULL COMMENT '买家外部id',
  `item_id` bigint(20) NOT NULL COMMENT '商品id',
  `item_name` varchar(512) NOT NULL COMMENT '商品名称',
  `sku_image` varchar(512) DEFAULT NULL COMMENT 'sku主图',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(512) NOT NULL COMMENT '店铺名称',
  `out_shop_id` varchar(512) DEFAULT NULL COMMENT '店铺外部id',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `out_sku_id` varchar(64) DEFAULT NULL COMMENT 'sku外部id',
  `sku_attributes` varchar(512) DEFAULT NULL COMMENT 'sku属性, json表示',
  `channel` smallint(6) DEFAULT NULL COMMENT '订单渠道 1-pc, 2-手机',
  `pay_type` smallint(6) DEFAULT NULL COMMENT '支付类型 1-在线支付 2-货到付款',
  `shipment_type` smallint(6) DEFAULT NULL COMMENT '配送方式',
  `origin_fee` bigint(20) DEFAULT NULL COMMENT '原价',
  `discount` bigint(20) DEFAULT '0',
  `ship_fee` bigint(20) DEFAULT NULL COMMENT '运费',
  `ship_fee_discount` bigint(20) DEFAULT NULL COMMENT '运费折扣',
  `integral` int(11) DEFAULT NULL COMMENT '积分减免金额',
  `balance` int(11) DEFAULT NULL COMMENT '余额减免金额',
  `promotion_id` bigint(20) DEFAULT NULL COMMENT '单品级别的优惠id',
  `item_snapshot_id` bigint(20) DEFAULT NULL COMMENT '商品快照id',
  `has_refund` tinyint(4) DEFAULT NULL COMMENT '是否申请过退款',
  `invoiced` tinyint(4) DEFAULT NULL COMMENT '是否已开具过发票',
  `commented` smallint(6) DEFAULT NULL COMMENT '是否已评价',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '子订单额外信息,json表示',
  `tags_json` varchar(2048) DEFAULT NULL COMMENT '子订单tag信息, json表示',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sku_orders_buyer_id` (`buyer_id`),
  KEY `idx_sku_orders_shop_id` (`shop_id`),
  KEY `idx_sku_orders_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='sku维度订单';



# Dump of table parana_sku_templates
# ------------------------------------------------------------

CREATE TABLE `parana_sku_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sku_code` varchar(40) DEFAULT NULL COMMENT 'SKU 编码 (标准库存单位编码)',
  `spu_id` bigint(20) NOT NULL COMMENT '商品id',
  `specification` varchar(50) DEFAULT NULL COMMENT '型号/款式',
  `status` tinyint(1) NOT NULL COMMENT 'sku template 状态, 1: 上架, -1:下架,  -3:删除',
  `image` varchar(128) DEFAULT NULL COMMENT '图片url',
  `thumbnail` varchar(128) DEFAULT NULL COMMENT '样本图 (SKU 缩略图) URL',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `extra_price_json` varchar(255) DEFAULT NULL COMMENT '其他各种价格的json表示形式',
  `price` int(11) DEFAULT NULL COMMENT '实际售卖价格',
  `attrs_json` varchar(1024) DEFAULT NULL COMMENT 'json存储的sku属性键值对',
  `stock_type` tinyint(4) NOT NULL COMMENT '库存类型, 0: 不分仓存储, 1: 分仓存储, (冗余自SPU表)',
  `stock_quantity` int(11) DEFAULT NULL COMMENT '库存',
  `extra` text COMMENT 'sku额外信息',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_skutmpls_spu_id` (`spu_id`),
  KEY `idx_skutmpls_sku_code` (`sku_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='SKU模板表';



# Dump of table parana_skus
# ------------------------------------------------------------

CREATE TABLE `parana_skus` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sku_code` varchar(40) DEFAULT NULL COMMENT 'SKU 编码 (标准库存单位编码)',
  `item_id` bigint(20) NOT NULL COMMENT '商品id',
  `shop_id` bigint(20) unsigned NOT NULL COMMENT '店铺 ID (冗余自商品表)',
  `status` tinyint(1) NOT NULL COMMENT '商品状态 (冗余自商品表)',
  `specification` varchar(50) DEFAULT NULL COMMENT '型号/款式',
  `outer_sku_id` varchar(32) DEFAULT NULL COMMENT '外部sku编号',
  `outer_shop_id` varchar(32) DEFAULT NULL COMMENT '外部店铺id',
  `image` varchar(128) DEFAULT NULL COMMENT '图片url',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `extra_price_json` varchar(255) DEFAULT NULL COMMENT 'sku其他各种价格的json表示形式',
  `price` int(11) DEFAULT NULL COMMENT '实际售卖价格(低)',
  `attrs_json` varchar(1024) DEFAULT NULL COMMENT 'json存储的sku属性键值对',
  `stock_type` tinyint(4) NOT NULL COMMENT '库存类型, 0: 不分仓存储, 1: 分仓存储, (冗余自商品表)',
  `stock_quantity` int(11) DEFAULT NULL COMMENT '库存',
  `extra` text COMMENT 'sku额外信息',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `thumbnail` varchar(128) DEFAULT NULL COMMENT '样本图 (SKU 缩略图) URL',
  PRIMARY KEY (`id`),
  KEY `idx_skus_item_id` (`item_id`),
  KEY `idx_skus_shop_id` (`shop_id`),
  KEY `idx_skus_sku_code` (`sku_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品SKU表';



# Dump of table parana_special_delivery_fees
# ------------------------------------------------------------

CREATE TABLE `parana_special_delivery_fees` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_fee_template_id` bigint(20) NOT NULL COMMENT '所属运费模板id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `address_json` varchar(1024) NOT NULL COMMENT '区域id列表,json存储',
  `address_tree_json` text NOT NULL COMMENT '层级结构的区域json,主要用于前端展示',
  `desc` varchar(64) DEFAULT NULL COMMENT '描述',
  `is_free` tinyint(1) NOT NULL COMMENT '是否包邮',
  `deliver_method` tinyint(4) NOT NULL COMMENT '运送方式:1-快递,2-EMS,3-平邮',
  `charge_method` tinyint(4) NOT NULL COMMENT '计价方式:1-按计量单位,2-固定运费',
  `fee` int(11) DEFAULT NULL COMMENT '运费,当计价方式为固定运费时使用',
  `low_price` int(11) NULL COMMENT '订单不满该金额时，运费为lowFee',
  `low_fee` int(11) NULL COMMENT '订单不满low_price时，运费为lowFee',
  `high_price` int(11) NULL COMMENT '订单高于该金额时，运费为highFee',
  `high_fee` int(11) NULL COMMENT '订单高于high_price时，运费为highFee',
  `middle_fee` int(11) NULL COMMENT '订单价格在lowFee，highFee之间时，运费为middleFee',
  `init_amount` int(11) DEFAULT NULL COMMENT '首费数量',
  `init_fee` int(11) DEFAULT NULL COMMENT '首费金额',
  `incr_amount` int(11) DEFAULT NULL COMMENT '增费数量',
  `incr_fee` int(11) DEFAULT NULL COMMENT '增费金额',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_specail_delivery_fees_dfti` (`delivery_fee_template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='特殊区域运费';



# Dump of table parana_spu_attributes
# ------------------------------------------------------------

CREATE TABLE `parana_spu_attributes` (
  `spu_id` bigint(20) NOT NULL COMMENT 'spu id',
  `sku_attributes` varchar(4096) DEFAULT NULL COMMENT 'spu的sku属性, json存储',
  `other_attributes` varchar(8192) DEFAULT NULL COMMENT 'spu的其他属性, json存储',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`spu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spu属性';



# Dump of table parana_spu_details
# ------------------------------------------------------------

CREATE TABLE `parana_spu_details` (
  `spu_id` bigint(20) NOT NULL COMMENT 'spu id',
  `images_json` varchar(1024) DEFAULT NULL COMMENT '图片列表, json表示',
  `detail` text COMMENT '富文本详情',
  `packing_json` varchar(1024) DEFAULT NULL COMMENT '包装清单,kv对, json表示',
  `service` text COMMENT '售后服务',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`spu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='SPU详情';



# Dump of table parana_spus
# ------------------------------------------------------------

CREATE TABLE `parana_spus` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `spu_code` varchar(40) DEFAULT NULL COMMENT 'spu编码',
  `category_id` int(11) unsigned NOT NULL COMMENT '后台类目 ID',
  `brand_id` bigint(20) DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(100) DEFAULT NULL COMMENT '品牌名称',
  `name` varchar(200) NOT NULL COMMENT 'spu名称',
  `main_image` varchar(128) DEFAULT NULL COMMENT '主图',
  `low_price` int(11) DEFAULT NULL COMMENT '实际售卖价格(所有sku的最低实际售卖价格)',
  `high_price` int(11) DEFAULT NULL COMMENT '实际售卖价格(所有sku的最高实际售卖价格)',
  `stock_type` tinyint(4) DEFAULT NULL COMMENT '库存类型, 0: 不分仓存储, 1: 分仓存储',
  `stock_quantity` int(11) DEFAULT NULL COMMENT '库存',
  `status` tinyint(1) NOT NULL COMMENT '状态',
  `advertise` varchar(255) DEFAULT NULL COMMENT '广告语',
  `specification` varchar(128) DEFAULT NULL COMMENT '规格型号',
  `type` smallint(6) DEFAULT NULL COMMENT 'spu类型 1为普通spu, 2为组合spu',
  `reduce_stock_type` smallint(6) DEFAULT '1' COMMENT '减库存方式, 1为拍下减库存, 2为付款减库存',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT 'spu额外信息,建议json字符串',
  `spu_info_md5` char(32) DEFAULT NULL COMMENT 'spu信息的m5值, 交易快照可能需要和这个摘要进行对比',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_spus_spu_code` (`spu_code`),
  KEY `idx_spus_cid` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='SPU表';



# Dump of table parana_sub_seller_roles
# ------------------------------------------------------------

CREATE TABLE `parana_sub_seller_roles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) DEFAULT NULL COMMENT '角色名',
  `desc` varchar(256) DEFAULT NULL COMMENT '角色描述',
  `master_user_id` bigint(20) NOT NULL COMMENT '主账户用户 ID',
  `status` tinyint(4) NOT NULL COMMENT '0. 未生效(冻结), 1. 生效, -1. 删除',
  `allow_json` varchar(2048) DEFAULT NULL COMMENT '角色对应选中权限树节点列表',
  `extra_json` varchar(4096) DEFAULT NULL COMMENT '用户额外信息,建议json字符串',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sub_seller_roles_master_user_id` (`master_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='卖家角色表';



# Dump of table parana_unionpay_trans
# ------------------------------------------------------------

CREATE TABLE `parana_unionpay_trans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `transaction_code` varchar(32) DEFAULT NULL COMMENT '交易码',
  `acq_ins_code` varchar(32) DEFAULT NULL COMMENT '代理机构标识码',
  `send_code` varchar(32) DEFAULT NULL COMMENT '发送机构标识码',
  `trace_no` varchar(32) DEFAULT NULL COMMENT '系统跟踪号',
  `pay_card_no` varchar(32) DEFAULT NULL COMMENT '帐号',
  `txn_amt` varchar(32) DEFAULT NULL COMMENT '交易金额',
  `mer_cat_code` varchar(32) DEFAULT NULL COMMENT '商户类别',
  `term_type` varchar(32) DEFAULT NULL COMMENT '终端类型',
  `query_id` varchar(32) DEFAULT NULL COMMENT '查询流水号',
  `type` varchar(32) DEFAULT NULL COMMENT '支付方式（旧）',
  `order_id` varchar(32) DEFAULT NULL COMMENT '商户订单号',
  `pay_card_type` varchar(32) DEFAULT NULL COMMENT '支付卡类型',
  `original_trace_no` varchar(32) DEFAULT NULL COMMENT '原始交易的系统跟踪号',
  `original_time` varchar(32) DEFAULT NULL COMMENT '原始交易日期时间',
  `third_party_fee` varchar(32) DEFAULT NULL COMMENT '商户手续费',
  `settle_amount` varchar(32) DEFAULT NULL COMMENT '结算金额',
  `pay_type` varchar(32) DEFAULT NULL COMMENT '支付方式',
  `company_code` varchar(32) DEFAULT NULL COMMENT '集团商户代码',
  `txn_type` varchar(32) DEFAULT NULL COMMENT '交易类型',
  `txn_sub_type` varchar(32) DEFAULT NULL COMMENT '交易子类',
  `biz_type` varchar(32) DEFAULT NULL COMMENT '业务类型',
  `acc_type` varchar(32) DEFAULT NULL COMMENT '帐号类型',
  `bill_type` varchar(32) DEFAULT NULL COMMENT '账单类型',
  `bill_no` varchar(32) DEFAULT NULL COMMENT '账单号码  ',
  `interact_mode` varchar(32) DEFAULT NULL COMMENT '交互方式',
  `orig_qry_id` varchar(32) DEFAULT NULL COMMENT '原交易查询流水号',
  `mer_id` varchar(32) DEFAULT NULL COMMENT '商户代码',
  `divide_type` varchar(32) DEFAULT NULL COMMENT '分账入账方式',
  `sub_mer_id` varchar(32) DEFAULT NULL COMMENT '二级商户代码',
  `sub_mer_abbr` varchar(32) DEFAULT NULL COMMENT '二级商户简称',
  `divide_amount` varchar(32) DEFAULT NULL COMMENT '二级商户分账入账金额',
  `clearing` varchar(32) DEFAULT NULL COMMENT '清算净额',
  `term_id` varchar(32) DEFAULT NULL COMMENT '终端号',
  `mer_reserved` varchar(32) DEFAULT NULL COMMENT '商户自定义域',
  `discount` varchar(32) DEFAULT NULL COMMENT '优惠金额',
  `invoice` varchar(32) DEFAULT NULL COMMENT '发票金额',
  `addition_third_party_fee` varchar(32) DEFAULT NULL COMMENT '分期付款附加手续费',
  `stage` varchar(32) DEFAULT NULL COMMENT '分期付款期数',
  `transaction_media` varchar(32) DEFAULT NULL COMMENT '交易介质',
  `original_order_id` varchar(32) DEFAULT NULL COMMENT '原始交易订单号',
  `txn_time` datetime DEFAULT NULL COMMENT '交易时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_put_query_id` (`query_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_user_files
# ------------------------------------------------------------

CREATE TABLE `parana_user_files` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `create_by` bigint(20) NOT NULL COMMENT '用户id',
  `file_type` smallint(6) NOT NULL COMMENT '文件类型',
  `group` varchar(128) DEFAULT NULL COMMENT '用户族',
  `folder_id` bigint(20) NOT NULL COMMENT '文件夹id',
  `name` varchar(128) DEFAULT '' COMMENT '文件名称',
  `path` varchar(128) NOT NULL DEFAULT '' COMMENT '文件相对路径',
  `size` int(11) NOT NULL COMMENT '文件大小',
  `extra` varchar(512) DEFAULT '' COMMENT '文件信息介绍',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_files_create_by` (`create_by`),
  KEY `idx_user_files_folder_id` (`folder_id`),
  KEY `idx_user_files_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户文件表';



# Dump of table parana_user_folders
# ------------------------------------------------------------

CREATE TABLE `parana_user_folders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `create_by` bigint(20) NOT NULL COMMENT '用户id',
  `group` varchar(128) DEFAULT NULL COMMENT '用户族',
  `pid` bigint(20) DEFAULT NULL COMMENT '父级id',
  `level` tinyint(1) DEFAULT NULL COMMENT '级别',
  `has_children` bit(1) DEFAULT NULL COMMENT '是否有孩子',
  `folder` varchar(128) NOT NULL DEFAULT '' COMMENT '文件夹名称',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_folders_create_by` (`create_by`),
  KEY `idx_user_folders_pid` (`pid`),
  KEY `idx_user_folders_folder` (`folder`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户文件夹管理表';



# Dump of table parana_user_operators
# ------------------------------------------------------------

CREATE TABLE `parana_user_operators` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名 (登录名, 冗余)',
  `role_id` bigint(20) DEFAULT NULL COMMENT '运营角色 ID',
  `role_name` varchar(32) DEFAULT NULL COMMENT '角色名 (冗余)',
  `status` tinyint(4) NOT NULL COMMENT '运营状态',
  `extra_json` varchar(4096) DEFAULT NULL COMMENT '运营额外信息, 建议json字符串',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_operator_user_id` (`user_id`),
  KEY `idx_user_operator_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户运营表';



# Dump of table parana_user_profiles
# ------------------------------------------------------------

CREATE TABLE `parana_user_profiles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `realname` varchar(32) DEFAULT NULL COMMENT '真实姓名',
  `gender` smallint(6) DEFAULT NULL COMMENT '性别1男2女',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省id',
  `province` varchar(100) DEFAULT '' COMMENT '省',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城id',
  `city` varchar(100) DEFAULT NULL COMMENT '城',
  `region_id` bigint(20) DEFAULT NULL COMMENT '区id',
  `region` varchar(100) DEFAULT NULL COMMENT '区',
  `street` varchar(130) DEFAULT NULL COMMENT '地址',
  `extra_json` varchar(2048) DEFAULT NULL COMMENT '其他信息, 以json形式存储',
  `avatar` varchar(512) NOT NULL COMMENT '头像',
  `birth` varchar(40) DEFAULT NULL COMMENT '出生日期',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户详情表';



# Dump of table parana_user_promotions
# ------------------------------------------------------------

CREATE TABLE `parana_user_promotions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `promotion_id` bigint(20) NOT NULL COMMENT '营销活动id',
  `type` smallint(6) NOT NULL COMMENT '营销工具类型,冗余',
  `name` varchar(32) NOT NULL COMMENT '营销活动名称',
  `available_quantity` bigint(20) DEFAULT NULL COMMENT '可用数量(或金额)',
  `frozen_quantity` bigint(20) DEFAULT NULL COMMENT '冻结数量(或金额)',
  `status` smallint(6) NOT NULL COMMENT '状态, 0: 初始化, 1: 可能生效, 需要根据生效开始和截至时间进一步判断 -1:已过期',
  `extra_json` varchar(512) DEFAULT NULL COMMENT '附加信息',
  `start_at` datetime NOT NULL COMMENT '营销活动开始时间',
  `end_at` datetime NOT NULL COMMENT '营销活动结束时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_up_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table parana_user_sellers
# ------------------------------------------------------------

CREATE TABLE `parana_user_sellers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名 (冗余)',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺 ID',
  `shop_name` varchar(64) DEFAULT NULL COMMENT '店铺名 (冗余)',
  `status` tinyint(4) NOT NULL COMMENT '状态',
  `extra_json` varchar(4096) DEFAULT NULL COMMENT '用户额外信息, 建议json字符串',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_seller_user_id` (`user_id`),
  KEY `idx_user_seller_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商家子账户表';



# Dump of table parana_user_sub_sellers
# ------------------------------------------------------------

CREATE TABLE `parana_user_sub_sellers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名 (冗余)',
  `master_user_id` bigint(20) NOT NULL COMMENT '主账户用户 ID',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色 ID',
  `role_name` varchar(32) DEFAULT NULL COMMENT '角色名 (冗余)',
  `status` tinyint(4) NOT NULL COMMENT '状态',
  `extra_json` varchar(4096) DEFAULT NULL COMMENT '用户额外信息, 建议json字符串',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_sub_seller_user_id` (`user_id`),
  KEY `idx_user_sub_seller_sub_id` (`master_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商家子账户表';



# Dump of table parana_users
# ------------------------------------------------------------

CREATE TABLE `parana_users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(40) DEFAULT NULL COMMENT '用户名',
  `email` varchar(32) DEFAULT NULL COMMENT '邮件',
  `mobile` varchar(16) DEFAULT NULL COMMENT '手机号码',
  `password` varchar(32) DEFAULT NULL COMMENT '登录密码',
  `type` smallint(6) NOT NULL COMMENT '用户类型',
  `status` tinyint(4) NOT NULL COMMENT '状态 0:未激活, 1:正常, -1:锁定, -2:冻结, -3: 删除',
  `roles_json` varchar(512) DEFAULT NULL COMMENT '用户角色信息',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT '用户额外信息,建议json字符串',
  `tags_json` varchar(1024) DEFAULT NULL COMMENT '用户标签的json表示形式,只能运营操作, 对商家不可见',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_users_name` (`name`),
  UNIQUE KEY `idx_users_email` (`email`),
  UNIQUE KEY `idx_users_mobile` (`mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';



# Dump of table parana_wechatpay_trans
# ------------------------------------------------------------

CREATE TABLE `parana_wechatpay_trans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(32) NOT NULL COMMENT '微信支付订单号',
  `out_trade_no` varchar(32) NOT NULL COMMENT '商户系统的订单号，与请求一致',
  `trade_status` varchar(16) DEFAULT NULL COMMENT '交易状态 SUCCESS FAIL',
  `trade_time` varchar(32) NOT NULL COMMENT '交易时间',
  `appid` varchar(64) DEFAULT NULL COMMENT '公众账号 ID',
  `mch_id` varchar(32) DEFAULT NULL COMMENT '微信支付分配的商户号',
  `sub_mch_id` varchar(32) DEFAULT NULL COMMENT '微信支付分配的子商户号， 受理模式下必填',
  `device_info` varchar(128) DEFAULT NULL COMMENT '微信支付分配的终端设备号',
  `open_id` varchar(128) DEFAULT NULL COMMENT '用户标识',
  `trade_type` varchar(16) DEFAULT NULL COMMENT 'JSAPI、NATIVE、APP 交易类型',
  `bank_type` varchar(32) DEFAULT NULL COMMENT '付款银行，采用字符串类型的银行标识',
  `fee_type` varchar(16) DEFAULT NULL COMMENT '货币种类，符合ISO 4217 标准的三位字母代码，默认人民币：CNY',
  `total_fee` varchar(64) NOT NULL COMMENT '本次交易金额 元',
  `coupon_fee` varchar(64) DEFAULT NULL COMMENT '现金券金额 元',
  `refund_apply_date` varchar(32) DEFAULT NULL COMMENT '退款申请时间',
  `refund_success_date` varchar(32) DEFAULT NULL COMMENT '退款成功时间',
  `refund_id` varchar(32) DEFAULT NULL COMMENT '微信退款单号',
  `out_refund_no` varchar(32) DEFAULT NULL COMMENT '商户退款单号',
  `refund_fee` varchar(64) DEFAULT NULL COMMENT '退款金额',
  `coupon_refund_fee` varchar(64) DEFAULT NULL COMMENT '现金券退款金额',
  `refund_channel` varchar(16) DEFAULT NULL COMMENT '退款类型 ORIGINAL—原路退款 BALANCE—退回到余额',
  `refund_status` varchar(16) DEFAULT NULL COMMENT '退款状态：SUCCES—退款成功 FAIL—退款失败 PROCESSING—退款处理中 NOTSURE—未确定，需要商户 原退款单号重新发起 CHANGE—转入代发，退款到 银行发现用户的卡作废或者冻结了，导致原路退款银行 卡失败，资金回流到商户的现金帐号，需要商户人工干 预，通过线下或者财付通转 账的方式进行退款。',
  `body` varchar(128) DEFAULT NULL COMMENT '商品名称',
  `attach` text COMMENT '商户数据包 附加数据',
  `poundage_fee` varchar(64) DEFAULT NULL COMMENT '手续费',
  `rate` varchar(16) DEFAULT NULL COMMENT '费率',
  `bank_order_no` varchar(64) DEFAULT NULL COMMENT '银行订单号',
  `trade_info` text COMMENT '交易说明',
  `trade_at` datetime DEFAULT NULL COMMENT '交易时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='微信支付账务明细';