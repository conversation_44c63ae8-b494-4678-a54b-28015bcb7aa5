package moonstone.user.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户绑定汇付支付
 */
@Data
public class UserBindHuifu implements Serializable {
    
    private Long id;
    
    private Long shopId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * userId的替代品，本地用户唯一标示（门店、服务商）
     */
    private String bizUserId;
    
    /**
     * 与汇付产生绑定关系的userId
     */
    private String huifuUserId;
    
    /**
     * 汇付会员类型 2：企业会员 3：个人会员
     */
    private Long huifuRoleType;
    
    /**
     * 角色类型（门店、服务商）
     */
    private String roleType;
    
    /**
     * 实名：用户姓名（法人姓名）
     */
    private String authUserName;
    
    /**
     * 实名：用户证件号
     */
    private String authUserIdNumber;
    
    /**
     * 实名：用户证件类型
     */
    private String authUserIdType;
    
    /**
     * 绑定的手机号
     */
    private String bindPhone;
    
    /**
     * 当前绑定企业流程走到哪一步
     * 企业：【绑定手机号、设置企业信息】
     * 个人：【实名认证、绑定手机号、绑定完第一张卡】
     */
    private String bindStep;
    
    /**
     * 所选通商身份是否走完 true：走完
     */
    private Boolean stepStatus;
    
    /**
     * 账户标识 微信-openid,支付宝-userid
     * 仅记录创建时使用的
     */
    private String acct;
    
    /**
     * 微信appId
     */
    private String appId;
    
    /**
     * 支付账户类型
     */
    private String acctType;
    
    private Boolean isEnabled;
    
    /**
     * 绑定的银行卡号(第二次绑卡会被覆盖)
     */
    private String bindCardNo;
    
    private String bindCardPhone;
    
    private String bindCardData;
    
    private String companyInfo;
    
    private String companyInfoResult;
    
    private String companyInfoFailReason;
    
    /**
     * 电子协议签约状态：0未签约1签约中2已签约
     */
    private Integer signContractStatus;
    
    /**
     * 账户提现协议编号
     */
    private String acctProtocolNo;
    
    /**
     * 开户行支行名
     */
    private String bindCardBranchName;
    
    /**
     * 会员状态
     */
    private Boolean memberStatus;
    
    private Date createdAt;
    
    private Date updatedAt;
    
    /**
     * 0-对私
     * 1- 对公
     */
    private Long payAcctType;
}
