package blue.sea.moonstone.bridge.app

import blue.sea.moonstone.bridge.model.Request
import com.google.gson.Gson
import io.vertx.core.eventbus.{DeliveryOptions, Message}
import io.vertx.core.http.impl.headers.HeadersMultiMap
import io.vertx.core._
import io.vertx.core.json.JsonObject
import org.slf4j.Logger

import java.io.ByteArrayInputStream
import java.util
import java.util.function.Consumer


object HttpServletVerticle {
  private val log: Logger = org.slf4j.LoggerFactory.getLogger(classOf[HttpServletVerticle])
  val GSON: Gson = new Gson
}

class HttpServletVerticle extends AbstractVerticle {
  protected def serve(url: String, handler: Consumer[Request]): HttpServletVerticle = {
    vertx.eventBus.consumer("http:" + url).handler(handleMessageByHandler(handler))
    vertx.eventBus().publish("http-service-center", new JsonObject()
      .put("path", s"http:$url")
      .put("verticle", getClass.getName)
    )
    this
  }

  private def handleMessageByHandler(handler: Consumer[Request]): Handler[Message[Array[Byte]]] = {
    (message: Message[Array[Byte]]) => {
      def foo(message: Message[Array[Byte]]): Unit = {
        val path: String = message.headers.get(".path")
        val query: String = message.headers.get(".query")
        val queryMap: util.Map[String, String] = wrapQuery(query)
        val header: MultiMap = new HeadersMultiMap
        val response: Promise[Array[Byte]] = Promise.promise
        response.future.onComplete((result: AsyncResult[Array[Byte]]) => {
          if (result.succeeded) {
            message.reply(result.result, new DeliveryOptions().setHeaders(header))
          }
          else {
            message.fail(-1, result.cause.getMessage)
            HttpServletVerticle.log.error("{} fail to reply", "[Handler-Response]", result.cause)
          }
        })
        try handler.accept(Request(path
          , queryMap
          , message.headers
          , Option.apply(message.body()).map(byte => new ByteArrayInputStream(byte))
            .getOrElse[ByteArrayInputStream](new ByteArrayInputStream(new Array[Byte](0)))
          , header
          , SessionAtRedisByVertx.generateSession(vertx
            , SessionAtRedisByVertx.extractSessionId(vertx
              , message.headers)
            , header)
          , Option.apply(message.headers.get(Request.USER))
          , response))
        catch {
          case e: Exception =>
            HttpServletVerticle.log.error("{} fail to handle the [Path => {}, Query => {}] method", "[Serv-Middle]", path, query, e)
            message.fail(-1, e.getMessage)
        }
      }

      foo(message)
    }
  }

  protected def wrapQuery(query: String): util.Map[String, String] = {
    var queryMap: util.HashMap[String, String] = null
    if (query != null) {
      queryMap = new util.HashMap[String, String]
      for (part <- query.split("&")) {
        val kv: Array[String] = part.split("=", 2)
        if (kv.length != 0) {
          if (kv.length > 1) {
            queryMap.put(kv(0), kv(1))
          }
          else {
            queryMap.put(kv(0), "")
          }
        }
      }
    }
    queryMap
  }
}
