<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>moonstone-mall</artifactId>
        <groupId>moonstone</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>moonstone-web-distribution</artifactId>
    <version>1.0.0.RELEASE</version>

    <dependencies>
        <!-- moonstone -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-item-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-decoration-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-distribution-api</artifactId>
        </dependency>

        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-web-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!--pay-->
        <dependency>
            <groupId>io.terminus.pay</groupId>
            <artifactId>terminus-pay-api</artifactId>
            <optional>true</optional>
        </dependency>

        <!--session-->

        <!--ip-->
        <dependency>
            <groupId>io.terminus.ip</groupId>
            <artifactId>terminus-ip</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>2.8.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-item</artifactId>
            <version>1.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-distribution</artifactId>
            <version>1.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>