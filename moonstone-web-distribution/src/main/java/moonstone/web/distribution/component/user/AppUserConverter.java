package moonstone.web.distribution.component.user;

import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.utils.LogUtil;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.UserProfile;
import moonstone.weShop.model.WeShop;
import moonstone.web.distribution.user.app.view.UserForApp;
import moonstone.web.distribution.user.app.view.UserForAppImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AppUserConverter {
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private UserProfileCacheHolder userProfileCacheHolder;

    /**
     * convert user into userAppView
     *
     * @param user user
     * @return view
     */
    public Optional<UserForApp> convert(BaseUser user) {
        UserForAppImpl userForApp = new UserForAppImpl();
        Optional<WeShop> weShopOpt = weShopCacheHolder.findByUserId(user.getId());
        if (!weShopOpt.isPresent()) {
            log.error("{} fail to convert user[{}] into AppView(compose with weShop), cause weShop not exists", LogUtil.getClassMethodName(), user.getId());
            return Optional.empty();
        }
        BeanUtils.copyProperties(weShopOpt.get(), userForApp);
        Optional.ofNullable(userProfileCacheHolder.getUserProfileByUserId(user.getId()))
                .map(UserProfile::getAvatar)
                .ifPresent(userForApp::setAvatar);
        return Optional.of(userForApp);
    }
}
