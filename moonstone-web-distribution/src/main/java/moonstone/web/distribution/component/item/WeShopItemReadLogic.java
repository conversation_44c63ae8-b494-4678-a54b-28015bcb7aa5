package moonstone.web.distribution.component.item;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopItemSearchCacher;
import moonstone.cache.WeShopSkuCacheHolder;
import moonstone.category.dto.ShopCategoryWithChildren;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.BondedType;
import moonstone.common.model.CommonUser;
import moonstone.common.model.PagingCriteria;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.ResultResponse;
import moonstone.countryImage.model.CountryImage;
import moonstone.countryImage.service.CountryImageReadService;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.item.dto.ViewedItem;
import moonstone.item.dto.paging.SearchItemCriteria;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.search.dto.SearchedItem;
import moonstone.weShop.dto.DistributionViewedItem;
import moonstone.weShop.dto.RichWeShopItem;
import moonstone.weShop.dto.SearchItemForWeDistributor;
import moonstone.weShop.enums.WeShopItemIndexDisplayStatus;
import moonstone.weShop.enums.WeShopItemStatus;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.web.distribution.config.DistributionConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * Create By wulianlei@2018/12/19
 * Modified By wulianlei@2018/12/26
 */
@Component
@Slf4j
public class WeShopItemReadLogic {
    @RpcConsumer
    private ItemReadService itemReadService;
    @RpcConsumer
    private ShopCategoryItemReadService shopCategoryItemReadService;
    @RpcConsumer
    private ShopCategoryReadService shopCategoryReadService;
    @RpcConsumer
    private WeShopItemReadService weShopItemReadService;
    @RpcConsumer
    private WeShopItemWriteService weShopItemWriteService;
    @RpcConsumer
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @RpcConsumer
    private CountryImageReadService countryImageReadService;
    @RpcConsumer
    private SkuReadService skuReadService;
    @RpcConsumer
    private SkuCustomReadService skuCustomReadService;
    @RpcConsumer
    private DeliveryFeeReadService deliveryFeeTemplateReadService;
    @Autowired
    private WeShopItemSearchCacher weShopItemSearchCacher;
    @Autowired
    private DistributionConfig distributionConfig;
    @Autowired
    private WeShopSkuCacheHolder weShopSkuCacheHolder;

    /**
     * 根据商品id查找微分销商城店主端对应的商品信息及sku, 这个是给渲染店主端商品详情页用的
     *
     * @param weShopId 微分销店铺id
     * @param itemId   商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    public Response<DistributionViewedItem> findForDistributionView(Long weShopId, Long itemId) {
        try {
            Response<ViewedItem> rViewedItem = itemReadService.findForView(itemId);
            if (!rViewedItem.isSuccess()) {
                log.error("failed to find item for view by id={}, error code: {}", itemId, rViewedItem.getError());
                throw new JsonResponseException(rViewedItem.getError());
            }
            ViewedItem viewedItem = rViewedItem.getResult();
            DistributionViewedItem distributionViewedItem = CopyUtil.copy(viewedItem, DistributionViewedItem.class);
            //处理微分销商品最高、最低售价、最高最低利润
            List<Sku> skus = distributionViewedItem.getSkus();
            Integer lowPrice = null;
            Integer highPrice = null;
            Integer lowProfit = null;
            Integer highProfit = null;
            for (Sku sku : skus) {
                Optional<WeShopSku> weShopSku = weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShopId, sku.getId());
                if (lowProfit == null)
                    lowProfit = weShopSku.map(WeShopSku::getProfit).map(Long::intValue).orElse(null);
                else
                    lowProfit = Math.min(lowProfit, weShopSku.map(WeShopSku::getProfit).map(Long::intValue).orElse(Integer.MAX_VALUE));

                if (highProfit == null)
                    highProfit = weShopSku.map(WeShopSku::getProfit).map(Long::intValue).orElse(null);
                else
                    highProfit = Math.min(highProfit, weShopSku.map(WeShopSku::getProfit).map(Long::intValue).orElse(Integer.MIN_VALUE));

                if (lowPrice == null) lowPrice = weShopSku.map(WeShopSku::getPrice).map(Long::intValue).orElse(null);
                else
                    lowPrice = Math.min(lowPrice, weShopSku.map(WeShopSku::getPrice).map(Long::intValue).orElse(Integer.MAX_VALUE));

                if (highPrice == null) highPrice = weShopSku.map(WeShopSku::getPrice).map(Long::intValue).orElse(null);
                else
                    highPrice = Math.min(highPrice, weShopSku.map(WeShopSku::getPrice).map(Long::intValue).orElse(Integer.MIN_VALUE));
            }
            distributionViewedItem.setLowDistributionPrice(lowPrice);
            distributionViewedItem.setHighDistributionPrice(highPrice);
            distributionViewedItem.setLowProfit(lowProfit);
            distributionViewedItem.setHighProfit(highProfit);
            Response<WeShopItem> rWeShopItem = weShopItemReadService.findByWeShopIdAndItemId(weShopId, itemId);
            if (!rWeShopItem.isSuccess()) {
                log.error("failed to find weShop item by weShopId={}, itemId={}, error code: {}", weShopId, itemId, rWeShopItem.getError());
                throw new JsonResponseException(rWeShopItem.getError());
            }
            distributionViewedItem.setWeShopItem(rWeShopItem.getResult());
            return Response.ok(distributionViewedItem);
        } catch (Exception e) {
            log.error("fail to find for distribution view by weShopId={}, itemId={}, cause: {}", weShopId, itemId, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 扩充查找范围然后添加到缓存内存中
     * 以每次查询极限为500进行缓存查询 实际上300后就已经达到了性能瓶颈，但是500以内依旧可以接收
     * 只缓存该信息还是？目前不做过度优化 因为不属于少变动数据，且切分后效率达标
     *
     * @param userId             用户id
     * @param searchItemCriteria 分页查询聚合
     * @return 不包括总数量总数的查询分页数据聚合
     */
    public Paging<SearchItemForWeDistributor> searchForWeShop(long userId, SearchItemCriteria searchItemCriteria) {
        //参数重定义
        if (searchItemCriteria.getPageSize() > 500)//超过500大小的是毫无意义的且低效率
            searchItemCriteria.setPageSize(500);//强行设置500
        //提取账户信息
        List<WeShopShopAccount> weShopShopAccounts = getWeShopAccountByUserId(userId);
        //获取缓存
        long total = weShopItemSearchCacher.getWeShopItemSearchedCount(userId, weShopShopAccounts.stream().map(WeShopShopAccount::getShopId).filter(Objects::nonNull).collect(toList()), searchItemCriteria);
        Paging<SearchItemForWeDistributor> data = getSearchForWeShopData(weShopShopAccounts, searchItemCriteria);
        log.debug("[search](weShop)-start data size:{}", data.getData().size());
        //过滤不符合status条件的例子
        data.setData(data.getData().stream().filter((searchItemForWeDistributor -> {
            if (searchItemCriteria.getStatus() == null || searchItemCriteria.getStatus() == 0) {
                return true;
            }
            boolean status = searchItemCriteria.getStatus() > 0;
            if (status) {
                if (searchItemForWeDistributor.getWeShopItem() != null) {
                    if (searchItemForWeDistributor.getWeShopItem().getStatus() > 0) {
                        return true;
                    }
                }
            } else {
                if (searchItemForWeDistributor.getWeShopItem() == null)
                    return true;
                if (searchItemForWeDistributor.getWeShopItem().getStatus() < 0)
                    return true;
            }
            log.debug("skip searchItemForWeDistributor (Id:{}) (name:{}) weShopItem:{}", searchItemForWeDistributor.getSearchedItem().getId(), searchItemForWeDistributor.getSearchedItem().getName(), JSON.toJSONString(searchItemForWeDistributor.getWeShopItem()));
            return false;
        })).collect(Collectors.toList()));
        log.debug("[search](weShop)-final data size:{}", data.getData().size());
        data.setTotal(total);
        return data;
    }

    private List<WeShopShopAccount> getWeShopAccountByUserId(long userId) {
        return weShopShopAccountReadService.findByUserId(userId).getResult().stream().filter(Objects::nonNull).collect(toList());
    }

    /**
     * 根据条件查询用户所拥有的所有分销店铺商品
     *
     * @param weShopShopAccounts 用户账户信息
     * @param searchItemCriteria 分页查询条件聚合 limit为必需其它均为非必要条件
     * @return 不包括总数量总数的查询分页数据聚合
     */
    public Paging<SearchItemForWeDistributor> getSearchForWeShopData(List<WeShopShopAccount> weShopShopAccounts, SearchItemCriteria searchItemCriteria) {
        //处理简单逻辑 负责提取itemList然后交给pack进行深度打包
        String name = searchItemCriteria.getName();
        long shopId = searchItemCriteria.getShopId();
        long shopCatId = searchItemCriteria.getShopCatId();
        int limit = searchItemCriteria.getLimit();
        int offset = searchItemCriteria.getOffset();
        int status = searchItemCriteria.getStatus() == null ? 0 : searchItemCriteria.getStatus();
        List<Item> itemList;
        List<Long> shopIds;
        //List<WeShopShopAccount> weShopShopAccounts = weShopShopAccountReadService.findByUserId(userId).getResult().stream().filter(Objects::nonNull).collect(toList());
        if (shopId == 0) {
            shopIds = weShopShopAccounts.stream()
                    .map(WeShopShopAccount::getShopId).filter(Objects::nonNull).collect(toList());
        } else {
            shopIds = Stream.of(shopId).collect(toList());
        }
        if (shopIds.size() == 0) {
            return Paging.empty();
        }
        if (ObjectUtils.isEmpty(name)) {
            if (shopCatId == 0) {
                if (status == 0) {
                    log.debug("[search](weShop)-find by shopIds");
                    itemList = itemReadService.pageByShopIdsInWeShop(shopIds, offset, limit).getResult();
                } else {
                    log.debug("[search](weShop)-find by shopIds,status");
                    itemList = itemReadService.pageByShopIdsAndStatusInWeShop(shopIds, offset, limit, status).getResult();
                }
                //直接单表查询
            } else {
                log.debug("[search](weShop)-find by shopCatId and shopIds");
                List<Long> itemIds = shopCategoryItemReadService.pageByShopIdsAndShopCategoryId(shopIds, shopCatId, offset, limit).getResult().stream().map(ShopCategoryItem::getItemId).collect(toList());
                if (status == 0) {
                    log.debug("[search](weShop)-find by shopCatId-items");
                    itemList = itemReadService.findByIdsInWeShop(itemIds).getResult();
                } else {
                    log.debug("[search](weShop)-find by shopCatId-items,status");
                    itemList = itemReadService.findByIdsAndStatusInWeShop(itemIds, status).getResult();
                }
                //二次查询 先查shopCategoryItem再筛选
            }
        } else {
            if (shopCatId == 0) {
                if (status == 0) {
                    log.debug("[search](weShop)-find by shopIds,name");
                    itemList = itemReadService.pageByShopIdsAndLikeNameInWeShop(shopIds, name, offset, limit).getResult();
                } else {
                    log.debug("[search](weShop)-find by shopIds,status,name");
                    itemList = itemReadService.pageByShopIdsAndLikeNameAndStatusInWeShop(shopIds, name, offset, limit, status).getResult();
                }
            } else {
                log.debug("[search](weShop)-find by shopCatId and shopIds");
                List<Long> itemIds = shopCategoryItemReadService.findByShopIdsAndShopCategoryId(shopIds, shopCatId).getResult().stream().map(ShopCategoryItem::getItemId).collect(toList());
                if (status == 0) {
                    log.debug("[search](weShop)-find by shopCatId-items,name");
                    itemList = itemReadService.pageByItemIdsAndLikeNameInWeShop(itemIds, name, offset, limit).getResult();
                } else {
                    log.debug("[search](weShop)-find by shopCatId-items,status,name");
                    itemList = itemReadService.pageByItemIdsAndLikeNameAndStatusInWeShop(itemIds, name, offset, limit, status).getResult();
                }
            }
        }
        if (itemList.size() == 0) {
            return Paging.empty();
        }
        log.debug("[search](weShop)-start found item list size:{}", itemList.size());
        itemList = itemList.stream().filter((it) -> Objects.equals(it.getExtra().get("sellInWeShop"), "true")).collect(toList());
        log.debug("[search](weShop)-final sell in weShop item list size:{}", itemList.size());
        List<SearchItemForWeDistributor> resultList = packItemIntoDistribution(itemList, weShopShopAccounts.stream().map(WeShopShopAccount::getWeShopId).filter(Objects::nonNull).collect(toList()));
        Paging<SearchItemForWeDistributor> searchItemPaging = new Paging<>();
        searchItemPaging.setData(resultList);
        return searchItemPaging;
    }

    private List<SearchItemForWeDistributor> packItemIntoDistribution(List<Item> itemList, List<Long> weShopIdList) {
        //进行深度打包 将其他数据也打包进去
        //打包后结果列表
        List<SearchItemForWeDistributor> searchItemForWeDistributorList = new ArrayList<>();
        //从item获取weShopItem列表,可能一个item对应着多个weShopItem
        List<WeShopItem> weShopItemList = weShopItemReadService.findByWeShopIds(weShopIdList).getResult().stream().filter(Objects::nonNull).collect(toList());

        //获取所有的物品单品(Sku)列表
        List<Sku> skuList = skuReadService.findSkusByItemIds(itemList.stream().map(Item::getId).collect(toList())).getResult().stream().filter(Objects::nonNull).collect(toList());

        //获取所有的skuCustom
        List<SkuCustom> skuCustomsList;
        if (skuList.size() != 0) {
            skuCustomsList = skuCustomReadService.findBySkuIds(skuList.stream().map(Sku::getId).collect(toList())).getResult().stream().filter(Objects::nonNull).collect(toList());
        } else {
            skuCustomsList = new ArrayList<>();
        }

        //获取所有的物品的运费模板
        List<ItemDeliveryFee> itemDeliveryFeeList = deliveryFeeTemplateReadService.findByItemIds(itemList.stream().map(Item::getId).collect(toList())).getResult().stream().filter(Objects::nonNull).collect(toList());

        //获取使用到的运费模板
        List<DeliveryFeeTemplate> deliveryFeeTemplateList;
        if (itemDeliveryFeeList.size() != 0) {
            deliveryFeeTemplateList = deliveryFeeTemplateReadService.findByIds(itemDeliveryFeeList.stream().map(ItemDeliveryFee::getDeliveryFeeTemplateId).collect(toList())).getResult().stream().filter(Objects::nonNull).collect(toList());
        } else {
            deliveryFeeTemplateList = new ArrayList<>();
        }

        //做运费模板id映射
        HashMap<Long, DeliveryFeeTemplate> delieverTowerTemplete = new HashMap<>();
        for (DeliveryFeeTemplate deliveryFeeTemplate : deliveryFeeTemplateList) {
            delieverTowerTemplete.put(deliveryFeeTemplate.getId(), deliveryFeeTemplate);
        }

        //做物品id到运费模板的映射
        HashMap<Long, DeliveryFeeTemplate> itemTowerItemDeliveryFee = new HashMap<>();
        for (ItemDeliveryFee itemDeliveryFee : itemDeliveryFeeList) {
            itemTowerItemDeliveryFee.put(itemDeliveryFee.getItemId(), delieverTowerTemplete.get(itemDeliveryFee.getDeliveryFeeTemplateId()));
        }

        //做item到SkuCustom列表的映射
        HashMap<Long, SkuCustom> skuTowerSkuCustom = new HashMap<>();
        for (SkuCustom skuCustom : skuCustomsList) {
            skuTowerSkuCustom.put(skuCustom.getSkuId(), skuCustom);
        }

        //做item到sku列表映射 用于查找最高价和最低价
        HashMap<Long, List<Sku>> itemTowerSku = new HashMap<>();
        //循环中做物品到SkuCustom映射
        HashMap<Long, SkuCustom> itemTowerSkuCustom = new HashMap<>();
        for (Sku sku : skuList) {
            if (itemTowerSku.containsKey(sku.getItemId())) {
                itemTowerSku.get(sku.getItemId()).add(sku);
            } else {
                itemTowerSku.put(sku.getItemId(), Stream.of(sku).collect(Collectors.toList()));
            }

            //做item到SkuCustom的映射（只映射第一个)
            if (!itemTowerSkuCustom.containsKey(sku.getItemId())) {
                itemTowerSkuCustom.put(sku.getItemId(), skuTowerSkuCustom.get(sku.getId()));//可能为null
            }
        }

        //做等itemid与item本身的映射
        HashMap<Long, WeShopItem> itemIdTowerWeShopItem = new HashMap<>();
        for (WeShopItem weShopItem : weShopItemList) {
            itemIdTowerWeShopItem.put(weShopItem.getItemId(), weShopItem);
        }

        //临时变量
        SearchedItem searchedItem;
        SearchItemForWeDistributor searchItemForWeDistributor;
        WeShopItem weShopItem;

        //获取countryImage列表 准备用于SkuCustom映射
        List<CountryImage> countryImageList = new ArrayList<>();
        getCountryImageBySkuCustoms(skuCustomsList, countryImageList);
        //映射CountryImage到其CountryId
        HashMap<Long, CountryImage> itemTowerCountryImage = new HashMap<>();
        for (CountryImage countryImage : countryImageList)
            itemTowerCountryImage.put(countryImage.getCountryId(), countryImage);

        for (Item item : itemList) {
            weShopItem = itemIdTowerWeShopItem.getOrDefault(item.getId(), null);

            searchItemForWeDistributor = new SearchItemForWeDistributor();
            searchItemForWeDistributor.setWeShopItem(weShopItem);

            searchedItem = new SearchedItem();
            packItemIntoSearchItem(item, searchedItem, itemTowerSkuCustom, itemTowerCountryImage);
            //packItemIntoDistribution(item, searchedItem);
            searchItemForWeDistributor.setSearchedItem(searchedItem);

            SkuCustom skuCustom = itemTowerSkuCustom.getOrDefault(item.getId(), null);

            if (skuCustom != null) {//如果找不到SkuCustom接下来的信息 报错且设置不保税
                searchItemForWeDistributor.setTaxIsFree(skuCustom.getCustomTaxHolder() == 2);
                DeliveryFeeTemplate deliveryFeeTemplate = itemTowerItemDeliveryFee.getOrDefault(item.getId(), null);
                if (Objects.isNull(deliveryFeeTemplate)) {
                    searchItemForWeDistributor.setShipIsFree(false);
                    log.debug("skip delievryTemplate empty item (id:{})", item.getId());
                    continue;//跳过该物品
                } else {
                    boolean shipIsFree = deliveryFeeTemplate.getIsFree();
                    searchItemForWeDistributor.setShipIsFree(shipIsFree);
                }
            } else {
                log.warn("SkuCustom of ItemId found null {}", item.getId());
                searchItemForWeDistributor.setShipIsFree(false);
                searchItemForWeDistributor.setTaxIsFree(false);
                //continue;//跳过该物品 > 继续显示该物品
            }
            //开始设置商品最低价和最高价
            for (Sku sku : itemTowerSku.getOrDefault(item.getId(), new ArrayList<>())) {
                Optional<WeShopSku> weShopSku = weShopSkuCacheHolder.findByWeShopIdAndSkuId(weShopItem.getWeShopId(), sku.getId());
                if (!weShopSku.isPresent()) {
                    log.warn("{} unknown weShopSku for sku [{}]", LogUtil.getClassMethodName(), sku.getId());
                    continue;
                }
                Long weShopSkuPrice = weShopSku.map(WeShopSku::getPrice).orElseGet(() -> weShopSku.get().getDiffPrice() + sku.getPrice());
                Optional<Long> profit = weShopSku.map(WeShopSku::getProfit);
                if (!Optional.ofNullable(searchItemForWeDistributor.getMinDistributionPrice()).filter(lowPrice -> lowPrice < weShopSkuPrice).isPresent()) {
                    searchItemForWeDistributor.setMinDistributionPrice(weShopSkuPrice);
                }
                if (!Optional.ofNullable(searchItemForWeDistributor.getMaxDistributionPrice()).filter(highPrice -> highPrice > weShopSkuPrice).isPresent()) {
                    searchItemForWeDistributor.setMaxDistributionPrice(weShopSkuPrice);
                }
                if (profit.isPresent()) {
                    if (!Optional.ofNullable(searchItemForWeDistributor.getMinProfit()).filter(lowProfit -> lowProfit < profit.get()).isPresent()) {
                        searchItemForWeDistributor.setMinProfit(profit.get());
                    }
                    if (!Optional.ofNullable(searchItemForWeDistributor.getMaxProfit()).filter(highProfit -> highProfit > profit.get()).isPresent()) {
                        searchItemForWeDistributor.setMaxProfit(profit.get());
                    }
                }
            }
            //获取额外价格表

            searchItemForWeDistributorList.add(searchItemForWeDistributor);
        }
        return searchItemForWeDistributorList;
    }

    /**
     * 将Item打包入SearchedItem
     *
     * @param item         商品
     * @param searchedItem 搜索商品
     */
    private void packItemIntoSearchItem(Item item, SearchedItem searchedItem) {
        searchedItem.setId(item.getId());
        searchedItem.setName(item.getName());
        searchedItem.setMainImage(item.getMainImage_());
        searchedItem.setPrice(item.getLowPrice());
        searchedItem.setShopId(item.getShopId());
        searchedItem.setShopName(item.getShopName());
        searchedItem.setSaleQuantity(item.getSaleQuantity());
        searchedItem.setIsBonded(item.getIsBonded());
        //searchedItem.(item.getStatus());
        searchedItem.setPromotionTypes(Splitters.splitToInteger(item.getTags().get("promotionTypes"), Splitters.COMMA));
        if(BondedType.fromInt(item.getIsBonded()).isBonded()){ // 跨境商品，需要获取海关信息
            Sku sku = skuReadService.findSkusByItemId(item.getId()).getResult().get(0);//取单品获取税率与来源
            //SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
            SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
            if (skuCustom != null) {
                searchedItem.setOriginId(skuCustom.getCustomOriginId());
                searchedItem.setOrigin(skuCustom.getCustomOrigin());
                CountryImage countryImage = countryImageReadService.findByCountryId(skuCustom.getCustomOriginId()).getResult();
                if (countryImage != null) {
                    searchedItem.setOriginUrl(countryImage.getImageUrl());
                }
            }
        }
    }

    private void getCountryImageBySkuCustoms(List<SkuCustom> skuCustoms, List<CountryImage> countryImages) {
        if (skuCustoms.size() != 0) {
            countryImages.addAll(countryImageReadService.findByCountryIds(skuCustoms.stream().map(SkuCustom::getCustomOriginId).collect(toList())).getResult());
        }
    }

    private void packItemIntoSearchItem(Item item, SearchedItem searchedItem, HashMap<Long, SkuCustom> itemTowerSkuCustom, HashMap<Long, CountryImage> originIdTowerSkuCountryImage) {
        searchedItem.setId(item.getId());
        searchedItem.setName(item.getName());
        searchedItem.setMainImage(item.getMainImage_());
        searchedItem.setPrice(item.getLowPrice());
        searchedItem.setShopId(item.getShopId());
        searchedItem.setShopName(item.getShopName());
        searchedItem.setSaleQuantity(item.getSaleQuantity());
        searchedItem.setIsBonded(item.getIsBonded());
        searchedItem.setPromotionTypes(new ArrayList<>());
        if (item.getTags() != null) {
            if (item.getTags().containsKey("promotionTypes")) {
                if (StringUtils.hasText(item.getTags().get("promotionTypes"))) {
                    //if (item.getTags().get("promotionTypes").contains(Splitters.COMMA.toString()))
                    searchedItem.setPromotionTypes(Splitters.splitToInteger(item.getTags().get("promotionTypes"), Splitters.COMMA));
                }
            }
        }
        //使用传参进行关系数据
        if(BondedType.fromInt(item.getIsBonded()).isBonded()){
            SkuCustom skuCustom;
            skuCustom = itemTowerSkuCustom.getOrDefault(item.getId(), null);
            if (skuCustom != null) {
                searchedItem.setOriginId(skuCustom.getCustomOriginId());
                searchedItem.setOrigin(skuCustom.getCustomOrigin());
                //CountryImage countryImage = countryImageReadService.findByCountryId(skuCustom.getCustomOriginId());
                CountryImage countryImage = originIdTowerSkuCountryImage.getOrDefault(skuCustom.getCustomOriginId(), null);
                if (countryImage != null) {
                    searchedItem.setOriginUrl(countryImage.getImageUrl());
                }
            }
        }
    }

    /**
     * 根据用户id和shopid进行获取供应商的商品类别树
     *
     * @param userId 用户id
     * @param shopId 供应商id
     * @return 商品类目树列表
     */
    public List<ShopCategoryWithChildren> getListByShopId(long userId, long shopId) {
        List<ShopCategoryWithChildren> resultList = new ArrayList<>();

        List<Long> shopIds = new ArrayList<>();//包装shopId以便下面使用
        if (shopId == 0) {
            shopIds = weShopShopAccountReadService.findByUserId(userId).getResult().stream()
                    .map(WeShopShopAccount::getShopId).collect(toList());
        } else {
            shopIds.add(shopId);
        }
        if (shopIds.size() == 0) {
            return new ArrayList<>();
        }
        List<ShopCategory> shopCategories = shopCategoryReadService.findByShopIds(shopIds).getResult();//获取全部的Shop Category

        ShopCategoryWithChildren shopCategoryWithChildren;
        shopCategories.sort(Comparator.comparing(ShopCategory::getPid));

        HashMap<Long, ShopCategoryWithChildren> pidTowerShopCategoryWithChildren = new HashMap<>();
        HashMap<Long, List<ShopCategoryWithChildren>> pidTowerFakeList = new HashMap<>();//归属影子树的映射
        //双Map建树
        for (ShopCategory shopCategory : shopCategories) {
            shopCategoryWithChildren = new ShopCategoryWithChildren();
            setShop(shopCategoryWithChildren, shopCategory);
            //记录下Map方便下文映射
            pidTowerShopCategoryWithChildren.put(shopCategoryWithChildren.getId(), shopCategoryWithChildren);//节点记录用于映射
            if (pidTowerFakeList.containsKey(shopCategoryWithChildren.getId())) {//若其有归属影子树则将影子树取回
                shopCategoryWithChildren.setChildren(pidTowerFakeList.get(shopCategoryWithChildren.getId()));
                //pidTowerFakeList.remove(shopCategoryWithChildren.getId()); //暂时不需要及时清除
            }

            if (shopCategoryWithChildren.getPid() == 0) {//如果是底层的 也就是pid为0是根节点
                resultList.add(shopCategoryWithChildren);//插入根节点
            } else if (pidTowerShopCategoryWithChildren.containsKey(shopCategoryWithChildren.getPid())) {//非根节点的父亲节点是否已经出现 出现则插入归属
                pidTowerShopCategoryWithChildren.get(shopCategoryWithChildren.getPid()).getChildren().add(shopCategoryWithChildren);
            } else {//未出现则进入影子
                if (pidTowerFakeList.containsKey(shopCategoryWithChildren.getPid())) {//判断该归属影子树是否存在 存在则加入 不存在则新建归属影子树
                    pidTowerFakeList.get(shopCategoryWithChildren.getPid()).add(shopCategoryWithChildren);
                } else {
                    pidTowerFakeList.put(shopCategoryWithChildren.getPid(), Stream.of(shopCategoryWithChildren).collect(Collectors.toList()));
                }
            }

        }
        return resultList;
    }

    private void setShop(ShopCategoryWithChildren shopCategoryWithChildren, ShopCategory shopCategory) {
        shopCategoryWithChildren.setName(shopCategory.getName());
        shopCategoryWithChildren.setCreatedAt(shopCategory.getCreatedAt());
        shopCategoryWithChildren.setDisclosed(shopCategory.getDisclosed());
        shopCategoryWithChildren.setHasChildren(shopCategory.getHasChildren());
        shopCategoryWithChildren.setHasItem(shopCategory.getHasItem());
        shopCategoryWithChildren.setId(shopCategory.getId());
        shopCategoryWithChildren.setIndex(shopCategory.getIndex());
        shopCategoryWithChildren.setLevel(shopCategory.getLevel());
        shopCategoryWithChildren.setLogo(shopCategory.getLogo());
        shopCategoryWithChildren.setPid(shopCategory.getPid());
        shopCategoryWithChildren.setShopId(shopCategory.getShopId());
        shopCategoryWithChildren.setUpdatedAt(shopCategory.getUpdatedAt());
        shopCategoryWithChildren.setChildren(new ArrayList<>());
    }

    public Response<Boolean> setItemIndexDisplayInWeShop(CommonUser commonUser, Long weShopItemId, Boolean indexDisplay) {
        try {
            Response<WeShopItem> rWeShopItem = weShopItemReadService.findById(weShopItemId);
            if (!rWeShopItem.isSuccess()) {
                log.error("failed to find weShop item by id={}, error code: {}", weShopItemId, rWeShopItem.getError());
                throw new JsonResponseException(rWeShopItem.getError());
            }
            WeShopItem weShopItem = rWeShopItem.getResult();
            //检查权限
            if (!Objects.equals(weShopItem.getWeShopId(), commonUser.getWeShopId())) {
                log.error("weShop item(id={}) not belong to user(id={})", weShopItemId, commonUser.getId());
                throw new JsonResponseException("weShopItem.not.belong.to.user");
            }
            //检查微分销商品状态
            if (Objects.equals(weShopItem.getStatus(), WeShopItemStatus.PUT_OFF.getValue())) {
                log.error("weShop item(id={}) is put off, user(id={}) can not to set onDisplay", weShopItemId, commonUser.getId());
                throw new JsonResponseException("weShopItem.put.off.can.not.set.on.display");
            }
            //如果是设置首页显，检查上限，未达上限则设定排序
            Long indexDisplayNum = null;
            if (indexDisplay) {
                Response<Long> rIndexDisplayNum = weShopItemReadService.countIndexDisplayByWeShopId(commonUser.getWeShopId());
                if (!rIndexDisplayNum.isSuccess()) {
                    log.error("failed to count index display by weShopId={}, error code: {}",
                            commonUser.getWeShopId(), rIndexDisplayNum.getError());
                    throw new JsonResponseException(rIndexDisplayNum.getError());
                }
                indexDisplayNum = rIndexDisplayNum.getResult();
                if (indexDisplayNum >= distributionConfig.getIndexDisplayNum()) {
                    throw new JsonResponseException("weShopItem.index.display.num.restriction");
                }
            }

            int onDisplayValue = (indexDisplay ? WeShopItemIndexDisplayStatus.Show.getValue() : WeShopItemIndexDisplayStatus.Hide.getValue());
            if (indexDisplay && Objects.equals(weShopItem.getIndexDisplay(), onDisplayValue)) {
                return Response.ok(Boolean.TRUE);
            }
            WeShopItem toUpdate = new WeShopItem();
            toUpdate.setId(weShopItem.getId());
            toUpdate.setIndexDisplay(onDisplayValue);
            toUpdate.setSortIndex(indexDisplayNum == null ? distributionConfig.getIndexDisplayNum().intValue() : indexDisplayNum.intValue() + 1);
            Response<Boolean> response = weShopItemWriteService.update(toUpdate);
            if (!response.isSuccess()) {
                log.error("failed to update weShop item({}), error code: {}", toUpdate, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response;
        } catch (Exception e) {
            log.error("fail to set weShop item(id={}) onDisplay in weShop by indexDisplay={}, cause: {}",
                    weShopItemId, indexDisplay, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    public Response<Boolean> onSaleItemInWeShop(CommonUser commonUser, Long itemId) {
        try {
            Response<Item> rItem = itemReadService.findById(itemId);
            if (!rItem.isSuccess()) {
                log.error("failed to find item by id={}, error code: {}", itemId, rItem.getError());
                throw new JsonResponseException(rItem.getError());
            }
            Item item = rItem.getResult();
            Map<String, String> extra = item.getExtra();
            if (extra == null || !Objects.equals("true", extra.get(DistributionConstants.SELL_IN_WE_SHOP))) {
                throw new JsonResponseException("weShopItem.onSale.item.not.sell.in.weShop");
            }

            Response<WeShopShopAccount> rWeShopShopAccount =
                    weShopShopAccountReadService.findByWeShopIdAndShopId(commonUser.getWeShopId(), item.getShopId());
            if (!rWeShopShopAccount.isSuccess()) {
                log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}",
                        commonUser.getWeShopId(), item.getShopId(), rWeShopShopAccount.getError());
                throw new JsonResponseException(rWeShopShopAccount.getError());
            }
            if (rWeShopShopAccount.getResult() == null) {
                log.error("fail to auth user:{} to sale item:{}", commonUser.getId(), itemId);
                throw new JsonResponseException("weShopShopAccount.not.exist");
            }

            return Response.ok(doOnSale(commonUser.getWeShopId(), item));
        } catch (Exception e) {
            log.error("fail to on sale item in weShop by user(id={}), itemId={}, cause: {}", commonUser.getId(), itemId, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    public Response<Boolean> offSaleItemInWeShop(CommonUser commonUser, Long weShopItemId) {
        try {
            Response<WeShopItem> result = weShopItemReadService.findById(weShopItemId);
            if (!result.isSuccess()) {
                log.error("failed to find weShop item by id={}, error code: {}", weShopItemId, result.getError());
                throw new JsonResponseException(result.getError());
            }
            WeShopItem weShopItem = result.getResult();
            if (!Objects.equals(weShopItem.getWeShopId(), commonUser.getWeShopId())) {
                log.error("weShop item(id={}) not belong to user(id={})", weShopItemId, commonUser.getId());
                throw new JsonResponseException("weShopItem.not.belong.to.user");
            }
            WeShopItem toUpdate = new WeShopItem();
            toUpdate.setId(weShopItemId);
            toUpdate.setStatus(WeShopItemStatus.PUT_OFF.getValue());
            toUpdate.setIndexDisplay(WeShopItemIndexDisplayStatus.Hide.getValue());
            Response<Boolean> response = weShopItemWriteService.update(toUpdate);
            if (!response.isSuccess()) {
                log.error("failed to update weShop item({}), error code: {}", toUpdate, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response;
        } catch (Exception e) {
            log.error("fail to off sale item in weShop by user(id={}), weShopItemId={}, cause: {}",
                    commonUser.getId(), weShopItemId, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 上架商品
     * 上架后将会将显示状态置不显示。
     *
     * @param weShopId 微分销店铺id
     * @param item     商品
     * @return 是否修改成功
     */
    private boolean doOnSale(Long weShopId, Item item) {
        Integer saleStatus = WeShopItemStatus.NORMAL.getValue();
        Response<WeShopItem> result = weShopItemReadService.findByWeShopIdAndItemId(weShopId, item.getId());
        if (!result.isSuccess()) {
            log.error("failed to find weShop item by weShopId={}, weShopItemId={}, error code: {}", weShopId, item.getId(), result.getError());
            throw new JsonResponseException(result.getError());
        }
        WeShopItem weShopItem = result.getResult();
        if (weShopItem != null) {
            if (Objects.equals(weShopItem.getStatus(), saleStatus)) {
                return true;
            }
            WeShopItem toUpdate = new WeShopItem();
            toUpdate.setId(weShopItem.getId());
            toUpdate.setIndexDisplay(WeShopItemIndexDisplayStatus.Hide.getValue());
            toUpdate.setStatus(saleStatus);
            Response<Boolean> response = weShopItemWriteService.update(toUpdate);
            if (!response.isSuccess()) {
                log.error("failed to update weShop item({}), error code: {}", toUpdate, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } else {
            weShopItem = new WeShopItem();
            weShopItem.setItemId(item.getId());
            weShopItem.setShopId(item.getShopId());
            weShopItem.setIndexDisplay(WeShopItemIndexDisplayStatus.Hide.getValue());
            weShopItem.setStatus(saleStatus);
            weShopItem.setWeShopId(weShopId);
            Response<Long> response = weShopItemWriteService.create(weShopItem);
            if (!response.isSuccess()) {
                log.error("failed to create weShop item({}), error code: {}", weShopItem, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return true;
        }
    }

    /**
     * 由weShopId查找Item和打包它进入DTO
     * manad风格，自动打日志。求不删233
     *
     * @param weShopId       查找依据的Id
     * @param pagingCriteria 分页用参数
     * @return 打包的数据
     */
    private ResultResponse<List<SearchItemForWeDistributor>> monadPagingItemByShopIdAndMatch(Long weShopId, PagingCriteria pagingCriteria) {
        return
                ResultResponse.repack("fail to find the WeShopItem that WeShopId in :" + weShopId.toString(),
                        weShopItemReadService.pagingOnSellItemByWeShopIds(Stream.of(weShopId).collect(toList()), pagingCriteria))
                        .then("fail to unpack ItemId", weShopItems -> weShopItems.stream().map(WeShopItem::getItemId).filter(Objects::nonNull).collect(toList())
                        )
                        .thenDepack("fail to get Item List", itemReadService::findByIds)
                        .then("fail to pack Item into SearchItem", itemList -> packItemIntoDistribution(itemList, Stream.of(weShopId).collect(toList())))
                ;
    }

    private Response<List<SearchItemForWeDistributor>> pagingItemByShopIdAndMatch(Long weShopId, PagingCriteria pagingCriteria) {

        Response<List<WeShopItem>> response = weShopItemReadService.pagingOnSellItemByWeShopIds(Stream.of(weShopId).collect(toList()), pagingCriteria);
        if (response.isSuccess()) {
            List<WeShopItem> weShopItems = response.getResult();
            List<Long> ids = weShopItems.stream().map(WeShopItem::getItemId).filter(Objects::nonNull).collect(toList());
            Response<List<Item>> itemResponse = itemReadService.findByIds(ids);
            if (itemResponse.isSuccess()) {
                List<Item> itemList = itemResponse.getResult();
                List<SearchItemForWeDistributor> searchItemForWeDistributorList = packItemIntoDistribution(itemList, Stream.of(weShopId).collect(toList()));
                return Response.ok(searchItemForWeDistributorList);
            } else {
                log.error("fail to find Item by ItemId:{}", ids);
                return Response.fail("fail to find Items");
            }

        }
        log.error("fail to find WeShopItem by weShopId:{}", weShopId);
        return Response.fail("fail to find Items");
    }

    /**
     * 由weShopid打包好的List包装的SearchItemForWeDitributor封装入Paging
     *
     * @param weShopId       由UserId获取的WeShopId
     * @param pagingCriteria 分页参数
     * @return 业务前端数据
     */
    public Response<Paging<SearchItemForWeDistributor>> monadGetPagingOfOnSaleWeShopItem(Long weShopId, PagingCriteria pagingCriteria) {
        return ResultResponse.repack("fail to get total of OnSaleWeShopItem", weShopItemReadService.countOnSellItemByWeShopId(weShopId))
                .thenDepack("fail to pack SearchItemForWeDistributor into Paging", count ->
                        monadPagingItemByShopIdAndMatch(weShopId, pagingCriteria)
                                .then("fail to pack the SearchItemForWeDistributor into Paging", searchItemForWeDistributors -> {
                                            Paging<SearchItemForWeDistributor> searchItemForWeDistributorPaging = new Paging<>();
                                            searchItemForWeDistributorPaging.setTotal(count);
                                            searchItemForWeDistributorPaging.setData(searchItemForWeDistributors);
                                            return searchItemForWeDistributorPaging;
                                        }
                                )
                )
                .solve("end the error Chain", (errorMsg, impossibleItem) ->
                {
                    log.error(errorMsg);
                    return null;
                });
    }

    public Response<Paging<SearchItemForWeDistributor>> getPagingOfOnSaleWeShopItem(Long weShopId, PagingCriteria pagingCriteria) {
        Response<Long> countResult = weShopItemReadService.countOnSellItemByWeShopId(weShopId);
        if (countResult.isSuccess()) {
            long count = countResult.getResult();
            Response<List<SearchItemForWeDistributor>> pagingResponse = pagingItemByShopIdAndMatch(weShopId, pagingCriteria);
            if (pagingResponse.isSuccess()) {
                List<SearchItemForWeDistributor> searchItemForWeDistributors = pagingResponse.getResult();
                Paging<SearchItemForWeDistributor> searchItemForWeDistributorPaging = new Paging<>();
                searchItemForWeDistributorPaging.setTotal(count);
                searchItemForWeDistributorPaging.setData(searchItemForWeDistributors);
                return Response.ok(searchItemForWeDistributorPaging);
            } else {
                log.error("fail to find List<SearchItemForWeDistributor>");
            }
        } else {
            log.error("fail to count total");
        }
        return Response.fail("fail to find Item");
    }

    public Response<Paging<SearchItemForWeDistributor>> getPagingOfOnSaleWeShopItemByUserId(long userId, PagingCriteria pagingCriteria) {
        long weShopId;
        // 这边就不进入函数式编程啦
        Response<List<WeShopShopAccount>> listResponse = weShopShopAccountReadService.findByUserId(userId);
        if (listResponse.isSuccess()) {
            weShopId = listResponse.getResult().stream().map(WeShopShopAccount::getWeShopId).filter(Objects::nonNull).iterator().next();
            return getPagingOfOnSaleWeShopItem(weShopId, pagingCriteria);
        }
        return Response.fail("fail to find WeShop");
    }

    public Response<List<RichWeShopItem>> listIndexDisplay(Long weShopId, Long shopId) {
        try {
            Map<String, Object> criteria = new HashMap<>();
            criteria.put("weShopId", weShopId);
            criteria.put("indexDisplay", WeShopItemIndexDisplayStatus.Show.getValue());
            criteria.put("sortBy", "sortIndex");
            criteria.put("sortType", 1);
            criteria.put("shopId", shopId);
            Response<List<WeShopItem>> rWeShopItems = weShopItemReadService.list(criteria);
            if (!rWeShopItems.isSuccess()) {
                log.error("failed to list weShop items by weShopId={}, indexDisplay={}, sortBy={}, sortType={}, error code: {}",
                        weShopId, WeShopItemIndexDisplayStatus.Show.getValue(), "sortIndex", 1, rWeShopItems.getError());
                throw new JsonResponseException(rWeShopItems.getError());
            }
            List<WeShopItem> weShopItems = rWeShopItems.getResult();
            List<Long> ids = weShopItems.stream().map(WeShopItem::getItemId).filter(Objects::nonNull).collect(toList());
            Response<List<Item>> itemResponse = itemReadService.findByIds(ids);
            if (!itemResponse.isSuccess()) {
                log.error("fail to find Items by ids:{}, error code: {}", ids, itemResponse.getError());
                return Response.fail(itemResponse.getError());
            }
            List<Item> itemList = itemResponse.getResult();
            Map<Long, Item> itemMapById = Maps.uniqueIndex(itemList, Item::getId);
            List<RichWeShopItem> richWeShopItems = new ArrayList<>();
            for (WeShopItem weShopItem : weShopItems) {
                RichWeShopItem richWeShopItem = new RichWeShopItem();
                richWeShopItem.setWeShopItem(weShopItem);
                richWeShopItem.setItem(itemMapById.get(weShopItem.getItemId()));
                richWeShopItems.add(richWeShopItem);
            }
            return Response.ok(richWeShopItems);
        } catch (Exception e) {
            log.error("fail to list index display weShopItems by weShopId={}, cause: {}", weShopId, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }
}
