package moonstone.web.distribution.component.user;

import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.enums.UserRole;
import moonstone.common.enums.UserStatus;
import moonstone.common.model.CommonUser;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;
import moonstone.user.service.AdminUserService;
import moonstone.user.service.UserWriteService;
import moonstone.user.service.UserWxWriteService;
import moonstone.weShop.enums.WeShopStatus;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/12.
 */
@Slf4j
@Component
public class DistributionUserWriteLogic {
    @RpcConsumer
    private WeShopReadService weShopReadService;

    @RpcConsumer
    private WeShopWriteService weShopWriteService;

    @RpcConsumer
    private AdminUserService adminUserService;

    @RpcConsumer
    private UserWriteService<User> userWriteService;

    @RpcConsumer
    private UserWxWriteService userWxWriteService;

    @Autowired
    private UserTypeBean userTypeBean;

    @Transactional
    public void becomeDistributor(CommonUser commonUser){
        //如果还没有微分销店铺，则创建微分销店铺
        Response<WeShop> rWeShop = weShopReadService.findByUserId(commonUser.getId());
        if (!rWeShop.isSuccess()){
            log.error("failed to find weShop by userId={}, error code: {}", commonUser.getId(), rWeShop.getError());
            throw new JsonResponseException(rWeShop.getError());
        }
        WeShop weShop = rWeShop.getResult();
        if (ObjectUtils.isEmpty(weShop)){
            Response<Long> maxIdResponse = weShopReadService.maxId();
            if (!maxIdResponse.isSuccess()) {
                log.error("failed to get max weShop Id, error code: {}", maxIdResponse.getError());
                throw new JsonResponseException(maxIdResponse.getError());
            }
            weShop = new WeShop();
            weShop.setName("微分销店铺" + (maxIdResponse.getResult() + 1));
            weShop.setUserId(commonUser.getId());
            weShop.setStatus(WeShopStatus.NOT_ACTIVE.getValue());
            Response<Long> response = weShopWriteService.create(weShop);
            if (!response.isSuccess()){
                log.error("failed to create weShop({}), error code: {}", weShop, response.getError());
                throw new JsonResponseException(response.getError());
            }
        }
        //增加微分销角色
        List<String> roles = commonUser.getRoles();
        roles.add(UserRole.WE_DISTRIBUTOR.name());
        //TODO 下面直接用adminUserService改了角色，但实际上数据库中改角色应该调用/api/user/{userId}/roles或者/api/user/{userId}/role-names接口来进行动态验证有的角色
        Response<Boolean> response = adminUserService.updateRoles(commonUser.getId(), roles);
        if (!response.isSuccess() || !response.getResult()){
            log.error("failed to update user roles by id={}, roles={}, error code: {}", commonUser.getId(), roles, response.getError());
            throw new JsonResponseException(response.getError());
        }
        commonUser.setWeShopId(weShop.getId());
        commonUser.setRoles(roles);
    }

    @Transactional
    public Long wxUserBindUser(UserWx existUserWx, User wxUser, User existUser, String mobile){
        Long resultUserId;
        if (existUser == null) {
            User user = new User();
            user.setId(existUserWx.getUserId());
            user.setMobile(mobile);
            Response<Boolean> rUpdateUser = userWriteService.update(user);
            if (!rUpdateUser.isSuccess() || !rUpdateUser.getResult()) {
                log.error("[op:newUserBind] failed to update user mobile by id={}, mobile={}, error code: {}", existUserWx.getUserId(), mobile, rUpdateUser.getError());
                throw new JsonResponseException("user.update.fail");
            }
            resultUserId = user.getId();
        } else if (Objects.equal(existUser.getId(), existUserWx.getUserId())) {
            resultUserId = existUser.getId();
        } else {
            //微信用户改绑手机用户
            UserWx userWx = new UserWx();
            userWx.setId(existUserWx.getId());
            userWx.setUserId(existUser.getId());
            Response<Boolean> rUpdateUserWx = userWxWriteService.update(userWx);
            if (!rUpdateUserWx.isSuccess() || !rUpdateUserWx.getResult()) {
                log.error("[op:newUserBind] failed to update userWx by id={}, userId={}, error code: {}", existUserWx.getId(), existUser.getId(), rUpdateUserWx.getError());
                throw new JsonResponseException(rUpdateUserWx.getError());
            }
            //处理微分销店铺相关
            Response<WeShop> weShopResponse = weShopReadService.findByUserId(existUserWx.getUserId());
            if (!weShopResponse.isSuccess()) {
                log.error("failed to find weShop by userId={}, error code: {}", existUserWx.getUserId(), weShopResponse.getError());
                throw new JsonResponseException(weShopResponse.getError());
            }
            WeShop weShop = weShopResponse.getResult();
            if (!userTypeBean.isWeDistributor(existUser)) {
                //手机用户增加微分销角色
                List<String> roles = existUser.getRoles();
                roles.add(UserRole.WE_DISTRIBUTOR.name());
                Response<Boolean> response = adminUserService.updateRoles(existUser.getId(), roles);
                if (!response.isSuccess() || !response.getResult()) {
                    log.error("failed to update user roles by id={}, roles={}, error code: {}", existUser.getId(), roles, response.getError());
                    throw new JsonResponseException(response.getError());
                }
                //原用户的微分销店铺改为当前用户
                WeShop toUpdateWeShop = new WeShop();
                toUpdateWeShop.setId(weShop.getId());
                toUpdateWeShop.setUserId(existUser.getId());
                weShopWriteService.update(toUpdateWeShop);
            } else {
                //关闭旧的微分销店铺
                Response<Boolean> response = weShopWriteService.updateStatus(weShop.getId(), WeShopStatus.CLOSED.getValue());
                if (!response.isSuccess()) {
                    log.error("failed to close weShop(id={}), error code: {}", weShop.getId(), response.getError());
                    //这里真的没关掉也没关系，就不抛异常了
                }
            }
            //删除微信用户绑定的用户
            User user = new User();
            user.setId(existUserWx.getUserId());
            user.setStatus(UserStatus.DELETED.value());
            Response<Boolean> rUpdateUser = userWriteService.update(user);
            if (!rUpdateUser.isSuccess() || !rUpdateUser.getResult()) {
                log.error("[op:newUserBind] failed to delete user by id={}, error code: {}", existUserWx.getUserId(), rUpdateUser.getError());
            }
            resultUserId = userWx.getUserId();
        }
        Map<String, String> tags = wxUser.getTags();
        if (tags == null) {
            tags = new HashMap<>();
        }
        tags.put(ParanaConstants.USER_IS_WX_USER, "true");
        tags.put(ParanaConstants.USER_IS_NEW_WX_USER, "false");
        Response<Boolean> response = adminUserService.updateTags(resultUserId, tags);
        if (!response.isSuccess() || !response.getResult()) {
            log.error("failed to update user(id={}) tags to ({}), error code: {}", resultUserId, tags, response.getError());
            throw new JsonResponseException(response.getError());
        }
        return resultUserId;
    }
}
