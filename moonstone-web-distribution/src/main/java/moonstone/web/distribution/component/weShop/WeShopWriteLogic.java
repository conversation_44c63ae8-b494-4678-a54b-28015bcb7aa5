package moonstone.web.distribution.component.weShop;

import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.UserRole;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.user.enums.AreaType;
import moonstone.user.model.User;
import moonstone.user.service.AreaInfoWriteService;
import moonstone.user.service.UserWriteService;
import moonstone.weShop.dto.WeShopApply;
import moonstone.weShop.enums.WeShopStatus;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.weShop.service.WeShopWriteService;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.events.shop.WeShopUpdateEvent;
import moonstone.web.distribution.util.WeShopWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class WeShopWriteLogic {
    private final WeShopShopAccountReadService weShopShopAccountReadService;
    private final WeShopReadService weShopReadService;
    private final WeShopWriteService weShopWriteService;
    private final AreaInfoWriteService areaInfoWriteService;
    private final UserWriteService<User> userWriteService;

    /**
     * 注册或者更新原有未验证的微店
     *
     * @param weShopApply 申请实体
     * @param applier     申请人
     * @return 结果
     */
    public Either<Boolean> signInOrUpdate(WeShopApply weShopApply, BaseUser applier) {
        List<WeShopShopAccount> weShopShopAccounts = weShopShopAccountReadService.findByUserId(applier.getId()).getResult();
        if (!weShopShopAccounts.isEmpty()) {
            Optional<WeShop> weShopOpt = weShopShopAccounts.stream()
                    .filter(account -> Objects.equals(account.getShopId(), Optional.ofNullable(weShopApply.getShopId()).orElse(0L)))
                    .map(WeShopShopAccount::getWeShopId).map(weShopReadService::findById).map(Response::getResult).findFirst();
            if (weShopOpt.isPresent()) {
                Long updateId = updateWeShop(weShopApply, weShopOpt.get()).elseThrow();
                EventSender.publish(new WeShopUpdateEvent(updateId));
                return Either.ok(true);
            }
        }
        // register the weShop
        WeShop weShop = new WeShop();
        BeanUtils.copyProperties(weShopApply, weShop);
        Optional.ofNullable(weShopApply.getOutShopId()).map(Objects::toString).ifPresent(weShop::setOutShopCode);
        weShop.setUserId(applier.getId());
        Response<Long> regResult = weShopWriteService.register(weShop, weShopApply.getShopId());
        EventSender.publish(new WeShopUpdateEvent(weShop.getId()));
        if (regResult.isSuccess()) {
            areaInfoWriteService.saveFromBean(regResult.getResult(), AreaType.WeShop.getValue(), weShopApply);
            userWriteService.addRole(applier.getId(), UserRole.WE_DISTRIBUTOR);
            EventSender.publish(new UserUpdateEvent(applier.getId()));
            return Either.ok(true);
        }
        return Either.error(Translate.of("注册失败"));
    }

    /**
     * 更新已有的未审核的商店
     *
     * @param weShopApply 商店申请实体
     * @param weShop      原有微店
     * @return 更新结果
     */
    private Either<Long> updateWeShop(WeShopApply weShopApply, WeShop weShop) {
        Optional.ofNullable(weShopApply.getOutShopId()).map(Objects::toString).ifPresent(weShop::setOutShopCode);
        if (weShop.getStatus() == WeShopStatus.NORMAL.getValue()) {
            return Either.error(Translate.of("分销店铺已经存在"));
        }
        if (weShop.getStatus() == WeShopStatus.FROZEN.getValue()) {
            return Either.error(Translate.of("分销店铺已经被冻结, 无法重新申请"));
        }
        WeShop update = WeShopWrapper.wrapUpdate(weShopApply);
        update.setId(weShop.getId());
        log.debug("{} update [{}]", LogUtil.getClassMethodName(), Json.toJson(update));
        areaInfoWriteService.saveFromBean(weShop.getId(), AreaType.WeShop.getValue(), weShopApply);
        return weShopWriteService.update(update).isSuccess() ? Either.ok(weShop.getId())
                : Either.error(Translate.of("更新失败"));
    }
}
