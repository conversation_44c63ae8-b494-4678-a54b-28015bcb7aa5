package moonstone.web.distribution.component.weShop;

import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.InComeDetail;
import moonstone.order.model.BalanceDetail;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.WeShopMember;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.weDistributionApplication.enums.RecommendType;
import moonstone.weShop.dto.WeShopInvitationRecord;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.web.core.component.RecordManager;
import moonstone.web.core.component.profit.WeShopWithdrawComponent;
import moonstone.web.core.model.Record;
import moonstone.web.core.model.dto.WeShopIndexStatisticalData;
import moonstone.web.core.model.dto.WeShopMemberView;
import moonstone.web.core.model.dto.record.ForeseeProfitSumToday;
import moonstone.web.core.model.dto.record.TradeAmountToday;
import moonstone.web.core.model.dto.record.TradeTimeToday;
import moonstone.web.core.model.dto.record.UserRegisterCount;
import moonstone.web.core.model.enu.RecordDateLimitType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;

/**
 * Author:  CaiZhy
 * Date:    2018/12/29
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeShopReadLogic {
    @Autowired
    private WeShopReadService weShopReadService;
    @Autowired
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private BalanceDetailManager balanceDetailManager;
    @Autowired
    private RecordManager recordManager;
    @Autowired
    private WeShopWithdrawComponent weShopWithdrawComponent;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private UserRelationEntityReadService userRelationEntityReadService;
    @Autowired
    private UserProfileReadService userProfileReadService;

    public Response<WeShopIndexStatisticalData> countIndexData(CommonUser commonUser, Long shopId) {
        try {
            WeShopIndexStatisticalData weShopIndexStatisticalData = new WeShopIndexStatisticalData();
            //查找店铺信息，并填充
            if (commonUser.getWeShopId() == null) {
                return Response.fail(Translate.of("店铺不存在"));
            }
            Response<WeShop> weShopResponse = weShopReadService.findById(commonUser.getWeShopId());
            if (!weShopResponse.isSuccess()) {
                log.error("failed to find weShop by id={}, error code: {}", commonUser.getWeShopId(), weShopResponse.getError());
                throw new JsonResponseException(Translate.of("查找店铺数据失败"));
            }
            WeShop weShop = weShopResponse.getResult();
            weShopIndexStatisticalData.setWeShop(weShop);
            //统计订单数量
            recordManager.findRecord(weShop.getUserId(), TradeTimeToday.build(weShop.getUserId(), OrderOutFrom.WE_SHOP, weShop.getId()), RecordDateLimitType.day)
                    .stream().findFirst().map(Record::getNum).ifPresent(weShopIndexStatisticalData::setTodayOrderNum);
            //统计今日待收益
            recordManager.findRecord(weShop.getUserId(), ForeseeProfitSumToday.build(weShop.getUserId(), OrderOutFrom.WE_SHOP, weShop.getId()), RecordDateLimitType.day)
                    .stream().findFirst().map(Record::getNum).ifPresent(weShopIndexStatisticalData::setTodayForeseeProfit);
            recordManager.findRecord(weShop.getUserId(), TradeAmountToday.build(weShop.getUserId(), OrderOutFrom.WE_SHOP, weShop.getId()), RecordDateLimitType.day)
                    .stream().findFirst().map(Record::getNum).ifPresent(weShopIndexStatisticalData::setTodayTradeAmount);
            //统计累计待收益
            for (InComeDetail inComeDetail : Optional.ofNullable(balanceDetailManager.getCash(commonUser.getId(), shopId).getResult()).orElseGet(ArrayList::new)) {
                if (inComeDetail.isPresent()) {
                    weShopIndexStatisticalData.setBalance(inComeDetail.getFee());
                } else {
                    weShopIndexStatisticalData.setTotalForeseeProfit(inComeDetail.getFee());
                }
            }
            balanceDetailManager.getEarned(weShop.getUserId(), shopId).map(BalanceDetail::getFee).ifSuccess(weShopIndexStatisticalData::setTotalPresentProfit);
            //填充其它账户相关数据
            balanceDetailManager.getEarned(weShop.getUserId(), shopId).map(BalanceDetail::getFee).map(earned -> earned - Optional.ofNullable(weShopIndexStatisticalData.getBalance()).orElse(0L)).ifSuccess(weShopIndexStatisticalData::setTotalWithdrawSum);//已经赚取的

            try {
                weShopIndexStatisticalData.setWithdrawLimitView(weShopWithdrawComponent.constructTimeLimitForProfitDTO(commonUser.getId(), shopId));
            } catch (Exception ex) {
                log.error("{} fail to set the withdrawLimitView", LogUtil.getClassMethodName(), ex);
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("设置提现限制失败", String.format("供销平台 用户[%s] 设置提现上限失败", commonUser.getId()), ex, EmailReceiverGroup.DEVELOPER));
            }

            return Response.ok(weShopIndexStatisticalData);
        } catch (Exception e) {
            log.error("fail to count index data by paranaUser({})", commonUser, e);
            return Response.fail(e.getMessage());
        }
    }

    public Response<Paging<WeShopInvitationRecord>> pagingRecord(Long currentWeShopId, Long currentUserId, Long shopId, Integer pageNo, Integer pageSize) {
        try {
            Response<Paging<WeShopShopAccount>> rWeShopShopAccountPaging = weShopShopAccountReadService.paging(
                    null, null, null, null, shopId, null, null,
                    RecommendType.WESHOP.getValue(), currentWeShopId, currentUserId, null, null, pageNo, pageSize);
            if (!rWeShopShopAccountPaging.isSuccess()) {
                log.error("failed to paging weShopShopAccounts by shopId={}, recommendType={}, recommendShopId={}, recommendUserId={}, " +
                                "pageNo={}, pageSize={}, error code: {}", shopId, RecommendType.WESHOP.getValue(), currentWeShopId, currentUserId,
                        pageNo, pageSize, rWeShopShopAccountPaging.getError());
                throw new JsonResponseException(rWeShopShopAccountPaging.getError());
            }
            Paging<WeShopShopAccount> weShopShopAccountPaging = rWeShopShopAccountPaging.getResult();
            List<WeShopInvitationRecord> data = new ArrayList<>();
            for (WeShopShopAccount weShopShopAccount : weShopShopAccountPaging.getData()) {
                WeShopInvitationRecord weShopInvitationRecord = new WeShopInvitationRecord();
                weShopInvitationRecord.setCreatedAt(weShopShopAccount.getCreatedAt());
                Response<WeShop> weShopResponse = weShopReadService.findById(weShopShopAccount.getWeShopId());
                if (!weShopResponse.isSuccess()) {
                    log.error("failed to find weShop by id={}, error code: {}", weShopShopAccount.getWeShopId(), weShopResponse.getError());
                    throw new JsonResponseException(weShopResponse.getError());
                }
                weShopInvitationRecord.setWeShopName(weShopResponse.getResult().getName());
                Response<User> userResponse = userReadService.findById(weShopShopAccount.getUserId());
                if (!userResponse.isSuccess()) {
                    log.error("failed to find user by id={}, error code: {}", weShopShopAccount.getUserId(), userResponse.getError());
                    throw new JsonResponseException(userResponse.getError());
                }
                weShopInvitationRecord.setMobile(userResponse.getResult().getMobile());
                data.add(weShopInvitationRecord);
            }
            return Response.ok(new Paging<>(weShopShopAccountPaging.getTotal(), data));
        } catch (Exception e) {
            log.error("fail to paging invitation records by currentWeShopId={}, currentUserId={}, shopId={}, pageNo={}, pageSize={}, cause: {}",
                    currentWeShopId, currentUserId, shopId, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }

    public WeShopMemberView getMember(Long weShopId) {
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseGet(() -> weShopReadService.findById(weShopId).getResult());
        Long yesterdayCount = recordManager.findRecord(weShop.getUserId(), UserRegisterCount.build(weShopId, OrderOutFrom.WE_SHOP), LocalDate.now().toEpochDay() - 1).stream().findFirst().map(Record::getNum).orElse(0L);
        List<WeShopMember> weShopMembers = new LinkedList<>();
        for (UserRelationEntity userRelationEntity : userRelationEntityReadService.listByRelationIdAndType(weShopId, UserRelationEntity.UserRelationType.WeShopMember).getResult()) {
            if (Objects.equals(weShop.getUserId(), userRelationEntity.getUserId())) {
                continue;
            }
            UserProfile userProfile = userProfileReadService.findProfileByUserId(userRelationEntity.getUserId()).getResult();
            User user = userReadService.findById(userRelationEntity.getUserId()).getResult();
            weShopMembers.add(WeShopMember.from(user, userProfile, userRelationEntity.getCreatedAt()));
        }
        return new WeShopMemberView(weShopMembers, yesterdayCount);
    }
}
