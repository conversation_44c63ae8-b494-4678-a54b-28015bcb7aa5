package moonstone.web.distribution.distribution;

import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.constants.ParanaConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author:  <PERSON><PERSON><PERSON><PERSON>
 * Date:    2019/1/11
 */
@Slf4j
@RestController
@RequestMapping("/api/distribution")
public class Distributions {
    @Autowired
    private ParanaConfig paranaConfig;

    @RequestMapping(value = "/share/url", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String shareWxaUrl(){
        String url = paranaConfig.getParanaWeSellerUrl();
        return url + "/";
    }
}
