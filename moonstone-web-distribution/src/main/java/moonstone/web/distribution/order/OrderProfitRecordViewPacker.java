package moonstone.web.distribution.order;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.utils.LogUtil;
import moonstone.item.model.Sku;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileReadService;
import moonstone.web.core.component.item.model.view.UnSettleOrderProfitRecordView;
import moonstone.web.core.component.profit.dto.OrderProfitRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class OrderProfitRecordViewPacker {
    @Autowired
    private UserProfileReadService userProfileReadService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private SkuCacheHolder skuCacheHolder;

    /**
     * 将待收益订单利润数据打包进入View层
     *
     * @param orderProfitRecordList 订单收益数据
     * @return 待收益订单数据
     */
    public List<UnSettleOrderProfitRecordView> packView(List<OrderProfitRecord> orderProfitRecordList) {
        List<UnSettleOrderProfitRecordView> viewList = new ArrayList<>();
        for (OrderProfitRecord record : orderProfitRecordList) {
            try {
                UnSettleOrderProfitRecordView view = new UnSettleOrderProfitRecordView();
                viewList.add(view);
                ShopOrder shopOrder = shopOrderReadService.findById(record.getOrderId()).getResult();
                BeanUtils.copyProperties(record, view);
                Optional.ofNullable(userProfileReadService.findProfileByUserId(record.getBuyerId()))
                        .map(Response::getResult)
                        .map(UserProfile::getAvatar)
                        .ifPresent(view::setBuyerAvatar);
                // set order List
                view.setSkuOrderList(new ArrayList<>());
                for (SkuOrder skuOrder : skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult()) {
                    UnSettleOrderProfitRecordView.UnSettleSkuOrderView skuOrderView = new UnSettleOrderProfitRecordView.UnSettleSkuOrderView();
                    view.getSkuOrderList().add(skuOrderView);
                    skuOrderView.setOriginPrice(skuOrder.getOriginFee());
                    skuOrderView.setQuantity(skuOrder.getQuantity());
                    skuOrderView.setItemName(skuOrder.getItemName());
                    skuOrderView.setImage(skuOrder.getSkuImage());
                    Optional.ofNullable(skuCacheHolder.findSkuById(skuOrder.getSkuId()))
                            .map(Sku::getSpecification).ifPresent(skuOrderView::setSpecification);
                }
                view.setStatus(OrderStatus.fromInt(shopOrder.getStatus()).intoString());
                view.setQuantity(view.getSkuOrderList().stream().map(UnSettleOrderProfitRecordView.UnSettleSkuOrderView::getQuantity).reduce(Integer::sum).orElse(0));
            } catch (Exception ex) {
                log.error("{} fail to pack view from order[{}] record[{}] profit[{}] status[{}]", LogUtil.getClassMethodName(), record.getOrderId(), record.get_id(), record.getProfit(), record.getProfitStatus(), ex);
            }
        }
        return viewList;
    }
}
