package moonstone.web.distribution.order;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.Json;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.PreviewRefund;
import moonstone.order.dto.RefundDetail;
import moonstone.order.enu.RefundReasonType;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.OrderRelation;
import moonstone.order.model.Refund;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.web.core.order.RefundReadLogic;
import moonstone.web.core.refund.application.RefundForBuyerApplication;
import moonstone.web.core.refund.application.RefundPreviewApplication;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Author:  CaiZhy
 * Date:    2018/12/25
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/weDistributor")
@AllArgsConstructor
public class DistributionRefunds {

    private final RefundReadLogic refundReadLogic;

    private final RefundPreviewApplication refundPreviewApplication;
    private final RefundForBuyerApplication refundForBuyerApplication;
    private final RefundReadService refundReadService;
    private final RefundWriteService refundWriteService;

    /**
     * 退货款预览
     *
     * @param orderIds  订单id列表
     * @param orderType 订单级别
     */
    @RequestMapping(value = "/refund/preview", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PreviewRefund preview(@RequestParam("orderIds") List<Long> orderIds,
                                 @RequestParam("orderType") Integer orderType) {
        try {
            return refundPreviewApplication.previewRefund(orderIds, orderType);
        } catch (Exception e) {
            log.error("fail to preview refund by orderIds={}, orderType={}, cause:",
                    orderIds, orderType, e);
            throw new JsonResponseException("order.preview.refund.fail");
        }
    }

    /**
     * 微分销店主获取退货款单详情
     *
     * @param refundId 退货款单id
     * @return 退货款单详情
     */
    @RequestMapping(value = "/refund/{refundId}/detail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RefundDetail findDetailForDistributor(@PathVariable("refundId") Long refundId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<RefundDetail> detailRes = refundReadLogic.findDetailForDistributor(refundId, commonUser);
        if (!detailRes.isSuccess()) {
            log.error("fail to find refund detail by id={},error:{}",
                    refundId, detailRes.getError());
            throw new JsonResponseException(detailRes.getError());
        }
        return detailRes.getResult();
    }

    /**
     * 微分销店主根据订单号获取退货款单详情
     *
     * @param orderId   订单号
     * @param orderType 订单级别
     * @return 退货款单详情
     */
    @RequestMapping(value = "/refund/detailByOrder", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<RefundDetail> findDetailByOrderForDistributor(@RequestParam Long orderId,
                                                              @RequestParam(defaultValue = "2") Integer orderType) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<List<RefundDetail>> detailsRes = refundReadLogic.findDetailByOrderForDistributor(orderId, orderType, commonUser);
        if (!detailsRes.isSuccess()) {
            log.error("fail to find refund details by orderId={}, orderType={},error:{}",
                    orderId, orderType, detailsRes.getError());
            throw new JsonResponseException(detailsRes.getError());
        }
        return detailsRes.getResult();
    }


    /**
     * 当前不能跨订单申请退款, 但是支持同一订单的多个子订单申请退款  TODO 仅退款
     *
     * @param buyerNote 买家备注
     * @param orderIds  (子)订单列表
     * @param orderType 订单级别
     * @return 创建的退款单id
     */
    @RequestMapping(value = "/order/refund", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Long applyRefund(@RequestParam(value = "buyerNote", required = false) String buyerNote,
                            @RequestParam("orderIds") List<Long> orderIds,
                            @RequestParam(value = "orderType", defaultValue = "2") Integer orderType,
                            @RequestParam("imagesJson") String imagesJson,
                            @RequestParam("reasonType") Integer reasonType) {
        if (CollectionUtils.isEmpty(orderIds)) {
            log.error("no orderIds specified when apply refund");
            throw new JsonResponseException("order.refund.fail");
        }
        try {
            CommonUser distributor = UserUtil.getCurrentUser();
            List<OrderRelation> orderRelations = new ArrayList<>(orderIds.size());
            for (Long orderId : orderIds) {
                OrderRefund orderRefund = new OrderRefund();
                orderRefund.setOrderId(orderId);
                orderRefund.setOrderLevel(OrderLevel.fromInt(orderType));
                orderRelations.add(orderRefund);
            }
            return refundForBuyerApplication.apply(distributor, orderRelations, buyerNote, Json.parseObject(imagesJson, new TypeReference<List<String>>() {
            }), Refund.RefundType.ON_SALE_REFUND, RefundReasonType.from(reasonType), null).take();
        } catch (Exception e) {
            log.error("failed to  apply refund for order(ids={}, level={}), cause:",
                    orderIds, orderType, e);
            throw new JsonResponseException(e.getMessage());
        }
    }

    /**
     * 取消退款 TODO 仅退款
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/refund/{refundId}/cancel", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void cancelRefund(@PathVariable("refundId") Long refundId) {
        try {
            CommonUser distributor = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (Objects.equals(refund.getBuyerId(), distributor.getId())) {
                refundForBuyerApplication.cancel(refundId);
            }
            throw Translate.exceptionOf("退款单不属于你, 请由消费者取消退款");
        } catch (Exception e) {
            log.error("fail to cancel refund by refund id={}, cause:",
                    refundId, e);
            throw new JsonResponseException("order.refund.cancel.fail");
        }
    }


    /**
     * 当前不能跨订单申请退货, 但是支持同一订单的多个子订单申请退货  TODO 退货退款
     *
     * @param buyerNote  买家备注
     * @param orderIds   订单id列表
     * @param orderType  订单级别
     * @param imagesJson 图片json
     * @param reasonType 退货原因
     * @param refundType 退货退款类型
     * @return 创建的退款单id
     */
    @RequestMapping(value = "/order/return", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Long applyReturn(@RequestParam(value = "buyerNote", required = false) String buyerNote,
                            @RequestParam("orderIds") List<Long> orderIds,
                            @RequestParam(value = "orderType", defaultValue = "2") Integer orderType,
                            @RequestParam("imagesJson") String imagesJson,
                            @RequestParam("reasonType") Integer reasonType,
                            @RequestParam("type") Integer refundType) {
        if (CollectionUtils.isEmpty(orderIds)) {
            log.error("no orderIds specified when apply return");
            throw new JsonResponseException("order.return.fail");
        }
        try {
            CommonUser distributor = UserUtil.getCurrentUser();
            List<OrderRelation> orderRelations = new ArrayList<>(orderIds.size());
            for (Long orderId : orderIds) {
                OrderRefund orderRefund = new OrderRefund();
                orderRefund.setOrderId(orderId);
                orderRefund.setOrderLevel(OrderLevel.fromInt(orderType));
                orderRelations.add(orderRefund);
            }
            return refundForBuyerApplication.apply(distributor, orderRelations, buyerNote, Json.parseObject(imagesJson, new TypeReference<List<String>>() {
            }), Refund.RefundType.AFTER_SALE_RETURN, RefundReasonType.from(reasonType), null).take();
        } catch (Exception e) {
            log.error("failed to  apply return for order(ids={}, level={}), cause:",
                    orderIds, orderType, e);
            throw new JsonResponseException("order.return.fail");
        }
    }

    /**
     * 取消退款申请
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/return/{refundId}/cancel", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void cancelReturn(@PathVariable("refundId") Long refundId) {
        try {
            cancelRefund(refundId);
        } catch (Exception e) {
            log.error("fail to cancel return by refund id={}, cause:",
                    refundId, e);
            throw new JsonResponseException("order.return.cancel.fail");
        }
    }

    /**
     * 退货发货
     *
     * @param refundId        退款单id
     * @param returnExpressNo 退货快递单号
     */
    @RequestMapping(value = "/return/{refundId}/deliver", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void returns(@PathVariable("refundId") Long refundId, @RequestParam String returnExpressNo) {
        try {
            CommonUser distributor = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(refund.getBuyerId(), distributor.getId())) {
                throw Translate.exceptionOf("退款单不属于你");
            }
            // 临时操作
            Map<String, String> extra = refund.getExtra();
            extra.put("returnExpressNo", returnExpressNo);
            Refund update = new Refund();
            update.setId(refundId);
            update.setExtra(extra);
            refundWriteService.update(update);
        } catch (Exception e) {
            log.error("fail to returns by refund id={}, cause:",
                    refundId, e);
            throw new JsonResponseException("order.return.fail");
        }
    }

}
