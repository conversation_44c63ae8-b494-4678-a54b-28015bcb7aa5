package moonstone.web.distribution.order;

import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.DistributionOrderGroup;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.OrderGroup;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.service.WeShopReadService;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.component.profit.OrderProfitRecordManager;
import moonstone.web.core.component.profit.dto.OrderProfitRecord;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.core.express.dto.OrderExpressTrack;
import moonstone.web.core.express.service.impl.OrderExpress;
import moonstone.web.core.order.OrderReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/14.
 */
@Slf4j
@RestController
@RequestMapping("/api/weDistributor")
public class DistributionOrders {
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private WeShopReadService weShopReadService;
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private OrderProfitRecordManager orderProfitRecordManager;

    @Autowired
    private OrderReadLogic orderReadLogic;

    @Autowired
    private OrderExpress orderExpress;
    @Autowired
    private UserSubShopPackComponent userSubShopPackComponent;

    @RequestMapping(value = "/order/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Paging<DistributionOrderGroup> findForWeDistributor(@RequestParam Map<String, String> orderCriteria) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(commonUser, Json.OBJECT_MAPPER.convertValue(orderCriteria, OrderCriteria.class).getShopId());
        final Long weShopId = commonUser.getWeShopId();
        orderCriteria.put("outFrom", OrderOutFrom.WE_SHOP.Code());
        orderCriteria.put("outShopId", weShopId.toString());
        var param = Json.parseObject(Json.toJson(orderCriteria), OrderCriteria.class);
        Response<Paging<OrderGroup>> findResp = orderReadLogic.pagingOrder(param);
        if (!findResp.isSuccess()) {
            log.error("fail to find order for weDistributor(weShop id={}),criteria={},cause:{}",
                    weShopId, orderCriteria, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<DistributionOrderGroup> orderGroups = new ArrayList<>();
        Paging<OrderGroup> orderGroupPaging = findResp.getResult();
        for (OrderGroup orderGroup : orderGroupPaging.getData()) {
            DistributionOrderGroup distributionOrderGroup = CopyUtil.copy(orderGroup, DistributionOrderGroup.class);
            String weShopName;
            String outShopId = distributionOrderGroup.getShopOrder().getOutShopId();
            if (ObjectUtils.isEmpty(outShopId)) {
                log.error("{} shop order(id={}) lost outShopId", LogUtil.getClassMethodName(), distributionOrderGroup.getShopOrder().getId());
                String errorMsg = String.format("订单[%s] outShopId[%s] 查询失败", orderGroup.getShopOrder().getId(), outShopId);
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("微分销查询订单失败", errorMsg, new RuntimeException(errorMsg), EmailReceiverGroup.DEVELOPER));
                continue;
            } else {
                WeShop weShop = weShopCacheHolder.findByWeShopId(Long.valueOf(outShopId)).orElseGet(() -> {
                    Response<WeShop> rWeShop = weShopReadService.findById(Long.valueOf(outShopId));
                    if (!rWeShop.isSuccess()) {
                        log.error("failed to find weShop by id={}, error code: {}", outShopId, rWeShop.getError());
                        throw new JsonResponseException(rWeShop.getError());
                    }
                    return rWeShop.getResult();
                });
                weShopName = weShop.getName();
            }
            orderProfitRecordManager.findOrderProfit(orderGroup.getShopOrder())
                    .stream().findFirst()
                    .map(OrderProfitRecord::getProfit)
                    .ifPresent(distributionOrderGroup::setProfit);
            distributionOrderGroup.setWeShopName(weShopName);
            orderGroups.add(distributionOrderGroup);
        }
        return new Paging<>(orderGroupPaging.getTotal(), orderGroups);
    }

    @RequestMapping(value = "/order/{id}/detail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetail> detailForWeDistributor(@PathVariable("id") Long shopOrderId) {
        Response<ShopOrder> shopOrderR = shopOrderReadService.findById(shopOrderId);
        if (!shopOrderR.isSuccess()) {
            return Response.fail(shopOrderR.getError());
        }
        ShopOrder shopOrder = shopOrderR.getResult();
        CommonUser commonUser = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(commonUser, shopOrder.getShopId());
        boolean orderNotOwn = (commonUser.getWeShopId() == null || !Objects.equal(shopOrder.getOutShopId(), commonUser.getWeShopId().toString()))
                && !Objects.equal(shopOrder.getShopId(), commonUser.getShopId())
                && !Objects.equal(shopOrder.getBuyerId(), commonUser.getId());
        if (orderNotOwn) {
            log.error("the shopOrder(id={}) not belong to weDistributor(id={})",
                    shopOrder.getId(), commonUser.getId());
            throw new JsonResponseException("shop.order.not.belong.to.weDistributor");
        }

        return orderReadLogic.orderDetail(shopOrderId);
    }

    @RequestMapping(value = "/order/express", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderExpressTrack> orderExpress(@RequestParam("orderId") Long orderId,
                                                @RequestParam("orderType") Integer orderType) {
        return orderExpress.frontFindExpressTrack(UserUtil.getCurrentUser(), orderId, orderType);
    }

    @RequestMapping(value = "/shipment/{id}/sku-orders", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SkuOrder> findSkuOrdersForWeDistributor(@PathVariable("id") Long shipmentId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        List<SkuOrder> skuOrders = orderExpress.findSkuOrders(shipmentId);
        for (SkuOrder skuOrder : skuOrders) {
            if (!Objects.equal(skuOrder.getOutShopId(), commonUser.getShopId().toString()) && !Objects.equal(skuOrder.getBuyerId(), commonUser.getId())) {
                log.error("the skuOrder(id={}) not belong to weDistributor(id={})",
                        skuOrder.getId(), commonUser.getId());
                throw new JsonResponseException("sku.order.not.belong.to.weDistributor");
            }
        }
        return skuOrders;
    }

    @GetMapping(value = "/order/count-by-status")
    public Long countByStatus(int status, @RequestParam(required = false) Long shopId) {
        CommonUser user = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(user, shopId);
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setBuyerId(user.getId());
        orderCriteria.setStatus(Collections.singletonList(status));
        Response<Long> countResult = orderReadLogic.countShopOrder(orderCriteria);
        if (countResult.isSuccess()) {
            return countResult.getResult();
        }
        throw new JsonResponseException(countResult.getError());
    }
}
