package moonstone.web.distribution.user.app;

import moonstone.cache.WeShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.UserUtil;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.weShop.service.WeShopWriteService;
import moonstone.web.core.component.UserSessionManager;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.distribution.component.user.AppUserConverter;
import moonstone.web.distribution.user.app.dto.LoginParam;
import moonstone.web.distribution.user.app.view.UserForApp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/app/v1/user")
public class AppUserController {
    @Autowired
    private WeShopCacheHolder weShopCacheHolder;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private AppUserConverter appUserConverter;
    @Autowired
    private WeShopWriteService weShopWriteService;
    @Autowired
    private UserSessionManager userSessionManager;
    @Autowired
    private UserSubShopPackComponent userSubShopPackComponent;

    @PostMapping("/login")
    public APIResp<UserForApp> login(@RequestBody LoginParam loginParam, @RequestParam(required = false) Long shopId) {
        if (current(shopId).ok()) {
            return current(shopId);
        }
        return appUserConverter.convert(userReadService.login(loginParam.getLoginId(), loginParam.getPassword(), loginParam.getLoginType()).getResult())
                .map(APIResp::ok).orElseGet(() -> APIResp.error("用户登录失败, 请检查用户名与密码"));
    }

    /**
     * 下线
     *
     * @return 是否成功下线或者未登录
     */
    @DeleteMapping("/current")
    public APIResp<Boolean> logout() {
        if (UserUtil.getCurrentUser() == null) {
            return APIResp.notLogin();
        }
        userSessionManager.kickTheUserByUserId(UserUtil.getCurrentUser());
        return APIResp.ok(true);
    }

    public APIResp<UserForApp> current() {
        return current(null);
    }

    /**
     * 获取目前用户
     *
     * @return 登录成功的用户信息
     */
    @GetMapping("/current")
    public APIResp<UserForApp> current(@RequestParam(required = false) Long shopId) {
        // 从Session中获取用户信息, 以此为登录凭证 但是不以之为全部凭证
        CommonUser current = UserUtil.getCurrentUser();
        if (current == null || current.getId() == null || !weShopCacheHolder.findByUserId(current.getId()).isPresent()) {
            return APIResp.notLogin();
        }
        userSubShopPackComponent.wrap(current, shopId);
        Optional<UserForApp> convertResult = appUserConverter.convert(current);
        return convertResult.map(APIResp::ok).orElseGet(() -> APIResp.error("用户信息获取失败"));
    }

    /**
     * 修改App中的用户店铺名称
     *
     * @param name 店铺新名字
     * @return 修改成功
     */
    @PutMapping("/rename")
    public APIResp<Boolean> rename(String name, @RequestParam(required = false) Long shopId) {
        if (!current(shopId).ok()) {
            return APIResp.notLogin();
        }
        return APIRespWrapper.wrap(weShopWriteService.rename(current(shopId).getData().getId(), name));
    }

    /**
     * 切换新Logo
     *
     * @param logo 新LogoUrl 请使用OSS上传后的Url, 系统将自动剪切URL
     * @return 修改成功无异常
     */
    @PutMapping("/logo")
    public APIResp<Boolean> changeLogo(String logo, @RequestParam(required = false) Long shopId) {
        if (!current(shopId).ok()) {
            return APIResp.notLogin();
        }
        return APIRespWrapper.wrap(weShopWriteService.changeLogo(current(shopId).getData().getId(), logo));
    }

}
