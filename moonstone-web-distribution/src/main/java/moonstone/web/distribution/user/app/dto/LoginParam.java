package moonstone.web.distribution.user.app.dto;

import lombok.Data;
import moonstone.user.model.LoginType;
import org.springframework.util.StringUtils;

@Data
public class LoginParam {
    String mobile;
    String code;
    String username;
    String password;

    /**
     * 获取用户登录用数据
     *
     * @return 登录凭证
     */
    public String getLoginId() {
        if (StringUtils.hasText(mobile))
            return mobile;
        if (StringUtils.hasText(username))
            return username;
        return null;
    }

    /**
     * 获取目前的登录类型
     *
     * @return 登录类型
     */
    public LoginType getLoginType() {
        // 邮件Match
        final String EMAIL_MATCH = ".*@.*\\..*";

        if (StringUtils.hasText(mobile))
            return LoginType.MOBILE;
        if (StringUtils.hasText(username)) {
            if (username.matches(EMAIL_MATCH)) {
                return LoginType.EMAIL;
            }
            return LoginType.NAME;
        }
        return LoginType.OTHER;
    }
}
