package moonstone.web.distribution.user;

import com.alibaba.fastjson.JSON;
import com.google.common.base.CharMatcher;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.api.APIResp;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.enums.CountryCode;
import moonstone.common.enums.ParanaUserDistributedStatus;
import moonstone.common.enums.UserStatus;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.model.ParanaUserProfile;
import moonstone.common.model.WxCommonUser;
import moonstone.common.utils.*;
import moonstone.shop.service.ShopReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.dto.WxSpBean;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.LoginType;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.model.UserWx;
import moonstone.user.service.*;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopWallet;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopWalletReadService;
import moonstone.web.core.AppConstants;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.events.user.LoginEvent;
import moonstone.web.core.events.user.UserProfileUpdateEvent;
import moonstone.web.core.events.user.UserRegisteredEvent;
import moonstone.web.core.events.user.WxLoginEvent;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.user.api.LoginService;
import moonstone.web.core.util.ParanaUserMaker;
import moonstone.web.distribution.component.user.DistributionUserWriteLogic;
import moonstone.web.distribution.constants.DistributionSessions;
import moonstone.web.distribution.user.dto.DistributionParanaUserProfile;
import moonstone.web.distribution.util.DistributionParanaUserProfileMaker;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Created by CaiZhy on 2018/12/11.
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class DistributionUsers {

    private static final DateTimeFormatter DTF = DateTimeFormat.forPattern("yyyy-MM-dd");
    @RpcConsumer
    private UserProfileReadService userProfileReadService;
    @RpcConsumer
    private UserProfileWriteService userProfileWriteService;
    @RpcConsumer
    private UserReadService<User> userReadService;
    @RpcConsumer
    private UserWriteService<User> userWriteService;
    @RpcConsumer
    private UserWxReadService userWxReadService;
    @RpcConsumer
    private UserWxWriteService userWxWriteService;
    @RpcConsumer
    private ShopWxaReadService shopWxaReadService;
    @RpcConsumer
    private ShopWxaProjectReadService shopWxaProjectReadeService;
    @RpcConsumer
    private ShopReadService shopReadService;
    @RpcConsumer
    private WeShopReadService weShopReadService;
    @RpcConsumer
    private WeShopWalletReadService weShopWalletReadService;
    @RpcConsumer
    private LoginService loginService;
    @Autowired
    private DistributionUserWriteLogic distributionUserWriteLogic;
    @Autowired
    private UserTypeBean userTypeBean;
    @Autowired
    private ParanaConfig paranaConfig;
    @Autowired
    private FunctionSwitch functionSwitch;
    @Autowired
    private UserSubShopPackComponent userSubShopPackComponent;

    @RequestMapping(value = "/login-by-mobile", produces = MediaType.APPLICATION_JSON_VALUE)
    public CommonUser loginByMobileMsg(@RequestParam(defaultValue = "0086") String countryCode,
                                       @RequestParam String mobile,
                                       @RequestParam String code,
                                       @RequestParam(required = false) Long shopId,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {
        log.debug("[op:login] mobile={}, code={}", mobile, code);
        mobile = judgeMobile(countryCode, mobile);

        HttpSession session = request.getSession();
        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_LOGIN, code, mobile);

        Response<User> userResp = userReadService.findByMobile(mobile);
        if (!userResp.isSuccess()) {
            log.error("failed to find user by mobile={}, error={}", mobile, userResp.getError());
            throw new JsonResponseException(userResp.getError());
        }
        User user = userResp.getResult();
        CommonUser commonUser;
        if (user != null) {
            commonUser = buildParanaUser(user);
        } else {
            Long userId = doRegister(mobile, null, null, null);
            commonUser = new CommonUser();
            commonUser.setId(userId);
            commonUser.setType(userTypeBean.getBuyerType());
            commonUser.setRoles(userTypeBean.getBuyerRole());
            commonUser.setHasPassword(false);
        }

        checkAndBecomeWeDistributor(commonUser);
        userSubShopPackComponent.wrap(commonUser, shopId);

        checkDistributedStatus(commonUser);

        EventSender.sendApplicationEvent(new LoginEvent(request, response, commonUser));
        return commonUser;
    }

    private void checkAndBecomeWeDistributor(CommonUser commonUser) {
        if (!userTypeBean.isWeDistributor(commonUser)) {
            distributionUserWriteLogic.becomeDistributor(commonUser);
        }
    }

    private void checkDistributedStatus(CommonUser commonUser) {
        Optional<WeShop> weShopOpt = Optional.ofNullable(weShopReadService.findById(commonUser.getWeShopId()).getResult());
        if (!weShopOpt.isPresent()) {
            commonUser.setDistributedStatus(ParanaUserDistributedStatus.NONE_EXISTS.getValue());
            return;
        }
        WeShop weShop = weShopOpt.get();
        if (Objects.isNull(weShop.getStatus())) {
            weShop.setStatus(-999);
        }
        switch (weShop.getStatus()) {
            case -1:
                commonUser.setDistributedStatus(ParanaUserDistributedStatus.REJECT.getValue());
                break;
            case 0:
                commonUser.setDistributedStatus(ParanaUserDistributedStatus.WAITING_AUTH.getValue());
                break;
            case 1:
                commonUser.setDistributedStatus(ParanaUserDistributedStatus.AUTHED.getValue());
                break;
            default:
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("微店状态判断不明", Translate.of("微店[id => %s, userId => %s] 状态[%s]不明确", weShop.getId(), weShop.getUserId(), weShop.getStatus()), EmailReceiverGroup.DEVELOPER));
        }
    }

    @RequestMapping(value = "/wxaProjectLogin", produces = MediaType.APPLICATION_JSON_VALUE)
    public WxCommonUser wxaProjectLogin(String wxCode, Long wxaProjectId, @RequestParam(required = false) Long shopId
                                        , HttpServletRequest request            , HttpServletResponse response) {
        try {
            log.debug("[op:wxaProjectLogin] wxCode={}", wxCode);
            String appId;
            String appSecret;
            ShopWxa shopWxa = new ShopWxa();
            if (wxaProjectId.equals(0L)) {
                appId = paranaConfig.getParanaWeSellerAppId();
                appSecret = paranaConfig.getParanaWeSellerAppSecret();
            } else {
                Response<ShopWxaProject> shopWxaProjectResponse = shopWxaProjectReadeService.findById(wxaProjectId);
                if (!shopWxaProjectResponse.isSuccess()) {
                    log.error("[op:wxaProjectLogin] failed to find shopWxaProject by id={}, error code: {}",
                            wxaProjectId, shopWxaProjectResponse.getError());
                    throw new JsonResponseException(shopWxaProjectResponse.getError());
                }
                ShopWxaProject shopWxaProject = shopWxaProjectResponse.getResult();

                Response<ShopWxa> shopWxaResponse = shopWxaReadService.findById(shopWxaProject.getShopWxaId());
                if (!shopWxaResponse.isSuccess()) {
                    log.error("[op:wxaProjectLogin] failed to find shopWxa by id={}, error code: {}",
                            shopWxaProject.getShopWxaId(), shopWxaResponse.getError());
                    throw new JsonResponseException(shopWxaResponse.getError());
                }
                shopWxa = shopWxaResponse.getResult();

                log.debug("[op:wxaProjectLogin] use shopWxa({})", shopWxa);

                appId = shopWxa.getAppId();
                appSecret = shopWxa.getAppSecret();
                shopId = shopWxa.getShopId();
            }
            WxCommonUser wxParanaUser = loginService.wxaLogin(wxCode, appId, appSecret);

            if (userTypeBean.isSeller(wxParanaUser)) {
                val shopResp = shopReadService.findByUserId(wxParanaUser.getId());
                if (!shopResp.isSuccess()) {
                    log.warn("[op:wxaProjectLogin] find shop by userId={} failed, error={}", wxParanaUser.getId(), shopResp.getError());
                    throw new JsonResponseException(shopResp.getError());
                }
                wxParanaUser.setShopId(shopResp.getResult().getId());
            }

            checkAndBecomeWeDistributor(wxParanaUser);
            userSubShopPackComponent.wrap(wxParanaUser, shopId);

            checkDistributedStatus(wxParanaUser);

            UserUtil.getCurrentSession().invalidate();
            HttpSession session = request.getSession(true);
            session.setAttribute(AppConstants.SESSION_USER_ID, wxParanaUser.getId());
            session.setAttribute(AppConstants.SESSION_OPEN_ID, wxParanaUser.getOpenId());

            log.debug("[op:wxaProjectLogin] result={}", JSON.toJSONString(wxParanaUser));
            EventSender.sendApplicationEvent(new WxLoginEvent(response, wxParanaUser));
            wxParanaUser.setSessionId(session.getId());
            wxParanaUser.setUserId(wxParanaUser.getId());
            try {
                wxParanaUser.setGuider(guiderCache.findByShopIdAndUserId(shopWxa.getShopId(), wxParanaUser.getUserId()).isPresent());
            } catch (Exception ignore) {

            }

            log.info("Login Code {} -> {}", wxCode, wxParanaUser);
            return wxParanaUser;
        } catch (Exception e) {
            log.warn("[op:wxaProjectLogin] login failed, wxCode={}, wxaProjectId={}", wxCode, wxaProjectId, e);
            throw new JsonResponseException("login.wxa.fail");
        }
    }

    @Autowired
    GuiderCache guiderCache;

    @RequestMapping(value = "/wxa/code2Session", produces = MediaType.APPLICATION_JSON_VALUE)
    public WxSpBean wxaCode2Session(String wxCode, Long wxaProjectId, HttpServletResponse response) {
        try {
            log.debug("[op:wxaCode2Session] wxCode={}", wxCode);
            String appId;
            String appSecret;
            if (wxaProjectId.equals(0L)) {
                appId = paranaConfig.getParanaWeSellerAppId();
                appSecret = paranaConfig.getParanaWeSellerAppSecret();
            } else {
                Response<ShopWxaProject> shopWxaProjectResponse = shopWxaProjectReadeService.findById(wxaProjectId);
                if (!shopWxaProjectResponse.isSuccess()) {
                    log.error("[op:wxaCode2Session] failed to find shopWxaProject by id={}, error code: {}",
                            wxaProjectId, shopWxaProjectResponse.getError());
                    throw new JsonResponseException(shopWxaProjectResponse.getError());
                }
                ShopWxaProject shopWxaProject = shopWxaProjectResponse.getResult();

                Response<ShopWxa> shopWxaResponse = shopWxaReadService.findById(shopWxaProject.getShopWxaId());
                if (!shopWxaResponse.isSuccess()) {
                    log.error("[op:wxaCode2Session] failed to find shopWxa by id={}, error code: {}",
                            shopWxaProject.getShopWxaId(), shopWxaResponse.getError());
                    throw new JsonResponseException(shopWxaResponse.getError());
                }
                ShopWxa shopWxa = shopWxaResponse.getResult();

                log.debug("[op:wxaCode2Session] use shopWxa({})", shopWxa);

                appId = shopWxa.getAppId();
                appSecret = shopWxa.getAppSecret();
            }

            WxSpBean wxSpBean = loginService.code2Session(wxCode, appId, appSecret);

            return wxSpBean;
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("[op:wxaCode2Session] code2Session failed by wxCode={}, wxaProjectId={}, error={}", wxCode, wxaProjectId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("code2Session.fail");
        }
    }

    /**
     * 未绑定手机号的微信用户，绑定手机号
     *
     * @param mobile
     * @param code
     * @param request
     * @return 绑定后的用户id
     */
    @RequestMapping(value = "/newWxUserBind", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long newWxUserBind(@RequestParam(defaultValue = "0086") String countryCode,
                              @RequestParam String mobile,
                              @RequestParam String code,
                              @RequestParam String appId,
                              HttpServletRequest request) {
        mobile = judgeMobile(countryCode, mobile);

        HttpSession session = request.getSession();
        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_LOGIN, code, mobile);

        String openId = UserOpenIdUtil.getOpenId();
        //微信信息
        Response<UserWx> rUserWx = userWxReadService.findByOpenIdAndAppId(openId, appId);
        if (!rUserWx.isSuccess()) {
            log.error("[op:newUserBind] failed to find userWx by openId={}, appId={}, error code: {}", openId, appId, rUserWx.getError());
            throw new JsonResponseException(rUserWx.getError());
        }
        UserWx userWx = rUserWx.getResult();
        //首次微信登录时创建的用户
        Response<User> rWxUser = userReadService.findById(userWx.getUserId());
        if (!rWxUser.isSuccess()) {
            log.error("failed to find user by id={}, error code: {}", userWx.getUserId(), rWxUser.getError());
            throw new JsonResponseException(rWxUser.getError());
        }
        User wxUser = rWxUser.getResult();
        //判断是不是新用户
        if (wxUser.getTags() == null || !"true".equals(wxUser.getTags().get(ParanaConstants.USER_IS_NEW_WX_USER))) {
            log.error("[op:newUserBind] illegal request, openId={}, appId={}, mobile={}", openId, appId, mobile);
            throw new JsonResponseException("user.bind.illegal");
        }
        //要绑定的手机号用户
        Response<User> rUser = userReadService.findByMobile(mobile);
       /* if (!rUser.isSuccess()) {
            log.error("[op:newUserBind] failed to find user by mobile={}, error code: {}", mobile, rUser.getError());
            throw new JsonResponseException(rUser.getError());
        }*/
        User existUser = rUser.getResult();

        return distributionUserWriteLogic.wxUserBindUser(userWx, wxUser, existUser, mobile);
    }

    private CommonUser buildParanaUser(User user) {
        if (Objects.equals(user.getStatus(), UserStatus.DELETED.value())) {
            throw new JsonResponseException("user.not.found");
        }
        if (Objects.equals(user.getStatus(), UserStatus.FROZEN.value())) {
            throw new JsonResponseException("user.status.frozen");
        }
        if (Objects.equals(user.getStatus(), UserStatus.LOCKED.value())) {
            throw new JsonResponseException("user.status.locked");
        }
        CommonUser commonUser = ParanaUserMaker.from(user);

//        TODO 需不需要shopId
        if (userTypeBean.isSeller(user)) {
            val shopResp = shopReadService.findByUserId(user.getId());
            if (!shopResp.isSuccess()) {
                log.warn("find shop by userId={} failed, error={}", user.getId(), shopResp.getError());
                throw new JsonResponseException(shopResp.getError());
            }
            commonUser.setShopId(shopResp.getResult().getId());
        }

        if (userTypeBean.isWeDistributor(user)) {
            val weShopResp = weShopReadService.findByUserId(user.getId());
            if (!weShopResp.isSuccess()) {
                log.warn("find weShop by userId={} failed, error={}", user.getId(), weShopResp.getError());
                throw new JsonResponseException(weShopResp.getError());
            }
            commonUser.setWeShopId(weShopResp.getResult().getId());
        }

        return commonUser;
    }

    @RequestMapping(value = "/mobile-available")
    public Boolean mobile_available(@RequestParam(defaultValue = "0086") String countryCode,
                                    @RequestParam String mobile) {
        mobile = judgeMobile(countryCode, mobile);
        Response<User> rUser = userReadService.findByMobile(mobile);
        if (mobile.startsWith(CountryCode.China.getCode()) && (!rUser.isSuccess() && rUser.getResult() == null)) {
            rUser = userReadService.findByMobile(mobile.substring(CountryCode.PREFIX_CODE_LEN));
        }
        return rUser.isSuccess() && rUser.getResult() != null;
    }

    @RequestMapping(value = "/register-by-mobile")
    public Long registerByMobile(@RequestParam(defaultValue = "0086") String countryCode,
                                 @RequestParam String mobile,
                                 @RequestParam String code,
                                 @RequestParam String password,
                                 HttpServletRequest request) {

        mobile = judgeMobile(countryCode, mobile);
        HttpSession session = request.getSession();
        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_REG, code, mobile);

        judgePassword(password);
        judgeMobileNotExist(mobile);
        Long userId = doRegister(mobile, null, null, password);

        session.setAttribute("userId", userId);
        return userId;
    }

    private Long doRegister(String mobile, String email, String username, String password) {
        User user = new User();
        user.setMobile(mobile);
        user.setEmail(email);
        user.setName(username);
        user.setPassword(password);

        user.setStatus(UserStatus.NORMAL.value());
        user.setType(userTypeBean.getBuyerType());
        user.setRoles(userTypeBean.getBuyerRole());

        Response<Long> resp = userWriteService.create(user);

        if (!resp.isSuccess()) {
            log.warn("create user failed, mobile={}, error={}", mobile, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
        Long userId = resp.getResult();
        postRegisteredEvent(userId);
        return userId;
    }

    private void postRegisteredEvent(Long userId) {
        try {
            log.debug("posting user registered event, userId={}", userId);
            EventSender.sendApplicationEvent(new UserRegisteredEvent(userId));
        } catch (Exception e) {
            log.warn("post user registered event failed, userId={}, cause:{}",
                    userId, Throwables.getStackTraceAsString(e));
        }
    }

    @RequestMapping(value = "/change-mobile", method = RequestMethod.POST)
    public void changeMobile(@RequestParam(defaultValue = "0086") String countryCode,
                             @RequestParam String mobile,
                             @RequestParam String code,
                             HttpServletRequest request) {
        mobile = judgeMobile(countryCode, mobile);

        judgeMobileNotExist(mobile);

        HttpSession session = request.getSession();
        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_CHG_MOB, code, mobile);

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }

        User user = new User();
        user.setId(userId);
        user.setMobile(mobile);
        Response<Boolean> resp = userWriteService.update(user);
        if (!resp.isSuccess()) {
            log.error("change mobile for user(id={}) failed, error={}", userId, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
    }

    @RequestMapping(value = "/change-mobile-by-old/send-sms", method = RequestMethod.POST)
    public void sendSmsForChangeMobileByOld(HttpServletRequest request) {

        HttpSession session = request.getSession();

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }
        Response<User> rUser = userReadService.findById(userId);
        if (!rUser.isSuccess()) {
            log.error("failed to find user by id={}, error code: {}", userId, rUser.getError());
            throw new JsonResponseException(rUser.getError());
        }
        String mobile = rUser.getResult().getMobile();

        String chgCode = (String) session.getAttribute(AppConstants.SESSION_SMS_CODE_CHG_MOB_BY_OLD);
        failIfCannotResend(chgCode);

        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        session.setAttribute(AppConstants.SESSION_SMS_CODE_CHG_MOB_BY_OLD, code + "@" + System.currentTimeMillis() + "@" + mobile);
        // 发送验证码
        doSendSmsForChangeMobile(mobile, code);
    }

    @RequestMapping(value = "/change-mobile-by-old-code-check", method = RequestMethod.POST)
    public Boolean checkCodeForChangeMobileByOld(@RequestParam String code,
                                                 HttpServletRequest request) {
        HttpSession session = request.getSession();

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }
        Response<User> rUser = userReadService.findById(userId);
        if (!rUser.isSuccess()) {
            log.error("failed to find user by id={}, error code: {}", userId, rUser.getError());
            throw new JsonResponseException(rUser.getError());
        }
        String mobile = rUser.getResult().getMobile();

        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_CHG_MOB_BY_OLD, code, mobile);

        session.setAttribute(DistributionSessions.CHECK_FOR_CHG_MOB, true);

        return Boolean.TRUE;
    }

    @RequestMapping(value = "/change-mobile-wallet-password-check", method = RequestMethod.POST)
    public Boolean checkWalletPasswordForChangeMobile(@RequestParam String password,
                                                      HttpServletRequest request) {
        HttpSession session = request.getSession();

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new JsonResponseException("user.not.login");
        }

        Long weShopId = commonUser.getWeShopId();
        if (weShopId == null) {
            Response<WeShop> weShopResponse = weShopReadService.findByUserId(commonUser.getId());
            if (!weShopResponse.isSuccess()) {
                log.error("failed to find weShop by userId={}, error code: {}", commonUser.getId(), weShopResponse.getError());
                throw new JsonResponseException(weShopResponse.getError());
            }
            WeShop weShop = weShopResponse.getResult();
            if (ObjectUtils.isEmpty(weShop)) {
                log.error("can not find weShop bu userId={}", commonUser.getId());
                throw new JsonResponseException("weShop.can.not.find");
            }
            weShopId = weShop.getId();
        }

        Response<WeShopWallet> rWeShopWallet = weShopWalletReadService.findByWeShopId(weShopId);
        if (!rWeShopWallet.isSuccess()) {
            log.error("failed to find weShop wallet by weShopId={}, error code: {}", weShopId, rWeShopWallet.getError());
            throw new JsonResponseException(rWeShopWallet.getError());
        }
        WeShopWallet weShopWallet = rWeShopWallet.getResult();
        if (ObjectUtils.isEmpty(weShopWallet)) {
            log.error("can not find weShop wallet by weShopId={}", weShopId);
            throw new JsonResponseException("weShopWallet.can.not.find");
        }
        if (!EncryptUtil.match(password, weShopWallet.getPassword())) {
            log.warn("weShop wallet password{} isn't matched.", password);
            throw new JsonResponseException("weShopWallet.password.mismatch");
        }

        session.setAttribute(DistributionSessions.CHECK_FOR_CHG_MOB, true);

        return Boolean.TRUE;
    }

    @RequestMapping(value = "/change-mobile-by-old-after-check", method = RequestMethod.POST)
    public Boolean changeMobileByOldAfterCheck(@RequestParam(defaultValue = "0086") String countryCode,
                                               @RequestParam String mobile,
                                               HttpServletRequest request) {
        mobile = judgeMobile(countryCode, mobile);

        judgeMobileNotExist(mobile);

        HttpSession session = request.getSession();

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }

        Boolean isChecked = (Boolean) session.getAttribute(DistributionSessions.CHECK_FOR_CHG_MOB);

        if (isChecked == null || !isChecked) {
            log.error("user(id={}) want to change mobile without checked", userId);
            throw new JsonResponseException("user.change.mobile.not.checked");
        }

        User user = new User();
        user.setId(userId);
        user.setMobile(mobile);
        Response<Boolean> resp = userWriteService.update(user);
        if (!resp.isSuccess()) {
            log.error("change mobile for user(id={}) failed, error={}", userId, resp.getError());
            throw new JsonResponseException(resp.getError());
        }

        session.removeAttribute(DistributionSessions.CHECK_FOR_CHG_MOB);

        return Boolean.TRUE;
    }

    @RequestMapping(value = "/reset-password-by-mobile", method = RequestMethod.POST)
    public void resetPasswordByMobile(@RequestParam(defaultValue = "0086") String countryCode,
                                      @RequestParam String mobile,
                                      @RequestParam String code,
                                      @RequestParam String password,
                                      HttpServletRequest request) {
        mobile = judgeMobile(countryCode, mobile);
        judgePassword(password);
        val mobileResp = userReadService.findBy(mobile, LoginType.MOBILE);
        if (!mobileResp.isSuccess()) {
            log.warn("find user by mobile failed, mobile={}, error={}", mobile, mobileResp.getError());
            throw new JsonResponseException(mobileResp.getError());
        }
        User user = mobileResp.getResult();
        Long userId = user.getId();

        HttpSession session = request.getSession();
        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_RESET_PW, code, mobile);

        User toUpdate = new User();
        toUpdate.setId(userId);
        toUpdate.setPassword(EncryptUtil.encrypt(password));
        Response<Boolean> resp = userWriteService.update(toUpdate);
        if (!resp.isSuccess()) {
            log.error("change mobile for user(id={}) failed, error={}", userId, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
    }

    private String judgeMobile(String countryCode, String mobile) {
        countryCode = countryCode.trim();
        if (countryCode.startsWith("+")) {
            countryCode = countryCode.replaceAll("^\\+", CountryCode.PREFIX_CODE);
        }
        if (countryCode.equals(CountryCode.China.getCode())) {
            judgeMobile(mobile);
            return countryCode + mobile;
        }
        if (!functionSwitch.getForeignSms()) {
            throw new JsonResponseException(400, "user.mobile.unsupport");
        }
        if (countryCode.equals(CountryCode.Australia.getCode())) {
            if (!mobile.matches("^\\d{9}$")) {
                throw new JsonResponseException(400, "user.mobile.invalid");
            }
            return countryCode + mobile;
        }
        throw new JsonResponseException(400, "user.mobile.invalid");
    }

    private void judgeMobile(String mobile) {
//        if (!mobile.matches("^((13[0-9])|(14[0-9])|(15[0-9])|(17[0-9])|(18[0-9]))\\d{8}$")) {
        if (!mobile.matches("^\\d{11}$")) {
            throw new JsonResponseException(400, "user.mobile.invalid");
        }
    }

    private void judgePassword(String password) {
        if (!password.matches("[\\s\\S]{6,16}")) {
            throw new JsonResponseException(400, "user.password.invalid");
        }
    }

    private void judgeUsername(String username) {
        if (CharMatcher.inRange('0', '9').matchesAllOf(username)
                || CharMatcher.is('@').matchesAnyOf(username)) {
            throw new JsonResponseException(400, "user.username.invalid");
        }
    }

    private void judgeMobileNotExist(String mobile) {
        val r = userReadService.checkExist(mobile, LoginType.MOBILE);
        if (!r.isSuccess()) {
            log.warn("checking mobile exist failed, mobile={}, error={}", mobile, r.getError());
            throw new JsonResponseException(r.getError());
        }
        if (r.getResult()) {
            log.warn("mobile existing, mobile={}", mobile);
            throw new JsonResponseException("user.mobile.already.exist");
        }
    }

    private void verifySmsCode(HttpSession session, String codeKey, String code, String mobile) {
        // session verify, value = code@time@mobile
        String codeInSession = (String) session.getAttribute(codeKey);
        if (Strings.isNullOrEmpty(codeInSession)) {
            log.warn("sent sms code not in session, mobile={}", mobile);
            throw new JsonResponseException("sms.code.expired");
        }
        String expectedCode = Splitters.AT.splitToList(codeInSession).get(0);
        if (!Objects.equals(code, expectedCode)) {
            log.warn("sms code mismatch, for mobile={}", mobile);
            throw new JsonResponseException("sms.code.mismatch");
        }
        String expectedMobile = Splitters.AT.splitToList(codeInSession).get(2);
        if (!Objects.equals(mobile, expectedMobile)) {
            log.warn("mobile not match for sms code, intended={}, actual={}", expectedMobile, mobile);
            throw new JsonResponseException(400, "invoke.invalid");
        }
        // 如果验证成功则删除之前的code
        session.removeAttribute(codeKey);
    }

    @RequestMapping(value = "/register-by-mobile/send-sms", method = RequestMethod.POST)
    public void sendSmsForRegister(@RequestParam(defaultValue = "0086") String countryCode,
                                   @RequestParam String mobile,
                                   HttpServletRequest request) {

        mobile = judgeMobile(countryCode, mobile);
        HttpSession session = request.getSession();

        String regCode = (String) session.getAttribute(AppConstants.SESSION_SMS_CODE_REG);

        failIfCannotResend(regCode);

        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        session.setAttribute(AppConstants.SESSION_SMS_CODE_REG, code + "@" + System.currentTimeMillis() + "@" + mobile);
        // 发送验证码
        doSendSmsForRegister(mobile, code);
    }

    @RequestMapping(value = "/login-by-mobile/send-sms", method = RequestMethod.POST)
    public void sendSmsForLogin(@RequestParam(defaultValue = "0086") String countryCode,
                                @RequestParam String mobile, HttpServletRequest request) {

        mobile = judgeMobile(countryCode, mobile);
        HttpSession session = request.getSession();

        String regCode = (String) session.getAttribute(AppConstants.SESSION_SMS_CODE_LOGIN);

        failIfCannotResend(regCode);

        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        session.setAttribute(AppConstants.SESSION_SMS_CODE_LOGIN, code + "@" + System.currentTimeMillis() + "@" + mobile);
        // 发送验证码
        doSendSmsForLogin(mobile, code);
    }

    @RequestMapping(value = "/change-mobile/send-sms", method = RequestMethod.POST)
    public void sendSmsForChangeMobile(@RequestParam(defaultValue = "0086") String countryCode,
                                       @RequestParam String mobile,
                                       HttpServletRequest request) {
        mobile = judgeMobile(countryCode, mobile);

        judgeMobileNotExist(mobile);

        HttpSession session = request.getSession();

        String chgCode = (String) session.getAttribute(AppConstants.SESSION_SMS_CODE_CHG_MOB);
        failIfCannotResend(chgCode);

        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        session.setAttribute(AppConstants.SESSION_SMS_CODE_CHG_MOB, code + "@" + System.currentTimeMillis() + "@" + mobile);
        // 发送验证码
        doSendSmsForChangeMobile(mobile, code);
    }

    @RequestMapping(value = "/reset-password-by-mobile/send-sms", method = RequestMethod.POST)
    public void sendSmsForResetPassword(@RequestParam(defaultValue = "0086") String countryCode,
                                        @RequestParam String mobile,
                                        HttpServletRequest request) {
        mobile = judgeMobile(countryCode, mobile);

        HttpSession session = request.getSession();

        String fgtCode = (String) session.getAttribute(AppConstants.SESSION_SMS_CODE_RESET_PW);
        failIfCannotResend(fgtCode);

        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        session.setAttribute(AppConstants.SESSION_SMS_CODE_RESET_PW, code + "@" + System.currentTimeMillis() + "@" + mobile);
        // 发送验证码
        doSendSmsForForgetPassword(mobile, code);
    }

    @RequestMapping(value = "/my-profile", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public DistributionParanaUserProfile getProfileByLoginUser() {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException(401, "user.not.login");
        }
        val userResp = userReadService.findById(userId);
        if (!userResp.isSuccess()) {
            throw new JsonResponseException(userResp.getError());
        }
        User user = userResp.getResult();
        val profileResp = userProfileReadService.findProfileByUserId(userId);
        if (!profileResp.isSuccess()) {
            throw new JsonResponseException(profileResp.getError());
        }
        UserProfile profile = profileResp.getResult();
        val weShopResp = weShopReadService.findByUserId(userId);
        if (!weShopResp.isSuccess()) {
            throw new JsonResponseException(weShopResp.getError());
        }
        WeShop weShop = weShopResp.getResult();

        DistributionParanaUserProfile distributionParanaUserProfile = DistributionParanaUserProfileMaker.from(user, profile, weShop);

        String mobile = distributionParanaUserProfile.getMobile();
        if (!ObjectUtils.isEmpty(mobile)) {
            distributionParanaUserProfile.setMobile(mobile.substring(0, 3) + "****" + mobile.substring(7));
        }

        return distributionParanaUserProfile;
    }

    @RequestMapping(value = "/my-profile", method = RequestMethod.PUT)
    public void updateMyProfile(@RequestBody ParanaUserProfile profile) {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException(401, "user.not.login");
        }
        if (!Strings.isNullOrEmpty(profile.getUsername())) {
            judgeUsername(profile.getUsername());
            Boolean isExist = RespHelper.or500(userReadService.checkExist(profile.getUsername(), LoginType.NAME));
            if (isExist) {
                User existUser = RespHelper.or500(userReadService.findBy(profile.getUsername(), LoginType.NAME));
                if (!Objects.equals(userId, existUser.getId())) {
                    throw new JsonResponseException("user.username.already.exist");
                }
            }
            User u = new User();
            u.setId(userId);
            u.setName(profile.getUsername());
            val updateUserResp = userWriteService.update(u);
            if (!updateUserResp.isSuccess()) {
                log.warn("update user username in profile failed, error={}", updateUserResp.getError());
                throw new JsonResponseException(updateUserResp.getError());
            }
        }
        val existResp = userProfileReadService.findProfileByUserId(userId);
        if (!existResp.isSuccess()) {
            throw new JsonResponseException(existResp.getError());
        }
        UserProfile toUpdate = new UserProfile(userId);
        toUpdate.setAvatar(profile.getAvatar());
        toUpdate.setRealName(profile.getRealName());
        toUpdate.setGender(profile.getGender());
        if (profile.getBirthday() != null) {
            toUpdate.setBirth(DTF.print(new LocalDate(profile.getBirthday())));
        }
        toUpdate.setProvinceId(profile.getProvinceId());
        toUpdate.setProvince(profile.getProvince());
        toUpdate.setCityId(profile.getCityId());
        toUpdate.setCity(profile.getCity());
        toUpdate.setRegionId(profile.getRegionId());
        toUpdate.setRegion(profile.getRegion());
        toUpdate.setStreet(profile.getStreet());
        Response<Boolean> resp;
        if (existResp.getResult().getId() == null) {
            resp = userProfileWriteService.createProfile(toUpdate);
        } else {
            resp = userProfileWriteService.updateProfile(toUpdate);
        }
        if (!resp.isSuccess()) {
            throw new JsonResponseException(resp.getError());
        }
        EventSender.publish(new UserProfileUpdateEvent(userId));
    }

    @RequestMapping(value = "/current", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public CommonUser findCurrentUser(@RequestParam(required = false) Long shopId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(commonUser, shopId);
        if (!ObjectUtils.isEmpty(commonUser)) {
            checkDistributedStatus(commonUser);
        }
        return commonUser;
    }

    @RequestMapping(value = "/mobile", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public APIResp<String> userCurrentMobile() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (null == commonUser) {
            return APIResp.notLogin();
        }
        return APIResp.ok(userReadService.findById(commonUser.getId()).getResult().getMobile());
    }

    private void doSendSmsForLogin(String mobile, String code) {
        log.debug("sending code={} to mobile={} for login ...", code, mobile);
        doSendSms(mobile, "登录验证", code);
    }

    private void doSendSmsForRegister(String mobile, String code) {
        log.debug("sending code={} to mobile={} for registering ...", code, mobile);
        doSendSms(mobile, "注册验证", code);
    }

    private void doSendSmsForChangeMobile(String mobile, String code) {
        log.debug("sending code={} to mobile={} for changing mobile ...", code, mobile);
        doSendSms(mobile, "手机号验证", code);
    }

    private void doSendSmsForForgetPassword(String mobile, String code) {
        log.debug("sending code={} to mobile={} for changing password by mobile", code, mobile);
        doSendSms(mobile, "身份验证", code);
    }

    public void doSendSms(String mobile, String template, String code) {
        // TODO: 19-3-25 临时修复,将国际号码判断交给短信发送模块
        if (mobile.startsWith(CountryCode.China.getCode())) {
            mobile = mobile.substring(CountryCode.PREFIX_CODE_LEN);
        } else {
            template = template.replaceAll("^sms", "sms.foreign");
        }
        String receivers = mobile;
        EventSender.sendApplicationEvent(new MsgSendRequestEvent(receivers, template, ImmutableMap.of("code", code)));
        log.info("sendSms mobile={}, message={}", mobile, code);
    }

    private void failIfCannotResend(String code) {
        if (!Strings.isNullOrEmpty(code)) {
            List<String> parts = Splitters.AT.splitToList(code);
            long sendTime = Long.parseLong(parts.get(1));
            if (System.currentTimeMillis() - sendTime < TimeUnit.MINUTES.toMillis(1)) { //
                log.error("could not send sms, sms only can be sent once in one minute");
                throw new JsonResponseException(500, "1分钟内只能获取一次验证码");
            }
        }
    }
}
