package moonstone.web.distribution.user.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.ParanaUserProfile;

/**
 * Created by CaiZhy on 2018/12/21.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributionParanaUserProfile extends ParanaUserProfile {
    private static final long serialVersionUID = -8254120677276903841L;
    /**
     * 微分销店铺id
     */
    private Long weShopId;

    /**
     * 微分销店铺是否实名认证
     */
    private String weShopRealName;
}
