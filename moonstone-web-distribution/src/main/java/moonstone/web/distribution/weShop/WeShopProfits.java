package moonstone.web.distribution.weShop;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.OrderGroup;
import moonstone.weShop.enums.WeShopProfitAvailableFlag;
import moonstone.weShop.enums.WeShopProfitType;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopProfit;
import moonstone.weShop.service.WeShopProfitReadService;
import moonstone.weShop.service.WeShopReadService;
import moonstone.web.core.order.OrderReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author:  CaiZhy
 * Date:    2018/12/26
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping("/api/weShopProfit")
public class WeShopProfits {
    @RpcConsumer
    private WeShopProfitReadService weShopProfitReadService;

    @RpcConsumer
    private WeShopReadService weShopReadService;

    @Autowired
    private OrderReadLogic orderReadLogic;

    @RequestMapping(value = "/profitToBeCollected/pagingOrder", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<OrderGroup> pagingProfitToBeCollectedOrder(@RequestParam(required = false) Long shopId,
                                                             @RequestParam(required = false) Integer pageNo,
                                                             @RequestParam(required = false) Integer pageSize){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long weShopId = getWeShopId(commonUser);
            Response<Paging<WeShopProfit>> rWeShopProfitPaging = weShopProfitReadService.pagingGroupByShopOrderId(WeShopProfitType.ORDER_PROFIT.getValue(),
                    weShopId, shopId, WeShopProfitAvailableFlag.UNAVAILABLE.getValue(), "id", 2, pageNo, pageSize);
            if (!rWeShopProfitPaging.isSuccess()) {
                log.error("failed to paging weShop profits, by type={}, weShopId={}, shopId={}, availableFlag={}, " +
                        "sortBy={}, sortType={}, pageNo={}, pageSize={}, error code: {}", WeShopProfitType.ORDER_PROFIT.getValue(),
                        weShopId, shopId, WeShopProfitAvailableFlag.UNAVAILABLE.getValue(), "id", 2, pageNo, pageSize, rWeShopProfitPaging.getError());
                throw new JsonResponseException(rWeShopProfitPaging.getError());
            }
            Paging<WeShopProfit> weShopProfitPaging = rWeShopProfitPaging.getResult();
            Response<Paging<OrderGroup>> response = orderReadLogic.convertProfitToOrderGroup(weShopProfitPaging);
            if (!response.isSuccess()){
                log.error("failed to convert profit to group order by weShop profit paging({}), error code: {}",
                        weShopProfitPaging, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("failed to paging profit to be collected with order, pageNo={}, pageSize={}, error={}",
                    pageNo, pageSize, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(e.getMessage());
        }
    }

    private Long getWeShopId(CommonUser commonUser){
        Long weShopId = commonUser.getWeShopId();
        if (weShopId == null){
            Response<WeShop> weShopResponse = weShopReadService.findByUserId(commonUser.getId());
            if (!weShopResponse.isSuccess()){
                log.error("failed to find weShop by userId={}, error code: {}", commonUser.getId(), weShopResponse.getError());
                throw new JsonResponseException(weShopResponse.getError());
            }
            WeShop weShop = weShopResponse.getResult();
            if (ObjectUtils.isEmpty(weShop)) {
                log.error("can not find weShop bu userId={}", commonUser.getId());
                throw new JsonResponseException("weShop.can.not.find");
            }
            return weShop.getId();
        } else {
            return weShopId;
        }
    }
}
