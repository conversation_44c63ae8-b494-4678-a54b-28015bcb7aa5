package moonstone.web.distribution.weShop;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.UserOpenIdUtil;
import moonstone.common.utils.UserUtil;
import moonstone.weShop.dto.WeWithdrawApplicationSubmit;
import moonstone.weShop.dto.WeWithdrawDetailForList;
import moonstone.weShop.enums.WeWithdrawDetailType;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.model.WeWithdrawDetail;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.weShop.service.WeWithdrawDetailReadService;
import moonstone.web.core.AppConstants;
import moonstone.web.core.component.distribution.WeWithdrawDetailWriteLogic;
import moonstone.web.core.component.distribution.WithDrawTaxReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;

/**
 * Author:  CaiZhy
 * Date:    2019/1/9
 */
@Slf4j
@RestController
@RequestMapping("/api/weWithdrawDetail")
public class WeWithdrawDetails {
    @Autowired
    private WeWithdrawDetailWriteLogic weWithdrawDetailWriteLogic;
    @RpcConsumer
    private WeWithdrawDetailReadService weWithdrawDetailReadService;
    @RpcConsumer
    private WeShopReadService weShopReadService;
    @RpcConsumer
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @Autowired
    WithDrawTaxReadService withDrawTaxReadService;

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody WeWithdrawApplicationSubmit weWithdrawApplicationSubmit, HttpServletRequest request){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            //检查微分销店铺及其账户是否被冻结
            Response<WeShop> rWeShop = weShopReadService.findById(commonUser.getWeShopId());
            if (!rWeShop.isSuccess()) {
                log.error("failed to find weShop by id={}, error code: {}", commonUser.getWeShopId(), rWeShop.getError());
                throw new JsonResponseException(rWeShop.getError());
            }
            WeShop weShop = rWeShop.getResult();
            if (weShop.getStatus() <= 0) {
                log.error("weShop(id={}) status is abnormal:{}", weShop.getId(), weShop.getStatus());
                throw new InvalidException("weShop({0}).status.abnormal", weShop.getId());
            }
            Response<WeShopShopAccount> rWeShopShopAccount =
                    weShopShopAccountReadService.findByWeShopIdAndShopId(weShop.getId(), weWithdrawApplicationSubmit.getShopId());
            if (!rWeShopShopAccount.isSuccess()) {
                log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}",
                        weShop.getId(), weWithdrawApplicationSubmit.getShopId(), rWeShopShopAccount.getError());
                throw new JsonResponseException(rWeShopShopAccount.getError());
            }
            WeShopShopAccount weShopShopAccount = rWeShopShopAccount.getResult();
            if (weShopShopAccount.getStatus() <= 0) {
                log.error("weShopShopAccount(id={}) status is abnormal:{}", weShopShopAccount.getId(), weShopShopAccount.getStatus());
                throw new InvalidException("weShopShopAccount({0}).status.abnormal", weShopShopAccount.getId());
            }
            //防止虚假数据
            weWithdrawApplicationSubmit.setUserId(commonUser.getId());
            weWithdrawApplicationSubmit.setWeShopId(commonUser.getWeShopId());
            switch (WeWithdrawDetailType.fromInt(weWithdrawApplicationSubmit.getType())){
                case WEIXIN: {
                    weWithdrawApplicationSubmit.setCommissionCharge(0L);
                    weWithdrawApplicationSubmit.setOpenId(UserOpenIdUtil.getOpenId());
                    weWithdrawApplicationSubmit.setClientIp(request.getRemoteAddr());
                    break;
                }
                case BANK:
                default: {
                    throw new JsonResponseException("weWithdrawDetail.create.type.not.supportive");
                }
            }
            Response<Long> response = weWithdrawDetailWriteLogic.create(weWithdrawApplicationSubmit,withDrawTaxReadService.getTaxBy(weWithdrawApplicationSubmit.getShopId(),weWithdrawApplicationSubmit.getWeShopId()).getTaxRate());
            if (!response.isSuccess()) {
                log.error("failed to create weWithdrawApplicationSubmit({}), error code: {}", weWithdrawApplicationSubmit);
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("fail to create weWithdrawApplicationSubmit({}), cause: {}", weWithdrawApplicationSubmit, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/pagingForDistributor", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<WeWithdrawDetailForList> pagingForDistributor(@RequestParam(required = false) Long shopId,
                                                                @RequestParam(required = false) Integer type,
                                                                @RequestParam(required = false) Integer status,
                                                                @RequestParam(required = false) String statuses,
                                                                @RequestParam(required = false) Integer pageNo,
                                                                @RequestParam(required = false) Integer pageSize) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        try {
            List<Long> userIds = new ArrayList<>();
            userIds.add(commonUser.getId());
            List<Long> weShopIds = new ArrayList<>();
            weShopIds.add(commonUser.getWeShopId());
            Response<Paging<WeWithdrawDetail>> rWeWithdrawDetailPaging = weWithdrawDetailReadService.paging(
                    null, userIds, weShopIds, shopId, type, null, status, statuses,
                    null, null, null, null, pageNo, pageSize);
            if (!rWeWithdrawDetailPaging.isSuccess()) {
                log.error("failed to paging weWithdrawDetail by userId={}, weShopId={, shopId={}, type={}, status={}, statuses={}, " +
                        "pageNo={}, pageSize={}, error code: {}", commonUser.getId(), commonUser.getWeShopId(), shopId, type, status, statuses,
                        pageNo, pageSize, rWeWithdrawDetailPaging.getError());
                throw new JsonResponseException(rWeWithdrawDetailPaging.getError());
            }
            Paging<WeWithdrawDetail> weWithdrawDetailPaging = rWeWithdrawDetailPaging.getResult();
            List<WeWithdrawDetailForList> data = CopyUtil.copyList(weWithdrawDetailPaging.getData(), WeWithdrawDetailForList.class);
            return new Paging<>(weWithdrawDetailPaging.getTotal(), data);
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("fail to paging weWithdrawDetails for distributor " +
                            "by weShopId={}, shopId={}, type={}, status={}, statuses={}, pageNo={}, pageSize={}, cause: {}",
                    commonUser.getWeShopId(), shopId, type, status, statuses, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping("/setOpenId")
    public Boolean setOpenId(@RequestParam String openId,
                             HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        session.setAttribute(AppConstants.SESSION_OPEN_ID, openId);
        return Boolean.TRUE;
    }
    @RequestMapping("/getTaxRate")
    public Long getTaxRate()
    {
      CommonUser user= UserUtil.getCurrentUser();
      return withDrawTaxReadService.getTaxBy(user.getShopId(),user.getWeShopId()).getTaxRate();
    }
}
