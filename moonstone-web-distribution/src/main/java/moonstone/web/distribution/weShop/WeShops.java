package moonstone.web.distribution.weShop;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.model.BalanceDetail;
import moonstone.user.area.model.dto.AreaInfoView;
import moonstone.user.enums.AreaType;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.service.AreaInfoReadService;
import moonstone.user.service.AreaInfoWriteService;
import moonstone.weShop.dto.WeShopApply;
import moonstone.weShop.dto.WeShopView;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.weShop.service.WeShopWriteService;
import moonstone.web.core.component.RecordManager;
import moonstone.web.core.component.item.model.view.SettleOrderProfitRecordView;
import moonstone.web.core.component.item.model.view.SettleProfitLayoutView;
import moonstone.web.core.component.item.model.view.UnSettleOrderProfitRecordView;
import moonstone.web.core.component.profit.OrderProfitRecordManager;
import moonstone.web.core.component.profit.dto.OrderProfitRecord;
import moonstone.web.core.component.profit.dto.OrderProfitViewCriteria;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.core.events.shop.WeShopUpdateEvent;
import moonstone.web.core.mirror.app.RemoteApiOfGongXiao;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.model.Record;
import moonstone.web.core.model.dto.WeShopIndexStatisticalData;
import moonstone.web.core.model.dto.WeShopMemberView;
import moonstone.web.core.model.dto.record.UserRegisterCount;
import moonstone.web.core.shop.application.DefaultWeShopSupplier;
import moonstone.web.distribution.component.weShop.WeShopReadLogic;
import moonstone.web.distribution.component.weShop.WeShopWriteLogic;
import moonstone.web.distribution.order.OrderProfitRecordViewPacker;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by CaiZhy on 2018/12/21.
 */
@Slf4j
@RestController
@RequestMapping("/api/weShop")
@AllArgsConstructor
public class WeShops {
    private final WeShopReadService weShopReadService;
    private final WeShopWriteService weShopWriteService;
    private final WeShopShopAccountReadService weShopShopAccountReadService;
    private final WeShopReadLogic weShopReadLogic;
    private final RecordManager recordManager;
    private final AreaInfoReadService areaInfoReadService;
    private final AreaInfoWriteService areaInfoWriteService;
    private final WeShopWriteLogic weShopWriteLogic;
    private final OrderProfitRecordManager orderProfitRecordManager;
    private final OrderProfitRecordViewPacker orderProfitRecordViewPacker;
    private final BalanceDetailManager balanceDetailManager;
    private final WeShopCacheHolder weShopCacheHolder;
    private final DefaultWeShopSupplier defaultWeShopSupplier;
    private final UserSubShopPackComponent userSubShopPackComponent;
    private final SourceShopQuerySlice sourceShopQuerySlice;
    private final RemoteApiOfGongXiao remoteApiOfGongXiao;
    private final UserTypeBean userTypeBean;

    /**
     * take the default weShopId for weShop outFrom
     *
     * @param shopId shopId (for special shop), weShop is over the shop commonly but some shop is not
     * @return default weShopId
     */
    @GetMapping("/default")
    public APIResp<Long> defaultWeShopId(@RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(defaultWeShopSupplier.getDefaultWeShop(shopId));
    }

    /**
     * set the default the weShopId for weShop outFrom
     *
     * @param shopId   for special shop
     * @param weShopId default weShopId
     * @return update success or error
     */
    @PostMapping("/default")
    public APIResp<Boolean> defaultWeShopId(@RequestParam(required = false) Long shopId, Long weShopId) {
        BaseUser operator = UserUtil.getCurrentUser();
        if (Objects.isNull(operator)) {
            return APIResp.notLogin();
        }
        if (Objects.nonNull(shopId) && shopId != 0L) {
            CommonUser userWithShopInfo = (CommonUser) operator;
            if (!Objects.equals(userWithShopInfo.getShopId(), shopId) && !userTypeBean.isAdmin(operator)) {
                return APIResp.error(Translate.of("店铺不属于你"));
            }
        } else {
            if (!userTypeBean.isAdmin(operator)) {
                return APIResp.error(Translate.of("请使用管理员帐号登录操作"));
            }
        }
        return APIRespWrapper.wrap(defaultWeShopSupplier.setDefaultWeShop(shopId, weShopId));
    }

    @DeleteMapping("/cancel")
    public APIResp<Boolean> cancelApply(Long shopId) {
        BaseUser user = UserUtil.getCurrentUser();
        if (Objects.isNull(user)) {
            return APIResp.notLogin();
        }
        Optional<WeShop> weShopOpt = weShopCacheHolder.findByUserIdAndShopId(user.getId(), shopId);
        if (!weShopOpt.isPresent()) {
            return APIResp.error(Translate.of("不存在可以取消的门店申请"));
        }
        WeShop weShop = weShopOpt.get();
        if (Objects.equals(1, weShop.getStatus())) {
            return APIResp.error(Translate.of("申请审核已经通过, 请联系小二注销门店"));
        }
        return APIRespWrapper.wrap(weShopWriteService.cancelWeShop(weShop));
    }

    /**
     * 判断名字是否重名
     *
     * @param name 店铺名字
     * @return 没有重名
     */
    @GetMapping("/check-name")
    public APIResp<Boolean> checkName(String name) {
        return APIResp.ok(weShopReadService.findLikeName(name)
                .getResult().stream().noneMatch(weShop -> weShop.getName().equals(name)));
    }

    /**
     * 注册与修改原微店数据
     *
     * @param weShopApply 微店申请
     * @return 注册结果
     */
    @PostMapping("/register")
    public APIResp<Boolean> register(@RequestBody WeShopApply weShopApply) {
        BaseUser applier = UserUtil.getCurrentUser();
        if (Objects.isNull(applier)) {
            return APIResp.notLogin();
        }
        try {
            return APIRespWrapper.wrap(weShopWriteLogic.signInOrUpdate(weShopApply, applier));
        } catch (Exception exception) {
            log.error("{} fail to register [{}] for user[{}]"
                    , LogUtil.getClassMethodName(), applier, applier.getId(), exception);
            return APIResp.error(Translate.of("注册失败"));
        }
    }

    @GetMapping("/member")
    public APIResp<WeShopMemberView> queryWeShopMember(@RequestParam(required = false) Long shopId) {
        CommonUser weShopUser = UserUtil.getCurrentUser();
        if (Objects.isNull(weShopUser)) {
            return APIResp.notLogin();
        }
        userSubShopPackComponent.wrap(weShopUser, shopId);
        return APIResp.ok(weShopReadLogic.getMember(weShopUser.getWeShopId()));
    }

    @GetMapping("/member-test")
    public APIResp<Long> countForWeShopMember(Long weShopId, Long day) {
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId).orElseThrow(() -> Translate.exceptionOf("微店查找失败"));
        return APIResp.ok(recordManager.findRecord(weShop.getUserId(), UserRegisterCount.build(weShopId, OrderOutFrom.WE_SHOP), day).stream()
                .map(Record::getNum).filter(Objects::nonNull).findFirst().orElse(0L));
    }

    /**
     * @param page 页面
     * @return 获取待收益利润列表
     */
    @GetMapping("/profit/order/unsettle")
    public Response<List<UnSettleOrderProfitRecordView>> getWaitSettleProfit(@RequestParam(defaultValue = "1") int page, @RequestParam(required = false) Long shopId) {
        CommonUser weShopUser = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(weShopUser, shopId);
        Optional<WeShop> weShop = weShopCacheHolder.findByWeShopId(weShopUser.getWeShopId());
        if (!weShop.isPresent()) {
            return Response.fail(new Translate("user.not.login").toString());
        }
        page = Math.max(1, page);
        OrderProfitViewCriteria criteria = OrderProfitViewCriteria.builder().userId(weShop.get().getUserId())
                .shopId(shopId).outFrom(OrderOutFrom.WE_SHOP).settled(false)
                .pageNo(page).pageSize(50).build();
        List<OrderProfitRecord> profitRecordList = orderProfitRecordManager.findProfit(criteria).getData();
        return Response.ok(orderProfitRecordViewPacker.packView(profitRecordList));
    }

    /**
     * @param page 页面
     * @return 获取已收益利润列表
     */
    @GetMapping("/profit/order/settle")
    public Response<SettleProfitLayoutView> getSettleProfit(@RequestParam(defaultValue = "1") int page, @RequestParam(required = false) Long shopId) {
        CommonUser weShopUser = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(weShopUser, shopId);
        Optional<WeShop> weShop = weShopCacheHolder.findByWeShopId(weShopUser.getWeShopId());
        if (!weShop.isPresent()) {
            return Response.fail(new Translate("user.not.login").toString());
        }
        page = Math.min(1, page);
        OrderProfitViewCriteria criteria = OrderProfitViewCriteria.builder().userId(weShop.get().getUserId())
                .shopId(shopId).outFrom(OrderOutFrom.WE_SHOP).settled(true)
                .pageNo(page).pageSize(50).build();
        List<OrderProfitRecord> profitRecordList = orderProfitRecordManager.findProfit(criteria).getData();
        List<SettleOrderProfitRecordView> viewList = profitRecordList.stream().map(record -> {
            SettleOrderProfitRecordView view = new SettleOrderProfitRecordView();
            view.setOrderName(record.getOrderName());
            view.setOrderId(record.getOrderId());
            view.setOrderAt(record.getOrderAt());
            view.setProfit(record.getProfit());
            return view;
        }).collect(Collectors.toList());
        SettleProfitLayoutView view = new SettleProfitLayoutView();
        view.setProfitList(viewList);
        view.setTotalProfit(balanceDetailManager.getEarned(weShopUser.getId(), shopId).map(BalanceDetail::getFee).orElse(0L));
        return Response.ok(view);
    }

    @RequestMapping(value = "/statisticalIndexData", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WeShopIndexStatisticalData statisticalIndexData(@RequestParam(required = false) Long shopId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (java.util.Objects.isNull(commonUser)) {
            throw new JsonResponseException(Translate.of("请登录"));
        }
        try {
            userSubShopPackComponent.wrap(commonUser, shopId);
            Response<WeShopIndexStatisticalData> response = weShopReadLogic.countIndexData(commonUser, shopId);
            if (!response.isSuccess()) {
                log.error("failed to count index data by user(id={}), error code: {}", commonUser.getId(), response.getError());
                return new WeShopIndexStatisticalData();
            }
            return response.getResult();
        } catch (Exception e) {
            log.warn("failed to statistical index data for user(id={})", commonUser.getId(), e);
            throw e;
        }
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WeShopView findById(@PathVariable Long id) {
        Response<WeShop> weShopResponse = weShopReadService.findById(id);
        if (!weShopResponse.isSuccess()) {
            log.error("failed to find weShop by id={}, error code: {}", id, weShopResponse.getError());
            throw new JsonResponseException(weShopResponse.getError());
        }
        AreaInfoView areaInfo = areaInfoReadService.findAreaInfoByMainIdAndType(id, AreaType.WeShop.getValue()).orElseGet(AreaInfoView::none);
        return WeShopView.build(weShopResponse.getResult(), areaInfo);
    }

    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateWeShop(@RequestBody WeShopApply weShop) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Optional<WeShop> existsWeShop = weShop.getShopId() == null ? weShopCacheHolder.findByUserId(commonUser.getId())
                : weShopCacheHolder.findByUserIdAndShopId(commonUser.getId(), weShop.getShopId());
        if (!existsWeShop.isPresent()) {
            return false;
        }
        EventSender.publish(new WeShopUpdateEvent(existsWeShop.get().getId()));
        WeShop toUpdate = new WeShop();
        toUpdate.setId(existsWeShop.get().getId());
        toUpdate.setName(weShop.getName());
        toUpdate.setDescription(weShop.getDescription());
        toUpdate.setLogoUrl(weShop.getLogoUrl());
        toUpdate.setBackgroundUrl(weShop.getBackgroundUrl());
        Response<Boolean> response = weShopWriteService.update(toUpdate);
        if (!response.isSuccess()) {
            log.error("failed to update weShop({}), error code: {}", toUpdate, response.getError());
            throw new JsonResponseException(response.getError());
        }
        if (StringUtils.hasLength(weShopReadService.findById(existsWeShop.get().getId()).getResult().getOutShopCode())) {
            if (StringUtils.hasLength(weShop.getName()) && sourceShopQuerySlice.queryProjectIdByShopIdAndSource(weShop.getShopId(), MirrorSource.GongXiao.name()).isSuccess()) {
                remoteApiOfGongXiao.updateShopName(existsWeShop.get().getOutShopCode(), weShop.getName()).take();
            }
        }
        areaInfoWriteService.saveFromBean(existsWeShop.get().getId(), AreaType.WeShop.getValue(), weShop);
        return response.getResult();
    }

    @RequestMapping(value = "/listDistributedShopId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Long> listDistributedShopId() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new JsonResponseException("user.not.login");
        }

        Long weShopId = commonUser.getWeShopId();
        if (weShopId == null) {
            Response<WeShop> weShopResponse = weShopReadService.findByUserId(commonUser.getId());
            if (!weShopResponse.isSuccess()) {
                log.error("failed to find weShop by userId={}, error code: {}", commonUser.getId(), weShopResponse.getError());
                throw new JsonResponseException(weShopResponse.getError());
            }
            WeShop weShop = weShopResponse.getResult();
            if (ObjectUtils.isEmpty(weShop)) {
                log.error("can not find weShop bu userId={}", commonUser.getId());
                throw new JsonResponseException("weShop.can.not.find");
            }
            weShopId = weShop.getId();
        }

        Response<List<WeShopShopAccount>> rWeShopShopAccount = weShopShopAccountReadService.findByWeShopId(weShopId);
        if (!rWeShopShopAccount.isSuccess()) {
            log.error("failed to find weShopShopAccounts by weShopId={}, error code: {}", weShopId, rWeShopShopAccount.getError());
            throw new JsonResponseException(rWeShopShopAccount.getError());
        }
        List<Long> result = new ArrayList<>();
        for (WeShopShopAccount weShopShopAccount : rWeShopShopAccount.getResult()) {
            result.add(weShopShopAccount.getShopId());
        }
        return result;
    }

    @RequestMapping(value = "/getCertification", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> getCertification() {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }
        Response<WeShop> rWeShop = weShopReadService.findByUserId(userId);
        if (!rWeShop.isSuccess()) {
            log.error("failed to find weShop by userId={}, error code: {}", userId, rWeShop.getError());
            throw new JsonResponseException(rWeShop.getError());
        }
        WeShop weShop = rWeShop.getResult();
        if (ObjectUtils.isEmpty(weShop.getRealName())) {
            throw new JsonResponseException("weShop.certification.not.find");
        }
        String realName = weShop.getRealName();

        Map<String, String> result = new HashMap<>();
        result.put(DistributionConstants.WE_SHOP_REAL_NAME, realName);
        return result;
    }

    @RequestMapping(value = "/certificate", method = RequestMethod.POST)
    public void certificate(@RequestParam String realName) {

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }

        Response<WeShop> rWeShop = weShopReadService.findByUserId(userId);
        if (!rWeShop.isSuccess()) {
            log.error("failed to find weShop by userId={}, error code: {}", userId, rWeShop.getError());
            throw new JsonResponseException(rWeShop.getError());
        }
        WeShop weShop = rWeShop.getResult();
        WeShop toUpdate = new WeShop();
        toUpdate.setId(weShop.getId());
        toUpdate.setRealName(realName);
        Response<Boolean> resp = weShopWriteService.update(toUpdate);
        if (!resp.isSuccess()) {
            log.error("failed to update weShop({}), error code: {}", weShop, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
    }
}
