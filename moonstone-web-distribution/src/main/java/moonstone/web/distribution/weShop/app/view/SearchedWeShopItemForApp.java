package moonstone.web.distribution.weShop.app.view;

import lombok.Data;
import moonstone.search.dto.SearchedWeShopItem;
import org.springframework.beans.BeanUtils;

@Data
public class SearchedWeShopItemForApp {
    Long id;
    Long itemId;
    String name;
    Long price;
    Long tax;
    Long profit;
    Integer isBonded;
    String origin;
    String originUrl;

    public static SearchedWeShopItemForApp from(SearchedWeShopItem searchedWeShopItem) {
        SearchedWeShopItemForApp view = new SearchedWeShopItemForApp();
        BeanUtils.copyProperties(searchedWeShopItem, view);
        return view;
    }
}
