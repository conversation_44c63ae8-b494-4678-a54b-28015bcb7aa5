package moonstone.web.distribution.weShop;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.ShareCodeUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.weShop.dto.WeShopInvitationRecord;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopShopAccountReadService;
import moonstone.weShop.service.WeShopShopAccountWriteService;
import moonstone.web.distribution.component.weShop.WeShopReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author:  CaiZhy
 * Date:    2018/12/29
 */
@Slf4j
@RestController
@RequestMapping("/api/weShop/invitation")
public class WeShopInvitations {
    @Autowired
    private WeShopReadLogic weShopReadLogic;
    @RpcConsumer
    private WeShopShopAccountReadService weShopShopAccountReadService;
    @RpcConsumer
    private WeShopShopAccountWriteService weShopShopAccountWriteService;
    @RpcConsumer
    private ShopReadService shopReadService;

    @RequestMapping(value = "/inviteCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getInviteCode(@RequestParam Long shopId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<Shop> rShop = shopReadService.findById(commonUser.getShopId());
        if (!rShop.isSuccess()) {
            log.error("shop find error {}", rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        if (!(rShop.getResult().getExtra()).getOrDefault("inviteCodeActive", "true").equals("true")) {
            return new Translate("inviteCode.active.false").toString();
        }
        try {
            Response<WeShopShopAccount> weShopShopAccountResponse = weShopShopAccountReadService.findByWeShopIdAndShopId(commonUser.getWeShopId(), shopId);
            if (!weShopShopAccountResponse.isSuccess()) {
                log.error("failed to find weShopShopAccount by weShopId={}, shopId={}, error code: {}",
                        commonUser.getWeShopId(), shopId, weShopShopAccountResponse.getError());
                throw new JsonResponseException(weShopShopAccountResponse.getError());
            }
            WeShopShopAccount weShopShopAccount = weShopShopAccountResponse.getResult();
            if (ObjectUtils.isEmpty(weShopShopAccount)) {
                throw new JsonResponseException("weShopShopAccount.not.exist");
            }
            String inviteCode = weShopShopAccount.getInviteCode();
            if (ObjectUtils.isEmpty(inviteCode)) {
                //若还没有邀请码，则创建
                inviteCode = ShareCodeUtil.weShopPrefix + ShareCodeUtil.toSerialCode(weShopShopAccount.getId());
                WeShopShopAccount toUpdate = new WeShopShopAccount();
                toUpdate.setId(weShopShopAccount.getId());
                toUpdate.setInviteCode(inviteCode);
                Response<Boolean> response = weShopShopAccountWriteService.update(toUpdate);
                if (!response.isSuccess()) {
                    log.error("failed to update weShopShopAccount by id={}, inviteCode={}, error code: {}",
                            toUpdate.getId(), toUpdate.getInviteCode(), response.getError());
                    throw new JsonResponseException(response.getError());
                }
            }

            return inviteCode;
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to get weShopShopAccount invite code by weShopId={}, shopId={}, cause: {}",
                    commonUser.getWeShopId(), shopId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("weShopShopAccount.invite.code.get.fail");
        }
    }

    @RequestMapping(value = "/pagingRecord", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<WeShopInvitationRecord> pagingRecord(@RequestParam(required = false) Long shopId,
                                                       @RequestParam(required = false) Integer pageNo,
                                                       @RequestParam(required = false) Integer pageSize) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Response<Paging<WeShopInvitationRecord>> pagingResponse =
                    weShopReadLogic.pagingRecord(commonUser.getWeShopId(), commonUser.getId(), shopId, pageNo, pageSize);
            if (!pagingResponse.isSuccess()) {
                log.error("failed to paging records by currentWeShopId={}, currentUserId={}, shopId={}, pageNo={}, pageSize={}, error code: {}",
                        commonUser.getWeShopId(), commonUser.getId(), shopId, pageNo, pageSize, pagingResponse.getError());
                throw new JsonResponseException(pagingResponse.getError());
            }
            return pagingResponse.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("failed to paging invitation records by shopId={}, pageNo={}, pageSize={}, error={}",
                    shopId, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(e.getMessage());
        }
    }
}
