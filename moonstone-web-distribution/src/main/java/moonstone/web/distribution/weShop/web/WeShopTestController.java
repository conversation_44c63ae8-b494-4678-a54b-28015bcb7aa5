package moonstone.web.distribution.weShop.web;

import moonstone.web.core.mirror.model.api.MirrorUserPersistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Profile("dev")
@RequestMapping("/api/test")
public class WeShopTestController {
    @Autowired
    MirrorUserPersistService mirrorUserPersistService;

    @GetMapping
    public void test(String userCode, Long userId) {
        mirrorUserPersistService.syncShop(userCode, userId, "GongXiao");
    }
}
