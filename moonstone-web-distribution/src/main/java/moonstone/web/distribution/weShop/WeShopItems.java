package moonstone.web.distribution.weShop;

import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopItemCacher;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.PagingCriteria;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.weShop.dto.RichWeShopItem;
import moonstone.weShop.dto.SearchItemForWeDistributor;
import moonstone.weShop.dto.WeShopViewedItem;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.web.core.component.WeShopItemWriteLogic;
import moonstone.web.core.events.item.DistributionPriceSetEvent;
import moonstone.web.core.shop.application.WeShopMemberRegisterApp;
import moonstone.web.distribution.component.item.WeShopItemReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by CaiZhy on 2018/12/24.
 */
@Slf4j
@RestController
@RequestMapping("/api/weShopItem")
public class WeShopItems {
    @Autowired
    private WeShopItemCacher weShopItemCacher;

    @Autowired
    private WeShopItemReadLogic weShopItemReadLogic;

    @Autowired
    private WeShopMemberRegisterApp weShopMemberRegisterApp;
    @Autowired
    private WeShopItemWriteLogic weShopItemWriteLogic;
    @Autowired
    private WeShopItemReadService weShopItemReadService;
    @Autowired
    private WeShopItemWriteService weShopItemWriteService;
    @Autowired
    private SkuReadService skuReadService;

    @RequestMapping(value = "/{weShopItemId}/for-view", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public WeShopViewedItem findForView(@PathVariable Long weShopItemId) {
        if (UserUtil.getCurrentUser() != null) {
            CommonUser user = UserUtil.getCurrentUser();
            WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemId);
            if (!Objects.equals(user.getWeShopId(), weShopItem.getWeShopId())) {
                weShopMemberRegisterApp.registerMemberForWeShop(weShopItem.getWeShopId(), user);
            }
        }
        return weShopItemCacher.findForView(weShopItemId);
    }

    @RequestMapping(value = "/onSale", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean onSale(long itemId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        try {
            Response<Boolean> response = weShopItemReadLogic.onSaleItemInWeShop(commonUser, itemId);
            if (!response.isSuccess() || !response.getResult()) {
                log.error("failed to on sale item(id={}) in weShop(userId={}), error code: {}", itemId, commonUser.getId());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("fail to on sale item(id={}) in weShop(userId={}), cause: {}", itemId, commonUser.getId(), Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("weShopItem.on.sale.fail");
        }
    }

    @RequestMapping(value = "/offSale", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean offSale(long weShopItemId)
    {
        CommonUser commonUser = UserUtil.getCurrentUser();
        try {
            Response<Boolean> response = weShopItemReadLogic.offSaleItemInWeShop(commonUser, weShopItemId);
            if (!response.isSuccess() || !response.getResult()) {
                log.error("failed to off sale weShop item(id={}) in weShop(userId={}), error code: {}", weShopItemId, commonUser.getId());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("fail to off sale weShop item(id={}) in weShop(userId={}), cause: {}", weShopItemId, commonUser.getId(), Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("weShopItem.of.sale.fail");
        }
    }

    @RequestMapping(value = "/setIndexDisplay", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean setIndexDisplay(long weShopItemId, boolean indexDisplay)
    {
        CommonUser commonUser = UserUtil.getCurrentUser();
        try {
            Response<Boolean> response = weShopItemReadLogic.setItemIndexDisplayInWeShop(commonUser, weShopItemId, indexDisplay);
            if (!response.isSuccess() || !response.getResult()) {
                log.error("failed to set weShopItem(id={}) index display({}) for user(id={}). error code: {}",
                        weShopItemId, indexDisplay, commonUser.getId(), response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e) {{
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("fail to set weShopItem(id={}) index display({}) for user(id={}), cause: {}",
                    weShopItemId, indexDisplay, commonUser.getId(), Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("weShopItem.set.index.display.fail");
        }
        }
    }

    @RequestMapping(value = "/pagingOnSellItem", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<SearchItemForWeDistributor> pagingOnSellItem(PagingCriteria pagingCriteria)
    {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<Paging<SearchItemForWeDistributor>> response = weShopItemReadLogic.getPagingOfOnSaleWeShopItemByUserId(commonUser.getId(),pagingCriteria);
        if (!response.isSuccess()) {
            log.error("failed to paging of on sale weShop items by userId={}, error code: {}", commonUser.getId(), response.getError());
            throw new JsonResponseException(response.getError());
        }
        return response.getResult();
    }

    @RequestMapping(value = "/indexDisplay/list", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<RichWeShopItem> indexDisplayList(@RequestParam(required = true)Long shopId){
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<List<RichWeShopItem>> response = weShopItemReadLogic.listIndexDisplay(commonUser.getWeShopId(),shopId);
        if (!response.isSuccess()) {
            log.error("failed to list index display weShop items by weShopId={}, error code: {}", commonUser.getWeShopId(), response.getError());
            throw new JsonResponseException(response.getError());
        }
        return response.getResult();
    }

    @RequestMapping(value = "/sort", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean sort(@RequestBody List<Long> ids){
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<Boolean> response = weShopItemWriteLogic.sort(ids, commonUser.getWeShopId());
        if (!response.isSuccess()){
            log.error("failed to sort weShopItems by ids={}, error code: {}", ids, response.getError());
            throw new JsonResponseException(response.getError());
        }
        return response.getResult();
    }
    // add by liuchao 20190327
    @RequestMapping(value = "/weShopOwerPrice", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean>  findForView(@RequestParam(required = true) Long itemId,
                                          @RequestParam(required = true) String price,
                                          @RequestParam(required = true)Long weShopId) {
        log.debug("findForView.weShopOwerPrice"+itemId+price+weShopId);
        Response<WeShopItem> rWeShopItem= weShopItemReadService.findByWeShopIdAndItemId(weShopId,itemId);
        Response<List<Sku>> rSkus=skuReadService.findSkusByItemId(rWeShopItem.getResult().getItemId());
        Map<String,String> extra=new HashMap<>();
        extra.put(DistributionConstants.WE_SHOP_OWER_PRICE,price);
        if(rSkus.getResult().get(0).getExtraPrice().get(DistributionConstants.SUPPLY_PRICE)!=null) {
            extra.put(DistributionConstants.SUPPLY_PRICE, rSkus.getResult().get(0).getExtraPrice().get(DistributionConstants.SUPPLY_PRICE).toString());
        }
        WeShopItem weShopItem=rWeShopItem.getResult();
        weShopItem.setExtra(extra);
        Response<Boolean> response =weShopItemWriteService.update(weShopItem);
        EventSender.sendApplicationEvent(new DistributionPriceSetEvent(weShopItem.getItemId()));
        return response;
    }
}
