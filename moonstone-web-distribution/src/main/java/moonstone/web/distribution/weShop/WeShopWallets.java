package moonstone.web.distribution.weShop;

import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.CountryCode;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EncryptUtil;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopWallet;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopWalletReadService;
import moonstone.weShop.service.WeShopWalletWriteService;
import moonstone.web.core.AppConstants;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.distribution.constants.DistributionSessions;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by CaiZhy on 2018/12/21.
 */
@Slf4j
@RestController
@RequestMapping("/api/weShopWallet")
public class WeShopWallets {
    @RpcConsumer
    private WeShopReadService weShopReadService;

    @RpcConsumer
    private WeShopWalletReadService weShopWalletReadService;

    @RpcConsumer
    private WeShopWalletWriteService weShopWalletWriteService;

    @RpcConsumer
    private UserReadService<User> userReadService;

    @RequestMapping(value = "/change-password-by-old", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean changePasswordByOld(String oldPassword, String newPassword, HttpServletRequest request){
        try {
            HttpSession session = request.getSession();

            CommonUser commonUser = UserUtil.getCurrentUser();
            if (ObjectUtils.isEmpty(commonUser)) {
                throw new JsonResponseException(401, "user.not.login");
            }

            Boolean isChecked = (Boolean) session.getAttribute(DistributionSessions.CHECK_CODE_FOR_SET_PW);
            if (isChecked == null || !isChecked){
                log.error("user(id={}) want to change weShop wallet password without checked", commonUser.getId());
                throw new JsonResponseException("weShopWallet.set.password.not.checked");
            }

            if (!newPassword.matches("[\\s\\S]{6,16}")) {
                log.warn("password syntax error");
                throw new JsonResponseException(500,"weShopWallet.password.6to16");
            }
            log.debug("user(id={}) want to change weShop wallet password at {}", commonUser.getId(), new Date());
            Long weShopId = getWeShopId(commonUser);
            Response<WeShopWallet> weShopWalletResponse = weShopWalletReadService.findByWeShopId(weShopId);
            if (!weShopWalletResponse.isSuccess()){
                log.error("failed to find weShop wallet by weShopId={}, error code: {}", weShopId, weShopWalletResponse.getError());
                throw new JsonResponseException(weShopWalletResponse.getError());
            }
            WeShopWallet weShopWallet = weShopWalletResponse.getResult();
            if (weShopWallet == null){
                log.error("can not find weShop wallet by weShopId={}",weShopId);
                throw new JsonResponseException("weShopWallet.can.not.find");
            }
            if (!EncryptUtil.match(oldPassword, weShopWallet.getPassword())) {
                log.warn("weShop wallet old password{} isn't matched.", oldPassword);
                throw new JsonResponseException("weShopWallet.old.password.mismatch");
            }
            WeShopWallet toUpdate = new WeShopWallet();
            toUpdate.setId(weShopWallet.getId());
            toUpdate.setPassword(EncryptUtil.encrypt(newPassword));
            Response<Boolean> result = weShopWalletWriteService.update(toUpdate);
            if (result.isSuccess()) {
                session.removeAttribute(DistributionSessions.CHECK_CODE_FOR_SET_PW);
                return result.getResult();
            } else {
                log.warn("failed to change password for weShop wallet id={},error code:{}", weShopWallet.getId(), result.getError());
                throw new JsonResponseException(500, result.getError());
            }
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("change wallet password failed, oldPassword={}, newPassword={}, error={}",
                    oldPassword, newPassword, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/set-password", method = RequestMethod.POST)
    public Boolean setPassword(@RequestParam String password,
                               HttpServletRequest request) {
        HttpSession session = request.getSession();

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (ObjectUtils.isEmpty(commonUser)) {
            throw new JsonResponseException("user.not.login");
        }

        Boolean isChecked = (Boolean) session.getAttribute(DistributionSessions.CHECK_CODE_FOR_SET_PW);
        if (isChecked == null || !isChecked){
            log.error("user(id={}) want to set weShop wallet password without checked", commonUser.getId());
            throw new JsonResponseException("weShopWallet.set.password.not.checked");
        }

        Long weShopId = getWeShopId(commonUser);
        Response<WeShopWallet> rWeShopWallet = weShopWalletReadService.findByWeShopId(weShopId);
        if (!rWeShopWallet.isSuccess()) {
            log.error("failed to find weShop wallet by weShopId={}, error code: {}", weShopId, rWeShopWallet.getError());
            throw new JsonResponseException(rWeShopWallet.getError());
        }
        WeShopWallet weShopWallet = rWeShopWallet.getResult();
        if (ObjectUtils.isEmpty(weShopWallet)){
            WeShopWallet toCreate = new WeShopWallet();
            toCreate.setWeShopId(weShopId);
            toCreate.setUserId(commonUser.getId());
            toCreate.setPassword(EncryptUtil.encrypt(password));
            toCreate.setStatus(1);
            Response<Long> response = weShopWalletWriteService.create(toCreate);
            if (!response.isSuccess()){
                log.error("failed to create weShop wallet({}), error code: {}", toCreate, response.getError());
                throw new JsonResponseException(response.getError());
            }
        } else {
            WeShopWallet toUpdate = new WeShopWallet();
            toUpdate.setId(weShopWallet.getId());
            toUpdate.setPassword(EncryptUtil.encrypt(password));
            Response<Boolean> response = weShopWalletWriteService.update(toUpdate);
            if (!response.isSuccess()){
                log.error("failed to update weShop wallet({}), error code: {}", toUpdate, response.getError());
                throw new JsonResponseException(response.getError());
            }
        }

        session.removeAttribute(DistributionSessions.CHECK_CODE_FOR_SET_PW);

        return Boolean.TRUE;
    }

    @RequestMapping(value = "/set-password-code-check", method = RequestMethod.POST)
    public Boolean checkCodeForSetPassword(@RequestParam String code,
                                           HttpServletRequest request) {
        HttpSession session = request.getSession();

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }
        Response<User> rUser = userReadService.findById(userId);
        if (!rUser.isSuccess()){
            log.error("failed to find user by id={}, error code: {}", userId, rUser.getError());
            throw new JsonResponseException(rUser.getError());
        }
        String mobile = rUser.getResult().getFullMobile();

        verifySmsCode(session, AppConstants.SESSION_SMS_CODE_SET_WALLET_PW, code, mobile);

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (ObjectUtils.isEmpty(commonUser)) {
            throw new JsonResponseException("user.not.login");
        }

        session.setAttribute(DistributionSessions.CHECK_CODE_FOR_SET_PW, true);

        Long weShopId = getWeShopId(commonUser);
        Response<WeShopWallet> weShopWalletResponse = weShopWalletReadService.findByWeShopId(weShopId);
        if (!weShopWalletResponse.isSuccess()){
            log.error("failed to find weShop wallet by weShopId={}, error code: {}", weShopId, weShopWalletResponse.getError());
            throw new JsonResponseException(weShopWalletResponse.getError());
        }
        if (ObjectUtils.isEmpty(weShopWalletResponse.getResult())){
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    @RequestMapping(value = "/set-password-by-mobile/send-sms", method = RequestMethod.POST)
    public void sendSmsForSetPassword(HttpServletRequest request) {

        HttpSession session = request.getSession();

        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }
        Response<User> rUser = userReadService.findById(userId);
        if (!rUser.isSuccess()){
            log.error("failed to find user by id={}, error code: {}", userId, rUser.getError());
            throw new JsonResponseException(rUser.getError());
        }
        String mobile = rUser.getResult().getFullMobile();

        String regCode = (String) session.getAttribute(AppConstants.SESSION_SMS_CODE_SET_WALLET_PW);

        failIfCannotResend(regCode);

        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        session.setAttribute(AppConstants.SESSION_SMS_CODE_SET_WALLET_PW, code + "@" + System.currentTimeMillis() + "@" + mobile);
        // 发送验证码
        doSendSmsForSetPassword(mobile, code);
    }

    private void doSendSmsForSetPassword(String mobile, String code) {
        log.debug("sending code={} to mobile={} for setting wallet password by mobile", code, mobile);
        doSendSms(mobile, "sms.we.shop.wallet.set.password", code);
    }

    public void doSendSms(String mobile, String template, String code){
        if (mobile.startsWith(CountryCode.PREFIX_CODE)) {
            if (mobile.startsWith(CountryCode.China.getCode())) {
                mobile = mobile.substring(CountryCode.PREFIX_CODE_LEN);
            } else {
                template = template.replaceAll("^sms", "sms.foreign");
            }
        }
        String receivers = mobile;
        EventSender.sendApplicationEvent(new MsgSendRequestEvent(receivers, template, ImmutableMap.of("code", code)));
        log.info("sendSms mobile={}, message={}", mobile, code);
    }

    private void failIfCannotResend(String code) {
        if (!Strings.isNullOrEmpty(code)) {
            List<String> parts = Splitters.AT.splitToList(code);
            long sendTime = Long.parseLong(parts.get(1));
            if (System.currentTimeMillis() - sendTime < TimeUnit.MINUTES.toMillis(1)) { //
                log.error("could not send sms, sms only can be sent once in one minute");
                throw new JsonResponseException(500, "1分钟内只能获取一次验证码");
            }
        }
    }

    private void verifySmsCode(HttpSession session, String codeKey, String code, String mobile) {
        // session verify, value = code@time@mobile
        String codeInSession = (String) session.getAttribute(codeKey);
        if (Strings.isNullOrEmpty(codeInSession)) {
            log.warn("sent sms code not in session, mobile={}", mobile);
            throw new JsonResponseException("sms.code.expired");
        }
        String expectedCode = Splitters.AT.splitToList(codeInSession).get(0);
        if (!Objects.equal(code, expectedCode)) {
            log.warn("sms code mismatch, for mobile={}", mobile);
            throw new JsonResponseException("sms.code.mismatch");
        }
        String expectedMobile = Splitters.AT.splitToList(codeInSession).get(2);
        if (!Objects.equal(mobile, expectedMobile)) {
            log.warn("mobile not match for sms code, intended={}, actual={}", expectedMobile, mobile);
            throw new JsonResponseException(400, "invoke.invalid");
        }
        // 如果验证成功则删除之前的code
        session.removeAttribute(codeKey);
    }

    private Long getWeShopId(CommonUser commonUser){
        Long weShopId = commonUser.getWeShopId();
        if (weShopId == null){
            Response<WeShop> weShopResponse = weShopReadService.findByUserId(commonUser.getId());
            if (!weShopResponse.isSuccess()){
                log.error("failed to find weShop by userId={}, error code: {}", commonUser.getId(), weShopResponse.getError());
                throw new JsonResponseException(weShopResponse.getError());
            }
            WeShop weShop = weShopResponse.getResult();
            if (ObjectUtils.isEmpty(weShop)) {
                log.error("can not find weShop bu userId={}", commonUser.getId());
                throw new JsonResponseException("weShop.can.not.find");
            }
            return weShop.getId();
        } else {
            return weShopId;
        }
    }
}
