package moonstone.web.distribution.weShop.web;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import io.vertx.core.AbstractVerticle;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.utils.*;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.user.area.model.Area;
import moonstone.user.area.service.AreaReadService;
import moonstone.user.dto.EncryptImage;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.model.User;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.user.service.UserReadService;
import moonstone.weShop.dto.WeShopApply;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.service.WeShopReadService;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.shop.WeShopUpdateEvent;
import moonstone.web.core.files.service.impl.OSSClientServiceImpl;
import moonstone.web.core.mirror.app.RemoteApiOfGongXiao;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.mirror.model.factory.UserDomainFactory;
import moonstone.web.core.weShop.model.WeShopApplyForGx;
import moonstone.web.distribution.component.weShop.WeShopWriteLogic;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 暴力解决, 因为这个目前没有思路去抽象出一个合理层 => Crud
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/api/app/v1/weShop/GongXiao")
public class GongXiaoWeShopRegisterApp extends AbstractVerticle {
    WeShopReadService weShopReadService;
    WeShopWriteLogic weShopWriteLogic;
    ThirdPartyUserReadService thirdPartyUserReadService;
    RemoteApiOfGongXiao remoteApiOfGongXiao;
    ShopWxaCacheHolder shopWxaCacheHolder;
    WeShopCacheHolder weShopCacheHolder;
    MongoTemplate mongoTemplate;
    AreaReadService areaReadService;
    UserReadService<User> userReadService;
    OSSClientServiceImpl ossClientService;
    EnvironmentConfig environmentConfig;
    SourceShopQuerySlice sourceShopQuerySlice;
    UserDomainFactory userDomainFactory;

    @VertxEventBusListener(WeShopUpdateEvent.class)
    public void updateApplier(WeShopUpdateEvent weShopUpdateEvent) {
        WeShop weShop = weShopReadService.findById(weShopUpdateEvent.getWeShopId())
                .getResult();
        mongoTemplate.updateMulti(Query.query(Criteria.where("userId").is(weShop.getUserId()))
                        .addCriteria(Criteria.where("projectId").is(sourceShopQuerySlice.queryProjectIdByShopIdAndSource(
                                sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).take()
                                , MirrorSource.GongXiao.name()
                        ).take()))
                        .addCriteria(Criteria.where("status").ne(-99))
                ,
                Update.update("status", weShop.getStatus())
                        .set("reason", weShop.getReason())
                ,
                WeShopApplyForGx.class
        );
    }

    /**
     * 注册与修改原微店数据
     *
     * @param weShopApply 微店申请
     * @return 注册结果
     */
    @PostMapping("/register")
    public APIResp<Boolean> register(@RequestBody WeShopApplyForGx weShopApply) {
        BaseUser applier = UserUtil.getCurrentUser();
        weShopApply.setStatus(0);
        if (Objects.isNull(applier)) {
            return APIResp.notLogin();
        }
        if (weShopApply.getProjectId() == null) {
            sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name())
                    .flatMap(shopId -> sourceShopQuerySlice.queryProjectIdByShopIdAndSource(shopId, MirrorSource.GongXiao.name()))
                    .ifSuccess(weShopApply::setProjectId);
        }
        if (mongoTemplate.exists(Query.query(Criteria.where("userId").is(applier.getId()))
                        .addCriteria(Criteria.where("projectId").is(weShopApply.getProjectId()))
                        .addCriteria(Criteria.where("status").is(1)),
                WeShopApplyForGx.class)) {
            return APIResp.error(Translate.of("您已经申请成功, 不需要再次申请"));
        }
        weShopApply.setUserId(applier.getId());
        try {
            ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(weShopApply.getProjectId());
            WeShopApply originApply = new WeShopApply();
            BeanUtils.copyProperties(weShopApply, originApply);
            areaFill(weShopApply);
            originApply.setShopId(shopWxa.getShopId());
            String userId = thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), applier.getId())
                    .map(ThirdPartyUser::getThirdPartyId)
                    .ifFail(() -> userDomainFactory.build(null, MirrorSource.GongXiao.name()).register().take())
                    .take();
            remoteApiOfGongXiao.registerShop(userId, weShopApply.extract(img -> {
                EncryptImage encryptImage = new EncryptImage(img);
                encryptImage.fetchData(Runnable::run);
                ByteArrayOutputStream buff = new ByteArrayOutputStream();
                try {
                    ImageIO.write(encryptImage.image(), "png", buff);
                    return ossClientService.upload(environmentConfig.getEnv(), UUID.randomUUID() + ".png", new ByteArrayInputStream(buff.toByteArray()));
                } catch (Exception ignore) {

                }
                return "IMG_LOST";
            }))
                    .ifSuccess(originApply::setOutShopId).take();
            mongoTemplate.updateMulti(Query.query(Criteria.where("projectId").is(weShopApply.getProjectId()))
                            .addCriteria(Criteria.where("userId").is(UserUtil.getUserId()))
                            .addCriteria(Criteria.where("status").ne(1))
                    , Update.update("status", -99)
                    , WeShopApplyForGx.class);
            return APIRespWrapper.wrap(weShopWriteLogic.signInOrUpdate(originApply, applier)
                    .ifSuccess(ignore -> mongoTemplate.insert(weShopApply))
            );
        } catch (Exception exception) {
            log.error("{} fail to register [{}] for user[{}]"
                    , LogUtil.getClassMethodName(), applier, applier.getId(), exception);
            return APIResp.error(Translate.of("注册失败"));
        }
    }

    private void areaFill(WeShopApplyForGx weShopApply) {
        try {
            ImmutableMap.<Supplier<Long>, Consumer<String>>of(weShopApply::getProvinceId, weShopApply::setProvince,
                    weShopApply::getCityId, weShopApply::setCity,
                    weShopApply::getAreaId, weShopApply::setArea)
                    .forEach((areaId, fillArea) -> Optional.ofNullable(areaId.get()).map(areaReadService::findById).map(Response::getResult)
                            .map(Area::getName)
                            .ifPresent(fillArea));
        } catch (Exception e) {
            throw new RuntimeException("读取地址失败", e.getCause());
        }
    }

    @GetMapping("/current")
    public APIResp<WeShopApplyForGx> current(Long projectId) {
        if (UserUtil.getUserId() == null) {
            return APIResp.notLogin();
        }
        Long userId = UserUtil.getUserId();
        Long shopId = sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).take();
        if (null == projectId) {
            projectId = sourceShopQuerySlice.queryProjectIdByShopIdAndSource(shopId, MirrorSource.GongXiao.name()).take();
        }
        WeShopApplyForGx weShopApplyForGx = mongoTemplate.findOne(
                Query.query(Criteria.where("projectId").is(projectId))
                        .addCriteria(Criteria.where("userId").is(userId))
                        .addCriteria(Criteria.where("status").ne(-99))
                , WeShopApplyForGx.class);
        if (weShopApplyForGx != null) {
            weShopCacheHolder.findByUserIdAndShopId(userId, shopId).map(WeShop::getId).ifPresent(weShopApplyForGx::setWeShopId);
            weShopApplyForGx.setMobile(userReadService.findById(userId).getResult().getMobile());
        }
        return APIResp.ok(weShopApplyForGx);
    }

    @GetMapping("/list")
    public APIResp<List<WeShopApplyForGx>> list(@RequestParam(required = false) boolean noAuth) {
        if (noAuth) {
            return APIResp.ok(mongoTemplate.findAll(WeShopApplyForGx.class));
        }
        return APIResp.notLogin();
    }

    @DeleteMapping("/clearAll")
    public APIResp<Boolean> clear(Long userId, @RequestParam(required = false) boolean noAuth) {
        if (!noAuth) {
            return APIResp.notLogin();
        }
        Long projectId = sourceShopQuerySlice.queryProjectIdByShopIdAndSource(sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).take(), MirrorSource.GongXiao.name()).take();
        if (userId == null) {
            mongoTemplate.findAndRemove(Query.query(Criteria.where("projectId").is(projectId)), WeShopApplyForGx.class);
        } else {
            mongoTemplate.findAllAndRemove(Query.query(Criteria.where("userId").is(userId)).addCriteria(Criteria.where("projectId").is(projectId)), WeShopApplyForGx.class);
        }
        return APIResp.ok(true);
    }

}
