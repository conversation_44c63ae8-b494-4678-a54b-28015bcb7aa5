package moonstone.web.distribution.weShop.app;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonPaging;
import moonstone.common.utils.APIRespWrapper;
import moonstone.search.dto.SearchedWeShopItem;
import moonstone.web.core.component.item.model.PriceModifyDTO;
import moonstone.web.core.component.item.model.WeShopItemPreview;
import moonstone.web.core.component.weShop.WeShopItemPriceModifyManager;
import moonstone.web.distribution.weShop.app.view.SearchedWeShopItemForApp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/app/v1/item/list")
@Slf4j
public class AppWeShopItemController {
    @Autowired
    private WeShopItemPriceModifyManager weShopItemPriceModifyManager;

    /**
     * 修改清单内的商品改价
     *
     * @param priceModifyDTOList 改价参数
     * @return 结果
     */
    @PutMapping("/modify")
    APIResp<Boolean> priceModify(@RequestBody List<PriceModifyDTO> priceModifyDTOList, @RequestParam(required = false) Long shopId) {
        Response<Boolean> modifyRes = weShopItemPriceModifyManager.modifyItemPriceBatch(shopId, priceModifyDTOList);
        return APIRespWrapper.wrap(modifyRes);
    }

    @PostMapping
    APIResp<Boolean> addIntoList(@RequestBody List<PriceModifyDTO> priceModifyDTOList, @RequestParam(required = false) Long shopId) {
        Response<Boolean> addIntoList = weShopItemPriceModifyManager.addWeShopItemWithPriceModifyBatch(shopId, priceModifyDTOList);
        return APIRespWrapper.wrap(addIntoList);
    }

    /**
     * 预览改价利润结果
     *
     * @param priceModifyDTOList 改价参数
     * @return 结果
     */
    @PutMapping("/preview")
    APIResp<List<WeShopItemPreview>> previewPriceModify(@RequestBody List<PriceModifyDTO> priceModifyDTOList) {
        Response<List<WeShopItemPreview>> previewResult = weShopItemPriceModifyManager.previewPriceModifyBatch(priceModifyDTOList);
        return APIRespWrapper.wrap(previewResult);
    }

    /**
     * 清单内商品查询
     *
     * @param params 传递status来表示商品状态
     * @return 搜索结果, 清单内商品
     */
    @GetMapping
    APIResp<List<SearchedWeShopItemForApp>> searchItem(Map<String, String> params) {
        int pageNo = Integer.parseInt(params.getOrDefault("pageNo", "1"));
        int pageSize = Integer.parseInt(params.getOrDefault("pageSize", "100"));
        Response<Paging<SearchedWeShopItem>> pagingRes = weShopItemPriceModifyManager.listItem(params);
        if (pagingRes.isSuccess()) {
            Paging<SearchedWeShopItem> paging = pagingRes.getResult();
            return CommonPaging.sliceOf(paging.getTotal().intValue(), paging.getTotal().intValue(), pageNo, pageSize, paging.getData()
                    .stream().map(SearchedWeShopItemForApp::from).collect(Collectors.toList()));
        }
        return APIResp.error(pagingRes.getError());
    }

    @DeleteMapping
    APIResp<Boolean> deleteItem(Long[] weShopItemIds, @RequestParam(required = false) Long shopId) {
        Response<Boolean> deleteRes = weShopItemPriceModifyManager.deleteWeShopItem(shopId, weShopItemIds, null);
        return APIRespWrapper.wrap(deleteRes);
    }

    @PutMapping("/onSell")
    APIResp<Boolean> setOnSell(Long[] weShopItemIds, @RequestParam(defaultValue = "true") boolean onSell, @RequestParam(required = false) Long shopId) {
        Response<Boolean> onSellRes = weShopItemPriceModifyManager.switchStatus(shopId, weShopItemIds, onSell);
        return APIRespWrapper.wrap(onSellRes);
    }
}
