package moonstone.web.distribution.weShop;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.weShop.model.WeShopAccountDetail;
import moonstone.weShop.service.WeShopAccountDetailReadService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * Author:  CaiZhy
 * Date:    2018/12/28
 */
@Slf4j
@RestController
@RequestMapping("/api/weShopAccountDetail")
public class WeShopAccountDetails {
    @RpcConsumer
    private WeShopAccountDetailReadService weShopAccountDetailReadService;

    @RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<WeShopAccountDetail> paging(@RequestParam(required = false) Integer type,
                                              @RequestParam(required = false) Long shopId,
                                              @RequestParam(required = false) Integer status,
                                              @RequestParam(required = false) String statuses,
                                              @RequestParam(required = false) Date startAt,
                                              @RequestParam(required = false) Date endAt,
                                              @RequestParam(required = false) String sortBy,
                                              @RequestParam(required = false) Integer sortType,
                                              @RequestParam(required = false) Integer pageNo,
                                              @RequestParam(required = false) Integer pageSize){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Response<Paging<WeShopAccountDetail>> rWeShopAccountDetailPaging = weShopAccountDetailReadService.paging(
                    type, commonUser.getWeShopId(), shopId, status, statuses, startAt, endAt, sortBy, sortType, pageNo, pageSize);
            if (!rWeShopAccountDetailPaging.isSuccess()) {
                log.error("failed to paging weShop account details by type={}, shopId={}, status={}, statuses={}, startAt={}, endAt={}, " +
                        "sortBy={}, sortType, pageNo={}, pageSize={}, error={}",
                        type, shopId, status, statuses, startAt, endAt, sortBy, sortType, pageNo, pageSize, rWeShopAccountDetailPaging.getError());
                throw new JsonResponseException(rWeShopAccountDetailPaging.getError());
            }
            return rWeShopAccountDetailPaging.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.warn("failed to paging weShop account details by type={}, shopId={}, status={}, statuses={}, startAt={}, endAt={}, " +
                            "sortBy={}, sortType, pageNo={}, pageSize={}, error={}",
                    type, shopId, status, statuses, startAt, endAt, sortBy, sortType, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException(e.getMessage());
        }
    }
}
