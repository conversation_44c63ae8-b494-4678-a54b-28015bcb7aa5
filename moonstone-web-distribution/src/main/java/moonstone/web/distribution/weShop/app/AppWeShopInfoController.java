package moonstone.web.distribution.weShop.app;

import moonstone.common.api.APIResp;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.component.profit.AppOrderProfitReader;
import moonstone.web.core.component.profit.WithdrawInfoReader;
import moonstone.web.core.component.profit.api.AppWithdrawReadService;
import moonstone.web.core.component.profit.api.AppWithdrawWriteService;
import moonstone.web.core.component.profit.dto.OrderProfitViewCriteria;
import moonstone.web.core.component.profit.view.AppOrderProfitView;
import moonstone.web.core.component.profit.view.AppWithdrawApplyView;
import moonstone.web.core.component.profit.view.WithdrawAndProfitView;
import moonstone.web.core.model.dto.WeShopIndexStatisticalData;
import moonstone.web.core.model.dto.WithDrawProfitApplyCriteriaDTO;
import moonstone.web.core.model.dto.WithdrawFeeRequest;
import moonstone.web.distribution.component.weShop.WeShopReadLogic;
import moonstone.web.distribution.user.app.AppUserController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/app/v1/info")
public class AppWeShopInfoController {

    @Autowired
    private WeShopReadLogic weShopReadLogic;
    @Autowired
    private AppUserController appUserController;
    @Autowired
    private WithdrawInfoReader withdrawInfoReader;
    @Autowired
    private AppWithdrawWriteService appWithdrawWriteService;
    @Autowired
    private AppWithdrawReadService appWithdrawReadService;
    @Autowired
    private AppOrderProfitReader appOrderProfitReader;


    /**
     * 获取当前登录的店铺的基础统计信息, 包括提现与单日订单数量
     *
     * @return 简单的统计信息
     */
    @GetMapping
    APIResp<WeShopIndexStatisticalData> getInfo(@RequestParam(required = false) Long shopId) {
        if (!appUserController.current(shopId).ok()) {
            return APIResp.notLogin();
        }
        return APIRespWrapper.wrap(weShopReadLogic.countIndexData(UserUtil.getCurrentUser(), shopId));
    }

    /**
     * 获取可提现利润
     *
     * @return 可提现利润以及限制
     */
    @GetMapping("/balance")
    APIResp<WithdrawAndProfitView> getBalanceInfo(@RequestParam(required = false) Long shopId) {
        if (!appUserController.current(shopId).ok()) {
            return APIResp.notLogin();
        }
        return APIResp.ok(withdrawInfoReader.getWithdrawAndProfitViewFromUserIdAndShopId(UserUtil.getUserId(), shopId));
    }

    /**
     * 获取利润相关订单
     *
     * @param criteria 条件
     * @return 利润相关订单分页
     */
    @GetMapping("/balance/order")
    APIResp<List<AppOrderProfitView>> getOrderProfitView(OrderProfitViewCriteria criteria) {
        if (!appUserController.current().ok()) {
            return APIResp.notLogin();
        }
        if (criteria.getShopId() == null) {
            criteria.setShopId(0L);
        }
        criteria.setUserId(UserUtil.getUserId());
        return appOrderProfitReader.paging(criteria);
    }

    /**
     * 发起提现请求
     *
     * @param withdraw 提现参数
     * @return 是否成功发起提现
     */
    @PostMapping("/withdraw")
    APIResp<Boolean> withdraw(@RequestBody WithdrawFeeRequest withdraw, @RequestParam(required = false) Long shopId) {
        if (!appUserController.current(shopId).ok()) {
            return APIResp.notLogin();
        }
        if (withdraw.getSourceId() == 0 && shopId != null) {
            // 避免错误信息
            withdraw.setSourceId(shopId);
        }
        return APIRespWrapper.wrap(appWithdrawWriteService.withdraw(UserUtil.getUserId(), withdraw));
    }

    /**
     * 获取提现记录
     *
     * @param criteria 查询条件
     * @return 提现记录
     */
    @GetMapping("/withdraw")
    APIResp<List<AppWithdrawApplyView>> pagingWithdrawList(WithDrawProfitApplyCriteriaDTO criteria) {
        if (!appUserController.current(criteria.getSourceId()).ok()) {
            return APIResp.notLogin();
        }
        // 避免错误信息
        criteria.setUserId(UserUtil.getUserId());
        if (criteria.getSourceId() == null) {
            criteria.setSourceId(0L);
        }
        return APIRespWrapper.wrapPaging(appWithdrawReadService.paging(criteria), criteria.getPageNo(), criteria.getPageSize());
    }

}
