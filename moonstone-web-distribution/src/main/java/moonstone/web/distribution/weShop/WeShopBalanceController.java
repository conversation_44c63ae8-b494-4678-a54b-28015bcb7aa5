package moonstone.web.distribution.weShop;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonPaging;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.*;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.api.BankAccountJudge;
import moonstone.order.dto.InComeDetail;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.BalanceDetailWriteService;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.order.service.WithdrawAccountWriteService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserWxReadService;
import moonstone.web.core.component.profit.WeShopWithdrawComponent;
import moonstone.web.core.component.profit.view.AppWithdrawApplyView;
import moonstone.web.core.component.profit.view.AppWithdrawApplyViewImpl;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.model.dto.WithDrawProfitApplyCriteriaDTO;
import moonstone.web.core.model.dto.WithdrawAccountWithBankInfo;
import moonstone.web.core.model.dto.WithdrawFeeRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/weShop/balance")
@Slf4j
public class WeShopBalanceController {
    final String WithDrawSessionCode = "WITH_DRAW_SC";
    @Autowired
    WithDrawProfitApplyReadService withDrawProfitApplyReadService;
    @Autowired
    BalanceDetailManager balanceDetailManager;
    @Autowired
    BalanceDetailWriteService balanceDetailWriteService;
    @Autowired
    UserReadService<User> userReadService;
    @Autowired
    WithdrawAccountReadService withdrawAccountReadService;
    @Autowired
    WithdrawAccountWriteService withdrawAccountWriteService;
    @Autowired
    UserWxReadService userWxReadService;
    @Autowired
    ShopWxaReadService shopWxaReadService;
    @Autowired
    BankAccountJudge bankAccountJudge;
    @Autowired
    WeShopWithdrawComponent weShopWithdrawComponent;
    @Value("${timeout.sms:5}")
    Long timeoutMinus;

    @PostMapping
    @RequestMapping("/withdraw")
    public APIResp<Boolean> withDraw(@RequestBody WithdrawFeeRequest fee) {
        try {
            fee.setSourceId(fee.getSourceId());
            CommonUser user = UserUtil.getCurrentUser();
            if (user == null) {
                throw new JsonResponseException("user.not.login");
            }
            if (fee.fee <= 0) {
                throw new JsonResponseException(new Translate("金额数目不对").toString());
            }
            String password;
            InComeDetail inComeDetail = getPresentCash(user.getId(), fee.getSourceId()).getResult();
            if (inComeDetail == null) {
                log.error("{} userId:{} sourceId:{}", LogUtil.getClassMethodName(), user.getId(), fee.getSourceId());
                throw new JsonResponseException(new Translate("查找账户信息失败").toString());
            }
            password = inComeDetail.getExtra().get(BalanceDetail.ExtraEnu.password.getIndex());
            if (!EncryptUtil.match(fee.getPassword(), password)) {
                throw new JsonResponseException(new Translate("提现密码不正确").toString());
            }
            WithdrawAccount account = weShopWithdrawComponent.findWithdrawAccountOrWechatAccount(fee.getSourceId(), fee.getWithdrawAccount(), user.getId());
            // 试图更新一下信息然后将需要显示的信息填入到account里当作一个参数传递,并不存储入数据库
            account = beautyBankAccount(account);
            if (account instanceof WithdrawAccountWithBankInfo) {
                account.getExtra().put("showName", ((WithdrawAccountWithBankInfo) account).getBankName());
                account.getExtra().put("showAccount", ((WithdrawAccountWithBankInfo) account).getAccountTail());
            } else {
                account.getExtra().put("showName", WithDrawProfitApply.WithdrawPaidType.from(account.getType()).map(WithDrawProfitApply.WithdrawPaidType::getName).orElse(new Translate("未知").toString()));
                if (WithDrawProfitApply.WithdrawPaidType.WECHAT.getType() != (account.getType())) {
                    account.getExtra().put("showAccount", account.getAccount() != null && account.getAccount().length() > 4 ? account.getAccount().substring(account.getAccount().length() - 4) : account.getAccount());
                }
            }
            val rApply = balanceDetailManager.withdraw(user.getId(), fee.getFee(), fee.getSourceId(), WithDrawProfitApply.WithdrawPaidType.from(account.getType()).orElse(WithDrawProfitApply.WithdrawPaidType.OTHER), account);
            return APIResp.ok(rApply.isSuccess());
        } catch (Exception ex) {
            log.error("{} failed cause:{}", LogUtil.getClassMethodName(), ex.getMessage(), ex);
            return APIRespWrapper.error(ex.getMessage());
        }
    }

    @GetMapping("/withdraw")
    public APIResp<List<AppWithdrawApplyView>> page(WithDrawProfitApplyCriteriaDTO criteria) {
        try {
            CommonUser user = UserUtil.requireLoginUser();
            criteria.setSourceId(criteria.getSourceId());
            criteria.setUserId(user.getId());
            Paging<WithDrawProfitApply> profitApplyPaging = withDrawProfitApplyReadService.paging(criteria).getResult();
            if (profitApplyPaging.isEmpty()) {
                return CommonPaging.empty(criteria.getPageNo(), criteria.getPageSize());
            }
            return APIRespWrapper.wrapPaging(Response.ok(new Paging<>(profitApplyPaging.getTotal(), profitApplyPaging.getData().stream().map(AppWithdrawApplyViewImpl::from).collect(Collectors.toList())))
                    , criteria.getPageNo(), criteria.getPageSize());
        } catch (Exception ex) {
            log.error("{} fail to page withdraw[{}]", LogUtil.getClassMethodName(), criteria.toMap());
            return APIRespWrapper.error(ex.getMessage());
        }
    }

    /**
     * 将银行信息打包进入到这个帐号中
     */
    WithdrawAccount beautyBankAccount(WithdrawAccount withdrawAccount) {
        Set<Integer> bankTypeSet = Stream.of(WithDrawProfitApply.WithdrawPaidType.BANK, WithDrawProfitApply.WithdrawPaidType.SelfBank).map(WithDrawProfitApply.WithdrawPaidType::getType).collect(Collectors.toSet());
        if (!bankTypeSet.contains(withdrawAccount.getType())) {
            return withdrawAccount;
        }
        WithdrawAccountWithBankInfo beautyAccount = new WithdrawAccountWithBankInfo();
        BeanUtils.copyProperties(withdrawAccount, beautyAccount);
        BankAccountJudge.BankType bankInfo = bankAccountJudge.judge(withdrawAccount.getAccount());
        if (bankInfo == BankAccountJudge.UNKNOW) {
            return withdrawAccount;
        }
        String account = withdrawAccount.getAccount();
        beautyAccount.setAccountTail(account.substring(account.length() - 4));
        beautyAccount.setBankImg(bankInfo.getImg());
        beautyAccount.setBankImgUrl(bankInfo.getImgUrl());
        beautyAccount.setBankName(bankInfo.getName());
        return beautyAccount;
    }

    @PutMapping("/account/update")
    public APIResp<Boolean> updateAccount(WithdrawAccount account) {
        WithdrawAccount exists = withdrawAccountReadService.findById(account.getId()).orElse(Optional.empty()).orElse(null);
        if (exists == null) {
            return APIRespWrapper.wrap(Response.fail(new Translate("目标账户不存在").toString()));
        }
        if (Objects.equals(exists.getUserId(), UserUtil.getUserId())) {
            return APIRespWrapper.wrap(Response.fail(new Translate("该账户不属于你").toString()));
        }
        return APIRespWrapper.wrap(withdrawAccountWriteService.update(account));
    }

    @PostMapping("/account/default")
    public APIResp<Boolean> setAccountDefault(Long shopId, Long accountId) {
        WithdrawAccount account = withdrawAccountReadService.findById(accountId).orElse(Optional.empty()).orElse(null);
        if (account == null) {
            return APIRespWrapper.wrap(Response.fail(new Translate("目标帐号不存在").toString()));
        }
        if (UserUtil.getUserId() == null) {
            return APIRespWrapper.wrap(Response.fail("user.not.login"));
        }
        if (!Objects.equals(account.getUserId(), UserUtil.getUserId())) {
            return APIRespWrapper.wrap(Response.fail(new Translate("目标账户不属于你").toString()));
        }
        withdrawAccountWriteService.revokeDefault(account.getShopId(), account.getUserId());
        account.setDefault();
        return APIRespWrapper.wrap(withdrawAccountWriteService.update(account));
    }

    @PostMapping("/account/add")
    public APIResp<WithdrawAccount> addAccount(@RequestBody WithdrawAccount account) {
        account.setShopId(account.getShopId());
        if (UserUtil.getUserId() == null) {
            return APIRespWrapper.wrap(Response.fail("user.not.login"));
        }
        account.setUserId(UserUtil.getUserId());
        account.setDefault();
        Either<WithdrawAccount> res = withdrawAccountWriteService.create(account);
        if (res.isSuccess()) {
            return APIRespWrapper.wrap(Response.ok(res.take()));
        } else {
            return APIRespWrapper.wrap(Response.fail(new Translate("创建帐号失败").toString()));
        }
    }

    /**
     * 发送SMS信息提供修改验证码
     */
    @PostMapping("/set-withdraw-password-sms")
    public APIResp<Boolean> getWithDrawPasswordSms(Long shopId, HttpSession session) {
        User user = Optional.ofNullable(userReadService.findById(UserUtil.getUserId()).getResult()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        Random random = new Random(Instant.now().getEpochSecond());
        byte[] rom = new byte[6];
        for (int i = 0, num = 0; i < rom.length; i++, num = Math.abs(random.nextInt() % 10)) {
            rom[i] = (byte) (num + '0');
        }
        setSmsCode(session, new String(rom));
        String template = "设置提现密码验证";
        MsgSendRequestEvent event = new MsgSendRequestEvent(user.getMobile(), template, ImmutableMap.of("code", new String(rom)));
        EventSender.sendApplicationEvent(event);
        return APIResp.ok(true);
    }

    /**
     * 设置短信code
     *
     * @param session session
     * @param code    代码
     */
    void setSmsCode(HttpSession session, String code) {
        code = code + "," + Instant.now().getEpochSecond();
        log.info("{} (setSmsCode) set SmsCode:{}", LogUtil.getClassMethodName(), code);
        session.setAttribute(WithDrawSessionCode, code);
    }

    /**
     * 读取SmsCode
     *
     * @param session session
     * @return 是否有Code
     */
    Optional<String> getSmsCode(HttpSession session) {
        String code = (String) session.getAttribute(WithDrawSessionCode);
        if (code == null) {
            return Optional.empty();
        }
        String[] rom = code.split(",");
        if (rom.length != 2) {
            log.error("[{}](getSms) error in split code:{}", LogUtil.getClassMethodName(), code);
            return Optional.empty();
        }
        try {
            if (Instant.now().getEpochSecond() - Long.parseLong(rom[1]) > 60 * timeoutMinus) {
                return Optional.empty();
            }
            return Optional.of(rom[0]);
        } catch (Exception ex) {
            log.error("[{}](getSms) parse time failed code:{}", LogUtil.getClassMethodName(), code);
            ex.printStackTrace();
            return Optional.empty();
        }
    }

    /**
     * 由用户Id获取可用钱包数据
     */
    Response<InComeDetail> getPresentCash(long userId, long sourceId) {
        Response<List<InComeDetail>> cashListRes = balanceDetailManager.getCash(userId, sourceId);
        if (!cashListRes.isSuccess()) {
            log.error("[{}](changeWithDrawPassword) userId:{} failed because:{}", LogUtil.getClassMethodName(), userId, cashListRes.getError());
            return Response.fail(cashListRes.getError());
        }
        List<InComeDetail> cachedList = cashListRes.getResult();
        if (CollectionUtils.isEmpty(cachedList)) {
            cachedList = balanceDetailManager.initCash(userId, sourceId);
        }

        Optional<InComeDetail> balanceDetail = Optional.empty();
        for (InComeDetail inComeDetail : cachedList) {
            if (inComeDetail.isPresent()) {
                balanceDetail = Optional.of(inComeDetail);
            }
        }
        if (!balanceDetail.isPresent()) {
            log.error("[{}](changeWithDrawPassword) fail to get Cash for userId:{}", LogUtil.getClassMethodName(), userId);
            return Response.fail("get cash fail");
        }
        return Response.ok(balanceDetail.get());
    }

    @PostMapping("/change-withdraw-password")
    public APIResp<Boolean> changeWithDrawPassword(Long shopId, String oldPassword, String password) {
        try {
            final long sourceId = shopId;
            long userId = Optional.ofNullable(UserUtil.getCurrentUser().getId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
            InComeDetail presentInCome = Optional.ofNullable(getPresentCash(userId, sourceId).getResult()).orElseThrow(() ->
                    new JsonResponseException(new Translate("获取账户信息失败").toString()));
            password = EncryptUtil.encrypt(password);
            if (!presentInCome.getExtra().containsKey(BalanceDetail.ExtraEnu.password.getIndex())) {
                throw new JsonResponseException(new Translate("支付密码不存在,请先设置支付密码").toString());
            }
            if (!EncryptUtil.match(oldPassword, presentInCome.getExtra().get(BalanceDetail.ExtraEnu.password.getIndex()))) {
                throw new JsonResponseException(new Translate("旧密码错误,请重试").toString());
            }
            presentInCome.getExtra().put(BalanceDetail.ExtraEnu.password.getIndex(), password);
            return APIResp.ok(balanceDetailWriteService.update(presentInCome).isSuccess());
        } catch (Exception ex) {
            log.error("{} fail to change-withdraw-password user[{}]", LogUtil.getClassMethodName(), UserUtil.getUserId(), ex);
            return APIRespWrapper.error(ex.getMessage());
        }
    }

    @PostMapping("/set-withdraw-password")
    public APIResp<Boolean> setWithDrawPassword(Long shopId, String smsCode, String password, HttpSession session) {
        try {
            final long sourceId = shopId;

            long userId = Optional.ofNullable(UserUtil.getCurrentUser().getId())
                    .orElseThrow(() -> new JsonResponseException("user.not.login"));
            boolean match = getSmsCode(session).map(code -> code.equals(smsCode))
                    .orElseThrow(() -> new JsonResponseException("sms.code.expired"));
            if (!match) {
                throw new JsonResponseException("sms.code.mismatch");
            }
            InComeDetail presentInCome = Optional.ofNullable(getPresentCash(userId, sourceId).getResult())
                    .orElseThrow(() -> new JsonResponseException(new Translate("获取账户信息失败").toString()));
            password = EncryptUtil.encrypt(password);
            if (presentInCome.getExtra().containsKey(BalanceDetail.ExtraEnu.password.getIndex())) {
                throw new JsonResponseException(new Translate("支付密码已经存在").toString());
            }
            presentInCome.getExtra().put(BalanceDetail.ExtraEnu.password.getIndex(), password);
            return APIResp.ok(balanceDetailWriteService.update(presentInCome).isSuccess());
        } catch (Exception ex) {
            log.error("{} fail to set-withdraw-password for user[{}]", LogUtil.getClassMethodName(), UserUtil.getUserId(), ex);
            return APIRespWrapper.error(ex.getMessage());
        }
    }

    @PostMapping("/forget-withdraw-password")
    public APIResp<Boolean> forgetWithDrawPassword(Long shopId, String smsCode, String password, HttpSession session) {
        try {
            final long sourceId = shopId;

            long userId = Optional.ofNullable(UserUtil.getCurrentUser().getId())
                    .orElseThrow(() -> new JsonResponseException("user.not.login"));
            boolean match = getSmsCode(session).map(code -> code.equals(smsCode))
                    .orElseThrow(() -> new JsonResponseException("sms.code.expired"));
            if (!match) {
                throw new JsonResponseException("sms.code.mismatch");
            }
            InComeDetail presentInCome = Optional.ofNullable(getPresentCash(userId, sourceId).getResult())
                    .orElseThrow(() -> new JsonResponseException(new Translate("获取账户信息失败").toString()));
            password = EncryptUtil.encrypt(password);
            presentInCome.getExtra().put(BalanceDetail.ExtraEnu.password.getIndex(), password);
            return APIResp.ok(balanceDetailWriteService.update(presentInCome).isSuccess());
        } catch (Exception exception) {
            log.error("{} fail to forget withdraw password for user[{}]", LogUtil.getClassMethodName(), UserUtil.getUserId(), exception);
            return APIRespWrapper.error(exception.getMessage());
        }
    }

}
