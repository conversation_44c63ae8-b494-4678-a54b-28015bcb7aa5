package moonstone.web.distribution;

import moonstone.web.core.CoreWebConfiguration;
import moonstone.web.distribution.interceptors.ParanaLoginInterceptor;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * Created by CaiZhy on 2018/11/27.
 */
@Configuration
@ComponentScan
@EnableWebMvc
@EnableAutoConfiguration
@Import({CoreWebConfiguration.class})
public class DistributionConfiguration {

    @ConditionalOnProperty(name = "enable.parana.login.interceptor", havingValue = "true", matchIfMissing = true)
    @Bean
    public ParanaLoginInterceptor paranaLoginInterceptor() {
        return new ParanaLoginInterceptor();
    }

}
