package moonstone.web.distribution.items;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.dto.ViewedItemDetailInfo;
import moonstone.item.dto.paging.SearchItemCriteria;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.weShop.dto.DistributionViewedItem;
import moonstone.weShop.dto.SearchItemForWeDistributor;
import moonstone.web.distribution.component.item.WeShopItemReadLogic;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Create By wulianlei@2018/12/19
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class DistributionItems {
    @Autowired
    private WeShopItemReadLogic weShopItemReadLogic;
    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ItemCacheHolder itemCacheHolder;

    @GetMapping("/search-for-we-shop")
    public Paging<SearchItemForWeDistributor> searchForWeShop(SearchItemCriteria searchItemCriteria) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        final Long userId = commonUser.getId();
        return weShopItemReadLogic.searchForWeShop(userId, searchItemCriteria);
    }

    @GetMapping("/weDistributor/{itemId}/for-view")
    public DistributionViewedItem getViewItem(@PathVariable(name = "itemId") long itemId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            throw new JsonResponseException(new Translate("请以正确身份登录").toString());
        }
        return weShopItemReadLogic.findForDistributionView(commonUser.getWeShopId(), itemId).getResult();
    }

    @GetMapping(value = "/item/{itemId}/detail-info")
    public Response<ViewedItemDetailInfo> findItemDetailInfoByItemId(@PathVariable Long itemId) {
        return Response.ok(itemCacheHolder.findViewDetail(itemId));
    }

    @RequestMapping(value = "/seller/items/query")
    private List<String> quertyStockName(Long itemId) {
        Response<Item> rItems = itemReadService.findById(itemId);
        if (!rItems.isSuccess()) {
            log.error("item didnt exsit (id:{})", itemId);
            throw new JsonResponseException("item.find.fail");
        }
        Response<List<Sku>> rSkus = skuReadService.findSkusByItemId(itemId);
        if (!rSkus.isSuccess()) {
            log.error("skus list find failed (itemId:{})", itemId);
            throw new JsonResponseException("sku.find.fail");
        }
        List<List<String>> outerSkuIds = rSkus.getResult().stream().map(this::getDeportNameFromSku).collect(Collectors.toList());
        Set<String> resultSet = new LinkedHashSet<>();
        for (List<String> deportName : outerSkuIds) {
            resultSet.addAll(deportName);
        }
        return new ArrayList<>(resultSet);
    }

    private List<String> getDeportNameFromSku(Sku sku) {
        Map<String, String> tags = sku.getTags() == null ? new HashMap<>() : sku.getTags();
        String pushSystemStr = tags.getOrDefault("pushSystem", "");
        if (pushSystemStr.isEmpty()) {
            return new ArrayList<>();
        }
        int pushSystemId;
        try {
            pushSystemId = Integer.parseInt(pushSystemStr);
        } catch (Exception ex) {
            log.error("parse pushSystemStr fail,str:{}", pushSystemStr);
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        if (ThirdPartySystem.fromInt(pushSystemId) == ThirdPartySystem.Y800_V2) {
            Response<List<ThirdPartySkuStock>> thirdPartySkuStockResponse = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(sku.getShopId(), pushSystemId, sku.getOuterSkuId());
            if (thirdPartySkuStockResponse.isSuccess() && !CollectionUtils.isEmpty(thirdPartySkuStockResponse.getResult())) {
                {
                    resultList = thirdPartySkuStockResponse.getResult().stream().map(ThirdPartySkuStock::getDepotName).collect(Collectors.toList());
                }
            }
        } else {
            log.error("find pushSystem failed,pushSystem:{},outerSkuId:{}", pushSystemId, sku.getOuterShopId());
        }
        return resultList;
    }
}