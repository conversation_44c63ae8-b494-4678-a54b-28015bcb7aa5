package moonstone.web.distribution.util;

import moonstone.weShop.dto.WeShopApply;
import moonstone.weShop.model.WeShop;

public interface WeShopWrapper {
    /**
     * 获取更新的微店数据
     *
     * @param weShopApply 微店申请实体
     * @return 微店数据
     */
    static WeShop wrapUpdate(WeShopApply weShopApply) {
        WeShop weShop = new WeShop();
        weShop.setName(weShopApply.getName());
        weShop.setDescription(weShopApply.getDescription());
        weShop.setLogoUrl(weShopApply.getLogoUrl());
        weShop.setBackgroundUrl(weShopApply.getBackgroundUrl());
        weShop.setRealName(weShopApply.getRealName());
        weShop.setStatus(0);
        weShop.setReason("");
        return weShop;
    }
}
