package moonstone.web.distribution.util;

import io.terminus.common.utils.BeanMapper;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.weShop.model.WeShop;
import moonstone.web.distribution.user.dto.DistributionParanaUserProfile;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * Created by CaiZhy on 2018/12/21.
 */
public class DistributionParanaUserProfileMaker {

    private static final DateTimeFormatter DTF = DateTimeFormat.forPattern("yyyy-MM-dd");

    public static DistributionParanaUserProfile from(User user, UserProfile userProfile, WeShop weShop) {
        DistributionParanaUserProfile distributionParanaUserProfile = new DistributionParanaUserProfile();
        BeanMapper.copy(userProfile, distributionParanaUserProfile);
        BeanMapper.copy(user, distributionParanaUserProfile);
        distributionParanaUserProfile.setUsername(user.getName());
        distributionParanaUserProfile.setAvatar(userProfile.getAvatar_());
        if (StringUtils.hasText(userProfile.getBirth())) {
            distributionParanaUserProfile.setBirthday(DTF.parseLocalDate(userProfile.getBirth()).toDate());
        }
        if (Objects.isNull(weShop)) return distributionParanaUserProfile;
        distributionParanaUserProfile.setWeShopId(weShop.getId());
        if (StringUtils.hasText(weShop.getRealName())) {
            distributionParanaUserProfile.setWeShopRealName(weShop.getRealName());
        }
        return distributionParanaUserProfile;
    }
}
