package moonstone.web.distribution.adv;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.adv.dto.AdvDTO;
import moonstone.adv.enums.AdvColumnNameType;
import moonstone.cache.AdvCacher;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopShopAccountReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * Author:  CaiZhy
 * Date:    2018/12/25
 */
@Slf4j
@RestController
@RequestMapping("/api/weDistribution/adv")
public class DistributionAdvs {
    @RpcConsumer
    private WeShopShopAccountReadService weShopShopAccountReadService;

    @Autowired
    private AdvCacher advCacher;

    @GetMapping("/listWeShopAdv")
    public List<AdvDTO> listWeShopAdv(@RequestParam(name="shopId", defaultValue = "0")long shopId)
    {
        CommonUser commonUser = UserUtil.getCurrentUser();
        final Long userId = commonUser.getId();
        List<Long> shopIds;
        if (shopId==0)
            shopIds = weShopShopAccountReadService.findByUserId(userId).getResult().stream()
                    .map(WeShopShopAccount::getShopId).collect(toList());
        else
            shopIds= Stream.of(shopId).collect(toList());
        List<AdvDTO> result = new ArrayList<>();
        for (Long oneShopId : shopIds) {
            List<AdvDTO> advDTOS = advCacher.listByColumnSnAndShopId(AdvColumnNameType.WXA_WE_SHOP_BANNER.getSn(), oneShopId);
            result.addAll(advDTOS);
        }
        return result;
    }
}
