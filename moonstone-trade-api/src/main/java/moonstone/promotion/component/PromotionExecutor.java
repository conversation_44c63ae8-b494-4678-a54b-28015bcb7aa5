/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.component;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.cache.PromotionToolCacher;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.LogUtil;
import moonstone.component.item.component.TaxChecker;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.order.model.ReceiverInfo;
import moonstone.promotion.api.*;
import moonstone.promotion.dto.PromotionContext;
import moonstone.promotion.model.Promotion;
import moonstone.shop.model.Shop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Optional;

/**
 * 计算各种级别的优惠
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-12
 */
@Component
@Slf4j
public class PromotionExecutor {
    private final TaxChecker taxChecker;

    private final PromotionCacher promotionCacher;

    private final PromotionToolCacher promotionToolCacher;

    private final PromotionQualificationValidator promotionQualificationValidator;

    @Autowired
    public PromotionExecutor(TaxChecker taxChecker,
                             PromotionCacher promotionCacher,
                             PromotionToolCacher promotionToolCacher,
                             PromotionQualificationValidator promotionQualificationValidator) {
        this.taxChecker = taxChecker;
        this.promotionCacher = promotionCacher;
        this.promotionToolCacher = promotionToolCacher;
        this.promotionQualificationValidator = promotionQualificationValidator;
    }

    /**
     * 传入richSku以及选择的优惠, 计算需要实际支付的单品金额, 会将优惠结果设置到richSku的对应字段中
     * <p>
     * 如果选择的营销活动不满足使用条件, 则将营销id设为空
     *
     * @param buyer   买家
     * @param shop    店铺
     * @param richSku 提交的richSku
     */
    public void processSkuOrder(CommonUser buyer, Shop shop, RichSku richSku) {
        Sku sku = richSku.getSku();
        final long originFee = Long.valueOf(sku.getPrice()) * richSku.getQuantity();
        richSku.setOriginFee(originFee);
        if (Optional.ofNullable(richSku.getExtra()).orElseGet(HashMap::new).getOrDefault("freeGift", "false").equals("true")) {
            richSku.setPromotionId(null);
            richSku.setOriginFee(0L);
            richSku.setFee(0L);
            richSku.setDiffFee(0);
            richSku.setDiscount(0L);
            richSku.setAfterDiscountFee(0L);
            return;
        }
        final Long promotionId = richSku.getPromotionId();
        if (promotionId == null) {
            richSku.setFee(originFee);
            richSku.setAfterDiscountFee(richSku.getFee());
            log.debug("{} richSku:{}, afterFee:{}, tax:{}", LogUtil.getClassMethodName(), JSON.toJSONString(richSku), richSku.getAfterDiscountFee(), richSku.getTax());
            richSku.setFee(richSku.getAfterDiscountFee() + richSku.getTax());
            return;
        }
        //处理sku级别的营销
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);
        PromotionTool<? extends Behavior> promotionTool =
                promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());

        if (promotionQualificationValidator.skuPromotionQualified(buyer, richSku, shop, promotion)) {
            final Behavior behavior = promotionTool.behavior();
            PromotionContext promotionContext =
                    new PromotionContext(buyer, shop, null, promotion);

            if (behavior.affectPay()) { //如果营销活动影响支付金额, 则需要实时计算
                ((SkuOrderBehavior) behavior).execute(richSku, promotionContext, promotion.getBehaviorParams());
            } else {
                richSku.setFee(originFee);
            }
        } else {//如果营销不适用, 则将对应的字段设置为null, 不抛出异常
            log.error("sku promotion(id={}) is not apply", promotionId);
            richSku.setPromotionId(null);
            richSku.setFee(originFee);
        }
        richSku.setAfterDiscountFee(richSku.getFee());
        richSku.setFee(richSku.getAfterDiscountFee() + richSku.getTax());
    }

    /**
     * 传入richSkusByShop以及选择的优惠, 计算需要实际支付的店铺订单金额, 会将优惠结果设置到richSkusByShop的对应字段中
     *
     * @param buyer          买家
     * @param richSkusByShop 输入的店铺订单相关信息
     */
    public void processShopOrder(CommonUser buyer, RichSkusByShop richSkusByShop) {
        final Long promotionId = richSkusByShop.getPromotionId();
        if (promotionId == null) {
            richSkusByShop.setFee(richSkusByShop.getOriginFee());
            return;
        }

        //处理店铺级别的营销
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);
        PromotionTool<? extends Behavior> promotionTool =
                promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());

        if (promotionQualificationValidator.shopPromotionQualified(buyer, richSkusByShop, promotion)) {
            final Behavior behavior = promotionTool.behavior();
            PromotionContext promotionContext =
                    new PromotionContext(buyer, richSkusByShop.getShop(), null, promotion);

            if (behavior.affectPay()) { //如果营销活动影响支付金额, 则需要实时计算
                ((ShopOrderBehavior) behavior).execute(richSkusByShop, promotionContext, promotion.getBehaviorParams());
            } else {
                richSkusByShop.setFee(richSkusByShop.getOriginFee());
            }
        } else { //如果营销不适用, 则将对应的字段设置为null, 不抛出异常
            log.error("shop promotion(id={}) is not apply", promotionId);
            richSkusByShop.setFee(richSkusByShop.getOriginFee());
            richSkusByShop.setPromotionId(null);
        }
    }


    /**
     * 传入richSkusByShop以及选择的优惠, 计算需要实际支付的运费, 会将优惠结果设置到 richSkusByShop的对应字段中
     * <p>
     * 如果选择的营销活动不满足使用条件, 则将营销id设为空
     *
     * @param buyer          买家
     * @param richSkusByShop 输入的店铺订单相关信息
     */
    public void processShipment(CommonUser buyer, ReceiverInfo receiverInfo, RichSkusByShop richSkusByShop) {
        final Long promotionId = richSkusByShop.getShipmentPromotionId();
        if (promotionId == null) {
            richSkusByShop.setShipFee(richSkusByShop.getOriginShipFee());
            return;
        }

        if (receiverInfo != null) {
            richSkusByShop.setReceiverInfo(receiverInfo);
        }
        //处理运费营销
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);
        PromotionTool<? extends Behavior> promotionTool =
                promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());

        if (promotionQualificationValidator.shipmentPromotionQualified(buyer, richSkusByShop, promotion)) {
            final Behavior behavior = promotionTool.behavior();
            PromotionContext promotionContext =
                    new PromotionContext(buyer, richSkusByShop.getShop(), richSkusByShop.getReceiverInfo(), promotion);

            //运费营销一定会影响支付金额
            ((ShipmentFeeBehavior) behavior).execute(richSkusByShop, promotionContext, promotion.getBehaviorParams());
        } else { //如果营销不适用, 则将对应的字段设置为null, 不抛出异常
            log.error("shipment promotion(id={}) is not apply", promotionId);
            richSkusByShop.setShipmentPromotionId(null);
            richSkusByShop.setShipFee(richSkusByShop.getOriginShipFee());
        }
    }

    /**
     * 传入richOrder及选择的优惠, 计算需要实际支付的金额, 会将优惠结果设置到richOrder对应的字段中,
     * <p>
     * 如果选择的营销活动不满足使用条件, 则将营销id设为空
     *
     * @param richOrder 传入的多个店铺订单信息
     */
    public void processRichOrder(RichOrder richOrder) {
        if (richOrder.getPromotionId() == null) {
            richOrder.setFee(richOrder.getOriginFee());
            return;
        }

        //处理全局营销
        final Long promotionId = richOrder.getPromotionId();
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);

        if (promotionQualificationValidator.globalPromotionQualified(richOrder, promotion)) {

            PromotionContext promotionContext =
                    new PromotionContext(richOrder.getBuyer(), null, richOrder.getReceiverInfo(), promotion);
            PromotionTool<? extends Behavior> promotionTool =
                    promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());

            final Behavior behavior = promotionTool.behavior();
            if (behavior.affectPay()) { //如果营销活动影响支付金额, 则需要实时计算
                ((RichOrderBehavior) behavior).execute(richOrder, promotionContext, promotion.getBehaviorParams());
            } else {
                richOrder.setFee(richOrder.getOriginFee());
            }
        } else {
            log.error("rich order promotion(id={}) is not apply", promotionId);
            richOrder.setPromotionId(null);
        }
    }
}
