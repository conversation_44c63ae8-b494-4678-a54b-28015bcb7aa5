/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.bricks;

import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.order.dto.RichSku;
import moonstone.promotion.annotation.PromotionBrick;
import moonstone.promotion.api.BrickType;
import moonstone.promotion.api.SkuOrderBehavior;
import moonstone.promotion.dto.PromotionContext;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * sku级别的满减, 根据数量, 比如满2件减50之类的
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
@PromotionBrick(key = "sku-if-then-reduce-by-quantity", type = BrickType.BEHAVIOR)
@Slf4j
public class IfThenReduceByQuantitySkuOrderBehavior implements SkuOrderBehavior {

    /**
     * 是否影响支付金额
     *
     * @return 是否影响支付金额, 如果不影响支付金额, 则在支付回调时执行
     */
    @Override
    public boolean affectPay() {
        return true;
    }

    /**
     * 检查用户是否有这项优惠, 比如优惠券需要检查用户是否有优惠, 而对于直降这个营销则不需要检查, 默认所有用户都有直降优惠
     *
     * @param buyer       买家
     * @param promotionId 营销活动id
     * @return 是否拥有
     */
    @Override
    public boolean userOwn(CommonUser buyer, Long promotionId) {
        return true;
    }

    @Override
    public Map<String, Object> extractSkuOrderPromotionInfo(RichSku richSku, PromotionContext promotionContext, Map<String, String> conditionParams, Map<String, String> behaviorParams) {
        return null;
    }

    /**
     * 判断sku级别的营销是否满足执行条件
     *
     * @param richSku          sku订单
     * @param promotionContext 营销执行上下文
     * @param conditionParams  营销方式涉及的条件参数
     * @return 是否满足
     */
    @Override
    public boolean condition(RichSku richSku, PromotionContext promotionContext, Map<String, String> conditionParams) {
        String conditionQuantity = conditionParams.get("conditionQuantity");
        if (ObjectUtils.isEmpty(conditionQuantity)) {
            log.error("conditionQuantity can not empty");
            throw new ServiceException("promotion.param.miss");
        }
        return richSku.getQuantity() >= Integer.parseInt(conditionQuantity);
    }

    /**
     * sku级别营销的结果, 如果影响支付金额, 会设置richSku的fee字段,以及可能的discount, integral, balance等字段,
     *
     * 注意, 不要有任何持久化操作!!
     *
     * @param richSku        sku订单
     * @param promotionContext   营销执行上下文
     * @param behaviorParams 营销方式涉及的参数
     */
    @Override
    public void execute(RichSku richSku, PromotionContext promotionContext, Map<String, String> behaviorParams) {

        String reduceFee = behaviorParams.get("reduceFee");
        if (ObjectUtils.isEmpty(reduceFee)) {
            log.error("reduceFee can not empty ");
            throw new ServiceException("promotion.param.miss");
        }

        Long fee = richSku.getFee();
        richSku.setOriginFee(fee);

        final long discount = Long.parseLong(reduceFee);
        final long actualDiscount = fee >= discount ? discount : fee;

        long afterFee = fee - actualDiscount;
        afterFee = afterFee > 0 ? afterFee : 0L;
        richSku.setFee(afterFee);
        richSku.setDiscount(actualDiscount);
    }


}
