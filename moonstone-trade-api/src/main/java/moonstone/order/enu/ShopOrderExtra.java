package moonstone.order.enu;

/**
 * <AUTHOR>
 */

public enum ShopOrderExtra {
    /**
     * 主订单后的额外数据
     */
    skipPayment("跳过支付单推送")
    , payerNo("支付人身份证")
    , payerName("支付人名称")
    , enhancedFee("被扣减的波动费用")
    , identityError("身份证错误")
    , outFrom("外部订单来源")
    , declareOk("申报成功")
    , subBankNo("子支付单号")
    , status("异常订单的原订单状态")
    , customsClearanceInfoPushState("用于标记api-v3订单的清关信息是否成功推送（true-推送成功过）")
    , shipCorp("快递公司代码")
    , orderPushErrorType("订单推送异常类型")
    , orderPushErrorDetailType("订单推送异常明细类型")
    , orderPushErrorMessage("订单推送异常信息")
    ;


    String desc;

    ShopOrderExtra(String desc) {
        this.desc = desc;
    }
}
