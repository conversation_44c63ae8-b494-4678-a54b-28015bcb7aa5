package moonstone.settle.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.settle.dto.paging.SellerTradeDailySummaryCriteria;
import moonstone.settle.model.SellerTradeDailySummary;

import java.util.Date;

/**
 * Code generated by terminus code gen
 * Desc: 读服务
 * Date: 2016-07-24
 */

public interface SellerTradeDailySummaryReadService {

    /**
     * 根据id查询
     * @param sellerTradeDailySummaryId 主键id
     * @return 
     */
    Response<SellerTradeDailySummary> findSellerTradeDailySummaryById(Long sellerTradeDailySummaryId);

    /**
     * 根据条件获取商家日汇总分页
     * @param criteria 分页条件
     * @return 商家日汇总分页
     */
    Response<Paging<SellerTradeDailySummary>> pagingSellerTradeDailySummarys(SellerTradeDailySummaryCriteria criteria);

    /**
     * 根据商家主键 和 汇总日期 和 汇总类型查询店铺日汇总
     * @param sellerId 店铺主键
     * @param sumAt 汇总日期
     * @param summaryType 汇总类型{@link moonstone.settle.enums.SummaryType}
     * @return 店铺日汇总
     */
    Response<SellerTradeDailySummary> findBySellerIdAndSumAtAndSummaryType(Long sellerId,Date sumAt,Integer summaryType);


}
