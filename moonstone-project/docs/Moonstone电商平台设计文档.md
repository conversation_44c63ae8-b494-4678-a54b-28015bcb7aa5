# Moonstone电商平台设计文档

## 1. 项目概览

### 1.1 项目简介

Moonstone是一个基于Spring Boot构建的B2B2C电商平台，采用微服务架构设计，支持多店铺、分销、支付、订单管理等完整的电商业务流程。项目采用领域驱动设计(DDD)思想，将业务功能按照领域进行模块化拆分。

### 1.2 核心功能

- **商品管理**：支持SPU/SKU商品模型，商品分类、品牌管理、库存管理
- **订单交易**：完整的订单流程，支持购物车、下单、支付、发货、确认收货
- **用户管理**：多类型用户体系，支持买家、卖家、管理员等角色
- **店铺管理**：多店铺支持，店铺装修、店铺分类管理
- **分销系统**：支持分销商申请、分销商品管理、佣金结算
- **支付系统**：集成多种支付渠道，支持支付宝、微信支付等
- **营销推广**：优惠券、促销活动、积分系统
- **数据统计**：订单统计、销售报表、用户行为分析

### 1.3 技术特点

- **微服务架构**：模块化设计，服务间松耦合
- **领域驱动设计**：按业务领域划分模块，清晰的业务边界
- **分层架构**：API层、Service层、DAO层清晰分离
- **缓存优化**：Redis缓存提升性能，避免数据竞争
- **搜索引擎**：集成Elasticsearch进行商品搜索
- **消息队列**：异步处理，提升系统响应性能
- **开放平台**：提供开放API，支持第三方集成

### 1.4 项目目标

构建一个高性能、高可用、易扩展的电商平台，支持B2B2C业务模式，为商家和消费者提供完整的电商解决方案。

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│   商家后台管理    │    用户前台      │      分销商管理          │
│  (showcase-admin)│   (showcase)    │   (showcase-we)         │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        Web层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│  moonstone-web- │ moonstone-web-  │  moonstone-web-         │
│     admin       │    front        │   distribution          │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      业务服务层                              │
├─────────┬─────────┬─────────┬─────────┬─────────┬─────────┤
│ 商品服务 │ 交易服务 │ 用户服务 │ 分销服务 │ 支付服务 │ 装修服务 │
│moonstone│moonstone│moonstone│moonstone│moonstone│moonstone│
│  -item  │ -trade  │ -user   │-distrib │  -pay   │-decorat │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      基础设施层                              │
├─────────┬─────────┬─────────┬─────────┬─────────┬─────────┤
│  MySQL  │  Redis  │Elasticsearch│ RabbitMQ│  OSS    │ 第三方API│
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

### 2.2 模块划分

#### 2.2.1 API模块（接口定义层）
- **moonstone-common**：公共组件和工具类
- **moonstone-item-api**：商品相关接口定义
- **moonstone-user-api**：用户相关接口定义
- **moonstone-trade-api**：交易相关接口定义
- **moonstone-distribution-api**：分销相关接口定义
- **moonstone-decoration-api**：装修相关接口定义

#### 2.2.2 业务实现模块
- **moonstone-item**：商品服务实现
- **moonstone-user**：用户服务实现
- **moonstone-trade**：交易服务实现
- **moonstone-distribution**：分销服务实现
- **moonstone-decoration**：装修服务实现
- **moonstone-category**：分类服务实现

#### 2.2.3 Web应用模块
- **moonstone-web-core**：Web核心组件
- **moonstone-web-front**：前台Web应用
- **moonstone-web-admin**：后台管理Web应用
- **moonstone-web-distribution**：分销Web应用

#### 2.2.4 运行时模块
- **showcase**：前台应用启动模块
- **showcase-admin**：后台管理启动模块
- **showcase-we**：分销应用启动模块

#### 2.2.5 扩展模块
- **moonstone-project**：项目扩展模块
- **moonstone-op**：运维相关模块

### 2.3 技术栈

#### 2.3.1 后端技术栈
- **框架**：Spring Boot 2.x、Spring Framework 5.x
- **数据库**：MySQL 8.0
- **缓存**：Redis 6.x
- **搜索引擎**：Elasticsearch 7.x
- **消息队列**：RabbitMQ
- **ORM框架**：MyBatis
- **连接池**：HikariCP
- **序列化**：Jackson、Fastjson
- **工具库**：Hutool、Guava、Lombok

#### 2.3.2 前端技术栈
- **前端框架**：基于模板引擎的传统Web应用
- **UI组件**：Bootstrap、jQuery
- **模板引擎**：Thymeleaf

#### 2.3.3 基础设施
- **容器化**：Docker
- **监控**：APM监控、P6Spy SQL监控
- **日志**：Logback
- **文件存储**：阿里云OSS
- **第三方服务**：微信开放平台、支付宝开放平台

### 2.4 数据流向

```
用户请求 → Web层 → 业务服务层 → 数据访问层 → 数据库
    ↓
缓存层(Redis) ← 业务服务层 → 搜索引擎(ES)
    ↓
消息队列(MQ) ← 业务服务层 → 第三方服务
```

## 3. 模块设计

### 3.1 商品模块 (moonstone-item)

#### 3.1.1 核心功能
- **商品管理**：商品的增删改查、上下架管理
- **SKU管理**：商品规格管理、库存管理
- **SPU管理**：商品模板管理
- **分类管理**：商品分类体系管理
- **品牌管理**：品牌信息管理
- **库存管理**：库存扣减、库存同步

#### 3.1.2 核心实体
- **Item**：商品实体，面向用户的商品展示单位
- **Sku**：商品规格实体，具体的可售商品单位
- **Spu**：商品模板实体，商品的标准化模板
- **Category**：商品分类实体
- **Brand**：品牌实体

#### 3.1.3 设计特点
- 采用SPU/SKU商品模型，支持多规格商品
- 使用Redis缓存库存信息，避免超卖
- 集成Elasticsearch进行商品搜索
- 支持商品信息的批量导入和同步

### 3.2 交易模块 (moonstone-trade)

#### 3.2.1 核心功能
- **购物车管理**：商品加入购物车、购物车操作
- **订单管理**：订单创建、状态流转、订单查询
- **支付管理**：支付单创建、支付状态管理
- **发货管理**：发货单创建、物流跟踪
- **售后管理**：退款、退货、换货流程

#### 3.2.2 核心实体
- **ShopOrder**：店铺维度订单
- **SkuOrder**：SKU维度子订单
- **Payment**：支付单
- **Shipment**：发货单
- **Refund**：退款单

#### 3.2.3 设计特点
- 两级订单结构：ShopOrder和SkuOrder
- 支持多种支付方式和分批支付
- 完整的订单状态机管理
- 支持多种营销活动和优惠计算

### 3.3 用户模块 (moonstone-user)

#### 3.3.1 核心功能
- **用户管理**：用户注册、登录、信息管理
- **角色权限**：多角色权限体系
- **店铺管理**：店铺信息管理、店铺认证
- **地址管理**：收货地址管理
- **会员体系**：会员等级、积分管理

#### 3.3.2 核心实体
- **User**：用户实体
- **Shop**：店铺实体
- **UserProfile**：用户详细信息
- **Address**：地址信息
- **UserExtraInformation**：用户扩展信息

#### 3.3.3 设计特点
- 多类型用户体系：买家、卖家、管理员
- 基于角色的权限控制
- 支持多种登录方式
- 完善的用户信息管理

### 3.4 分销模块 (moonstone-distribution)

#### 3.4.1 核心功能
- **分销商管理**：分销商申请、审核、管理
- **分销商品**：分销商品管理、价格管理
- **佣金管理**：佣金计算、结算、提现
- **分销订单**：分销订单跟踪、统计

#### 3.4.2 核心实体
- **WeShop**：微店实体
- **WeDistributionApplication**：分销申请
- **DistributionOrder**：分销订单
- **Commission**：佣金记录

#### 3.4.3 设计特点
- 支持多级分销体系
- 灵活的佣金计算规则
- 完整的分销商管理流程
- 分销数据统计和分析

### 3.5 支付模块 (moonstone-pay)

#### 3.5.1 核心功能
- **支付渠道**：集成多种支付渠道
- **支付处理**：支付请求处理、回调处理
- **支付安全**：签名验证、防重复支付
- **对账管理**：支付对账、异常处理

#### 3.5.2 设计特点
- 统一的支付接口设计
- 支持多种支付方式
- 完善的支付安全机制
- 支付数据的实时同步

### 3.6 Web前端模块 (moonstone-web-front)

#### 3.6.1 核心功能
- **商品展示**：商品列表、详情页面
- **购物流程**：购物车、下单、支付
- **用户中心**：个人信息、订单管理
- **店铺页面**：店铺首页、商品展示

#### 3.6.2 设计特点
- 响应式设计，支持多端访问
- 前后端分离架构
- 组件化开发
- SEO友好

### 3.7 Web后台模块 (moonstone-web-core)

#### 3.7.1 核心功能
- **商品管理**：商品发布、编辑、管理
- **订单管理**：订单处理、发货管理
- **用户管理**：用户信息管理、权限管理
- **数据统计**：销售统计、用户分析

#### 3.7.2 设计特点
- 基于角色的权限控制
- 丰富的数据统计功能
- 批量操作支持
- 操作日志记录

## 4. 核心业务流程

### 4.1 商品管理流程

```mermaid
graph TD
    A[商品信息录入] --> B[商品审核]
    B --> C{审核通过?}
    C -->|是| D[商品上架]
    C -->|否| E[修改商品信息]
    E --> B
    D --> F[商品展示]
    F --> G[库存管理]
    G --> H[商品下架]
```

#### 4.1.1 商品发布流程
1. 商家录入商品基本信息（名称、描述、价格等）
2. 上传商品图片和详情
3. 设置商品规格和库存
4. 选择商品分类和品牌
5. 提交审核
6. 平台审核通过后商品上架

#### 4.1.2 库存管理流程
1. 商品库存初始化
2. 订单下单时预扣库存
3. 支付成功后确认扣库存
4. 支付失败或取消订单时释放库存
5. 库存不足时自动下架商品

### 4.2 订单交易流程

```mermaid
graph TD
    A[商品加入购物车] --> B[确认订单信息]
    B --> C[选择支付方式]
    C --> D[创建订单]
    D --> E[发起支付]
    E --> F{支付成功?}
    F -->|是| G[订单支付成功]
    F -->|否| H[支付失败]
    G --> I[商家发货]
    I --> J[买家确认收货]
    J --> K[交易完成]
    H --> L[订单取消]
```

#### 4.2.1 下单流程
1. 用户浏览商品，选择规格和数量
2. 加入购物车或直接购买
3. 确认订单信息（收货地址、配送方式等）
4. 选择支付方式
5. 创建订单（ShopOrder和SkuOrder）
6. 跳转到支付页面

#### 4.2.2 支付流程
1. 创建支付单（Payment）
2. 调用支付渠道接口
3. 用户完成支付
4. 接收支付回调通知
5. 验证支付结果
6. 更新订单状态

#### 4.2.3 发货流程
1. 商家确认订单
2. 准备商品和包装
3. 创建发货单（Shipment）
4. 选择物流公司
5. 生成物流单号
6. 更新订单状态为已发货

### 4.3 支付流程

```mermaid
graph TD
    A[创建支付单] --> B[选择支付渠道]
    B --> C[生成支付请求]
    C --> D[跳转支付页面]
    D --> E[用户完成支付]
    E --> F[接收支付回调]
    F --> G[验证支付结果]
    G --> H{验证通过?}
    H -->|是| I[更新支付状态]
    H -->|否| J[支付失败处理]
    I --> K[更新订单状态]
    K --> L[发送支付成功通知]
```

#### 4.3.1 支付渠道集成
- 支付宝PC支付
- 支付宝手机支付
- 微信支付
- 银联支付
- 通联支付
- 汇付支付

#### 4.3.2 支付安全机制
- 签名验证
- 防重复支付
- 支付金额校验
- 支付超时处理

### 4.4 分销流程

```mermaid
graph TD
    A[用户申请成为分销商] --> B[提交申请资料]
    B --> C[平台审核]
    C --> D{审核通过?}
    D -->|是| E[开通分销权限]
    D -->|否| F[申请被拒绝]
    E --> G[选择分销商品]
    G --> H[推广商品]
    H --> I[产生分销订单]
    I --> J[计算佣金]
    J --> K[佣金结算]
```

#### 4.4.1 分销商申请流程
1. 用户提交分销申请
2. 填写个人/企业信息
3. 上传相关证件
4. 平台审核申请
5. 审核通过后开通分销权限

#### 4.4.2 佣金计算流程
1. 分销订单确认收货
2. 根据佣金规则计算佣金
3. 生成佣金记录
4. 佣金结算到分销商账户

## 5. 数据库设计

### 5.1 核心表结构

#### 5.1.1 商品相关表
- **items**：商品主表
- **skus**：SKU表
- **spus**：SPU表
- **categories**：分类表
- **brands**：品牌表
- **item_details**：商品详情表

#### 5.1.2 订单相关表
- **shop_orders**：店铺订单表
- **sku_orders**：SKU订单表
- **payments**：支付单表
- **shipments**：发货单表
- **refunds**：退款单表

#### 5.1.3 用户相关表
- **users**：用户表
- **shops**：店铺表
- **user_profiles**：用户详情表
- **addresses**：地址表
- **user_extra_information**：用户扩展信息表

#### 5.1.4 分销相关表
- **we_shops**：微店表
- **we_distribution_applications**：分销申请表
- **distribution_orders**：分销订单表
- **commissions**：佣金表

### 5.2 数据关系图

```
Users (1:N) Shops (1:N) Items (1:N) Skus
  |                                    |
  |                                    |
(1:N)                                (N:1)
  |                                    |
ShopOrders (1:N) SkuOrders ←----------┘
  |
(1:N)
  |
Payments
```

### 5.3 索引设计

#### 5.3.1 主要索引
- **商品表**：shop_id, category_id, status, created_at
- **订单表**：buyer_id, shop_id, status, created_at
- **用户表**：mobile, email, name
- **SKU表**：item_id, shop_id, outer_sku_id

#### 5.3.2 复合索引
- **订单查询**：(buyer_id, status, created_at)
- **商品搜索**：(shop_id, category_id, status)
- **库存管理**：(item_id, shop_id, status)

## 6. API设计

### 6.1 RESTful API规范

#### 6.1.1 URL设计规范
- 使用名词复数形式：`/api/items`、`/api/orders`
- 使用HTTP方法表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 使用路径参数表示资源ID：`/api/items/{id}`
- 使用查询参数进行过滤和分页：`/api/items?category=1&page=1&size=20`

#### 6.1.2 响应格式规范
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": 1640995200000
}
```

#### 6.1.3 错误处理规范
```json
{
  "success": false,
  "data": null,
  "message": "参数错误",
  "code": 400,
  "timestamp": 1640995200000
}
```

### 6.2 开放平台API

#### 6.2.1 认证机制
- 使用AppKey和AppSecret进行身份认证
- 采用签名机制防止请求被篡改
- 支持访问频率限制

#### 6.2.2 签名算法
1. 将所有请求参数按字母序排列
2. 拼接成查询字符串
3. 附加AppSecret
4. 计算MD5签名

#### 6.2.3 开放接口
- 商品信息查询
- 订单信息查询
- 库存信息查询
- 物流信息查询

### 6.3 内部服务API

#### 6.3.1 服务间通信
- 使用RPC框架进行服务间调用
- 统一的服务接口定义
- 服务注册与发现

#### 6.3.2 接口版本管理
- 使用版本号进行接口版本控制
- 向后兼容原则
- 废弃接口的平滑迁移

## 7. 部署架构

### 7.1 环境要求

#### 7.1.1 硬件要求
- **CPU**：8核心以上
- **内存**：16GB以上
- **存储**：SSD 500GB以上
- **网络**：千兆网络

#### 7.1.2 软件要求
- **操作系统**：Linux (CentOS 7+/Ubuntu 18+)
- **Java**：JDK 17+
- **数据库**：MySQL 8.0+
- **缓存**：Redis 6.0+
- **搜索引擎**：Elasticsearch 7.x
- **消息队列**：RabbitMQ 3.8+

### 7.2 部署拓扑

```
                    ┌─────────────┐
                    │   负载均衡   │
                    │   (Nginx)   │
                    └─────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
┌─────────────┐   ┌─────────────┐   ┌─────────────┐
│  前台应用    │   │  后台应用    │   │  分销应用    │
│ (showcase)  │   │(showcase-   │   │(showcase-   │
│             │   │  admin)     │   │    we)      │
└─────────────┘   └─────────────┘   └─────────────┘
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
┌─────────────┐   ┌─────────────┐   ┌─────────────┐
│   MySQL     │   │    Redis    │   │Elasticsearch│
│   主从集群   │   │   集群      │   │   集群      │
└─────────────┘   └─────────────┘   └─────────────┘
```

### 7.3 配置管理

#### 7.3.1 配置文件结构
```
application.yml          # 基础配置
application-dev.yml      # 开发环境配置
application-test.yml     # 测试环境配置
application-prod.yml     # 生产环境配置
```

#### 7.3.2 环境变量配置
- 数据库连接信息
- Redis连接信息
- 第三方服务配置
- 文件存储配置

#### 7.3.3 配置中心
- 支持配置的动态更新
- 配置版本管理
- 配置安全加密

## 8. 扩展性设计

### 8.1 微服务化方案

#### 8.1.1 服务拆分原则
- 按业务领域拆分
- 单一职责原则
- 数据独立性
- 服务自治性

#### 8.1.2 服务治理
- 服务注册与发现
- 负载均衡
- 熔断降级
- 链路追踪

#### 8.1.3 数据一致性
- 分布式事务处理
- 最终一致性
- 补偿机制
- 幂等性设计

### 8.2 缓存策略

#### 8.2.1 多级缓存
- **L1缓存**：本地缓存（Caffeine）
- **L2缓存**：分布式缓存（Redis）
- **L3缓存**：CDN缓存

#### 8.2.2 缓存模式
- **Cache-Aside**：旁路缓存模式
- **Write-Through**：写透模式
- **Write-Behind**：写回模式

#### 8.2.3 缓存更新策略
- TTL过期策略
- 主动更新策略
- 缓存预热策略

### 8.3 性能优化

#### 8.3.1 数据库优化
- 读写分离
- 分库分表
- 索引优化
- 查询优化

#### 8.3.2 应用优化
- 连接池优化
- 线程池优化
- JVM参数调优
- 代码优化

#### 8.3.3 架构优化
- 异步处理
- 批量操作
- 预计算
- 数据预加载

---

## 附录

### A. 技术选型说明

本项目采用的技术栈都是经过生产环境验证的成熟技术，具有以下优势：

1. **Spring Boot**：简化配置，快速开发
2. **MySQL**：成熟稳定，支持ACID事务
3. **Redis**：高性能缓存，支持多种数据结构
4. **Elasticsearch**：强大的搜索和分析能力
5. **RabbitMQ**：可靠的消息传递

### B. 开发规范

#### B.1 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化规则
- 必要的注释和文档
- 单元测试覆盖率要求

#### B.2 数据库规范
- 统一的命名规范
- 必要的索引设计
- 数据库版本管理
- 备份和恢复策略

#### B.3 接口规范
- RESTful API设计原则
- 统一的响应格式
- 完整的接口文档
- 接口版本管理

### C. 运维监控

#### C.1 监控指标
- 应用性能监控（APM）
- 数据库性能监控
- 缓存性能监控
- 业务指标监控

#### C.2 日志管理
- 统一的日志格式
- 日志级别管理
- 日志收集和分析
- 日志归档策略

#### C.3 告警机制
- 系统异常告警
- 性能指标告警
- 业务指标告警
- 告警升级机制

---

*本文档版本：v1.0*  
*最后更新时间：2024年12月*  
*文档维护者：开发团队*
