package moonstone.showcase.mq;

import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.OpenConfig;
import com.allinpay.sdk.util.SecretUtils;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.utils.StringUtils;
import moonstone.user.entity.UserBindAllinSignature;
import moonstone.user.service.UserBindAllinSignatureService;
import moonstone.web.core.util.OSSClientUtils;
import moonstone.showcase.mq.message.SignConfirmationMessage;
import moonstone.web.op.allinpay.AllinPayFactory;
import moonstone.web.op.allinpay.constant.AllinPayConstant;
import moonstone.web.op.allinpay.dto.signature.req.DownloadContractFileDTO;
import moonstone.web.op.allinpay.dto.signature.res.DownloadContractFileResponse;
import moonstone.web.op.allinpay.service.AllinPaySignature;
import moonstone.web.op.allinpay.util.NacosAutoConfiguration;
import org.apache.http.*;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.security.PrivateKey;
import java.util.*;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = "ALLINPAY_SIGN_CONFIRMATION_TOPIC",
        consumerGroup = "ALLINPAY_SIGN_CONFIRMATION_TOPIC",
        enableMsgTrace = false
)
public class SignConfirmationConsumer implements RocketMQListener<SignConfirmationMessage> {

    @Resource
    private UserBindAllinSignatureService userBindAllinSignatureService;

    @Resource
    private OSSClientUtils ossClientUtils;

//    @Resource
//    private AllinSignatureProperties allinSignatureProperties;

    @Resource
    private AllinPaySignature allinPaySignature;

    @Resource
    private NacosAutoConfiguration nacosAutoConfiguration;

    @Resource
    private AllinPayFactory allinPayFactory;

//    /**
//     * 测试环境-文件系统appId
//     */
//    private static final String APPID = "1596041822008590337";
//    /**
//     * 测试环境-文件系统证书
//     */
//    public static final String privateKeyPath = "F:\\\\codeup_supply\\\\mall\\\\showcase\\\\src\\\\main\\\\resources\\\\allinpay\\\\test\\\\signature\\\\1596041822008590337(RSA2).pfx";
//    /**
//     * 测试环境-文件系统证书密码
//     */
//    public static final String pwd = "123456";
//    /**
//     * 测试环境-文件下载地址
//     */
//    public static final String DOWNLOADFILEURL = "http://test.allinpay.com/file/open/downloadFile";

    @Override
    public void onMessage(SignConfirmationMessage message) {
        log.info("收到签约确认消息 {}", message);
        UserBindAllinSignature userBindAllinSignature = userBindAllinSignatureService.getByShopIdAndUserId(message.getShopId(), message.getUserId());
        if(userBindAllinSignature == null){
            log.error("通联会员不存在 shopId:{} userId:{}", message.getShopId(), message.getUserId());
            return;
        }
        String merId = nacosAutoConfiguration.getValue(AllinPayConstant.Signature.MER_ID);
        if(StringUtils.isEmpty(merId)){
            log.error("merId为空");
            throw new RuntimeException("merId为空");
        }
        String downloadUrl = nacosAutoConfiguration.getValue(AllinPayConstant.Signature.DOWNLOAD_URL);
        if(StringUtils.isEmpty(downloadUrl)){
            log.error("downloadUrl为空");
            throw new RuntimeException("downloadUrl为空");
        }

        DownloadContractFileDTO genContractFileByTmpDTO = new DownloadContractFileDTO();
        genContractFileByTmpDTO.setShopId(message.getShopId());
        genContractFileByTmpDTO.setMerId(merId);
        genContractFileByTmpDTO.setCustOrderId(String.valueOf(System.currentTimeMillis()));
        genContractFileByTmpDTO.setAccount(userBindAllinSignature.getSignatureAccountId());
        genContractFileByTmpDTO.setFid(message.getSignatureFid());

        // 1. 签署成功后  调用downloadContractFile获取下载合同所需的token
        Result<DownloadContractFileResponse> result = allinPaySignature.downloadContractFile(genContractFileByTmpDTO);
        if(!result.isSuccess()){
            log.error("下载文件失败 {} {} {}", message.getShopId(), message.getUserId(), result.getErrorMsg());
            throw new RuntimeException(result.getErrorMsg());
        }
        DownloadContractFileResponse downloadContractFileResponse = result.getData();
        String token = downloadContractFileResponse.getInfo().getToken();

        OpenConfig openConfig = allinPayFactory.getConfig(message.getShopId());
        OpenClient openClient = allinPayFactory.get(message.getShopId());

        // 2. 下载合同
        HttpResponse response;
        try {
            response = downloadFile2(token, downloadUrl, openConfig, openClient);
        } catch (Exception e) {
            log.error("下载文件失败 {} {} {}", message.getShopId(), message.getUserId(), e.getMessage());
            throw new RuntimeException(e);
        }

        // 3. 上传到oss
        URL url;
        try {
            String fileName = userBindAllinSignature.getShopId() + "-" + userBindAllinSignature.getUserId() + "-allinSignature.pdf";
            url = uploadToOSS(response, fileName);
        } catch (IOException e) {
            log.error("上传到oss失败 {} {} {}", message.getShopId(), message.getUserId(), e.getMessage());
            throw new RuntimeException(e);
        }

        String ossPath = url.getProtocol() + "://" + url.getHost() + url.getPath();
        log.info("ossPath : {}", ossPath);

        UserBindAllinSignature updateEntity = new UserBindAllinSignature();
        updateEntity.setId(userBindAllinSignature.getId());
        updateEntity.setSignatureOssUrl(ossPath);
        updateEntity.setUpdatedAt(new Date());
        userBindAllinSignatureService.updateById(updateEntity);
        log.info("签约确认成功 {} {}", message.getShopId(), message.getUserId());
    }

    private URL uploadToOSS(HttpResponse response, String fileName) throws IOException {
        // 处理返回结果
        if (response.getStatusLine().getStatusCode() == 200) {
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                InputStream is = responseEntity.getContent();
                String fileName2 = getFileName(response);
                Date date = new Date();

                try {
                    byte[] bytes = is.readAllBytes();
                    URL url = ossClientUtils.uploadObject(bytes, fileName, date);
                    return url;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public HttpResponse downloadFile2(final String tokenStr, String downloadUrl, OpenConfig openConfig, OpenClient openClient) {
        try {
            // 创建HttpClient
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(downloadUrl);// 测试环境
            List<NameValuePair> parameters = new ArrayList<>();

            // 添加参数
            parameters.add(new BasicNameValuePair("appId", openConfig.getAppId()));
            parameters.add(new BasicNameValuePair("timestamp", System.currentTimeMillis() + ""));
            parameters.add(new BasicNameValuePair("token", tokenStr));

            // 加签
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("appId", openConfig.getAppId());
            resMap.put("timestamp", System.currentTimeMillis() + "");
            resMap.put("token", tokenStr);
            String source = getSignedValue(resMap);

//            // 这里应该是访问了证书文件
//            RSAPrivateKey privateKey = (RSAPrivateKey) SecretUtils.loadPrivateKey(null, openConfig.getCertPath(), openConfig.getCertPwd());
            PrivateKey privateKey = openClient.getPrivateKey();
            String sign = SecretUtils.sign(privateKey, source, "SHA256WithRSA");
            parameters.add(new BasicNameValuePair("sign", sign));

            // 设置请求体
            httpPost.setEntity(new UrlEncodedFormEntity(parameters));

            // 执行提交
            HttpResponse response = httpClient.execute(httpPost);
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private static String getSignedValue(final Map<String, Object> params) {
        final Map<String, Object> copy = new TreeMap<>();
        params.forEach((k, v) -> {
            if (v != null) {
                copy.put(k, v);
            }
        });
        copy.remove("sign");
        final StringBuilder sb = new StringBuilder();
        copy.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
        return sb.length() == 0 ? "" : sb.substring(0, sb.length() - 1);
    }


    /**
     * 获取response header中Content-Disposition中的filename值
     *
     * @param response
     * @return
     */
    public static String getFileName(HttpResponse response) {
        Header contentHeader = response.getFirstHeader("Content-Disposition");
        String filename = null;
        if (contentHeader != null) {
            HeaderElement[] values = contentHeader.getElements();
            if (values.length == 1) {
                NameValuePair param = values[0].getParameterByName("filename");
                if (param != null) {
                    try {
                        filename = param.getValue();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return filename;
    }

}
