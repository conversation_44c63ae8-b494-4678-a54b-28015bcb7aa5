package moonstone.web.front.shop;

import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.Customs;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDTO;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.model.ShopPayCustoms;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayCustomsReadService;
import moonstone.shop.service.ShopPayCustomsWriteService;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.shop.service.ShopPayInfoWriteService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.events.shop.ShopPayInfoChangeEvent;
import moonstone.web.core.events.shop.ShopPayInfoDeleteEvent;
import moonstone.web.core.model.YunAccount;
import moonstone.web.core.registers.shop.TokenRegister;
import moonstone.web.core.shop.application.ShopPayInfoComponent;
import moonstone.web.core.shop.application.ShopYunAccountManager;
import moonstone.web.core.util.LockKeyUtils;
import moonstone.web.front.shop.convert.ShopPayInfoConvertor;
import moonstone.web.front.shop.dto.ShopPayInfoGreyReleaseRequest;
import org.redisson.api.RedissonClient;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;


/**
 * 店铺支付信息
 */
@RestController
@Slf4j
public class ShopPayInfos {

    @Resource
    private ShopYunAccountManager shopYunAccountManager;

    @Resource
    private ShopPayInfoWriteService shopPayInfoWriteService;

    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;

    @Resource
    private ShopPayCustomsReadService shopPayCustomsReadService;

    @Resource
    private ShopPayCustomsWriteService shopPayCustomsWriteService;

    @Resource
    private ShopPayInfoConvertor shopPayInfoConvertor;

    @Resource
    private ShopPayInfoComponent shopPayInfoComponent;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private TokenRegister tokenRegister;

    /**
     * 新增店铺支付信息
     *
     * @param shopPayInfo 支付信息
     * @return 新增记录ID
     */
    @Deprecated
    @RequestMapping(method = RequestMethod.POST, path = "/api/shop/profile/pay", produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createShopPayInfo(@RequestBody ShopPayInfo shopPayInfo) {
        try {
            //获取当前登录账户的店铺ID
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();
            //判断该店铺是否已有该类型的payInfo
            checkRepeat(null, shopId, shopPayInfo.getPayChannel());
            //设置所属店铺ID
            shopPayInfo.setShopId(shopId);
            //设置默认状态
            shopPayInfo.setStatus(ShopPayInfoStatusEnum.INACTIVE.getCode());
            Response<Long> response = shopPayInfoWriteService.create(shopPayInfo);
            if (!response.isSuccess()) {
                log.error("failed to create shopPayInfo, cause:{}", response.getError());
                throw new JsonResponseException(response.getError());
            }
            //发出支付信息改变事件
            EventSender.publish(new ShopPayInfoChangeEvent(shopPayInfo));
            return response.getResult();
        } catch (Exception e) {
            log.error("{} failed to create shopPayInfo, cause:", LogUtil.getClassMethodName(), e);
            throw new JsonResponseException("shop.pay.info.create.fail");
        }
    }

    /**
     * 新增支付配置
     *
     * @param file
     * @param payChannel
     * @param detail
     * @return
     */
    @PostMapping("/api/shop/profile/pay/create")
    public Result<Boolean> create(@RequestParam(name = "file", required = false) MultipartFile file,
                                  @RequestParam(name = "file2", required = false) MultipartFile file2,
                                  @RequestParam("payChannel") String payChannel, @RequestParam("detail") String detail) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoCreate(user.getShopId()));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }
        try {
            var shopPayInfo = shopPayInfoConvertor.convert(user.getShopId(), payChannel, detail, file);

            // 同一支付渠道，只能有一个配置
            checkRepeat(null, user.getShopId(), shopPayInfo.getPayChannel());

            return Result.data(shopPayInfoComponent.create(shopPayInfo, file, file2));
        } catch (Exception ex) {
            log.error("ShopPayInfos.create error, shopId={}, payChannel={}, detail={} ", user.getShopId(), payChannel, detail, ex);
            return Result.fail(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 根据登录用户查找支付配置信息
     *
     * @return 支付记录
     */
    @RequestMapping(value = "/api/shop/profile/pay/list", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<ShopPayInfoDTO>> findShopPayInfoByShopId() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {

            return Result.fail("请重新登录");
        }

        try {
            var list = shopPayInfoReadService.findByShopId(user.getShopId()).getResult();

            return Result.data(shopPayInfoConvertor.convert(list));
        } catch (Exception e) {
            log.error("shopPayInfos.findShopPayInfoByShopId error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * llyj
     */
    @RequestMapping(value = "/api/shop/profile/pay/list/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<ShopPayInfo>> findShopPayInfoByShopIdNew() {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            //查找支付信息
            Response<List<ShopPayInfo>> rShopPayInfoList = shopPayInfoReadService.findByShopId(commonUser.getShopId());
            if (!rShopPayInfoList.isSuccess()) {
                log.error("failed to find ShopPayInfo by id = {}", rShopPayInfoList);
                throw new JsonResponseException(rShopPayInfoList.getError());
            }
            for (ShopPayInfo shopPayInfo : rShopPayInfoList.getResult()) {
                //查找支付海关信息
                Response<List<ShopPayCustoms>> rShopPayCustoms = shopPayCustomsReadService.findListByPayInfoId(shopPayInfo.getId());
                if (!rShopPayCustoms.isSuccess()) {
                    log.warn("not find ShopPayCustoms by payInfoId = {},error : {}", shopPayInfo.getId(), rShopPayCustoms.getError());
                }
                shopPayInfo.encryptionSecretKey();
                shopPayInfo.setHasChildren(!CollectionUtils.isEmpty(rShopPayCustoms.getResult()));
                shopPayInfo.setShopPayCustoms(rShopPayCustoms.getResult());
            }
            return Result.data(rShopPayInfoList.getResult());
        } catch (Exception e) {
            log.error("{} not find ShopPayInfo,cause:", LogUtil.getClassMethodName(), e);
            throw new JsonResponseException("shop.pay.info.not.find");
        }
    }

    /**
     * 更改店铺支付信息
     */
    @RequestMapping(value = "/api/shop/profile/pay/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> updateShopPayInfoById(@RequestParam(name = "file", required = false) MultipartFile file,
                                                 @RequestParam(name = "file2", required = false) MultipartFile file2,
                                                 @RequestParam("payChannel") String payChannel, @RequestParam("detail") String detail,
                                                 @PathVariable("id") Long shopPayInfoId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }

        try {
            // 查询
            var existedObject = shopPayInfoReadService.getById(shopPayInfoId).getResult();

            // 构造待更新对象
            var updateObject = shopPayInfoConvertor.convert(commonUser.getShopId(), existedObject, payChannel, detail, file);

            // 更新
            return Result.data(shopPayInfoComponent.update(updateObject, file, file2));
        } catch (Exception e) {
            log.error("ShopPayInfos.updateShopPayInfoById error, shopPayInfoId={}, payChannel={}, detail={}",
                    shopPayInfoId, payChannel, detail, e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 删除支付信息记录
     *
     * @param shopPayInfoId 支付记录ID
     * @return 成功返回true
     */
    @RequestMapping(value = "/api/shop/profile/pay/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deleteShopPayInfoById(@PathVariable("id") Long shopPayInfoId) {
        try {
            //查找当前登录账户的店铺ID
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();

            //检查操作权限
            checkShopPayInfoUser(shopId, shopPayInfoId);

            Response<ShopPayInfo> rShopPayInfo = shopPayInfoReadService.findById(shopPayInfoId);
            if (!rShopPayInfo.isSuccess()) {
                log.error("fail to find shopPayInfo, by id={}, cause:{}", shopPayInfoId, rShopPayInfo.getError());
                throw new JsonResponseException(rShopPayInfo.getError());
            }
            ShopPayInfo shopPayInfo = rShopPayInfo.getResult();
            shopPayInfo.setStatus(ShopPayInfoStatusEnum.DELETED.getCode());

            Response<Boolean> resp = shopPayInfoWriteService.update(shopPayInfo);
            if (!resp.isSuccess()) {
                log.error("delete shopPayInfo fail by id = {},cause={}", shopPayInfoId, resp.getError());
                throw new JsonResponseException(resp.getError());
            }
            //发出支付信息删除事件
            EventSender.publish(new ShopPayInfoDeleteEvent(shopPayInfo.getShopId(), shopPayInfo.getPayChannel()));
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to update shopPayInfo by id = {}, cause:", LogUtil.getClassMethodName(), shopPayInfoId, e);
            throw new JsonResponseException("shop.pay.info.delete.fail");
        }
    }

    /**
     * 检查登录用户对该支付记录是否有操作权限
     *
     * @param shopId        店铺ID
     * @param shopPayInfoId 支付记录ID
     */
    private void checkShopPayInfoUser(Long shopId, Long shopPayInfoId) {
        //查找原有的记录
        Response<ShopPayInfo> rShopPayInfoOld = shopPayInfoReadService.findById(shopPayInfoId);
        if (!rShopPayInfoOld.isSuccess()) {
            log.error("shopPayInfo is not find by id = {}", shopPayInfoId);
            throw new JsonResponseException(rShopPayInfoOld.getError());
        }
        ShopPayInfo shopPayInfoOld = rShopPayInfoOld.getResult();
        //检查修改的记录是否属于当前登录用户
        if (!shopPayInfoOld.getShopId().equals(shopId)) {
            log.error("shopPayInfo not belong to user");
            throw new JsonResponseException("shop.pay.info.not.belong.to.user");
        }
    }

    /**
     * 检查该店铺是否已存在该支付渠道的支付信息
     *
     * @param id      店铺支付信息id
     * @param shopId  店铺id
     * @param channel 支付渠道
     */
    private void checkRepeat(Long id, Long shopId, String channel) {
        //判断该店铺是否已有该类型的payInfo
        var rShopPayInfo = shopPayInfoReadService.findAllByShopIdAndPayChannel(shopId, ShopPayInfoPayChannelEnum.parse(channel),
                List.of(ShopPayInfoStatusEnum.ACTIVE, ShopPayInfoStatusEnum.GRAY_RELEASE, ShopPayInfoStatusEnum.INACTIVE));
        if (!rShopPayInfo.isSuccess()) {
            log.error("failed to find shopPayInfo, cause:{}", rShopPayInfo.getError());
            throw new JsonResponseException(rShopPayInfo.getError());
        }

        var shopPayInfos = rShopPayInfo.getResult();
        if (!CollectionUtils.isEmpty(shopPayInfos) && shopPayInfos.stream().noneMatch(shopPayInfo -> shopPayInfo.getId().equals(id))) {
            log.error("shopPayInfo(shopId={}, payChannel={}) is exist", shopId, channel);
            throw new RuntimeException("该渠道的店铺支付信息已存在");
        }
    }

    /**
     * 新增支付海关信息
     *
     * @param shopPayCustoms 支付海关信息
     * @return 新增记录ID
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createShopPayCustoms(@RequestBody ShopPayCustoms shopPayCustoms) {
        try {
            //获取当前登录用户
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();
            //将该记录的拥有者设为当前登录用户
            shopPayCustoms.setShopId(shopId);
            shopPayCustoms.setStatus(1);
            shopPayCustoms.setCustomsName(Customs.fromInt(shopPayCustoms.getCustomsCode()).Name());
            //添加操作
            Response<Long> resp = shopPayCustomsWriteService.create(shopPayCustoms);
            if (!resp.isSuccess()) {
                log.error("failed ro create shopPayCustoms {}", shopPayCustoms);
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to create shopPayCustoms {}, cause:", LogUtil.getClassMethodName(), shopPayCustoms, e);
            throw new JsonResponseException("shop.pay.customs.create.fail");
        }
    }

    /**
     * 根据ID查找支付海关信息
     *
     * @param shopPayCustomsId ID
     * @return 支付海关信息
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ShopPayCustoms findShopPayCustomsById(@PathVariable("id") Long shopPayCustomsId) {
        try {
            Response<ShopPayCustoms> rShopPayCustoms = shopPayCustomsReadService.findById(shopPayCustomsId);
            if (!rShopPayCustoms.isSuccess()) {
                log.error("failed to find shopPayCustoms by id = {}", shopPayCustomsId);
                throw new JsonResponseException(rShopPayCustoms.getError());
            }
            return rShopPayCustoms.getResult();
        } catch (Exception e) {
            log.error("{} failed to find shopPayCustoms by id = {}, cause:", LogUtil.getClassMethodName(), shopPayCustomsId, e);
            throw new JsonResponseException("shop.pay.customs.not.find");
        }
    }

    /**
     * 编辑支付海关信息
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateShopPayCustomsById(@RequestBody ShopPayCustoms shopPayCustoms, @PathVariable("id") Long shopPayCustomsId) {
        try {
            //检查用户操作权限
            checkUserShopPayCustoms(shopPayCustomsId);

            shopPayCustoms.setId(shopPayCustomsId);
            shopPayCustoms.setStatus(null);
            shopPayCustoms.setShopId(null);
            shopPayCustoms.setPayInfoId(null);
            shopPayCustoms.setCustomsName(Customs.fromInt(shopPayCustoms.getCustomsCode()).Name());

            Response<Boolean> resp = shopPayCustomsWriteService.update(shopPayCustoms);
            if (!resp.isSuccess()) {
                log.error("failed to update shopPayCustoms {}", shopPayCustoms);
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to update shopPayCustoms {}, cause:", LogUtil.getClassMethodName(), shopPayCustoms, e);
            throw new JsonResponseException("shop.pay.customs.update.fail");
        }
    }

    /**
     * 删除支付海关信息
     *
     * @param shopPayCustomsId 记录ID
     * @return 成功返回true
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deleteShopPayCustomsById(@PathVariable("id") Long shopPayCustomsId) {
        try {
            //检查操作权限
            checkUserShopPayCustoms(shopPayCustomsId);

            ShopPayCustoms shopPayCustoms = new ShopPayCustoms();
            shopPayCustoms.setId(shopPayCustomsId);
            shopPayCustoms.setStatus(-1);
            Response<Boolean> resp = shopPayCustomsWriteService.update(shopPayCustoms);
            if (!resp.isSuccess()) {
                log.error("failed to delete shopPayCustoms by id = {}", shopPayCustomsId);
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to delete shopPayCustoms by id = {}, cause:", LogUtil.getClassMethodName(), shopPayCustomsId, e);
            throw new JsonResponseException("shop.pay.customs.delete.fail");
        }
    }

    @RequestMapping(value = "/api/shop/profile/pay/customs/list", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Map<String, Object>> customsList() {
        List<Map<String, Object>> result = new ArrayList<>();

        for (Customs customs : Customs.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("customsCode", customs.Code());
            map.put("customsName", customs.Name());
            result.add(map);
        }

        return result;
    }

    private void checkUserShopPayCustoms(Long shopPayCustomsId) {
        //获取登录用户
        CommonUser user = UserUtil.getCurrentUser();
        Long shopId = user.getShopId();
        //获取原有记录
        Response<ShopPayCustoms> rShopPayCustomsOld = shopPayCustomsReadService.findById(shopPayCustomsId);
        if (!rShopPayCustomsOld.isSuccess()) {
            log.error("failed to find ShopPayCustoms by id = {}", shopPayCustomsId);
            throw new JsonResponseException("shop.pay.customs.not.find");
        }
        //检查shopId是否一致
        if (!shopId.equals(rShopPayCustomsOld.getResult().getShopId())) {
            log.error("failed to find ShopPayCustoms by id = {}", shopPayCustomsId);
            throw new JsonResponseException("shop.pay.customs.not.belong.to.user");
        }
    }

    @PostMapping("/api/shop/profile/pay/yunAccount/add")
    public boolean addYunAccount(String appKey, String desKey, String brokerId, String dealerId) {
        CommonUser shopUser = UserUtil.getCurrentUser();
        if (shopUser == null || shopUser.getShopId() == null) {
            throw new RuntimeException(new Translate("未登录或者非店铺帐号").toString());
        }
        YunAccount yunAccount = new YunAccount();
        yunAccount.setAppKey(appKey);
        yunAccount.setKey_3Des(desKey);
        yunAccount.setDealerId(dealerId);
        yunAccount.setBrokerId(brokerId);
        yunAccount.setShopId(shopUser.getShopId());
        yunAccount.setCreatedAt(new Date());
        return shopYunAccountManager.saveYunAccount(yunAccount).orElse(false);
    }

    @PostMapping("/api/shop/profile/pay/yunAccount/modify")
    public boolean modify(String appKey, String desKey, String brokerId, String dealerId) {
        CommonUser shopUser = UserUtil.getCurrentUser();
        if (shopUser == null || shopUser.getShopId() == null) {
            throw new RuntimeException(new Translate("未登录或者非店铺帐号").toString());
        }
        YunAccount yunAccount = shopYunAccountManager.getOneByShopId(shopUser.getShopId()).orElse(null);
        if (yunAccount == null) {
            throw new RuntimeException(new Translate("该数据不正常请刷新重试").toString());
        }
        if (null != appKey) {
            yunAccount.setAppKey(appKey);
        }
        if (null != desKey) {
            yunAccount.setKey_3Des(desKey);
        }
        if (null != brokerId) {
            yunAccount.setBrokerId(brokerId);
        }
        if (null != dealerId) {
            yunAccount.setDealerId(dealerId);
        }
        return shopYunAccountManager.saveYunAccount(yunAccount).orElse(false);
    }

    /**
     * 全量发布
     *
     * @param id
     * @return
     */
    @PostMapping("/api/shop/profile/pay/{id}/fullRelease")
    public Result<Boolean> fullRelease(@PathVariable("id") Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoFullRelease(shopId));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }

        try {
            var target = shopPayInfoReadService.getById(id).getResult();
            fullReleaseCheck(target, shopId);

            // 发布
            var deleteList = shopPayInfoComponent.fullRelease(target);
            tokenRegister.regPayTokenFromShopPayInfo(shopPayInfoReadService.getById(id).getResult());

            // 事件通知
            EventSender.publish(new ShopPayInfoChangeEvent(target));
            Optional.of(deleteList).ifPresent(list -> list.forEach(deleteObject ->
                    EventSender.publish(new ShopPayInfoDeleteEvent(deleteObject.getShopId(), deleteObject.getPayChannel()))));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.fullRelease error, id={}", id, ex);
            return Result.fail(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }

    private void fullReleaseCheck(ShopPayInfo target, Long shopId) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.GRAY_RELEASE.getCode().equals(target.getStatus()) &&
                !ShopPayInfoStatusEnum.INACTIVE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
    }

    /**
     * 灰度发布
     *
     * @param request
     * @return
     */
    @PostMapping("/api/shop/profile/pay/greyRelease")
    public Result<Boolean> greyRelease(@RequestBody ShopPayInfoGreyReleaseRequest request) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoGreyRelease(shopId));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }

        try {
            var target = shopPayInfoReadService.getById(request.getShopPayInfoId()).getResult();
            greyReleaseCheck(target, shopId, request);

            // 发布
            shopPayInfoComponent.greyRelease(target, request.getMembers());
            tokenRegister.regPayTokenFromShopPayInfo(shopPayInfoReadService.getById(request.getShopPayInfoId()).getResult());

            // 事件通知
            EventSender.publish(new ShopPayInfoChangeEvent(target));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.greyRelease error, shopId={}, request={}", shopId, JSON.toJSONString(request), ex);
            return Result.fail(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }

    private void greyReleaseCheck(ShopPayInfo target, Long shopId, ShopPayInfoGreyReleaseRequest request) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.INACTIVE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
        if (CollectionUtils.isEmpty(request.getMembers())) {
            throw new RuntimeException("灰度成员不能为空");
        }
        if (request.getMembers().size() > 5) {
            throw new RuntimeException("灰度成员数量不能超过5");
        }
    }

    /**
     * 判断指定手机号用户是否为商家的会员
     *
     * @param mobile
     * @return
     */
    @PostMapping("/api/shop/profile/pay/{mobile}/judgeMemberUser")
    public Result<Boolean> judgeMemberUser(@PathVariable("mobile") String mobile) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        try {
            var targetUser = userReadService.findByMobile(mobile).getResult();
            if (targetUser == null) {
                return Result.data(false);
            }

            return Result.data(shopPayInfoComponent.judgeMemberUser(shopId, targetUser.getId()));
        } catch (Exception ex) {
            log.error("ShopPayInfos.judgeMemberUser error, shopId={}, mobile={}", shopId, mobile, ex);
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 停止灰度发布
     *
     * @param id
     * @return
     */
    @PostMapping("/api/shop/profile/pay/{id}/stopGreyRelease")
    public Result<Boolean> stopGreyRelease(@PathVariable Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        try {
            var target = shopPayInfoReadService.getById(id).getResult();
            stopGreyReleaseCheck(target, shopId);

            // 停止
            shopPayInfoComponent.stopGreyRelease(id);

            // 事件通知
            EventSender.publish(new ShopPayInfoDeleteEvent(target.getShopId(), target.getPayChannel()));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.stopGreyRelease error, shopId={}, id={}", shopId, id, ex);
            return Result.fail(ex.getMessage());
        }
    }

    private void stopGreyReleaseCheck(ShopPayInfo target, long shopId) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.GRAY_RELEASE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
    }

    /**
     * 停用支付配置
     *
     * @param id
     * @return
     */
    @PostMapping("/api/shop/profile/pay/{id}/stopUsage")
    public Result<Boolean> stopUsage(@PathVariable Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        try {
            var target = shopPayInfoReadService.getById(id).getResult();
            stopUsageCheck(target, shopId);

            // 停止
            shopPayInfoComponent.stopUsage(id);

            // 事件通知
            EventSender.publish(new ShopPayInfoDeleteEvent(target.getShopId(), target.getPayChannel()));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.stopUsage error, shopId={}, id={}", shopId, id, ex);
            return Result.fail(ex.getMessage());
        }
    }

    private void stopUsageCheck(ShopPayInfo target, long shopId) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.ACTIVE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
    }
}
