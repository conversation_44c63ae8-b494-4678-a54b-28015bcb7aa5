package moonstone.web.front.shop.jifen;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.RiskManagementRedisName;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.enums.RiskManagementTime;
import moonstone.common.enums.RiskManagementType;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.MyLog;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.order.model.IntegralError;
import moonstone.order.model.IntegralFrequency;
import moonstone.order.model.IntegralIps;
import moonstone.order.model.RiskManagements;
import moonstone.user.model.IntegralRiskManagement;
import moonstone.user.service.IntegralRiskManagementReadService;
import moonstone.user.service.IntegralRiskManagementWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.PostConstruct;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/11/4 13:23
 * RiskManagementType
 */
@RestController
@RequestMapping("/api/integral/risk")
@Slf4j
public class RiskManagement {

    @Autowired(required = false)
    private JedisPool jedisPool;

    @RpcConsumer
    IntegralRiskManagementWriteService integralRiskManagementWriteService;

    @RpcConsumer
    IntegralRiskManagementReadService integralRiskManagementReadService;
    //缓存风控限制条件--增加缓存，防止被刷
    //ip限条件
    private LoadingCache<String, Either<Optional<IntegralRiskManagement>>> ipRiskCache;
    //扫码间隔限
    private LoadingCache<String, Either<Optional<IntegralRiskManagement>>> intervalRiskCache;
    //日限
    private LoadingCache<String, Either<Optional<IntegralRiskManagement>>> dayRiskCache;
    //月限
    private LoadingCache<String, Either<Optional<IntegralRiskManagement>>> monthRiskCache;
    //错误次数限制
    private LoadingCache<String, Either<Optional<IntegralRiskManagement>>> errorRiskCache;


    @PostConstruct
    public void init() {

        ipRiskCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(new CacheLoader<String, Either<Optional<IntegralRiskManagement>>>() {
                    @Override
                    public Either<Optional<IntegralRiskManagement>> load(String key) throws Exception {
                        Long shopId = Long.valueOf(key.substring(0, key.indexOf("_")));
                        final Either<Optional<IntegralRiskManagement>> ipRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_IP.Code());
                        return ipRisk;
                    }
                });
        intervalRiskCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(new CacheLoader<String, Either<Optional<IntegralRiskManagement>>>() {
                    @Override
                    public Either<Optional<IntegralRiskManagement>> load(String key) throws Exception {
                        Long shopId = Long.valueOf(key.substring(0, key.indexOf("_")));
                        final Either<Optional<IntegralRiskManagement>> ipRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_INTERVAL.Code());
                        return ipRisk;
                    }
                });
        dayRiskCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(new CacheLoader<String, Either<Optional<IntegralRiskManagement>>>() {
                    @Override
                    public Either<Optional<IntegralRiskManagement>> load(String key) throws Exception {
                        Long shopId = Long.valueOf(key.substring(0, key.indexOf("_")));
                        final Either<Optional<IntegralRiskManagement>> ipRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_DAY.Code());
                        return ipRisk;
                    }
                });
        monthRiskCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(new CacheLoader<String, Either<Optional<IntegralRiskManagement>>>() {
                    @Override
                    public Either<Optional<IntegralRiskManagement>> load(String key) throws Exception {
                        Long shopId = Long.valueOf(key.substring(0, key.indexOf("_")));
                        final Either<Optional<IntegralRiskManagement>> ipRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_MONTH.Code());
                        return ipRisk;
                    }
                });
        errorRiskCache = Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(new CacheLoader<String, Either<Optional<IntegralRiskManagement>>>() {
                    @Override
                    public Either<Optional<IntegralRiskManagement>> load(String key) throws Exception {
                        Long shopId = Long.valueOf(key.substring(0, key.indexOf("_")));
                        final Either<Optional<IntegralRiskManagement>> ipRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_ERROR.Code());
                        return ipRisk;
                    }
                });
    }

    private void clearCache(Long shopId) {
        ipRiskCache.refresh(shopId + "_" + RiskManagementType.LIMIT_IP.Code());
        intervalRiskCache.refresh(shopId + "_" + RiskManagementType.LIMIT_INTERVAL.Code());
        dayRiskCache.refresh(shopId + "_" + RiskManagementType.LIMIT_DAY.Code());
        monthRiskCache.refresh(shopId + "_" + RiskManagementType.LIMIT_MONTH.Code());
        errorRiskCache.refresh(shopId + "_" + RiskManagementType.LIMIT_ERROR.Code());
    }

    @MyLog(title = "积分码风控" ,value = "添加积分码风控规则",opBusinessType = OpBusinessType.INSERT)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R createRiskManagement(@RequestBody RiskManagements riskManagements) {
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (ObjectUtils.isEmpty(user) || StringUtils.isEmpty(user.getId())) {
                return R.error(-1, "未登录，请登录！！");
            }
            if (ObjectUtils.isEmpty(riskManagements) || StringUtils.isEmpty(riskManagements.getShopId())) {
                return R.error(-1, "必要参数为空");
            }

            //ip系列次数限制
            IntegralRiskManagement ipRisk = new IntegralRiskManagement();
            ipRisk.setType(RiskManagementType.LIMIT_IP.Code());
            ipRisk.setTimeType(RiskManagementTime.MINUTE.Code());
            if (riskManagements.getIps() == null || riskManagements.getIps().getNum() == null
                    || riskManagements.getIps().getTime() == null) {
                ipRisk.setStatus(0);
                ipRisk.setNum(0);
                ipRisk.setFrequency(0);
            } else {
                ipRisk.setNum(Optional.ofNullable(riskManagements.getIps().getNum()).orElse(0));
                ipRisk.setFrequency(Optional.ofNullable(riskManagements.getIps().getTime()).orElse(0));
            }
            ipRisk.setShopId(riskManagements.getShopId());
            Either<Long> ipRiskReslut = integralRiskManagementWriteService.insert(ipRisk);
            if (!ipRiskReslut.isSuccess() || ObjectUtils.isEmpty(ipRiskReslut.take())) {
                log.error("{} ipRisk:{}", LogUtil.getClassMethodName(), ipRisk);
                return R.error(-1, "添加规则失败");
            }

            //扫码间隔限制
            IntegralRiskManagement intervalRisk = new IntegralRiskManagement();
            intervalRisk.setType(RiskManagementType.LIMIT_INTERVAL.Code());
            intervalRisk.setTimeType(RiskManagementTime.SECOND.Code());
            if (riskManagements.getIps() == null || riskManagements.getIps().getInterval() == null) {
                intervalRisk.setStatus(0);
                intervalRisk.setNum(0);
            } else {
                intervalRisk.setNum(Optional.ofNullable(riskManagements.getIps().getInterval()).orElse(0));
            }
            intervalRisk.setFrequency(0);
            intervalRisk.setShopId(riskManagements.getShopId());
            Either<Long> intervalRiskReslut = integralRiskManagementWriteService.insert(intervalRisk);
            if (!intervalRiskReslut.isSuccess() || ObjectUtils.isEmpty(intervalRiskReslut.take())) {
                log.error("{} intervalRisk:{}", LogUtil.getClassMethodName(), intervalRisk);
                return R.error(-1, "添加规则失败");
            }

            //日次数限制
            IntegralRiskManagement dayRisk = new IntegralRiskManagement();
            dayRisk.setType(RiskManagementType.LIMIT_DAY.Code());
            dayRisk.setTimeType(RiskManagementTime.DAY.Code());
            dayRisk.setShopId(riskManagements.getShopId());
            dayRisk.setFrequency(0);
            if (riskManagements.getFrequency() == null || riskManagements.getFrequency().getToday() == null) {
                dayRisk.setNum(0);
                dayRisk.setStatus(0);
            } else {
                dayRisk.setNum(Optional.ofNullable(riskManagements.getFrequency().getToday()).orElse(0));
            }
            Either<Long> dayRiskReslut = integralRiskManagementWriteService.insert(dayRisk);
            if (!dayRiskReslut.isSuccess() || ObjectUtils.isEmpty(dayRiskReslut.take())) {
                log.error("{} dayRisk:{}", LogUtil.getClassMethodName(), dayRisk);
                return R.error(-1, "添加规则失败");
            }

            //月次数限制
            IntegralRiskManagement monthRisk = new IntegralRiskManagement();
            monthRisk.setType(RiskManagementType.LIMIT_MONTH.Code());
            monthRisk.setTimeType(RiskManagementTime.MONTH.Code());
            monthRisk.setShopId(riskManagements.getShopId());
            monthRisk.setFrequency(0);
            if (riskManagements.getFrequency() == null || riskManagements.getFrequency().getMonths() == null) {
                monthRisk.setNum(0);
                monthRisk.setStatus(0);
            } else {
                monthRisk.setNum(Optional.ofNullable(riskManagements.getFrequency().getMonths()).orElse(0));
            }
            Either<Long> monthRiskReslut = integralRiskManagementWriteService.insert(monthRisk);
            if (!monthRiskReslut.isSuccess() || ObjectUtils.isEmpty(monthRiskReslut.take())) {
                log.error("{} monthRisk:{}", LogUtil.getClassMethodName(), monthRisk);
                return R.error(-1, "添加规则失败");
            }

            //错误次数限制
            IntegralRiskManagement errorRisk = new IntegralRiskManagement();
            errorRisk.setType(RiskManagementType.LIMIT_ERROR.Code());
            errorRisk.setTimeType(RiskManagementTime.DAY.Code());
            errorRisk.setShopId(riskManagements.getShopId());
            errorRisk.setFrequency(0);
            if (riskManagements.getError() == null || riskManagements.getError().getErrorCount() == null) {
                errorRisk.setStatus(0);
                errorRisk.setNum(0);
            } else {
                errorRisk.setNum(Optional.ofNullable(riskManagements.getError().getErrorCount()).orElse(0));
            }
            Either<Long> errorRiskReslut = integralRiskManagementWriteService.insert(errorRisk);
            if (!errorRiskReslut.isSuccess() || ObjectUtils.isEmpty(errorRiskReslut.take())) {
                log.error("{} errorRisk:{}", LogUtil.getClassMethodName(), errorRisk);
                return R.error(-1, "添加规则失败");
            }
        } catch (Exception e) {
            log.error("{} createRiskManagement is error", LogUtil.getClassMethodName());
            return R.error(-1, "系统异常");
        }
        clearCache(riskManagements.getShopId());//击穿缓存
        return R.ok();
    }

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R findRiskManagement(Long shopId) {
        RiskManagements riskManagements = new RiskManagements();
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (ObjectUtils.isEmpty(user) || StringUtils.isEmpty(user.getId())) {
                return R.error(-1, "未登录，请登录！！");
            }
            IntegralIps integralIps = new IntegralIps();
            //ip系列
            Either<Optional<IntegralRiskManagement>> ipRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_IP.Code());
            if (!ipRisk.isSuccess()) {
                return R.error(-1, "数据异常");
            }
            if (ipRisk.take().isPresent() && Objects.equals(1, ipRisk.take().get().getStatus())) {
                riskManagements.setShopId(shopId);
                integralIps.setNum(ipRisk.take().get().getNum());
                integralIps.setTime(ipRisk.take().get().getFrequency());
                riskManagements.setIps(integralIps);
            }

            //间隔
            Either<Optional<IntegralRiskManagement>> intervalRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_INTERVAL.Code());
            if (!intervalRisk.isSuccess()) {
                return R.error(-1, "数据异常");
            }
            if (intervalRisk.take().isPresent() && Objects.equals(1, intervalRisk.take().get().getStatus())) {
                integralIps.setInterval(intervalRisk.take().get().getNum());
                riskManagements.setIps(integralIps);
            }

            IntegralFrequency integralFrequency = new IntegralFrequency();
            //日次数限制
            Either<Optional<IntegralRiskManagement>> dayRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_DAY.Code());
            if (!dayRisk.isSuccess()) {
                return R.error(-1, "数据异常");
            }

            if (dayRisk.take().isPresent() && Objects.equals(1, dayRisk.take().get().getStatus())) {
                integralFrequency.setToday(dayRisk.take().get().getNum());
                riskManagements.setFrequency(integralFrequency);
            }


            //月次数限制
            Either<Optional<IntegralRiskManagement>> monthRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_MONTH.Code());
            if (!monthRisk.isSuccess()) {
                return R.error(-1, "数据异常");
            }

            if (monthRisk.take().isPresent() && Objects.equals(1, monthRisk.take().get().getStatus())) {
                integralFrequency.setMonths(monthRisk.take().get().getNum());
                riskManagements.setFrequency(integralFrequency);
            }
            IntegralError integralError = new IntegralError();
            //错误次数限制
            Either<Optional<IntegralRiskManagement>> errorRisk = integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_ERROR.Code());
            if (!errorRisk.isSuccess()) {
                return R.error(-1, "数据异常");
            }
            if (errorRisk.take().isPresent() && Objects.equals(1, errorRisk.take().get().getStatus())) {
                integralError.setErrorCount(errorRisk.take().get().getNum());
                riskManagements.setError(integralError);
            }

            return R.ok().add("data", riskManagements);

        } catch (Exception e) {
            log.error("{} findRiskManagement is error {} ", LogUtil.getClassMethodName(), e.getStackTrace());
            return R.error(-1, "系统异常");
        }
    }

    //    @RequestMapping(value = "/ip", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> checkIntegralLimit(Long userId, Long shopId, String ip) {
//        ip = ip.replaceAll("\\.", "");
        Map<String, String> map = new HashMap<>();
        map.put("tip", "PASS");//绿色放行
        // 如果没有配置redis 那么就直接走吧
        if (jedisPool == null) {
            log.error("{} checkIntegralLimit redis not enable so may cause over sell", LogUtil.getClassMethodName());
            return map;
        }
        // 配置了redis 那么就从redis里拉缓存
        //// NX是不存在时才set， XX是存在时才set， EX是秒，PX是毫秒
        try {
            //ip系列
            Boolean ipCheck = ipCheck(userId, shopId, ip);
            if (!ipCheck) {
                map.put("tip", "FIAL");
                map.put("msg", "扫码过于频繁,请稍后再试!!");
                return map;
            }
            //间隔--userId
            Boolean intervalCheck = intervalCheck(userId, shopId, ip);
            if (!intervalCheck) {
                map.put("tip", "FIAL");
                map.put("msg", "扫码过于频繁,请稍后再试!!!");
                return map;
            }
            //日次数限制
            Boolean dayCheck = dayCheck(userId, shopId, ip);
            if (!dayCheck) {
                map.put("tip", "FIAL");
                map.put("msg", "当日扫码超过扫码次数!!!");
                return map;
            }
            //月次数限制
            Boolean monthCheck = monthCheck(userId, shopId, ip);
            if (!monthCheck) {
                map.put("tip", "FIAL");
                map.put("msg", "当月扫码超过扫码次数!!!");
                return map;
            }
            //错误次数限制
            Boolean errorCheck = errorCheck(userId, shopId, ip);
            if (!errorCheck) {
                map.put("tip", "FIAL");
                map.put("msg", "请次日再来扫码!!!");
                return map;
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("tip", "FIAL");
            map.put("msg", "请稍后重试!!!");
            return map;
        }
        return map;
    }

    /**
     * 错误次数check
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    private Boolean errorCheck(Long userId, Long shopId, String ip) {
        String key = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_ERROR + shopId + "_" + userId;
        Either<Optional<IntegralRiskManagement>> errorRisk = errorRiskCache.get(shopId + "_" + RiskManagementType.LIMIT_ERROR.Code());
//                    integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_ERROR.Code());
        if (!errorRisk.isSuccess()) {
            return false;
        }
        if (!errorRisk.take().isPresent() || Objects.equals(0, errorRisk.take().get().getStatus())) {
            return true;
        }
        Integer num = Optional.ofNullable(errorRisk.take().get().getNum()).orElse(0);

        if (Objects.equals(0, num) && Objects.equals(errorRisk.take().get().getStatus(), 1)) {
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis.exists(key)) {
                String count = jedis.get(key);
                return Integer.valueOf(count) < num;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 月限check
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    private Boolean monthCheck(Long userId, Long shopId, String ip) {
        Either<Optional<IntegralRiskManagement>> monthRisk = monthRiskCache.get(shopId + "_" + RiskManagementType.LIMIT_MONTH.Code());
//                    integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_MONTH.Code());
        if (!monthRisk.isSuccess()) {
            return false;
        }
        if (!monthRisk.take().isPresent() || Objects.equals(0, monthRisk.take().get().getStatus())) {
            return true;
        }
        Integer num = Optional.ofNullable(monthRisk.take().get().getNum()).orElse(0);

        if (Objects.equals(0, num) && Objects.equals(monthRisk.take().get().getStatus(), 1)) {
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            String key = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_MONTH + dateToString("yyyyMM") + ":" + shopId + "_" + userId;
            if (jedis.exists(key)) {
                String count = jedis.get(key);
                return Integer.valueOf(count) < num;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 日限check
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    private Boolean dayCheck(Long userId, Long shopId, String ip) {
        Either<Optional<IntegralRiskManagement>> dayRisk = dayRiskCache.get(shopId + "_" + RiskManagementType.LIMIT_DAY.Code());
//                   integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_DAY.Code());
        if (!dayRisk.isSuccess()) {
            return false;
        }
        if (!dayRisk.take().isPresent() || Objects.equals(0, dayRisk.take().get().getStatus())) {
            return true;
        }
        Integer num = Optional.ofNullable(dayRisk.take().get().getNum()).orElse(0);

        if (Objects.equals(0, num) && Objects.equals(dayRisk.take().get().getStatus(), 1)) {
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            String key = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_DAY + dateToString("yyyyMMdd") + ":" + shopId + "_" + userId;

            if (jedis.exists(key)) {
                String count = jedis.get(key);
                return Integer.valueOf(count) < num;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 扫码间隔check
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    private Boolean intervalCheck(Long userId, Long shopId, String ip) {
        String key = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_INTERVAL + shopId + "_" + userId;
        Either<Optional<IntegralRiskManagement>> intervalRisk = intervalRiskCache.get(shopId + "_" + RiskManagementType.LIMIT_INTERVAL.Code());
//                    integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_IP_INTERVAL.Code());
        if (!intervalRisk.isSuccess()) {
            return false;
        }
        if (!intervalRisk.take().isPresent() || Objects.equals(0, intervalRisk.take().get().getStatus())) {
            return true;
        }
        Integer num = Optional.ofNullable(intervalRisk.take().get().getNum()).orElse(0);

        if (Objects.equals(0, num) && Objects.equals(intervalRisk.take().get().getStatus(), 1)) {
            return true;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis.exists(key)) {
                Long times = Long.valueOf(jedis.get(key));
                if ((System.currentTimeMillis() - times) / 1000 < num) {
                    return false;
                }
                //jedis.set(key, System.currentTimeMillis() + "", "xx", "ex", 24 * 60 * 60);
                jedis.setex(key, 24 * 60 * 60, System.currentTimeMillis() + "");
                //jedis.expire(key,24*60*60);
                return true;
            } else {
                //jedis.set(key, System.currentTimeMillis() + "", "nx", "ex", 24 * 60 * 60);
                jedis.setnx(key, System.currentTimeMillis() + "");
                jedis.expire(key, 24 * 60 * 60);
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * ipcheck
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    private Boolean ipCheck(Long userId, Long shopId, String ip) {
        String key = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_IP + ip + "_" + shopId + "_" + userId;

        log.info("limit_ip:{}",key);

        Either<Optional<IntegralRiskManagement>> ipRisk = ipRiskCache.get(shopId + "_" + RiskManagementType.LIMIT_IP.Code());
        //integralRiskManagementReadService.findByRiskManagementType(shopId, RiskManagementType.LIMIT_IP.Code());
        if (!ipRisk.isSuccess()) {
            return false;
        }
        if (!ipRisk.take().isPresent() || Objects.equals(0, ipRisk.take().get().getStatus())) {
            return true;
        }

        Integer num = Optional.ofNullable(ipRisk.take().get().getNum()).orElse(0);

        if (Objects.equals(0, num) && Objects.equals(ipRisk.take().get().getStatus(), 1)) {
            return false;
        }
        Integer timeS = Optional.ofNullable(ipRisk.take().get().getFrequency()).orElse(0);
        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis.exists(key)) {
                String count = jedis.get(key);
                if (Integer.valueOf(count) >= num) {
                    return false;
                }
                jedis.incr(key);
                return true;
            } else {
                //jedis.set(key, 1 + "", "nx", "ex", 60 * timeS);
                jedis.setnx(key, "1");
                jedis.expire(key, 60 * timeS);
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取月限和日限的参数
     *
     * @param str
     * @return
     * @throws ParseException
     */
    public static String dateToString(String str) throws ParseException {
        if (Objects.equals(str, "yyyyMM")) {
            SimpleDateFormat s = new SimpleDateFormat("yyyyMM");
            return s.format(new Date());
        } else if (Objects.equals(str, "yyyyMMdd")) {
            SimpleDateFormat s = new SimpleDateFormat("yyyyMMdd");
            return s.format(new Date());
        }
        return str;
    }

    /**
     * 获取一个月多少天
     *
     * @return
     */
    public static int getDaysOfMonth() {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 增加错误次数check
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    public Boolean addErrorCount(Long userId, Long shopId, String ip) {
        String key = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_ERROR + shopId + "_" + userId;
        try (Jedis jedis = jedisPool.getResource()) {
            if (jedis.exists(key)) {
                jedis.incr(key);
            } else {
                //jedis.set(key, 1 + "", "nx", "ex", 24 * 60 * 60);
                jedis.setnx(key, "1");
                jedis.expire(key, 24 * 60 * 60);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 增加成功扫码次数check
     *
     * @param userId
     * @param shopId
     * @param ip
     * @return
     */
    public Boolean addSuccessCount(Long userId, Long shopId, String ip) {

        try (Jedis jedis = jedisPool.getResource()) {
            String keyDay = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_DAY + dateToString("yyyyMMdd") + ":" + shopId + "_" + userId;

            String keyMonth = RiskManagementRedisName.PFFIX + RiskManagementRedisName.REDIS_MONTH + dateToString("yyyyMM") + ":" + shopId + "_" + userId;
            //增加日扫码次数
            if (jedis.exists(keyDay)) {
                jedis.incr(keyDay);
            } else {
                //jedis.set(keyDay, 1 + "", "nx", "ex", 24 * 60 * 60);
                jedis.setnx(keyDay, "1");
                jedis.expire(keyDay, 24 * 60 * 60);
            }
            //增加月扫码次数
            if (jedis.exists(keyMonth)) {
                jedis.incr(keyMonth);
            } else {
                //jedis.set(keyMonth, 1 + "", "nx", "ex", 24 * 60 * 60 * getDaysOfMonth());
                jedis.setnx(keyMonth, "1");
                jedis.expire(keyMonth, 24 * 60 * 60 * getDaysOfMonth());
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
