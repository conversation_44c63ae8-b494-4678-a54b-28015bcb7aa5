package moonstone.web.front.shop.substore.app;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.utils.EventSender;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.component.siteMessage.SiteMessageComponent;
import moonstone.web.core.msg.enu.SiteMessageLevel;
import moonstone.web.core.msg.enu.SiteMessageTemplate;
import moonstone.web.core.msg.event.SiteMsgNotify;
import moonstone.web.front.shop.substore.event.SubStoreApplyRegisterEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 站内信: 门店申请注册事件处理
 */
@Slf4j
@Component
public class SubStoreApplyRegisterNotify {

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private SiteMessageComponent siteMessageComponent;

    @EventListener(SubStoreApplyRegisterEvent.class)
    public void onApply(SubStoreApplyRegisterEvent event) {
        var store = event.getSubStore();
        if (store == null) {
            log.error("门店信息不存在！");
            return;
        }

        Date at = new Date();

        //待通知的用户id集合
        var receiverUserIdSet = findReceiverIds(store);

        for (Long userId : receiverUserIdSet) {
            EventSender.sendApplicationEvent(new SiteMsgNotify(store.getShopId(),
                    userId,
                    SiteMessageTemplate.SUBSTORE_APPLY_REGISTER.getName(),
                    null,
                    Map.of("name", store.getName()),
                    Collections.emptyList(),
                    at,
                    SiteMessageLevel.SHOP_AUTH.getCode()));
        }
    }

    private Set<Long> findReceiverIds(SubStore store) {
        Set<Long> receiverUserIdSet = new HashSet<>();

        var shop = shopReadService.findById(store.getShopId()).getResult();

        //通知超管
        receiverUserIdSet.add(shop.getUserId());

        //通知管理员
        receiverUserIdSet.addAll(siteMessageComponent.findUserIdsByRoleName(shop.getId(),
                SiteMessageComponent.ROLE_NAME_MANAGER));

        //通知“销售运营”
        receiverUserIdSet.addAll(siteMessageComponent.findUserIdsByRoleName(shop.getId(),
                SiteMessageComponent.ROLE_NAME_SALES_OPERATION));

        return receiverUserIdSet;
    }
}
