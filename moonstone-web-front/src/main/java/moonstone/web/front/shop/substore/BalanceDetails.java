package moonstone.web.front.shop.substore;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.constants.ShopExtra;
import moonstone.common.model.ResultResponse;
import moonstone.common.model.*;
import moonstone.common.utils.*;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.BalanceDetailQueryDTO;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OutComeDetail;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.view.WithdrawApplyView;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.DataExportTask;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.model.view.UserLevelView;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.StoreProxyRegisterComponent;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.core.model.dto.WithDrawProfitApplyCriteriaDTO;
import moonstone.web.core.model.dto.WithdrawFeeRequest;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.profit.application.WithdrawProfitHelper;
import moonstone.web.front.shop.substore.app.GuiderOrderSumApp;
import moonstone.web.front.shop.substore.app.SubStoreWithdrawAccountInitApp;
import moonstone.web.front.shop.substore.component.WithdrawDomainSlice;
import moonstone.web.front.shop.substore.component.WithdrawProfitApplyBeautyComponent;
import moonstone.web.front.shop.substore.component.WithdrawProfitApplyExporter;
import moonstone.web.front.shop.substore.component.WithdrawProxyModeJudgeComponent;
import moonstone.web.front.shop.substore.util.WithdrawTypeParser;
import moonstone.web.front.util.SimpleExportUtil;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/balance")
@Slf4j
public class BalanceDetails {
	private final String WithDrawSessionCode = "WITH_DRAW_SC";
	@Autowired
	WeShopCacheHolder weShopCacheHolder;
	@Autowired
	StoreProxyRegisterComponent storeProxyRegisterComponent;
	@Autowired
	ServiceProviderCache serviceProviderCache;
	@Autowired
	SubStoreCache subStoreCache;
	@Autowired
	GuiderCache guiderCache;
	@Autowired
	SubStoreReadService subStoreReadService;
	@Autowired
	ShopOrderReadService shopOrderReadService;
	@Autowired
	BalanceDetailReadService balanceDetailReadService;
	@Autowired
	WithDrawProfitApplyReadService withDrawProfitApplyReadService;
	@Autowired
	BalanceDetailManager balanceDetailManager;
	@Autowired
	BalanceDetailWriteService balanceDetailWriteService;
	@Autowired
	UserReadService<User> userReadService;
	@Autowired
	UserProfileReadService userProfileReadService;
	@Autowired
	SubStoreTStoreGuiderReadService subStoreGuiderReadService;
	@Autowired
	StoreProxyReadService storeProxyReadService;
	@Autowired
	WithdrawAccountReadService withdrawAccountReadService;
	@Autowired
	WithdrawAccountWriteService withdrawAccountWriteService;
	@Autowired
	ShopReadService shopReadService;
	@Value("${timeout.sms:5}")
	Long timeoutMinus;
	@Autowired
	Exporter exporter;
	@Autowired
	ShopCacheHolder shopCacheHolder;
	@Autowired
	WithdrawPrincipleManager withdrawPrincipleManager;
	@Autowired
	WithdrawProfitHelper withdrawProfitHelper;
	@Autowired
	WithdrawDomainSlice withdrawDomainSlice;
	@Autowired
	SubStoreWithdrawAccountInitApp subStoreWithdrawAccountInitApp;
	@Autowired
	GuiderOrderSumApp guiderOrderSumApp;
	@Autowired
	private WithdrawProxyModeJudgeComponent withdrawProxyModeJudgeComponent;
	@Autowired
	WithdrawProfitApplyBeautyComponent withdrawProfitApplyBeautyComponent;
	@Resource
	private WithdrawProfitApplyExporter withdrawProfitApplyExporter;

	/**
	 * 获取过去几天的收益汇总(排除提现影响)
	 *
	 * @param day      今天到前day天
	 * @param sourceId 数据来源的小程序平台(目前为ShopId)
	 * @return 汇总VO层
	 */
	@GetMapping("/profit-total")
	public BalanceDetailManager.ProfitDataVO profitTotal(@RequestParam(defaultValue = "0") int day, long sourceId) {
		long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
		Date startAt =
				DateUtil.withTimeAtStartOfDay(
						Date.from(LocalDateTime.now().minusDays(day)
								.atZone(ZoneId.systemDefault())
								.toInstant()));
		Date endAt = day == 0 ? Date.from(Instant.now()) : DateUtil.withTimeAtEndOfDay(Date.from(LocalDateTime.now().minusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
		if (guiderCache.findByShopIdAndUserId(sourceId, userId).isPresent()) {
			return guiderOrderSumApp.queryTheOrderSum(sourceId, userId, startAt, endAt);
		}
		Optional<BalanceDetailManager.ProfitDataVO> voOptional = balanceDetailManager.queryProfitList(userId, sourceId, startAt, endAt
				, Arrays.asList(BalanceDetail.maskBit.WithDrawRelated.getValue(), BalanceDetail.maskBit.RefundRelated.getValue()), null);
		return voOptional.orElseThrow(() -> new JsonResponseException(new Translate("获取信息失败").toString()));
	}

	@GetMapping("/getOrderNum")
	public BalanceDetailManager.ProfitDataVO getOrderSum(Long sourceId, Integer day) {
		BalanceDetailManager.ProfitDataVO vo = new BalanceDetailManager.ProfitDataVO();
		long paymentFee = 0;
		long unsettledProfit = 0;
		HashSet<Long> unsettledProfitIds = new HashSet<>();
		Long refererId = UserUtil.getUserId();

		BalanceDetailCriteria criteria = new BalanceDetailCriteria();
		criteria.setType(ProfitType.InCome.getValue());
		criteria.setUserId(refererId);
		criteria.setSourceId(sourceId);
		Date startAt = DateUtil.withTimeAtStartOfDay(Date.from(LocalDateTime.now().minusDays(day).atZone(ZoneId.systemDefault()).toInstant()));
		Date endAt = DateUtil.withTimeAtEndOfDay(Date.from(LocalDateTime.now().minusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
		criteria.setCreatedStartAt(startAt);
		criteria.setCreatedEndAt(endAt);
		criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.orderRelatedMask.ShopOrder.getValue()
				, BalanceDetail.maskBit.OrderRelated.getValue()
				, IsPersistAble.maskBit.PersistAble.getValue()));
		criteria.setNotStatusBitMarks(Collections.singletonList(IsPresent.presentMaskBit.Present.getValue()));
		criteria.setPageSize(Integer.MAX_VALUE);

		List<BalanceDetail> balanceDetails = balanceDetailReadService.list(criteria).getResult();
		log.debug("{} criteria:{} balanceDetails-size:{}", LogUtil.getClassMethodName(), criteria, balanceDetails.size());

		Set<Long> unpresentOrderIdSet = balanceDetails.stream()
				.filter(detail -> !detail.isPresent())
				.map(BalanceDetail::getRelatedId)
				.collect(Collectors.toSet());

		List<Long> orderIds = balanceDetails.stream().map(BalanceDetail::getRelatedId).distinct().collect(Collectors.toList());
		List<ShopOrder> shopOrders = shopOrderReadService.findByIds(orderIds).getResult();

		log.debug("{} orderIds:{} shopOrders-size:{}", LogUtil.getClassMethodName(), Arrays.toString(orderIds.toArray()), (shopOrders.size()));
		HashSet<Integer> except = Stream.of(OrderStatus.NOT_PAID
				, OrderStatus.TIMEOUT_CANCEL
				, OrderStatus.BUYER_CANCEL
				, OrderStatus.SELLER_CANCEL
				, OrderStatus.DELETED
				, OrderStatus.REFUND_APPLY_AGREED
				, OrderStatus.REFUND
				, OrderStatus.REFUND_PROCESSING
				, OrderStatus.RETURN_APPLY_AGREED
				, OrderStatus.RETURN
				, OrderStatus.RETURN_CONFIRMED)
				.map(OrderStatus::getValue).collect(Collectors.toCollection(HashSet::new));
		HashSet<Long> countHashSet = new HashSet<>();
		for (ShopOrder shopOrder : shopOrders) {
			if (shopOrder.getStatus() == OrderStatus.PAID.getValue() || shopOrder.getStatus() == OrderStatus.SHIPPED.getValue()) {
				unsettledProfitIds.add(shopOrder.getId());
			}
			if (!except.contains(shopOrder.getStatus())) {
				countHashSet.add(shopOrder.getId());
				if (unpresentOrderIdSet.contains(shopOrder.getId())) {
					paymentFee += shopOrder.getOriginFee();
				}
			}
		}
		for (BalanceDetail balanceDetail : balanceDetails) {
			if (unsettledProfitIds.contains(balanceDetail.getRelatedId())) {
				unsettledProfit += balanceDetail.getChangeFee();
			}
		}
		vo.setPaymentSum(paymentFee);
		vo.setCount(countHashSet.size());
		vo.setUnsettledProfit(unsettledProfit);
		return vo;
	}

	/**
	 * 获取过去day天的每天收益汇总列表(排除提现影响)
	 *
	 * @param day      过去day天
	 * @param sourceId 数据来源的子平台(目前是shopId)
	 * @return 由`{day:汇总}`合成的map
	 */
	@GetMapping("/profit-list")
	public Map<String, Object> profitList(@RequestParam(defaultValue = "7") int day, long sourceId) {
		/// LinkedHashMap来保障其day顺序
		Map<String, Object> resultMap = new LinkedHashMap<>();
		long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
		for (int iday = 0; iday < day; iday++) {
			Date standardDay =
					DateUtil.withTimeAtStartOfDay(
							Date.from(LocalDateTime.now().minusDays(iday)
									.atZone(ZoneId.systemDefault())
									.toInstant()));
			if (guiderCache.findByShopIdAndUserId(sourceId, userId).isPresent()) {
				resultMap.put(new DateTime(standardDay).toString("yyyy-MM-dd"), guiderOrderSumApp.queryTheOrderSum(sourceId, userId, DateUtil.withTimeAtStartOfDay(standardDay), DateUtil.withTimeAtEndOfDay(standardDay)));
				continue;
			}
			Optional<BalanceDetailManager.ProfitDataVO> voOptional = balanceDetailManager.queryProfitList(userId, sourceId
					, DateUtil.withTimeAtStartOfDay(standardDay), DateUtil.withTimeAtEndOfDay(standardDay)
					, Collections.singletonList(BalanceDetail.maskBit.RefundRelated.getValue()), null);
			resultMap.put(new DateTime(standardDay).toString("yyyy-MM-dd"), voOptional.orElseGet(() -> {
				log.error("{} day:{} standardDay:{} userId:{}", LogUtil.getClassMethodName(), day, standardDay, userId);
				return new BalanceDetailManager.ProfitDataVO();
			}));
		}
		return resultMap;
	}

    /**
     * 被整合至 /api/withdrawProfitApply/findAll
     *
     * @param criteria
     * @return
     */
    @Deprecated
    @GetMapping("/with-draw-page")
	public Paging<WithdrawApplyView> page(WithDrawProfitApplyCriteriaDTO criteria) {
		CommonUser user = UserUtil.getCurrentUser();
		criteria.setUserId(user.getId());
		Paging<WithDrawProfitApply> withDrawProfitApplyPaging = withDrawProfitApplyReadService.paging(criteria).getResult();
		return new Paging<>(withDrawProfitApplyPaging.getTotal(), withDrawProfitApplyPaging.getData().stream().map(WithdrawApplyView::from).collect(Collectors.toList()));
	}


	/**
	 * 获取可提现金额
	 * 2019-06-12 yjp
	 */
	@GetMapping
	@RequestMapping("/profit-balance")
	public CurrentProfitVO getCurrentProfit(long sourceId) {
		/// 不需要保证门店的存在 因为是和导购员共用
		CommonUser user = UserUtil.getCurrentUser();
		if (Objects.isNull(user) || Objects.isNull(user.getId())) {
			throw new RuntimeException(new Translate("请登录").toString());
		}
		subStoreWithdrawAccountInitApp.initSubStoreWithdrawAccount(user.getId(), sourceId);
		return Optional.ofNullable(withdrawProfitHelper.queryWithdrawAbleProfit(user.getId(), sourceId).getData())
				.orElseThrow(() -> new JsonResponseException("查询利润失败"));
	}

	@PutMapping("/account/update")
	public Response<Boolean> updateAccount(WithdrawAccount account) {
		WithdrawAccount exists = withdrawAccountReadService.findById(account.getId()).orElse(Optional.empty()).orElse(null);
		if (exists == null) {
			return Response.fail(new Translate("目标账户不存在").toString());
		}
		if (Objects.equals(exists.getUserId(), UserUtil.getUserId())) {
			return Response.fail(new Translate("该账户不属于你").toString());
		}
		return new ResultResponse<>(withdrawAccountWriteService.update(account));
	}

	@PostMapping("/account/default")
	public Response<Boolean> setAccountDefault(Long accountId) {
		WithdrawAccount account = withdrawAccountReadService.findById(accountId).orElse(Optional.empty()).orElse(null);
		if (account == null) {
			return Response.fail(new Translate("目标帐号不存在").toString());
		}
		if (UserUtil.getUserId() == null) {
			return Response.fail("user.not.login");
		}
		if (!Objects.equals(account.getUserId(), UserUtil.getUserId())) {
			return Response.fail(new Translate("目标账户不属于你").toString());
		}
		withdrawAccountWriteService.revokeDefault(account.getShopId(), account.getUserId());
		account.setDefault();
		return new ResultResponse<>(withdrawAccountWriteService.update(account));
	}

	/**
	 * 预览提现服务费
	 *
	 * @param fee      提现费用
	 * @param sourceId 店铺Id
	 * @return 服务费用
	 */
	@GetMapping("/preview-withdraw")
	public BigDecimal previewWithdrawServiceFee(Long fee, Long sourceId) {
		WithdrawPrinciple withdrawPrinciple = withdrawPrincipleManager.findByShopId(sourceId).orElseGet(WithdrawPrinciple::new);
		return BigDecimal.valueOf(fee).divide(new BigDecimal("100"), RoundingMode.DOWN)
				.multiply(withdrawPrinciple.getRateServiceFee()).add(withdrawPrinciple.getStaticServiceFee());
	}

	/**
	 * 提现
	 *
	 * @param fee 金额参数
	 * @return 提现是否成功
	 * @see WithdrawPrinciple 提现规则
	 * @see UserWithdrawSum 提现记录(规则相关)
	 * @see ShopFunctionSlice#isWithdrawRequireCertification() 提现是否需要设置实名验证
	 * @see WithdrawAccount#getType() 提现类型
	 */
	@PostMapping
	@RequestMapping("/profit-withdraw")
	public boolean withDraw(@RequestBody WithdrawFeeRequest fee) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			throw new JsonResponseException("user.not.login");
		}
		if (!subStoreCache.findByShopIdAndUserId(fee.sourceId, user.getId()).map(AuthAbleByStatus::isAuthed).orElse(true)) {
			throw Translate.exceptionOf("门店审核未通过");
		}
		return withdrawDomainSlice.withdraw(user, fee).take() != null;
	}

	@PostMapping("/account/add")
	public Response<WithdrawAccount> addAccount(@RequestBody WithdrawAccount account) {
		if (UserUtil.getUserId() == null) {
			return Response.fail("user.not.login");
		}
		log.info("{} WithdrawAccount:{}", LogUtil.getClassMethodName(), account);
		account.setName(Optional.ofNullable(account.getName()).orElseGet(account::getRealName));
		account.setUserId(UserUtil.getUserId());
		account.setDefault();
		Either<WithdrawAccount> res = withdrawAccountWriteService.create(account);
		if (res.isSuccess()) {
			return Response.ok(res.take());
		} else {
			return Response.fail(new Translate("创建帐号失败").toString());
		}
	}

	@GetMapping("/profit-page")
	public List<BalanceDetailVo> profitPage(BalanceDetailQueryDTO balanceDetailQueryDTO) {
		BalanceDetailCriteria balanceDetailCriteria = new BalanceDetailCriteria();
		balanceDetailCriteria.setUserId(UserUtil.getUserId());
		if (balanceDetailQueryDTO != null) {
			BeanUtils.copyProperties(balanceDetailQueryDTO, balanceDetailCriteria);
		} else {
			balanceDetailQueryDTO = new BalanceDetailQueryDTO();
		}
		if (balanceDetailCriteria.getCreatedStartAt() != null) {
			balanceDetailCriteria.setCreatedStartAt(DateUtil.withTimeAtStartOfDay(balanceDetailCriteria.getCreatedStartAt()));
		}
		if (balanceDetailCriteria.getCreatedEndAt() != null) {
			balanceDetailCriteria.setCreatedEndAt(DateUtil.withTimeAtEndOfDay(balanceDetailCriteria.getCreatedEndAt()));
		}
		balanceDetailCriteria.setType(null);
		if (balanceDetailQueryDTO.getType() == null) {
			balanceDetailQueryDTO.setType(1);
		}
		switch (balanceDetailQueryDTO.getType()) {
			case 1: {
				/// 全部
				balanceDetailCriteria.setStatusBitMarks(Collections.singletonList(
						IsPersistAble.maskBit.PersistAble.getValue()
				));
				break;
			}
			case 5: {
				/// 退款
				balanceDetailCriteria.setStatusBitMarks(Arrays.asList(
						OutComeDetail.maskBit.RefundRelated.getValue(),
						IsPersistAble.maskBit.PersistAble.getValue()
				));
				balanceDetailCriteria.setType(ProfitType.OutCome.getValue());
				break;
			}
			case 2: {
				/// 已收益
				balanceDetailCriteria.setStatusBitMarks(
						Arrays.asList(
								IsPersistAble.maskBit.PersistAble.getValue(),
								IsPresent.presentMaskBit.Present.getValue()
						));
				balanceDetailCriteria.setNotStatusBitMarks(Collections.singletonList(BalanceDetail.maskBit.WithDrawRelated.getValue()));
				balanceDetailCriteria.setType(ProfitType.InCome.getValue());
				break;
			}
			case 3: {
				/// 待收益
				balanceDetailCriteria.setStatusBitMarks(
						Arrays.asList(
								IsPersistAble.maskBit.PersistAble.getValue(),
								OutComeDetail.orderRelatedMask.ShopOrder.getValue()
						)
				);
				balanceDetailCriteria.setNotStatusBitMarks(Collections.singletonList(IsPresent.presentMaskBit.Present.getValue()));
				break;
			}
			case 4: {
				/// 提现
				balanceDetailCriteria.setStatusBitMarks(Arrays.asList(
						OutComeDetail.maskBit.WithDrawRelated.getValue(),
						IsPersistAble.maskBit.PersistAble.getValue()
				));
				break;
			}
			default: {

			}
		}
		if (balanceDetailCriteria.getSourceId() != null) {
			Map<String, String> extra = Optional.ofNullable(shopCacheHolder.findShopById(balanceDetailCriteria.getSourceId())).map(Shop::getExtra).orElse(new HashMap<>(8));
			if (!"1".equals(extra.getOrDefault(ShopExtra.openFans.getCode(), "1"))) {
				List<Integer> notMaskBit = new ArrayList<>(Optional.ofNullable(balanceDetailCriteria.getNotStatusBitMarks()).orElse(new ArrayList<>()));
				notMaskBit.add(BalanceDetail.SourceMark.Linked.getBitMark());
				balanceDetailCriteria.setNotStatusBitMarks(notMaskBit);
			}
		}
		log.debug("{} criteria:{}", LogUtil.getClassMethodName(), balanceDetailCriteria.toMap());
		return balanceDetailReadService.paging(balanceDetailCriteria.toMap()).getResult().getData().stream()
				.filter(detail -> detail.getType() != ProfitType.CacheInCome.getValue())
				.map(BalanceDetailVo::new).collect(Collectors.toList());
	}

	private void setSmsCode(HttpSession session, String code) {
		code = code + "," + Instant.now().getEpochSecond();
		log.info("[SubStores](setSmsCode) set SmsCode:{}", code);
		session.setAttribute(WithDrawSessionCode, code);
	}

	private Optional<String> getSmsCode(HttpSession session) {
		String code = (String) session.getAttribute(WithDrawSessionCode);
		log.debug("{} withdraw-password-set sms-code [{}] by sessionId[{}]", LogUtil.getClassMethodName(), code, session.getId());
		if (code == null) {
			return Optional.empty();
		}
		String[] rom = code.split(",");
		if (rom.length != 2) {
			log.error("[SubStores](getSms) error in split code:{}", code);
			return Optional.empty();
		}
		try {
			if (Instant.now().getEpochSecond() - Long.parseLong(rom[1]) > 60 * timeoutMinus) {
				return Optional.empty();
			}
			return Optional.of(rom[0]);
		} catch (Exception ex) {
			log.error("[SubStores](getSms) parse time failed code:{}", code);
			ex.printStackTrace();
			return Optional.empty();
		}
	}

	/**
	 * 由用户Id获取可用钱包数据
	 */
	private Response<InComeDetail> getPresentCash(long userId, long sourceId) {
		Response<List<InComeDetail>> listResponse = balanceDetailManager.getCash(userId, sourceId);
		if (!listResponse.isSuccess()) {
			log.error("[SubStores](changeWithDrawPassword) userId:{} failed because:{}", userId, listResponse.getError());
			return Response.fail(listResponse.getError());
		}
		List<InComeDetail> cachedList = listResponse.getResult();
		if (CollectionUtils.isEmpty(cachedList)) {
			cachedList = balanceDetailManager.initCash(userId, sourceId);
		}
		Optional<InComeDetail> oPresentInCome = getPresentCash(cachedList);
		if (!oPresentInCome.isPresent()) {
			log.error("[SubStores](changeWithDrawPassword) fail to get Cash for userId:{}", userId);
			return Response.fail("get cash fail");
		}
		return Response.ok(oPresentInCome.get());
	}

	@PostMapping("/change-withdraw-password")
	public boolean changeWithDrawPassword(long sourceId, String oldPassword, String password) {
		long userId = Optional.ofNullable(UserUtil.getCurrentUser().getId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
		InComeDetail presentInCome = Optional.ofNullable(getPresentCash(userId, sourceId).getResult()).orElseThrow(() ->
				new JsonResponseException(new Translate("获取账户信息失败").toString()));
		password = EncryptUtil.encrypt(password);
		if (!presentInCome.getExtra().containsKey(BalanceDetail.ExtraEnu.password.getIndex())) {
			throw new JsonResponseException(new Translate("支付密码不存在,请先设置支付密码").toString());
		}
		if (!EncryptUtil.match(oldPassword, presentInCome.getExtra().get(BalanceDetail.ExtraEnu.password.getIndex()))) {
			throw new JsonResponseException(new Translate("旧密码错误,请重试").toString());
		}
		presentInCome.getExtra().put(BalanceDetail.ExtraEnu.password.getIndex(), password);
		return balanceDetailWriteService.update(presentInCome).isSuccess();
	}

	@PostMapping("/set-withdraw-password")
	public boolean setWithDrawPassword(long sourceId, String smsCode, String password, HttpSession session) {
		long userId = Optional.ofNullable(UserUtil.getCurrentUser().getId())
				.orElseThrow(() -> new JsonResponseException("user.not.login"));
		boolean match = getSmsCode(session).map(code -> code.equals(smsCode))
				.orElseThrow(() -> new JsonResponseException("sms.code.expired"));
		if (!match) {
			throw new JsonResponseException("sms.code.mismatch");
		}
		InComeDetail presentInCome = Optional.ofNullable(getPresentCash(userId, sourceId).getResult())
				.orElseThrow(() -> new JsonResponseException(new Translate("获取账户信息失败").toString()));
		password = EncryptUtil.encrypt(password);
		if (presentInCome.getExtra().containsKey(BalanceDetail.ExtraEnu.password.getIndex())) {
			throw new JsonResponseException(new Translate("支付密码已经存在").toString());
		}
		presentInCome.getExtra().put(BalanceDetail.ExtraEnu.password.getIndex(), password);
		return balanceDetailWriteService.update(presentInCome).isSuccess();
	}

	@PostMapping("/forget-withdraw-password")
	public boolean forgetWithDrawPassword(long sourceId, String smsCode, String password, HttpSession session) {
		long userId = Optional.ofNullable(UserUtil.getCurrentUser().getId())
				.orElseThrow(() -> new JsonResponseException("user.not.login"));
		boolean match = getSmsCode(session).map(code -> code.equals(smsCode))
				.orElseThrow(() -> new JsonResponseException("sms.code.expired"));
		if (!match) {
			throw new JsonResponseException("sms.code.mismatch");
		}
		InComeDetail presentInCome = Optional.ofNullable(getPresentCash(userId, sourceId).getResult())
				.orElseThrow(() -> new JsonResponseException(new Translate("获取账户信息失败").toString()));
		password = EncryptUtil.encrypt(password);
		presentInCome.getExtra().put(BalanceDetail.ExtraEnu.password.getIndex(), password);
		return balanceDetailWriteService.update(presentInCome).isSuccess();
	}

	/**
	 * 从列表中获取到presentInCome
	 */
	private Optional<InComeDetail> getPresentCash(List<InComeDetail> inComeDetails) {
		Objects.requireNonNull(inComeDetails);
		for (InComeDetail inComeDetail : inComeDetails) {
			if (inComeDetail.isPresent()) {
				return Optional.of(inComeDetail);
			}
		}
		return Optional.empty();
	}

	/**
	 * 发送SMS信息提供修改验证码
	 */
	@PostMapping("/set-withdraw-password-sms")
	public boolean getWithDrawPasswordSms(HttpSession session, @RequestParam(required = false) Long shopId) {
		User user = Optional.ofNullable(userReadService.findById(UserUtil.getUserId()).getResult()).orElseThrow(() -> new JsonResponseException("user.not.login"));
		Random random = new Random(Instant.now().getEpochSecond());
		byte[] rom = new byte[6];
		for (int i = 0, num = 0; i < rom.length; i++, num = Math.abs(random.nextInt() % 10)) {
			rom[i] = (byte) (num + '0');
		}
		setSmsCode(session, new String(rom));
		String template ="设置提现密码验证";
		MsgSendRequestEvent event = shopId == null ?
				new MsgSendRequestEvent(user.getMobile(), template, ImmutableMap.of("code", new String(rom), "name", user.getName(), "operation", "忘记提现密码"))
				: new MsgSendRequestEvent(user.getMobile(), template, ImmutableMap.of("code", new String(rom)));

		EventSender.sendApplicationEvent(event);
		return true;
	}

	@GetMapping("/export/balanceDetail")
	public void exportBalance(HttpServletRequest request, HttpServletResponse response, BalanceDetailCriteria criteria, @RequestParam(defaultValue = "false") boolean skipAuth) {

		response.setHeader("Content-Disposition", "attachment; filename=\"balanceDetails.xls\"");
		response.setContentType("application/octet-stream;charset=utf-8");
		CommonUser user = UserUtil.getCurrentUser();
		criteria.setPageSize(3000);
		if (!skipAuth) {
			criteria.setSourceId(user.getShopId());
		}
		if (criteria.getSourceId() != null) {
			Map<String, String> extra = Optional.ofNullable(shopCacheHolder.findShopById(criteria.getSourceId())).map(Shop::getExtra).orElse(new HashMap<>(8));
			if (!"1".equals(extra.getOrDefault(ShopExtra.openFans.getCode(), "1"))) {
				List<Integer> notMaskBit = new ArrayList<>(Optional.ofNullable(criteria.getNotStatusBitMarks()).orElse(new ArrayList<>()));
				notMaskBit.add(BalanceDetail.SourceMark.Linked.getBitMark());
				criteria.setNotStatusBitMarks(notMaskBit);
			}
		}
		Response<Paging<BalanceDetail>> rDetailPaging = balanceDetailReadService.paging(criteria.toMap());
		LoadingCache<Long, Optional<String>> nameCache =
				Caffeine.newBuilder().expireAfterWrite(15, TimeUnit.SECONDS)
						.build(key -> Optional.ofNullable(userProfileReadService.findProfileByUserId(key).getResult()).map(UserProfile::getRealName));
		if (rDetailPaging.isSuccess()) {
			List<BalanceDetail> dataList = rDetailPaging.getResult().getData();
			List<BalanceVO> voList = new ArrayList<>(dataList.size());
			for (BalanceDetail detail : dataList) {
				BalanceVO vo = new BalanceVO();
				vo.setId(detail.getId());
				vo.setUserId(detail.getUserId());
				vo.setOwnerName(Objects.requireNonNull(nameCache.get(detail.getUserId())).orElse(new Translate("用户:").toString() + detail.getUserId()));
				vo.setChangeFee(new BigDecimal(detail.getChangeFee() + "").divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
				vo.setPacketFeeWhenChange(new BigDecimal(detail.getFee() == null ? "0" : detail.getFee().toString() + "").divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
				vo.setType(detail.getType() == ProfitType.CacheInCome.getValue()
						? new Translate("钱包").toString()
						: detail.getType() == ProfitType.InCome.getValue()
						? new Translate("收入").toString() : new Translate("支出").toString());
				vo.setWhen(new DateTime(detail.getCreatedAt()).toString("YYYY-MM-dd hh:mm:ss"));
				vo.setRelatedId(detail.getRelatedId());
				// 开始设置字段
				String prefix = new Translate("待收入").toString();
				String body = new Translate("普通").toString();
				if (detail.isPresent()) {
					prefix = new Translate("已收入").toString();
				}
				if (detail.isLinked()) {
					body = new Translate("间接").toString();
				}
				if (detail.isTransmit()) {
					body = new Translate("二级").toString();
				}
				if (detail.isReach()) {
					body = new Translate("一级").toString();
				}
				if (detail.isOrderRelated()) {
					vo.setRelatedType(detail.getOrderLevel().orElse(OrderLevel.SHOP).equals(OrderLevel.SHOP) ? new Translate("订单").toString() : new Translate("子订单").toString());
				}
				if (detail.getType() != ProfitType.CacheInCome.getValue()) {
					detail = detail.getType() == ProfitType.InCome.getValue() ? new InComeDetail(detail) : new OutComeDetail(detail);
					if (detail instanceof InComeDetail) {
						InComeDetail inComeDetail = (InComeDetail) detail;
						if (inComeDetail.isWithDrawRelated()) {
							body = new Translate("提现失败回退").toString();
							vo.setRelatedType(new Translate("提现单").toString());
						}
						if (!inComeDetail.isPersistAble()) {
							body = new Translate("仅记录") + " " + body;
						}
					} else {
						OutComeDetail outComeDetail = (OutComeDetail) detail;
						if (outComeDetail.isWithDrawRelated()) {
							body = new Translate("提现").toString();
							vo.setRelatedType(new Translate("提现单").toString());
						}
						if (outComeDetail.isRefundRelated()) {
							body = new Translate("退款").toString();
							vo.setRelatedType(new Translate("退款单").toString());
						}
					}
					if (detail.isIgnoreAble()) {
						body = new Translate("内部流转") + " " + body;
					}
					body += detail.isPresent() ? new Translate("可提现").toString() : new Translate("待提现").toString();
					vo.setStatus(prefix + " " + body);
				}
				if (detail.getStatus() < 0) {
					// skip the wrong data
					continue;
				}
				voList.add(vo);
			}
			voList.sort(Comparator.comparing(BalanceVO::getId));
			Either<Workbook> workbookResult = SimpleExportUtil.tryMake(voList, BalanceVO.class, new Translate("收入明细导出表").toString());
			Workbook defaultErrorWorkBook = new XSSFWorkbook();
			Sheet sheet = defaultErrorWorkBook.createSheet(new Translate("结果").toString());
			sheet.createRow(0).createCell(0).setCellValue(new Translate("数据读取失败")
					+ " " + (workbookResult.getErrorMsg() == null ? new Translate("未知异常").toString() : workbookResult.getErrorMsg()));
			try {
				OutputStream outputStream = response.getOutputStream();
				workbookResult.orElse(defaultErrorWorkBook).write(outputStream);
			} catch (Exception ex) {
				ex.printStackTrace();
				log.error("{} ex:{} stack:{} criteria:{} data:{}", LogUtil.getClassMethodName(), ex, ex.getStackTrace()[0], criteria, rDetailPaging);
			}
		}
	}

	@GetMapping("/export/withdraw")
	public APIResp<Long> exportWithdrawList(@RequestParam(required = false) Map<String, Object> param,
											@RequestParam(defaultValue = "0") int type,
											HttpServletRequest request, HttpServletResponse response) {
		CommonUser operator = UserUtil.getCurrentUser();
		if (operator == null) {
			return APIResp.error("请先登录");
		}
		if (operator.getShopId() == null) {
			return APIResp.error("商家平台id为空");
		}

		try {
			//导出文件名
			String fileName = DataExportTaskTypeEnum.WITHDRAW_PROFIT_APPLY_OLD.getDescription() + DateUtil.getDateString() + ".xlsx";

			//查询参数
			var parameter = convert(param);
			WithdrawTypeParser.parseTheWithdrawType(type, parameter);
			withdrawProxyModeJudgeComponent.judgeProxyModeAndFillTheSubStoreUserIdList(operator, parameter);

			//执行导出
			DataExportTask task = withdrawProfitApplyExporter.asyncExport(UserUtil.getCurrentUser(), fileName, parameter);

			return Result.data(task.getId());
		} catch (Exception ex) {
			log.error("BalanceDetails.exportWithdrawList error ", ex);
			return APIResp.error(ex.getMessage());
		}
	}

	private WithDrawProfitApplyCriteriaDTO convert(Map<String, Object> param) {
		WithDrawProfitApplyCriteriaDTO criteriaDTO = new WithDrawProfitApplyCriteriaDTO();

		try {
			var name = param.get("name");
			if (name != null && StringUtils.hasText(name.toString())) {
				criteriaDTO.setName(name.toString().trim());
			}

			var mobile = param.get("mobile");
			if (mobile != null && StringUtils.hasText(mobile.toString())) {
				criteriaDTO.setMobile(mobile.toString().trim());
			}

			var type = param.get("type");
			if (type != null && StringUtils.hasText(type.toString())) {
				criteriaDTO.setType(Integer.parseInt(type.toString()));
			}

			var sourceId = param.get("sourceId");
			if (sourceId != null && StringUtils.hasText(sourceId.toString())) {
				criteriaDTO.setSourceId(Long.parseLong(sourceId.toString()));
			}

			var userType = param.get("userType");
			if (userType != null && StringUtils.hasText(userType.toString())) {
				criteriaDTO.setUserType(userType.toString().trim());
			}
		} catch (Exception ex) {
			log.error("convert WithDrawProfitApplyCriteriaDTO error, param={}", JSON.toJSONString(param), ex);
		}

		return criteriaDTO;
	}

	/**
	 * query the name from subStore system, require to check identify from sequence
	 *
	 * @param shopId shopId
	 * @param userId userId
	 * @return user name
	 * @apiNote lazy fetch from stream
	 */
	private String queryNameFromSubStoreSystem(Long shopId, Long userId) {
		return Stream.<Supplier<Optional<String>>>of(() ->
						// check if serviceProvider
						serviceProviderCache.findServiceProviderByShopIdAndUserId(shopId, userId)
								.map(UserLevelView::getRelation)
								.map(WithExtraMap::getExtra)
								.map(extra -> extra.get("name")),
				// check if subStore
				() -> subStoreCache.findByShopIdAndUserId(shopId, userId).map(SubStore::getName),
				// check if guider
				() -> guiderCache.findByShopIdAndUserId(shopId, userId).map(SubStoreTStoreGuider::getStoreGuiderNickname))
				.map(Supplier::get)
				.filter(Optional::isPresent)
				.map(Optional::get)
				.findFirst()
				.orElseGet(() ->
						Optional.ofNullable(userReadService.findById(userId).getResult())
								.map(User::getName).orElse("用户" + userId + "名字丢失"));
	}

	private String findTheLeftFee(Long withdrawApplyId, Long userId, Long sourceId) {
		return balanceDetailReadService.findByRelatedIdAndTypeAndSourceAndStatusMask(withdrawApplyId, userId, sourceId, 2, IsPersistAble.maskBit.PersistAble.getValue())
				.getResult().stream().findFirst()
				.map(BalanceDetail::getFee)
				.map(fee -> BigDecimal.valueOf(fee).divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN))
				.map(BigDecimal::toString)
				.orElse("数据丢失");
	}

	@Data
	static class BalanceVO {
		@ExcelField(name = "Id", column = "A")
		Long id;
		@ExcelField(name = "用户Id", column = "B")
		Long userId;
		@ExcelField(name = "用户名", column = "C")
		String ownerName;
		@ExcelField(name = "类型", column = "D")
		String type;
		@ExcelField(name = "状态", column = "E")
		String status;
		@ExcelField(name = "变动额度", column = "F")
		BigDecimal changeFee;
		@ExcelField(name = "变动时钱包额度", column = "G")
		BigDecimal packetFeeWhenChange;
		@ExcelField(name = "关联单类型", column = "H")
		String relatedType;
		@ExcelField(name = "关联单Id", column = "I")
		Long relatedId;
		@ExcelField(name = "时间", column = "J")
		String when;
	}

	@Data
	public static class BalanceDetailVo {
		Date createdAt;
		String status = "";
		long fee;

		BalanceDetailVo(BalanceDetail detail) {
			createdAt = detail.getCreatedAt();
			fee = detail.getChangeFee();
			if (detail.getType().equals(ProfitType.InCome.getValue())) {
				InComeDetail inComeDetail = new InComeDetail(detail);
				status = "待收益";
				if (inComeDetail.isOrderRelated()) {
					if (inComeDetail.isPresent()) {
						status = "已收益";
					}
				}
				if (inComeDetail.isWithDrawRelated()) {
					status = "提现失败退款";
				}
				if (inComeDetail.isIntegralRelated()) {
					status = "积分";
				}
				if (inComeDetail.isLinked()) {
					status = "(忠诚)" + status;
				}
				if (inComeDetail.isExLinked()) {
					status = "(新客)" + status;
				}
			} else if (detail.getType().equals(ProfitType.OutCome.getValue())) {
				OutComeDetail outComeDetail = new OutComeDetail(detail);
				status = "支出";
				if (outComeDetail.isRefundRelated()) {
					status = "退款";
				}
				if (outComeDetail.isWithDrawRelated()) {
					status = "提现";
				}
				if (outComeDetail.isIgnoreAble()) {
					status = "(转入已收益)" + status;
				}
				fee = -fee;
			}
			status = new Translate(status).toString();
		}
	}
}
