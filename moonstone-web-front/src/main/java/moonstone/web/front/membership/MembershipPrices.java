package moonstone.web.front.membership;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.CountryCode;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.membership.dto.MembershipPriceInfo;
import moonstone.membership.dto.paging.MembershipPriceCriteria;
import moonstone.membership.model.MembershipPrice;
import moonstone.membership.service.MembershipPriceReadService;
import moonstone.membership.service.MembershipPriceWriteService;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.front.component.item.MembershipPriceReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * Author: g
 * Date: 2018-07-25
 */
@RestController
@Slf4j
@RequestMapping("/api/membershipPrice")
public class MembershipPrices {

    @RpcConsumer
    private MembershipPriceReadService membershipPriceReadService;

    @RpcConsumer
    private MembershipPriceWriteService membershipPriceWriteService;

    @Autowired
    private MembershipPriceReadLogic membershipPriceReadLogic;

    @Autowired
    private Exporter exporter;

    /**
     * 新增会员价记录
     * @param membershipPrice
     * @return 新增记录ID
     */
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody MembershipPrice membershipPrice){
        try {
            Long shopId = getCurrentShopId();
            membershipPrice.setShopId(shopId);
            Response<Long> resp = membershipPriceWriteService.create(membershipPrice);
            if (!resp.isSuccess()) {
                log.warn("failed to create {}, error code:{}", membershipPrice, resp.getError());
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to create {}, cause:{}", membershipPrice, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("membershipPrice.create.fail");
        }
    }

    /**
     * 删除会员价记录
     * @param membershipPriceId 记录ID
     * @return 操作结果
     */
    @RequestMapping(value = "/{id}",method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean delete(@PathVariable("id")Long membershipPriceId){
        try{
            checkShopId(membershipPriceId);
            Response<Boolean> resp = membershipPriceWriteService.delete(membershipPriceId);
            if (!resp.isSuccess()) {
                log.warn("failed to delete membership by id = {}, error code:{}", membershipPriceId, resp.getError());
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        }catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to delete membershipPrice by Id = {}, cause:{}", membershipPriceId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("membershipPrice.delete.fail");
        }
    }

    /**
     * 更新会员价记录
     *
     * @param membershipPriceId 记录ID
     * @param membershipPrice   新内容
     * @return 更新结果
     */
    @RequestMapping(value = "/{id}",method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean update(@PathVariable("id")Long membershipPriceId,@RequestBody MembershipPrice membershipPrice){
        try{
            Long shopId = getCurrentShopId();
            checkShopId(membershipPriceId);
//            避免在此处产生假删效果
            membershipPrice.setStatus(null);
            membershipPrice.setId(membershipPriceId);
            membershipPrice.setShopId(shopId);
            Response<Boolean> resp = membershipPriceWriteService.update(membershipPrice);
            if (!resp.isSuccess()) {
                log.warn("failed to update{}, error code:{}", membershipPrice, resp.getError());
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        }catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to update {}, cause:{}", membershipPrice, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("membershipPrice.update.fail");
        }
    }

    /**
     * 根据分页信息查询当前登录用户店铺下的会员价记录
     * @param skuName 查询商品名称条件
     * @param userName 查询用户名条件
     * @param pageNo 页码
     * @param pageSize 页内消息数量
     * @return 查询结果
     */
    @RequestMapping(value = "/shop",method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<MembershipPriceInfo> pagingShopMembershipPrice(@RequestParam(required = false) List<Long> ids,
                                                                 @RequestParam(required = false) String skuName,
                                                                 @RequestParam(required = false) String userName,
                                                                 @RequestParam(required = false) Integer pageNo,
                                                                 @RequestParam(required = false) Integer pageSize) {
        try{
            Long shopId = getCurrentShopId();
            MembershipPriceCriteria criteria = new MembershipPriceCriteria();
            criteria.setIds(ids);
            criteria.setShopId(shopId);
            criteria.setSkuName(skuName);
            criteria.setUserName(userName);
            criteria.setPageNo(pageNo);
            criteria.setPageSize(pageSize);
            Response<Paging<MembershipPriceInfo>> response = membershipPriceReadLogic.pagingShopMembershipPrice(criteria);
            if (!response.isSuccess()) {
                log.error("failed to paging membership price for shop(id={}) by skuName={}, userName={}, pageNo={}, pageSize={}, error code: {}",
                        shopId, skuName, userName, pageNo, pageSize, response.getError());
                throw new JsonResponseException(response.getError());
            }
            //add by liuchao 20190415
            List<MembershipPriceInfo> data = new ArrayList<>();
            for(MembershipPriceInfo membershipPriceInfo:response.getResult().getData()){
                if (membershipPriceInfo.getMobile() != null && (membershipPriceInfo.getMobile().startsWith(CountryCode.China.getCode()) || membershipPriceInfo.getMobile().startsWith(CountryCode.Australia.getCode()))) {
                    membershipPriceInfo.setMobile(membershipPriceInfo.getMobile().substring(CountryCode.PREFIX_CODE_LEN));
                }
                data.add(membershipPriceInfo);
            }
            return new Paging(response.getResult().getTotal(),data);
        }catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to find membershipPrice, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("membershipPrice.found.fail");
        }
    }

    @RequestMapping(value = "/shop/membership-price-export", method = RequestMethod.GET)
    public void exportSellingItems(MembershipPriceCriteria criteria, HttpServletResponse response, HttpServletRequest request) {
        checkShopIdNotNull();
        CommonUser user= UserUtil.getCurrentUser();
        criteria.setShopId(user.getShopId());
        exporter.export(MembershipPriceInfo.class, criteria,
                membershipPriceReadLogic::pagingShopMembershipPrice,
                request, response);
    }

    private void checkShopIdNotNull(){
        CommonUser user= UserUtil.getCurrentUser();
        if(user.getShopId()==null){
            log.warn("permission deny for user={}", user);
            throw new JsonResponseException("permission.deny");
        }
    }

    /**
     * 检查该记录是否存在
     * 检查登录用户是否对该记录拥有操作权限
     * @param membershipPriceId
     */
    private void checkShopId(Long membershipPriceId){
        Long shopId = getCurrentShopId();
//            检验该记录是否存在
        Response<MembershipPrice> rMembershipPrice = membershipPriceReadService.findById(membershipPriceId);
        if (!rMembershipPrice.isSuccess()){
            log.error("failed to find MembershipPrice by id={}, cause:{}", membershipPriceId);
            throw new JsonResponseException(rMembershipPrice.getError());
        }
//            检验该账户是否对该记录拥有操作权限
        if (!shopId.equals(rMembershipPrice.getResult().getShopId())){
            log.error("MembershipPrice(id={}) not belong to the Seller", membershipPriceId);
            throw new JsonResponseException("insufficient.operation.permissions");
        }
    }

    /**
     * 查找当前登录账户的shop信息
     * @return
     */
    private Long getCurrentShopId(){
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null){
            throw new JsonResponseException(401, "user.not.login");
        }
        Long shopId = user.getShopId();
        if (shopId == null){
            log.warn("no shop found for user(id={})", user.getId());
            throw new JsonResponseException(403, "user.no.permission");
        }
        return shopId;
    }
}
