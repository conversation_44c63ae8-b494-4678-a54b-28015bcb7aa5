package moonstone.web.front.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.search.model.ItemsEsIndex;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.component.item.SearchItemProfitViewHelper;
import moonstone.web.front.item.app.ItemSearchDecorateHelper;
import moonstone.web.front.item.search.ItemSearchesNew;
import moonstone.web.front.shop.app.ShopExtraConfigureComponent;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ItemsEsIndexFrontLogic {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private ShopExtraConfigureComponent shopExtraConfigureComponent;

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Resource
    private SubStoreCache subStoreCache;

    @Resource
    private GuiderCache guiderCache;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private SearchItemProfitViewHelper searchItemProfitViewHelper;

    @Resource
    private ItemSearchDecorateHelper itemSearchDecorateHelper;


    public ItemSearchesNew.SearchedItemNew miniPageItem(Map<String, String> params) {
        JSONObject paramsJson = checkAndAppendParams(params);
        log.info("填充后的参数 {}", JSONUtil.toJsonStr(paramsJson));
        // 构建查询条件
        NativeSearchQueryBuilder queryBuilder = createQueryParamsBuild(paramsJson);
        NativeSearchQuery query = queryBuilder.build();
        log.info("查询的语句 {}", query.getQuery());
        SearchHits<ItemsEsIndex> searchHits = elasticsearchRestTemplate.search(query, ItemsEsIndex.class);
        long total = searchHits.getTotalHits();
        ItemSearchesNew.SearchedItemNew result = new ItemSearchesNew.SearchedItemNew();
        if (total == 0) {
            return result;
        }
        Long shopId = paramsJson.get("shopId", Long.class);
        Shop shop = shopCacheHolder.findShopById(shopId);
        boolean isCommonShop = Optional.ofNullable(shop.getExtra()).orElse(new HashMap<>()).getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()).equals(ShopExtra.commonShop.getCode());
        List<ItemSearchesNew.ItemsNew> inl = new ArrayList<>();
        for (SearchHit<ItemsEsIndex> hit : searchHits.getSearchHits()) {
            ItemsEsIndex itemsEsIndex = hit.getContent();
            ItemSearchesNew.ItemsNew itemsNew = new ItemSearchesNew.ItemsNew();
            itemsNew.setId(itemsEsIndex.getId());
            itemsNew.setShopId(itemsEsIndex.getShopId());
            itemsNew.setShopName(itemsEsIndex.getShopName());
            itemsNew.setPrice(itemsEsIndex.getPrice());
            itemsNew.setSaleQuantity(itemsEsIndex.getSaleQuantity());
            itemsNew.setIsBonded(itemsEsIndex.getIsBonded());
            itemsNew.setMainPic(itemsEsIndex.getMainImage());
            itemsNew.setTitle(infoNames(itemsEsIndex.getName()));
            if (itemsEsIndex.getOriginId() != null) {
                com.alibaba.fastjson.JSONObject o = new com.alibaba.fastjson.JSONObject();
                o.put("id", itemsEsIndex.getOriginId());
                o.put("icon", itemsEsIndex.getOriginUrl());
                o.put("name", itemsEsIndex.getOrigin());
                itemsNew.setOrigin(o);
            }
            //目前只有普通商品才会显示佣金率
            if (Objects.equals(itemsEsIndex.getType(), 1) && !isCommonShop) {
                itemsNew.setRate(searchItemProfitViewHelper.packProfitView(itemsEsIndex.getId(), String.valueOf(shopId), itemsEsIndex.getPrice(), UserUtil.getCurrentUser()));
            }
            inl.add(itemsNew);
        }
        //填充额外的商品和sku信息
        itemSearchDecorateHelper.appendAdditionalItemInfo(inl);
        itemSearchDecorateHelper.appendAdditionalSkuInfo(inl);
        result.setList(inl);
        result.setTotal(total);
        return result;
    }

    //过滤所有的搜索<em></em>
    private String infoNames(String name) {
        return name.replaceAll("<em>", "").replaceAll("</em>", "");
    }


    private NativeSearchQueryBuilder createQueryParamsBuild(JSONObject paramsJson) {
        Long shopId = paramsJson.get("shopId", Long.class);
        NativeSearchQueryBuilder query = new NativeSearchQueryBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("shopId", shopId));
        if (ObjUtil.isNotEmpty(paramsJson.get("type"))) {
            queryBuilder.must(QueryBuilders.termQuery("type", paramsJson.get("type")));
        }
        if (ObjUtil.isNotEmpty(paramsJson.get("ids"))) {
            String[] defaultFrontPageItemIdArray = paramsJson.get("ids", String.class).split("_");
            queryBuilder.must(QueryBuilders.termsQuery("id", defaultFrontPageItemIdArray));
        }
        if (ObjUtil.isNotEmpty(paramsJson.get("ssid"))) {
            queryBuilder.must(QueryBuilders.termsQuery("ssid", paramsJson.get("ssid")));
        }
        if (ObjUtil.isNotEmpty(paramsJson.get("shopCatId"))) {
            queryBuilder.must(QueryBuilders.termsQuery("shopCategoryIds", paramsJson.get("shopCatId")));
        }
        query.withQuery(queryBuilder);
        FieldSortBuilder cateIndexAscSort = SortBuilders.fieldSort("categoryIndex").order(SortOrder.ASC);
        FieldSortBuilder indexAscSort = SortBuilders.fieldSort("index").order(SortOrder.ASC);
        FieldSortBuilder updatedAtDescSort = SortBuilders.fieldSort("updatedAt").order(SortOrder.DESC);
        query.withSorts(cateIndexAscSort, indexAscSort, updatedAtDescSort);
        Integer pageNo = paramsJson.get("pageNo", Integer.class);
        Integer pageSize = paramsJson.get("pageSize", Integer.class);
        PageRequest pageable = PageRequest.of(pageNo-1, pageSize);
        query.withPageable(pageable);
        return query;
    }

    private JSONObject checkAndAppendParams(Map<String, String> params) {
        JSONObject result = BeanUtil.toBean(params, JSONObject.class);
        Long shopId = result.get("shopId", Long.class);
        if (shopId == null) {
            throw new ApiException("店铺id不能为空");
        }
        Long pageNo = result.get("pageNo", Long.class);
        pageNo = pageNo == null ? 1 : pageNo;
        result.set("pageNo", pageNo);
        Long pageSize = result.get("pageSize", Long.class);
        pageSize = pageSize == null ? 20 : Math.min(pageSize, 20);
        result.set("pageSize", pageSize);
        String typeAll = result.get("typeAll", String.class);
        if (StrUtil.isBlank(typeAll)) {
            Integer type = result.get("type", Integer.class);
            if (type == null) {
                result.set("type", 1);
            }
        }
        CommonUser currentUser = UserUtil.getCurrentUser();
        if (currentUser == null) {
            // 用户未登录的情况  显示默认商品
            String defaultFrontPageItemIds = shopExtraConfigureComponent.findDefaultFrontPageItemIds(shopId);
            if (StrUtil.isNotBlank(defaultFrontPageItemIds)) {
                result.set("ids", defaultFrontPageItemIds);
                result.remove("ssid");
            }
            return result;
        }
        Long userId = currentUser.getId();
        Map<String, String> shopModel = Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra())
                .orElse(Collections.emptyMap());
        if (!SalePattern.SubStore.getCode().equals(shopModel.get(ShopExtra.SalesPattern.getCode()))) {
            return result;
        }
        Optional<SubStore> subStore = subStoreCache.findByShopIdAndUserId(shopId, userId);
        if (subStore.isPresent()) {
            result.set("ssid", subStore.get().getId());
            return result;
        }
        Optional<SubStoreTStoreGuider> guider = guiderCache.findByShopIdAndUserId(shopId, userId);
        if (guider.isPresent()) {
            result.set("ssid", guider.get().getSubStoreId());
            return result;
        }
        serviceProviderCache.findServiceProviderByShopIdAndUserId(shopId, userId)
                .ifPresent(userLevelView ->
                        result.set("ssid", serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, shopId)
                                .getId())
                );
        return result;
    }
}
