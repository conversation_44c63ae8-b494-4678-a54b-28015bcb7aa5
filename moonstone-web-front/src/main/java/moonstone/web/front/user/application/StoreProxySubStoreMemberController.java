package moonstone.web.front.user.application;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.view.SubUserView;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.user.service.UserRelationEntityWriteService;
import moonstone.web.core.user.service.StoreProxySubUserManager;
import moonstone.web.front.component.user.UserViewBuildHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店下用户相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/subStore/member")
public class StoreProxySubStoreMemberController {
    @Autowired
    private UserRelationEntityReadService userRelationEntityReadService;
    @Autowired
    private UserRelationEntityWriteService userRelationEntityWriteService;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private UserProfileReadService userProfileReadService;
    @Autowired
    private StoreProxySubUserManager storeProxySubUserManager;
    @Autowired
    private UserViewBuildHelper userViewBuildHelper;

    /**
     * 分页查询门店用户（GET）
     * // d1: 待废弃 10月1号之后
     * @param criteria 查询条件对象
     * @return SubUserView对象的分页信息
     */
    @GetMapping("/page")
    @Deprecated
    public Paging<SubUserView> pagingGet(UserRelationEntityCriteria criteria) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        criteria.setRelationId(user.getShopId());
        criteria.setType(UserRelationEntity.UserRelationType.Member.getType());
        if (!ObjectUtils.isEmpty(criteria.getName())) {
            criteria.setUserIds(userProfileReadService.findProfileByRealNameLike(criteria.getName()).take().stream().map(UserProfile::getUserId).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(criteria.getMobile())) {
            criteria.setUserIds(userReadService.findLikeMobile(criteria.getMobile()).getResult().stream().map(User::getId).collect(Collectors.toList()));
        }
//        if (criteria.getCreatedFrom() != null) {
//            criteria.setCreatedFrom(DateUtil.withTimeAtStartOfDay(criteria.getCreatedFrom()));
//        }
//        if (criteria.getCreatedTo() != null) {
//            criteria.setCreatedTo(DateUtil.withTimeAtEndOfDay(criteria.getCreatedTo()));
//        }
        if (criteria.getRegistrationTimestampStart() != null && criteria.getRegistrationTimestampEnd() != null) {
            criteria.setCreatedFrom(new Date(criteria.getRegistrationTimestampStart()));
            criteria.setCreatedTo(new Date(criteria.getRegistrationTimestampEnd()));
        }
        //因身份数据隔离--进行数据隔离
        if (!ObjectUtils.isEmpty(user.getShopId())) {
            Boolean isolatedByLevelDistributionMode = storeProxySubUserManager.isAuthProxy(user.getId(), user.getShopId());
            if (isolatedByLevelDistributionMode) {
                List<Long> userIds = storeProxySubUserManager.queryBelongProxyHonestFanUserIdList(user.getId(), user.getShopId());
                if (userIds.isEmpty()) {
                    return Paging.empty();
                }
                if (criteria.getUserIds() != null && !criteria.getUserIds().isEmpty()) {
                    userIds.retainAll(criteria.getUserIds());
                }
                criteria.setUserIds(userIds);
            }
        }

        Paging<UserRelationEntity> userRelationEntityViewPage = userRelationEntityReadService.paging(criteria).getResult();
        if (userRelationEntityViewPage == null || userRelationEntityViewPage.isEmpty()) {
            return Paging.empty();
        }
        return new Paging<>(userRelationEntityViewPage.getTotal(),
                userRelationEntityViewPage.getData().stream()
                        .map(entity -> userViewBuildHelper.buildUserView(entity, user.getShopId()))
                        .collect(Collectors.toList()));
    }

    /**
     * 分页查询门店用户（POST）
     *
     * @param criteria 查询条件对象
     * @return SubUserView对象的分页信息
     */
    @PostMapping("/page")
    public Paging<SubUserView> pagingPost(@RequestBody UserRelationEntityCriteria criteria) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        criteria.setRelationId(user.getShopId());
        criteria.setType(UserRelationEntity.UserRelationType.Member.getType());
        if (!ObjectUtils.isEmpty(criteria.getName())) {
            criteria.setUserIds(userProfileReadService.findProfileByRealNameLike(criteria.getName()).take().stream().map(UserProfile::getUserId).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(criteria.getMobile())) {
            criteria.setUserIds(userReadService.findLikeMobile(criteria.getMobile()).getResult().stream().map(User::getId).collect(Collectors.toList()));
        }
//        if (criteria.getCreatedFrom() != null) {
//            criteria.setCreatedFrom(DateUtil.withTimeAtStartOfDay(criteria.getCreatedFrom()));
//        }
//        if (criteria.getCreatedTo() != null) {
//            criteria.setCreatedTo(DateUtil.withTimeAtEndOfDay(criteria.getCreatedTo()));
//        }
        if (criteria.getRegistrationTimestampStart() != null && criteria.getRegistrationTimestampEnd() != null) {
            criteria.setCreatedFrom(new Date(criteria.getRegistrationTimestampStart()));
            criteria.setCreatedTo(new Date(criteria.getRegistrationTimestampEnd()));
        }
        //因身份数据隔离--进行数据隔离
        if (!ObjectUtils.isEmpty(user.getShopId())) {
            Boolean isolatedByLevelDistributionMode = storeProxySubUserManager.isAuthProxy(user.getId(), user.getShopId());
            if (isolatedByLevelDistributionMode) {
                List<Long> userIds = storeProxySubUserManager.queryBelongProxyHonestFanUserIdList(user.getId(), user.getShopId());
                if (userIds.isEmpty()) {
                    return Paging.empty();
                }
                if (criteria.getUserIds() != null && !criteria.getUserIds().isEmpty()) {
                    userIds.retainAll(criteria.getUserIds());
                }
                criteria.setUserIds(userIds);
            }
        }

        Paging<UserRelationEntity> userRelationEntityViewPage = userRelationEntityReadService.paging(criteria).getResult();
        if (userRelationEntityViewPage == null || userRelationEntityViewPage.isEmpty()) {
            return Paging.empty();
        }
        return new Paging<>(userRelationEntityViewPage.getTotal(),
                userRelationEntityViewPage.getData().stream()
                        .map(entity -> userViewBuildHelper.buildUserView(entity, user.getShopId()))
                        .collect(Collectors.toList()));
    }

    /**
     * llyj
     *
     * @param criteria
     * @return
     */
    @GetMapping("/page/new")
    public Result<DataPage<SubUserView>> pagingNew(UserRelationEntityCriteria criteria) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        criteria.setRelationId(user.getShopId());
        criteria.setType(UserRelationEntity.UserRelationType.Member.getType());
        if (!ObjectUtils.isEmpty(criteria.getName())) {
            criteria.setUserIds(userProfileReadService.findProfileByRealNameLike(criteria.getName()).take().stream().map(UserProfile::getUserId).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(criteria.getMobile())) {
            criteria.setUserIds(userReadService.findLikeMobile(criteria.getMobile()).getResult().stream().map(User::getId).collect(Collectors.toList()));
        }
        if (criteria.getCreatedFrom() != null) {
            criteria.setCreatedFrom(DateUtil.withTimeAtStartOfDay(criteria.getCreatedFrom()));
        }
        if (criteria.getCreatedTo() != null) {
            criteria.setCreatedTo(DateUtil.withTimeAtEndOfDay(criteria.getCreatedTo()));
        }
        //因身份数据隔离--进行数据隔离
        if (!ObjectUtils.isEmpty(user.getShopId())) {
            Boolean isolatedByLevelDistributionMode = storeProxySubUserManager.isAuthProxy(user.getId(), user.getShopId());
            if (isolatedByLevelDistributionMode) {
                List<Long> userIds = storeProxySubUserManager.queryBelongProxyHonestFanUserIdList(user.getId(), user.getShopId());
                if (userIds.isEmpty()) {
                    return Result.data(DataPage.empty(new SubUserView()));
                }
                if (criteria.getUserIds() != null && !criteria.getUserIds().isEmpty()) {
                    userIds.retainAll(criteria.getUserIds());
                }
                criteria.setUserIds(userIds);
            }
        }

        Paging<UserRelationEntity> userRelationEntityViewPage = userRelationEntityReadService.paging(criteria).getResult();
        if (userRelationEntityViewPage == null || userRelationEntityViewPage.isEmpty()) {
            return Result.data(DataPage.empty(new SubUserView()));
        }
        return Result.data(DataPage.build(TotalCalculation.build(criteria.getPageSize(), criteria.getPageNo(), userRelationEntityViewPage.getTotal()),
                userRelationEntityViewPage.getData().stream()
                        .map(entity -> userViewBuildHelper.buildUserView(entity, user.getShopId()))
                        .collect(Collectors.toList())));
    }

    @PostMapping("/freeze")
    public boolean freeze(long userId) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        val rRelation = userRelationEntityReadService.findByUserIdAndRelationIdAndType(userId, user.getShopId(), UserRelationEntity.UserRelationType.Member);
        if (!rRelation.isSuccess()) {
            throw new JsonResponseException(new Translate("会员信息不存在").toString());
        }
        rRelation.getResult().freeze();
        val rUpdate = userRelationEntityWriteService.update(rRelation.getResult());
        return rUpdate.getResult() && rUpdate.isSuccess();
    }

    @PostMapping("/unFreeze")
    public boolean unFreeze(long userId) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        val rRelation = userRelationEntityReadService.findByUserIdAndRelationIdAndType(userId, user.getShopId(), UserRelationEntity.UserRelationType.Member);
        if (!rRelation.isSuccess()) {
            throw new JsonResponseException(new Translate("会员信息不存在").toString());
        }
        rRelation.getResult().unFreeze();
        val rUpdate = userRelationEntityWriteService.update(rRelation.getResult());
        return rUpdate.getResult() && rUpdate.isSuccess();
    }
}
