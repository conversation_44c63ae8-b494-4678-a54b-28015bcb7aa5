package moonstone.web.front.delivery;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.utils.StringUtils;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.promotion.service.UserPromotionWriteService;
import moonstone.web.core.express.component.TmsExpressService;
import moonstone.web.front.order.job.OrderAutoConfirmJob;
import moonstone.web.front.promotions.job.SubStoreOnlyAutoGiveJob;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试用，可删除
 */
@Slf4j
@RestController
public class TmsExpressTestController {

    @Autowired
    private TmsExpressService tmsExpressService;

    @Resource
    private OrderAutoConfirmJob orderAutoConfirmJob;

    @Resource
    private SubStoreOnlyAutoGiveJob subStoreOnlyAutoGiveJob;

    @Resource
    private UserPromotionReadService userPromotionReadService;

    @Resource
    private UserPromotionWriteService userPromotionWriteService;

    @GetMapping("api/tmsExpress/getSupportedExpressCode")
    public Result getSupportedExpressCode() {
        return Result.data(tmsExpressService.getSupportedExpressCode());
    }

    @GetMapping("api/tmsExpress/findWaybillTrack")
    public Result findWaybillTrack(@RequestParam("waybillNos") String waybillNos, @RequestParam("expressCodes") String expressCodes) {
        if (StringUtils.isBlank(waybillNos) || StringUtils.isBlank(expressCodes)) {
            return Result.fail("入参错误");
        }

        var waybillArray = waybillNos.split(",");
        var expressCodeArray = expressCodes.split(",");
        List<Pair<String, String>> list = new ArrayList<>();
        for (int index = 0; index < waybillArray.length; index++) {
            list.add(Pair.of(waybillArray[index], expressCodeArray[index]));
        }

        return Result.data(tmsExpressService.findWaybillTrack(list));
    }

    /**
     * 触发一下 订单的自动确认收货定时任务
     *
     * @return
     */
    @PostMapping("api/tmsExpress/trigger-orderAutoConfirmJob")
    public Result triggerOrderAutoConfirmJob() {
        orderAutoConfirmJob.autoConfirmShipment();

        return Result.data(true);
    }

    @PostMapping("api/tmsExpress/trigger-subStoreOnlyAutoGiveJob")
    public Result triggerSubStoreOnlyAutoGiveJob() {
        subStoreOnlyAutoGiveJob.autoGive();
        return Result.data(true);
    }

    @GetMapping("api/tmsExpress/trigger-insertOnDuplicate")
    public Result triggerinsertOnDuplicate(@RequestParam("userId") Long userId, @RequestParam("promotionId") Long promotionId) {
        var source = userPromotionReadService.findByUserIdAndPromotionId(userId, promotionId).getResult().get(0);

        source.setId(null);
        return Result.data(userPromotionWriteService.create(source));
    }
}
