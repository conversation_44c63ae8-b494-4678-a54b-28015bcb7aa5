package moonstone.web.front.item;

import com.alibaba.fastjson.JSONObject;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cache.ItemCacheHolder;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.model.IdNameVO;
import moonstone.common.utils.*;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.item.dto.ImageInfo;
import moonstone.item.dto.ItemWithAttribute;
import moonstone.item.dto.ViewedItem;
import moonstone.item.dto.ViewedItemDetailInfo;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.service.ShopReadService;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.web.front.item.vo.AppItemsVO;
import moonstone.web.front.item.vo.AppSkuVO;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public record ItemsApp(ShopReadService shopReadService,
                       ItemReadService itemReadService,
                       SkuReadService skuReadService,
                       ThirdPartySkuStockReadService thirdPartySkuStockReadService,
                       DeliveryFeeReadService deliveryFeeReadService,
                       MembershipPriceChecker membershipPriceChecker,
                       ItemCacheHolder itemCacheHolder,
                       SkuCustomReadService skuCustomReadService) {


    /**
     * 查询商品详情
     */
    @RequestMapping(value = "/api/app/v1/item/for-view", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object findForView(Long itemId) {
        //商品详情上半部
        Response<ViewedItem> res = itemReadService.findForView(itemId);
        if (!res.isSuccess() || EmptyUtils.isEmpty(res)) {
            return R.ok().add("errorMsg", "").add("data", null);
        }
        CommonUser user = UserUtil.getCurrentUser();
        ViewedItem viewedItem = res.getResult();
        if (user != null) {
            Integer lowPrice = null;
            Integer highPrice = null;
            for (Sku sku : viewedItem.getSkus()) {
                membershipPriceChecker.check(sku, user.getId());
                if (lowPrice == null) {
                    lowPrice = sku.getPrice();
                    highPrice = lowPrice;
                } else {
                    Integer price = sku.getPrice();
                    if (lowPrice > price) {
                        lowPrice = price;
                    } else if (highPrice < price) {
                        highPrice = price;
                    }
                }
            }
            viewedItem.getItem().setLowPrice(lowPrice);
            viewedItem.getItem().setHighPrice(highPrice);
        }
        //商品详情下半部
        Response<ViewedItemDetailInfo> detailInfo = Response.ok(itemCacheHolder.findViewDetail(itemId));
        if (!detailInfo.isSuccess()) {
            log.error("item  ViewedItemDetailInfo error (id:{})", itemId);
            throw new JsonResponseException("商品详情查询异常");
        }
        return packItemVO(viewedItem, detailInfo.getResult());
    }

    /**
     * 构建商品详情
     */
    private Object packItemVO(ViewedItem viewedItem, ViewedItemDetailInfo viewedItemDetailInfo) {

        AppItemsVO itemsVO = new AppItemsVO();

        BeanUtils.copyProperties(viewedItem.getItem(), itemsVO);
        itemsVO.setMainPic(viewedItem.getItem().getMainImage_());
        itemsVO.setTitle(viewedItem.getItem().getName());
        itemsVO.setShopNamePic(shopReadService.findById(viewedItem.getItem().getShopId()).getResult().getImageUrl());
        //设置来源国
        if (viewedItem.getDefaultOriginId() != null) {
            itemsVO.setOrigin(buildOrigin(viewedItem));
        }
        if (Objects.equals(1, itemsVO.getIsBonded())) {
            itemsVO.setRate(new BigDecimal(String.valueOf(EmptyUtils.isEmpty(viewedItem.getRate()) ? 0 : viewedItem.getRate())).setScale(2, RoundingMode.DOWN));
        }
        //第三方商品才有仓库
        if (Objects.equals(viewedItem.getItem().getIsThirdPartyItem(), 1)) {
            itemsVO.setWarehouse(buildWarehouse(itemsVO.getId()));
        }
        //设置说明
        itemsVO.setDescription("正品保障 | 需要实名认证");
        //设置分享人数
        itemsVO.setShare("1.1万人已分享");
        //设置辅图和详情图
        if (EmptyUtils.isEmpty(viewedItemDetailInfo)) {
            itemsVO.setSubPics(new ArrayList<>());
            itemsVO.setDetail(null);
        } else {
            itemsVO.setSubPics(buildSubPics(viewedItemDetailInfo));
            if (viewedItemDetailInfo.getItemDetail().getDetail() != null) {
                itemsVO.setDetail(ImageUtils.getImgStr(viewedItemDetailInfo.getItemDetail().getDetail()));
            }
        }
        itemsVO.setAdvertise(viewedItem.getItem().getAdvertise());
        //设置sku属性
        List<AppSkuVO> skusBodyList = new ArrayList<>();
        if (viewedItem.getSkus() != null && !viewedItem.getSkus().isEmpty()) {
            for (Sku skuList : viewedItem.getSkus()) {
                if (skuList.getStatus().equals(1)) {
                    AppSkuVO skusBody = new AppSkuVO();
                    skusBody.setId(skuList.getId());
                    skusBody.setPrice(skuList.getPrice());
                    skusBody.setQuantity(skuList.getStockQuantity());
                    skusBody.setKeyValueList(buildKeyValueList(skuList.getAttrs()));
                    skusBodyList.add(skusBody);
                }
            }

            if (skusBodyList.get(0).getId() > 0) {
                //设置是否包税
                SkuCustom skuCustom = skuCustomReadService.findBySkuId(skusBodyList.get(0).getId());
                if (Objects.equals(1, itemsVO.getIsBonded()) && EmptyUtils.isNotEmpty(skuCustom)
                        && Objects.equals(skuCustom.getCustomTaxHolder(), 2)) {
                    itemsVO.setIsFreeTax(true);
                }
            }
        }


        //设置是否包邮
        Response<List<ItemDeliveryFee>> itemDeliveryFee = deliveryFeeReadService.findByItemIds(Collections.singletonList(itemsVO.getId()));
        if (itemDeliveryFee.isSuccess() && EmptyUtils.isNotEmpty(itemDeliveryFee.getResult())) {
            Long deliveryFeeTemplateId = itemDeliveryFee.getResult().get(0).getDeliveryFeeTemplateId();
            Response<DeliveryFeeTemplate> deliveryFeeTemplateResponse = deliveryFeeReadService.findDeliveryFeeTemplateById(deliveryFeeTemplateId);
            if (deliveryFeeTemplateResponse.isSuccess() && deliveryFeeTemplateResponse.getResult() != null &&
                    deliveryFeeTemplateResponse.getResult().getIsFree()) {
                itemsVO.setIsFreeExp(true);
            }
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar ca = Calendar.getInstance();
        /*
         * 设置预计送达时间，3 为天数以业务为准
         */
        ca.add(Calendar.DATE, 3);
        Date d = ca.getTime();
        String enddate = format.format(d);
        itemsVO.setExpress("16:00前付款,预计" + enddate + "送达");

        itemsVO.setParams(packParams(viewedItem.getItem()));
        if (itemsVO.getParams() != null && !itemsVO.getParams().isEmpty()) {
            int size = itemsVO.getParams().size();
            if (size >= 4) {
                List<IdNameVO> params = itemsVO.getParams().subList(0, 4);
                itemsVO.setParamsName(params.stream().map(IdNameVO::getId).map(String::valueOf).collect(Collectors.joining(" ")));
            } else if (size > 0) {
                List<IdNameVO> params = itemsVO.getParams().subList(0, size);
                itemsVO.setParamsName(params.stream().map(IdNameVO::getId).map(String::valueOf).collect(Collectors.joining(" ")));
            } else {
                itemsVO.setParamsName("");
            }
        }
        itemsVO.setSkus(skusBodyList);
        return AppResponse.ok(itemsVO);
    }

    /**
     * 获取仓库
     */
    private String buildWarehouse(Long itemId) {

        Response<Item> rItems = itemReadService.findById(itemId);
        if (!rItems.isSuccess()) {
            log.error("item didn't exist (id:{})", itemId);
            throw new JsonResponseException("item.find.fail");
        }
        Response<List<Sku>> rSkus = skuReadService.findSkusByItemId(itemId);
        if (!rSkus.isSuccess()) {
            log.error("skus list find failed (itemId:{})", itemId);
            throw new JsonResponseException("sku.find.fail");
        }
        List<List<String>> outerSkuIds = rSkus.getResult().stream().map(this::getDeportNameFromSku).collect(Collectors.toList());
        Set<String> resultSet = new LinkedHashSet<>();
        for (List<String> deportName : outerSkuIds) {
            resultSet.addAll(deportName);
        }
        if (resultSet.size() > 0) {
            return new ArrayList<>(resultSet).get(0);
        }
        return "";
    }

    private List<IdNameVO> packParams(Item item) {
        List<IdNameVO> kvBodies = new ArrayList<>();
        if (!EmptyUtils.isEmpty(item.getBrandId())) {
            IdNameVO kvBody = new IdNameVO();
            kvBody.setId("品牌");
            kvBody.setName(item.getBrandName());
            kvBodies.add(kvBody);
        }
        if (!EmptyUtils.isEmpty(item.getSpecification())) {
            IdNameVO kvBody = new IdNameVO();
            kvBody.setId("规格");
            kvBody.setName(item.getSpecification());
            kvBodies.add(kvBody);
        }

        Response<ItemWithAttribute> itemWithAttributeResponse = itemReadService.findItemWithAttributeById(item.getId());
        if (itemWithAttributeResponse.isSuccess() && itemWithAttributeResponse.getResult() != null
                && itemWithAttributeResponse.getResult().getItemAttribute() != null) {
            ItemAttribute itemAttribute = itemWithAttributeResponse.getResult().getItemAttribute();
            if (!ObjectUtils.isEmpty(itemAttribute.getOtherAttrs()) && !itemAttribute.getOtherAttrs().isEmpty()) {
                List<GroupedOtherAttribute> groupedOtherAttributes = itemAttribute.getOtherAttrs();
                if (ObjectUtils.isEmpty(groupedOtherAttributes) || groupedOtherAttributes.isEmpty()) {
                    return kvBodies;
                }
                List<OtherAttribute> otherAttributes = groupedOtherAttributes.get(0).getOtherAttributes();
                if (!EmptyUtils.isEmpty(otherAttributes) && !otherAttributes.isEmpty()) {
                    if (EmptyUtils.isNotEmpty(otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "origin"))
                            .map(OtherAttribute::getAttrVal).findFirst().orElse(""))) {
                        IdNameVO origin = new IdNameVO();
                        origin.setId("产地");
                        origin.setName(otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "origin"))
                                .map(OtherAttribute::getAttrVal).findFirst().orElse(""));
                        kvBodies.add(origin);
                    }
                    if (EmptyUtils.isNotEmpty(otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "weight"))
                            .map(OtherAttribute::getAttrVal).findFirst().orElse(""))) {
                        IdNameVO weight = new IdNameVO();
                        weight.setId("重量");
                        weight.setName(otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "weight"))
                                .map(OtherAttribute::getAttrVal).findFirst().orElse(""));
                        kvBodies.add(weight);
                    }
                }
            }
        }
        return kvBodies;
    }

    private List<IdNameVO> buildKeyValueList(List<SkuAttribute> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<IdNameVO> kvBodies = new ArrayList<>();
        for (SkuAttribute skuAttribute : list) {
            IdNameVO kvBody = new IdNameVO();
            kvBody.setId(skuAttribute.getAttrKey());
            kvBody.setName(skuAttribute.getAttrVal());
            kvBodies.add(kvBody);
        }
        return kvBodies;
    }

    /**
     * 
     */
    private List<String> buildSubPics(ViewedItemDetailInfo detailInfo) {
        if (detailInfo == null || detailInfo.getItemDetail() == null || detailInfo.getItemDetail().getImages() == null
                || detailInfo.getItemDetail().getImages().isEmpty()) {
            return new ArrayList<>();
        }
        List<String> subPics = new ArrayList<>();

        for (ImageInfo image : detailInfo.getItemDetail().getImages()) {
            subPics.add(image.getUrl());
        }
        return subPics;
    }

    /**
     * 构建来源国
     */
    private JSONObject buildOrigin(ViewedItem result) {
        JSONObject json = new JSONObject();
        json.put("id", result.getDefaultOriginId());
        json.put("name", result.getDefaultOrigin());
        json.put("icon", result.getDefaultOriginUrl());
        return json;
    }


    private List<String> getDeportNameFromSku(Sku sku) {
        Map<String, String> tags = sku.getTags() == null ? new HashMap<>(8) : sku.getTags();
        String pushSystemStr = tags.getOrDefault("pushSystem", "");
        if (pushSystemStr.isEmpty()) {
            log.error("this item didnt belong any ThirdPartyStock (itemId:{}),(pushSystem:{})", sku.getItemId(), pushSystemStr);
            return new ArrayList<>();
        }
        Integer pushSystemId = null;
        try {
            pushSystemId = Integer.parseInt(pushSystemStr);
        } catch (Exception ex) {
            log.error("parse pushSystemStr fail,str:{}", pushSystemStr);
        }
        if (pushSystemId == null) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        if (ThirdPartySystem.fromInt(pushSystemId) == ThirdPartySystem.Y800_V2) {
            Response<List<ThirdPartySkuStock>> thirdPartySkuStockResponse = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(sku.getShopId(), pushSystemId, sku.getOuterSkuId());
            if (thirdPartySkuStockResponse.isSuccess() && !CollectionUtils.isEmpty(thirdPartySkuStockResponse.getResult())) {
                {
                    resultList = thirdPartySkuStockResponse.getResult().stream().map(ThirdPartySkuStock::getDepotName).collect(Collectors.toList());
                }
            }
            if (sku.getShopId() == 28) {
                for (int i = 0; i < resultList.size(); i++) {
                    if (resultList.get(i).startsWith("但丁")) {
                        resultList.set(i, resultList.get(i).substring(2));
                    }
                    if (resultList.get(i).contains("金义仓")) {
                        resultList.set(i, "金义保税区");
                    }
                }
            }
        } else {
            log.error("find pushSystem failed,pushSystem:{},outerSkuId:{}", pushSystemId, sku.getOuterShopId());
        }
        return resultList;
    }
}
