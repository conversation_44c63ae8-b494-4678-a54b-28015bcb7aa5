package moonstone.web.front.promotions;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.DataPage;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.TotalCalculation;
import moonstone.common.utils.UserUtil;
import moonstone.promotion.dto.RichPromotion;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.service.PromotionReadService;
import moonstone.promotion.service.PromotionWriteService;
import moonstone.web.core.events.promotion.PromotionCreatedEvent;
import moonstone.web.core.events.promotion.PromotionPublishEvent;
import moonstone.web.core.events.promotion.PromotionStoppedEvent;
import moonstone.web.core.fileNew.dto.PromotionPageDto;
import moonstone.web.front.promotions.convert.PromotionDecorator;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;

/**
 * 商家营销活动相关接口
 */
@RestController
@Slf4j
public record Promotions(PromotionReadService promotionReadService,
                         PromotionWriteService promotionWriteService,
                         PromotionDecorator promotionDecorator) {

    @RequestMapping(value = "/api/seller/promotion", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody Promotion promotion) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        promotion.setShopId(commonUser.getShopId());

        Response<Long> createResp = promotionWriteService.create(promotion);
        if (!createResp.isSuccess()) {
            log.error("fail to create promotion:{},cause:{}",
                    promotion, createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        final Long promotionId = createResp.getResult();

        EventSender.sendApplicationEvent(new PromotionCreatedEvent(promotionId));
        return promotionId;
    }

    @RequestMapping(value = "/api/seller/promotion", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean update(@RequestBody Promotion promotion) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        checkAuth(promotion.getId(), commonUser.getShopId());

        // prevent
        promotion.setShopId(null);
        promotion.setPromotionDefId(null);
        promotion.setType(null);
        promotion.setStatus(null);

        Response<Boolean> updateResp = promotionWriteService.update(promotion);
        if (!updateResp.isSuccess()) {
            log.error("fail to update promotion:{},cause:{}",
                    promotion, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }
        return updateResp.getResult();
    }

    @RequestMapping(value = "/api/seller/promotion/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean delete(@PathVariable("id") Long promotionId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Promotion promotion = checkAuth(promotionId, commonUser.getShopId());
        if (PromotionStatus.fromInt(promotion.getStatus()) != PromotionStatus.INIT) {
            log.error("promotion(id={}) can not be deleted with current status:{}",
                    promotionId, promotion.getStatus());
            throw new JsonResponseException("invalid.status");
        }

        Response<Boolean> deleteResp = promotionWriteService.delete(promotionId);
        if (!deleteResp.isSuccess()) {
            log.error("fail to delete promotion by id:{},cause:{}",
                    promotionId, deleteResp.getError());
            throw new JsonResponseException(deleteResp.getError());
        }

        return deleteResp.getResult();

    }

    /**
     * 发布营销活动，使其生效
     *
     * @param promotionId 营销ID
     * @return boolean true:成功 false:失败
     */
    @RequestMapping(value = "/api/seller/promotion/{id}/publish", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean publish(@PathVariable("id") Long promotionId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Promotion promotion = checkAuth(promotionId, commonUser.getShopId());
        if (PromotionStatus.fromInt(promotion.getStatus()) != PromotionStatus.INIT) {
            log.error("promotion(id={}) can not be published with current status:{}",
                    promotionId, promotion.getStatus());
            throw new JsonResponseException("invalid.status");
        }

        int targetStatus;
        if (promotion.isExpired()) {
            targetStatus = PromotionStatus.EXPIRE.getValue();
        } else {
            promotion.setStatus(PromotionStatus.PUBLISHED.getValue());
            targetStatus = promotion.inProcess() ? PromotionStatus.ONGOING.getValue() : PromotionStatus.PUBLISHED.getValue();
        }

        Response<Boolean> updateResp = promotionWriteService.updateStatus(promotionId, targetStatus);
        if (!updateResp.isSuccess()) {
            log.error("fail to publish promotion(id={}),cause:{}", promotionId, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }

        // 手动触发 因为EventBus似乎忽略了(为了防止二次触发所以将Subscribe也去除了 因为这边已经手动触发了)
        EventSender.sendApplicationEvent(new PromotionPublishEvent(promotionId, targetStatus));
        return updateResp.getResult();
    }

    /**
     * 发布营销活动，使其生效
     *
     * @param promotionId 营销ID
     * @return boolean true:成功 false:失败
     */
    @RequestMapping(value = "/api/seller/promotion/v2/{id}/publish", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> publishV2(@PathVariable("id") Long promotionId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Promotion promotion = checkAuth(promotionId, commonUser.getShopId());
        if (PromotionStatus.fromInt(promotion.getStatus()) != PromotionStatus.INIT) {
            log.error("promotion(id={}) can not be published with current status:{}",
                    promotionId, promotion.getStatus());
            throw new JsonResponseException("invalid.status");
        }

        int targetStatus;
        if (promotion.isExpired()) {
            targetStatus = PromotionStatus.EXPIRE.getValue();
        } else {
            promotion.setStatus(PromotionStatus.PUBLISHED.getValue());
            targetStatus = promotion.inProcess() ? PromotionStatus.ONGOING.getValue() : PromotionStatus.PUBLISHED.getValue();
        }

        Response<Boolean> updateResp = promotionWriteService.updateStatus(promotionId, targetStatus);
        if (!updateResp.isSuccess()) {
            log.error("fail to publish promotion(id={}),cause:{}", promotionId, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }

        // 手动触发 因为EventBus似乎忽略了(为了防止二次触发所以将Subscribe也去除了 因为这边已经手动触发了)
        EventSender.sendApplicationEvent(new PromotionPublishEvent(promotionId, targetStatus));
        return Result.data(updateResp.getResult());
    }


    /**
     * 停止营销活动，使其失效
     *
     * @param promotionId 营销ID
     * @return boolean true:成功 false:失败
     */
    @RequestMapping(value = "/api/seller/promotion/{id}/stop", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean stop(@PathVariable("id") Long promotionId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Promotion promotion = checkAuth(promotionId, commonUser.getShopId());
        PromotionStatus currentStatus = PromotionStatus.fromInt(promotion.getStatus());
        if (currentStatus != PromotionStatus.PUBLISHED && currentStatus != PromotionStatus.ONGOING) {
            log.error("promotion(id={}) can not be stop with current status:{}",
                    promotionId, promotion.getStatus());
            throw new JsonResponseException("invalid.status");
        }

        Response<Boolean> updateResp = promotionWriteService.updateStatus(promotionId, PromotionStatus.STOP.getValue());
        if (!updateResp.isSuccess()) {
            log.error("fail to stop promotion(id={}),cause:{}", promotionId, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }

        EventSender.sendApplicationEvent(new PromotionStoppedEvent(promotionId));
        return updateResp.getResult();
    }

    /**
     * 停止营销活动，使其失效
     *
     * @param promotionId 营销ID
     * @return boolean true:成功 false:失败
     */
    @RequestMapping(value = "/api/seller/promotion/v2/{id}/stop", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> stopV2(@PathVariable("id") Long promotionId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Promotion promotion = checkAuth(promotionId, commonUser.getShopId());
        PromotionStatus currentStatus = PromotionStatus.fromInt(promotion.getStatus());
        if (currentStatus != PromotionStatus.PUBLISHED && currentStatus != PromotionStatus.ONGOING) {
            log.error("promotion(id={}) can not be stop with current status:{}",
                    promotionId, promotion.getStatus());
            throw new JsonResponseException("invalid.status");
        }

        Response<Boolean> updateResp = promotionWriteService.updateStatus(promotionId, PromotionStatus.STOP.getValue());
        if (!updateResp.isSuccess()) {
            log.error("fail to stop promotion(id={}),cause:{}", promotionId, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }

        EventSender.sendApplicationEvent(new PromotionStoppedEvent(promotionId));
        return Result.data(updateResp.getResult());
    }

    @RequestMapping(value = "/api/seller/promotion/{promotionId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Promotion findById(@PathVariable("promotionId") Long promotionId) {
        CommonUser seller = UserUtil.getCurrentUser();
        return checkAuth(promotionId, seller.getShopId());
    }

    @RequestMapping(value = "/api/seller/promotion/find-for-edit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Promotion findForEdit(@RequestParam(value = "promotionId", required = false) Long promotionId) {
        if (promotionId == null) {
            return new Promotion();
        }
        CommonUser seller = UserUtil.getCurrentUser();
        return checkAuth(promotionId, seller.getShopId());
    }

    @RequestMapping(value = "/api/seller/promotion/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<RichPromotion> findBy(@RequestParam(value = "promotionDefId", required = false) Long promotionDefId,
                                        @RequestParam(value = "status", required = false) Integer status,
                                        @RequestParam(value = "name", required = false) String name,
                                        @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                        @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        CommonUser seller = UserUtil.getCurrentUser();
        Response<Paging<RichPromotion>> findResp = promotionReadService.findBy(seller.getShopId(), Strings.emptyToNull(name), promotionDefId, status, pageNo, pageSize);
        if (!findResp.isSuccess()) {
            log.error("fail to find rich promotion by shopId={}, promotionDefId:{},and status:{},pageNo:{},pageSize:{},cause:{}",
                    seller.getShopId(), promotionDefId, status, pageSize, pageNo, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }

        // 追加一些前端显示需要的东西
        if (findResp.getResult() != null && !findResp.getResult().isEmpty()) {
            promotionDecorator.decorate(findResp.getResult().getData());
        }

        return findResp.getResult();
    }

    /**
     * 分页查询营销活动列表
     *
     * @param req 请求参数
     * @return 营销活动列表
     */
    @PostMapping("/api/seller/promotion/v2/paging")
    public Result<PageVo<RichPromotion>> pages(@RequestBody @Valid PromotionPageDto req) {
        log.info("分页查询营销活动列表 请求参数 {}", JSONUtil.toJsonStr(req));
        CommonUser seller = UserUtil.getCurrentUser();
        Response<Paging<RichPromotion>> findResp = promotionReadService.findBy(seller.getShopId(), Strings.emptyToNull(req.getName()), req.getPromotionDefId(), req.getStatus(), (int) req.getCurrent(), (int) req.getSize());
        if (!findResp.isSuccess()) {
            log.error("分页查询营销活动列表失败 {}", findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        Paging<RichPromotion> paging = findResp.getResult();
        Long total = paging.getTotal();
        if (total == null || total == 0) {
            return Result.data(PageVo.build(0L, req.getSize(), req.getCurrent(), 0L, Collections.emptyList()));
        }
        // 追加一些前端显示需要的东西
        promotionDecorator.decorate(paging.getData());
        long pages = total % req.getSize() == 0 ? total / req.getSize() : total / req.getSize() + 1;
        return Result.data(PageVo.build(total, req.getSize(), req.getCurrent(), pages, paging.getData()));
    }

    /**
     * llyj
     */
    @RequestMapping(value = "/api/seller/promotion/paging/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<DataPage<RichPromotion>> findByNew(@RequestParam(value = "promotionDefId", required = false) Long promotionDefId,
                                                     @RequestParam(value = "status", required = false) Integer status,
                                                     @RequestParam(value = "name", required = false) String name,
                                                     @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                     @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        CommonUser seller = UserUtil.getCurrentUser();
        Response<Paging<RichPromotion>> findResp = promotionReadService.findBy(seller.getShopId(), Strings.emptyToNull(name), promotionDefId, status, pageNo, pageSize);
        if (!findResp.isSuccess()) {
            log.error("fail to find rich promotion by shopId={}, promotionDefId:{},and status:{},pageNo:{},pageSize:{},cause:{}",
                    seller.getShopId(), promotionDefId, status, pageSize, pageNo, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        return Result.data(DataPage.build(TotalCalculation.build(pageSize, pageNo, findResp.getResult().getTotal()), findResp.getResult().getData()));
    }


    private Promotion checkAuth(Long promotionId, Long shopId) {
        Response<Promotion> findResp = promotionReadService.findById(promotionId);
        if (!findResp.isSuccess()) {
            log.error("fail to find promotion by id:{},cause:{}",
                    promotionId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        Promotion promotion = findResp.getResult();

        if (!Objects.equal(promotion.getShopId(), shopId)) {
            log.error("promotion(id={}) not belong to shop(id={})",
                    promotionId, shopId);
            throw new JsonResponseException("promotion.not.belong.to.seller");
        }
        return promotion;
    }


}
