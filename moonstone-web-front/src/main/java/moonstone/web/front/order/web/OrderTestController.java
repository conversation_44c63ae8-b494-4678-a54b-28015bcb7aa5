package moonstone.web.front.order.web;

import moonstone.common.api.APIResp;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.SubmittedOrder;
import moonstone.order.dto.SubmittedSku;
import moonstone.order.dto.SubmittedSkusByShop;
import moonstone.user.cache.UserCacheHolder;
import moonstone.web.core.util.ParanaUserMaker;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
@Profile("dev")
@RequestMapping("/api/order/test")
@RestController
public record OrderTestController(Orders orders, UserCacheHolder userCacheHolder){

    private static final ForkJoinPool FORK_JOIN_POOL = new ForkJoinPool(Runtime.getRuntime().availableProcessors());
    private static final int ORDER_NUM = 10000;

    @PostMapping
    public APIResp<Boolean> start(HttpServletRequest request) {
        if (TestIndex.start.name().equals(request.getSession().getAttribute(TestIndex.test.name()))) {
            return APIResp.ok(false);
        }
        request.getSession().setAttribute(TestIndex.start.name(), System.currentTimeMillis());
        AtomicLong count = new AtomicLong(0);
        List<List<Long>> orderId = new LinkedList<>();
        for (int i = 0; i < Runtime.getRuntime().availableProcessors(); i++) {
            FORK_JOIN_POOL.submit(() -> {
                while (count.getAndIncrement() < ORDER_NUM) {
                    UserUtil.putCurrentUser(ParanaUserMaker.from(userCacheHolder.findByUserId(3L).orElseThrow(() -> Translate.exceptionOf("测试失败"))));
                    SubmittedOrder submittedOrder = new SubmittedOrder();
                    submittedOrder.setPayType(1);
                    submittedOrder.setChannel(2);

                    SubmittedSkusByShop submittedSkusByShop = new SubmittedSkusByShop();
                    SubmittedSku submittedSku = new SubmittedSku();
                    submittedSku.setSkuId(3196L);
                    submittedSku.setQuantity(1);
                    submittedSkusByShop.setSubmittedSkus(Collections.singletonList(submittedSku));
                    submittedOrder.setSubmittedSkusByShops(Collections.singletonList(submittedSkusByShop));
                   orderId.add( orders.create(submittedOrder, UserUtil.getCurrentUser()));
                }
                request.getSession().setAttribute(TestIndex.test.name(), TestIndex.end.name());
                request.getSession().setAttribute(TestIndex.end.name(), System.currentTimeMillis());
            });
        }
        request.getSession().setAttribute(TestIndex.test.name(), TestIndex.start.name());
        return APIResp.ok(true);
    }

    @GetMapping
    public APIResp<Long> result(HttpServletRequest request) {
        if (!TestIndex.end.name().equals(request.getSession().getAttribute(TestIndex.test.name()))) {
            return APIResp.error("test not finish");
        }
        return APIResp.ok(Long.parseLong(request.getSession().getAttribute(TestIndex.end.name()).toString()) - Long.parseLong(request.getSession().getAttribute(TestIndex.start.name()).toString()));
    }

    enum TestIndex {
        /**
         * 测试用
         */
        start, end, test
    }
}
