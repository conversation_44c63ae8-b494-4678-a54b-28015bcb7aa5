package moonstone.web.front.order.application;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EncryptHelper;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.ShopOrderIdentityErrorEnum;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.front.user.event.UserIdentityChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class OrderPayErrorRewindApp {
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    ShopOrderReadService shopOrderReadService;

    @Autowired
    OrderWriteService orderWriteService;
    @Autowired
    UserCertificationReadService userCertificationReadService;

    /**
     * 未支付的订单更新实名信息
     *
     * @param userIdentityChangeEvent
     */
    @EventListener(UserIdentityChangeEvent.class)
    public void fixTheNotPaidOrder(UserIdentityChangeEvent userIdentityChangeEvent) {
        log.debug("OrderPayErrorRewindApp.fixTheNotPaidOrder, 未支付的订单更新实名信息, receive event={}",
                JSON.toJSONString(userIdentityChangeEvent));

        UserCertification userCertification = userCertificationReadService.findDefaultByUserId(userIdentityChangeEvent.getUserId()).getResult().get();
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setBuyerId(userCertification.getUserId());
        orderCriteria.setStatus(Collections.singletonList(OrderStatus.NOT_PAID.getValue()));
        List<ShopOrder> shopOrderList = shopOrderReadService.findBy(0, 500, orderCriteria).getResult().getData();
        for (ShopOrder shopOrder : shopOrderList) {
            Map<String, String> orderExtra = Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new);
            if (!ShopOrderIdentityErrorEnum.TRUE.getCode().equals(orderExtra.get(ShopOrderExtra.identityError.name()))) {
                continue;
            }
            orderExtra.remove(ShopOrderExtra.identityError.name());
            orderWriteService.updateOrderExtra(shopOrder.getId(), OrderLevel.SHOP, orderExtra);
            // update the mongo-template
            PayerInfo payerInfo = PayerInfo.Helper.create(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),
                    userCertification.getPaperName(), userCertification.getPaperNo(), shopOrder.getId(), shopOrder.getShopId());
            mongoTemplate.upsert(Query.query(Criteria.where("orderId").is(shopOrder.getId())),
                    Update.update("password", payerInfo.getPassword())
                            .set("payerName", payerInfo.getPayerName())
                            .set("payerNo", payerInfo.getPayerNo())
                            .set("hash", payerInfo.getHash())
                            .set("shopId", payerInfo.getShopId())
                    , PayerInfo.class);
        }
    }
}