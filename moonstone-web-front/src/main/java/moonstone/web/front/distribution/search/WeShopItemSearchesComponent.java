package moonstone.web.front.distribution.search;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.UserUtil;
import moonstone.search.dto.SearchedWeShopItem;
import moonstone.search.dto.SearchedWeShopItemInWeShopWithAggs;
import moonstone.search.weShopItem.WeShopItemSearchReadService;
import moonstone.web.core.shop.application.WeShopMemberRegisterApp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Author:  CaiZhy
 * Date:    2018/12/24
 */
@ConditionalOnProperty(value = "enable.item.search", havingValue = "true", matchIfMissing = true)
@RestController
@Slf4j
public class WeShopItemSearchesComponent {
    @Autowired
    private WeShopItemSearchReadService weShopItemSearchReadService;
    @Autowired
    private WeShopMemberRegisterApp weShopMemberRegisterApp;

    /**
     * 微分销店铺内搜索
     *
     * @param pageNo   起始页码
     * @param pageSize 每页记录条数
     * @param params   搜索上下文
     * @return 搜索结果, 包括属性导航, 面包屑等
     */
    @RequestMapping(value = "/api/weShopItem/search-in-we-shop", produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends SearchedWeShopItemInWeShopWithAggs<SearchedWeShopItem>> searchWeShopItemInWeShopWithAggs(
            @RequestParam(required = false) Integer pageNo,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam Map<String, String> params) {
        //  只搜索上架商品
        params.put("status", "1");
        String weShopId = params.get("weShopId");
        if (!StringUtils.hasText(weShopId)) {
            log.error("weShop id is required when search in weShop");
            return Response.fail("weShop.id.empty");
        }
        NumberUtil.parseNumber(weShopId, Long.TYPE).ifSuccess(weShopIdLong -> weShopMemberRegisterApp.registerMemberForWeShop(weShopIdLong, UserUtil.getCurrentUser()));
        String templateName = "search.mustache";
        return weShopItemSearchReadService.searchInWeShopWithAggs(pageNo, pageSize, templateName, params, SearchedWeShopItem.class);
    }

    /**
     * 根据某种排序方式搜索
     * 已经不符合目前的业务 并且无人维护
     *
     * @param weShopId 微店Id
     * @param shopId   店铺Id
     * @param pageNo   未生效的分页
     * @param pageSize 未生效的分页
     * @return 搜索数据
     * @see WeShopItemSearchesComponent#searchWeShopItemInWeShopWithAggs(Integer, Integer, Map)
     */
    @Deprecated
    @RequestMapping(value = "/api/weShopItem/index/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends SearchedWeShopItemInWeShopWithAggs<SearchedWeShopItem>> pagingIndex(
            @RequestParam Long weShopId,
            @RequestParam Long shopId,
            @RequestParam(required = false) Integer pageNo,
            @RequestParam(required = false) Integer pageSize) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("weShopId", weShopId.toString());
            params.put("shopId", shopId.toString());
            params.put("idxDisplay", "1");
            params.put("sort", "1_0_0_0_0");
            params.put("status", "1");
            weShopMemberRegisterApp.registerMemberForWeShop(weShopId, UserUtil.getCurrentUser());
            //TODO 检查店铺是否存在和是否冻结?
            String templateName = "search.mustache";
            return weShopItemSearchReadService.searchInWeShopWithAggs(pageNo, pageSize, templateName, params, SearchedWeShopItem.class);
        } catch (Exception e) {
            log.error("fail to search index weShopItem by weShopId={}", weShopId, e);
            throw new JsonResponseException("weShopItem.index.paging.fail");
        }
    }
}
