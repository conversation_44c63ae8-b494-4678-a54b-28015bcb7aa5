package moonstone.web.front.profit.application;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.BankAccountJudge;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.front.profit.domain.AbstractBalanceProfitDomain;
import moonstone.web.front.profit.domain.AbstractWithdrawAccountDomain;
import moonstone.web.front.profit.domain.factory.WithdrawRuleFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Application 应用, 负责组合切面, 将各个切面组合成对外的应用
 * 不负责内部业务逻辑, 但是要负责切面组合逻辑
 * Q: 什么时候贴合切面?
 * Q: 什么时候嵌入切面?
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WithdrawProfitHelper {

    @Autowired
    BalanceDetailReadService balanceDetailReadService;
    @Autowired
    WithdrawAccountReadService withdrawAccountReadService;
    @Autowired
    WithdrawRuleFactory withdrawRuleFactory;
    @Autowired
    BankAccountJudge bankAccountJudge;

    /**
     * 查询可以提现的利润
     * 查询某个平台(店铺)下的用户可以提现的利润
     *
     * @param userId 用户Id
     * @param shopId 店铺Id(平台Id)
     * @return 用户当前可以提现的利润
     */
    public APIResp<CurrentProfitVO> queryWithdrawAbleProfit(Long userId, Long shopId) {
        try {
            CurrentProfitVO currentProfitVO = new CurrentProfitVO();
            AbstractBalanceProfitDomain.from(balanceDetailReadService, userId, shopId).currentProfitQuerySlice()
                    .currentProfitQuery().take().decorate(currentProfitVO);
            AbstractWithdrawAccountDomain.from(withdrawAccountReadService, bankAccountJudge, userId, shopId).withdrawAccountQuerySlice()
                    .withdrawAccountQuery().take().decorate(currentProfitVO);
            withdrawRuleFactory.create(userId, shopId).withdrawRuleResultVO().decorate(currentProfitVO);
            return APIResp.ok(currentProfitVO);
        } catch (Exception exception) {
            log.error("{} fail to query withdrawAble profit for [UserId=>{}, shopId=>{}]", LogUtil.getClassMethodName(), userId, shopId, exception);
            return APIResp.error(Translate.of("查询失败"));
        }
    }
}
