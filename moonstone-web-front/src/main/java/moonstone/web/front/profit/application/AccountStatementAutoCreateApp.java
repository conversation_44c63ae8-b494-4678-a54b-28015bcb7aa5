package moonstone.web.front.profit.application;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.AccountStatement;
import moonstone.order.model.AccountStatementDetail;
import moonstone.order.model.BalanceDetail;
import moonstone.order.service.AccountStatementReadService;
import moonstone.order.service.AccountStatementWriteService;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.shop.dto.SubStoreTStoreGuiderCriteria;
import moonstone.shop.model.Shop;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.component.roleSnapshot.OrderRoleSnapshotComponent;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.profit.convert.AccountStatementDetailConvertor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class AccountStatementAutoCreateApp {

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Resource
    private SubStoreTStoreGuiderReadService storeTStoreGuiderReadService;

    @Resource
    private SubStoreCache subStoreCache;

    @Resource
    private AccountStatementReadService accountStatementReadService;

    @Resource
    private AccountStatementWriteService accountStatementWriteService;

    @Resource
    private BalanceDetailReadService balanceDetailReadService;

    @Resource
    private OrderRoleSnapshotComponent orderRoleSnapshotComponent;

    private static final int USER_PAGE_SIZE = 100;

    /**
     * 指定时间区间，生成商家下各个用户的账单
     *
     * @param shop
     * @param startTime
     * @param endTime
     */
    public void create(Shop shop, Date startTime, Date endTime) {
        if (!needCreate(shop)) {
            return;
        }

        //服务商
        createForServiceProvider(shop, startTime, endTime);

        //门店 - 还走老的提现逻辑，不生成账单

        //导购
        createForGuider(shop, startTime, endTime);
    }

    /**
     * 为商家下的导购生成账单
     *
     * @param shop
     * @param startTime
     * @param endTime
     */
    private void createForGuider(Shop shop, Date startTime, Date endTime) {
        SubStoreTStoreGuiderCriteria criteria = new SubStoreTStoreGuiderCriteria();
        criteria.setShopId(shop.getId());
        criteria.setPageSize(USER_PAGE_SIZE);

        for (int currentPage = 1; ; currentPage++) {
            criteria.setPageNo(currentPage);
            var page = storeTStoreGuiderReadService.paging(criteria).getResult();
            if (page == null || CollectionUtils.isEmpty(page.getData())) {
                break;
            }

            //为每个导购生成账单
            page.getData().forEach(guider -> {
                //导购的所得佣金，当前记在服务商下面
                var serviceProviderUserId = subStoreCache.findServiceProviderUserId(guider.getSubStoreId());

                //生成
                create(shop.getId(), guider.getStoreGuiderId(), SubStoreUserIdentityEnum.STORE_GUIDER,
                        serviceProviderUserId, startTime, endTime);
            });

            if (page.getData().size() < criteria.getPageSize()) {
                break;
            }
        }

    }

    /**
     * 为商家下的服务商生成账单
     *
     * @param shop
     * @param startTime
     * @param endTime
     */
    private void createForServiceProvider(Shop shop, Date startTime, Date endTime) {
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setRelationIdA(Lists.newArrayList(shop.getId()));
        criteria.setRelationId(shop.getUserId());
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        criteria.setPageSize(USER_PAGE_SIZE);

        //分页查询的所有的服务商
        for (int currentPage = 1; ; currentPage++) {
            criteria.setPageNo(currentPage);
            var page = userRelationEntityReadService.paging(criteria).getResult();
            if (page == null || CollectionUtils.isEmpty(page.getData())) {
                break;
            }

            //为每个服务商生成账单
            page.getData().forEach(serviceProvider -> create(shop.getId(), serviceProvider.getUserId(),
                    SubStoreUserIdentityEnum.SERVICE_PROVIDER, serviceProvider.getUserId(), startTime, endTime));

            if (page.getData().size() < criteria.getPageSize()) {
                break;
            }
        }
    }

    /**
     * 创建账单
     *
     * @param shopId
     * @param userId
     * @param userRole
     * @param startTime
     * @param endTime
     */
    private void create(Long shopId, Long userId, SubStoreUserIdentityEnum userRole, Long profitBelongUserId,
                        Date startTime, Date endTime) {
        try {
            //是否已创建
            var existed = accountStatementReadService.findOverlap(shopId, userId, userRole, startTime, endTime);
            if (!existed.isSuccess() || existed.getResult() != null) {
                log.info("账单已存在或账期与其它账单有重合, shopId={}, userId={}, userRole={}, startTime={}, endTime={}",
                        shopId, userId, userRole.getCode(), startTime, endTime);
                return;
            }

            //利润没有归属的，不管了
            if (profitBelongUserId == null) {
                log.warn("userId={}, userRole={}, profitBelongUserId is null", userId, userRole.getCode());
                return;
            }

            //格造账单明细
            var detailList = buildDetailList(shopId, userId, userRole, profitBelongUserId, startTime, endTime);

            //构造账单
            var accountStatement = buildAccountStatement(detailList, shopId, userId, userRole, startTime, endTime);

            //写入数据库
            accountStatementWriteService.save(accountStatement, detailList);

            //角色信息快照
            orderRoleSnapshotComponent.createSnapshot(accountStatement.getId(), OrderRoleSnapshotOrderTypeEnum.ACCOUNT_STATEMENT);
        } catch (Exception ex) {
            log.error("AccountStatementAutoCreateApp.create error, shopId={}, userId={}, userRole={}, " +
                            "profitBelongUserId={}, startTime={}, endTime={}",
                    shopId, userId, userRole.getCode(), profitBelongUserId, startTime, endTime, ex);
        }
    }

    /**
     * 格造账单主体
     *
     * @param detailList
     * @param userRole
     * @param startTime
     * @param endTime
     * @return
     */
    private AccountStatement buildAccountStatement(List<AccountStatementDetail> detailList, Long shopId, Long userId,
                                                   SubStoreUserIdentityEnum userRole, Date startTime, Date endTime) {
        AccountStatement result = new AccountStatement();

        result.setIsValid(DataValidEnum.VALID.getCode());
        result.setPeriodEndAt(endTime);
        result.setPeriodStartAt(startTime);
        result.setPeriod(DateUtil.toString(startTime, DateUtil.YYYY_MM));

        result.setShopId(shopId);

        if (!CollectionUtils.isEmpty(detailList)) {
            result.setPromoteAmount(detailList.stream().mapToLong(AccountStatementDetail::getPromoteAmount).sum());
            result.setRewardAmount(detailList.stream().mapToLong(AccountStatementDetail::getRewardAmount).sum());
            result.setTotalAmount(detailList.stream().mapToLong(AccountStatementDetail::getCommissionAmount).sum());
        } else {
            result.setPromoteAmount(0L);
            result.setRewardAmount(0L);
            result.setTotalAmount(0L);
        }

        result.setUserId(userId);
        result.setUserRole(userRole.getCode());
        result.setAccountStatementNo(AccountStatement.generateAccountStatementNo(result));

        return result;
    }

    /**
     * 格造账单明细
     *
     * @param shopId
     * @param userId
     * @param userRole
     * @param startTime
     * @param endTime
     * @return
     */
    private List<AccountStatementDetail> buildDetailList(Long shopId, Long userId, SubStoreUserIdentityEnum userRole,
                                                         Long profitBelongUserId, Date startTime, Date endTime) {
        List<AccountStatementDetail> detailList = new ArrayList<>();

        int pageSize = 500;
        for (int currentPage = 1; ; currentPage++) {
            List<BalanceDetail> balanceDetailList = null;

            //查询利润流水明细
            switch (userRole) {
                case SERVICE_PROVIDER -> {
                    balanceDetailList = balanceDetailReadService.findForServiceProvider(
                            shopId, userId, startTime, endTime, currentPage, pageSize).getResult();
                }
                case STORE_GUIDER -> {
                    //导购的订单利润记在其服务商的下面
                    balanceDetailList = balanceDetailReadService.findForGuider(shopId, userId, profitBelongUserId,
                            startTime, endTime, currentPage, pageSize).getResult();
                }
            }

            if (!CollectionUtils.isEmpty(balanceDetailList)) {
                detailList.addAll(AccountStatementDetailConvertor.convert(balanceDetailList, userRole));
            }

            if (CollectionUtils.isEmpty(balanceDetailList) || balanceDetailList.size() < pageSize) {
                break;
            }
        }

        return detailList;
    }

    /**
     * 是否需要生成账单
     *
     * @param shop
     * @return
     */
    private boolean needCreate(Shop shop) {
        if (shop == null) {
            return false;
        }

        if (CollectionUtils.isEmpty(shop.getExtra())) {
            shop.setExtra(new HashMap<>());
        }

        return SalePattern.SubStore.getCode().equals(shop.getExtra().get(ShopExtra.SalesPattern.getCode()));
    }
}
