package moonstone.web.front.profit.convert;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.order.bo.ShopOrderProfitBO;
import moonstone.order.model.*;
import moonstone.order.model.result.OrderShipmentInfoDO;
import moonstone.order.model.result.SimpleShopOrderView;
import moonstone.web.front.component.order.convert.OrderExportConvertor;
import moonstone.web.front.profit.view.AccountStatementExportVO;
import moonstone.web.front.profit.view.AccountStatementPromoteDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AccountStatementDetailConvertor {

    @Autowired
    private OrderExportConvertor orderExportConvertor;

    public static List<AccountStatementDetail> convert(List<BalanceDetail> sourceList, SubStoreUserIdentityEnum userIdentity) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        return sourceList.stream()
                .map(source -> convert(source, userIdentity))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static AccountStatementDetail convert(BalanceDetail source, SubStoreUserIdentityEnum userIdentity) {
        if (source == null) {
            return null;
        }

        AccountStatementDetail target = new AccountStatementDetail();

        target.setAccountStatementId(null);
        target.setBalanceDetailId(source.getId());
        target.setCommissionAmount(source.getChangeFee());
        target.setIsValid(DataValidEnum.VALID.getCode());

        target.setOrderId(source.getRelatedId());

        //当前订单利润流水，只与主订单关联
        var orderLevel = source.getOrderLevel();
        if (orderLevel.isPresent()) {
            target.setOrderType(orderLevel.get().getValue());
        } else {
            target.setOrderType(OrderLevel.SHOP.getValue());
        }

        switch (userIdentity) {
            case SERVICE_PROVIDER -> {
                //服务商，都是奖励佣金
                target.setPromoteAmount(0L);
                target.setRewardAmount(source.getChangeFee());
            }
            case STORE_GUIDER -> {
                //导购，都是推广佣金
                target.setPromoteAmount(source.getChangeFee());
                target.setRewardAmount(0L);
            }
            case SUB_STORE -> {
                //门店
                throw new RuntimeException("门店店长当前不能生成账单");
            }
        }


        target.setShopId(source.getSourceId());

        return target;
    }

    public static List<AccountStatementPromoteDetailVO> convert(List<AccountStatementDetail> detailList,
                                                                Map<Long, SimpleShopOrderView> orderMap) {
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.emptyList();
        }

        return detailList.stream()
                .map(source -> convert(source, orderMap.get(source.getOrderId())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static AccountStatementPromoteDetailVO convert(AccountStatementDetail source, SimpleShopOrderView orderView) {
        if (source == null) {
            return null;
        }

        AccountStatementPromoteDetailVO target = new AccountStatementPromoteDetailVO();

        target.setAccountStatementId(source.getAccountStatementId());
        target.setCommissionAmount(source.getCommissionAmount());
        target.setDeclareId(orderView.getDeclaredId());
        target.setItemName(orderView.getItemName());

        target.setItemQuantity(orderView.getQuantity());
        target.setItemUrl(ImageUrlHandler.complete(orderView.getSkuImage()));
        target.setOrderAmount(orderView.getFee());
        target.setOrderAt(DateUtil.toString(orderView.getCreatedAt()));

        target.setOrderId(source.getOrderId());
        target.setOrderType(source.getOrderType());

        return target;
    }

    public static List<AccountStatementExportVO> convert(Collection<ShopOrder> shopOrders) {
        if (CollectionUtils.isEmpty(shopOrders)) {
            return Collections.emptyList();
        }

        return shopOrders.stream()
                .map(shopOrder -> {
                    AccountStatementExportVO target = new AccountStatementExportVO();

                    target.setShopOrderId(shopOrder.getId());
                    target.setDeclareId(shopOrder.getDeclaredId());
                    target.setOrderAmount(new BigDecimal(shopOrder.getFee())
                            .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toPlainString());
                    target.setOrderCreatedTime(DateUtil.toString(shopOrder.getCreatedAt()));

                    return target;
                })
                .collect(Collectors.toList());
    }

    public static List<List<String>> convert(List<AccountStatementExportVO> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        return sourceList.stream()
                .map(source -> {
                    List<String> target = new ArrayList<>(16);

                    //订单编号
                    target.add(source.getShopOrderId().toString());
                    //申报单号
                    target.add(wrapWithEmptyString(source.getDeclareId()));
                    //订单金额
                    target.add(wrapWithEmptyString(source.getOrderAmount()));
                    //下单时间
                    target.add(wrapWithEmptyString(source.getOrderCreatedTime()));
                    //完成时间
                    target.add(wrapWithEmptyString(source.getFinishTime()));
                    //推广角色
                    target.add(wrapWithEmptyString(source.getPromoteUserRole()));
                    //推广人手机号
                    target.add(wrapWithEmptyString(source.getPromoteUserMobile()));
                    //门店名称
                    target.add(wrapWithEmptyString(source.getSubStoreName()));
                    //服务商名称
                    target.add(wrapWithEmptyString(source.getServiceProviderName()));
                    //导购佣金
                    target.add(wrapWithEmptyString(source.getGuiderCommission()));
                    //门店佣金
                    target.add(wrapWithEmptyString(source.getSubStoreCommission()));
                    //服务商佣金
                    target.add(wrapWithEmptyString(source.getServiceProviderCommission()));

                    //子订单商品信息
                    for (AccountStatementExportVO.SkuOrderDetailVO detailVO : source.getSkuOrderDetailList()) {
                        //商品分类
                        target.add(wrapWithEmptyString(detailVO.getItemCategory()));
                        //商品名称
                        target.add(wrapWithEmptyString(detailVO.getItemName()));
                        //商品价格
                        target.add(wrapWithEmptyString(detailVO.getItemPrice()));
                        //数量
                        target.add(wrapWithEmptyString(detailVO.getItemQuantity().toString()));
                    }

                    return target;
                })
                .collect(Collectors.toList());
    }

    private static String wrapWithEmptyString(String source) {
        if (source == null) {
            return StringUtils.EMPTY;
        }

        return source;
    }

    public static void appendProfitInfo(AccountStatementExportVO target, ShopOrderProfitBO shopOrderProfit) {
        try {
            if (shopOrderProfit == null) {
                return;
            }

            if (Boolean.TRUE.equals(shopOrderProfit.getCExisted())) {
                target.setPromoteUserRole("导购");
                target.setPromoteUserMobile(shopOrderProfit.getCMobile());
            } else {
                target.setPromoteUserRole("门店");
                target.setPromoteUserMobile(shopOrderProfit.getBMobile());
            }

            target.setSubStoreName(shopOrderProfit.getBName());
            target.setServiceProviderName(shopOrderProfit.getAName());

            target.setServiceProviderCommission(shopOrderProfit.getAProfit());
            target.setSubStoreCommission(shopOrderProfit.getBProfit());
            target.setGuiderCommission(shopOrderProfit.getCProfit());
        } catch (Exception ex) {
            log.error("AccountStatementDetailConvertor.appendProfitInfo error ", ex);
        }
    }

    public static void appendShipmentInfo(AccountStatementExportVO target, OrderShipmentInfoDO shipmentByShopOrder,
                                          List<OrderShipmentInfoDO> shipmentListBySkuOrder) {
        if (target == null) {
            return;
        }

        if (shipmentByShopOrder != null && shipmentByShopOrder.getConfirmAt() != null) {
            target.setFinishTime(DateUtil.toString(shipmentByShopOrder.getConfirmAt()));
            return;
        }

        if (CollectionUtils.isEmpty(shipmentListBySkuOrder)) {
            return;
        }
        shipmentListBySkuOrder.stream()
                .filter(shipment -> shipment.getConfirmAt() != null)
                .findAny()
                .ifPresent(shipment -> target.setFinishTime(DateUtil.toString(shipment.getConfirmAt())));
    }

    public Pair<Long, ShopOrderProfitBO> convert(ShopOrder shopOrder, List<BalanceDetail> balanceDetailList) {
        if (shopOrder == null || StringUtils.isBlank(shopOrder.getOutFrom())) {
            return null;
        }

        ShopOrderProfitBO targetBO = new ShopOrderProfitBO();
        if (CollectionUtils.isEmpty(balanceDetailList)) {
            return Pair.of(shopOrder.getId(), targetBO);
        }

        balanceDetailList.stream()
                .filter(profit -> !profit.isPresent())
                .forEach(profit -> orderExportConvertor.appendProfitInfo(targetBO, profit, shopOrder, null));

        balanceDetailList.stream()
                .filter(BalanceDetail::isPresent)
                .forEach(profit -> orderExportConvertor.appendProfitInfo(targetBO, profit, shopOrder, null));

        return Pair.of(shopOrder.getId(), targetBO);
    }

    public void appendItemInfo(AccountStatementExportVO target, List<SkuOrder> skuOrderList) {
        if (target == null || CollectionUtils.isEmpty(skuOrderList)) {
            return;
        }

        target.setSkuOrderDetailList(skuOrderList.stream().map(this::convert).collect(Collectors.toList()));
    }

    private AccountStatementExportVO.SkuOrderDetailVO convert(SkuOrder skuOrder) {
        AccountStatementExportVO.SkuOrderDetailVO detailVO = new AccountStatementExportVO.SkuOrderDetailVO();

        try {
            detailVO.setItemName(skuOrder.getItemName());
            detailVO.setItemPrice(orderExportConvertor.toPrice(skuOrder.getFee() / skuOrder.getQuantity()));
            detailVO.setItemQuantity(skuOrder.getQuantity());
            detailVO.setItemCategory(orderExportConvertor.getCategoryName(skuOrder.getItemId(), skuOrder.getShopId()));
        } catch (Exception ex) {
            log.error("AccountStatementDetailConvertor.convert itemInfo error ", ex);
        }

        return detailVO;
    }
}
