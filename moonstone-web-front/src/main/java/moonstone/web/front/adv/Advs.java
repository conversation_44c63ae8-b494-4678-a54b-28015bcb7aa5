package moonstone.web.front.adv;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.adv.dto.AdvDTO;
import moonstone.adv.dto.FullAdv;
import moonstone.adv.enums.AdvColumnNameType;
import moonstone.adv.enums.AdvColumnPlatform;
import moonstone.adv.enums.AdvLinkType;
import moonstone.adv.model.Adv;
import moonstone.adv.model.AdvColumn;
import moonstone.adv.service.AdvColumnReadService;
import moonstone.adv.service.AdvReadService;
import moonstone.adv.service.AdvWriteService;
import moonstone.cache.AdvCacher;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.front.component.distribution.DistributionChecker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/10/9.
 */
@Slf4j
@RestController
@RequestMapping("/api/adv")
public class Advs {
    @Value("${parana.web.url}")
    private String paranaWebUrl;

    @RpcConsumer
    private AdvReadService advReadService;

    @RpcConsumer
    private AdvWriteService advWriteService;

    @RpcConsumer
    private AdvColumnReadService advColumnReadService;

    @RpcConsumer
    private ItemReadService itemReadService;

    @Autowired
    private AdvCacher advCacher;

    @Autowired
    private UserTypeBean userTypeBean;

    @Autowired
    private DistributionChecker distributionChecker;

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody Adv adv){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            if (adv.getColumnId()==null){
                log.error("columnId is empty");
                throw new JsonResponseException("adv.create.columnId.empty");
            }
            if (adv.getColumnId() == AdvColumnNameType.WXA_WE_SHOP_BANNER.getValue() && !distributionChecker.checkShopIsOpenWeShop(commonUser.getShopId())) {
                log.warn("shop(id={}) not open weShop");
                throw new JsonResponseException("shop.not.open.weShop");
            }
            Response<AdvColumn> rAdvColumn = advColumnReadService.findById(adv.getColumnId());
            if (!rAdvColumn.isSuccess()){
                log.error("failed to find adv column by id={}, error code: {}", adv.getColumnId(), rAdvColumn.getError());
                throw new JsonResponseException(rAdvColumn.getError());
            }
            AdvColumn advColumn = rAdvColumn.getResult();
            if (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser) && advColumn.getCanSellerEdit()!=1){
                log.error("can not create adv in column(id={}) by user(id={})", adv.getColumnId(), commonUser.getId());
                throw new JsonResponseException("adv.create.not.authorized");
            } else if (userTypeBean.isNormal(commonUser) && !userTypeBean.isSeller(commonUser)){
                log.error("can not create adv by user(id={})", commonUser.getId());
                throw new JsonResponseException("adv.create.not.authorized");
            }
            adv.setShopId(commonUser.getShopId());
            adv.setColumnSn(advColumn.getSn());
            //生成url
            makeUrl(adv, advColumn);
            Response<Long> rId = advWriteService.create(adv);
            if (!rId.isSuccess()){
                log.error("failed to create adv({}), error code: {}", adv, rId.getError());
                throw new JsonResponseException(rId.getError());
            }
            //清对应缓存
            advCacher.clearAdvCache(adv.getColumnSn(), adv.getShopId());
            return rId.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to create adv({}), cause:{}", adv, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.create.fail");
        }
    }

    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean update(@RequestBody Adv adv){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            if (adv.getId() == null){
                log.warn("advId is empty");
                throw new JsonResponseException("adv.id.empty");
            }

            Response<Adv> rAdv = advReadService.findById(adv.getId());
            if (!rAdv.isSuccess()){
                log.error("failed to find adv by id={}, error code: {}", adv.getId(), rAdv.getError());
                throw new JsonResponseException(rAdv.getError());
            }
            Adv oldAdv = rAdv.getResult();
            //检查权限
            log.debug("oldAdv({})", oldAdv);
            if (oldAdv == null || !Objects.equal(oldAdv.getShopId(), commonUser.getShopId())){
                log.error("adv( id={}) not belong to user", adv.getId());
                throw new JsonResponseException("adv.not.belong.to.user");
            }
            //方式虚假信息
            adv.setShopId(null);
            Response<AdvColumn> advColumnResponse = advColumnReadService.findById(adv.getColumnId()==null?oldAdv.getColumnId():adv.getColumnId());
            if (!advColumnResponse.isSuccess()){
                log.error("failed to find adv column by id={}, error code: {}", adv.getColumnId(), advColumnResponse.getError());
                throw new JsonResponseException(advColumnResponse.getError());
            }
            AdvColumn advColumn = advColumnResponse.getResult();
            adv.setColumnId(advColumn.getId());
            adv.setColumnSn(advColumn.getSn());

            if (adv.getLinkType() != null || adv.getLinkId() !=null){
                if (adv.getLinkType() == null){
                    adv.setLinkType(oldAdv.getLinkType());
                } else if (adv.getLinkId() == null){
                    adv.setLinkId(oldAdv.getLinkId());
                }
                //生成url
                makeUrl(adv, advColumn);
            }

            Response<Boolean> response = advWriteService.update(adv);
            if (!response.isSuccess()){
                log.error("failed to update adv({}), error code: {}", adv, response.getError());
                throw new JsonResponseException(response.getError());
            }
            //清对应缓存
            advCacher.clearAdvCache(oldAdv.getColumnSn(), oldAdv.getShopId());
            advCacher.clearAdvCache(adv.getColumnSn(), adv.getShopId());
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to update adv({}), cause:{}", adv, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.update.fail");
        }
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean delete(@PathVariable Long id){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();

            Response<Adv> rAdv = advReadService.findById(id);
            if (!rAdv.isSuccess()){
                log.error("failed to find adv by id={}, error code: {}", id, rAdv.getError());
                throw new JsonResponseException(rAdv.getError());
            }
            Adv oldAdv = rAdv.getResult();
            //检查权限
            if (oldAdv==null || oldAdv.getShopId()!= commonUser.getShopId()){
                log.error("adv( id={}) not belong to user", id);
                throw new JsonResponseException("adv.not.belong.to.user");
            }

            Response<Boolean> response = advWriteService.updateStatus(id, -1);
            if (!response.isSuccess()){
                log.error("failed to delete adv status by id={}, error code: {}", id, response.getError());
                throw new JsonResponseException(response.getError());
            }
            //清对应缓存
            advCacher.clearAdvCache(oldAdv.getColumnSn(), oldAdv.getShopId());
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to delete adv by id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.delete.fail");
        }
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateStatus(@PathVariable Long id, @RequestParam Integer status){
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();

            Response<Adv> rAdv = advReadService.findById(id);
            if (!rAdv.isSuccess()){
                log.error("failed to find adv by id={}, error code: {}", id, rAdv.getError());
                throw new JsonResponseException(rAdv.getError());
            }
            Adv oldAdv = rAdv.getResult();
            //检查权限
            if (oldAdv==null || oldAdv.getShopId()!= commonUser.getShopId()){
                log.error("adv( id={}) not belong to user", id);
                throw new JsonResponseException("adv.not.belong.to.user");
            }

            Response<Boolean> response = advWriteService.updateStatus(id, status);
            if (!response.isSuccess()){
                log.error("failed to update adv status by id={}, status={}, error code: {}", id, status, response.getError());
                throw new JsonResponseException(response.getError());
            }
            //清对应缓存
            advCacher.clearAdvCache(oldAdv.getColumnSn(), oldAdv.getShopId());
            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to update adv status by id={}, status={}, cause:{}", id, status, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.delete.fail");
        }
    }

    /**
     * 用于根据广告的链接类型和链接id生成链接
     * @param adv
     * @return
     */
    private void makeUrl(Adv adv, AdvColumn advColumn){
        switch (AdvLinkType.fromInt(adv.getLinkType())){
            case NONE:{
                adv.setUrl("#");
                break;
            }
            case ITEM:{
                Response<Item> rItem = itemReadService.findById(adv.getLinkId());
                if (!rItem.isSuccess()){
                    log.error("failed to find item by id={}, error code: {}", adv.getLinkId(), rItem.getError());
                    throw new JsonResponseException(rItem.getError());
                }
                if (rItem.getResult().getStatus() == -3){
                    log.warn("item(id={}) is not exist", adv.getLinkId());
                    throw new JsonResponseException("item.not.exist");
                }
                switch (AdvColumnPlatform.fromInt(advColumn.getPlatform())){
                    case PC:{
                        adv.setUrl(paranaWebUrl + "/items/" + adv.getLinkId());
                        break;
                    }
                    case WXA:{
                        adv.setUrl("/pages/goods_detail?id=" + adv.getLinkId());
                        break;
                    }
                    case WESHOP:{
                        adv.setUrl("/pages/goods_detail?id=" + adv.getLinkId());
                        break;
                    }
                }
                break;
            }
            case ACTIVITY_PAGE:{
                adv.setUrl("/activity?" + adv.getLinkId());
                break;
            }
            default:{
                log.error("link type is illegal, adv({})", adv);
                throw new JsonResponseException("adv.link.type.illegal");
            }
        }
    }

    /**
     * 获取微信小程序店铺广告图
     *
     * @return
     */
    @RequestMapping(value = "/img/wxaShopImg", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<AdvDTO> wxaShopImg(@RequestParam Long shopId) {
        try {
            log.debug("[op:wxaShopImg]");
            List<AdvDTO> advDTOs = advCacher.listByColumnSnAndShopId(AdvColumnNameType.WXA_SHOP_BANNER.getSn(), shopId);
            log.debug("[op:wxaShopImg] advDTOs={}", JSON.toJSONString(advDTOs));
            return advDTOs;
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to list wxaShopImg by shopId={}, cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.list.wxaShopImg.fail");
        }
    }

    /**
     * 按广告位编码和店铺id获取广告图
     *
     * @return
     */
    @RequestMapping(value = "/listByColumnSnAndShopId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<AdvDTO> listByColumnSnAndShopId(@RequestParam String columnSn, @RequestParam(required = false) Long shopId){
        try {
            log.debug("[op:listByColumnSnAndShopId] columnSn={}, shopId={}", columnSn, shopId);
            List<AdvDTO> advDTOs = advCacher.listByColumnSnAndShopId(columnSn, shopId);
            log.debug("[op:listByColumnSnAndShopId] advDTOs={}", JSON.toJSONString(advDTOs));
            return advDTOs;
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("failed to list wxaShopImg by shopId={}, cause:{}", shopId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.list.fail");
        }
    }

    /**
     * 分页查找广告
     *
     * @return
     */
    @RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<FullAdv> paging(@RequestParam(required = false) List<Long> ids,
                                  @RequestParam(required = false) Long columnId,
                                  @RequestParam(required = false) String name,
                                  @RequestParam(required = false) Integer linkType,
                                  @RequestParam(required = false) Long linkId,
                                  @RequestParam(required = false) Integer status,
                                  @RequestParam(required = false) String statuses,
                                  @RequestParam(required = false) Date startedAt,
                                  @RequestParam(required = false) Date endedAt,
                                  @RequestParam(required = false) String sortBy,
                                  @RequestParam(required = false) Integer sortType,
                                  @RequestParam(required = false) Integer pageNo,
                                  @RequestParam(required = false) Integer pageSize){
        CommonUser commonUser = UserUtil.getCurrentUser();
        try {
            Response<Paging<Adv>> rAdvPaging = advReadService.paging(ids, commonUser.getShopId(), columnId, name, linkType, linkId,
                    status, statuses, startedAt, endedAt, sortBy, sortType, pageNo, pageSize);
            if (!rAdvPaging.isSuccess()){
                log.error("failed to paging advs by ids={}, shopId={}, columnId={}, name={}, linkType={}, linkId={}, status={}, statuses={}," +
                                "startedAt={}, endedAt={}, sortBy={}, sortType={}, pageNo={}, pageSize={}, error code: {}",
                        ids, commonUser.getShopId(), columnId, name, linkType, linkId, status, statuses,
                        startedAt, endedAt, sortBy, sortType, pageNo, pageSize, rAdvPaging.getError());
                throw new JsonResponseException(rAdvPaging.getError());
            }
            Paging<Adv> advPaging = rAdvPaging.getResult();
            List<FullAdv> fullAdvs = CopyUtil.copyList(advPaging.getData(), FullAdv.class);
            if (!advPaging.isEmpty()) {
                Map<Long, AdvColumn> advColumnById = new HashMap<>();
                for (FullAdv fullAdv : fullAdvs) {
                    Long advColumnId = fullAdv.getColumnId();
                    AdvColumn advColumn = advColumnById.get(advColumnId);
                    if (ObjectUtils.isEmpty(advColumn)) {
                        Response<AdvColumn> rAdvColumn = advColumnReadService.findById(advColumnId);
                        if (!rAdvColumn.isSuccess()) {
                            log.error("failed to find adv column by id={}, error code: {}", advColumnId, rAdvColumn.getError());
                            throw new JsonResponseException(rAdvColumn.getError());
                        }
                        fullAdv.setColumnName(rAdvColumn.getResult().getName());
                        advColumnById.put(advColumnId, rAdvColumn.getResult());
                    } else {
                        fullAdv.setColumnName(advColumn.getName());
                    }
                }
            }
            return new Paging<FullAdv>(advPaging.getTotal(), fullAdvs);
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to paging adv by ids={}, shopId={}, columnId={}, name={}, linkType={}, linkId={}, status={}, statuses={}," +
                            "startedAt={}, endedAt={}, sortBy={}, sortType={}, pageNo={}, pageSize={}, cause: {}",
                    ids, commonUser.getShopId(), columnId, name, linkType, linkId, status, statuses,
                    startedAt, endedAt, sortBy, sortType, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.list.fail");
        }
    }

    @RequestMapping(value = "/{advId}/move-up", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> moveUp(@PathVariable Long advId) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();

            Response<Adv> rAdv = advReadService.findById(advId);
            if (!rAdv.isSuccess()) {
                log.error("failed to find adv by id={}, error code: {}", advId, rAdv.getError());
                throw new JsonResponseException(rAdv.getError());
            }
            if (!Objects.equal(commonUser.getShopId(), rAdv.getResult().getShopId())){
                log.error("adv(id={}) is not belong to user(id={})", advId, commonUser.getId());
                throw new JsonResponseException("adv.not.belong.to.user");
            }

            return advWriteService.move(advId, -1);
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to move adv(id={}) up, cause: {}", advId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.move.up.fail");
        }
    }

    @RequestMapping(value = "/{advId}/move-down", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> moveDown(@PathVariable Long advId) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();

            Response<Adv> rAdv = advReadService.findById(advId);
            if (!rAdv.isSuccess()) {
                log.error("failed to find adv by id={}, error code: {}", advId, rAdv.getError());
                throw new JsonResponseException(rAdv.getError());
            }
            if (!Objects.equal(commonUser.getShopId(), rAdv.getResult().getShopId())){
                log.error("adv(id={}) is not belong to user(id={})", advId, commonUser.getId());
                throw new JsonResponseException("adv.not.belong.to.user");
            }

            return advWriteService.move(advId, 1);
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to move adv(id={}) up, cause: {}", advId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("adv.move.up.fail");
        }
    }
}
