package moonstone.web.front.open.outer;

import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ExpressCompanyCacher;
import moonstone.express.model.ExpressCompany;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Author:cp
 * Created on 9/14/16.
 */
@OpenBean
@Slf4j
public class ExpressApi {

    @Autowired
    private ExpressCompanyCacher expressCompanyCacher;

    @OpenMethod(key = "express.company.list", httpMethods = RequestMethod.GET)
    public List<ExpressCompany> findAll() {
        return expressCompanyCacher.listAllActiveExpressCompanies();
    }

}
