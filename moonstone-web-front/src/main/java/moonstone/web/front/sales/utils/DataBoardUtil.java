package moonstone.web.front.sales.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.Json;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.dto.DataBoardDto;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.OrderRoleSnapshotExtra;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.OrderRoleSnapshotReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.service.SubStoreReadService;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.sales.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class DataBoardUtil {


    private static final int NOT_PAID = OrderStatus.NOT_PAID.getValue();
    private static final int PAID = OrderStatus.PAID.getValue();
    private static final int SHIPPED = OrderStatus.SHIPPED.getValue();
    private static final int CONFIRMED = OrderStatus.CONFIRMED.getValue();

    private static final String TODAY = "today";
    private static final String MONTH = "month";
    private static final String YESTERDAY = "yesterday";
    private static final String OPTIONAL = "optional";

    private static final String ITEM_IMAGE_PREFIX = "https://dante-img.oss-cn-hangzhou.aliyuncs.com";


    @Resource
    ShopCacheHolder shopCacheHolder;

    @Resource
    ShopOrderReadService shopOrderReadService;

    @Resource
    SkuOrderReadService skuOrderReadService;

    @Resource
    OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    ServiceProviderCache serviceProviderCache;

    @Resource
    SubStoreReadService subStoreReadService;

    @Resource
    private SubStoreCache subStoreCache;

    @Resource
    ItemReadService itemReadService;

    @Resource
    SkuReadService skuReadService;

    @Autowired
    private MongoTemplate mongoTemplate;


    /**
     * 获得dataBoardShowVO
     *
     * @param startTimeMills
     * @param endTimeMills
     * @param type
     * @param shopId
     * @return
     */
    public DataBoardShowVO getDataBoard(Long startTimeMills, Long endTimeMills, String type, Long shopId) {
        DataBoardShowVO dataBoardShowVO = new DataBoardShowVO();
        Date startTime = new Date(startTimeMills);
        Date endTime = new Date(endTimeMills);

        try {
            dataBoardShowVO.setType(type);
            setTotalSalesShowVO(dataBoardShowVO, startTime, endTime, shopId);
            setServiceProviderTop(dataBoardShowVO, startTime, endTime, shopId);
            setSubStoreTop(dataBoardShowVO, startTime, endTime, shopId);
            setSkuTop(dataBoardShowVO, startTime, endTime, shopId);
            setProvinceTop(dataBoardShowVO, startTime, endTime, shopId);
        } catch (Exception e) {
            log.warn("fail to getDataBoard, cause :{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.getDataBoard");
        }


        return dataBoardShowVO;
    }


    /**
     * 通过shopId获得昨天的DataBoardShowVO数据，昨天零点~今天零点
     *
     * @return
     */
    public DataBoardShowVO getDataBoardYesterday(Long shopId) {
        Calendar calendar = Calendar.getInstance();
        var todayStart = DateUtil.withTimeAtStartOfDay(calendar.getTime()).getTime();

        calendar.add(Calendar.DAY_OF_YEAR, -1);
        var yesterdayStart = DateUtil.withTimeAtStartOfDay(calendar.getTime()).getTime();

        return getDataBoard(yesterdayStart, todayStart, YESTERDAY, shopId);
    }


    /**
     * 填装 总览视图数据 TotalSalesShowVO
     *
     * @param dataBoardShowVO
     * @param startTime
     * @param endTime
     * @return
     */
    public DataBoardShowVO setTotalSalesShowVO(DataBoardShowVO dataBoardShowVO, Date startTime, Date endTime, Long shopId) {

        TotalSalesShowVO totalSalesShow = new TotalSalesShowVO();
        totalSalesShow.setUpdateTime(new Date());

        try {
            List<ShopOrder> shopOrderList = getShopOrderList(shopId, startTime, endTime, NOT_PAID, CONFIRMED);

            for (ShopOrder shopOrder : shopOrderList) {
                int orderStatus = shopOrder.getStatus();
                long orderFee = shopOrder.getFee();

                if (orderStatus == PAID || orderStatus == SHIPPED || orderStatus == CONFIRMED) {
                    totalSalesShow.addTotalSales(orderFee);
                }
                if (orderStatus == NOT_PAID) {
                    totalSalesShow.addWaittingForPaySales(orderFee);
                } else if (orderStatus == PAID) {
                    totalSalesShow.addPaidSales(orderFee);
                } else if (orderStatus == SHIPPED || orderStatus == CONFIRMED) {
                    totalSalesShow.addShippedSales(orderFee);
                }
            }
        } catch (Exception e) {
            log.warn("fail to setTotalSalesShowVO, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.set.totalSalesShowVO");
        }

        dataBoardShowVO.setTotalSalesShowVO(totalSalesShow);
        return dataBoardShowVO;
    }

    /**
     * 填装 服务商按 GMV排名 和 按单量排名，按名次给前十 List<ServiceProviderRankVO> serviceProviderGmvTop and serviceProviderOrderTop
     *
     * @param dataBoardShowVO
     * @param startTime
     * @param endTime
     * @return
     */
    public DataBoardShowVO setServiceProviderTop(DataBoardShowVO dataBoardShowVO, Date startTime, Date endTime, Long shopId) {

        Response<List<DataBoardDto>> serviceProviderGmvResp = shopOrderReadService.findServiceProviderGmvTop(shopId, startTime, endTime);
        if (!serviceProviderGmvResp.isSuccess()) {
            log.warn("fail to find serviceProviderGmvTop, cause:{}", serviceProviderGmvResp.getError());
            throw new JsonResponseException(serviceProviderGmvResp.getError());
        }
        List<DataBoardDto> serviceProviderGmvList = serviceProviderGmvResp.getResult();

        try {
            List<ServiceProviderRankVO> serviceProviderGmvTop = getServiceProviderRankList(serviceProviderGmvList, shopId);
            dataBoardShowVO.setServiceProviderGmvTop(serviceProviderGmvTop);
        } catch (Exception e) {
            log.warn("fail to setServiceProviderGmvTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.serviceProviderGmvTop");
        }


        Response<List<DataBoardDto>> serviceProviderOrderResp = shopOrderReadService.findServiceProviderOrderTop(shopId, startTime, endTime);
        if (!serviceProviderOrderResp.isSuccess()) {
            log.warn("fail to find serviceProviderOrderTop, cause:{}", serviceProviderOrderResp.getError());
            throw new JsonResponseException(serviceProviderOrderResp.getError());
        }
        List<DataBoardDto> serviceProviderOrderList = serviceProviderOrderResp.getResult();

        try {
            List<ServiceProviderRankVO> serviceProviderOrderTop = getServiceProviderRankList(serviceProviderOrderList, shopId);
            dataBoardShowVO.setServiceProviderOrderTop(serviceProviderOrderTop);
        } catch (Exception e) {
            log.warn("fail to setServiceProviderOrderTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.serviceProviderOrderTop");
        }
        return dataBoardShowVO;
    }

    /**
     * 填装 门店按 GMV排名 和 按单量排名，按名次给前十 List<SubStoreRankVO> subStoreGmvTop and subStoreOrderTop
     *
     * @param dataBoardShowVO
     * @param startTime
     * @param endTime
     * @param shopId
     * @return
     */
    public DataBoardShowVO setSubStoreTop(DataBoardShowVO dataBoardShowVO, Date startTime, Date endTime, Long shopId) {

        Response<List<DataBoardDto>> subStoreGmvResp = shopOrderReadService.findSubStoreGmvTop(shopId, startTime, endTime);
        if (!subStoreGmvResp.isSuccess()) {
            log.warn("fail to find subStoreGmvTop, cause:{}", subStoreGmvResp.getError());
            throw new JsonResponseException(subStoreGmvResp.getError());
        }
        List<DataBoardDto> subStoreGmvList = subStoreGmvResp.getResult();

        try {
            List<SubStoreRankVO> subStoreGmvTop = getSubStoreRankList(subStoreGmvList, shopId);
            dataBoardShowVO.setSubStoreGmvTop(subStoreGmvTop);
        } catch (Exception e) {
            log.warn("fail to setSubStoreGmvTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.subStoreGmvTop");
        }

        Response<List<DataBoardDto>> subStoreOrderResp = shopOrderReadService.findSubStoreOrderTop(shopId, startTime, endTime);
        if (!subStoreOrderResp.isSuccess()) {
            log.warn("fail to find subStoreOrderTop, cause:{}", subStoreOrderResp.getError());
            throw new JsonResponseException(subStoreOrderResp.getError());
        }
        List<DataBoardDto> subStoreOrderList = subStoreOrderResp.getResult();

        try {
            List<SubStoreRankVO> subStoreOrderTop = getSubStoreRankList(subStoreOrderList, shopId);
            dataBoardShowVO.setSubStoreOrderTop(subStoreOrderTop);
        } catch (Exception e) {
            log.warn("fail to setSubStoreOrderTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.subStoreOrderTop");
        }

        return dataBoardShowVO;
    }


    /**
     * 填装 商品sku按 GMV排名 和 按单量排名，按名次给前十 List<SkuRankVO> itemsGmvTop and itemsOrderTop
     *
     * @param dataBoardShowVO
     * @param startTime
     * @param endTime
     * @return
     */
    public DataBoardShowVO setSkuTop(DataBoardShowVO dataBoardShowVO, Date startTime, Date endTime, Long shopId) {

        Response<List<DataBoardDto>> skuGmvResp = shopOrderReadService.findSkuGmvTop(shopId, startTime, endTime);
        if (!skuGmvResp.isSuccess()) {
            log.warn("fail to find skuGmvTop, cause:{}", skuGmvResp.getError());
            throw new JsonResponseException(skuGmvResp.getError());
        }
        List<DataBoardDto> skuGmvList = skuGmvResp.getResult();

        try {
            List<SkuRankVO> skuGmvTop = getSkuRankList(skuGmvList);
            dataBoardShowVO.setSkuGmvTop(skuGmvTop);
        } catch (Exception e) {
            log.warn("fail to setSkuGmvTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.skuGmvTop");
        }

        Response<List<DataBoardDto>> skuOrderResp = shopOrderReadService.findSkuOrderTop(shopId, startTime, endTime);
        if (!skuOrderResp.isSuccess()) {
            log.warn("fail to find skuOrderTop, cause:{}", skuOrderResp.getError());
            throw new JsonResponseException(skuOrderResp.getError());
        }
        List<DataBoardDto> skuOrderList = skuOrderResp.getResult();
        try {
            List<SkuRankVO> skuOrderTop = getSkuRankList(skuOrderList);
            dataBoardShowVO.setSkuOrderTop(skuOrderTop);
        } catch (Exception e) {
            log.warn("fail to setSkuOrderTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.skuOrderTop");
        }
        return dataBoardShowVO;
    }

    public DataBoardShowVO setProvinceTop(DataBoardShowVO dataBoardShowVO, Date startTime, Date endTime, Long shopId) {
        Response<List<DataBoardDto>> provinceGmvResp = shopOrderReadService.findProvinceGmvTop(shopId, startTime, endTime);
        if (!provinceGmvResp.isSuccess()) {
            log.warn("fail to find provinceGmvTop, cause:{}", provinceGmvResp.getError());
            throw new JsonResponseException(provinceGmvResp.getError());
        }
        List<DataBoardDto> provinceGmvList = provinceGmvResp.getResult();

        try {
            List<ProvinceRankVO> provinceGmvTop = getProvinceRankList(provinceGmvList, shopId);
            dataBoardShowVO.setProvinceGmvTop(provinceGmvTop);
        } catch (Exception e) {
            log.warn("fail to setProvinceGmvTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.provinceGmvTop");
        }


        Response<List<DataBoardDto>> provinceOrderResp = shopOrderReadService.findProvinceOrderTop(shopId, startTime, endTime);
        if (!provinceOrderResp.isSuccess()) {
            log.warn("fail to find provinceOrderTop, cause:{}", provinceOrderResp.getError());
            throw new JsonResponseException(provinceOrderResp.getError());
        }
        List<DataBoardDto> provinceOrderList = provinceOrderResp.getResult();

        try {
            List<ProvinceRankVO> provinceOrderTop = getProvinceRankList(provinceOrderList, shopId);
            dataBoardShowVO.setProvinceOrderTop(provinceOrderTop);
        } catch (Exception e) {
            log.warn("fail to setServiceProviderOrderTop, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("fail.to.set.serviceProviderOrderTop");
        }
        return dataBoardShowVO;
    }


    private List<ServiceProviderRankVO> getServiceProviderRankList(List<DataBoardDto> curList, Long shopId) {
        List<ServiceProviderRankVO> serviceProviderTop = new ArrayList<>();
        for (int i = 1; i <= 10 && i <= curList.size(); i++) {
            DataBoardDto dataBoardDto = curList.get(i - 1);
            long serviceProviderUserId = dataBoardDto.getResId();
            ServiceProviderRankVO serviceProviderRankVO = new ServiceProviderRankVO();

            var latestSnapshot = orderRoleSnapshotReadService.findByUserIdAndShopId(dataBoardDto.getResId(), shopId).getResult();

            serviceProviderRankVO.setRank(i);
            serviceProviderRankVO.setServiceProviderName(findServiceProviderName(dataBoardDto, latestSnapshot, shopId));
            serviceProviderRankVO.setProvince(findServiceProviderProvince(dataBoardDto, latestSnapshot, shopId));
            serviceProviderRankVO.setGmv(dataBoardDto.getSumCol());
            serviceProviderRankVO.setOrderQuantity(dataBoardDto.getCountCol());

            serviceProviderTop.add(serviceProviderRankVO);
        }
        return serviceProviderTop;
    }

    /**
     * 获得服务商的省份
     *
     * @param dataBoardDto
     * @param shopId
     * @return
     */
    private String findServiceProviderProvince(DataBoardDto dataBoardDto, OrderRoleSnapshot latestSnapshot, Long shopId) {
        if (StringUtils.isNotBlank(dataBoardDto.getProvince())) {
            return dataBoardDto.getProvince();
        }
        if (latestSnapshot != null && StringUtils.isNotBlank(latestSnapshot.getProvince())) {
            return latestSnapshot.getProvince();
        }

        var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(dataBoardDto.getResId(), shopId);
        if (serviceProvider != null) {
            return serviceProvider.getProvince();
        }

        return "null";
    }

    /**
     * 查询服务端的名称
     *
     * @param dataBoardDto
     * @param shopId
     * @return
     */
    private String findServiceProviderName(DataBoardDto dataBoardDto, OrderRoleSnapshot latestSnapshot, Long shopId) {
        if (StringUtils.isNotBlank(dataBoardDto.getResName())) {
            return dataBoardDto.getResName();
        }
        if (latestSnapshot != null && StringUtils.isNotBlank(latestSnapshot.getName())) {
            return latestSnapshot.getName();
        }

        var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(dataBoardDto.getResId(), shopId);
        if (serviceProvider != null) {
            return serviceProvider.getName();
        }

        return "null";
    }

    private List<SubStoreRankVO> getSubStoreRankList(List<DataBoardDto> curList, Long shopId) {
        List<SubStoreRankVO> subStoreTop = new ArrayList<>();
        for (int i = 1; i <= 10 && i <= curList.size(); i++) {
            DataBoardDto dataBoardDto = curList.get(i - 1);
            long subStoreUserId = dataBoardDto.getResId();
            SubStoreRankVO subStoreRankVO = new SubStoreRankVO();

            //serviceProviderCache初始化也是从MongoDB中查，所以也会出现没有数据的情况。
            //ServiceProvider serviceProviderBySubStore = serviceProviderCache.findBySubStoreUserIdAndShopId(subStoreUserId, shopId);
            Response<OrderRoleSnapshot> orderRoleSnapshotResp = orderRoleSnapshotReadService.findByUserIdAndShopId(subStoreUserId, shopId);
            if (!orderRoleSnapshotResp.isSuccess()) {
                log.error("fail to find OrderRoleSnapshot, cause:{}", orderRoleSnapshotResp.getError());
                throw new JsonResponseException(orderRoleSnapshotResp.getError());
            }
            OrderRoleSnapshot orderRoleSnapshot = orderRoleSnapshotResp.getResult();

            Map<Object, Object> map = null;
            String serviceProviderName = "null";
            String serviceProviderProvince = "null";
            if (orderRoleSnapshot != null && orderRoleSnapshot.getId() != null) {
                map = Json.parseObject(orderRoleSnapshot.getExtraJson(), new TypeReference<>() {
                });
                if (map != null && map.containsKey(OrderRoleSnapshotExtra.serviceProviderName.name()) && map.containsKey(OrderRoleSnapshotExtra.serviceProviderProvince.name())) {
                    serviceProviderName = (String) map.getOrDefault(OrderRoleSnapshotExtra.serviceProviderName.name(), "null");
                    serviceProviderProvince = (String) map.getOrDefault(OrderRoleSnapshotExtra.serviceProviderProvince.name(), "null");
                }
            }

            subStoreRankVO.setRank(i);
            subStoreRankVO.setSubStoreName(findSubStoreName(dataBoardDto, orderRoleSnapshot, shopId));
            subStoreRankVO.setServiceProviderName(serviceProviderName);
            subStoreRankVO.setProvince(serviceProviderProvince);
            subStoreRankVO.setGmv(dataBoardDto.getSumCol());
            subStoreRankVO.setOrderQuantity(dataBoardDto.getCountCol());

            subStoreTop.add(subStoreRankVO);
        }
        return subStoreTop;
    }

    /**
     * 获取门店名称
     *
     * @param dataBoardDto
     * @param lastSnapshot
     * @param shopId
     * @return
     */
    private String findSubStoreName(DataBoardDto dataBoardDto, OrderRoleSnapshot lastSnapshot, Long shopId) {
        if (StringUtils.isNotBlank(dataBoardDto.getResName())) {
            return dataBoardDto.getResName();
        }
        if (lastSnapshot != null && StringUtils.isNotBlank(lastSnapshot.getName())) {
            return lastSnapshot.getName();
        }

        var subStore = subStoreCache.findByShopIdAndUserId(shopId, dataBoardDto.getResId()).orElse(null);
        if (subStore != null) {
            return subStore.getName();
        }

        return "null";
    }

    private List<SkuRankVO> getSkuRankList(List<DataBoardDto> curList) {
        List<SkuRankVO> skuTop = new ArrayList<>();
        for (int i = 1; i <= 10 && i <= curList.size(); i++) {
            DataBoardDto dataBoardDto = curList.get(i - 1);
            long skuId = dataBoardDto.getResId();
            SkuRankVO skuRankVO = new SkuRankVO();

            Response<Sku> skuResp = skuReadService.findSkuById(skuId);
            if (!skuResp.isSuccess()) {
                log.error("fail to find sku, cause:{}", skuResp.getError());
                throw new JsonResponseException(skuResp.getError());
            }
            Sku sku = skuResp.getResult();

            skuRankVO.setRank(i);
            if (sku.getImage() == null) {
                Response<Item> itemResp = itemReadService.findById(sku.getItemId());
                if (!itemResp.isSuccess()) {
                    log.error("fail to find item by sku, cause: {}", itemResp.getError());
                    throw new JsonResponseException(itemResp.getError());
                }
                skuRankVO.setImage(ITEM_IMAGE_PREFIX + itemResp.getResult().getMainImage());
            } else {
                skuRankVO.setImage(sku.getImage());
            }
            skuRankVO.setSkuName(sku.getName());
            skuRankVO.setSkuId(skuId);
            skuRankVO.setGmv(dataBoardDto.getSumCol());
            skuRankVO.setOrderQuantity(dataBoardDto.getCountCol());

            skuTop.add(skuRankVO);
        }
        return skuTop;
    }

    private List<ProvinceRankVO> getProvinceRankList(List<DataBoardDto> curList, Long shopId) {
        List<ProvinceRankVO> provinceTop = new ArrayList<>();
        for (int i = 1; i <= 10 && i <= curList.size(); i++) {
            DataBoardDto dataBoardDto = curList.get(i - 1);
            ProvinceRankVO provinceRankVO = new ProvinceRankVO();

            provinceRankVO.setRank(i);
            provinceRankVO.setProvince(dataBoardDto.getProvince());
            provinceRankVO.setGmv(dataBoardDto.getSumCol());
            provinceRankVO.setOrderQuantity(dataBoardDto.getCountCol());

            provinceTop.add(provinceRankVO);
        }

        return provinceTop;
    }

    /**
     * 将时间范围内处于对应状态的 ShopOrder返回
     *
     * @param shopId
     * @param startTime
     * @param endTime
     * @param startStatus
     * @param endStatus
     * @return
     */
    public List<ShopOrder> getShopOrderList(long shopId, Date startTime, Date endTime, int startStatus, int endStatus) {
        List<Integer> statusList = new ArrayList<>();
        for (int i = startStatus; i <= endStatus; i++) {
            statusList.add(i);
        }
        //查出时间段内的未付款的订单
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setShopId(shopId);
        orderCriteria.setStatus(statusList);
        orderCriteria.setStartAt(startTime);
        orderCriteria.setEndAt(endTime);

        Response<List<ShopOrder>> shopOrdersRes = shopOrderReadService.listShopOrdersBy(orderCriteria);
        if (!shopOrdersRes.isSuccess()) {
            log.error("fail to find ShopOrders, cause: {}", shopOrdersRes.getError());
            throw new JsonResponseException(shopOrdersRes.getError());
        }
        return shopOrdersRes.getResult();
    }


    public SalesEmailTextVO findSalesEmailText(Long shopId) {
        SalesEmailTextVO salesEmailTextVO = new SalesEmailTextVO();

        DataBoardShowVO dataBoardShowVO = getDataBoardYesterday(shopId);
        salesEmailTextVO.setGmvYesterday(dataBoardShowVO.getTotalSalesShowVO().getTotalSalesGmv() * 1.0 / 100);
        salesEmailTextVO.setOrderQuantityYesterday(dataBoardShowVO.getTotalSalesShowVO().getTotalSalesOrderQuantity());

        var now = new Date();
        dataBoardShowVO = getDataBoard(DateUtil.withTimeAtStartOfMonth(now).getTime(), DateUtil.withTimeAtStartOfDay(now).getTime(),
                MONTH, shopId);
        salesEmailTextVO.setGmvMonth(dataBoardShowVO.getTotalSalesShowVO().getTotalSalesGmv() * 1.0 / 100);
        salesEmailTextVO.setOrderQuantityMonth(dataBoardShowVO.getTotalSalesShowVO().getTotalSalesOrderQuantity());

        return salesEmailTextVO;
    }


}
