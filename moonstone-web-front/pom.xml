<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>moonstone-mall</artifactId>
        <groupId>moonstone</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>moonstone-web-front</artifactId>
    <version>1.0.0.RELEASE</version>

    <dependencies>
        <dependency>
            <groupId>co.elastic.apm</groupId>
            <artifactId>apm-agent-api</artifactId>
            <version>1.34.1</version>
        </dependency>

        <!-- nacos config -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>soul-client-springmvc</artifactId>
            <version>${soul.mvc.version}</version>
        </dependency>
        <!-- moonstone -->
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-item-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-decoration-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-distribution-api</artifactId>
        </dependency>

        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-web-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <!--pay-->
        <dependency>
            <groupId>io.terminus.pay</groupId>
            <artifactId>terminus-pay-api</artifactId>
            <optional>true</optional>
        </dependency>

        <!--ip-->
        <dependency>
            <groupId>io.terminus.ip</groupId>
            <artifactId>terminus-ip</artifactId>
            <version>1.1</version>
        </dependency>

        <!-- open platform -->
        <dependency>
            <groupId>io.terminus.pampas.openplatform</groupId>
            <artifactId>core</artifactId>
            <version>0.5.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>2.8.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>4.2.0</version>
        </dependency>

        <!-- 引入本地lib包, 联动支付的sdk -->
        <dependency>
            <groupId>UMF_SDK</groupId>
            <artifactId>UMF_SDK</artifactId>
            <scope>system</scope>
            <version>1.0</version>
            <systemPath>${pom.basedir}/src/lib/UMF_SDK_V01.jar</systemPath>
        </dependency>
    </dependencies>

</project>
