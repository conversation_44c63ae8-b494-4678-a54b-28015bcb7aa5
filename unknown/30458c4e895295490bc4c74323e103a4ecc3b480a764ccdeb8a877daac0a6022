package moonstone.web.core.component.pay.xinbada.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * servers: [{
 * "server_uuid": 供应商uuid
 * "server_name":  供应商名称
 * "contract_uuid": 服务协议uuid
 * }]
 *
 * <AUTHOR>
 */
@Data
public class ServerUUIDQueryRespDTO {
    List<ServerDTO> servers;

    @Data
    public static class ServerDTO {
        String server_uuid;
        String server_name;
        String contract_uuid;
    }
}
