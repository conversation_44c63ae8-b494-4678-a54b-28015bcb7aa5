package moonstone.web.core.component.pay.bhecard.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.pay.app.Json;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class QueryRequest {
    @JsonProperty("merchant_id")
    String merchantId;
    @JsonProperty("out_trade_no")
    String outTradeNo;

    @JsonIgnore
    boolean ignoreDomainCheck = false;

    public Either<QueryResult> request(String gateWay, String partnerId, String key) {
        try {
            HttpRequest request = HttpRequest.post(gateWay);
            if (ignoreDomainCheck) {
                request.trustAllHosts();
            }
            ObjectMapper objectMapper = Json.OBJECT_MAPPER;
            CommonRequest<QueryRequest> wrappedRequest = new CommonRequest<>(CommonRequest.Service.JsPayPush, partnerId, key, "", this);
            wrappedRequest.sign();
            Map<?, ?> form = objectMapper.convertValue(wrappedRequest, Map.class);
            request.form(form);
            log.debug("{} form to gateWay[{}] with [{}]", LogUtil.getClassMethodName(), gateWay, form);
            if (request.ok()) {
                String body = request.body();
                log.debug("{} response [{}]", LogUtil.getClassMethodName(), body);
                PayResponse payResponse = Json.parseObject(body, PayResponse.class);
                if (Objects.requireNonNull(payResponse).getBody().success()) {
                    QueryResult result = new QueryResult(QueryResult.Type.valueOf(payResponse.getBody().getTradeType()),
                            LocalDateTime.from(DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss").parse(payResponse.getBody().getSuccessTime())));
                    return Either.ok(result);
                }
                return Either.error(payResponse.getBody().getMsg());
            }
            return Either.error("HTTP ERROR");
        } catch (Exception e) {
            log.error("{} fail to request JsPayRequest[{}] at gate[{}] with partnerId[{}] by key[{}]", LogUtil.getClassMethodName(), this, gateWay, partnerId, key, e);
            return Either.error(e);
        }
    }

    @Data
    private static class PayResponse {
        @JsonProperty("easypay_merchant_query_response")
        Body body;
        String sign;

        @Data
        private static class Body {
            String code;
            String msg;
            String subject;
            @JsonProperty("success_time")
            String successTime;
            @JsonProperty("trade_type")
            String tradeType;
            @JsonProperty("trade_status")
            String tradeStatus;

            public boolean success() {
                return "00".equals(code) && ("SUCCESS".equals(tradeStatus) || "BUSINESS_OK".equals(tradeStatus));
            }
        }
    }

    @AllArgsConstructor
    @Data
    public static class QueryResult {
        Type tradeType;
        LocalDateTime successTime;

        public enum Type {
            REFUND("退款"), CONSUME("支付"), DEPOSIT("充值");
            String desc;

            Type(String desc) {
                this.desc = desc;
            }

            @JsonValue
            public String getDesc() {
                return desc;
            }
        }
    }
}
