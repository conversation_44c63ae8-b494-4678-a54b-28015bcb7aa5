package moonstone.web.core.component.api;

import moonstone.common.api.OmsV3Rpc;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.model.Either;
import moonstone.common.model.rpcAPI.y800Storage.Y800OrderCancelResult;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentCancel;
import moonstone.order.dto.PayInfoPushCondition;
import moonstone.web.core.component.api.bo.y800v3.OrderPayerChangeRequest;
import moonstone.web.core.component.api.bo.y800v3.OrderStatus;
import moonstone.web.core.component.api.bo.y800v3.Y800CustomsClearanceRequest;
import moonstone.web.core.component.api.bo.y800v3.Y800OrderQuery;

import java.lang.reflect.Method;

public interface Y800V3Api extends OmsV3Rpc {

    /**
     * 发货单查询
     *
     * @param query
     * @return
     */
    @RemoteAPI(name = "delivery.getDetail")
    Either<OrderStatus> deliveryGetDetail(Y800OrderQuery query);

    /**
     * 推送清关信息
     *
     * @param request
     * @return
     */
    @RemoteAPI(name = "delivery.qgInfo")
    Either<Object> deliveryQGInfo(Y800CustomsClearanceRequest request);

    @RemoteAPI(name = "order.cancel")
    Either<Y800OrderCancelResult> orderCancelWithResult(Y800ShipmentCancel request);

    /**
     * 支付单推送
     *
     * @param request
     * @return
     */
    @RemoteAPI(name = "push.pay.info")
    Either<Boolean> pushPayInfo(PayInfoPushCondition request);

    @RemoteAPI(name = "order.payer.change")
    Either<Object> orderPayerChange(OrderPayerChangeRequest request);

    interface Y800V3RpcCore extends OmsRPCCore {
        @Override
        default String convertMethodIntoServiceName(Method method) {
            var config = method.getAnnotation(RemoteAPI.class);
            if (config == null || config.name().isEmpty()) {
                return OmsRPCCore.super.convertMethodIntoServiceName(method);
            }
            return config.name();
        }
    }
}
