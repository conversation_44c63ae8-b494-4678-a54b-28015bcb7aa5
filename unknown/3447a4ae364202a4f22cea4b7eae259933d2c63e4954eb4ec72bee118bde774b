package moonstone.web.core.component.pay.xinbada.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PayTask {
    /**
     * 姓名
     */
    String name;
    /**
     * 身份证号码
     */
    String social_no;
    String identity_type = "身份证";
    /**
     * 收款帐号
     */
    String card_no;
    /**
     * 非必填，若支付宝账户则必填
     */
    String bank_branch_name;
    /**
     * 手机号
     */
    String mobile_no;
    /**
     * 金额保留两位小数
     */
    BigDecimal amount;
}
