package moonstone.web.core.component.pay.allinpay.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "通联绑定-为指定用户注册会员", description = "通联绑定-注册会员")
public class AllinPayLoginUserRegisterAccountDTO implements Serializable {

//    @ApiModelProperty(value = "企业组织要成为的通联会员类型 2:企业会员 3：个人会员")
//    private Long allinPayRoleTypeByTenantType;

    @ApiModelProperty(value = "shopId")
    private Long shopId;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "企业组织要成为的通联会员类型")
    private Long allinPayRoleTypeByTenantType;

    public AllinPayLoginUserRegisterAccountDTO(){}

    public AllinPayLoginUserRegisterAccountDTO(Long shopId){
        this.shopId = shopId;
    }

}
