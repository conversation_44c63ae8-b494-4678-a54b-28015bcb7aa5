package moonstone.web.core.fileNew.view;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * author：书生
 */
@Data
public class AdminOrderExportView {

    /**.
     * 订单号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "订单号")
    private Long orderId;

    /**
     * 订单类型
     */
    @ExcelIgnore
    private Integer orderType;
    @ExcelIgnore
    private Long shopId;

    /**
     * 店铺名称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "店铺名称")
    private String shopName;


    /**
     * 买家id
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "买家id")
    private Long buyerId;

    /**
     * 买家昵称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "买家")
    private String buyerName;

    /**
     * 外部Sku编码
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "外部Sku编码")
    private String outerSkuId;

    /**
     * 订单状态
     */

    @ExcelIgnore
    private Integer status;

    /**
     * 订单状态描述
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "订单状态")
    private String statusDesc;

    /**
     * 支付流水号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "支付流水号")
    private String paySerialNo;

    /**
     * 下单时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "下单时间")
    private String createAt;

    /**
     * 支付时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "支付时间")
    private String payAt;

    /**
     * 订单总金额
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "订单总金额")
    private String totalAmount;

    /**
     * 收件人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "收件人")
    private String receiveUserName;

    /**
     * 收件人电话
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "收件人电话")
    private String receiveUserPhone;

    /**
     * 支付人
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "支付人")
    private String payerName;

    /**
     * 支付人电话
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "支付人电话")
    private String payerPhone;

    /**
     * 支付人身份证
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "支付人身份证")
    private String payerIdCard;

    /**
     * 收货地址
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "收货地址")
    private String address;

    /**
     * 快递公司
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "快递公司")
    private String expressCompany;

    /**
     * 快递编码
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "快递编码")
    private String expressCode;

    /**
     * 发货时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "发货时间")
    private String sendTime;

    /**
     * 确认收货时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "确认收货时间")
    private String confirmReceiveTime;

    /**
     * 商品id
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "商品id")
    private Long itemId;

    /**
     * 商品名称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品价格
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "商品价格")
    private String itemPrice;

    /**
     * 商品数量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "商品数量")
    private Integer quantity;

    /**
     * 税费
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "税费")
    private String tax;

    /**
     * 运费
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "运费")
    private String shipFee;

    /**
     * 优惠金额
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "优惠金额")
    private String discount;

    /**
     * 推送状态
     */
    @ExcelIgnore
    private Integer pushStatus;

    /**
     * 推送状态描述
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "推送状态")
    private String pushStatusDesc;
}
