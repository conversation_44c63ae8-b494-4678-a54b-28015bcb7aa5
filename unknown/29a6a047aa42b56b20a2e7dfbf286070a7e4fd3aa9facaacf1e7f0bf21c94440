package moonstone.web.core.component.pay.xinbada.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SignDTO {
    @J<PERSON>NField(name = "ent_user_uuid")
    @JsonProperty("ent_user_uuid")
    String entUserUuid;
    @JSONField(name = "personal_social_no")
    @JsonProperty("personal_social_no")
    String personalSocialNo;
    @JSONField(name = "personal_user_name")
    @JsonProperty("personal_user_name")
    String personalUserName;
    @J<PERSON><PERSON>ield(name = "personal_mobile_no")
    @JsonProperty("personal_mobile_no")
    String personalMobileNo;
    @JSONField(name = "personal_social_no_face_base64")
    @JsonProperty("personal_social_no_face_base64")
    String personalSocialNoFaceBase64;
    @<PERSON><PERSON><PERSON><PERSON>(name = "personal_social_no_back_base64")
    @JsonProperty("personal_social_no_back_base64")
    String personalSocialNoBackBase64;
    @<PERSON><PERSON><PERSON><PERSON>(name = "personal_social_no_base64")
    @JsonProperty("personal_social_no_base64")
    String personalSocialNoBase64;
    @JSONField(name = "personal_address")
    @JsonProperty("personal_address")
    String personalAddress;
    @JSONField(name = "personal_seal_base64")
    @JsonProperty("personal_seal_base64")
    String personalSealBase64;
    @JSONField(name = "signed_contract_base64")
    @JsonProperty("signed_contract_base64")
    String signedContractBase64;
    @JSONField(name = "provider_user_uuid")
    @JsonProperty("provider_user_uuid")
    String providerUserUuid;
}
