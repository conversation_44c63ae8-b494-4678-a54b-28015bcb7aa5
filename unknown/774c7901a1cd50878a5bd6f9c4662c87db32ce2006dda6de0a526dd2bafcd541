package moonstone.web.core.component.pay.allinpay.user.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "请求绑卡DTO", description = "请求绑卡")
public class AllinPayApplyBindBankCardDTO {

	@ApiModelProperty(value = "银行预留手机号码", required = true)
	@NotNull(message = "银行预留手机号码不能为空")
	private String phone;

	@ApiModelProperty(value = "银行卡号")
	@NotNull(message = "银行卡号不能为空")
	private String cardNo;

//	@NotNull(message = "支行名不能为空")
	@ApiModelProperty(value = "开户行支行名")
	private String branchName;

	@ApiModelProperty(value = "商家ID")
	private Long shopId;

	@ApiModelProperty(value = "通联会员类型 2：企业会员 3：个人会员")
	private Long allinpayRoleType;

	@ApiModelProperty(value = "用户ID 门店or消费者")
	private Long userId;

//	@ApiModelProperty(value = "姓名 如果是企业会员，请填写法人姓名")
//	@NotNull(message = "姓名不能为空")
//	private String name;
//
//	@ApiModelProperty(value = "证件类型 1-身份证 2-护照")
//	@NotNull(message = "证件类型不能为空")
//	private String identityType;
//
//	@ApiModelProperty(value = "证件号码")
//	@NotNull(message = "证件号码不能为空")
//	private String identityNo;
}
