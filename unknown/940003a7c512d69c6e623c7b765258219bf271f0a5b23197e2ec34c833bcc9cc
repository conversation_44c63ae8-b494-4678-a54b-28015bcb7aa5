package moonstone.web.core.component.pay.allinpay.user;

import moonstone.common.api.Result;
import moonstone.web.core.component.pay.allinpay.user.req.signature.AllinSignatureUserRegisterReq;
import moonstone.web.core.component.pay.allinpay.user.res.signature.AllinSignatureStatusVo;
import moonstone.web.core.component.pay.allinpay.user.signature.GetDownloadLinkReq;
import moonstone.web.core.component.pay.allinpay.user.signature.SignConfirmationReq;

public interface AllinPaySignatureService {

    Result<AllinSignatureStatusVo> signConfirmation(SignConfirmationReq req);

    Result<AllinSignatureStatusVo> userRegister(AllinSignatureUserRegisterReq dto);

    Result<String> getDownloadLink(GetDownloadLinkReq req);
}
