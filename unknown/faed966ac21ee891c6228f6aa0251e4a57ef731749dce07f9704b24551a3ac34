package moonstone.web.core.component.pay.allinpay.user.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "通联绑定手机", description = "通联绑定解绑手机")
public class AllinPayUnBindingPhoneDTO {

	@ApiModelProperty(value = "shopId")
	@NotNull(message = "shopId不能为空")
	private Long shopId;

	@ApiModelProperty(value = "手机号码（与发送验证码时使用的一致，交给通联判断）", required = true)
	@NotNull(message = "手机号码不能为空")
	private String phone;

	@ApiModelProperty(value = "短信验证码")
	private String verificationCode;

}
