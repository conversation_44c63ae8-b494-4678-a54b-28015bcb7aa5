package moonstone.web.core.session;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;

import javax.annotation.PostConstruct;
import javax.servlet.ServletContext;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SessionManagerImpl implements SessionManager {
    @Autowired
    private SessionConfig sessionConfig;
    @Autowired
    private JedisPool jedisPool;
    private static final String NONE = "none";

    @PostConstruct
    public void init() {
        log.debug("{} SessionManager init", LogUtil.getClassMethodName());
    }

    @Override
    public HttpSession getSession(String sessionId) {
        if (sessionId == null) {
            return null;
        }
        return new HeraldSession(sessionConfig.getRedisPrefix(), sessionId, jedisPool, sessionConfig.getMaxInactiveInterval(), null);
    }

    @Override
    public HttpSession getSession(HttpServletRequest request) {
        if (request instanceof HeraldRequestWithSession) {
            return getSession(request, ((HeraldRequestWithSession) request).getResponse());
        }
        return getSession(request, null);
    }

    @Override
    public HttpSession getSession(HttpServletRequest request, HttpServletResponse response) {
        String sessionId = Optional.ofNullable(getSessionCookie(request.getCookies())).orElseGet(request::getRequestedSessionId);
        if (sessionId == null) {
            return new HeraldSession(sessionConfig.getRedisPrefix(), this::generateSessionId, true, jedisPool, sessionConfig.getMaxInactiveInterval()
                    , request, response == null ? null : (id) -> addCookie(response, cookie -> cookie.setSecure(request.isSecure()), id));
        }
        return new HeraldSession(sessionConfig.getRedisPrefix(), sessionId, false, jedisPool, sessionConfig.getMaxInactiveInterval(), request, (id) -> addCookie(response, cookie -> cookie.setSecure(request.isSecure()), id));
    }

    @Override
    public HeraldRequestWithSession createSession(HttpServletRequest request, HttpServletResponse response) {
        return new HeraldRequestWithSession(request, response, this, sessionConfig.getCookieMaxAge());
    }

    @Override
    public void addCookie(HeraldRequestWithSession requestWithSession) {
        String sessionId = Optional.ofNullable(requestWithSession.getRequestedSessionId()).orElseGet(this::generateSessionId);
        requestWithSession.setSessionId(sessionId);
        addCookie(requestWithSession.getResponse(), cookie -> {
            cookie.setSecure(requestWithSession.isSecure());
            Optional.ofNullable(requestWithSession.getMaxAge()).ifPresent(cookie::setMaxAge);
        }, sessionId);
    }

    @Override
    public void addCookie(HttpServletResponse response, Consumer<Cookie> custom, String sessionId) {
        Cookie cookie = new Cookie(sessionConfig.cookieName, sessionId);
        Optional.ofNullable(sessionConfig.getCookieContextPath()).ifPresent(cookie::setPath);
        Optional.ofNullable(sessionConfig.getCookieMaxAge()).ifPresent(cookie::setMaxAge);
        if (Objects.nonNull(sessionConfig.getCookieDomain()) && !NONE.equals(sessionConfig.getCookieDomain())) {
            cookie.setDomain(sessionConfig.getCookieDomain());
        }
        cookie.setHttpOnly(true);
        cookie.setMaxAge(Math.min(Optional.ofNullable(sessionConfig.cookieMaxAge).orElse(0), Optional.ofNullable(sessionConfig.getMaxInactiveInterval()).orElse(0)));
        if (custom != null) {
            custom.accept(cookie);
        }
        response.addCookie(cookie);
    }

    @Override
    public boolean validate(String sessionId) {
        HttpSession session = new HeraldSession(sessionConfig.getRedisPrefix(), sessionId, false, jedisPool, sessionConfig.getMaxInactiveInterval(), (ServletContext) null, null);
        return session.getCreationTime() > 0;
    }

    @Override
    public String getSessionCookie(Cookie[] cookies) {
        if (cookies == null) {
            return null;
        }
        for (Cookie cookie : cookies) {
            if (checkSessionCookie(cookie)) {
                return cookie.getValue();
            }
        }
        return null;
    }

    private boolean checkSessionCookie(Cookie cookie) {
        if (Objects.isNull(cookie.getName())) {
            return false;
        }
        if (!cookie.getName().equals(sessionConfig.getCookieName())) {
            return false;
        }
        if (Objects.nonNull(sessionConfig.getCookieDomain()) && !NONE.equals(sessionConfig.getCookieDomain())) {
            if (Objects.nonNull(cookie.getDomain())) {
                if (cookie.getDomain().startsWith(sessionConfig.getCookieDomain())) {
                    return false;
                }
            }
        }
        if (Objects.nonNull(sessionConfig.getCookieContextPath())) {
            if (Objects.nonNull(cookie.getPath())) {
                return cookie.getPath().startsWith(sessionConfig.getCookieContextPath());
            }
        }
        return true;
    }

    @Override
    public String generateSessionId() {
        while (true) {
            String sessionId = UUID.randomUUID().toString().replaceAll("-", "");
            HttpSession session = new HeraldSession(sessionConfig.getRedisPrefix(), sessionId, false, jedisPool, sessionConfig.getMaxInactiveInterval(), (ServletContext) null, null);
            if (session.getCreationTime() == -1) {
                return sessionId;
            }
        }
    }
}
