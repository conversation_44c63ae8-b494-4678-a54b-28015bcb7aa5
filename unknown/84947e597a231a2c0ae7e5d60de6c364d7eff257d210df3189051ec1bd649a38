package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ShopMiniVersionStateEnum;
import moonstone.common.exception.ApiException;
import moonstone.common.model.vo.PageVo;
import moonstone.shopMini.model.ShopMiniVersion;
import moonstone.shopMini.service.ShopMiniVersionService;
import moonstone.web.core.fileNew.chain.ShopMiniPublishHandlerChain;
import moonstone.web.core.fileNew.dto.ShopMiniVersionPageDto;
import moonstone.web.core.fileNew.vo.ShopMiniVersionVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author：书生
 */

@Service
@Slf4j
public class ShopMiniVersionLogic {

    @Resource
    private ShopMiniVersionService shopMiniVersionService;

    @Resource
    private ShopMiniPublishHandlerChain shopMiniPublishHandlerChain;


    /**
     * 分页查询店铺小程序迭代信息列表
     *
     * @param req 请求参数
     * @return 店铺小程序迭代信息列表
     */
    public PageVo<ShopMiniVersionVo> pages(ShopMiniVersionPageDto req) {
        long current = req.getCurrent() == 0 ? 1 : req.getCurrent();
        long size = req.getSize() == 0 ? 5 : req.getSize();
        long offset = (current - 1) * size;
        Map<String, Object> query = new HashMap<>();
        query.put("offset", offset);
        query.put("limit", size);
        query.put("sortBy", "id");
        query.put("sortType", "2");
        query.put("shopWxaId", req.getShopWxaId());
        query.put("templateId", req.getTemplateId());
        if (req.getWhetherOnline()) {
            query.put("whetherOnline", 1);
        } else {
            query.put("whetherOnline", 0);
        }
        Paging<ShopMiniVersion> paging = shopMiniVersionService.pages(query);
        Long total = paging.getTotal();
        long pages = total / size + (total % size == 0 ? 0 : 1);
        if (total == 0) {
            return PageVo.build(total, size, current, pages, List.of());
        }
        List<ShopMiniVersionVo> dataList = paging.getData().stream().map(shopMiniVersion -> {
            ShopMiniVersionVo entity = new ShopMiniVersionVo();
            BeanUtil.copyProperties(shopMiniVersion, entity);
            String desc = ShopMiniVersionStateEnum.getDesc(shopMiniVersion.getVersionState());
            entity.setVersionDesc(desc);
            return entity;
        }).toList();
        return PageVo.build(total, size, current, pages, dataList);
    }

    public Boolean cancel(Long id) {
        Map<String, Object> query = new HashMap<>();
        query.put("id", id);
        ShopMiniVersion shopMiniVersion = shopMiniVersionService.getOne(query);
        if (!shopMiniVersion.getId().equals(id)) {
            throw new ApiException("未找到需要回退的版本信息");
        }
        Integer versionState = shopMiniVersion.getVersionState();
        if (!versionState.equals(ShopMiniVersionStateEnum.AUDIT_REJECTED.getCode())) {
            throw new ApiException("该迭代版本状态无法进行回退操作");
        }
        return shopMiniPublishHandlerChain.cancelVersion(shopMiniVersion);
    }
}
