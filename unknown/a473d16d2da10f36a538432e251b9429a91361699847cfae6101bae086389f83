package moonstone.web.core.component.pay.bhecard.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.pay.app.Json;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class H5Request {
    @JSONField(name = "account_type")
    @JsonProperty("account_type")
    Integer accountType = 1;
    Long amount;
    @JSONField(name = "bank_code")
    @JsonProperty("bank_code")
    String bankCode = "EASYPAY";
    String body;
    @JSONField(name = "front_url")
    @JsonProperty("front_url")
    String frontUrl = "http://mall.yang800.com";
    @JSONField(name = "merchant_id")
    @JsonProperty("merchant_id")
    String merchantId;
    @JSONField(name = "notify_url")
    @JsonProperty("notify_url")
    String notifyUrl;
    @JSONField(name = "order_type")
    @JsonProperty("order_type")
    Integer orderType;
    @JsonProperty("timeout_minutes")
    String timeoutMinutes = "120";
    @JSONField(name = "out_trade_no")
    @JsonProperty("out_trade_no")
    String outTradeNo;
    String subject;

    @JsonIgnore
    boolean ignoreDomainCheck = false;

    public Either<String> request(String gateWay, String partnerId, String key) {
        try {
            HttpRequest request = HttpRequest.post(gateWay);
            if (ignoreDomainCheck) {
                request.trustAllHosts();
            }
            ObjectMapper objectMapper = Json.OBJECT_MAPPER;
            CommonRequest<H5Request> wrappedRequest = new CommonRequest<>(CommonRequest.Service.H5PayPush, partnerId, key, "", this);
            wrappedRequest.sign();
            Map<?, ?> form = objectMapper.convertValue(wrappedRequest, Map.class);
            request.form(form);
            log.debug("{} form to gateWay[{}] with [{}]", LogUtil.getClassMethodName(), gateWay, form);
            if (request.ok()) {
                String body = request.body();
                log.debug("{} response [{}]", LogUtil.getClassMethodName(), body);
                PayResponse payResponse = Json.parseObject(body, PayResponse.class);
                if (Objects.requireNonNull(payResponse).getBody().success()) {
                    return Either.ok(payResponse.getBody().getOrderNo());
                }
                return Either.error(payResponse.getBody().getMsg());
            }
            return Either.error("HTTP ERROR");
        } catch (Exception e) {
            log.error("{} fail to request JsPayRequest[{}] at gate[{}] with partnerId[{}] by key[{}]", LogUtil.getClassMethodName(), this, gateWay, partnerId, key, e);
            return Either.error(e);
        }
    }

    @Data
    private static class PayResponse {
        String sign;
        @JsonProperty("easypay_merchant_easyPayh5_response")
        Resp body;

        @Data
        private static class Resp {
            String code;
            String msg;
            String url;
            @JsonProperty("order_no")
            String orderNo;

            public boolean success() {
                return "SUCCESS".equals(msg) || "00".equals(code) || "BUSINESS_OK".equals(msg);
            }
        }
    }
}
